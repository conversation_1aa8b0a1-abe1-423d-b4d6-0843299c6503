"use strict";(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[443],{677:function(e,t,a){a.d(t,{A:function(){return L}});var n=a(6540),o=a(6942),r=a.n(o),i=a(9853),l=a(2279),d=a(829),s=a(7072),c=a(6161),g=function(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(a[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(a[n[o]]=e[n[o]])}return a};var b=e=>{var{prefixCls:t,className:a,hoverable:o=!0}=e,i=g(e,["prefixCls","className","hoverable"]);const{getPrefixCls:d}=n.useContext(l.QO),s=d("card",t),c=r()(`${s}-grid`,a,{[`${s}-grid-hoverable`]:o});return n.createElement("div",Object.assign({},i,{className:c}))},p=a(2187),m=a(5905),u=a(7358),h=a(4277);const $=e=>{const{antCls:t,componentCls:a,headerHeight:n,headerPadding:o,tabsMarginBottom:r}=e;return Object.assign(Object.assign({display:"flex",justifyContent:"center",flexDirection:"column",minHeight:n,marginBottom:-1,padding:`0 ${(0,p.zA)(o)}`,color:e.colorTextHeading,fontWeight:e.fontWeightStrong,fontSize:e.headerFontSize,background:e.headerBg,borderBottom:`${(0,p.zA)(e.lineWidth)} ${e.lineType} ${e.colorBorderSecondary}`,borderRadius:`${(0,p.zA)(e.borderRadiusLG)} ${(0,p.zA)(e.borderRadiusLG)} 0 0`},(0,m.t6)()),{"&-wrapper":{width:"100%",display:"flex",alignItems:"center"},"&-title":Object.assign(Object.assign({display:"inline-block",flex:1},m.L9),{[`\n          > ${a}-typography,\n          > ${a}-typography-edit-content\n        `]:{insetInlineStart:0,marginTop:0,marginBottom:0}}),[`${t}-tabs-top`]:{clear:"both",marginBottom:r,color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,"&-bar":{borderBottom:`${(0,p.zA)(e.lineWidth)} ${e.lineType} ${e.colorBorderSecondary}`}}})},y=e=>{const{cardPaddingBase:t,colorBorderSecondary:a,cardShadow:n,lineWidth:o}=e;return{width:"33.33%",padding:t,border:0,borderRadius:0,boxShadow:`\n      ${(0,p.zA)(o)} 0 0 0 ${a},\n      0 ${(0,p.zA)(o)} 0 0 ${a},\n      ${(0,p.zA)(o)} ${(0,p.zA)(o)} 0 0 ${a},\n      ${(0,p.zA)(o)} 0 0 0 ${a} inset,\n      0 ${(0,p.zA)(o)} 0 0 ${a} inset;\n    `,transition:`all ${e.motionDurationMid}`,"&-hoverable:hover":{position:"relative",zIndex:1,boxShadow:n}}},v=e=>{const{componentCls:t,iconCls:a,actionsLiMargin:n,cardActionsIconSize:o,colorBorderSecondary:r,actionsBg:i}=e;return Object.assign(Object.assign({margin:0,padding:0,listStyle:"none",background:i,borderTop:`${(0,p.zA)(e.lineWidth)} ${e.lineType} ${r}`,display:"flex",borderRadius:`0 0 ${(0,p.zA)(e.borderRadiusLG)} ${(0,p.zA)(e.borderRadiusLG)}`},(0,m.t6)()),{"& > li":{margin:n,color:e.colorTextDescription,textAlign:"center","> span":{position:"relative",display:"block",minWidth:e.calc(e.cardActionsIconSize).mul(2).equal(),fontSize:e.fontSize,lineHeight:e.lineHeight,cursor:"pointer","&:hover":{color:e.colorPrimary,transition:`color ${e.motionDurationMid}`},[`a:not(${t}-btn), > ${a}`]:{display:"inline-block",width:"100%",color:e.colorIcon,lineHeight:(0,p.zA)(e.fontHeight),transition:`color ${e.motionDurationMid}`,"&:hover":{color:e.colorPrimary}},[`> ${a}`]:{fontSize:o,lineHeight:(0,p.zA)(e.calc(o).mul(e.lineHeight).equal())}},"&:not(:last-child)":{borderInlineEnd:`${(0,p.zA)(e.lineWidth)} ${e.lineType} ${r}`}}})},f=e=>Object.assign(Object.assign({margin:`${(0,p.zA)(e.calc(e.marginXXS).mul(-1).equal())} 0`,display:"flex"},(0,m.t6)()),{"&-avatar":{paddingInlineEnd:e.padding},"&-detail":{overflow:"hidden",flex:1,"> div:not(:last-child)":{marginBottom:e.marginXS}},"&-title":Object.assign({color:e.colorTextHeading,fontWeight:e.fontWeightStrong,fontSize:e.fontSizeLG},m.L9),"&-description":{color:e.colorTextDescription}}),S=e=>{const{componentCls:t,colorFillAlter:a,headerPadding:n,bodyPadding:o}=e;return{[`${t}-head`]:{padding:`0 ${(0,p.zA)(n)}`,background:a,"&-title":{fontSize:e.fontSize}},[`${t}-body`]:{padding:`${(0,p.zA)(e.padding)} ${(0,p.zA)(o)}`}}},z=e=>{const{componentCls:t}=e;return{overflow:"hidden",[`${t}-body`]:{userSelect:"none"}}},x=e=>{const{componentCls:t,cardShadow:a,cardHeadPadding:n,colorBorderSecondary:o,boxShadowTertiary:r,bodyPadding:i,extraColor:l}=e;return{[t]:Object.assign(Object.assign({},(0,m.dF)(e)),{position:"relative",background:e.colorBgContainer,borderRadius:e.borderRadiusLG,[`&:not(${t}-bordered)`]:{boxShadow:r},[`${t}-head`]:$(e),[`${t}-extra`]:{marginInlineStart:"auto",color:l,fontWeight:"normal",fontSize:e.fontSize},[`${t}-body`]:Object.assign({padding:i,borderRadius:`0 0 ${(0,p.zA)(e.borderRadiusLG)} ${(0,p.zA)(e.borderRadiusLG)}`},(0,m.t6)()),[`${t}-grid`]:y(e),[`${t}-cover`]:{"> *":{display:"block",width:"100%",borderRadius:`${(0,p.zA)(e.borderRadiusLG)} ${(0,p.zA)(e.borderRadiusLG)} 0 0`}},[`${t}-actions`]:v(e),[`${t}-meta`]:f(e)}),[`${t}-bordered`]:{border:`${(0,p.zA)(e.lineWidth)} ${e.lineType} ${o}`,[`${t}-cover`]:{marginTop:-1,marginInlineStart:-1,marginInlineEnd:-1}},[`${t}-hoverable`]:{cursor:"pointer",transition:`box-shadow ${e.motionDurationMid}, border-color ${e.motionDurationMid}`,"&:hover":{borderColor:"transparent",boxShadow:a}},[`${t}-contain-grid`]:{borderRadius:`${(0,p.zA)(e.borderRadiusLG)} ${(0,p.zA)(e.borderRadiusLG)} 0 0 `,[`${t}-body`]:{display:"flex",flexWrap:"wrap"},[`&:not(${t}-loading) ${t}-body`]:{marginBlockStart:e.calc(e.lineWidth).mul(-1).equal(),marginInlineStart:e.calc(e.lineWidth).mul(-1).equal(),padding:0}},[`${t}-contain-tabs`]:{[`> div${t}-head`]:{minHeight:0,[`${t}-head-title, ${t}-extra`]:{paddingTop:n}}},[`${t}-type-inner`]:S(e),[`${t}-loading`]:z(e),[`${t}-rtl`]:{direction:"rtl"}}},O=e=>{const{componentCls:t,bodyPaddingSM:a,headerPaddingSM:n,headerHeightSM:o,headerFontSizeSM:r}=e;return{[`${t}-small`]:{[`> ${t}-head`]:{minHeight:o,padding:`0 ${(0,p.zA)(n)}`,fontSize:r,[`> ${t}-head-wrapper`]:{[`> ${t}-extra`]:{fontSize:e.fontSize}}},[`> ${t}-body`]:{padding:a}},[`${t}-small${t}-contain-tabs`]:{[`> ${t}-head`]:{[`${t}-head-title, ${t}-extra`]:{paddingTop:0,display:"flex",alignItems:"center"}}}}};var A=(0,u.OF)("Card",e=>{const t=(0,h.oX)(e,{cardShadow:e.boxShadowCard,cardHeadPadding:e.padding,cardPaddingBase:e.paddingLG,cardActionsIconSize:e.fontSize});return[x(t),O(t)]},e=>{var t,a;return{headerBg:"transparent",headerFontSize:e.fontSizeLG,headerFontSizeSM:e.fontSize,headerHeight:e.fontSizeLG*e.lineHeightLG+2*e.padding,headerHeightSM:e.fontSize*e.lineHeight+2*e.paddingXS,actionsBg:e.colorBgContainer,actionsLiMargin:`${e.paddingSM}px 0`,tabsMarginBottom:-e.padding-e.lineWidth,extraColor:e.colorText,bodyPaddingSM:12,headerPaddingSM:12,bodyPadding:null!==(t=e.bodyPadding)&&void 0!==t?t:e.paddingLG,headerPadding:null!==(a=e.headerPadding)&&void 0!==a?a:e.paddingLG}}),C=a(124),j=function(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(a[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(a[n[o]]=e[n[o]])}return a};const w=e=>{const{actionClasses:t,actions:a=[],actionStyle:o}=e;return n.createElement("ul",{className:t,style:o},a.map((e,t)=>{const o=`action-${t}`;return n.createElement("li",{style:{width:100/a.length+"%"},key:o},n.createElement("span",null,e))}))},P=n.forwardRef((e,t)=>{const{prefixCls:a,className:o,rootClassName:g,style:p,extra:m,headStyle:u={},bodyStyle:h={},title:$,loading:y,bordered:v,variant:f,size:S,type:z,cover:x,actions:O,tabList:P,children:E,activeTabKey:N,defaultActiveTabKey:B,tabBarExtraContent:L,hoverable:T,tabProps:M={},classNames:G,styles:H}=e,R=j(e,["prefixCls","className","rootClassName","style","extra","headStyle","bodyStyle","title","loading","bordered","variant","size","type","cover","actions","tabList","children","activeTabKey","defaultActiveTabKey","tabBarExtraContent","hoverable","tabProps","classNames","styles"]),{getPrefixCls:I,direction:W,card:k}=n.useContext(l.QO),[D]=(0,C.A)("card",f,v);const F=e=>{var t;return r()(null===(t=null==k?void 0:k.classNames)||void 0===t?void 0:t[e],null==G?void 0:G[e])},K=e=>{var t;return Object.assign(Object.assign({},null===(t=null==k?void 0:k.styles)||void 0===t?void 0:t[e]),null==H?void 0:H[e])},q=n.useMemo(()=>{let e=!1;return n.Children.forEach(E,t=>{(null==t?void 0:t.type)===b&&(e=!0)}),e},[E]),X=I("card",a),[Q,J,U]=A(X),V=n.createElement(s.A,{loading:!0,active:!0,paragraph:{rows:4},title:!1},E),Y=void 0!==N,Z=Object.assign(Object.assign({},M),{[Y?"activeKey":"defaultActiveKey"]:Y?N:B,tabBarExtraContent:L});let _;const ee=(0,d.A)(S),te=ee&&"default"!==ee?ee:"large",ae=P?n.createElement(c.A,Object.assign({size:te},Z,{className:`${X}-head-tabs`,onChange:t=>{var a;null===(a=e.onTabChange)||void 0===a||a.call(e,t)},items:P.map(e=>{var{tab:t}=e,a=j(e,["tab"]);return Object.assign({label:t},a)})})):null;if($||m||ae){const e=r()(`${X}-head`,F("header")),t=r()(`${X}-head-title`,F("title")),a=r()(`${X}-extra`,F("extra")),o=Object.assign(Object.assign({},u),K("header"));_=n.createElement("div",{className:e,style:o},n.createElement("div",{className:`${X}-head-wrapper`},$&&n.createElement("div",{className:t,style:K("title")},$),m&&n.createElement("div",{className:a,style:K("extra")},m)),ae)}const ne=r()(`${X}-cover`,F("cover")),oe=x?n.createElement("div",{className:ne,style:K("cover")},x):null,re=r()(`${X}-body`,F("body")),ie=Object.assign(Object.assign({},h),K("body")),le=n.createElement("div",{className:re,style:ie},y?V:E),de=r()(`${X}-actions`,F("actions")),se=(null==O?void 0:O.length)?n.createElement(w,{actionClasses:de,actionStyle:K("actions"),actions:O}):null,ce=(0,i.A)(R,["onTabChange"]),ge=r()(X,null==k?void 0:k.className,{[`${X}-loading`]:y,[`${X}-bordered`]:"borderless"!==D,[`${X}-hoverable`]:T,[`${X}-contain-grid`]:q,[`${X}-contain-tabs`]:null==P?void 0:P.length,[`${X}-${ee}`]:ee,[`${X}-type-${z}`]:!!z,[`${X}-rtl`]:"rtl"===W},o,g,J,U),be=Object.assign(Object.assign({},null==k?void 0:k.style),p);return Q(n.createElement("div",Object.assign({ref:t},ce,{className:ge,style:be}),_,oe,le,se))});var E=function(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(a[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(a[n[o]]=e[n[o]])}return a};var N=e=>{const{prefixCls:t,className:a,avatar:o,title:i,description:d}=e,s=E(e,["prefixCls","className","avatar","title","description"]),{getPrefixCls:c}=n.useContext(l.QO),g=c("card",t),b=r()(`${g}-meta`,a),p=o?n.createElement("div",{className:`${g}-meta-avatar`},o):null,m=i?n.createElement("div",{className:`${g}-meta-title`},i):null,u=d?n.createElement("div",{className:`${g}-meta-description`},d):null,h=m||u?n.createElement("div",{className:`${g}-meta-detail`},m,u):null;return n.createElement("div",Object.assign({},s,{className:b}),p,h)};const B=P;B.Grid=b,B.Meta=N;var L=B}}]);
//# sourceMappingURL=b860bc639f3f8d1ea168479a44bcc7637de1cc4f-f81bdf2f02b4a8a55f0b.js.map