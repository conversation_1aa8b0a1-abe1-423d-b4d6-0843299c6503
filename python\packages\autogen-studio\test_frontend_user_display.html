<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端用户显示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="url"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button.danger {
            background-color: #dc3545;
        }
        button.danger:hover {
            background-color: #c82333;
        }
        button.success {
            background-color: #28a745;
        }
        button.success:hover {
            background-color: #218838;
        }
        .result {
            padding: 15px;
            border-radius: 4px;
            margin-top: 15px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .step {
            margin-bottom: 20px;
            padding: 15px;
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
        }
        .step h3 {
            margin-top: 0;
            color: #007bff;
        }
        .status-display {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>前端用户显示修复测试</h1>
    <p>此页面用于测试前端用户显示修复后的效果</p>

    <div class="container">
        <div class="form-group">
            <label for="serverUrl">服务器地址:</label>
            <input type="url" id="serverUrl" value="http://127.0.0.1:8081" placeholder="http://127.0.0.1:8081">
        </div>
        <div class="form-group">
            <label for="token">JWT Token:</label>
            <input type="text" id="token" placeholder="输入JWT token">
        </div>
    </div>

    <div class="step">
        <h3>当前状态</h3>
        <div class="status-display">
            <div><strong>存储的Token:</strong> <span id="storedToken">未检测到</span></div>
            <div><strong>认证类型:</strong> <span id="authType">检查中...</span></div>
            <div><strong>当前用户:</strong> <span id="currentUser">检查中...</span></div>
        </div>
        <button onclick="checkCurrentStatus()" class="success">刷新状态</button>
    </div>

    <div class="step">
        <h3>步骤1: 测试Token登录</h3>
        <p>测试token登录并存储到localStorage</p>
        <button onclick="testTokenLogin()">执行Token登录</button>
        <button onclick="clearStoredToken()" class="danger">清除存储的Token</button>
    </div>

    <div class="step">
        <h3>步骤2: 测试用户状态获取</h3>
        <p>测试使用存储的token获取用户信息</p>
        <button onclick="testGetCurrentUser()" class="success">获取当前用户</button>
        <button onclick="testAuthType()" class="success">检查认证类型</button>
    </div>

    <div class="step">
        <h3>步骤3: 模拟前端认证流程</h3>
        <p>模拟前端AuthProvider的初始化流程</p>
        <button onclick="simulateAuthProviderInit()" class="success">模拟认证初始化</button>
    </div>

    <div id="result"></div>

    <script>
        // 页面加载时检查状态
        window.onload = function() {
            // 设置默认token
            document.getElementById('token').value = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************.example';
            
            checkCurrentStatus();
        };

        async function checkCurrentStatus() {
            const serverUrl = document.getElementById('serverUrl').value.trim();
            
            // 检查存储的token
            const storedToken = localStorage.getItem('auth_token');
            document.getElementById('storedToken').textContent = storedToken ? 
                `${storedToken.substring(0, 30)}... (${storedToken.length}字符)` : '未检测到';
            
            // 检查认证类型
            try {
                const authTypeResponse = await fetch(`${serverUrl}/api/auth/type`);
                const authTypeData = await authTypeResponse.json();
                document.getElementById('authType').textContent = authTypeData.type || '未知';
            } catch (error) {
                document.getElementById('authType').textContent = '检查失败';
            }
            
            // 检查当前用户
            if (storedToken) {
                try {
                    const userResponse = await fetch(`${serverUrl}/api/auth/me`, {
                        headers: {
                            'Authorization': `Bearer ${storedToken}`
                        }
                    });
                    
                    if (userResponse.ok) {
                        const userData = await userResponse.json();
                        document.getElementById('currentUser').textContent = 
                            `${userData.name} (${userData.id})`;
                    } else {
                        document.getElementById('currentUser').textContent = '获取失败';
                    }
                } catch (error) {
                    document.getElementById('currentUser').textContent = '请求异常';
                }
            } else {
                document.getElementById('currentUser').textContent = '无token';
            }
        }

        async function testTokenLogin() {
            const serverUrl = document.getElementById('serverUrl').value.trim();
            const token = document.getElementById('token').value.trim();
            
            if (!serverUrl || !token) {
                showResult('请输入服务器地址和token', 'error');
                return;
            }

            try {
                showResult('正在执行token登录...', 'info');

                const response = await fetch(`${serverUrl}/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ token: token })
                });

                const data = await response.json();

                if (response.ok && data.status) {
                    // 存储token到localStorage
                    const tokenToStore = data.token || token;
                    localStorage.setItem('auth_token', tokenToStore);
                    
                    showResult(`✅ Token登录成功！

响应数据:
${JSON.stringify(data, null, 2)}

Token已存储到localStorage: ${tokenToStore.substring(0, 50)}...

现在刷新状态检查用户信息`, 'success');
                    
                    // 自动刷新状态
                    setTimeout(checkCurrentStatus, 1000);
                } else {
                    showResult(`❌ Token登录失败！

错误信息: ${data.message}
完整响应: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult(`❌ 请求失败: ${error.message}`, 'error');
            }
        }

        async function testGetCurrentUser() {
            const serverUrl = document.getElementById('serverUrl').value.trim();
            const token = localStorage.getItem('auth_token');
            
            if (!token) {
                showResult('❌ 未找到存储的token，请先进行token登录', 'error');
                return;
            }

            try {
                showResult('正在获取当前用户信息...', 'info');

                const response = await fetch(`${serverUrl}/api/auth/me`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const data = await response.json();

                if (response.ok) {
                    showResult(`✅ 获取用户信息成功:

用户ID: ${data.id}
用户名: ${data.name}
邮箱: ${data.email || '未设置'}
提供商: ${data.provider}
角色: ${JSON.stringify(data.roles || [])}

完整用户数据:
${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    showResult(`❌ 获取用户信息失败:

状态码: ${response.status}
错误信息: ${data.message || '未知错误'}
完整响应: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult(`❌ 请求异常: ${error.message}`, 'error');
            }
        }

        async function testAuthType() {
            const serverUrl = document.getElementById('serverUrl').value.trim();

            try {
                showResult('正在检查认证类型...', 'info');

                const response = await fetch(`${serverUrl}/api/auth/type`);
                const data = await response.json();

                if (response.ok) {
                    showResult(`✅ 认证类型检查成功:

认证类型: ${data.type}
排除路径: ${JSON.stringify(data.exclude_paths || [], null, 2)}

完整响应:
${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    showResult(`❌ 认证类型检查失败:

状态码: ${response.status}
完整响应: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult(`❌ 请求异常: ${error.message}`, 'error');
            }
        }

        async function simulateAuthProviderInit() {
            const serverUrl = document.getElementById('serverUrl').value.trim();
            
            showResult('🔄 模拟前端AuthProvider初始化流程...', 'info');
            
            try {
                // 步骤1: 检查认证类型
                const authTypeResponse = await fetch(`${serverUrl}/api/auth/type`);
                const authTypeData = await authTypeResponse.json();
                const authType = authTypeData.type;
                
                let result = `步骤1: 检查认证类型
认证类型: ${authType}

`;
                
                // 步骤2: 检查存储的token
                const storedToken = localStorage.getItem('auth_token');
                result += `步骤2: 检查存储的token
存储的token: ${storedToken ? '存在' : '不存在'}

`;
                
                if (storedToken) {
                    // 步骤3: 尝试使用token获取用户信息
                    try {
                        const userResponse = await fetch(`${serverUrl}/api/auth/me`, {
                            headers: {
                                'Authorization': `Bearer ${storedToken}`
                            }
                        });
                        
                        if (userResponse.ok) {
                            const userData = await userResponse.json();
                            result += `步骤3: 使用token获取用户信息
✅ 成功获取用户信息:
   用户ID: ${userData.id}
   用户名: ${userData.name}
   提供商: ${userData.provider}

🎉 结论: 用户应该显示为 "${userData.name}" 而不是默认用户`;
                            showResult(result, 'success');
                        } else {
                            result += `步骤3: 使用token获取用户信息
❌ Token无效或已过期

🔄 回退: 根据认证类型设置默认用户`;
                            showResult(result, 'error');
                        }
                    } catch (error) {
                        result += `步骤3: 使用token获取用户信息
❌ 请求异常: ${error.message}`;
                        showResult(result, 'error');
                    }
                } else {
                    result += `步骤3: 无存储的token
🔄 根据认证类型设置默认用户`;
                    showResult(result, 'info');
                }
                
            } catch (error) {
                showResult(`❌ 模拟初始化失败: ${error.message}`, 'error');
            }
        }

        function clearStoredToken() {
            localStorage.removeItem('auth_token');
            showResult('✅ Token已从localStorage中清除', 'info');
            checkCurrentStatus();
        }

        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = `result ${type}`;
        }
    </script>
</body>
</html>
