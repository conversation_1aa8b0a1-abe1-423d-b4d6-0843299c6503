{"name": "autogentstudio", "version": "1.0.0", "private": true, "description": "AutoGen Studio - Build LLM Enabled Agents", "author": "Microsoft", "keywords": ["gatsby"], "scripts": {"develop": "gatsby clean && gatsby develop", "dev": "npm run develop", "start": "gatsby clean && gatsby develop", "build": "gatsby clean && rimraf ../autogenstudio/web/ui && PREFIX_PATH_VALUE='' gatsby build --prefix-paths && cpx \"public/**\" \"../autogenstudio/web/ui/\" --clean", "serve": "gatsby serve", "clean": "gatsby clean", "typecheck": "tsc --noEmit"}, "resolutions": {"cookie": "^0.7.1", "path-to-regexp": "^0.1.12"}, "dependencies": {"@dagrejs/dagre": "^1.1.4", "@dnd-kit/core": "^6.2.0", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.0.18", "@mdx-js/react": "^3.1.0", "@monaco-editor/react": "^4.6.0", "@tailwindcss/typography": "^0.5.9", "@xyflow/react": "^12.3.5", "antd": "^5.22.1", "autoprefixer": "^10.4.20", "gatsby": "^5.14.0", "gatsby-plugin-image": "^3.14.0", "gatsby-plugin-manifest": "^5.14.0", "gatsby-plugin-mdx": "^5.14.0", "gatsby-plugin-postcss": "^6.14.0", "gatsby-plugin-sharp": "^5.14.0", "gatsby-source-filesystem": "^5.14.0", "gatsby-transformer-sharp": "^5.14.0", "install": "^0.13.0", "lucide-react": "^0.460.0", "monaco-editor": "^0.52.2", "monaco-editor-webpack-plugin": "^7.1.0", "postcss": "^8.4.49", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.1", "tailwindcss": "^3.4.14", "yarn": "^1.22.22", "zustand": "^5.0.1"}, "devDependencies": {"@types/cpx": "^1", "@types/lodash.debounce": "^4.0.9", "@types/node": "^22.9.0", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@types/uuid": "^10.0.0", "cpx": "^1.5.0", "rimraf": "^6.0.1", "typescript": "^5.3.3"}, "packageManager": "yarn@4.9.1+sha512.f95ce356460e05be48d66401c1ae64ef84d163dd689964962c6888a9810865e39097a5e9de748876c2e0bf89b232d583c33982773e9903ae7a76257270986538"}