import * as React from "react";
import { graphql } from "gatsby";

import { SessionManager } from "../components/views/playground/manager";
import { LiteLayout } from "../components/layout";

// markup
const LitePage = ({ data }: any) => {
  return (
    <LiteLayout>
      <main style={{ height: "100%" }} className="h-full">
        <SessionManager />
      </main>
    </LiteLayout>
  );
};

export const query = graphql`
  query LitePageQuery {
    site {
      siteMetadata {
        description
        title
      }
    }
  }
`;

export default LitePage;

export const Head = () => (
  <>
    <title>多智能体工作室 - 轻量模式</title>
    <meta
      name="description"
      content="多智能体工作室 轻量模式 - 简化的聊天界面"
    />
  </>
);
