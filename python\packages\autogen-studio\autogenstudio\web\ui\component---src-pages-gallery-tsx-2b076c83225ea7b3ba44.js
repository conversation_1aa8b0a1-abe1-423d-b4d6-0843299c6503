/*! For license information please see component---src-pages-gallery-tsx-2b076c83225ea7b3ba44.js.LICENSE.txt */
"use strict";(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[355],{684:function(e,t,n){n.d(t,{A:function(){return o}});const o=(0,n(1788).A)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},1610:function(e){e.exports=JSON.parse('{"id":"gallery_default","name":"Default Component Gallery","url":null,"metadata":{"author":"AutoGen Team","version":"1.0.0","description":"A default gallery containing basic components for human-in-loop conversations","tags":["human-in-loop","assistant","web agents"],"license":"MIT","homepage":null,"category":"conversation","last_synced":null},"components":{"agents":[{"provider":"autogen_agentchat.agents.AssistantAgent","component_type":"agent","version":2,"component_version":2,"description":"An agent that provides assistance with ability to use tools.","label":"AssistantAgent","config":{"name":"assistant_agent","model_client":{"provider":"autogen_ext.models.openai.OpenAIChatCompletionClient","component_type":"model","version":1,"component_version":1,"description":"Chat completion client for OpenAI hosted models.","label":"OpenAIChatCompletionClient","config":{"model":"gpt-4o-mini"}},"workbench":[{"provider":"autogen_core.tools.StaticWorkbench","component_type":"workbench","version":1,"component_version":1,"description":"A workbench that provides a static set of tools that do not change after\\n    each tool execution.","label":"StaticWorkbench","config":{"tools":[{"provider":"autogen_core.tools.FunctionTool","component_type":"tool","version":1,"component_version":1,"description":"Create custom tools by wrapping standard Python functions.","label":"FunctionTool","config":{"source_code":"def calculator(a: float, b: float, operator: str) -> str:\\n    try:\\n        if operator == \\"+\\":\\n            return str(a + b)\\n        elif operator == \\"-\\":\\n            return str(a - b)\\n        elif operator == \\"*\\":\\n            return str(a * b)\\n        elif operator == \\"/\\":\\n            if b == 0:\\n                return \\"Error: Division by zero\\"\\n            return str(a / b)\\n        else:\\n            return \\"Error: Invalid operator. Please use +, -, *, or /\\"\\n    except Exception as e:\\n        return f\\"Error: {str(e)}\\"\\n","name":"calculator","description":"A simple calculator that performs basic arithmetic operations","global_imports":[],"has_cancellation_support":false}}]}}],"model_context":{"provider":"autogen_core.model_context.UnboundedChatCompletionContext","component_type":"chat_completion_context","version":1,"component_version":1,"description":"An unbounded chat completion context that keeps a view of the all the messages.","label":"UnboundedChatCompletionContext","config":{}},"description":"An agent that provides assistance with ability to use tools.","system_message":"You are a helpful assistant. Solve tasks carefully. When done, say TERMINATE.","model_client_stream":false,"reflect_on_tool_use":false,"tool_call_summary_format":"{result}","metadata":{}}},{"provider":"autogen_ext.agents.web_surfer.MultimodalWebSurfer","component_type":"agent","version":1,"component_version":1,"description":"An agent that solves tasks by browsing the web using a headless browser.","label":"Web Surfer Agent","config":{"name":"websurfer_agent","model_client":{"provider":"autogen_ext.models.openai.OpenAIChatCompletionClient","component_type":"model","version":1,"component_version":1,"description":"Chat completion client for OpenAI hosted models.","label":"OpenAIChatCompletionClient","config":{"model":"gpt-4o-mini"}},"description":"an agent that solves tasks by browsing the web","headless":true,"start_page":"https://www.bing.com/","animate_actions":false,"to_save_screenshots":false,"use_ocr":false,"to_resize_viewport":true}},{"provider":"autogen_agentchat.agents.AssistantAgent","component_type":"agent","version":2,"component_version":2,"description":"an agent that verifies and summarizes information","label":"Verification Assistant","config":{"name":"assistant_agent","model_client":{"provider":"autogen_ext.models.openai.OpenAIChatCompletionClient","component_type":"model","version":1,"component_version":1,"description":"Chat completion client for OpenAI hosted models.","label":"OpenAIChatCompletionClient","config":{"model":"gpt-4o-mini"}},"workbench":[{"provider":"autogen_core.tools.StaticWorkbench","component_type":"workbench","version":1,"component_version":1,"description":"A workbench that provides a static set of tools that do not change after\\n    each tool execution.","label":"StaticWorkbench","config":{"tools":[]}}],"model_context":{"provider":"autogen_core.model_context.UnboundedChatCompletionContext","component_type":"chat_completion_context","version":1,"component_version":1,"description":"An unbounded chat completion context that keeps a view of the all the messages.","label":"UnboundedChatCompletionContext","config":{}},"description":"an agent that verifies and summarizes information","system_message":"You are a task verification assistant who is working with a web surfer agent to solve tasks. At each point, check if the task has been completed as requested by the user. If the websurfer_agent responds and the task has not yet been completed, respond with what is left to do and then say \'keep going\'. If and only when the task has been completed, summarize and present a final answer that directly addresses the user task in detail and then respond with TERMINATE.","model_client_stream":false,"reflect_on_tool_use":false,"tool_call_summary_format":"{result}","metadata":{}}},{"provider":"autogen_agentchat.agents.UserProxyAgent","component_type":"agent","version":1,"component_version":1,"description":"An agent that can represent a human user through an input function.","label":"UserProxyAgent","config":{"name":"user_proxy","description":"a human user that should be consulted only when the assistant_agent is unable to verify the information provided by the websurfer_agent"}}],"models":[{"provider":"autogen_ext.models.openai.OpenAIChatCompletionClient","component_type":"model","version":1,"component_version":1,"description":"OpenAI GPT-4o-mini","label":"OpenAI GPT-4o Mini","config":{"model":"gpt-4o-mini"}},{"provider":"autogen_ext.models.openai.OpenAIChatCompletionClient","component_type":"model","version":1,"component_version":1,"description":"Local Mistral-7B model client for instruction-based generation (Ollama, LMStudio).","label":"Mistral-7B Local","config":{"model":"TheBloke/Mistral-7B-Instruct-v0.2-GGUF","model_info":{"vision":false,"function_calling":true,"json_output":false,"family":"unknown","structured_output":false},"base_url":"http://localhost:1234/v1"}},{"provider":"autogen_ext.models.anthropic.AnthropicChatCompletionClient","component_type":"model","version":1,"component_version":1,"description":"Anthropic Claude-3 model client.","label":"Anthropic Claude-3-7","config":{"model":"claude-3-7-sonnet-20250219","max_tokens":4096,"temperature":1}},{"provider":"autogen_ext.models.openai.AzureOpenAIChatCompletionClient","component_type":"model","version":1,"component_version":1,"description":"GPT-4o Mini Azure OpenAI model client.","label":"AzureOpenAI GPT-4o-mini","config":{"model":"gpt-4o-mini","api_key":"**********","azure_endpoint":"https://{your-custom-endpoint}.openai.azure.com/","azure_deployment":"{your-azure-deployment}","api_version":"2024-06-01"}}],"tools":[{"provider":"autogen_core.tools.FunctionTool","component_type":"tool","version":1,"component_version":1,"description":"A tool that performs basic arithmetic operations (addition, subtraction, multiplication, division).","label":"Calculator Tool","config":{"source_code":"def calculator(a: float, b: float, operator: str) -> str:\\n    try:\\n        if operator == \\"+\\":\\n            return str(a + b)\\n        elif operator == \\"-\\":\\n            return str(a - b)\\n        elif operator == \\"*\\":\\n            return str(a * b)\\n        elif operator == \\"/\\":\\n            if b == 0:\\n                return \\"Error: Division by zero\\"\\n            return str(a / b)\\n        else:\\n            return \\"Error: Invalid operator. Please use +, -, *, or /\\"\\n    except Exception as e:\\n        return f\\"Error: {str(e)}\\"\\n","name":"calculator","description":"A simple calculator that performs basic arithmetic operations","global_imports":[],"has_cancellation_support":false}},{"provider":"autogen_core.tools.FunctionTool","component_type":"tool","version":1,"component_version":1,"description":"A tool that generates images based on a text description using OpenAI\'s DALL-E model. Note: Requires OpenAI API key to function.","label":"Image Generation Tool","config":{"source_code":"async def generate_image(\\n    query: str, output_dir: Optional[Path] = None, image_size: Literal[\\"1024x1024\\", \\"512x512\\", \\"256x256\\"] = \\"1024x1024\\"\\n) -> List[str]:\\n    \\"\\"\\"\\n    Generate images using OpenAI\'s DALL-E model based on a text description.\\n\\n    Args:\\n        query: Natural language description of the desired image\\n        output_dir: Directory to save generated images (default: current directory)\\n        image_size: Size of generated image (1024x1024, 512x512, or 256x256)\\n\\n    Returns:\\n        List[str]: Paths to the generated image files\\n    \\"\\"\\"\\n    # Initialize the OpenAI client\\n    client = OpenAI()\\n\\n    # Generate images using DALL-E 3\\n    response = client.images.generate(model=\\"dall-e-3\\", prompt=query, n=1, response_format=\\"b64_json\\", size=image_size)\\n\\n    saved_files = []\\n\\n    # Process the response\\n    if response.data:\\n        for image_data in response.data:\\n            # Generate a unique filename\\n            file_name: str = f\\"{uuid.uuid4()}.png\\"\\n\\n            # Use output_dir if provided, otherwise use current directory\\n            file_path = Path(output_dir) / file_name if output_dir else Path(file_name)\\n\\n            base64_str = image_data.b64_json\\n            if base64_str:\\n                img = Image.open(io.BytesIO(base64.decodebytes(bytes(base64_str, \\"utf-8\\"))))\\n                # Save the image to a file\\n                img.save(file_path)\\n                saved_files.append(str(file_path))\\n\\n    return saved_files\\n","name":"generate_image","description":"Generate images using DALL-E based on text descriptions.","global_imports":["io","uuid","base64",{"module":"typing","imports":["List","Optional","Literal"]},{"module":"pathlib","imports":["Path"]},{"module":"openai","imports":["OpenAI"]},{"module":"PIL","imports":["Image"]}],"has_cancellation_support":false}},{"provider":"autogen_core.tools.FunctionTool","component_type":"tool","version":1,"component_version":1,"description":"A tool that fetches the content of a webpage and converts it to markdown. Requires the requests and beautifulsoup4 library to function.","label":"Fetch Webpage Tool","config":{"source_code":"async def fetch_webpage(\\n    url: str, include_images: bool = True, max_length: Optional[int] = None, headers: Optional[Dict[str, str]] = None\\n) -> str:\\n    \\"\\"\\"Fetch a webpage and convert it to markdown format.\\n\\n    Args:\\n        url: The URL of the webpage to fetch\\n        include_images: Whether to include image references in the markdown\\n        max_length: Maximum length of the output markdown (if None, no limit)\\n        headers: Optional HTTP headers for the request\\n\\n    Returns:\\n        str: Markdown version of the webpage content\\n\\n    Raises:\\n        ValueError: If the URL is invalid or the page can\'t be fetched\\n    \\"\\"\\"\\n    # Use default headers if none provided\\n    if headers is None:\\n        headers = {\\"User-Agent\\": \\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36\\"}\\n\\n    try:\\n        # Fetch the webpage\\n        async with httpx.AsyncClient() as client:\\n            response = await client.get(url, headers=headers, timeout=10)\\n            response.raise_for_status()\\n\\n            # Parse HTML\\n            soup = BeautifulSoup(response.text, \\"html.parser\\")\\n\\n            # Remove script and style elements\\n            for script in soup([\\"script\\", \\"style\\"]):\\n                script.decompose()\\n\\n            # Convert relative URLs to absolute\\n            for tag in soup.find_all([\\"a\\", \\"img\\"]):\\n                if tag.get(\\"href\\"):\\n                    tag[\\"href\\"] = urljoin(url, tag[\\"href\\"])\\n                if tag.get(\\"src\\"):\\n                    tag[\\"src\\"] = urljoin(url, tag[\\"src\\"])\\n\\n            # Configure HTML to Markdown converter\\n            h2t = html2text.HTML2Text()\\n            h2t.body_width = 0  # No line wrapping\\n            h2t.ignore_images = not include_images\\n            h2t.ignore_emphasis = False\\n            h2t.ignore_links = False\\n            h2t.ignore_tables = False\\n\\n            # Convert to markdown\\n            markdown = h2t.handle(str(soup))\\n\\n            # Trim if max_length is specified\\n            if max_length and len(markdown) > max_length:\\n                markdown = markdown[:max_length] + \\"\\\\n...(truncated)\\"\\n\\n            return markdown.strip()\\n\\n    except httpx.RequestError as e:\\n        raise ValueError(f\\"Failed to fetch webpage: {str(e)}\\") from e\\n    except Exception as e:\\n        raise ValueError(f\\"Error processing webpage: {str(e)}\\") from e\\n","name":"fetch_webpage","description":"Fetch a webpage and convert it to markdown format, with options for including images and limiting length","global_imports":["os","html2text",{"module":"typing","imports":["Optional","Dict"]},"httpx",{"module":"bs4","imports":["BeautifulSoup"]},{"module":"html2text","imports":["HTML2Text"]},{"module":"urllib.parse","imports":["urljoin"]}],"has_cancellation_support":false}},{"provider":"autogen_core.tools.FunctionTool","component_type":"tool","version":1,"component_version":1,"description":"A tool that performs Bing searches using the Bing Web Search API. Requires the requests library, BING_SEARCH_KEY env variable to function.","label":"Bing Search Tool","config":{"source_code":"async def bing_search(\\n    query: str,\\n    num_results: int = 3,\\n    include_snippets: bool = True,\\n    include_content: bool = True,\\n    content_max_length: Optional[int] = 10000,\\n    language: str = \\"en\\",\\n    country: Optional[str] = None,\\n    safe_search: str = \\"moderate\\",\\n    response_filter: str = \\"webpages\\",\\n) -> List[Dict[str, str]]:\\n    \\"\\"\\"\\n    Perform a Bing search using the Bing Web Search API.\\n\\n    Args:\\n        query: Search query string\\n        num_results: Number of results to return (max 50)\\n        include_snippets: Include result snippets in output\\n        include_content: Include full webpage content in markdown format\\n        content_max_length: Maximum length of webpage content (if included)\\n        language: Language code for search results (e.g., \'en\', \'es\', \'fr\')\\n        country: Optional market code for search results (e.g., \'us\', \'uk\')\\n        safe_search: SafeSearch setting (\'off\', \'moderate\', or \'strict\')\\n        response_filter: Type of results (\'webpages\', \'news\', \'images\', or \'videos\')\\n\\n    Returns:\\n        List[Dict[str, str]]: List of search results\\n\\n    Raises:\\n        ValueError: If API credentials are invalid or request fails\\n    \\"\\"\\"\\n    # Get and validate API key\\n    api_key = os.getenv(\\"BING_SEARCH_KEY\\", \\"\\").strip()\\n\\n    if not api_key:\\n        raise ValueError(\\n            \\"BING_SEARCH_KEY environment variable is not set. \\" \\"Please obtain an API key from Azure Portal.\\"\\n        )\\n\\n    # Validate safe_search parameter\\n    valid_safe_search = [\\"off\\", \\"moderate\\", \\"strict\\"]\\n    if safe_search.lower() not in valid_safe_search:\\n        raise ValueError(f\\"Invalid safe_search value. Must be one of: {\', \'.join(valid_safe_search)}\\")\\n\\n    # Validate response_filter parameter\\n    valid_filters = [\\"webpages\\", \\"news\\", \\"images\\", \\"videos\\"]\\n    if response_filter.lower() not in valid_filters:\\n        raise ValueError(f\\"Invalid response_filter value. Must be one of: {\', \'.join(valid_filters)}\\")\\n\\n    async def fetch_page_content(url: str, max_length: Optional[int] = 50000) -> str:\\n        \\"\\"\\"Helper function to fetch and convert webpage content to markdown\\"\\"\\"\\n        headers = {\\"User-Agent\\": \\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36\\"}\\n\\n        try:\\n            async with httpx.AsyncClient() as client:\\n                response = await client.get(url, headers=headers, timeout=10)\\n                response.raise_for_status()\\n\\n                soup = BeautifulSoup(response.text, \\"html.parser\\")\\n\\n                # Remove script and style elements\\n                for script in soup([\\"script\\", \\"style\\"]):\\n                    script.decompose()\\n\\n                # Convert relative URLs to absolute\\n                for tag in soup.find_all([\\"a\\", \\"img\\"]):\\n                    if tag.get(\\"href\\"):\\n                        tag[\\"href\\"] = urljoin(url, tag[\\"href\\"])\\n                    if tag.get(\\"src\\"):\\n                        tag[\\"src\\"] = urljoin(url, tag[\\"src\\"])\\n\\n                h2t = html2text.HTML2Text()\\n                h2t.body_width = 0\\n                h2t.ignore_images = False\\n                h2t.ignore_emphasis = False\\n                h2t.ignore_links = False\\n                h2t.ignore_tables = False\\n\\n                markdown = h2t.handle(str(soup))\\n\\n                if max_length and len(markdown) > max_length:\\n                    markdown = markdown[:max_length] + \\"\\\\n...(truncated)\\"\\n\\n                return markdown.strip()\\n\\n        except Exception as e:\\n            return f\\"Error fetching content: {str(e)}\\"\\n\\n    # Build request headers and parameters\\n    headers = {\\"Ocp-Apim-Subscription-Key\\": api_key, \\"Accept\\": \\"application/json\\"}\\n\\n    params = {\\n        \\"q\\": query,\\n        \\"count\\": min(max(1, num_results), 50),\\n        \\"mkt\\": f\\"{language}-{country.upper()}\\" if country else language,\\n        \\"safeSearch\\": safe_search.capitalize(),\\n        \\"responseFilter\\": response_filter,\\n        \\"textFormat\\": \\"raw\\",\\n    }\\n\\n    # Make the request\\n    try:\\n        async with httpx.AsyncClient() as client:\\n            response = await client.get(\\n                \\"https://api.bing.microsoft.com/v7.0/search\\", headers=headers, params=params, timeout=10\\n            )\\n\\n            # Handle common error cases\\n            if response.status_code == 401:\\n                raise ValueError(\\"Authentication failed. Please verify your Bing Search API key.\\")\\n            elif response.status_code == 403:\\n                raise ValueError(\\n                    \\"Access forbidden. This could mean:\\\\n\\"\\n                    \\"1. The API key is invalid\\\\n\\"\\n                    \\"2. The API key has expired\\\\n\\"\\n                    \\"3. You\'ve exceeded your API quota\\"\\n                )\\n            elif response.status_code == 429:\\n                raise ValueError(\\"API quota exceeded. Please try again later.\\")\\n\\n            response.raise_for_status()\\n            data = response.json()\\n\\n        # Process results based on response_filter\\n        results = []\\n        if response_filter == \\"webpages\\" and \\"webPages\\" in data:\\n            items = data[\\"webPages\\"][\\"value\\"]\\n        elif response_filter == \\"news\\" and \\"news\\" in data:\\n            items = data[\\"news\\"][\\"value\\"]\\n        elif response_filter == \\"images\\" and \\"images\\" in data:\\n            items = data[\\"images\\"][\\"value\\"]\\n        elif response_filter == \\"videos\\" and \\"videos\\" in data:\\n            items = data[\\"videos\\"][\\"value\\"]\\n        else:\\n            if not any(key in data for key in [\\"webPages\\", \\"news\\", \\"images\\", \\"videos\\"]):\\n                return []  # No results found\\n            raise ValueError(f\\"No {response_filter} results found in API response\\")\\n\\n        # Extract relevant information based on result type\\n        for item in items:\\n            result = {\\"title\\": item.get(\\"name\\", \\"\\")}\\n\\n            if response_filter == \\"webpages\\":\\n                result[\\"link\\"] = item.get(\\"url\\", \\"\\")\\n                if include_snippets:\\n                    result[\\"snippet\\"] = item.get(\\"snippet\\", \\"\\")\\n                if include_content:\\n                    result[\\"content\\"] = await fetch_page_content(result[\\"link\\"], max_length=content_max_length)\\n\\n            elif response_filter == \\"news\\":\\n                result[\\"link\\"] = item.get(\\"url\\", \\"\\")\\n                if include_snippets:\\n                    result[\\"snippet\\"] = item.get(\\"description\\", \\"\\")\\n                result[\\"date\\"] = item.get(\\"datePublished\\", \\"\\")\\n                if include_content:\\n                    result[\\"content\\"] = await fetch_page_content(result[\\"link\\"], max_length=content_max_length)\\n\\n            elif response_filter == \\"images\\":\\n                result[\\"link\\"] = item.get(\\"contentUrl\\", \\"\\")\\n                result[\\"thumbnail\\"] = item.get(\\"thumbnailUrl\\", \\"\\")\\n                if include_snippets:\\n                    result[\\"snippet\\"] = item.get(\\"description\\", \\"\\")\\n\\n            elif response_filter == \\"videos\\":\\n                result[\\"link\\"] = item.get(\\"contentUrl\\", \\"\\")\\n                result[\\"thumbnail\\"] = item.get(\\"thumbnailUrl\\", \\"\\")\\n                if include_snippets:\\n                    result[\\"snippet\\"] = item.get(\\"description\\", \\"\\")\\n                result[\\"duration\\"] = item.get(\\"duration\\", \\"\\")\\n\\n            results.append(result)\\n\\n        return results[:num_results]\\n\\n    except httpx.HTTPError as e:\\n        error_msg = str(e)\\n        if \\"InvalidApiKey\\" in error_msg:\\n            raise ValueError(\\"Invalid API key. Please check your BING_SEARCH_KEY environment variable.\\") from e\\n        elif \\"KeyExpired\\" in error_msg:\\n            raise ValueError(\\"API key has expired. Please generate a new key.\\") from e\\n        else:\\n            raise ValueError(f\\"Search request failed: {error_msg}\\") from e\\n    except json.JSONDecodeError:\\n        raise ValueError(\\"Failed to parse API response. \\" \\"Please verify your API credentials and try again.\\") from None\\n    except Exception as e:\\n        raise ValueError(f\\"Unexpected error during search: {str(e)}\\") from e\\n","name":"bing_search","description":"\\n    Perform Bing searches using the Bing Web Search API. Requires BING_SEARCH_KEY environment variable.\\n    Supports web, news, image, and video searches.\\n    See function documentation for detailed setup instructions.\\n    ","global_imports":[{"module":"typing","imports":["List","Dict","Optional"]},"os","httpx","json","html2text",{"module":"bs4","imports":["BeautifulSoup"]},{"module":"urllib.parse","imports":["urljoin"]}],"has_cancellation_support":false}},{"provider":"autogen_core.tools.FunctionTool","component_type":"tool","version":1,"component_version":1,"description":"A tool that performs Google searches using the Google Custom Search API. Requires the requests library, [GOOGLE_API_KEY, GOOGLE_CSE_ID] to be set,  env variable to function.","label":"Google Search Tool","config":{"source_code":"async def google_search(\\n    query: str,\\n    num_results: int = 3,\\n    include_snippets: bool = True,\\n    include_content: bool = True,\\n    content_max_length: Optional[int] = 10000,\\n    language: str = \\"en\\",\\n    country: Optional[str] = None,\\n    safe_search: bool = True,\\n) -> List[Dict[str, str]]:\\n    \\"\\"\\"\\n    Perform a Google search using the Custom Search API and optionally fetch webpage content.\\n\\n    Args:\\n        query: Search query string\\n        num_results: Number of results to return (max 10)\\n        include_snippets: Include result snippets in output\\n        include_content: Include full webpage content in markdown format\\n        content_max_length: Maximum length of webpage content (if included)\\n        language: Language code for search results (e.g., en, es, fr)\\n        country: Optional country code for search results (e.g., us, uk)\\n        safe_search: Enable safe search filtering\\n\\n    Returns:\\n        List[Dict[str, str]]: List of search results, each containing:\\n            - title: Result title\\n            - link: Result URL\\n            - snippet: Result description (if include_snippets=True)\\n            - content: Webpage content in markdown (if include_content=True)\\n    \\"\\"\\"\\n    api_key = os.getenv(\\"GOOGLE_API_KEY\\")\\n    cse_id = os.getenv(\\"GOOGLE_CSE_ID\\")\\n\\n    if not api_key or not cse_id:\\n        raise ValueError(\\"Missing required environment variables. Please set GOOGLE_API_KEY and GOOGLE_CSE_ID.\\")\\n\\n    num_results = min(max(1, num_results), 10)\\n\\n    async def fetch_page_content(url: str, max_length: Optional[int] = 50000) -> str:\\n        \\"\\"\\"Helper function to fetch and convert webpage content to markdown\\"\\"\\"\\n        headers = {\\"User-Agent\\": \\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36\\"}\\n\\n        try:\\n            async with httpx.AsyncClient() as client:\\n                response = await client.get(url, headers=headers, timeout=10)\\n                response.raise_for_status()\\n\\n                soup = BeautifulSoup(response.text, \\"html.parser\\")\\n\\n                # Remove script and style elements\\n                for script in soup([\\"script\\", \\"style\\"]):\\n                    script.decompose()\\n\\n                # Convert relative URLs to absolute\\n                for tag in soup.find_all([\\"a\\", \\"img\\"]):\\n                    if tag.get(\\"href\\"):\\n                        tag[\\"href\\"] = urljoin(url, tag[\\"href\\"])\\n                    if tag.get(\\"src\\"):\\n                        tag[\\"src\\"] = urljoin(url, tag[\\"src\\"])\\n\\n                h2t = html2text.HTML2Text()\\n                h2t.body_width = 0\\n                h2t.ignore_images = False\\n                h2t.ignore_emphasis = False\\n                h2t.ignore_links = False\\n                h2t.ignore_tables = False\\n\\n                markdown = h2t.handle(str(soup))\\n\\n                if max_length and len(markdown) > max_length:\\n                    markdown = markdown[:max_length] + \\"\\\\n...(truncated)\\"\\n\\n                return markdown.strip()\\n\\n        except Exception as e:\\n            return f\\"Error fetching content: {str(e)}\\"\\n\\n    params = {\\n        \\"key\\": api_key,\\n        \\"cx\\": cse_id,\\n        \\"q\\": query,\\n        \\"num\\": num_results,\\n        \\"hl\\": language,\\n        \\"safe\\": \\"active\\" if safe_search else \\"off\\",\\n    }\\n\\n    if country:\\n        params[\\"gl\\"] = country\\n\\n    try:\\n        async with httpx.AsyncClient() as client:\\n            response = await client.get(\\"https://www.googleapis.com/customsearch/v1\\", params=params, timeout=10)\\n            response.raise_for_status()\\n            data = response.json()\\n\\n            results = []\\n            if \\"items\\" in data:\\n                for item in data[\\"items\\"]:\\n                    result = {\\"title\\": item.get(\\"title\\", \\"\\"), \\"link\\": item.get(\\"link\\", \\"\\")}\\n                    if include_snippets:\\n                        result[\\"snippet\\"] = item.get(\\"snippet\\", \\"\\")\\n\\n                    if include_content:\\n                        result[\\"content\\"] = await fetch_page_content(result[\\"link\\"], max_length=content_max_length)\\n\\n                    results.append(result)\\n\\n            return results\\n\\n    except httpx.RequestError as e:\\n        raise ValueError(f\\"Failed to perform search: {str(e)}\\") from e\\n    except KeyError as e:\\n        raise ValueError(f\\"Invalid API response format: {str(e)}\\") from e\\n    except Exception as e:\\n        raise ValueError(f\\"Error during search: {str(e)}\\") from e\\n","name":"google_search","description":"\\n    Perform Google searches using the Custom Search API with optional webpage content fetching.\\n    Requires GOOGLE_API_KEY and GOOGLE_CSE_ID environment variables to be set.\\n    ","global_imports":[{"module":"typing","imports":["List","Dict","Optional"]},"os","httpx","html2text",{"module":"bs4","imports":["BeautifulSoup"]},{"module":"urllib.parse","imports":["urljoin"]}],"has_cancellation_support":false}},{"provider":"autogen_ext.tools.code_execution.PythonCodeExecutionTool","component_type":"tool","version":1,"component_version":1,"description":"A tool that executes Python code in a local environment.","label":"Python Code Execution Tool","config":{"executor":{"provider":"autogen_ext.code_executors.local.LocalCommandLineCodeExecutor","component_type":"code_executor","version":1,"component_version":1,"description":"A code executor class that executes code through a local command line\\n    environment.","label":"LocalCommandLineCodeExecutor","config":{"timeout":360,"work_dir":".coding","functions_module":"functions","cleanup_temp_files":true}},"description":"Execute Python code blocks."}}],"terminations":[{"provider":"autogen_agentchat.conditions.TextMentionTermination","component_type":"termination","version":1,"component_version":1,"description":"Terminate the conversation if a specific text is mentioned.","label":"TextMentionTermination","config":{"text":"TERMINATE"}},{"provider":"autogen_agentchat.conditions.MaxMessageTermination","component_type":"termination","version":1,"component_version":1,"description":"Terminate the conversation after a maximum number of messages have been exchanged.","label":"MaxMessageTermination","config":{"max_messages":10,"include_agent_event":false}},{"provider":"autogen_agentchat.base.OrTerminationCondition","component_type":"termination","version":1,"component_version":1,"description":"Termination condition that ends the conversation when either a message contains \'TERMINATE\' or the maximum number of messages is reached.","label":"OR Termination","config":{"conditions":[{"provider":"autogen_agentchat.conditions.TextMentionTermination","component_type":"termination","version":1,"component_version":1,"description":"Terminate the conversation if a specific text is mentioned.","label":"TextMentionTermination","config":{"text":"TERMINATE"}},{"provider":"autogen_agentchat.conditions.MaxMessageTermination","component_type":"termination","version":1,"component_version":1,"description":"Terminate the conversation after a maximum number of messages have been exchanged.","label":"MaxMessageTermination","config":{"max_messages":10,"include_agent_event":false}}]}}],"teams":[{"provider":"autogen_agentchat.teams.RoundRobinGroupChat","component_type":"team","version":1,"component_version":1,"description":"A single AssistantAgent (with a calculator tool) in a RoundRobinGroupChat team. ","label":"RoundRobin Team","config":{"participants":[{"provider":"autogen_agentchat.agents.AssistantAgent","component_type":"agent","version":2,"component_version":2,"description":"An agent that provides assistance with tool use.","label":"AssistantAgent","config":{"name":"assistant_agent","model_client":{"provider":"autogen_ext.models.openai.OpenAIChatCompletionClient","component_type":"model","version":1,"component_version":1,"description":"Chat completion client for OpenAI hosted models.","label":"OpenAIChatCompletionClient","config":{"model":"gpt-4o-mini"}},"workbench":[{"provider":"autogen_core.tools.StaticWorkbench","component_type":"workbench","version":1,"component_version":1,"description":"A workbench that provides a static set of tools that do not change after\\n    each tool execution.","label":"StaticWorkbench","config":{"tools":[{"provider":"autogen_core.tools.FunctionTool","component_type":"tool","version":1,"component_version":1,"description":"Create custom tools by wrapping standard Python functions.","label":"FunctionTool","config":{"source_code":"def calculator(a: float, b: float, operator: str) -> str:\\n    try:\\n        if operator == \\"+\\":\\n            return str(a + b)\\n        elif operator == \\"-\\":\\n            return str(a - b)\\n        elif operator == \\"*\\":\\n            return str(a * b)\\n        elif operator == \\"/\\":\\n            if b == 0:\\n                return \\"Error: Division by zero\\"\\n            return str(a / b)\\n        else:\\n            return \\"Error: Invalid operator. Please use +, -, *, or /\\"\\n    except Exception as e:\\n        return f\\"Error: {str(e)}\\"\\n","name":"calculator","description":"A simple calculator that performs basic arithmetic operations","global_imports":[],"has_cancellation_support":false}}]}}],"model_context":{"provider":"autogen_core.model_context.UnboundedChatCompletionContext","component_type":"chat_completion_context","version":1,"component_version":1,"description":"An unbounded chat completion context that keeps a view of the all the messages.","label":"UnboundedChatCompletionContext","config":{}},"description":"An agent that provides assistance with ability to use tools.","system_message":"You are a helpful assistant. Solve tasks carefully. When done, say TERMINATE.","model_client_stream":false,"reflect_on_tool_use":false,"tool_call_summary_format":"{result}","metadata":{}}}],"termination_condition":{"provider":"autogen_agentchat.base.OrTerminationCondition","component_type":"termination","version":1,"component_version":1,"label":"OrTerminationCondition","config":{"conditions":[{"provider":"autogen_agentchat.conditions.TextMentionTermination","component_type":"termination","version":1,"component_version":1,"description":"Terminate the conversation if a specific text is mentioned.","label":"TextMentionTermination","config":{"text":"TERMINATE"}},{"provider":"autogen_agentchat.conditions.MaxMessageTermination","component_type":"termination","version":1,"component_version":1,"description":"Terminate the conversation after a maximum number of messages have been exchanged.","label":"MaxMessageTermination","config":{"max_messages":10,"include_agent_event":false}}]}},"emit_team_events":false}},{"provider":"autogen_agentchat.teams.SelectorGroupChat","component_type":"team","version":1,"component_version":1,"description":"A team with 2 agents - an AssistantAgent (with a calculator tool) and a CriticAgent in a SelectorGroupChat team.","label":"Selector Team","config":{"participants":[{"provider":"autogen_agentchat.agents.AssistantAgent","component_type":"agent","version":2,"component_version":2,"description":"An agent that provides assistance with tool use.","label":"AssistantAgent","config":{"name":"assistant_agent","model_client":{"provider":"autogen_ext.models.openai.OpenAIChatCompletionClient","component_type":"model","version":1,"component_version":1,"description":"Chat completion client for OpenAI hosted models.","label":"OpenAIChatCompletionClient","config":{"model":"gpt-4o-mini"}},"workbench":[{"provider":"autogen_core.tools.StaticWorkbench","component_type":"workbench","version":1,"component_version":1,"description":"A workbench that provides a static set of tools that do not change after\\n    each tool execution.","label":"StaticWorkbench","config":{"tools":[{"provider":"autogen_core.tools.FunctionTool","component_type":"tool","version":1,"component_version":1,"description":"Create custom tools by wrapping standard Python functions.","label":"FunctionTool","config":{"source_code":"def calculator(a: float, b: float, operator: str) -> str:\\n    try:\\n        if operator == \\"+\\":\\n            return str(a + b)\\n        elif operator == \\"-\\":\\n            return str(a - b)\\n        elif operator == \\"*\\":\\n            return str(a * b)\\n        elif operator == \\"/\\":\\n            if b == 0:\\n                return \\"Error: Division by zero\\"\\n            return str(a / b)\\n        else:\\n            return \\"Error: Invalid operator. Please use +, -, *, or /\\"\\n    except Exception as e:\\n        return f\\"Error: {str(e)}\\"\\n","name":"calculator","description":"A simple calculator that performs basic arithmetic operations","global_imports":[],"has_cancellation_support":false}}]}}],"model_context":{"provider":"autogen_core.model_context.UnboundedChatCompletionContext","component_type":"chat_completion_context","version":1,"component_version":1,"description":"An unbounded chat completion context that keeps a view of the all the messages.","label":"UnboundedChatCompletionContext","config":{}},"description":"An agent that provides assistance with ability to use tools.","system_message":"You are a helpful assistant. Solve tasks carefully. When done, say TERMINATE.","model_client_stream":false,"reflect_on_tool_use":false,"tool_call_summary_format":"{result}","metadata":{}}},{"provider":"autogen_agentchat.agents.AssistantAgent","component_type":"agent","version":2,"component_version":2,"description":"An agent that provides assistance with tool use.","label":"AssistantAgent","config":{"name":"critic_agent","model_client":{"provider":"autogen_ext.models.openai.OpenAIChatCompletionClient","component_type":"model","version":1,"component_version":1,"description":"Chat completion client for OpenAI hosted models.","label":"OpenAIChatCompletionClient","config":{"model":"gpt-4o-mini"}},"workbench":[{"provider":"autogen_core.tools.StaticWorkbench","component_type":"workbench","version":1,"component_version":1,"description":"A workbench that provides a static set of tools that do not change after\\n    each tool execution.","label":"StaticWorkbench","config":{"tools":[]}}],"model_context":{"provider":"autogen_core.model_context.UnboundedChatCompletionContext","component_type":"chat_completion_context","version":1,"component_version":1,"description":"An unbounded chat completion context that keeps a view of the all the messages.","label":"UnboundedChatCompletionContext","config":{}},"description":"an agent that critiques and improves the assistant\'s output","system_message":"You are a helpful assistant. Critique the assistant\'s output and suggest improvements.","model_client_stream":false,"reflect_on_tool_use":false,"tool_call_summary_format":"{result}","metadata":{}}}],"model_client":{"provider":"autogen_ext.models.openai.OpenAIChatCompletionClient","component_type":"model","version":1,"component_version":1,"description":"Chat completion client for OpenAI hosted models.","label":"OpenAIChatCompletionClient","config":{"model":"gpt-4o-mini"}},"termination_condition":{"provider":"autogen_agentchat.base.OrTerminationCondition","component_type":"termination","version":1,"component_version":1,"label":"OrTerminationCondition","config":{"conditions":[{"provider":"autogen_agentchat.conditions.TextMentionTermination","component_type":"termination","version":1,"component_version":1,"description":"Terminate the conversation if a specific text is mentioned.","label":"TextMentionTermination","config":{"text":"TERMINATE"}},{"provider":"autogen_agentchat.conditions.MaxMessageTermination","component_type":"termination","version":1,"component_version":1,"description":"Terminate the conversation after a maximum number of messages have been exchanged.","label":"MaxMessageTermination","config":{"max_messages":10,"include_agent_event":false}}]}},"selector_prompt":"You are in a role play game. The following roles are available:\\n{roles}.\\nRead the following conversation. Then select the next role from {participants} to play. Only return the role.\\n\\n{history}\\n\\nRead the above conversation. Then select the next role from {participants} to play. Only return the role.\\n","allow_repeated_speaker":false,"max_selector_attempts":3,"emit_team_events":false,"model_client_streaming":false}},{"provider":"autogen_agentchat.teams.SelectorGroupChat","component_type":"team","version":1,"component_version":1,"description":"A team with 3 agents - a Web Surfer agent that can browse the web, a Verification Assistant that verifies and summarizes information, and a User Proxy that provides human feedback when needed.","label":"Web Agent Team (Operator)","config":{"participants":[{"provider":"autogen_ext.agents.web_surfer.MultimodalWebSurfer","component_type":"agent","version":1,"component_version":1,"description":"MultimodalWebSurfer is a multimodal agent that acts as a web surfer that can search the web and visit web pages.","label":"MultimodalWebSurfer","config":{"name":"websurfer_agent","model_client":{"provider":"autogen_ext.models.openai.OpenAIChatCompletionClient","component_type":"model","version":1,"component_version":1,"description":"Chat completion client for OpenAI hosted models.","label":"OpenAIChatCompletionClient","config":{"model":"gpt-4o-mini"}},"description":"an agent that solves tasks by browsing the web","headless":true,"start_page":"https://www.bing.com/","animate_actions":false,"to_save_screenshots":false,"use_ocr":false,"to_resize_viewport":true}},{"provider":"autogen_agentchat.agents.AssistantAgent","component_type":"agent","version":2,"component_version":2,"description":"An agent that provides assistance with tool use.","label":"AssistantAgent","config":{"name":"assistant_agent","model_client":{"provider":"autogen_ext.models.openai.OpenAIChatCompletionClient","component_type":"model","version":1,"component_version":1,"description":"Chat completion client for OpenAI hosted models.","label":"OpenAIChatCompletionClient","config":{"model":"gpt-4o-mini"}},"workbench":[{"provider":"autogen_core.tools.StaticWorkbench","component_type":"workbench","version":1,"component_version":1,"description":"A workbench that provides a static set of tools that do not change after\\n    each tool execution.","label":"StaticWorkbench","config":{"tools":[]}}],"model_context":{"provider":"autogen_core.model_context.UnboundedChatCompletionContext","component_type":"chat_completion_context","version":1,"component_version":1,"description":"An unbounded chat completion context that keeps a view of the all the messages.","label":"UnboundedChatCompletionContext","config":{}},"description":"an agent that verifies and summarizes information","system_message":"You are a task verification assistant who is working with a web surfer agent to solve tasks. At each point, check if the task has been completed as requested by the user. If the websurfer_agent responds and the task has not yet been completed, respond with what is left to do and then say \'keep going\'. If and only when the task has been completed, summarize and present a final answer that directly addresses the user task in detail and then respond with TERMINATE.","model_client_stream":false,"reflect_on_tool_use":false,"tool_call_summary_format":"{result}","metadata":{}}},{"provider":"autogen_agentchat.agents.UserProxyAgent","component_type":"agent","version":1,"component_version":1,"description":"An agent that can represent a human user through an input function.","label":"UserProxyAgent","config":{"name":"user_proxy","description":"a human user that should be consulted only when the assistant_agent is unable to verify the information provided by the websurfer_agent"}}],"model_client":{"provider":"autogen_ext.models.openai.OpenAIChatCompletionClient","component_type":"model","version":1,"component_version":1,"description":"Chat completion client for OpenAI hosted models.","label":"OpenAIChatCompletionClient","config":{"model":"gpt-4o-mini"}},"termination_condition":{"provider":"autogen_agentchat.base.OrTerminationCondition","component_type":"termination","version":1,"component_version":1,"label":"OrTerminationCondition","config":{"conditions":[{"provider":"autogen_agentchat.conditions.MaxMessageTermination","component_type":"termination","version":1,"component_version":1,"description":"Terminate the conversation after a maximum number of messages have been exchanged.","label":"MaxMessageTermination","config":{"max_messages":20,"include_agent_event":false}},{"provider":"autogen_agentchat.conditions.TextMentionTermination","component_type":"termination","version":1,"component_version":1,"description":"Terminate the conversation if a specific text is mentioned.","label":"TextMentionTermination","config":{"text":"TERMINATE"}}]}},"selector_prompt":"You are the cordinator of role play game. The following roles are available:\\n{roles}. Given a task, the websurfer_agent will be tasked to address it by browsing the web and providing information.  The assistant_agent will be tasked with verifying the information provided by the websurfer_agent and summarizing the information to present a final answer to the user. If the task  needs assistance from a human user (e.g., providing feedback, preferences, or the task is stalled), you should select the user_proxy role to provide the necessary information.\\n\\nRead the following conversation. Then select the next role from {participants} to play. Only return the role.\\n\\n{history}\\n\\nRead the above conversation. Then select the next role from {participants} to play. Only return the role.","allow_repeated_speaker":false,"max_selector_attempts":3,"emit_team_events":false,"model_client_streaming":false}},{"provider":"autogen_agentchat.teams.SelectorGroupChat","component_type":"team","version":1,"component_version":1,"description":"A team with 3 agents - a Research Assistant that performs web searches and analyzes information, a Verifier that ensures research quality and completeness, and a Summary Agent that provides a detailed markdown summary of the research as a report to the user.","label":"Deep Research Team","config":{"participants":[{"provider":"autogen_agentchat.agents.AssistantAgent","component_type":"agent","version":2,"component_version":2,"description":"An agent that provides assistance with tool use.","label":"AssistantAgent","config":{"name":"research_assistant","model_client":{"provider":"autogen_ext.models.openai.OpenAIChatCompletionClient","component_type":"model","version":1,"component_version":1,"description":"Chat completion client for OpenAI hosted models.","label":"OpenAIChatCompletionClient","config":{"temperature":0.7,"model":"gpt-4o"}},"workbench":[{"provider":"autogen_core.tools.StaticWorkbench","component_type":"workbench","version":1,"component_version":1,"description":"A workbench that provides a static set of tools that do not change after\\n    each tool execution.","label":"StaticWorkbench","config":{"tools":[{"provider":"autogen_core.tools.FunctionTool","component_type":"tool","version":1,"component_version":1,"description":"Create custom tools by wrapping standard Python functions.","label":"FunctionTool","config":{"source_code":"async def google_search(\\n    query: str,\\n    num_results: int = 3,\\n    include_snippets: bool = True,\\n    include_content: bool = True,\\n    content_max_length: Optional[int] = 10000,\\n    language: str = \\"en\\",\\n    country: Optional[str] = None,\\n    safe_search: bool = True,\\n) -> List[Dict[str, str]]:\\n    \\"\\"\\"\\n    Perform a Google search using the Custom Search API and optionally fetch webpage content.\\n\\n    Args:\\n        query: Search query string\\n        num_results: Number of results to return (max 10)\\n        include_snippets: Include result snippets in output\\n        include_content: Include full webpage content in markdown format\\n        content_max_length: Maximum length of webpage content (if included)\\n        language: Language code for search results (e.g., en, es, fr)\\n        country: Optional country code for search results (e.g., us, uk)\\n        safe_search: Enable safe search filtering\\n\\n    Returns:\\n        List[Dict[str, str]]: List of search results, each containing:\\n            - title: Result title\\n            - link: Result URL\\n            - snippet: Result description (if include_snippets=True)\\n            - content: Webpage content in markdown (if include_content=True)\\n    \\"\\"\\"\\n    api_key = os.getenv(\\"GOOGLE_API_KEY\\")\\n    cse_id = os.getenv(\\"GOOGLE_CSE_ID\\")\\n\\n    if not api_key or not cse_id:\\n        raise ValueError(\\"Missing required environment variables. Please set GOOGLE_API_KEY and GOOGLE_CSE_ID.\\")\\n\\n    num_results = min(max(1, num_results), 10)\\n\\n    async def fetch_page_content(url: str, max_length: Optional[int] = 50000) -> str:\\n        \\"\\"\\"Helper function to fetch and convert webpage content to markdown\\"\\"\\"\\n        headers = {\\"User-Agent\\": \\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36\\"}\\n\\n        try:\\n            async with httpx.AsyncClient() as client:\\n                response = await client.get(url, headers=headers, timeout=10)\\n                response.raise_for_status()\\n\\n                soup = BeautifulSoup(response.text, \\"html.parser\\")\\n\\n                # Remove script and style elements\\n                for script in soup([\\"script\\", \\"style\\"]):\\n                    script.decompose()\\n\\n                # Convert relative URLs to absolute\\n                for tag in soup.find_all([\\"a\\", \\"img\\"]):\\n                    if tag.get(\\"href\\"):\\n                        tag[\\"href\\"] = urljoin(url, tag[\\"href\\"])\\n                    if tag.get(\\"src\\"):\\n                        tag[\\"src\\"] = urljoin(url, tag[\\"src\\"])\\n\\n                h2t = html2text.HTML2Text()\\n                h2t.body_width = 0\\n                h2t.ignore_images = False\\n                h2t.ignore_emphasis = False\\n                h2t.ignore_links = False\\n                h2t.ignore_tables = False\\n\\n                markdown = h2t.handle(str(soup))\\n\\n                if max_length and len(markdown) > max_length:\\n                    markdown = markdown[:max_length] + \\"\\\\n...(truncated)\\"\\n\\n                return markdown.strip()\\n\\n        except Exception as e:\\n            return f\\"Error fetching content: {str(e)}\\"\\n\\n    params = {\\n        \\"key\\": api_key,\\n        \\"cx\\": cse_id,\\n        \\"q\\": query,\\n        \\"num\\": num_results,\\n        \\"hl\\": language,\\n        \\"safe\\": \\"active\\" if safe_search else \\"off\\",\\n    }\\n\\n    if country:\\n        params[\\"gl\\"] = country\\n\\n    try:\\n        async with httpx.AsyncClient() as client:\\n            response = await client.get(\\"https://www.googleapis.com/customsearch/v1\\", params=params, timeout=10)\\n            response.raise_for_status()\\n            data = response.json()\\n\\n            results = []\\n            if \\"items\\" in data:\\n                for item in data[\\"items\\"]:\\n                    result = {\\"title\\": item.get(\\"title\\", \\"\\"), \\"link\\": item.get(\\"link\\", \\"\\")}\\n                    if include_snippets:\\n                        result[\\"snippet\\"] = item.get(\\"snippet\\", \\"\\")\\n\\n                    if include_content:\\n                        result[\\"content\\"] = await fetch_page_content(result[\\"link\\"], max_length=content_max_length)\\n\\n                    results.append(result)\\n\\n            return results\\n\\n    except httpx.RequestError as e:\\n        raise ValueError(f\\"Failed to perform search: {str(e)}\\") from e\\n    except KeyError as e:\\n        raise ValueError(f\\"Invalid API response format: {str(e)}\\") from e\\n    except Exception as e:\\n        raise ValueError(f\\"Error during search: {str(e)}\\") from e\\n","name":"google_search","description":"\\n    Perform Google searches using the Custom Search API with optional webpage content fetching.\\n    Requires GOOGLE_API_KEY and GOOGLE_CSE_ID environment variables to be set.\\n    ","global_imports":[{"module":"typing","imports":["List","Dict","Optional"]},"os","httpx","html2text",{"module":"bs4","imports":["BeautifulSoup"]},{"module":"urllib.parse","imports":["urljoin"]}],"has_cancellation_support":false}},{"provider":"autogen_core.tools.FunctionTool","component_type":"tool","version":1,"component_version":1,"description":"Create custom tools by wrapping standard Python functions.","label":"FunctionTool","config":{"source_code":"async def fetch_webpage(\\n    url: str, include_images: bool = True, max_length: Optional[int] = None, headers: Optional[Dict[str, str]] = None\\n) -> str:\\n    \\"\\"\\"Fetch a webpage and convert it to markdown format.\\n\\n    Args:\\n        url: The URL of the webpage to fetch\\n        include_images: Whether to include image references in the markdown\\n        max_length: Maximum length of the output markdown (if None, no limit)\\n        headers: Optional HTTP headers for the request\\n\\n    Returns:\\n        str: Markdown version of the webpage content\\n\\n    Raises:\\n        ValueError: If the URL is invalid or the page can\'t be fetched\\n    \\"\\"\\"\\n    # Use default headers if none provided\\n    if headers is None:\\n        headers = {\\"User-Agent\\": \\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36\\"}\\n\\n    try:\\n        # Fetch the webpage\\n        async with httpx.AsyncClient() as client:\\n            response = await client.get(url, headers=headers, timeout=10)\\n            response.raise_for_status()\\n\\n            # Parse HTML\\n            soup = BeautifulSoup(response.text, \\"html.parser\\")\\n\\n            # Remove script and style elements\\n            for script in soup([\\"script\\", \\"style\\"]):\\n                script.decompose()\\n\\n            # Convert relative URLs to absolute\\n            for tag in soup.find_all([\\"a\\", \\"img\\"]):\\n                if tag.get(\\"href\\"):\\n                    tag[\\"href\\"] = urljoin(url, tag[\\"href\\"])\\n                if tag.get(\\"src\\"):\\n                    tag[\\"src\\"] = urljoin(url, tag[\\"src\\"])\\n\\n            # Configure HTML to Markdown converter\\n            h2t = html2text.HTML2Text()\\n            h2t.body_width = 0  # No line wrapping\\n            h2t.ignore_images = not include_images\\n            h2t.ignore_emphasis = False\\n            h2t.ignore_links = False\\n            h2t.ignore_tables = False\\n\\n            # Convert to markdown\\n            markdown = h2t.handle(str(soup))\\n\\n            # Trim if max_length is specified\\n            if max_length and len(markdown) > max_length:\\n                markdown = markdown[:max_length] + \\"\\\\n...(truncated)\\"\\n\\n            return markdown.strip()\\n\\n    except httpx.RequestError as e:\\n        raise ValueError(f\\"Failed to fetch webpage: {str(e)}\\") from e\\n    except Exception as e:\\n        raise ValueError(f\\"Error processing webpage: {str(e)}\\") from e\\n","name":"fetch_webpage","description":"Fetch a webpage and convert it to markdown format, with options for including images and limiting length","global_imports":["os","html2text",{"module":"typing","imports":["Optional","Dict"]},"httpx",{"module":"bs4","imports":["BeautifulSoup"]},{"module":"html2text","imports":["HTML2Text"]},{"module":"urllib.parse","imports":["urljoin"]}],"has_cancellation_support":false}}]}}],"model_context":{"provider":"autogen_core.model_context.UnboundedChatCompletionContext","component_type":"chat_completion_context","version":1,"component_version":1,"description":"An unbounded chat completion context that keeps a view of the all the messages.","label":"UnboundedChatCompletionContext","config":{}},"description":"A research assistant that performs web searches and analyzes information","system_message":"You are a research assistant focused on finding accurate information.\\n        Use the google_search tool to find relevant information.\\n        Break down complex queries into specific search terms.\\n        Always verify information across multiple sources when possible.\\n        When you find relevant information, explain why it\'s relevant and how it connects to the query. When you get feedback from the a verifier agent, use your tools to act on the feedback and make progress.","model_client_stream":false,"reflect_on_tool_use":false,"tool_call_summary_format":"{result}","metadata":{}}},{"provider":"autogen_agentchat.agents.AssistantAgent","component_type":"agent","version":2,"component_version":2,"description":"An agent that provides assistance with tool use.","label":"AssistantAgent","config":{"name":"verifier","model_client":{"provider":"autogen_ext.models.openai.OpenAIChatCompletionClient","component_type":"model","version":1,"component_version":1,"description":"Chat completion client for OpenAI hosted models.","label":"OpenAIChatCompletionClient","config":{"temperature":0.7,"model":"gpt-4o"}},"workbench":[{"provider":"autogen_core.tools.StaticWorkbench","component_type":"workbench","version":1,"component_version":1,"description":"A workbench that provides a static set of tools that do not change after\\n    each tool execution.","label":"StaticWorkbench","config":{"tools":[]}}],"model_context":{"provider":"autogen_core.model_context.UnboundedChatCompletionContext","component_type":"chat_completion_context","version":1,"component_version":1,"description":"An unbounded chat completion context that keeps a view of the all the messages.","label":"UnboundedChatCompletionContext","config":{}},"description":"A verification specialist who ensures research quality and completeness","system_message":"You are a research verification specialist.\\n        Your role is to:\\n        1. Verify that search queries are effective and suggest improvements if needed\\n        2. Explore drill downs where needed e.g, if the answer is likely in a link in the returned search results, suggest clicking on the link\\n        3. Suggest additional angles or perspectives to explore. Be judicious in suggesting new paths to avoid scope creep or wasting resources, if the task appears to be addressed and we can provide a report, do this and respond with \\"TERMINATE\\".\\n        4. Track progress toward answering the original question\\n        5. When the research is complete, provide a detailed summary in markdown format. For incomplete research, end your message with \\"CONTINUE RESEARCH\\". For complete research, end your message with APPROVED.\\n        Your responses should be structured as:\\n        - Progress Assessment\\n        - Gaps/Issues (if any)\\n        - Suggestions (if needed)\\n        - Next Steps or Final Summary","model_client_stream":false,"reflect_on_tool_use":false,"tool_call_summary_format":"{result}","metadata":{}}},{"provider":"autogen_agentchat.agents.AssistantAgent","component_type":"agent","version":2,"component_version":2,"description":"An agent that provides assistance with tool use.","label":"AssistantAgent","config":{"name":"summary_agent","model_client":{"provider":"autogen_ext.models.openai.OpenAIChatCompletionClient","component_type":"model","version":1,"component_version":1,"description":"Chat completion client for OpenAI hosted models.","label":"OpenAIChatCompletionClient","config":{"temperature":0.7,"model":"gpt-4o"}},"workbench":[{"provider":"autogen_core.tools.StaticWorkbench","component_type":"workbench","version":1,"component_version":1,"description":"A workbench that provides a static set of tools that do not change after\\n    each tool execution.","label":"StaticWorkbench","config":{"tools":[]}}],"model_context":{"provider":"autogen_core.model_context.UnboundedChatCompletionContext","component_type":"chat_completion_context","version":1,"component_version":1,"description":"An unbounded chat completion context that keeps a view of the all the messages.","label":"UnboundedChatCompletionContext","config":{}},"description":"A summary agent that provides a detailed markdown summary of the research as a report to the user.","system_message":"You are a summary agent. Your role is to provide a detailed markdown summary of the research as a report to the user. Your report should have a reasonable title that matches the research question and should summarize the key details in the results found in natural an actionable manner. The main results/answer should be in the first paragraph. Where reasonable, your report should have clear comparison tables that drive critical insights. Most importantly, you should have a reference section and cite the key sources (where available) for facts obtained INSIDE THE MAIN REPORT. Also, where appropriate, you may add images if available that illustrate concepts needed for the summary.\\n        Your report should end with the word \\"TERMINATE\\" to signal the end of the conversation.","model_client_stream":false,"reflect_on_tool_use":false,"tool_call_summary_format":"{result}","metadata":{}}}],"model_client":{"provider":"autogen_ext.models.openai.OpenAIChatCompletionClient","component_type":"model","version":1,"component_version":1,"description":"Chat completion client for OpenAI hosted models.","label":"OpenAIChatCompletionClient","config":{"temperature":0.7,"model":"gpt-4o"}},"termination_condition":{"provider":"autogen_agentchat.base.OrTerminationCondition","component_type":"termination","version":1,"component_version":1,"label":"OrTerminationCondition","config":{"conditions":[{"provider":"autogen_agentchat.conditions.TextMentionTermination","component_type":"termination","version":1,"component_version":1,"description":"Terminate the conversation if a specific text is mentioned.","label":"TextMentionTermination","config":{"text":"TERMINATE"}},{"provider":"autogen_agentchat.conditions.MaxMessageTermination","component_type":"termination","version":1,"component_version":1,"description":"Terminate the conversation after a maximum number of messages have been exchanged.","label":"MaxMessageTermination","config":{"max_messages":30,"include_agent_event":false}}]}},"selector_prompt":"You are coordinating a research team by selecting the team member to speak/act next. The following team member roles are available:\\n    {roles}.\\n    The research_assistant performs searches and analyzes information.\\n    The verifier evaluates progress and ensures completeness.\\n    The summary_agent provides a detailed markdown summary of the research as a report to the user.\\n\\n    Given the current context, select the most appropriate next speaker.\\n    The research_assistant should search and analyze.\\n    The verifier should evaluate progress and guide the research (select this role is there is a need to verify/evaluate progress). You should ONLY select the summary_agent role if the research is complete and it is time to generate a report.\\n\\n    Base your selection on:\\n    1. Current stage of research\\n    2. Last speaker\'s findings or suggestions\\n    3. Need for verification vs need for new information\\n    Read the following conversation. Then select the next role from {participants} to play. Only return the role.\\n\\n    {history}\\n\\n    Read the above conversation. Then select the next role from {participants} to play. ONLY RETURN THE ROLE.","allow_repeated_speaker":true,"max_selector_attempts":3,"emit_team_events":false,"model_client_streaming":false}}],"workbenches":[{"provider":"autogen_core.tools.StaticWorkbench","component_type":"workbench","version":1,"component_version":1,"description":"A static workbench containing basic tools like calculator and webpage fetcher for common tasks.","label":"Basic Tools Workbench","config":{"tools":[{"provider":"autogen_core.tools.FunctionTool","component_type":"tool","version":1,"component_version":1,"description":"Create custom tools by wrapping standard Python functions.","label":"FunctionTool","config":{"source_code":"def calculator(a: float, b: float, operator: str) -> str:\\n    try:\\n        if operator == \\"+\\":\\n            return str(a + b)\\n        elif operator == \\"-\\":\\n            return str(a - b)\\n        elif operator == \\"*\\":\\n            return str(a * b)\\n        elif operator == \\"/\\":\\n            if b == 0:\\n                return \\"Error: Division by zero\\"\\n            return str(a / b)\\n        else:\\n            return \\"Error: Invalid operator. Please use +, -, *, or /\\"\\n    except Exception as e:\\n        return f\\"Error: {str(e)}\\"\\n","name":"calculator","description":"A simple calculator that performs basic arithmetic operations","global_imports":[],"has_cancellation_support":false}},{"provider":"autogen_core.tools.FunctionTool","component_type":"tool","version":1,"component_version":1,"description":"Create custom tools by wrapping standard Python functions.","label":"FunctionTool","config":{"source_code":"async def fetch_webpage(\\n    url: str, include_images: bool = True, max_length: Optional[int] = None, headers: Optional[Dict[str, str]] = None\\n) -> str:\\n    \\"\\"\\"Fetch a webpage and convert it to markdown format.\\n\\n    Args:\\n        url: The URL of the webpage to fetch\\n        include_images: Whether to include image references in the markdown\\n        max_length: Maximum length of the output markdown (if None, no limit)\\n        headers: Optional HTTP headers for the request\\n\\n    Returns:\\n        str: Markdown version of the webpage content\\n\\n    Raises:\\n        ValueError: If the URL is invalid or the page can\'t be fetched\\n    \\"\\"\\"\\n    # Use default headers if none provided\\n    if headers is None:\\n        headers = {\\"User-Agent\\": \\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36\\"}\\n\\n    try:\\n        # Fetch the webpage\\n        async with httpx.AsyncClient() as client:\\n            response = await client.get(url, headers=headers, timeout=10)\\n            response.raise_for_status()\\n\\n            # Parse HTML\\n            soup = BeautifulSoup(response.text, \\"html.parser\\")\\n\\n            # Remove script and style elements\\n            for script in soup([\\"script\\", \\"style\\"]):\\n                script.decompose()\\n\\n            # Convert relative URLs to absolute\\n            for tag in soup.find_all([\\"a\\", \\"img\\"]):\\n                if tag.get(\\"href\\"):\\n                    tag[\\"href\\"] = urljoin(url, tag[\\"href\\"])\\n                if tag.get(\\"src\\"):\\n                    tag[\\"src\\"] = urljoin(url, tag[\\"src\\"])\\n\\n            # Configure HTML to Markdown converter\\n            h2t = html2text.HTML2Text()\\n            h2t.body_width = 0  # No line wrapping\\n            h2t.ignore_images = not include_images\\n            h2t.ignore_emphasis = False\\n            h2t.ignore_links = False\\n            h2t.ignore_tables = False\\n\\n            # Convert to markdown\\n            markdown = h2t.handle(str(soup))\\n\\n            # Trim if max_length is specified\\n            if max_length and len(markdown) > max_length:\\n                markdown = markdown[:max_length] + \\"\\\\n...(truncated)\\"\\n\\n            return markdown.strip()\\n\\n    except httpx.RequestError as e:\\n        raise ValueError(f\\"Failed to fetch webpage: {str(e)}\\") from e\\n    except Exception as e:\\n        raise ValueError(f\\"Error processing webpage: {str(e)}\\") from e\\n","name":"fetch_webpage","description":"Fetch a webpage and convert it to markdown format, with options for including images and limiting length","global_imports":["os","html2text",{"module":"typing","imports":["Optional","Dict"]},"httpx",{"module":"bs4","imports":["BeautifulSoup"]},{"module":"html2text","imports":["HTML2Text"]},{"module":"urllib.parse","imports":["urljoin"]}],"has_cancellation_support":false}}]}},{"provider":"autogen_ext.tools.mcp.McpWorkbench","component_type":"workbench","version":1,"component_version":1,"description":"An MCP workbench that provides web content fetching capabilities using the mcp-server-fetch MCP server. Allows agents to fetch and read content from web pages and APIs.","label":"Web Content Fetch Workbench","config":{"server_params":{"command":"uvx","args":["mcp-server-fetch"],"encoding":"utf-8","encoding_error_handler":"strict","type":"StdioServerParams","read_timeout_seconds":60}}},{"provider":"autogen_ext.tools.mcp.McpWorkbench","component_type":"workbench","version":1,"component_version":1,"description":"An MCP workbench that connects to HTTP-based MCP servers using Server-Sent Events (SSE). Suitable for cloud-hosted MCP services and custom HTTP MCP implementations.","label":"HTTP Streamable MCP Workbench","config":{"server_params":{"type":"StreamableHttpServerParams","url":"http://localhost:8005/mcp","headers":{"Authorization":"Bearer your-api-key","Content-Type":"application/json"},"timeout":30,"sse_read_timeout":300,"terminate_on_close":true}}}]}}')},5977:function(e,t,n){n.d(t,{A:function(){return o}});const o=(0,n(1788).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},8216:function(e,t,n){n.r(t),n.d(t,{default:function(){return fe}});var o=n(6540),a=n(1155),r=n(436),i=n(9036),s=n(6108),l=n(7677),c=n(2744),m=n(2571),p=n(367),d=n(2941),u=n(9910),h=n(697),g=n(9644),f=n(5977),_=n(7213),v=n(684),y=n(2708),b=n(7133),x=n(7163);const w=e=>{let{isOpen:t,galleries:n,currentGallery:a,onToggle:r,onSelectGallery:i,onCreateGallery:s,onDeleteGallery:l,onSyncGallery:c,isLoading:m=!1}=e;return t?o.createElement("div",{className:"h-full border-r border-secondary"},o.createElement("div",{className:"flex items-center justify-between pt-0 p-4 pl-2 pr-2 border-b border-secondary"},o.createElement("div",{className:"flex items-center gap-2"},o.createElement("span",{className:"text-primary font-medium"},"Galleries"),o.createElement("span",{className:"px-2 py-0.5 text-xs bg-accent/10 text-accent rounded"},n.length)),o.createElement(p.A,{title:"Close Sidebar"},o.createElement("button",{onClick:r,className:"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50"},o.createElement(g.A,{strokeWidth:1.5,className:"h-6 w-6"})))),o.createElement("div",{className:"my-4 flex text-sm"},o.createElement("div",{className:"mr-2 w-full"},o.createElement(p.A,{title:"Create new gallery"},o.createElement(d.Ay,{type:"primary",className:"w-full",icon:o.createElement(h.A,{className:"w-4 h-4"}),onClick:s},"New Gallery")))),o.createElement("div",{className:"py-2 flex text-sm text-secondary"},o.createElement("div",{className:"flex"},"All Galleries"),m&&o.createElement(f.A,{className:"w-4 h-4 ml-2 animate-spin"})),!m&&0===n.length&&o.createElement("div",{className:"p-2 mr-2 text-center text-secondary text-sm border border-dashed rounded"},o.createElement(_.A,{className:"w-4 h-4 inline-block mr-1.5 -mt-0.5"}),"No galleries found"),o.createElement("div",{className:"scroll overflow-y-auto h-[calc(100%-170px)]"},n.map(e=>o.createElement("div",{key:e.id,className:"relative border-secondary"},o.createElement("div",{className:"absolute top-1 left-0.5 z-50 h-[calc(100%-8px)] w-1 bg-opacity-80 rounded "+((null==a?void 0:a.id)===e.id?"bg-accent":"bg-tertiary")}),e&&e.config&&e.config.components&&o.createElement("div",{className:"group ml-1 flex flex-col p-3 rounded-l cursor-pointer hover:bg-secondary "+((null==a?void 0:a.id)===e.id?"border-accent bg-secondary":"border-transparent"),onClick:()=>i(e)},o.createElement("div",{className:"flex items-center justify-between min-w-0"},o.createElement("div",{className:"flex items-center gap-2 min-w-0 flex-1"},o.createElement("div",{className:"truncate flex-1"},o.createElement("span",{className:"font-medium text-sm"},e.config.name)),e.config.url&&o.createElement(p.A,{title:"Remote Gallery"},o.createElement(v.A,{className:"w-3 h-3 text-secondary flex-shrink-0"}))),o.createElement("div",{className:"flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity ml-2 flex-shrink-0"},e.config.url&&o.createElement(p.A,{title:"Sync gallery"},o.createElement(d.Ay,{type:"text",size:"small",className:"p-0 min-w-[24px] h-6",icon:o.createElement(f.A,{className:"w-4 h-4"}),onClick:t=>{t.stopPropagation(),c(e.id)}})),o.createElement(p.A,{title:1===n.length?"Cannot delete the last gallery":"Delete gallery"},o.createElement(d.Ay,{type:"text",size:"small",className:"p-0 min-w-[24px] h-6",danger:!0,disabled:1===n.length,icon:o.createElement(y.A,{className:"w-4 h-4 text-red-500"}),onClick:t=>{t.stopPropagation(),l(e.id)}})))),o.createElement("div",{className:"mt-1 flex items-center gap-2 text-xs text-secondary"},o.createElement("span",{className:"bg-secondary/20 truncate rounded px-1"},"v",e.config.metadata.version),o.createElement("div",{className:"flex items-center gap-1"},o.createElement(b.A,{className:"w-3 h-3"}),o.createElement("span",null,Object.values(e.config.components).reduce((e,t)=>e+t.length,0)," ","components"))),e.updated_at&&o.createElement("div",{className:"mt-1 flex items-center gap-1 text-xs text-secondary"},o.createElement("span",null,(0,x.vq)(e.updated_at)))))))):o.createElement("div",{className:"h-full border-r border-secondary"},o.createElement("div",{className:"p-2 -ml-2"},o.createElement(p.A,{title:`Galleries (${n.length})`},o.createElement("button",{onClick:r,className:"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50"},o.createElement(u.A,{strokeWidth:1.5,className:"h-6 w-6"})))),o.createElement("div",{className:"mt-4 px-2 -ml-1"},o.createElement(p.A,{title:"Create new gallery"},o.createElement(d.Ay,{type:"text",className:"w-full p-2 flex justify-center",onClick:s,icon:o.createElement(h.A,{className:"w-4 h-4"})}))))};var E=n(9957),A=n(6161),k=n(6143),C=n(1788);const T=(0,C.A)("Trash",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]]);var N=n(5404),O=n(8188),S=n(6274),I=n(2640),M=n(6816),P=n(7073),R=n(5680);const G=(0,C.A)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]]);var L=n(8309),q=n(7934),F=n(3164),U=n(6812),W=n(9872),j=n(4279);const D=[{id:"round-robin-team",label:"Round Robin Team",description:"A team where agents take turns in a fixed order",provider:j.xq.ROUND_ROBIN_TEAM,component_type:"team",version:1,component_version:1,config:{participants:[],termination_condition:{provider:j.xq.TEXT_MENTION,component_type:"termination",config:{text:"TERMINATE"}},max_turns:10}},{id:"selector-team",label:"Selector Team",description:"A team with a model that selects which agent speaks next",provider:j.xq.SELECTOR_TEAM,component_type:"team",version:1,component_version:1,config:{participants:[],model_client:{provider:j.xq.OPENAI,component_type:"model",config:{model:"gpt-4o-mini"}},termination_condition:{provider:j.xq.TEXT_MENTION,component_type:"termination",config:{text:"TERMINATE"}},max_turns:10,selector_prompt:"Select the next speaker based on the conversation context and task requirements.",allow_repeated_speaker:!0}}],z=[{id:"assistant-agent",label:"Assistant Agent",description:"A helpful AI assistant with tool capabilities",provider:j.xq.ASSISTANT_AGENT,component_type:"agent",version:2,component_version:2,config:{name:"assistant_agent",description:"A helpful AI assistant that can use tools to solve problems",system_message:"You are a helpful assistant. Solve tasks carefully and methodically. When you have completed the task, say TERMINATE.",model_client:{provider:j.xq.OPENAI,component_type:"model",config:{model:"gpt-4o-mini"}},workbench:[],reflect_on_tool_use:!1,tool_call_summary_format:"{result}",model_client_stream:!1}},{id:"user-proxy-agent",label:"User Proxy Agent",description:"An agent that represents human input and interaction",provider:j.xq.USER_PROXY,component_type:"agent",version:1,component_version:1,config:{name:"user_proxy",description:"A human user proxy for providing input and feedback"}},{id:"web-surfer-agent",label:"Web Surfer Agent",description:"An agent that can browse and interact with web pages",provider:j.xq.WEB_SURFER,component_type:"agent",version:1,component_version:1,config:{name:"web_surfer",description:"An agent that can browse the web and interact with web pages",model_client:{provider:j.xq.OPENAI,component_type:"model",config:{model:"gpt-4o"}},headless:!0,start_page:"https://www.google.com",animate_actions:!1,to_save_screenshots:!1,use_ocr:!1,to_resize_viewport:!0}}],H=[{id:"openai-gpt-4o-mini",label:"OpenAI GPT-4o Mini",description:"Fast and cost-effective OpenAI model for most tasks",provider:j.xq.OPENAI,component_type:"model",version:1,component_version:1,config:{model:"gpt-4o-mini",temperature:.7,max_tokens:4096}},{id:"openai-gpt-4o",label:"OpenAI GPT-4o",description:"Advanced OpenAI model with vision and advanced reasoning",provider:j.xq.OPENAI,component_type:"model",version:1,component_version:1,config:{model:"gpt-4o",temperature:.7,max_tokens:4096}},{id:"azure-openai-gpt-4o-mini",label:"Azure OpenAI GPT-4o Mini",description:"Azure-hosted OpenAI model for enterprise use",provider:j.xq.AZURE_OPENAI,component_type:"model",version:1,component_version:1,config:{model:"gpt-4o-mini",azure_endpoint:"https://your-endpoint.openai.azure.com/",azure_deployment:"gpt-4o-mini",api_version:"2024-06-01",temperature:.7,max_tokens:4096}},{id:"anthropic-claude-3-sonnet",label:"Anthropic Claude-3 Sonnet",description:"Anthropic's balanced model for reasoning and creativity",provider:j.xq.ANTHROPIC,component_type:"model",version:1,component_version:1,config:{model:"claude-3-5-sonnet-20241022",max_tokens:4096,temperature:.7}}],B=[{id:"function-tool",label:"Function Tool",description:"A custom Python function that can be called by agents",provider:j.xq.FUNCTION_TOOL,component_type:"tool",version:1,component_version:1,config:{name:"my_function",description:"A custom function that performs a specific task",source_code:'def my_function(input_text: str) -> str:\n    """\n    A template function that processes input text.\n    \n    Args:\n        input_text: The text to process\n    \n    Returns:\n        Processed text result\n    """\n    # Replace this with your custom function logic\n    result = f"Processed: {input_text}"\n    return result',global_imports:[],has_cancellation_support:!1}},{id:"code-execution-tool",label:"Code Execution Tool",description:"Execute Python code in a secure environment",provider:j.xq.PYTHON_CODE_EXECUTION_TOOL,component_type:"tool",version:1,component_version:1,config:{executor:{provider:"autogen_ext.code_executors.local.LocalCommandLineCodeExecutor",config:{timeout:60,work_dir:"./coding",functions_module:"functions",cleanup_temp_files:!0}},description:"Execute Python code in a secure environment",name:"code_execution"}}],V=[{id:"static-workbench",label:"Static Workbench",description:"A workbench with a collection of tools",provider:j.xq.STATIC_WORKBENCH,component_type:"workbench",version:1,component_version:1,config:{tools:[]}},{id:"mcp-stdio-workbench",label:"MCP Stdio Server",description:"Model Context Protocol server via command line",provider:j.xq.MCP_WORKBENCH,component_type:"workbench",version:1,component_version:1,config:{server_params:{type:"StdioServerParams",command:"npx",args:["@modelcontextprotocol/server-everything"],env:{},read_timeout_seconds:30}}},{id:"mcp-sse-workbench",label:"MCP SSE Server",description:"Model Context Protocol server via server-sent events",provider:j.xq.MCP_WORKBENCH,component_type:"workbench",version:1,component_version:1,config:{server_params:{type:"SseServerParams",url:"http://localhost:3001/sse",headers:{Authorization:"Bearer your-token-here"},timeout:30,sse_read_timeout:30}}},{id:"mcp-http-workbench",label:"MCP HTTP Server",description:"Model Context Protocol server via streamable HTTP",provider:j.xq.MCP_WORKBENCH,component_type:"workbench",version:1,component_version:1,config:{server_params:{type:"StreamableHttpServerParams",url:"http://localhost:3001/mcp",headers:{"Content-Type":"application/json",Authorization:"Bearer your-token-here"},timeout:30,sse_read_timeout:30,terminate_on_close:!0}}}],Y=[{id:"text-mention-termination",label:"Text Mention Termination",description:"Terminate when a specific text is mentioned",provider:j.xq.TEXT_MENTION,component_type:"termination",version:1,component_version:1,config:{text:"TERMINATE"}},{id:"max-message-termination",label:"Max Message Termination",description:"Terminate after a maximum number of messages",provider:j.xq.MAX_MESSAGE,component_type:"termination",version:1,component_version:1,config:{max_messages:10,include_agent_event:!1}},{id:"stop-message-termination",label:"Stop Message Termination",description:"Terminate when a StopMessage is received",provider:j.xq.STOP_MESSAGE,component_type:"termination",version:1,component_version:1,config:{}},{id:"token-usage-termination",label:"Token Usage Termination",description:"Terminate when token usage limits are reached",provider:j.xq.TOKEN_USAGE,component_type:"termination",version:1,component_version:1,config:{max_total_token:1e3}},{id:"timeout-termination",label:"Timeout Termination",description:"Terminate after a specified duration",provider:j.xq.TIMEOUT,component_type:"termination",version:1,component_version:1,config:{timeout_seconds:300}},{id:"handoff-termination",label:"Handoff Termination",description:"Terminate when handoff to specific target is detected",provider:j.xq.HANDOFF,component_type:"termination",version:1,component_version:1,config:{target:"user"}},{id:"source-match-termination",label:"Source Match Termination",description:"Terminate when specific sources respond",provider:j.xq.SOURCE_MATCH,component_type:"termination",version:1,component_version:1,config:{sources:["agent1"]}},{id:"text-message-termination",label:"Text Message Termination",description:"Terminate when a TextMessage is received",provider:j.xq.TEXT_MESSAGE,component_type:"termination",version:1,component_version:1,config:{source:void 0}},{id:"external-termination",label:"External Termination",description:"Terminate when externally controlled by calling set() method",provider:j.xq.EXTERNAL,component_type:"termination",version:1,component_version:1,config:{}},{id:"or-termination",label:"OR Termination",description:"Terminate when any of the conditions are met",provider:j.xq.OR_TERMINATION,component_type:"termination",version:1,component_version:1,config:{conditions:[{provider:j.xq.TEXT_MENTION,component_type:"termination",config:{text:"TERMINATE"}},{provider:j.xq.MAX_MESSAGE,component_type:"termination",config:{max_messages:20}}]}},{id:"and-termination",label:"AND Termination",description:"Terminate when all conditions are met",provider:j.xq.AND_TERMINATION,component_type:"termination",version:1,component_version:1,config:{conditions:[{provider:j.xq.TEXT_MENTION,component_type:"termination",config:{text:"TASK_COMPLETE"}},{provider:j.xq.MAX_MESSAGE,component_type:"termination",config:{max_messages:5}}]}}],K={team:D,agent:z,model:H,tool:B,workbench:V,termination:Y};function J(e,t){return K[e].find(e=>e.id===t)}function $(e,t,n){const o=J(t,e);if(!o)throw new Error(`Template ${e} not found for component type ${t}`);return{provider:o.provider,component_type:o.component_type,version:o.version,component_version:o.component_version,description:o.description,config:o.config,label:o.label,...n}}function X(e,t){const n=J("workbench",e);if(!n)throw new Error(`Workbench template ${e} not found`);return $(e,"workbench",{label:t||`New ${n.label}`})}function Z(e){const t=function(e){return K[e]||[]}(e);return t.map(e=>({key:e.id,label:e.label,description:e.description,templateId:e.id}))}function Q(e,t,n){const o=J(e,t);if(!o)throw new Error(`${e} template ${t} not found`);return $(t,e,{label:n||`New ${o.label}`})}var ee=n(5144),te=n(6647),ne=n(8603),oe=n(5107);const ae=e=>{let{componentType:t,gallery:n,onComponentAdded:a,disabled:r=!1,showIcon:s=!0,showChevron:l=!0,size:c="middle",type:m="primary",className:p="",buttonText:u,templateFilter:g}=e;const[f,_]=i.Ay.useMessage(),v=(e,t)=>{const n=(e=>({team:"teams",agent:"agents",model:"models",tool:"tools",workbench:"workbenches",termination:"terminations"}[e]))(e);try{let o;switch(e){case"team":o=function(e,t){return Q("team",e,t)}(t);break;case"agent":o=function(e,t){return Q("agent",e,t)}(t);break;case"model":o=function(e,t){return Q("model",e,t)}(t);break;case"tool":o=function(e,t){return Q("tool",e,t)}(t);break;case"workbench":o=X(t);break;case"termination":o=function(e,t){return Q("termination",e,t)}(t);break;default:throw new Error(`Unsupported component type: ${e}`)}a(o,n)}catch(o){console.error(`Error creating ${e} from template:`,o),f.error(`Failed to create ${e}`)}},y=(e=>{let t=[];switch(e){case"team":t=Z("team");break;case"agent":t=Z("agent");break;case"model":t=Z("model");break;case"tool":t=Z("tool");break;case"workbench":t=V.map(e=>({key:e.id,label:e.label,description:e.description,templateId:e.id}));break;case"termination":t=Z("termination");break;default:t=[]}return g&&(t=t.filter(g)),t})(t);if(0===y.length)return null;const b=u||`Add ${t.charAt(0).toUpperCase()+t.slice(1)}`;return o.createElement(o.Fragment,null,_,o.createElement(ne.A,{menu:{items:y.map(e=>({key:e.key,label:o.createElement("div",{className:"py-1"},o.createElement("div",{className:"font-medium"},e.label),o.createElement("div",{className:"text-xs text-secondary"},e.description)),onClick:()=>v(t,e.templateId)}))},trigger:["click"],disabled:r},o.createElement(d.Ay,{type:m,size:c,icon:s?o.createElement(h.A,{className:"w-4 h-4"}):void 0,disabled:r,className:`flex items-center gap-1 ${p}`},b,l&&o.createElement(oe.A,{className:"w-3 h-3"}))))};const re=e=>({team:"teams",agent:"agents",model:"models",tool:"tools",workbench:"workbenches",termination:"terminations"}[e]),ie=e=>{let{item:t,onEdit:n,onDuplicate:a,onDelete:r,index:i,allowDelete:s,disabled:l=!1}=e;return o.createElement("div",{className:"bg-secondary rounded overflow-hidden group h-full "+(l?"opacity-50 cursor-not-allowed":"cursor-pointer"),onClick:()=>!l&&n(t,i)},o.createElement("div",{className:"px-4 py-3 flex items-center justify-between border-b border-tertiary"},o.createElement("div",{className:"text-xs text-secondary truncate flex-1"},t.label||"Unnamed Component"),o.createElement("div",{className:"flex gap-0"},s&&o.createElement(d.Ay,{title:"Delete",type:"text",className:"h-6 w-6 flex items-center justify-center p-0 opacity-0 group-hover:opacity-100 transition-opacity text-red-500 hover:text-red-600",icon:o.createElement(T,{className:"w-3.5 h-3.5"}),disabled:l,onClick:e=>{e.stopPropagation(),l||r(t,i)}}),o.createElement(d.Ay,{title:"Duplicate",type:"text",className:"h-6 w-6 flex items-center justify-center p-0 opacity-0 group-hover:opacity-100 transition-opacity",icon:o.createElement(N.A,{className:"w-3.5 h-3.5"}),disabled:l,onClick:e=>{e.stopPropagation(),l||a(t,i)}}),o.createElement(d.Ay,{title:"Edit",type:"text",className:"h-6 w-6 flex items-center justify-center p-0 opacity-0 group-hover:opacity-100 transition-opacity",icon:o.createElement(O.A,{className:"w-3.5 h-3.5"}),disabled:l,onClick:e=>{e.stopPropagation(),l||n(t,i)}}))),o.createElement("div",{className:"p-4 pb-0 pt-3"},o.createElement("div",{className:"text-base font-medium mb-2 flex items-center gap-2"},"workbench"===t.component_type&&(0,j.Mf)(t)&&o.createElement(te.A,{icon:"mcp",size:6,className:"inline-block"})," ",o.createElement("span",{className:"line-clamp-1"},t.label||"Unnamed Component")),o.createElement("div",{className:"text-xs text-secondary truncate mb-2"},t.provider),o.createElement("div",{className:"text-sm text-secondary line-clamp-2 mb-3 min-h-[40px]"},o.createElement(x.PA,{content:t.description||"",showFullscreen:!1,textThreshold:70}))))},se=e=>{let{items:t,title:n,disabled:a=!1,...r}=e;return o.createElement("div",null,o.createElement("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 auto-rows-fr"},t.map((e,n)=>o.createElement(ie,Object.assign({key:n,item:e,index:n,allowDelete:t.length>1,disabled:a},r)))))},le={team:S.A,agent:I.A,tool:M.A,model:P.A,termination:R.A,workbench:G},ce=e=>{let{gallery:t,onSave:n,onDirtyStateChange:a}=e;if(!t.config.components)return o.createElement("div",{className:"text-secondary"},"No components found");const{0:s,1:l}=(0,o.useState)(null),{0:c,1:m}=(0,o.useState)("team"),{0:u,1:h}=(0,o.useState)(!1),{0:g,1:f}=(0,o.useState)(!1),{0:_,1:y}=(0,o.useState)(t),{0:x,1:w}=(0,o.useState)(""),{0:C,1:T}=(0,o.useState)(!1),{0:N,1:S}=(0,o.useState)(!1),{0:I,1:M}=(0,o.useState)(t.config.name),{0:P,1:R}=(0,o.useState)(t.config.metadata.description),[G,j]=i.Ay.useMessage(),D=(0,o.useRef)(null);(0,o.useEffect)(()=>{M(t.config.name),R(t.config.metadata.description),y(t),w(JSON.stringify(t,null,2)),T(!1),S(!1),m("team"),l(null),f(!1)},[t.id]);const z=(e,o)=>{const r=g?_:t,i={...r,config:{...r.config,components:{...r.config.components,[e]:o(r.config.components[e])}}};g?y(i):(n(i),a(!0))},H={onEdit:(e,t)=>{l({component:e,category:re(c),index:t})},onDuplicate:(e,n)=>{var o;const a=re(c),i=null===(o=e.label)||void 0===o?void 0:o.replace(/_\d+$/,""),s=t.config.components[a]||[],l=Math.max.apply(Math,(0,r.A)(s.map(e=>{var t;const n=null===(t=e.label)||void 0===t?void 0:t.match(new RegExp(`^${i}_?(\\d+)?$`));return n?parseInt(n[1]||"0"):0}).filter(e=>!isNaN(e))).concat([0]))+1;z(a,t=>[].concat((0,r.A)(t),[{...e,label:`${i}_${l}`}]))},onDelete:(e,t)=>{const n=re(c);z(n,e=>e.filter((e,n)=>n!==t))}},B=(e,t)=>{z(t,n=>{const o=[].concat((0,r.A)(n),[e]);return l({component:e,category:t,index:o.length-1}),o})},V=g?_:t,Y=Object.entries(le).map(e=>{var t,n,a;let[r,i]=e;return{key:r,label:o.createElement("span",{className:"flex items-center gap-2"},o.createElement(i,{className:"w-5 h-5"}),r.charAt(0).toUpperCase()+r.slice(1),"s",o.createElement("span",{className:"text-xs font-light text-secondary"},"(",(null===(t=V.config.components[re(r)])||void 0===t?void 0:t.length)||0,")")),children:o.createElement("div",null,o.createElement("div",{className:"flex justify-between items-center mb-4"},o.createElement("h3",{className:"text-base font-medium"},(null===(n=V.config.components[re(r)])||void 0===n?void 0:n.length)||0," ",1===((null===(a=V.config.components[re(r)])||void 0===a?void 0:a.length)||0)?r.charAt(0).toUpperCase()+r.slice(1):r.charAt(0).toUpperCase()+r.slice(1)+"s"),o.createElement(ae,{componentType:r,gallery:V,onComponentAdded:B,disabled:g})),o.createElement(se,Object.assign({items:V.config.components[re(r)]||[],title:r,disabled:g},H)))}});return o.createElement("div",{className:"max-w-7xl px-4"},j,o.createElement("div",{className:"relative h-64 rounded bg-secondary overflow-hidden mb-8"},o.createElement("img",{src:"/images/bg/layeredbg.svg",alt:"Gallery Banner",className:"absolute w-full h-full object-cover"}),o.createElement("div",{className:"relative z-10 p-6 h-full flex flex-col justify-between"},o.createElement("div",null,o.createElement("div",{className:"flex items-center justify-between"},o.createElement("div",{className:"flex items-center gap-2"},u?o.createElement(E.A,{value:I,onChange:e=>M(e.target.value),className:"text-2xl font-medium bg-background/50 backdrop-blur px-2 py-1 rounded w-[400px]"}):o.createElement("h1",{className:"text-2xl font-medium text-primary"},V.config.name),V.config.url&&o.createElement(p.A,{title:"Remote Gallery"},o.createElement(v.A,{className:"w-5 h-5 text-secondary"})))),u?o.createElement(ee.A,{value:P,onChange:e=>R(e.target.value),className:"w-1/2 bg-background/50 backdrop-blur px-2 py-1 rounded mt-2",rows:2}):o.createElement("div",{className:"flex flex-col gap-2"},o.createElement("p",{className:"text-secondary w-1/2 mt-2 line-clamp-2"},V.config.metadata.description),o.createElement("div",{className:"flex gap-0"},o.createElement(p.A,{title:"Edit Gallery"},o.createElement(d.Ay,{icon:o.createElement(O.A,{className:"w-4 h-4"}),onClick:()=>h(!0),type:"text",className:"text-white hover:text-white/80",disabled:g})),o.createElement(p.A,{title:"Download Gallery"},o.createElement(d.Ay,{icon:o.createElement(L.A,{className:"w-4 h-4"}),onClick:()=>{const e=JSON.stringify(t,null,2),n=new Blob([e],{type:"application/json"}),o=URL.createObjectURL(n),a=document.createElement("a");a.href=o,a.download=`${t.config.name.toLowerCase().replace(/\s+/g,"_")}.json`,document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(o)},type:"text",className:"text-white hover:text-white/80"})),o.createElement(p.A,{title:g?"Form Editor":"JSON Editor"},o.createElement(d.Ay,{icon:g?o.createElement(q.A,{className:"w-4 h-4"}):o.createElement(F.A,{className:"w-4 h-4"}),onClick:()=>{const e=!g;f(e),e&&(w(JSON.stringify(V,null,2)),S(!1))},type:"text",className:"text-white hover:text-white/80"})))),u&&o.createElement("div",{className:"flex gap-2 mt-2"},o.createElement(d.Ay,{onClick:()=>h(!1)},"Cancel"),o.createElement(d.Ay,{type:"primary",onClick:()=>{const e={...t,config:{...t.config,name:I,metadata:{...t.config.metadata,description:P}}};n(e),a(!0),h(!1)}},"Save"))),o.createElement("div",{className:"flex gap-2"},o.createElement("div",{className:"bg-tertiary backdrop-blur rounded p-2 flex items-center gap-2"},o.createElement(b.A,{className:"w-4 h-4 text-secondary"}),o.createElement("span",{className:"text-sm"},Object.values(V.config.components).reduce((e,t)=>e+t.length,0)," ","components")),o.createElement("div",{className:"bg-tertiary backdrop-blur rounded p-2 text-sm"},"v",V.config.metadata.version)))),g?o.createElement("div",{className:"space-y-4"},o.createElement("div",{className:"flex items-center justify-between"},o.createElement("div",{className:"flex items-center gap-2"},o.createElement("h2",{className:"text-xl font-medium"},"JSON Editor"),N&&o.createElement("span",{className:"text-orange-500 text-sm"},"• Unsaved changes")),o.createElement("div",{className:"flex gap-2"},o.createElement(d.Ay,{onClick:()=>{f(!1),S(!1),w(JSON.stringify(t,null,2))}},"Cancel"),o.createElement(d.Ay,{type:"primary",onClick:()=>{try{const e=JSON.parse(x);y(e),n(e),a(!0),f(!1),S(!1),G.success("Gallery updated successfully!")}catch(e){G.error("Invalid JSON format. Please check your syntax."),console.error("JSON parse error:",e)}}},"Save Changes"))),o.createElement("div",{className:"h-[600px] border border-secondary rounded"},o.createElement(W.T,{editorRef:D,value:x,onChange:e=>{w(e),S(!0)},language:"json",minimap:!0}))):o.createElement(A.A,{items:Y,className:"gallery-tabs",size:"large",onChange:e=>m(e)}),o.createElement(k.A,{title:"Edit Component",placement:"right",size:"large",onClose:()=>l(null),open:!!s,className:"component-editor-drawer"},s&&o.createElement(U.L,{component:s.component,onChange:e=>{s&&(z(s.category,t=>t.map((t,n)=>n===s.index?e:t)),l(null))},onClose:()=>l(null),navigationDepth:!0})))};var me=n(8355),pe=n(7260),de=n(4796);const ue=(()=>{try{return n(1610)}catch(e){throw console.error("Error loading gallery JSON:",e),e}})(),he=e=>{let{open:t,onCancel:n,onCreateGallery:a}=e;const{0:r,1:i}=(0,o.useState)("url"),{0:l,1:c}=(0,o.useState)(""),{0:m,1:p}=(0,o.useState)(JSON.stringify(ue,null,2)),{0:u,1:h}=(0,o.useState)(""),{0:g,1:f}=(0,o.useState)(!1),_=(0,o.useRef)(null),y={accept:".json",showUploadList:!1,customRequest:e=>{let{file:t,onSuccess:n}=e;setTimeout(()=>{n&&n("ok")},0)},onChange:e=>{const{status:t,originFileObj:o}=e.file;if("done"===t&&o instanceof File){const e=new FileReader;e.onload=e=>{try{var t;const o=JSON.parse(null===(t=e.target)||void 0===t?void 0:t.result);a({config:o}),n()}catch(o){h("Invalid JSON file")}},e.readAsText(o)}else"error"===t&&h("File upload failed")}},b=(0,o.useRef)(null),x=[{key:"url",label:o.createElement("span",{className:"flex items-center gap-2"},o.createElement(v.A,{className:"w-4 h-4"})," URL Import"),children:o.createElement("div",{className:"space-y-4"},o.createElement(E.A,{ref:b,placeholder:"Enter gallery URL...",value:l,onChange:e=>c(e.target.value)}),o.createElement("div",{className:"text-xs"},"Sample",o.createElement("a",{role:"button",onClick:e=>{c("https://raw.githubusercontent.com/victordibia/multiagent-systems-with-autogen/refs/heads/main/research/components/gallery/base.json"),e.preventDefault()},href:"https://raw.githubusercontent.com/victordibia/multiagent-systems-with-autogen/refs/heads/main/research/components/gallery/base.json",target:"_blank",rel:"noreferrer",className:"text-accent"}," ","gallery.json"," ")),o.createElement(d.Ay,{type:"primary",onClick:async()=>{f(!0),h("");try{const e=await fetch(l),t=await e.json();a({config:t}),n()}catch(e){h("Failed to fetch or parse gallery from URL")}finally{f(!1)}},disabled:!l||g,block:!0},"Import from URL"))},{key:"file",label:o.createElement("span",{className:"flex items-center gap-2"},o.createElement(de.A,{className:"w-4 h-4"})," File Upload"),children:o.createElement("div",{className:"border-2 border-dashed rounded-lg p-8 text-center space-y-4"},o.createElement(me.A.Dragger,y,o.createElement("p",{className:"ant-upload-drag-icon"},o.createElement(de.A,{className:"w-8 h-8 mx-auto text-secondary"})),o.createElement("p",{className:"ant-upload-text"},"Click or drag JSON file to this area")))},{key:"paste",label:o.createElement("span",{className:"flex items-center gap-2"},o.createElement(F.A,{className:"w-4 h-4"})," Paste JSON"),children:o.createElement("div",{className:"space-y-4"},o.createElement("div",{className:"h-64"},o.createElement(W.T,{value:m,onChange:p,editorRef:_,language:"json",minimap:!1})),o.createElement(d.Ay,{type:"primary",onClick:()=>{try{const e=JSON.parse(m);a({config:e}),n()}catch(e){h("Invalid JSON format")}},block:!0},"Import JSON"))}];return o.createElement(s.A,{title:"Create New Gallery",open:t,onCancel:n,footer:null,width:800},o.createElement("div",{className:"mt-4"},o.createElement(A.A,{activeKey:r,onChange:i,items:x}),u&&o.createElement(pe.A,{message:u,type:"error",showIcon:!0,className:"mt-4"})))};var ge=()=>{const{0:e,1:t}=(0,o.useState)(!1),{0:n,1:a}=(0,o.useState)([]),{0:p,1:d}=(0,o.useState)(null),{0:u,1:h}=(0,o.useState)(!1),{0:g,1:f}=(0,o.useState)(!1),{0:_,1:v}=(0,o.useState)(()=>{if("undefined"!=typeof window){const e=localStorage.getItem("gallerySidebar");return null===e||JSON.parse(e)}return!0}),{user:y}=(0,o.useContext)(c.v),[b,x]=i.Ay.useMessage();(0,o.useEffect)(()=>{"undefined"!=typeof window&&localStorage.setItem("gallerySidebar",JSON.stringify(_))},[_]);const E=(0,o.useCallback)(async()=>{if(null!=y&&y.id)try{t(!0);const e=await m.f.listGalleries(y.id);a(e),!p&&e.length>0&&d(e[0])}catch(e){console.error("Error fetching galleries:",e),b.error("Failed to fetch galleries")}finally{t(!1)}},[null==y?void 0:y.id,p,b]);(0,o.useEffect)(()=>{E()},[E]),(0,o.useEffect)(()=>{const e=new URLSearchParams(window.location.search).get("galleryId");if(e&&!p){const t=parseInt(e,10);isNaN(t)||A(t)}},[]),(0,o.useEffect)(()=>{null!=p&&p.id&&window.history.pushState({},"",`?galleryId=${p.id.toString()}`)},[null==p?void 0:p.id]);const A=async e=>{null!=y&&y.id&&(g?s.A.confirm({title:"Unsaved Changes",content:"You have unsaved changes. Do you want to discard them?",okText:"Discard",cancelText:"Go Back",onOk:()=>{k(e),f(!1)}}):await k(e))},k=async e=>{if(null!=y&&y.id){t(!0);try{const t=await m.f.getGallery(e,y.id);d(t)}catch(n){console.error("Error loading gallery:",n),b.error("Failed to load gallery")}finally{t(!1)}}},C=async e=>{if(null!=y&&y.id&&null!=p&&p.id)try{const t={...e,created_at:void 0,updated_at:void 0},o=await m.f.updateGallery(p.id,t,y.id);a(n.map(e=>e.id===o.id?o:e)),d(o),f(!1),b.success("Gallery updated successfully")}catch(t){console.error("Error updating gallery:",t),b.error("Failed to update gallery")}};return null!=y&&y.id?o.createElement("div",{className:"relative flex h-full w-full"},x,o.createElement(he,{open:u,onCancel:()=>h(!1),onCreateGallery:async e=>{if(null!=y&&y.id){e.user_id=y.id;try{const t=await m.f.createGallery(e,y.id);a([t].concat((0,r.A)(n))),d(t),h(!1),b.success("Gallery created successfully")}catch(t){console.error("Error creating gallery:",t),b.error("Failed to create gallery")}}}}),o.createElement("div",{className:"absolute left-0 top-0 h-full transition-all duration-200 ease-in-out "+(_?"w-64":"w-12")},o.createElement(w,{isOpen:_,galleries:n,currentGallery:p,onToggle:()=>v(!_),onSelectGallery:e=>A(e.id),onCreateGallery:()=>h(!0),onDeleteGallery:async e=>{if(null!=y&&y.id)try{await m.f.deleteGallery(e,y.id),a(n.filter(t=>t.id!==e)),(null==p?void 0:p.id)===e&&d(null),b.success("Gallery deleted successfully")}catch(t){console.error("Error deleting gallery:",t),b.error("Failed to delete gallery")}},onSyncGallery:async e=>{if(null!=y&&y.id)try{t(!0);const o=n.find(t=>t.id===e);if(null==o||!o.config.url)return;const a=await m.f.syncGallery(o.config.url);await C({...a,id:e,config:{...a.config,metadata:{...a.config.metadata,lastSynced:(new Date).toISOString()}}}),b.success("Gallery synced successfully")}catch(o){console.error("Error syncing gallery:",o),b.error("Failed to sync gallery")}finally{t(!1)}},isLoading:e})),o.createElement("div",{className:"flex-1 transition-all -mr-6 duration-200 "+(_?"ml-64":"ml-12")},o.createElement("div",{className:"p-4 pt-2"},o.createElement("div",{className:"flex items-center gap-2 mb-4 text-sm"},o.createElement("span",{className:"text-primary font-medium"},"Galleries"),p&&o.createElement(o.Fragment,null,o.createElement(l.A,{className:"w-4 h-4 text-secondary"}),o.createElement("span",{className:"text-secondary"},p.config.name))),e&&!p?o.createElement("div",{className:"flex items-center justify-center h-[calc(100vh-120px)] text-secondary"},"Loading galleries..."):p?o.createElement(ce,{gallery:p,onSave:C,onDirtyStateChange:f}):o.createElement("div",{className:"flex items-center justify-center h-[calc(100vh-120px)] text-secondary"},"Select a gallery from the sidebar or create a new one")))):o.createElement("div",{className:"flex items-center justify-center h-[calc(100vh-120px)] text-secondary"},"Please log in to view galleries")};var fe=e=>{let{data:t}=e;return o.createElement(a.A,{meta:t.site.siteMetadata,title:"画廊",link:"/gallery"},o.createElement("main",{style:{height:"100%"},className:" h-full "},o.createElement(ge,null)))}}}]);
//# sourceMappingURL=component---src-pages-gallery-tsx-2b076c83225ea7b3ba44.js.map