"use strict";(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[845],{1133:function(e,n,t){t.d(n,{Gc:function(){return _n},Ln:function(){return An},VH:function(){return re},VS:function(){return Yn},_0:function(){return F},ck:function(){return Zn},fM:function(){return $n},h7:function(){return we},of:function(){return ut},rV:function(){return Bn},tE:function(){return Xe}});var o=t(4848),i=t(6540),r=t(6100),s=t(5154),a=t(4371),d=t(3973),l=t(961);const c=(0,i.createContext)(null),u=c.Provider,g=s.xc.error001();function p(e,n){const t=(0,i.useContext)(c);if(null===t)throw new Error(g);return(0,a.n)(t,e,n)}function m(){const e=(0,i.useContext)(c);if(null===e)throw new Error(g);return(0,i.useMemo)(()=>({getState:e.getState,setState:e.setState,subscribe:e.subscribe}),[e])}const h={display:"none"},f={position:"absolute",width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0px, 0px, 0px, 0px)",clipPath:"inset(100%)"},b="react-flow__node-desc",S="react-flow__edge-desc",y=e=>e.ariaLiveMessage,C=e=>e.ariaLabelConfig;function x({rfId:e}){const n=p(y);return(0,o.jsx)("div",{id:`react-flow__aria-live-${e}`,"aria-live":"assertive","aria-atomic":"true",style:f,children:n})}function v({rfId:e,disableKeyboardA11y:n}){const t=p(C);return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{id:`${b}-${e}`,style:h,children:n?t["node.a11yDescription.default"]:t["node.a11yDescription.keyboardDisabled"]}),(0,o.jsx)("div",{id:`${S}-${e}`,style:h,children:t["edge.a11yDescription.default"]}),!n&&(0,o.jsx)(x,{rfId:e})]})}const w=(0,i.forwardRef)(({position:e="top-left",children:n,className:t,style:i,...s},a)=>{const d=`${e}`.split("-");return(0,o.jsx)("div",{className:(0,r.A)(["react-flow__panel",t,...d]),style:i,ref:a,...s,children:n})});function E({proOptions:e,position:n="bottom-right"}){return e?.hideAttribution?null:(0,o.jsx)(w,{position:n,className:"react-flow__attribution","data-message":"Please only hide this attribution when you are subscribed to React Flow Pro: https://pro.reactflow.dev",children:(0,o.jsx)("a",{href:"https://reactflow.dev",target:"_blank",rel:"noopener noreferrer","aria-label":"React Flow attribution",children:"React Flow"})})}w.displayName="Panel";const k=e=>{const n=[],t=[];for(const[,o]of e.nodeLookup)o.selected&&n.push(o.internals.userNode);for(const[,o]of e.edgeLookup)o.selected&&t.push(o);return{selectedNodes:n,selectedEdges:t}},N=e=>e.id;function M(e,n){return(0,d.x)(e.selectedNodes.map(N),n.selectedNodes.map(N))&&(0,d.x)(e.selectedEdges.map(N),n.selectedEdges.map(N))}function P({onSelectionChange:e}){const n=m(),{selectedNodes:t,selectedEdges:o}=p(k,M);return(0,i.useEffect)(()=>{const i={nodes:t,edges:o};e?.(i),n.getState().onSelectionChangeHandlers.forEach(e=>e(i))},[t,o,e]),null}const D=e=>!!e.onSelectionChangeHandlers;function O({onSelectionChange:e}){const n=p(D);return e||n?(0,o.jsx)(P,{onSelectionChange:e}):null}const R=[0,0],L={x:0,y:0,zoom:1},I=["nodes","edges","defaultNodes","defaultEdges","onConnect","onConnectStart","onConnectEnd","onClickConnectStart","onClickConnectEnd","nodesDraggable","autoPanOnNodeFocus","nodesConnectable","nodesFocusable","edgesFocusable","edgesReconnectable","elevateNodesOnSelect","elevateEdgesOnSelect","minZoom","maxZoom","nodeExtent","onNodesChange","onEdgesChange","elementsSelectable","connectionMode","snapGrid","snapToGrid","translateExtent","connectOnClick","defaultEdgeOptions","fitView","fitViewOptions","onNodesDelete","onEdgesDelete","onDelete","onNodeDrag","onNodeDragStart","onNodeDragStop","onSelectionDrag","onSelectionDragStart","onSelectionDragStop","onMoveStart","onMove","onMoveEnd","noPanClassName","nodeOrigin","autoPanOnConnect","autoPanOnNodeDrag","onError","connectionRadius","isValidConnection","selectNodesOnDrag","nodeDragThreshold","connectionDragThreshold","onBeforeDelete","debug","autoPanSpeed","paneClickDistance","ariaLabelConfig","rfId"],A=e=>({setNodes:e.setNodes,setEdges:e.setEdges,setMinZoom:e.setMinZoom,setMaxZoom:e.setMaxZoom,setTranslateExtent:e.setTranslateExtent,setNodeExtent:e.setNodeExtent,reset:e.reset,setDefaultNodesAndEdges:e.setDefaultNodesAndEdges,setPaneClickDistance:e.setPaneClickDistance}),j={translateExtent:s.ZO,nodeOrigin:R,minZoom:.5,maxZoom:2,elementsSelectable:!0,noPanClassName:"nopan",rfId:"1",paneClickDistance:0};function z(e){const{setNodes:n,setEdges:t,setMinZoom:o,setMaxZoom:r,setTranslateExtent:a,setNodeExtent:l,reset:c,setDefaultNodesAndEdges:u,setPaneClickDistance:g}=p(A,d.x),h=m();(0,i.useEffect)(()=>(u(e.defaultNodes,e.defaultEdges),()=>{f.current=j,c()}),[]);const f=(0,i.useRef)(j);return(0,i.useEffect)(()=>{for(const i of I){const d=e[i];d!==f.current[i]&&(void 0!==e[i]&&("nodes"===i?n(d):"edges"===i?t(d):"minZoom"===i?o(d):"maxZoom"===i?r(d):"translateExtent"===i?a(d):"nodeExtent"===i?l(d):"paneClickDistance"===i?g(d):"ariaLabelConfig"===i?h.setState({ariaLabelConfig:(0,s.Q6)(d)}):"fitView"===i?h.setState({fitViewQueued:d}):"fitViewOptions"===i?h.setState({fitViewOptions:d}):h.setState({[i]:d})))}f.current=e},I.map(n=>e[n])),null}function _(){return"undefined"!=typeof window&&window.matchMedia?window.matchMedia("(prefers-color-scheme: dark)"):null}const V="undefined"!=typeof document?document:null;function B(e=null,n={target:V,actInsideInputWithModifier:!0}){const[t,o]=(0,i.useState)(!1),r=(0,i.useRef)(!1),a=(0,i.useRef)(new Set([])),[d,l]=(0,i.useMemo)(()=>{if(null!==e){const n=(Array.isArray(e)?e:[e]).filter(e=>"string"==typeof e).map(e=>e.replace("+","\n").replace("\n\n","\n+").split("\n")),t=n.reduce((e,n)=>e.concat(...n),[]);return[n,t]}return[[],[]]},[e]);return(0,i.useEffect)(()=>{const t=n?.target??V,i=n?.actInsideInputWithModifier??!0;if(null!==e){const e=e=>{r.current=e.ctrlKey||e.metaKey||e.shiftKey||e.altKey;if((!r.current||r.current&&!i)&&(0,s.v5)(e))return!1;const t=$(e.code,l);if(a.current.add(e[t]),Z(d,a.current,!1)){const t=e.composedPath?.()?.[0]||e.target,i="BUTTON"===t?.nodeName||"A"===t?.nodeName;!1===n.preventDefault||!r.current&&i||e.preventDefault(),o(!0)}},c=e=>{const n=$(e.code,l);Z(d,a.current,!0)?(o(!1),a.current.clear()):a.current.delete(e[n]),"Meta"===e.key&&a.current.clear(),r.current=!1},u=()=>{a.current.clear(),o(!1)};return t?.addEventListener("keydown",e),t?.addEventListener("keyup",c),window.addEventListener("blur",u),window.addEventListener("contextmenu",u),()=>{t?.removeEventListener("keydown",e),t?.removeEventListener("keyup",c),window.removeEventListener("blur",u),window.removeEventListener("contextmenu",u)}}},[e,o]),t}function Z(e,n,t){return e.filter(e=>t||e.length===n.size).some(e=>e.every(e=>n.has(e)))}function $(e,n){return n.includes(e)?"code":"key"}const T=()=>{const e=m();return(0,i.useMemo)(()=>({zoomIn:n=>{const{panZoom:t}=e.getState();return t?t.scaleBy(1.2,{duration:n?.duration}):Promise.resolve(!1)},zoomOut:n=>{const{panZoom:t}=e.getState();return t?t.scaleBy(1/1.2,{duration:n?.duration}):Promise.resolve(!1)},zoomTo:(n,t)=>{const{panZoom:o}=e.getState();return o?o.scaleTo(n,{duration:t?.duration}):Promise.resolve(!1)},getZoom:()=>e.getState().transform[2],setViewport:async(n,t)=>{const{transform:[o,i,r],panZoom:s}=e.getState();return s?(await s.setViewport({x:n.x??o,y:n.y??i,zoom:n.zoom??r},t),Promise.resolve(!0)):Promise.resolve(!1)},getViewport:()=>{const[n,t,o]=e.getState().transform;return{x:n,y:t,zoom:o}},setCenter:async(n,t,o)=>e.getState().setCenter(n,t,o),fitBounds:async(n,t)=>{const{width:o,height:i,minZoom:r,maxZoom:a,panZoom:d}=e.getState(),l=(0,s.R4)(n,o,i,r,a,t?.padding??.1);return d?(await d.setViewport(l,{duration:t?.duration,ease:t?.ease,interpolate:t?.interpolate}),Promise.resolve(!0)):Promise.resolve(!1)},screenToFlowPosition:(n,t={})=>{const{transform:o,snapGrid:i,snapToGrid:r,domNode:a}=e.getState();if(!a)return n;const{x:d,y:l}=a.getBoundingClientRect(),c={x:n.x-d,y:n.y-l},u=t.snapGrid??i,g=t.snapToGrid??r;return(0,s.Ff)(c,o,g,u)},flowToScreenPosition:n=>{const{transform:t,domNode:o}=e.getState();if(!o)return n;const{x:i,y:r}=o.getBoundingClientRect(),a=(0,s.zj)(n,t);return{x:a.x+i,y:a.y+r}}}),[])};function H(e,n){const t=[],o=new Map,i=[];for(const r of e)if("add"!==r.type)if("remove"===r.type||"replace"===r.type)o.set(r.id,[r]);else{const e=o.get(r.id);e?e.push(r):o.set(r.id,[r])}else i.push(r);for(const r of n){const e=o.get(r.id);if(!e){t.push(r);continue}if("remove"===e[0].type)continue;if("replace"===e[0].type){t.push({...e[0].item});continue}const n={...r};for(const t of e)X(t,n);t.push(n)}return i.length&&i.forEach(e=>{void 0!==e.index?t.splice(e.index,0,{...e.item}):t.push({...e.item})}),t}function X(e,n){switch(e.type){case"select":n.selected=e.selected;break;case"position":void 0!==e.position&&(n.position=e.position),void 0!==e.dragging&&(n.dragging=e.dragging);break;case"dimensions":void 0!==e.dimensions&&(n.measured??={},n.measured.width=e.dimensions.width,n.measured.height=e.dimensions.height,e.setAttributes&&(!0!==e.setAttributes&&"width"!==e.setAttributes||(n.width=e.dimensions.width),!0!==e.setAttributes&&"height"!==e.setAttributes||(n.height=e.dimensions.height))),"boolean"==typeof e.resizing&&(n.resizing=e.resizing)}}function F(e,n){return H(e,n)}function K(e,n){return H(e,n)}function W(e,n){return{id:e,type:"select",selected:n}}function Y(e,n=new Set,t=!1){const o=[];for(const[i,r]of e){const e=n.has(i);void 0===r.selected&&!e||r.selected===e||(t&&(r.selected=e),o.push(W(r.id,e)))}return o}function Q({items:e=[],lookup:n}){const t=[],o=new Map(e.map(e=>[e.id,e]));for(const[i,r]of e.entries()){const e=n.get(r.id),o=e?.internals?.userNode??e;void 0!==o&&o!==r&&t.push({id:r.id,item:r,type:"replace"}),void 0===o&&t.push({item:r,type:"add",index:i})}for(const[i]of n){void 0===o.get(i)&&t.push({id:i,type:"remove"})}return t}function G(e){return{id:e.id,type:"remove"}}const U=e=>(0,s.oB)(e),q=e=>(0,s.b$)(e);function J(e){return(0,i.forwardRef)(e)}const ee="undefined"!=typeof window?i.useLayoutEffect:i.useEffect;function ne(e){const[n,t]=(0,i.useState)(BigInt(0)),[o]=(0,i.useState)(()=>function(e){let n=[];return{get:()=>n,reset:()=>{n=[]},push:t=>{n.push(t),e()}}}(()=>t(e=>e+BigInt(1))));return ee(()=>{const n=o.get();n.length&&(e(n),o.reset())},[n]),o}const te=(0,i.createContext)(null);function oe({children:e}){const n=m(),t=ne((0,i.useCallback)(e=>{const{nodes:t=[],setNodes:o,hasDefaultNodes:i,onNodesChange:r,nodeLookup:s,fitViewQueued:a}=n.getState();let d=t;for(const n of e)d="function"==typeof n?n(d):n;const l=Q({items:d,lookup:s});i&&o(d),l.length>0?r?.(l):a&&window.requestAnimationFrame(()=>{const{fitViewQueued:e,nodes:t,setNodes:o}=n.getState();e&&o(t)})},[])),r=ne((0,i.useCallback)(e=>{const{edges:t=[],setEdges:o,hasDefaultEdges:i,onEdgesChange:r,edgeLookup:s}=n.getState();let a=t;for(const n of e)a="function"==typeof n?n(a):n;i?o(a):r&&r(Q({items:a,lookup:s}))},[])),s=(0,i.useMemo)(()=>({nodeQueue:t,edgeQueue:r}),[]);return(0,o.jsx)(te.Provider,{value:s,children:e})}const ie=e=>!!e.panZoom;function re(){const e=T(),n=m(),t=function(){const e=(0,i.useContext)(te);if(!e)throw new Error("useBatchContext must be used within a BatchProvider");return e}(),o=p(ie),r=(0,i.useMemo)(()=>{const e=e=>n.getState().nodeLookup.get(e),o=e=>{t.nodeQueue.push(e)},i=e=>{t.edgeQueue.push(e)},r=e=>{const{nodeLookup:t,nodeOrigin:o}=n.getState(),i=U(e)?e:t.get(e.id),r=i.parentId?(0,s.us)(i.position,i.measured,i.parentId,t,o):i.position,a={...i,position:r,width:i.measured?.width??i.width,height:i.measured?.height??i.height};return(0,s.kM)(a)},a=(e,n,t={replace:!1})=>{o(o=>o.map(o=>{if(o.id===e){const e="function"==typeof n?n(o):n;return t.replace&&U(e)?e:{...o,...e}}return o}))},d=(e,n,t={replace:!1})=>{i(o=>o.map(o=>{if(o.id===e){const e="function"==typeof n?n(o):n;return t.replace&&q(e)?e:{...o,...e}}return o}))};return{getNodes:()=>n.getState().nodes.map(e=>({...e})),getNode:n=>e(n)?.internals.userNode,getInternalNode:e,getEdges:()=>{const{edges:e=[]}=n.getState();return e.map(e=>({...e}))},getEdge:e=>n.getState().edgeLookup.get(e),setNodes:o,setEdges:i,addNodes:e=>{const n=Array.isArray(e)?e:[e];t.nodeQueue.push(e=>[...e,...n])},addEdges:e=>{const n=Array.isArray(e)?e:[e];t.edgeQueue.push(e=>[...e,...n])},toObject:()=>{const{nodes:e=[],edges:t=[],transform:o}=n.getState(),[i,r,s]=o;return{nodes:e.map(e=>({...e})),edges:t.map(e=>({...e})),viewport:{x:i,y:r,zoom:s}}},deleteElements:async({nodes:e=[],edges:t=[]})=>{const{nodes:o,edges:i,onNodesDelete:r,onEdgesDelete:a,triggerNodeChanges:d,triggerEdgeChanges:l,onDelete:c,onBeforeDelete:u}=n.getState(),{nodes:g,edges:p}=await(0,s.Tq)({nodesToRemove:e,edgesToRemove:t,nodes:o,edges:i,onBeforeDelete:u}),m=p.length>0,h=g.length>0;if(m){const e=p.map(G);a?.(p),l(e)}if(h){const e=g.map(G);r?.(g),d(e)}return(h||m)&&c?.({nodes:g,edges:p}),{deletedNodes:g,deletedEdges:p}},getIntersectingNodes:(e,t=!0,o)=>{const i=(0,s.mW)(e),a=i?e:r(e),d=void 0!==o;return a?(o||n.getState().nodes).filter(o=>{const r=n.getState().nodeLookup.get(o.id);if(r&&!i&&(o.id===e.id||!r.internals.positionAbsolute))return!1;const l=(0,s.kM)(d?o:r),c=(0,s.X6)(l,a);return t&&c>0||c>=l.width*l.height||c>=a.width*a.height}):[]},isNodeIntersecting:(e,n,t=!0)=>{const o=(0,s.mW)(e)?e:r(e);if(!o)return!1;const i=(0,s.X6)(o,n);return t&&i>0||i>=o.width*o.height},updateNode:a,updateNodeData:(e,n,t={replace:!1})=>{a(e,e=>{const o="function"==typeof n?n(e):n;return t.replace?{...e,data:o}:{...e,data:{...e.data,...o}}},t)},updateEdge:d,updateEdgeData:(e,n,t={replace:!1})=>{d(e,e=>{const o="function"==typeof n?n(e):n;return t.replace?{...e,data:o}:{...e,data:{...e.data,...o}}},t)},getNodesBounds:e=>{const{nodeLookup:t,nodeOrigin:o}=n.getState();return(0,s.Jo)(e,{nodeLookup:t,nodeOrigin:o})},getHandleConnections:({type:e,id:t,nodeId:o})=>Array.from(n.getState().connectionLookup.get(`${o}-${e}${t?`-${t}`:""}`)?.values()??[]),getNodeConnections:({type:e,handleId:t,nodeId:o})=>Array.from(n.getState().connectionLookup.get(`${o}${e?t?`-${e}-${t}`:`-${e}`:""}`)?.values()??[]),fitView:async e=>{const o=n.getState().fitViewResolver??(0,s.YN)();return n.setState({fitViewQueued:!0,fitViewOptions:e,fitViewResolver:o}),t.nodeQueue.push(e=>[...e]),o.promise}}},[]);return(0,i.useMemo)(()=>({...r,...e,viewportInitialized:o}),[o])}const se=e=>e.selected,ae="undefined"!=typeof window?window:void 0;const de={position:"absolute",width:"100%",height:"100%",top:0,left:0},le=e=>({userSelectionActive:e.userSelectionActive,lib:e.lib});function ce({onPaneContextMenu:e,zoomOnScroll:n=!0,zoomOnPinch:t=!0,panOnScroll:r=!1,panOnScrollSpeed:a=.5,panOnScrollMode:l=s.ny.Free,zoomOnDoubleClick:c=!0,panOnDrag:u=!0,defaultViewport:g,translateExtent:h,minZoom:f,maxZoom:b,zoomActivationKeyCode:S,preventScrolling:y=!0,children:C,noWheelClassName:x,noPanClassName:v,onViewportChange:w,isControlledViewport:E,paneClickDistance:k}){const N=m(),M=(0,i.useRef)(null),{userSelectionActive:P,lib:D}=p(le,d.x),O=B(S),R=(0,i.useRef)();!function(e){const n=m();(0,i.useEffect)(()=>{const t=()=>{if(!e.current)return!1;const t=(0,s.Eo)(e.current);0!==t.height&&0!==t.width||n.getState().onError?.("004",s.xc.error004()),n.setState({width:t.width||500,height:t.height||500})};if(e.current){t(),window.addEventListener("resize",t);const n=new ResizeObserver(()=>t());return n.observe(e.current),()=>{window.removeEventListener("resize",t),n&&e.current&&n.unobserve(e.current)}}},[])}(M);const L=(0,i.useCallback)(e=>{w?.({x:e[0],y:e[1],zoom:e[2]}),E||N.setState({transform:e})},[w,E]);return(0,i.useEffect)(()=>{if(M.current){R.current=(0,s.kO)({domNode:M.current,minZoom:f,maxZoom:b,translateExtent:h,viewport:g,paneClickDistance:k,onDraggingChange:e=>N.setState({paneDragging:e}),onPanZoomStart:(e,n)=>{const{onViewportChangeStart:t,onMoveStart:o}=N.getState();o?.(e,n),t?.(n)},onPanZoom:(e,n)=>{const{onViewportChange:t,onMove:o}=N.getState();o?.(e,n),t?.(n)},onPanZoomEnd:(e,n)=>{const{onViewportChangeEnd:t,onMoveEnd:o}=N.getState();o?.(e,n),t?.(n)}});const{x:e,y:n,zoom:t}=R.current.getViewport();return N.setState({panZoom:R.current,transform:[e,n,t],domNode:M.current.closest(".react-flow")}),()=>{R.current?.destroy()}}},[]),(0,i.useEffect)(()=>{R.current?.update({onPaneContextMenu:e,zoomOnScroll:n,zoomOnPinch:t,panOnScroll:r,panOnScrollSpeed:a,panOnScrollMode:l,zoomOnDoubleClick:c,panOnDrag:u,zoomActivationKeyPressed:O,preventScrolling:y,noPanClassName:v,userSelectionActive:P,noWheelClassName:x,lib:D,onTransformChange:L})},[e,n,t,r,a,l,c,u,O,y,v,P,x,D,L]),(0,o.jsx)("div",{className:"react-flow__renderer",ref:M,style:de,children:C})}const ue=e=>({userSelectionActive:e.userSelectionActive,userSelectionRect:e.userSelectionRect});function ge(){const{userSelectionActive:e,userSelectionRect:n}=p(ue,d.x);return e&&n?(0,o.jsx)("div",{className:"react-flow__selection react-flow__container",style:{width:n.width,height:n.height,transform:`translate(${n.x}px, ${n.y}px)`}}):null}const pe=(e,n)=>t=>{t.target===n.current&&e?.(t)},me=e=>({userSelectionActive:e.userSelectionActive,elementsSelectable:e.elementsSelectable,connectionInProgress:e.connection.inProgress,dragging:e.paneDragging});function he({isSelecting:e,selectionKeyPressed:n,selectionMode:t=s.Qc.Full,panOnDrag:a,selectionOnDrag:l,onSelectionStart:c,onSelectionEnd:u,onPaneClick:g,onPaneContextMenu:h,onPaneScroll:f,onPaneMouseEnter:b,onPaneMouseMove:S,onPaneMouseLeave:y,children:C}){const x=m(),{userSelectionActive:v,elementsSelectable:w,dragging:E,connectionInProgress:k}=p(me,d.x),N=w&&(e||v),M=(0,i.useRef)(null),P=(0,i.useRef)(),D=(0,i.useRef)(new Set),O=(0,i.useRef)(new Set),R=(0,i.useRef)(!1),L=(0,i.useRef)(!1),I=e=>{R.current||k?R.current=!1:(g?.(e),x.getState().resetSelectedElements(),x.setState({nodesSelectionActive:!1}))},A=f?e=>f(e):void 0,j=!0===a||Array.isArray(a)&&a.includes(0);return(0,o.jsxs)("div",{className:(0,r.A)(["react-flow__pane",{draggable:j,dragging:E,selection:e}]),onClick:N?void 0:pe(I,M),onContextMenu:pe(e=>{Array.isArray(a)&&a?.includes(2)?e.preventDefault():h?.(e)},M),onWheel:pe(A,M),onPointerEnter:N?void 0:b,onPointerDown:N?n=>{const{resetSelectedElements:t,domNode:o}=x.getState();if(P.current=o?.getBoundingClientRect(),!w||!e||0!==n.button||n.target!==M.current||!P.current)return;n.target?.setPointerCapture?.(n.pointerId),L.current=!0,R.current=!1;const{x:i,y:r}=(0,s.q1)(n.nativeEvent,P.current);t(),x.setState({userSelectionRect:{width:0,height:0,startX:i,startY:r,x:i,y:r}}),c?.(n)}:S,onPointerMove:N?e=>{const{userSelectionRect:n,transform:o,nodeLookup:i,edgeLookup:r,connectionLookup:a,triggerNodeChanges:d,triggerEdgeChanges:l,defaultEdgeOptions:c}=x.getState();if(!P.current||!n)return;R.current=!0;const{x:u,y:g}=(0,s.q1)(e.nativeEvent,P.current),{startX:p,startY:m}=n,h={startX:p,startY:m,x:u<p?u:p,y:g<m?g:m,width:Math.abs(u-p),height:Math.abs(g-m)},f=D.current,b=O.current;D.current=new Set((0,s.U$)(i,h,o,t===s.Qc.Partial,!0).map(e=>e.id)),O.current=new Set;const S=c?.selectable??!0;for(const t of D.current){const e=a.get(t);if(e)for(const{edgeId:n}of e.values()){const e=r.get(n);e&&(e.selectable??S)&&O.current.add(n)}}if(!(0,s._s)(f,D.current)){d(Y(i,D.current,!0))}if(!(0,s._s)(b,O.current)){l(Y(r,O.current))}x.setState({userSelectionRect:h,userSelectionActive:!0,nodesSelectionActive:!1})}:S,onPointerUp:N?e=>{if(0!==e.button||!L.current)return;e.target?.releasePointerCapture?.(e.pointerId);const{userSelectionRect:t}=x.getState();!v&&t&&e.target===M.current&&I?.(e),x.setState({userSelectionActive:!1,userSelectionRect:null,nodesSelectionActive:D.current.size>0}),u?.(e),(n||l)&&(R.current=!1),L.current=!1}:void 0,onPointerLeave:y,ref:M,style:de,children:[C,(0,o.jsx)(ge,{})]})}function fe({id:e,store:n,unselect:t=!1,nodeRef:o}){const{addSelectedNodes:i,unselectNodesAndEdges:r,multiSelectionActive:a,nodeLookup:d,onError:l}=n.getState(),c=d.get(e);c?(n.setState({nodesSelectionActive:!1}),c.selected?(t||c.selected&&a)&&(r({nodes:[c],edges:[]}),requestAnimationFrame(()=>o?.current?.blur())):i([e])):l?.("012",s.xc.error012(e))}function be({nodeRef:e,disabled:n=!1,noDragClassName:t,handleSelector:o,nodeId:r,isSelectable:a,nodeClickDistance:d}){const l=m(),[c,u]=(0,i.useState)(!1),g=(0,i.useRef)();return(0,i.useEffect)(()=>{g.current=(0,s.I$)({getStoreItems:()=>l.getState(),onNodeMouseDown:n=>{fe({id:n,store:l,nodeRef:e})},onDragStart:()=>{u(!0)},onDragStop:()=>{u(!1)}})},[]),(0,i.useEffect)(()=>{if(n)g.current?.destroy();else if(e.current)return g.current?.update({noDragClassName:t,handleSelector:o,domNode:e.current,isSelectable:a,nodeId:r,nodeClickDistance:d}),()=>{g.current?.destroy()}},[t,o,n,a,e,r]),c}function Se(){const e=m();return(0,i.useCallback)(n=>{const{nodeExtent:t,snapToGrid:o,snapGrid:i,nodesDraggable:r,onError:a,updateNodePositions:d,nodeLookup:l,nodeOrigin:c}=e.getState(),u=new Map,g=(e=>n=>n.selected&&(n.draggable||e&&void 0===n.draggable))(r),p=o?i[0]:5,m=o?i[1]:5,h=n.direction.x*p*n.factor,f=n.direction.y*m*n.factor;for(const[,e]of l){if(!g(e))continue;let n={x:e.internals.positionAbsolute.x+h,y:e.internals.positionAbsolute.y+f};o&&(n=(0,s.s_)(n,i));const{position:r,positionAbsolute:d}=(0,s.aE)({nodeId:e.id,nextPosition:n,nodeLookup:l,nodeExtent:t,nodeOrigin:c,onError:a});e.position=r,e.internals.positionAbsolute=d,u.set(e.id,e)}d(u)},[])}const ye=(0,i.createContext)(null),Ce=ye.Provider;ye.Consumer;const xe=()=>(0,i.useContext)(ye),ve=e=>({connectOnClick:e.connectOnClick,noPanClassName:e.noPanClassName,rfId:e.rfId});const we=(0,i.memo)(J(function({type:e="source",position:n=s.yX.Top,isValidConnection:t,isConnectable:i=!0,isConnectableStart:a=!0,isConnectableEnd:l=!0,id:c,onConnect:u,children:g,className:h,onMouseDown:f,onTouchStart:b,...S},y){const C=c||null,x="target"===e,v=m(),w=xe(),{connectOnClick:E,noPanClassName:k,rfId:N}=p(ve,d.x),{connectingFrom:M,connectingTo:P,clickConnecting:D,isPossibleEndHandle:O,connectionInProcess:R,clickConnectionInProcess:L,valid:I}=p(((e,n,t)=>o=>{const{connectionClickStartHandle:i,connectionMode:r,connection:a}=o,{fromHandle:d,toHandle:l,isValid:c}=a,u=l?.nodeId===e&&l?.id===n&&l?.type===t;return{connectingFrom:d?.nodeId===e&&d?.id===n&&d?.type===t,connectingTo:u,clickConnecting:i?.nodeId===e&&i?.id===n&&i?.type===t,isPossibleEndHandle:r===s.WZ.Strict?d?.type!==t:e!==d?.nodeId||n!==d?.id,connectionInProcess:!!d,clickConnectionInProcess:!!i,valid:u&&c}})(w,C,e),d.x);w||v.getState().onError?.("010",s.xc.error010());const A=e=>{const{defaultEdgeOptions:n,onConnect:t,hasDefaultEdges:o}=v.getState(),i={...n,...e};if(o){const{edges:e,setEdges:n}=v.getState();n((0,s.rN)(i,e))}t?.(i),u?.(i)},j=e=>{if(!w)return;const n=(0,s.Er)(e.nativeEvent);if(a&&(n&&0===e.button||!n)){const n=v.getState();s.aQ.onPointerDown(e.nativeEvent,{autoPanOnConnect:n.autoPanOnConnect,connectionMode:n.connectionMode,connectionRadius:n.connectionRadius,domNode:n.domNode,nodeLookup:n.nodeLookup,lib:n.lib,isTarget:x,handleId:C,nodeId:w,flowId:n.rfId,panBy:n.panBy,cancelConnection:n.cancelConnection,onConnectStart:n.onConnectStart,onConnectEnd:n.onConnectEnd,updateConnection:n.updateConnection,onConnect:A,isValidConnection:t||n.isValidConnection,getTransform:()=>v.getState().transform,getFromHandle:()=>v.getState().connection.fromHandle,autoPanSpeed:n.autoPanSpeed,dragThreshold:n.connectionDragThreshold})}n?f?.(e):b?.(e)};return(0,o.jsx)("div",{"data-handleid":C,"data-nodeid":w,"data-handlepos":n,"data-id":`${N}-${w}-${C}-${e}`,className:(0,r.A)(["react-flow__handle",`react-flow__handle-${n}`,"nodrag",k,h,{source:!x,target:x,connectable:i,connectablestart:a,connectableend:l,clickconnecting:D,connectingfrom:M,connectingto:P,valid:I,connectionindicator:i&&(!R||O)&&(R||L?l:a)}]),onMouseDown:j,onTouchStart:j,onClick:E?n=>{const{onClickConnectStart:o,onClickConnectEnd:i,connectionClickStartHandle:r,connectionMode:d,isValidConnection:l,lib:c,rfId:u,nodeLookup:g,connection:p}=v.getState();if(!w||!r&&!a)return;if(!r)return o?.(n.nativeEvent,{nodeId:w,handleId:C,handleType:e}),void v.setState({connectionClickStartHandle:{nodeId:w,type:e,id:C}});const m=(0,s.oj)(n.target),h=t||l,{connection:f,isValid:b}=s.aQ.isValid(n.nativeEvent,{handle:{nodeId:w,id:C,type:e},connectionMode:d,fromNodeId:r.nodeId,fromHandleId:r.id||null,fromType:r.type,isValidConnection:h,flowId:u,doc:m,lib:c,nodeLookup:g});b&&f&&A(f);const S=structuredClone(p);delete S.inProgress,S.toPosition=S.toHandle?S.toHandle.position:null,i?.(n,S),v.setState({connectionClickStartHandle:null})}:void 0,ref:y,...S,children:g})}));const Ee={ArrowUp:{x:0,y:-1},ArrowDown:{x:0,y:1},ArrowLeft:{x:-1,y:0},ArrowRight:{x:1,y:0}},ke={input:function({data:e,isConnectable:n,sourcePosition:t=s.yX.Bottom}){return(0,o.jsxs)(o.Fragment,{children:[e?.label,(0,o.jsx)(we,{type:"source",position:t,isConnectable:n})]})},default:function({data:e,isConnectable:n,targetPosition:t=s.yX.Top,sourcePosition:i=s.yX.Bottom}){return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(we,{type:"target",position:t,isConnectable:n}),e?.label,(0,o.jsx)(we,{type:"source",position:i,isConnectable:n})]})},output:function({data:e,isConnectable:n,targetPosition:t=s.yX.Top}){return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(we,{type:"target",position:t,isConnectable:n}),e?.label]})},group:function(){return null}};const Ne=e=>{const{width:n,height:t,x:o,y:i}=(0,s.aZ)(e.nodeLookup,{filter:e=>!!e.selected});return{width:(0,s.kf)(n)?n:null,height:(0,s.kf)(t)?t:null,userSelectionActive:e.userSelectionActive,transformString:`translate(${e.transform[0]}px,${e.transform[1]}px) scale(${e.transform[2]}) translate(${o}px,${i}px)`}};function Me({onSelectionContextMenu:e,noPanClassName:n,disableKeyboardA11y:t}){const s=m(),{width:a,height:l,transformString:c,userSelectionActive:u}=p(Ne,d.x),g=Se(),h=(0,i.useRef)(null);if((0,i.useEffect)(()=>{t||h.current?.focus({preventScroll:!0})},[t]),be({nodeRef:h}),u||!a||!l)return null;const f=e?n=>{const t=s.getState().nodes.filter(e=>e.selected);e(n,t)}:void 0;return(0,o.jsx)("div",{className:(0,r.A)(["react-flow__nodesselection","react-flow__container",n]),style:{transform:c},children:(0,o.jsx)("div",{ref:h,className:"react-flow__nodesselection-rect",onContextMenu:f,tabIndex:t?void 0:-1,onKeyDown:t?void 0:e=>{Object.prototype.hasOwnProperty.call(Ee,e.key)&&(e.preventDefault(),g({direction:Ee[e.key],factor:e.shiftKey?4:1}))},style:{width:a,height:l}})})}const Pe="undefined"!=typeof window?window:void 0,De=e=>({nodesSelectionActive:e.nodesSelectionActive,userSelectionActive:e.userSelectionActive});function Oe({children:e,onPaneClick:n,onPaneMouseEnter:t,onPaneMouseMove:r,onPaneMouseLeave:s,onPaneContextMenu:a,onPaneScroll:d,paneClickDistance:l,deleteKeyCode:c,selectionKeyCode:u,selectionOnDrag:g,selectionMode:h,onSelectionStart:f,onSelectionEnd:b,multiSelectionKeyCode:S,panActivationKeyCode:y,zoomActivationKeyCode:C,elementsSelectable:x,zoomOnScroll:v,zoomOnPinch:w,panOnScroll:E,panOnScrollSpeed:k,panOnScrollMode:N,zoomOnDoubleClick:M,panOnDrag:P,defaultViewport:D,translateExtent:O,minZoom:R,maxZoom:L,preventScrolling:I,onSelectionContextMenu:A,noWheelClassName:j,noPanClassName:z,disableKeyboardA11y:_,onViewportChange:V,isControlledViewport:Z}){const{nodesSelectionActive:$,userSelectionActive:T}=p(De),H=B(u,{target:Pe}),X=B(y,{target:Pe}),F=X||P,K=X||E,W=g&&!0!==F,Y=H||T||W;return function({deleteKeyCode:e,multiSelectionKeyCode:n}){const t=m(),{deleteElements:o}=re(),r=B(e,{actInsideInputWithModifier:!1}),s=B(n,{target:ae});(0,i.useEffect)(()=>{if(r){const{edges:e,nodes:n}=t.getState();o({nodes:n.filter(se),edges:e.filter(se)}),t.setState({nodesSelectionActive:!1})}},[r]),(0,i.useEffect)(()=>{t.setState({multiSelectionActive:s})},[s])}({deleteKeyCode:c,multiSelectionKeyCode:S}),(0,o.jsx)(ce,{onPaneContextMenu:a,elementsSelectable:x,zoomOnScroll:v,zoomOnPinch:w,panOnScroll:K,panOnScrollSpeed:k,panOnScrollMode:N,zoomOnDoubleClick:M,panOnDrag:!H&&F,defaultViewport:D,translateExtent:O,minZoom:R,maxZoom:L,zoomActivationKeyCode:C,preventScrolling:I,noWheelClassName:j,noPanClassName:z,onViewportChange:V,isControlledViewport:Z,paneClickDistance:l,children:(0,o.jsxs)(he,{onSelectionStart:f,onSelectionEnd:b,onPaneClick:n,onPaneMouseEnter:t,onPaneMouseMove:r,onPaneMouseLeave:s,onPaneContextMenu:a,onPaneScroll:d,panOnDrag:F,isSelecting:!!Y,selectionMode:h,selectionKeyPressed:H,selectionOnDrag:W,children:[e,$&&(0,o.jsx)(Me,{onSelectionContextMenu:A,noPanClassName:z,disableKeyboardA11y:_})]})})}Oe.displayName="FlowRenderer";const Re=(0,i.memo)(Oe);function Le(e){return p((0,i.useCallback)((e=>n=>e?(0,s.U$)(n.nodeLookup,{x:0,y:0,width:n.width,height:n.height},n.transform,!0).map(e=>e.id):Array.from(n.nodeLookup.keys()))(e),[e]),d.x)}const Ie=e=>e.updateNodeInternals;function Ae({id:e,onClick:n,onMouseEnter:t,onMouseMove:a,onMouseLeave:l,onContextMenu:c,onDoubleClick:u,nodesDraggable:g,elementsSelectable:h,nodesConnectable:f,nodesFocusable:S,resizeObserver:y,noDragClassName:C,noPanClassName:x,disableKeyboardA11y:v,rfId:w,nodeTypes:E,nodeClickDistance:k,onError:N}){const{node:M,internals:P,isParent:D}=p(n=>{const t=n.nodeLookup.get(e),o=n.parentLookup.has(e);return{node:t,internals:t.internals,isParent:o}},d.x);let O=M.type||"default",R=E?.[O]||ke[O];void 0===R&&(N?.("003",s.xc.error003(O)),O="default",R=E?.default||ke.default);const L=!!(M.draggable||g&&void 0===M.draggable),I=!!(M.selectable||h&&void 0===M.selectable),A=!!(M.connectable||f&&void 0===M.connectable),j=!!(M.focusable||S&&void 0===M.focusable),z=m(),_=(0,s.QE)(M),V=function({node:e,nodeType:n,hasDimensions:t,resizeObserver:o}){const r=m(),s=(0,i.useRef)(null),a=(0,i.useRef)(null),d=(0,i.useRef)(e.sourcePosition),l=(0,i.useRef)(e.targetPosition),c=(0,i.useRef)(n),u=t&&!!e.internals.handleBounds;return(0,i.useEffect)(()=>{!s.current||e.hidden||u&&a.current===s.current||(a.current&&o?.unobserve(a.current),o?.observe(s.current),a.current=s.current)},[u,e.hidden]),(0,i.useEffect)(()=>()=>{a.current&&(o?.unobserve(a.current),a.current=null)},[]),(0,i.useEffect)(()=>{if(s.current){const t=c.current!==n,o=d.current!==e.sourcePosition,i=l.current!==e.targetPosition;(t||o||i)&&(c.current=n,d.current=e.sourcePosition,l.current=e.targetPosition,r.getState().updateNodeInternals(new Map([[e.id,{id:e.id,nodeElement:s.current,force:!0}]])))}},[e.id,n,e.sourcePosition,e.targetPosition]),s}({node:M,nodeType:O,hasDimensions:_,resizeObserver:y}),B=be({nodeRef:V,disabled:M.hidden||!L,noDragClassName:C,handleSelector:M.dragHandle,nodeId:e,isSelectable:I,nodeClickDistance:k}),Z=Se();if(M.hidden)return null;const $=(0,s.uD)(M),T=function(e){return void 0===e.internals.handleBounds?{width:e.width??e.initialWidth??e.style?.width,height:e.height??e.initialHeight??e.style?.height}:{width:e.width??e.style?.width,height:e.height??e.style?.height}}(M),H=I||L||n||t||a||l,X=t?e=>t(e,{...P.userNode}):void 0,F=a?e=>a(e,{...P.userNode}):void 0,K=l?e=>l(e,{...P.userNode}):void 0,W=c?e=>c(e,{...P.userNode}):void 0,Y=u?e=>u(e,{...P.userNode}):void 0;return(0,o.jsx)("div",{className:(0,r.A)(["react-flow__node",`react-flow__node-${O}`,{[x]:L},M.className,{selected:M.selected,selectable:I,parent:D,draggable:L,dragging:B}]),ref:V,style:{zIndex:P.z,transform:`translate(${P.positionAbsolute.x}px,${P.positionAbsolute.y}px)`,pointerEvents:H?"all":"none",visibility:_?"visible":"hidden",...M.style,...T},"data-id":e,"data-testid":`rf__node-${e}`,onMouseEnter:X,onMouseMove:F,onMouseLeave:K,onContextMenu:W,onClick:t=>{const{selectNodesOnDrag:o,nodeDragThreshold:i}=z.getState();I&&(!o||!L||i>0)&&fe({id:e,store:z,nodeRef:V}),n&&n(t,{...P.userNode})},onDoubleClick:Y,onKeyDown:j?n=>{if(!(0,s.v5)(n.nativeEvent)&&!v)if(s.tn.includes(n.key)&&I){const t="Escape"===n.key;fe({id:e,store:z,unselect:t,nodeRef:V})}else if(L&&M.selected&&Object.prototype.hasOwnProperty.call(Ee,n.key)){n.preventDefault();const{ariaLabelConfig:e}=z.getState();z.setState({ariaLiveMessage:e["node.a11yDescription.ariaLiveMessage"]({direction:n.key.replace("Arrow","").toLowerCase(),x:~~P.positionAbsolute.x,y:~~P.positionAbsolute.y})}),Z({direction:Ee[n.key],factor:n.shiftKey?4:1})}}:void 0,tabIndex:j?0:void 0,onFocus:j?()=>{if(v||!V.current?.matches(":focus-visible"))return;const{transform:n,width:t,height:o,autoPanOnNodeFocus:i,setCenter:r}=z.getState();if(!i)return;(0,s.U$)(new Map([[e,M]]),{x:0,y:0,width:t,height:o},n,!0).length>0||r(M.position.x+$.width/2,M.position.y+$.height/2,{zoom:n[2]})}:void 0,role:M.ariaRole??(j?"group":void 0),"aria-roledescription":"node","aria-describedby":v?void 0:`${b}-${w}`,"aria-label":M.ariaLabel,...M.domAttributes,children:(0,o.jsx)(Ce,{value:e,children:(0,o.jsx)(R,{id:e,data:M.data,type:O,positionAbsoluteX:P.positionAbsolute.x,positionAbsoluteY:P.positionAbsolute.y,selected:M.selected??!1,selectable:I,draggable:L,deletable:M.deletable??!0,isConnectable:A,sourcePosition:M.sourcePosition,targetPosition:M.targetPosition,dragging:B,dragHandle:M.dragHandle,zIndex:P.z,parentId:M.parentId,...$})})})}const je=e=>({nodesDraggable:e.nodesDraggable,nodesConnectable:e.nodesConnectable,nodesFocusable:e.nodesFocusable,elementsSelectable:e.elementsSelectable,onError:e.onError});function ze(e){const{nodesDraggable:n,nodesConnectable:t,nodesFocusable:r,elementsSelectable:s,onError:a}=p(je,d.x),l=Le(e.onlyRenderVisibleElements),c=function(){const e=p(Ie),[n]=(0,i.useState)(()=>"undefined"==typeof ResizeObserver?null:new ResizeObserver(n=>{const t=new Map;n.forEach(e=>{const n=e.target.getAttribute("data-id");t.set(n,{id:n,nodeElement:e.target,force:!0})}),e(t)}));return(0,i.useEffect)(()=>()=>{n?.disconnect()},[n]),n}();return(0,o.jsx)("div",{className:"react-flow__nodes",style:de,children:l.map(i=>(0,o.jsx)(Ae,{id:i,nodeTypes:e.nodeTypes,nodeExtent:e.nodeExtent,onClick:e.onNodeClick,onMouseEnter:e.onNodeMouseEnter,onMouseMove:e.onNodeMouseMove,onMouseLeave:e.onNodeMouseLeave,onContextMenu:e.onNodeContextMenu,onDoubleClick:e.onNodeDoubleClick,noDragClassName:e.noDragClassName,noPanClassName:e.noPanClassName,rfId:e.rfId,disableKeyboardA11y:e.disableKeyboardA11y,resizeObserver:c,nodesDraggable:n,nodesConnectable:t,nodesFocusable:r,elementsSelectable:s,nodeClickDistance:e.nodeClickDistance,onError:a},i))})}ze.displayName="NodeRenderer";const _e=(0,i.memo)(ze);const Ve={[s.TG.Arrow]:({color:e="none",strokeWidth:n=1})=>(0,o.jsx)("polyline",{style:{stroke:e,strokeWidth:n},strokeLinecap:"round",strokeLinejoin:"round",fill:"none",points:"-5,-4 0,0 -5,4"}),[s.TG.ArrowClosed]:({color:e="none",strokeWidth:n=1})=>(0,o.jsx)("polyline",{style:{stroke:e,fill:e,strokeWidth:n},strokeLinecap:"round",strokeLinejoin:"round",points:"-5,-4 0,0 -5,4 -5,-4"})};const Be=({id:e,type:n,color:t,width:r=12.5,height:a=12.5,markerUnits:d="strokeWidth",strokeWidth:l,orient:c="auto-start-reverse"})=>{const u=function(e){const n=m();return(0,i.useMemo)(()=>Object.prototype.hasOwnProperty.call(Ve,e)?Ve[e]:(n.getState().onError?.("009",s.xc.error009(e)),null),[e])}(n);return u?(0,o.jsx)("marker",{className:"react-flow__arrowhead",id:e,markerWidth:`${r}`,markerHeight:`${a}`,viewBox:"-10 -10 20 20",markerUnits:d,orient:c,refX:"0",refY:"0",children:(0,o.jsx)(u,{color:t,strokeWidth:l})}):null},Ze=({defaultColor:e,rfId:n})=>{const t=p(e=>e.edges),r=p(e=>e.defaultEdgeOptions),a=(0,i.useMemo)(()=>(0,s.Hm)(t,{id:n,defaultColor:e,defaultMarkerStart:r?.markerStart,defaultMarkerEnd:r?.markerEnd}),[t,r,n,e]);return a.length?(0,o.jsx)("svg",{className:"react-flow__marker","aria-hidden":"true",children:(0,o.jsx)("defs",{children:a.map(e=>(0,o.jsx)(Be,{id:e.id,type:e.type,color:e.color,width:e.width,height:e.height,markerUnits:e.markerUnits,strokeWidth:e.strokeWidth,orient:e.orient},e.id))})}):null};Ze.displayName="MarkerDefinitions";var $e=(0,i.memo)(Ze);function Te({x:e,y:n,label:t,labelStyle:s,labelShowBg:a=!0,labelBgStyle:d,labelBgPadding:l=[2,4],labelBgBorderRadius:c=2,children:u,className:g,...p}){const[m,h]=(0,i.useState)({x:1,y:0,width:0,height:0}),f=(0,r.A)(["react-flow__edge-textwrapper",g]),b=(0,i.useRef)(null);return(0,i.useEffect)(()=>{if(b.current){const e=b.current.getBBox();h({x:e.x,y:e.y,width:e.width,height:e.height})}},[t]),t?(0,o.jsxs)("g",{transform:`translate(${e-m.width/2} ${n-m.height/2})`,className:f,visibility:m.width?"visible":"hidden",...p,children:[a&&(0,o.jsx)("rect",{width:m.width+2*l[0],x:-l[0],y:-l[1],height:m.height+2*l[1],className:"react-flow__edge-textbg",style:d,rx:c,ry:c}),(0,o.jsx)("text",{className:"react-flow__edge-text",y:m.height/2,dy:"0.3em",ref:b,style:s,children:t}),u]}):null}Te.displayName="EdgeText";const He=(0,i.memo)(Te);function Xe({path:e,labelX:n,labelY:t,label:i,labelStyle:a,labelShowBg:d,labelBgStyle:l,labelBgPadding:c,labelBgBorderRadius:u,interactionWidth:g=20,...p}){return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("path",{...p,d:e,fill:"none",className:(0,r.A)(["react-flow__edge-path",p.className])}),g&&(0,o.jsx)("path",{d:e,fill:"none",strokeOpacity:0,strokeWidth:g,className:"react-flow__edge-interaction"}),i&&(0,s.kf)(n)&&(0,s.kf)(t)?(0,o.jsx)(He,{x:n,y:t,label:i,labelStyle:a,labelShowBg:d,labelBgStyle:l,labelBgPadding:c,labelBgBorderRadius:u}):null]})}function Fe({pos:e,x1:n,y1:t,x2:o,y2:i}){return e===s.yX.Left||e===s.yX.Right?[.5*(n+o),t]:[n,.5*(t+i)]}function Ke({sourceX:e,sourceY:n,sourcePosition:t=s.yX.Bottom,targetX:o,targetY:i,targetPosition:r=s.yX.Top}){const[a,d]=Fe({pos:t,x1:e,y1:n,x2:o,y2:i}),[l,c]=Fe({pos:r,x1:o,y1:i,x2:e,y2:n}),[u,g,p,m]=(0,s.e_)({sourceX:e,sourceY:n,targetX:o,targetY:i,sourceControlX:a,sourceControlY:d,targetControlX:l,targetControlY:c});return[`M${e},${n} C${a},${d} ${l},${c} ${o},${i}`,u,g,p,m]}function We(e){return(0,i.memo)(({id:n,sourceX:t,sourceY:i,targetX:r,targetY:s,sourcePosition:a,targetPosition:d,label:l,labelStyle:c,labelShowBg:u,labelBgStyle:g,labelBgPadding:p,labelBgBorderRadius:m,style:h,markerEnd:f,markerStart:b,interactionWidth:S})=>{const[y,C,x]=Ke({sourceX:t,sourceY:i,sourcePosition:a,targetX:r,targetY:s,targetPosition:d}),v=e.isInternal?void 0:n;return(0,o.jsx)(Xe,{id:v,path:y,labelX:C,labelY:x,label:l,labelStyle:c,labelShowBg:u,labelBgStyle:g,labelBgPadding:p,labelBgBorderRadius:m,style:h,markerEnd:f,markerStart:b,interactionWidth:S})})}const Ye=We({isInternal:!1}),Qe=We({isInternal:!0});function Ge(e){return(0,i.memo)(({id:n,sourceX:t,sourceY:i,targetX:r,targetY:a,label:d,labelStyle:l,labelShowBg:c,labelBgStyle:u,labelBgPadding:g,labelBgBorderRadius:p,style:m,sourcePosition:h=s.yX.Bottom,targetPosition:f=s.yX.Top,markerEnd:b,markerStart:S,pathOptions:y,interactionWidth:C})=>{const[x,v,w]=(0,s.oN)({sourceX:t,sourceY:i,sourcePosition:h,targetX:r,targetY:a,targetPosition:f,borderRadius:y?.borderRadius,offset:y?.offset,stepPosition:y?.stepPosition}),E=e.isInternal?void 0:n;return(0,o.jsx)(Xe,{id:E,path:x,labelX:v,labelY:w,label:d,labelStyle:l,labelShowBg:c,labelBgStyle:u,labelBgPadding:g,labelBgBorderRadius:p,style:m,markerEnd:b,markerStart:S,interactionWidth:C})})}Ye.displayName="SimpleBezierEdge",Qe.displayName="SimpleBezierEdgeInternal";const Ue=Ge({isInternal:!1}),qe=Ge({isInternal:!0});function Je(e){return(0,i.memo)(({id:n,...t})=>{const r=e.isInternal?void 0:n;return(0,o.jsx)(Ue,{...t,id:r,pathOptions:(0,i.useMemo)(()=>({borderRadius:0,offset:t.pathOptions?.offset}),[t.pathOptions?.offset])})})}Ue.displayName="SmoothStepEdge",qe.displayName="SmoothStepEdgeInternal";const en=Je({isInternal:!1}),nn=Je({isInternal:!0});function tn(e){return(0,i.memo)(({id:n,sourceX:t,sourceY:i,targetX:r,targetY:a,label:d,labelStyle:l,labelShowBg:c,labelBgStyle:u,labelBgPadding:g,labelBgBorderRadius:p,style:m,markerEnd:h,markerStart:f,interactionWidth:b})=>{const[S,y,C]=(0,s.ah)({sourceX:t,sourceY:i,targetX:r,targetY:a}),x=e.isInternal?void 0:n;return(0,o.jsx)(Xe,{id:x,path:S,labelX:y,labelY:C,label:d,labelStyle:l,labelShowBg:c,labelBgStyle:u,labelBgPadding:g,labelBgBorderRadius:p,style:m,markerEnd:h,markerStart:f,interactionWidth:b})})}en.displayName="StepEdge",nn.displayName="StepEdgeInternal";const on=tn({isInternal:!1}),rn=tn({isInternal:!0});function sn(e){return(0,i.memo)(({id:n,sourceX:t,sourceY:i,targetX:r,targetY:a,sourcePosition:d=s.yX.Bottom,targetPosition:l=s.yX.Top,label:c,labelStyle:u,labelShowBg:g,labelBgStyle:p,labelBgPadding:m,labelBgBorderRadius:h,style:f,markerEnd:b,markerStart:S,pathOptions:y,interactionWidth:C})=>{const[x,v,w]=(0,s.Fp)({sourceX:t,sourceY:i,sourcePosition:d,targetX:r,targetY:a,targetPosition:l,curvature:y?.curvature}),E=e.isInternal?void 0:n;return(0,o.jsx)(Xe,{id:E,path:x,labelX:v,labelY:w,label:c,labelStyle:u,labelShowBg:g,labelBgStyle:p,labelBgPadding:m,labelBgBorderRadius:h,style:f,markerEnd:b,markerStart:S,interactionWidth:C})})}on.displayName="StraightEdge",rn.displayName="StraightEdgeInternal";const an=sn({isInternal:!1}),dn=sn({isInternal:!0});an.displayName="BezierEdge",dn.displayName="BezierEdgeInternal";const ln={default:dn,straight:rn,step:nn,smoothstep:qe,simplebezier:Qe},cn={sourceX:null,sourceY:null,targetX:null,targetY:null,sourcePosition:null,targetPosition:null},un=(e,n,t)=>t===s.yX.Left?e-n:t===s.yX.Right?e+n:e,gn=(e,n,t)=>t===s.yX.Top?e-n:t===s.yX.Bottom?e+n:e,pn="react-flow__edgeupdater";function mn({position:e,centerX:n,centerY:t,radius:i=10,onMouseDown:s,onMouseEnter:a,onMouseOut:d,type:l}){return(0,o.jsx)("circle",{onMouseDown:s,onMouseEnter:a,onMouseOut:d,className:(0,r.A)([pn,`${pn}-${l}`]),cx:un(n,i,e),cy:gn(t,i,e),r:i,stroke:"transparent",fill:"transparent"})}function hn({isReconnectable:e,reconnectRadius:n,edge:t,sourceX:i,sourceY:r,targetX:a,targetY:d,sourcePosition:l,targetPosition:c,onReconnect:u,onReconnectStart:g,onReconnectEnd:p,setReconnecting:h,setUpdateHover:f}){const b=m(),S=(e,n)=>{if(0!==e.button)return;const{autoPanOnConnect:o,domNode:i,isValidConnection:r,connectionMode:a,connectionRadius:d,lib:l,onConnectStart:c,onConnectEnd:m,cancelConnection:f,nodeLookup:S,rfId:y,panBy:C,updateConnection:x}=b.getState(),v="target"===n.type;s.aQ.onPointerDown(e.nativeEvent,{autoPanOnConnect:o,connectionMode:a,connectionRadius:d,domNode:i,handleId:n.id,nodeId:n.nodeId,nodeLookup:S,isTarget:v,edgeUpdaterType:n.type,lib:l,flowId:y,cancelConnection:f,panBy:C,isValidConnection:r,onConnect:e=>u?.(t,e),onConnectStart:(o,i)=>{h(!0),g?.(e,t,n.type),c?.(o,i)},onConnectEnd:m,onReconnectEnd:(e,o)=>{h(!1),p?.(e,t,n.type,o)},updateConnection:x,getTransform:()=>b.getState().transform,getFromHandle:()=>b.getState().connection.fromHandle,dragThreshold:b.getState().connectionDragThreshold})},y=()=>f(!0),C=()=>f(!1);return(0,o.jsxs)(o.Fragment,{children:[(!0===e||"source"===e)&&(0,o.jsx)(mn,{position:l,centerX:i,centerY:r,radius:n,onMouseDown:e=>S(e,{nodeId:t.target,id:t.targetHandle??null,type:"target"}),onMouseEnter:y,onMouseOut:C,type:"source"}),(!0===e||"target"===e)&&(0,o.jsx)(mn,{position:c,centerX:a,centerY:d,radius:n,onMouseDown:e=>S(e,{nodeId:t.source,id:t.sourceHandle??null,type:"source"}),onMouseEnter:y,onMouseOut:C,type:"target"})]})}function fn({id:e,edgesFocusable:n,edgesReconnectable:t,elementsSelectable:a,onClick:l,onDoubleClick:c,onContextMenu:u,onMouseEnter:g,onMouseMove:h,onMouseLeave:f,reconnectRadius:b,onReconnect:y,onReconnectStart:C,onReconnectEnd:x,rfId:v,edgeTypes:w,noPanClassName:E,onError:k,disableKeyboardA11y:N}){let M=p(n=>n.edgeLookup.get(e));const P=p(e=>e.defaultEdgeOptions);M=P?{...P,...M}:M;let D=M.type||"default",O=w?.[D]||ln[D];void 0===O&&(k?.("011",s.xc.error011(D)),D="default",O=w?.default||ln.default);const R=!!(M.focusable||n&&void 0===M.focusable),L=void 0!==y&&(M.reconnectable||t&&void 0===M.reconnectable),I=!!(M.selectable||a&&void 0===M.selectable),A=(0,i.useRef)(null),[j,z]=(0,i.useState)(!1),[_,V]=(0,i.useState)(!1),B=m(),{zIndex:Z,sourceX:$,sourceY:T,targetX:H,targetY:X,sourcePosition:F,targetPosition:K}=p((0,i.useCallback)(n=>{const t=n.nodeLookup.get(M.source),o=n.nodeLookup.get(M.target);if(!t||!o)return{zIndex:M.zIndex,...cn};const i=(0,s.b5)({id:e,sourceNode:t,targetNode:o,sourceHandle:M.sourceHandle||null,targetHandle:M.targetHandle||null,connectionMode:n.connectionMode,onError:k});return{zIndex:(0,s.qX)({selected:M.selected,zIndex:M.zIndex,sourceNode:t,targetNode:o,elevateOnSelect:n.elevateEdgesOnSelect}),...i||cn}},[M.source,M.target,M.sourceHandle,M.targetHandle,M.selected,M.zIndex]),d.x),W=(0,i.useMemo)(()=>M.markerStart?`url('#${(0,s.aW)(M.markerStart,v)}')`:void 0,[M.markerStart,v]),Y=(0,i.useMemo)(()=>M.markerEnd?`url('#${(0,s.aW)(M.markerEnd,v)}')`:void 0,[M.markerEnd,v]);if(M.hidden||null===$||null===T||null===H||null===X)return null;const Q=c?e=>{c(e,{...M})}:void 0,G=u?e=>{u(e,{...M})}:void 0,U=g?e=>{g(e,{...M})}:void 0,q=h?e=>{h(e,{...M})}:void 0,J=f?e=>{f(e,{...M})}:void 0;return(0,o.jsx)("svg",{style:{zIndex:Z},children:(0,o.jsxs)("g",{className:(0,r.A)(["react-flow__edge",`react-flow__edge-${D}`,M.className,E,{selected:M.selected,animated:M.animated,inactive:!I&&!l,updating:j,selectable:I}]),onClick:n=>{const{addSelectedEdges:t,unselectNodesAndEdges:o,multiSelectionActive:i}=B.getState();I&&(B.setState({nodesSelectionActive:!1}),M.selected&&i?(o({nodes:[],edges:[M]}),A.current?.blur()):t([e])),l&&l(n,M)},onDoubleClick:Q,onContextMenu:G,onMouseEnter:U,onMouseMove:q,onMouseLeave:J,onKeyDown:R?n=>{if(!N&&s.tn.includes(n.key)&&I){const{unselectNodesAndEdges:t,addSelectedEdges:o}=B.getState();"Escape"===n.key?(A.current?.blur(),t({edges:[M]})):o([e])}}:void 0,tabIndex:R?0:void 0,role:M.ariaRole??(R?"group":"img"),"aria-roledescription":"edge","data-id":e,"data-testid":`rf__edge-${e}`,"aria-label":null===M.ariaLabel?void 0:M.ariaLabel||`Edge from ${M.source} to ${M.target}`,"aria-describedby":R?`${S}-${v}`:void 0,ref:A,...M.domAttributes,children:[!_&&(0,o.jsx)(O,{id:e,source:M.source,target:M.target,type:M.type,selected:M.selected,animated:M.animated,selectable:I,deletable:M.deletable??!0,label:M.label,labelStyle:M.labelStyle,labelShowBg:M.labelShowBg,labelBgStyle:M.labelBgStyle,labelBgPadding:M.labelBgPadding,labelBgBorderRadius:M.labelBgBorderRadius,sourceX:$,sourceY:T,targetX:H,targetY:X,sourcePosition:F,targetPosition:K,data:M.data,style:M.style,sourceHandleId:M.sourceHandle,targetHandleId:M.targetHandle,markerStart:W,markerEnd:Y,pathOptions:"pathOptions"in M?M.pathOptions:void 0,interactionWidth:M.interactionWidth}),L&&(0,o.jsx)(hn,{edge:M,isReconnectable:L,reconnectRadius:b,onReconnect:y,onReconnectStart:C,onReconnectEnd:x,sourceX:$,sourceY:T,targetX:H,targetY:X,sourcePosition:F,targetPosition:K,setUpdateHover:z,setReconnecting:V})]})})}const bn=e=>({edgesFocusable:e.edgesFocusable,edgesReconnectable:e.edgesReconnectable,elementsSelectable:e.elementsSelectable,connectionMode:e.connectionMode,onError:e.onError});function Sn({defaultMarkerColor:e,onlyRenderVisibleElements:n,rfId:t,edgeTypes:r,noPanClassName:a,onReconnect:l,onEdgeContextMenu:c,onEdgeMouseEnter:u,onEdgeMouseMove:g,onEdgeMouseLeave:m,onEdgeClick:h,reconnectRadius:f,onEdgeDoubleClick:b,onReconnectStart:S,onReconnectEnd:y,disableKeyboardA11y:C}){const{edgesFocusable:x,edgesReconnectable:v,elementsSelectable:w,onError:E}=p(bn,d.x),k=(N=n,p((0,i.useCallback)(e=>{if(!N)return e.edges.map(e=>e.id);const n=[];if(e.width&&e.height)for(const t of e.edges){const o=e.nodeLookup.get(t.source),i=e.nodeLookup.get(t.target);o&&i&&(0,s.uj)({sourceNode:o,targetNode:i,width:e.width,height:e.height,transform:e.transform})&&n.push(t.id)}return n},[N]),d.x));var N;return(0,o.jsxs)("div",{className:"react-flow__edges",children:[(0,o.jsx)($e,{defaultColor:e,rfId:t}),k.map(e=>(0,o.jsx)(fn,{id:e,edgesFocusable:x,edgesReconnectable:v,elementsSelectable:w,noPanClassName:a,onReconnect:l,onContextMenu:c,onMouseEnter:u,onMouseMove:g,onMouseLeave:m,onClick:h,reconnectRadius:f,onDoubleClick:b,onReconnectStart:S,onReconnectEnd:y,rfId:t,onError:E,edgeTypes:r,disableKeyboardA11y:C},e))]})}Sn.displayName="EdgeRenderer";const yn=(0,i.memo)(Sn),Cn=e=>`translate(${e.transform[0]}px,${e.transform[1]}px) scale(${e.transform[2]})`;function xn({children:e}){const n=p(Cn);return(0,o.jsx)("div",{className:"react-flow__viewport xyflow__viewport react-flow__container",style:{transform:n},children:e})}const vn=e=>e.panZoom?.syncViewport;function wn(e){return e.connection.inProgress?{...e.connection,to:(0,s.Ff)(e.connection.to,e.transform)}:{...e.connection}}function En(e){const n=function(e){if(e)return n=>{const t=wn(n);return e(t)};return wn}(e);return p(n,d.x)}const kn=e=>({nodesConnectable:e.nodesConnectable,isValid:e.connection.isValid,inProgress:e.connection.inProgress,width:e.width,height:e.height});function Nn({containerStyle:e,style:n,type:t,component:i}){const{nodesConnectable:a,width:l,height:c,isValid:u,inProgress:g}=p(kn,d.x);return!!(l&&a&&g)?(0,o.jsx)("svg",{style:e,width:l,height:c,className:"react-flow__connectionline react-flow__container",children:(0,o.jsx)("g",{className:(0,r.A)(["react-flow__connection",(0,s.HF)(u)]),children:(0,o.jsx)(Mn,{style:n,type:t,CustomComponent:i,isValid:u})})}):null}const Mn=({style:e,type:n=s.Do.Bezier,CustomComponent:t,isValid:i})=>{const{inProgress:r,from:a,fromNode:d,fromHandle:l,fromPosition:c,to:u,toNode:g,toHandle:p,toPosition:m}=En();if(!r)return;if(t)return(0,o.jsx)(t,{connectionLineType:n,connectionLineStyle:e,fromNode:d,fromHandle:l,fromX:a.x,fromY:a.y,toX:u.x,toY:u.y,fromPosition:c,toPosition:m,connectionStatus:(0,s.HF)(i),toNode:g,toHandle:p});let h="";const f={sourceX:a.x,sourceY:a.y,sourcePosition:c,targetX:u.x,targetY:u.y,targetPosition:m};switch(n){case s.Do.Bezier:[h]=(0,s.Fp)(f);break;case s.Do.SimpleBezier:[h]=Ke(f);break;case s.Do.Step:[h]=(0,s.oN)({...f,borderRadius:0});break;case s.Do.SmoothStep:[h]=(0,s.oN)(f);break;default:[h]=(0,s.ah)(f)}return(0,o.jsx)("path",{d:h,fill:"none",className:"react-flow__connection-path",style:e})};Mn.displayName="ConnectionLine";const Pn={};function Dn(e=Pn){(0,i.useRef)(e),m();(0,i.useEffect)(()=>{0},[e])}function On({nodeTypes:e,edgeTypes:n,onInit:t,onNodeClick:r,onEdgeClick:s,onNodeDoubleClick:a,onEdgeDoubleClick:d,onNodeMouseEnter:l,onNodeMouseMove:c,onNodeMouseLeave:u,onNodeContextMenu:g,onSelectionContextMenu:h,onSelectionStart:f,onSelectionEnd:b,connectionLineType:S,connectionLineStyle:y,connectionLineComponent:C,connectionLineContainerStyle:x,selectionKeyCode:v,selectionOnDrag:w,selectionMode:E,multiSelectionKeyCode:k,panActivationKeyCode:N,zoomActivationKeyCode:M,deleteKeyCode:P,onlyRenderVisibleElements:D,elementsSelectable:O,defaultViewport:R,translateExtent:L,minZoom:I,maxZoom:A,preventScrolling:j,defaultMarkerColor:z,zoomOnScroll:_,zoomOnPinch:V,panOnScroll:B,panOnScrollSpeed:Z,panOnScrollMode:$,zoomOnDoubleClick:T,panOnDrag:H,onPaneClick:X,onPaneMouseEnter:F,onPaneMouseMove:K,onPaneMouseLeave:W,onPaneScroll:Y,onPaneContextMenu:Q,paneClickDistance:G,nodeClickDistance:U,onEdgeContextMenu:q,onEdgeMouseEnter:J,onEdgeMouseMove:ee,onEdgeMouseLeave:ne,reconnectRadius:te,onReconnect:oe,onReconnectStart:ie,onReconnectEnd:se,noDragClassName:ae,noWheelClassName:de,noPanClassName:le,disableKeyboardA11y:ce,nodeExtent:ue,rfId:ge,viewport:pe,onViewportChange:me}){return Dn(e),Dn(n),m(),(0,i.useRef)(!1),(0,i.useEffect)(()=>{},[]),function(e){const n=re(),t=(0,i.useRef)(!1);(0,i.useEffect)(()=>{!t.current&&n.viewportInitialized&&e&&(setTimeout(()=>e(n),1),t.current=!0)},[e,n.viewportInitialized])}(t),function(e){const n=p(vn),t=m();(0,i.useEffect)(()=>{e&&(n?.(e),t.setState({transform:[e.x,e.y,e.zoom]}))},[e,n])}(pe),(0,o.jsx)(Re,{onPaneClick:X,onPaneMouseEnter:F,onPaneMouseMove:K,onPaneMouseLeave:W,onPaneContextMenu:Q,onPaneScroll:Y,paneClickDistance:G,deleteKeyCode:P,selectionKeyCode:v,selectionOnDrag:w,selectionMode:E,onSelectionStart:f,onSelectionEnd:b,multiSelectionKeyCode:k,panActivationKeyCode:N,zoomActivationKeyCode:M,elementsSelectable:O,zoomOnScroll:_,zoomOnPinch:V,zoomOnDoubleClick:T,panOnScroll:B,panOnScrollSpeed:Z,panOnScrollMode:$,panOnDrag:H,defaultViewport:R,translateExtent:L,minZoom:I,maxZoom:A,onSelectionContextMenu:h,preventScrolling:j,noDragClassName:ae,noWheelClassName:de,noPanClassName:le,disableKeyboardA11y:ce,onViewportChange:me,isControlledViewport:!!pe,children:(0,o.jsxs)(xn,{children:[(0,o.jsx)(yn,{edgeTypes:n,onEdgeClick:s,onEdgeDoubleClick:d,onReconnect:oe,onReconnectStart:ie,onReconnectEnd:se,onlyRenderVisibleElements:D,onEdgeContextMenu:q,onEdgeMouseEnter:J,onEdgeMouseMove:ee,onEdgeMouseLeave:ne,reconnectRadius:te,defaultMarkerColor:z,noPanClassName:le,disableKeyboardA11y:ce,rfId:ge}),(0,o.jsx)(Nn,{style:y,type:S,component:C,containerStyle:x}),(0,o.jsx)("div",{className:"react-flow__edgelabel-renderer"}),(0,o.jsx)(_e,{nodeTypes:e,onNodeClick:r,onNodeDoubleClick:a,onNodeMouseEnter:l,onNodeMouseMove:c,onNodeMouseLeave:u,onNodeContextMenu:g,nodeClickDistance:U,onlyRenderVisibleElements:D,noPanClassName:le,noDragClassName:ae,disableKeyboardA11y:ce,nodeExtent:ue,rfId:ge}),(0,o.jsx)("div",{className:"react-flow__viewport-portal"})]})})}On.displayName="GraphView";const Rn=(0,i.memo)(On),Ln=({nodes:e,edges:n,defaultNodes:t,defaultEdges:o,width:i,height:r,fitView:a,fitViewOptions:d,minZoom:l=.5,maxZoom:c=2,nodeOrigin:u,nodeExtent:g}={})=>{const p=new Map,m=new Map,h=new Map,f=new Map,b=o??n??[],S=t??e??[],y=u??[0,0],C=g??s.ZO;(0,s.qn)(h,f,b);const x=(0,s.bi)(S,p,m,{nodeOrigin:y,nodeExtent:C,elevateNodesOnSelect:!1});let v=[0,0,1];if(a&&i&&r){const e=(0,s.aZ)(p,{filter:e=>!(!e.width&&!e.initialWidth||!e.height&&!e.initialHeight)}),{x:n,y:t,zoom:o}=(0,s.R4)(e,i,r,l,c,d?.padding??.1);v=[n,t,o]}return{rfId:"1",width:0,height:0,transform:v,nodes:S,nodesInitialized:x,nodeLookup:p,parentLookup:m,edges:b,edgeLookup:f,connectionLookup:h,onNodesChange:null,onEdgesChange:null,hasDefaultNodes:void 0!==t,hasDefaultEdges:void 0!==o,panZoom:null,minZoom:l,maxZoom:c,translateExtent:s.ZO,nodeExtent:C,nodesSelectionActive:!1,userSelectionActive:!1,userSelectionRect:null,connectionMode:s.WZ.Strict,domNode:null,paneDragging:!1,noPanClassName:"nopan",nodeOrigin:y,nodeDragThreshold:1,connectionDragThreshold:1,snapGrid:[15,15],snapToGrid:!1,nodesDraggable:!0,nodesConnectable:!0,nodesFocusable:!0,edgesFocusable:!0,edgesReconnectable:!0,elementsSelectable:!0,elevateNodesOnSelect:!0,elevateEdgesOnSelect:!1,selectNodesOnDrag:!0,multiSelectionActive:!1,fitViewQueued:a??!1,fitViewOptions:d,fitViewResolver:null,connection:{...s.bK},connectionClickStartHandle:null,connectOnClick:!0,ariaLiveMessage:"",autoPanOnConnect:!0,autoPanOnNodeDrag:!0,autoPanOnNodeFocus:!0,autoPanSpeed:15,connectionRadius:20,onError:s.KE,isValidConnection:void 0,onSelectionChangeHandlers:[],lib:"react",debug:!1,ariaLabelConfig:s.tM}},In=({nodes:e,edges:n,defaultNodes:t,defaultEdges:o,width:i,height:r,fitView:d,fitViewOptions:l,minZoom:c,maxZoom:u,nodeOrigin:g,nodeExtent:p})=>(0,a.h)((a,m)=>{async function h(){const{nodeLookup:e,panZoom:n,fitViewOptions:t,fitViewResolver:o,width:i,height:r,minZoom:d,maxZoom:l}=m();n&&(await(0,s.IO)({nodes:e,width:i,height:r,panZoom:n,minZoom:d,maxZoom:l},t),o?.resolve(!0),a({fitViewResolver:null}))}return{...Ln({nodes:e,edges:n,width:i,height:r,fitView:d,fitViewOptions:l,minZoom:c,maxZoom:u,nodeOrigin:g,nodeExtent:p,defaultNodes:t,defaultEdges:o}),setNodes:e=>{const{nodeLookup:n,parentLookup:t,nodeOrigin:o,elevateNodesOnSelect:i,fitViewQueued:r}=m(),d=(0,s.bi)(e,n,t,{nodeOrigin:o,nodeExtent:p,elevateNodesOnSelect:i,checkEquality:!0});r&&d?(h(),a({nodes:e,nodesInitialized:d,fitViewQueued:!1,fitViewOptions:void 0})):a({nodes:e,nodesInitialized:d})},setEdges:e=>{const{connectionLookup:n,edgeLookup:t}=m();(0,s.qn)(n,t,e),a({edges:e})},setDefaultNodesAndEdges:(e,n)=>{if(e){const{setNodes:n}=m();n(e),a({hasDefaultNodes:!0})}if(n){const{setEdges:e}=m();e(n),a({hasDefaultEdges:!0})}},updateNodeInternals:e=>{const{triggerNodeChanges:n,nodeLookup:t,parentLookup:o,domNode:i,nodeOrigin:r,nodeExtent:d,debug:l,fitViewQueued:c}=m(),{changes:u,updatedInternals:g}=(0,s.uL)(e,t,o,i,r,d);g&&((0,s.vS)(t,o,{nodeOrigin:r,nodeExtent:d}),c?(h(),a({fitViewQueued:!1,fitViewOptions:void 0})):a({}),u?.length>0&&(l&&console.log("React Flow: trigger node changes",u),n?.(u)))},updateNodePositions:(e,n=!1)=>{const t=[],o=[],{nodeLookup:i,triggerNodeChanges:r}=m();for(const[s,a]of e){const e=i.get(s),r=!!(e?.expandParent&&e?.parentId&&a?.position),d={id:s,type:"position",position:r?{x:Math.max(0,a.position.x),y:Math.max(0,a.position.y)}:a.position,dragging:n};r&&e.parentId&&t.push({id:s,parentId:e.parentId,rect:{...a.internals.positionAbsolute,width:a.measured.width??0,height:a.measured.height??0}}),o.push(d)}if(t.length>0){const{parentLookup:e,nodeOrigin:n}=m(),r=(0,s.r8)(t,i,e,n);o.push(...r)}r(o)},triggerNodeChanges:e=>{const{onNodesChange:n,setNodes:t,nodes:o,hasDefaultNodes:i,debug:r}=m();if(e?.length){if(i){t(F(e,o))}r&&console.log("React Flow: trigger node changes",e),n?.(e)}},triggerEdgeChanges:e=>{const{onEdgesChange:n,setEdges:t,edges:o,hasDefaultEdges:i,debug:r}=m();if(e?.length){if(i){t(K(e,o))}r&&console.log("React Flow: trigger edge changes",e),n?.(e)}},addSelectedNodes:e=>{const{multiSelectionActive:n,edgeLookup:t,nodeLookup:o,triggerNodeChanges:i,triggerEdgeChanges:r}=m();if(n){return void i(e.map(e=>W(e,!0)))}i(Y(o,new Set([...e]),!0)),r(Y(t))},addSelectedEdges:e=>{const{multiSelectionActive:n,edgeLookup:t,nodeLookup:o,triggerNodeChanges:i,triggerEdgeChanges:r}=m();if(n){return void r(e.map(e=>W(e,!0)))}r(Y(t,new Set([...e]))),i(Y(o,new Set,!0))},unselectNodesAndEdges:({nodes:e,edges:n}={})=>{const{edges:t,nodes:o,nodeLookup:i,triggerNodeChanges:r,triggerEdgeChanges:s}=m(),a=n||t,d=(e||o).map(e=>{const n=i.get(e.id);return n&&(n.selected=!1),W(e.id,!1)}),l=a.map(e=>W(e.id,!1));r(d),s(l)},setMinZoom:e=>{const{panZoom:n,maxZoom:t}=m();n?.setScaleExtent([e,t]),a({minZoom:e})},setMaxZoom:e=>{const{panZoom:n,minZoom:t}=m();n?.setScaleExtent([t,e]),a({maxZoom:e})},setTranslateExtent:e=>{m().panZoom?.setTranslateExtent(e),a({translateExtent:e})},setPaneClickDistance:e=>{m().panZoom?.setClickDistance(e)},resetSelectedElements:()=>{const{edges:e,nodes:n,triggerNodeChanges:t,triggerEdgeChanges:o,elementsSelectable:i}=m();if(!i)return;const r=n.reduce((e,n)=>n.selected?[...e,W(n.id,!1)]:e,[]),s=e.reduce((e,n)=>n.selected?[...e,W(n.id,!1)]:e,[]);t(r),o(s)},setNodeExtent:e=>{const{nodes:n,nodeLookup:t,parentLookup:o,nodeOrigin:i,elevateNodesOnSelect:r,nodeExtent:d}=m();e[0][0]===d[0][0]&&e[0][1]===d[0][1]&&e[1][0]===d[1][0]&&e[1][1]===d[1][1]||((0,s.bi)(n,t,o,{nodeOrigin:i,nodeExtent:e,elevateNodesOnSelect:r,checkEquality:!1}),a({nodeExtent:e}))},panBy:e=>{const{transform:n,width:t,height:o,panZoom:i,translateExtent:r}=m();return(0,s.No)({delta:e,panZoom:i,transform:n,translateExtent:r,width:t,height:o})},setCenter:async(e,n,t)=>{const{width:o,height:i,maxZoom:r,panZoom:s}=m();if(!s)return Promise.resolve(!1);const a=void 0!==t?.zoom?t.zoom:r;return await s.setViewport({x:o/2-e*a,y:i/2-n*a,zoom:a},{duration:t?.duration,ease:t?.ease,interpolate:t?.interpolate}),Promise.resolve(!0)},cancelConnection:()=>{a({connection:{...s.bK}})},updateConnection:e=>{a({connection:e})},reset:()=>a({...Ln()})}},Object.is);function An({initialNodes:e,initialEdges:n,defaultNodes:t,defaultEdges:r,initialWidth:s,initialHeight:a,initialMinZoom:d,initialMaxZoom:l,initialFitViewOptions:c,fitView:g,nodeOrigin:p,nodeExtent:m,children:h}){const[f]=(0,i.useState)(()=>In({nodes:e,edges:n,defaultNodes:t,defaultEdges:r,width:s,height:a,fitView:g,minZoom:d,maxZoom:l,fitViewOptions:c,nodeOrigin:p,nodeExtent:m}));return(0,o.jsx)(u,{value:f,children:(0,o.jsx)(oe,{children:h})})}function jn({children:e,nodes:n,edges:t,defaultNodes:r,defaultEdges:s,width:a,height:d,fitView:l,fitViewOptions:u,minZoom:g,maxZoom:p,nodeOrigin:m,nodeExtent:h}){return(0,i.useContext)(c)?(0,o.jsx)(o.Fragment,{children:e}):(0,o.jsx)(An,{initialNodes:n,initialEdges:t,defaultNodes:r,defaultEdges:s,initialWidth:a,initialHeight:d,fitView:l,initialFitViewOptions:u,initialMinZoom:g,initialMaxZoom:p,nodeOrigin:m,nodeExtent:h,children:e})}const zn={width:"100%",height:"100%",overflow:"hidden",position:"relative",zIndex:0};var _n=J(function({nodes:e,edges:n,defaultNodes:t,defaultEdges:a,className:d,nodeTypes:l,edgeTypes:c,onNodeClick:u,onEdgeClick:g,onInit:p,onMove:m,onMoveStart:h,onMoveEnd:f,onConnect:b,onConnectStart:S,onConnectEnd:y,onClickConnectStart:C,onClickConnectEnd:x,onNodeMouseEnter:w,onNodeMouseMove:k,onNodeMouseLeave:N,onNodeContextMenu:M,onNodeDoubleClick:P,onNodeDragStart:D,onNodeDrag:I,onNodeDragStop:A,onNodesDelete:j,onEdgesDelete:V,onDelete:B,onSelectionChange:Z,onSelectionDragStart:$,onSelectionDrag:T,onSelectionDragStop:H,onSelectionContextMenu:X,onSelectionStart:F,onSelectionEnd:K,onBeforeDelete:W,connectionMode:Y,connectionLineType:Q=s.Do.Bezier,connectionLineStyle:G,connectionLineComponent:U,connectionLineContainerStyle:q,deleteKeyCode:J="Backspace",selectionKeyCode:ee="Shift",selectionOnDrag:ne=!1,selectionMode:te=s.Qc.Full,panActivationKeyCode:oe="Space",multiSelectionKeyCode:ie=((0,s.Ue)()?"Meta":"Control"),zoomActivationKeyCode:re=((0,s.Ue)()?"Meta":"Control"),snapToGrid:se,snapGrid:ae,onlyRenderVisibleElements:de=!1,selectNodesOnDrag:le,nodesDraggable:ce,autoPanOnNodeFocus:ue,nodesConnectable:ge,nodesFocusable:pe,nodeOrigin:me=R,edgesFocusable:he,edgesReconnectable:fe,elementsSelectable:be=!0,defaultViewport:Se=L,minZoom:ye=.5,maxZoom:Ce=2,translateExtent:xe=s.ZO,preventScrolling:ve=!0,nodeExtent:we,defaultMarkerColor:Ee="#b1b1b7",zoomOnScroll:ke=!0,zoomOnPinch:Ne=!0,panOnScroll:Me=!1,panOnScrollSpeed:Pe=.5,panOnScrollMode:De=s.ny.Free,zoomOnDoubleClick:Oe=!0,panOnDrag:Re=!0,onPaneClick:Le,onPaneMouseEnter:Ie,onPaneMouseMove:Ae,onPaneMouseLeave:je,onPaneScroll:ze,onPaneContextMenu:_e,paneClickDistance:Ve=0,nodeClickDistance:Be=0,children:Ze,onReconnect:$e,onReconnectStart:Te,onReconnectEnd:He,onEdgeContextMenu:Xe,onEdgeDoubleClick:Fe,onEdgeMouseEnter:Ke,onEdgeMouseMove:We,onEdgeMouseLeave:Ye,reconnectRadius:Qe=10,onNodesChange:Ge,onEdgesChange:Ue,noDragClassName:qe="nodrag",noWheelClassName:Je="nowheel",noPanClassName:en="nopan",fitView:nn,fitViewOptions:tn,connectOnClick:on,attributionPosition:rn,proOptions:sn,defaultEdgeOptions:an,elevateNodesOnSelect:dn,elevateEdgesOnSelect:ln,disableKeyboardA11y:cn=!1,autoPanOnConnect:un,autoPanOnNodeDrag:gn,autoPanSpeed:pn,connectionRadius:mn,isValidConnection:hn,onError:fn,style:bn,id:Sn,nodeDragThreshold:yn,connectionDragThreshold:Cn,viewport:xn,onViewportChange:vn,width:wn,height:En,colorMode:kn="light",debug:Nn,onScroll:Mn,ariaLabelConfig:Pn,...Dn},On){const Ln=Sn||"1",In=function(e){const[n,t]=(0,i.useState)("system"===e?null:e);return(0,i.useEffect)(()=>{if("system"!==e)return void t(e);const n=_(),o=()=>t(n?.matches?"dark":"light");return o(),n?.addEventListener("change",o),()=>{n?.removeEventListener("change",o)}},[e]),null!==n?n:_()?.matches?"dark":"light"}(kn),An=(0,i.useCallback)(e=>{e.currentTarget.scrollTo({top:0,left:0,behavior:"instant"}),Mn?.(e)},[Mn]);return(0,o.jsx)("div",{"data-testid":"rf__wrapper",...Dn,onScroll:An,style:{...bn,...zn},ref:On,className:(0,r.A)(["react-flow",d,In]),id:Sn,role:"application",children:(0,o.jsxs)(jn,{nodes:e,edges:n,width:wn,height:En,fitView:nn,fitViewOptions:tn,minZoom:ye,maxZoom:Ce,nodeOrigin:me,nodeExtent:we,children:[(0,o.jsx)(Rn,{onInit:p,onNodeClick:u,onEdgeClick:g,onNodeMouseEnter:w,onNodeMouseMove:k,onNodeMouseLeave:N,onNodeContextMenu:M,onNodeDoubleClick:P,nodeTypes:l,edgeTypes:c,connectionLineType:Q,connectionLineStyle:G,connectionLineComponent:U,connectionLineContainerStyle:q,selectionKeyCode:ee,selectionOnDrag:ne,selectionMode:te,deleteKeyCode:J,multiSelectionKeyCode:ie,panActivationKeyCode:oe,zoomActivationKeyCode:re,onlyRenderVisibleElements:de,defaultViewport:Se,translateExtent:xe,minZoom:ye,maxZoom:Ce,preventScrolling:ve,zoomOnScroll:ke,zoomOnPinch:Ne,zoomOnDoubleClick:Oe,panOnScroll:Me,panOnScrollSpeed:Pe,panOnScrollMode:De,panOnDrag:Re,onPaneClick:Le,onPaneMouseEnter:Ie,onPaneMouseMove:Ae,onPaneMouseLeave:je,onPaneScroll:ze,onPaneContextMenu:_e,paneClickDistance:Ve,nodeClickDistance:Be,onSelectionContextMenu:X,onSelectionStart:F,onSelectionEnd:K,onReconnect:$e,onReconnectStart:Te,onReconnectEnd:He,onEdgeContextMenu:Xe,onEdgeDoubleClick:Fe,onEdgeMouseEnter:Ke,onEdgeMouseMove:We,onEdgeMouseLeave:Ye,reconnectRadius:Qe,defaultMarkerColor:Ee,noDragClassName:qe,noWheelClassName:Je,noPanClassName:en,rfId:Ln,disableKeyboardA11y:cn,nodeExtent:we,viewport:xn,onViewportChange:vn}),(0,o.jsx)(z,{nodes:e,edges:n,defaultNodes:t,defaultEdges:a,onConnect:b,onConnectStart:S,onConnectEnd:y,onClickConnectStart:C,onClickConnectEnd:x,nodesDraggable:ce,autoPanOnNodeFocus:ue,nodesConnectable:ge,nodesFocusable:pe,edgesFocusable:he,edgesReconnectable:fe,elementsSelectable:be,elevateNodesOnSelect:dn,elevateEdgesOnSelect:ln,minZoom:ye,maxZoom:Ce,nodeExtent:we,onNodesChange:Ge,onEdgesChange:Ue,snapToGrid:se,snapGrid:ae,connectionMode:Y,translateExtent:xe,connectOnClick:on,defaultEdgeOptions:an,fitView:nn,fitViewOptions:tn,onNodesDelete:j,onEdgesDelete:V,onDelete:B,onNodeDragStart:D,onNodeDrag:I,onNodeDragStop:A,onSelectionDrag:T,onSelectionDragStart:$,onSelectionDragStop:H,onMove:m,onMoveStart:h,onMoveEnd:f,noPanClassName:en,nodeOrigin:me,rfId:Ln,autoPanOnConnect:un,autoPanOnNodeDrag:gn,autoPanSpeed:pn,onError:fn,connectionRadius:mn,isValidConnection:hn,selectNodesOnDrag:le,nodeDragThreshold:yn,connectionDragThreshold:Cn,onBeforeDelete:W,paneClickDistance:Ve,debug:Nn,ariaLabelConfig:Pn}),(0,o.jsx)(O,{onSelectionChange:Z}),Ze,(0,o.jsx)(E,{proOptions:sn,position:rn}),(0,o.jsx)(v,{rfId:Ln,disableKeyboardA11y:cn})]})})});const Vn=e=>e.domNode?.querySelector(".react-flow__edgelabel-renderer");function Bn({children:e}){const n=p(Vn);return n?(0,l.createPortal)(e,n):null}function Zn(e){const[n,t]=(0,i.useState)(e),o=(0,i.useCallback)(e=>t(n=>F(e,n)),[]);return[n,t,o]}function $n(e){const[n,t]=(0,i.useState)(e),o=(0,i.useCallback)(e=>t(n=>K(e,n)),[]);return[n,t,o]}s.xc.error014();function Tn({dimensions:e,lineWidth:n,variant:t,className:i}){return(0,o.jsx)("path",{strokeWidth:n,d:`M${e[0]/2} 0 V${e[1]} M0 ${e[1]/2} H${e[0]}`,className:(0,r.A)(["react-flow__background-pattern",t,i])})}function Hn({radius:e,className:n}){return(0,o.jsx)("circle",{cx:e,cy:e,r:e,className:(0,r.A)(["react-flow__background-pattern","dots",n])})}var Xn;!function(e){e.Lines="lines",e.Dots="dots",e.Cross="cross"}(Xn||(Xn={}));const Fn={[Xn.Dots]:1,[Xn.Lines]:1,[Xn.Cross]:6},Kn=e=>({transform:e.transform,patternId:`pattern-${e.rfId}`});function Wn({id:e,variant:n=Xn.Dots,gap:t=20,size:s,lineWidth:a=1,offset:l=0,color:c,bgColor:u,style:g,className:m,patternClassName:h}){const f=(0,i.useRef)(null),{transform:b,patternId:S}=p(Kn,d.x),y=s||Fn[n],C=n===Xn.Dots,x=n===Xn.Cross,v=Array.isArray(t)?t:[t,t],w=[v[0]*b[2]||1,v[1]*b[2]||1],E=y*b[2],k=Array.isArray(l)?l:[l,l],N=x?[E,E]:w,M=[k[0]*b[2]||1+N[0]/2,k[1]*b[2]||1+N[1]/2],P=`${S}${e||""}`;return(0,o.jsxs)("svg",{className:(0,r.A)(["react-flow__background",m]),style:{...g,...de,"--xy-background-color-props":u,"--xy-background-pattern-color-props":c},ref:f,"data-testid":"rf__background",children:[(0,o.jsx)("pattern",{id:P,x:b[0]%w[0],y:b[1]%w[1],width:w[0],height:w[1],patternUnits:"userSpaceOnUse",patternTransform:`translate(-${M[0]},-${M[1]})`,children:C?(0,o.jsx)(Hn,{radius:E/2,className:h}):(0,o.jsx)(Tn,{dimensions:N,lineWidth:a,variant:n,className:h})}),(0,o.jsx)("rect",{x:"0",y:"0",width:"100%",height:"100%",fill:`url(#${P})`})]})}Wn.displayName="Background";const Yn=(0,i.memo)(Wn);function Qn(){return(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32",children:(0,o.jsx)("path",{d:"M32 18.133H18.133V32h-4.266V18.133H0v-4.266h13.867V0h4.266v13.867H32z"})})}function Gn(){return(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 5",children:(0,o.jsx)("path",{d:"M0 0h32v4.2H0z"})})}function Un(){return(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 30",children:(0,o.jsx)("path",{d:"M3.692 4.63c0-.53.4-.938.939-.938h5.215V0H4.708C2.13 0 0 2.054 0 4.63v5.216h3.692V4.631zM27.354 0h-5.2v3.692h5.17c.53 0 .984.4.984.939v5.215H32V4.631A4.624 4.624 0 0027.354 0zm.954 24.83c0 .532-.4.94-.939.94h-5.215v3.768h5.215c2.577 0 4.631-2.13 4.631-4.707v-5.139h-3.692v5.139zm-23.677.94c-.531 0-.939-.4-.939-.94v-5.138H0v5.139c0 2.577 2.13 4.707 4.708 4.707h5.138V25.77H4.631z"})})}function qn(){return(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32",children:(0,o.jsx)("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0 8 0 4.571 3.429 4.571 7.619v3.048H3.048A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047zm4.724-13.866H7.467V7.619c0-2.59 2.133-4.724 4.723-4.724 2.591 0 4.724 2.133 4.724 4.724v3.048z"})})}function Jn(){return(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32",children:(0,o.jsx)("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0c-4.114 1.828-1.37 2.133.305 2.438 1.676.305 4.42 2.59 4.42 5.181v3.048H3.047A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047z"})})}function et({children:e,className:n,...t}){return(0,o.jsx)("button",{type:"button",className:(0,r.A)(["react-flow__controls-button",n]),...t,children:e})}const nt=e=>({isInteractive:e.nodesDraggable||e.nodesConnectable||e.elementsSelectable,minZoomReached:e.transform[2]<=e.minZoom,maxZoomReached:e.transform[2]>=e.maxZoom,ariaLabelConfig:e.ariaLabelConfig});function tt({style:e,showZoom:n=!0,showFitView:t=!0,showInteractive:i=!0,fitViewOptions:s,onZoomIn:a,onZoomOut:l,onFitView:c,onInteractiveChange:u,className:g,children:h,position:f="bottom-left",orientation:b="vertical","aria-label":S}){const y=m(),{isInteractive:C,minZoomReached:x,maxZoomReached:v,ariaLabelConfig:E}=p(nt,d.x),{zoomIn:k,zoomOut:N,fitView:M}=re(),P="horizontal"===b?"horizontal":"vertical";return(0,o.jsxs)(w,{className:(0,r.A)(["react-flow__controls",P,g]),position:f,style:e,"data-testid":"rf__controls","aria-label":S??E["controls.ariaLabel"],children:[n&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(et,{onClick:()=>{k(),a?.()},className:"react-flow__controls-zoomin",title:E["controls.zoomIn.ariaLabel"],"aria-label":E["controls.zoomIn.ariaLabel"],disabled:v,children:(0,o.jsx)(Qn,{})}),(0,o.jsx)(et,{onClick:()=>{N(),l?.()},className:"react-flow__controls-zoomout",title:E["controls.zoomOut.ariaLabel"],"aria-label":E["controls.zoomOut.ariaLabel"],disabled:x,children:(0,o.jsx)(Gn,{})})]}),t&&(0,o.jsx)(et,{className:"react-flow__controls-fitview",onClick:()=>{M(s),c?.()},title:E["controls.fitView.ariaLabel"],"aria-label":E["controls.fitView.ariaLabel"],children:(0,o.jsx)(Un,{})}),i&&(0,o.jsx)(et,{className:"react-flow__controls-interactive",onClick:()=>{y.setState({nodesDraggable:!C,nodesConnectable:!C,elementsSelectable:!C}),u?.(!C)},title:E["controls.interactive.ariaLabel"],"aria-label":E["controls.interactive.ariaLabel"],children:C?(0,o.jsx)(Jn,{}):(0,o.jsx)(qn,{})}),h]})}tt.displayName="Controls";(0,i.memo)(tt);const ot=(0,i.memo)(function({id:e,x:n,y:t,width:i,height:s,style:a,color:d,strokeColor:l,strokeWidth:c,className:u,borderRadius:g,shapeRendering:p,selected:m,onClick:h}){const{background:f,backgroundColor:b}=a||{},S=d||f||b;return(0,o.jsx)("rect",{className:(0,r.A)(["react-flow__minimap-node",{selected:m},u]),x:n,y:t,rx:g,ry:g,width:i,height:s,style:{fill:S,stroke:l,strokeWidth:c},shapeRendering:p,onClick:h?n=>h(n,e):void 0})}),it=e=>e.nodes.map(e=>e.id),rt=e=>e instanceof Function?e:()=>e;const st=(0,i.memo)(function({id:e,nodeColorFunc:n,nodeStrokeColorFunc:t,nodeClassNameFunc:i,nodeBorderRadius:r,nodeStrokeWidth:a,shapeRendering:l,NodeComponent:c,onClick:u}){const{node:g,x:m,y:h,width:f,height:b}=p(n=>{const{internals:t}=n.nodeLookup.get(e),o=t.userNode,{x:i,y:r}=t.positionAbsolute,{width:a,height:d}=(0,s.uD)(o);return{node:o,x:i,y:r,width:a,height:d}},d.x);return g&&!g.hidden&&(0,s.QE)(g)?(0,o.jsx)(c,{x:m,y:h,width:f,height:b,style:g.style,selected:!!g.selected,className:i(g),color:n(g),borderRadius:r,strokeColor:t(g),strokeWidth:a,shapeRendering:l,onClick:u,id:g.id}):null});var at=(0,i.memo)(function({nodeStrokeColor:e,nodeColor:n,nodeClassName:t="",nodeBorderRadius:i=5,nodeStrokeWidth:r,nodeComponent:s=ot,onClick:a}){const l=p(it,d.x),c=rt(n),u=rt(e),g=rt(t),m="undefined"==typeof window||window.chrome?"crispEdges":"geometricPrecision";return(0,o.jsx)(o.Fragment,{children:l.map(e=>(0,o.jsx)(st,{id:e,nodeColorFunc:c,nodeStrokeColorFunc:u,nodeClassNameFunc:g,nodeBorderRadius:i,nodeStrokeWidth:r,NodeComponent:s,onClick:a,shapeRendering:m},e))})});const dt=e=>!e.hidden,lt=e=>{const n={x:-e.transform[0]/e.transform[2],y:-e.transform[1]/e.transform[2],width:e.width/e.transform[2],height:e.height/e.transform[2]};return{viewBB:n,boundingRect:e.nodeLookup.size>0?(0,s.Mi)((0,s.aZ)(e.nodeLookup,{filter:dt}),n):n,rfId:e.rfId,panZoom:e.panZoom,translateExtent:e.translateExtent,flowWidth:e.width,flowHeight:e.height,ariaLabelConfig:e.ariaLabelConfig}};function ct({style:e,className:n,nodeStrokeColor:t,nodeColor:a,nodeClassName:l="",nodeBorderRadius:c=5,nodeStrokeWidth:u,nodeComponent:g,bgColor:h,maskColor:f,maskStrokeColor:b,maskStrokeWidth:S,position:y="bottom-right",onClick:C,onNodeClick:x,pannable:v=!1,zoomable:E=!1,ariaLabel:k,inversePan:N,zoomStep:M=10,offsetScale:P=5}){const D=m(),O=(0,i.useRef)(null),{boundingRect:R,viewBB:L,rfId:I,panZoom:A,translateExtent:j,flowWidth:z,flowHeight:_,ariaLabelConfig:V}=p(lt,d.x),B=e?.width??200,Z=e?.height??150,$=R.width/B,T=R.height/Z,H=Math.max($,T),X=H*B,F=H*Z,K=P*H,W=R.x-(X-R.width)/2-K,Y=R.y-(F-R.height)/2-K,Q=X+2*K,G=F+2*K,U=`react-flow__minimap-desc-${I}`,q=(0,i.useRef)(0),J=(0,i.useRef)();q.current=H,(0,i.useEffect)(()=>{if(O.current&&A)return J.current=(0,s.di)({domNode:O.current,panZoom:A,getTransform:()=>D.getState().transform,getViewScale:()=>q.current}),()=>{J.current?.destroy()}},[A]),(0,i.useEffect)(()=>{J.current?.update({translateExtent:j,width:z,height:_,inversePan:N,pannable:v,zoomStep:M,zoomable:E})},[v,E,N,M,j,z,_]);const ee=C?e=>{const[n,t]=J.current?.pointer(e)||[0,0];C(e,{x:n,y:t})}:void 0,ne=x?(0,i.useCallback)((e,n)=>{const t=D.getState().nodeLookup.get(n).internals.userNode;x(e,t)},[]):void 0,te=k??V["minimap.ariaLabel"];return(0,o.jsx)(w,{position:y,style:{...e,"--xy-minimap-background-color-props":"string"==typeof h?h:void 0,"--xy-minimap-mask-background-color-props":"string"==typeof f?f:void 0,"--xy-minimap-mask-stroke-color-props":"string"==typeof b?b:void 0,"--xy-minimap-mask-stroke-width-props":"number"==typeof S?S*H:void 0,"--xy-minimap-node-background-color-props":"string"==typeof a?a:void 0,"--xy-minimap-node-stroke-color-props":"string"==typeof t?t:void 0,"--xy-minimap-node-stroke-width-props":"number"==typeof u?u:void 0},className:(0,r.A)(["react-flow__minimap",n]),"data-testid":"rf__minimap",children:(0,o.jsxs)("svg",{width:B,height:Z,viewBox:`${W} ${Y} ${Q} ${G}`,className:"react-flow__minimap-svg",role:"img","aria-labelledby":U,ref:O,onClick:ee,children:[te&&(0,o.jsx)("title",{id:U,children:te}),(0,o.jsx)(at,{onClick:ne,nodeColor:a,nodeStrokeColor:t,nodeBorderRadius:c,nodeClassName:l,nodeStrokeWidth:u,nodeComponent:g}),(0,o.jsx)("path",{className:"react-flow__minimap-mask",d:`M${W-K},${Y-K}h${Q+2*K}v${G+2*K}h${-Q-2*K}z\n        M${L.x},${L.y}h${L.width}v${L.height}h${-L.width}z`,fillRule:"evenodd",pointerEvents:"none"})]})})}ct.displayName="MiniMap";const ut=(0,i.memo)(ct),gt={[s.xN.Line]:"right",[s.xN.Handle]:"bottom-right"};(0,i.memo)(function({nodeId:e,position:n,variant:t=s.xN.Handle,className:a,style:l,children:c,color:u,minWidth:g=10,minHeight:h=10,maxWidth:f=Number.MAX_VALUE,maxHeight:b=Number.MAX_VALUE,keepAspectRatio:S=!1,resizeDirection:y,autoScale:C=!0,shouldResize:x,onResizeStart:v,onResize:w,onResizeEnd:E}){const k=xe(),N="string"==typeof e?e:k,M=m(),P=(0,i.useRef)(null),D=t===s.xN.Handle,O=p((0,i.useCallback)((R=D&&C,e=>R?`${Math.max(1/e.transform[2],1)}`:void 0),[D,C]),d.x);var R;const L=(0,i.useRef)(null),I=n??gt[t];(0,i.useEffect)(()=>{if(P.current&&N)return L.current||(L.current=(0,s.ET)({domNode:P.current,nodeId:N,getStoreItems:()=>{const{nodeLookup:e,transform:n,snapGrid:t,snapToGrid:o,nodeOrigin:i,domNode:r}=M.getState();return{nodeLookup:e,transform:n,snapGrid:t,snapToGrid:o,nodeOrigin:i,paneDomNode:r}},onChange:(e,n)=>{const{triggerNodeChanges:t,nodeLookup:o,parentLookup:i,nodeOrigin:r}=M.getState(),a=[],d={x:e.x,y:e.y},l=o.get(N);if(l&&l.expandParent&&l.parentId){const n=l.origin??r,t=e.width??l.measured.width??0,c=e.height??l.measured.height??0,u={id:l.id,parentId:l.parentId,rect:{width:t,height:c,...(0,s.us)({x:e.x??l.position.x,y:e.y??l.position.y},{width:t,height:c},l.parentId,o,n)}},g=(0,s.r8)([u],o,i,r);a.push(...g),d.x=e.x?Math.max(n[0]*t,e.x):void 0,d.y=e.y?Math.max(n[1]*c,e.y):void 0}if(void 0!==d.x&&void 0!==d.y){const e={id:N,type:"position",position:{...d}};a.push(e)}if(void 0!==e.width&&void 0!==e.height){const n={id:N,type:"dimensions",resizing:!0,setAttributes:!y||("horizontal"===y?"width":"height"),dimensions:{width:e.width,height:e.height}};a.push(n)}for(const s of n){const e={...s,type:"position"};a.push(e)}t(a)},onEnd:({width:e,height:n})=>{const t={id:N,type:"dimensions",resizing:!1,dimensions:{width:e,height:n}};M.getState().triggerNodeChanges([t])}})),L.current.update({controlPosition:I,boundaries:{minWidth:g,minHeight:h,maxWidth:f,maxHeight:b},keepAspectRatio:S,resizeDirection:y,onResizeStart:v,onResize:w,onResizeEnd:E,shouldResize:x}),()=>{L.current?.destroy()}},[I,g,h,f,b,S,v,w,E,x]);const A=I.split("-");return(0,o.jsx)("div",{className:(0,r.A)(["react-flow__resize-control","nodrag",...A,t,a]),ref:P,style:{...l,scale:O,...u&&{[D?"backgroundColor":"borderColor"]:u}},children:c})})}}]);
//# sourceMappingURL=e8c4b161-c0e5cbba1b1435f15253.js.map