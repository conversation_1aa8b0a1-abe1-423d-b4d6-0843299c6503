"use strict";(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[377],{57:function(o,e,t){t.d(e,{A:function(){return k}});var r=t(6540),n=t(6942),l=t.n(n),i=t(2467),a=t(8719),c=t(2279),s=t(682),d=t(7358);const u=o=>{const{componentCls:e,colorPrimary:t}=o;return{[e]:{position:"absolute",background:"transparent",pointerEvents:"none",boxSizing:"border-box",color:`var(--wave-color, ${t})`,boxShadow:"0 0 0 0 currentcolor",opacity:.2,"&.wave-motion-appear":{transition:[`box-shadow 0.4s ${o.motionEaseOutCirc}`,`opacity 2s ${o.motionEaseOutCirc}`].join(","),"&-active":{boxShadow:"0 0 0 6px currentcolor",opacity:0},"&.wave-quick":{transition:[`box-shadow ${o.motionDurationSlow} ${o.motionEaseInOut}`,`opacity ${o.motionDurationSlow} ${o.motionEaseInOut}`].join(",")}}}}};var g=(0,d.Or)("Wave",o=>[u(o)]),b=t(6956),m=t(5371),f=t(1320),p=t(4424),v=t(754),h=t(4642);function C(o){return o&&"#fff"!==o&&"#ffffff"!==o&&"rgb(255, 255, 255)"!==o&&"rgba(255, 255, 255, 1)"!==o&&!/rgba\((?:\d*, ){3}0\)/.test(o)&&"transparent"!==o}function y(o){return Number.isNaN(o)?0:o}const S=o=>{const{className:e,target:t,component:n,registerUnmount:i}=o,c=r.useRef(null),s=r.useRef(null);r.useEffect(()=>{s.current=i()},[]);const[d,u]=r.useState(null),[g,b]=r.useState([]),[f,h]=r.useState(0),[S,$]=r.useState(0),[x,k]=r.useState(0),[O,E]=r.useState(0),[A,H]=r.useState(!1),j={left:f,top:S,width:x,height:O,borderRadius:g.map(o=>`${o}px`).join(" ")};function B(){const o=getComputedStyle(t);u(function(o){const{borderTopColor:e,borderColor:t,backgroundColor:r}=getComputedStyle(o);return C(e)?e:C(t)?t:C(r)?r:null}(t));const e="static"===o.position,{borderLeftWidth:r,borderTopWidth:n}=o;h(e?t.offsetLeft:y(-parseFloat(r))),$(e?t.offsetTop:y(-parseFloat(n))),k(t.offsetWidth),E(t.offsetHeight);const{borderTopLeftRadius:l,borderTopRightRadius:i,borderBottomLeftRadius:a,borderBottomRightRadius:c}=o;b([l,i,c,a].map(o=>y(parseFloat(o))))}if(d&&(j["--wave-color"]=d),r.useEffect(()=>{if(t){const o=(0,m.A)(()=>{B(),H(!0)});let e;return"undefined"!=typeof ResizeObserver&&(e=new ResizeObserver(B),e.observe(t)),()=>{m.A.cancel(o),null==e||e.disconnect()}}},[]),!A)return null;const w=("Checkbox"===n||"Radio"===n)&&(null==t?void 0:t.classList.contains(p.D));return r.createElement(v.Ay,{visible:!0,motionAppear:!0,motionName:"wave-motion",motionDeadline:5e3,onAppearEnd:(o,e)=>{var t,r;if(e.deadline||"opacity"===e.propertyName){const o=null===(t=c.current)||void 0===t?void 0:t.parentElement;null===(r=s.current)||void 0===r||r.call(s).then(()=>{null==o||o.remove()})}return!1}},({className:o},t)=>r.createElement("div",{ref:(0,a.K4)(c,t),className:l()(e,o,{"wave-quick":w}),style:j}))};var $=(o,e)=>{var t;const{component:n}=e;if("Checkbox"===n&&!(null===(t=o.querySelector("input"))||void 0===t?void 0:t.checked))return;const l=document.createElement("div");l.style.position="absolute",l.style.left="0px",l.style.top="0px",null==o||o.insertBefore(l,null==o?void 0:o.firstChild);const i=(0,h.L)();let a=null;a=i(r.createElement(S,Object.assign({},e,{target:o,registerUnmount:function(){return a}})),l)};var x=(o,e,t)=>{const{wave:n}=r.useContext(c.QO),[,l,i]=(0,f.Ay)(),a=(0,b.A)(r=>{const a=o.current;if((null==n?void 0:n.disabled)||!a)return;const c=a.querySelector(`.${p.D}`)||a,{showEffect:s}=n||{};(s||$)(c,{className:e,token:l,component:t,event:r,hashId:i})}),s=r.useRef(null);return o=>{m.A.cancel(s.current),s.current=(0,m.A)(()=>{a(o)})}};var k=o=>{const{children:e,disabled:t,component:n}=o,{getPrefixCls:d}=(0,r.useContext)(c.QO),u=(0,r.useRef)(null),b=d("wave"),[,m]=g(b),f=x(u,l()(b,m),n);if(r.useEffect(()=>{const o=u.current;if(!o||1!==o.nodeType||t)return;const e=e=>{!(0,i.A)(e.target)||!o.getAttribute||o.getAttribute("disabled")||o.disabled||o.className.includes("disabled")||o.className.includes("-leave")||f(e)};return o.addEventListener("click",e,!0),()=>{o.removeEventListener("click",e,!0)}},[t]),!r.isValidElement(e))return null!=e?e:null;const p=(0,a.f3)(e)?(0,a.K4)((0,a.A9)(e),u):u;return(0,s.Ob)(e,{ref:p})}},2941:function(o,e,t){t.d(e,{Ay:function(){return zo}});var r=t(6540),n=t(6942),l=t.n(n),i=t(9853),a=t(8719),c=t(57),s=t(2279),d=t(8119),u=t(829),g=t(6327),b=t(1320),m=function(o,e){var t={};for(var r in o)Object.prototype.hasOwnProperty.call(o,r)&&e.indexOf(r)<0&&(t[r]=o[r]);if(null!=o&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(r=Object.getOwnPropertySymbols(o);n<r.length;n++)e.indexOf(r[n])<0&&Object.prototype.propertyIsEnumerable.call(o,r[n])&&(t[r[n]]=o[r[n]])}return t};const f=r.createContext(void 0);var p=o=>{const{getPrefixCls:e,direction:t}=r.useContext(s.QO),{prefixCls:n,size:i,className:a}=o,c=m(o,["prefixCls","size","className"]),d=e("btn-group",n),[,,u]=(0,b.Ay)(),g=r.useMemo(()=>{switch(i){case"large":return"lg";case"small":return"sm";default:return""}},[i]);const p=l()(d,{[`${d}-${g}`]:g,[`${d}-rtl`]:"rtl"===t},a,u);return r.createElement(f.Provider,{value:i},r.createElement("div",Object.assign({},c,{className:p})))},v=t(9449),h=t(3567),C=t(754);const y=(0,r.forwardRef)((o,e)=>{const{className:t,style:n,children:i,prefixCls:a}=o,c=l()(`${a}-icon`,t);return r.createElement("span",{ref:e,className:c,style:n},i)});var S=y;const $=(0,r.forwardRef)((o,e)=>{const{prefixCls:t,className:n,style:i,iconClassName:a}=o,c=l()(`${t}-loading-icon`,n);return r.createElement(S,{prefixCls:t,className:c,style:i,ref:e},r.createElement(h.A,{className:a}))}),x=()=>({width:0,opacity:0,transform:"scale(0)"}),k=o=>({width:o.scrollWidth,opacity:1,transform:"scale(1)"});var O=o=>{const{prefixCls:e,loading:t,existIcon:n,className:i,style:a,mount:c}=o,s=!!t;return n?r.createElement($,{prefixCls:e,className:i,style:a}):r.createElement(C.Ay,{visible:s,motionName:`${e}-loading-icon-motion`,motionAppear:!c,motionEnter:!c,motionLeave:!c,removeOnLeave:!0,onAppearStart:x,onAppearActive:k,onEnterStart:x,onEnterActive:k,onLeaveStart:k,onLeaveActive:x},({className:o,style:t},n)=>{const c=Object.assign(Object.assign({},a),t);return r.createElement($,{prefixCls:e,className:l()(i,o),style:c,ref:n})})},E=t(2187),A=t(5905),H=t(3950),j=t(4277),B=t(7358);const w=(o,e)=>({[`> span, > ${o}`]:{"&:not(:last-child)":{[`&, & > ${o}`]:{"&:not(:disabled)":{borderInlineEndColor:e}}},"&:not(:first-child)":{[`&, & > ${o}`]:{"&:not(:disabled)":{borderInlineStartColor:e}}}}});var I=o=>{const{componentCls:e,fontSize:t,lineWidth:r,groupBorderColor:n,colorErrorHover:l}=o;return{[`${e}-group`]:[{position:"relative",display:"inline-flex",[`> span, > ${e}`]:{"&:not(:last-child)":{[`&, & > ${e}`]:{borderStartEndRadius:0,borderEndEndRadius:0}},"&:not(:first-child)":{marginInlineStart:o.calc(r).mul(-1).equal(),[`&, & > ${e}`]:{borderStartStartRadius:0,borderEndStartRadius:0}}},[e]:{position:"relative",zIndex:1,"&:hover, &:focus, &:active":{zIndex:2},"&[disabled]":{zIndex:0}},[`${e}-icon-only`]:{fontSize:t}},w(`${e}-primary`,n),w(`${e}-danger`,l)]}},z=t(3029),P=t(2901),T=t(5501),L=t(9426),R=t(9379),N=t(3986),F=t(2284),G=t(2616),W=["b"],M=["v"],D=function(o){return Math.round(Number(o||0))},q=function(o){(0,T.A)(t,o);var e=(0,L.A)(t);function t(o){return(0,z.A)(this,t),e.call(this,function(o){if(o instanceof G.Y)return o;if(o&&"object"===(0,F.A)(o)&&"h"in o&&"b"in o){var e=o,t=e.b,r=(0,N.A)(e,W);return(0,R.A)((0,R.A)({},r),{},{v:t})}return"string"==typeof o&&/hsb/.test(o)?o.replace(/hsb/,"hsv"):o}(o))}return(0,P.A)(t,[{key:"toHsbString",value:function(){var o=this.toHsb(),e=D(100*o.s),t=D(100*o.b),r=D(o.h),n=o.a,l="hsb(".concat(r,", ").concat(e,"%, ").concat(t,"%)"),i="hsba(".concat(r,", ").concat(e,"%, ").concat(t,"%, ").concat(n.toFixed(0===n?0:2),")");return 1===n?l:i}},{key:"toHsb",value:function(){var o=this.toHsv(),e=o.v,t=(0,N.A)(o,M);return(0,R.A)((0,R.A)({},t),{},{b:e,a:this.a})}}]),t}(G.Y),X=function(o){return o instanceof q?o:new q(o)};X("#1677ff");t(1470);let Q=function(){return(0,P.A)(function o(e){var t;if((0,z.A)(this,o),this.cleared=!1,e instanceof o)return this.metaColor=e.metaColor.clone(),this.colors=null===(t=e.colors)||void 0===t?void 0:t.map(e=>({color:new o(e.color),percent:e.percent})),void(this.cleared=e.cleared);const r=Array.isArray(e);r&&e.length?(this.colors=e.map(({color:e,percent:t})=>({color:new o(e),percent:t})),this.metaColor=new q(this.colors[0].color.metaColor)):this.metaColor=new q(r?"":e),(!e||r&&!this.colors)&&(this.metaColor=this.metaColor.setA(0),this.cleared=!0)},[{key:"toHsb",value:function(){return this.metaColor.toHsb()}},{key:"toHsbString",value:function(){return this.metaColor.toHsbString()}},{key:"toHex",value:function(){return o=this.toHexString(),e=this.metaColor.a<1,o?((o,e)=>(null==o?void 0:o.replace(/[^\w/]/g,"").slice(0,e?8:6))||"")(o,e):"";var o,e}},{key:"toHexString",value:function(){return this.metaColor.toHexString()}},{key:"toRgb",value:function(){return this.metaColor.toRgb()}},{key:"toRgbString",value:function(){return this.metaColor.toRgbString()}},{key:"isGradient",value:function(){return!!this.colors&&!this.cleared}},{key:"getColors",value:function(){return this.colors||[{color:this,percent:0}]}},{key:"toCssString",value:function(){const{colors:o}=this;if(o){return`linear-gradient(90deg, ${o.map(o=>`${o.color.toRgbString()} ${o.percent}%`).join(", ")})`}return this.metaColor.toRgbString()}},{key:"equals",value:function(o){return!(!o||this.isGradient()!==o.isGradient())&&(this.isGradient()?this.colors.length===o.colors.length&&this.colors.every((e,t)=>{const r=o.colors[t];return e.percent===r.percent&&e.color.equals(r.color)}):this.toHexString()===o.toHexString())}}])}();t(2533);const V=(o,e)=>{const{r:t,g:r,b:n,a:l}=o.toRgb(),i=new q(o.toRgbString()).onBackground(e).toHsv();return l<=.5?i.v>.5:.299*t+.587*r+.114*n>192};var K=t(4925),U=t(5045);const _=o=>{const{paddingInline:e,onlyIconSize:t}=o;return(0,j.oX)(o,{buttonPaddingHorizontal:e,buttonPaddingVertical:0,buttonIconOnlyFontSize:t})},Y=o=>{var e,t,r,n,l,i;const a=null!==(e=o.contentFontSize)&&void 0!==e?e:o.fontSize,c=null!==(t=o.contentFontSizeSM)&&void 0!==t?t:o.fontSize,s=null!==(r=o.contentFontSizeLG)&&void 0!==r?r:o.fontSizeLG,d=null!==(n=o.contentLineHeight)&&void 0!==n?n:(0,K.k)(a),u=null!==(l=o.contentLineHeightSM)&&void 0!==l?l:(0,K.k)(c),g=null!==(i=o.contentLineHeightLG)&&void 0!==i?i:(0,K.k)(s),b=V(new Q(o.colorBgSolid),"#fff")?"#000":"#fff",m=H.s.reduce((e,t)=>Object.assign(Object.assign({},e),{[`${t}ShadowColor`]:`0 ${(0,E.zA)(o.controlOutlineWidth)} 0 ${(0,U.A)(o[`${t}1`],o.colorBgContainer)}`}),{});return Object.assign(Object.assign({},m),{fontWeight:400,defaultShadow:`0 ${o.controlOutlineWidth}px 0 ${o.controlTmpOutline}`,primaryShadow:`0 ${o.controlOutlineWidth}px 0 ${o.controlOutline}`,dangerShadow:`0 ${o.controlOutlineWidth}px 0 ${o.colorErrorOutline}`,primaryColor:o.colorTextLightSolid,dangerColor:o.colorTextLightSolid,borderColorDisabled:o.colorBorder,defaultGhostColor:o.colorBgContainer,ghostBg:"transparent",defaultGhostBorderColor:o.colorBgContainer,paddingInline:o.paddingContentHorizontal-o.lineWidth,paddingInlineLG:o.paddingContentHorizontal-o.lineWidth,paddingInlineSM:8-o.lineWidth,onlyIconSize:"inherit",onlyIconSizeSM:"inherit",onlyIconSizeLG:"inherit",groupBorderColor:o.colorPrimaryHover,linkHoverBg:"transparent",textTextColor:o.colorText,textTextHoverColor:o.colorText,textTextActiveColor:o.colorText,textHoverBg:o.colorFillTertiary,defaultColor:o.colorText,defaultBg:o.colorBgContainer,defaultBorderColor:o.colorBorder,defaultBorderColorDisabled:o.colorBorder,defaultHoverBg:o.colorBgContainer,defaultHoverColor:o.colorPrimaryHover,defaultHoverBorderColor:o.colorPrimaryHover,defaultActiveBg:o.colorBgContainer,defaultActiveColor:o.colorPrimaryActive,defaultActiveBorderColor:o.colorPrimaryActive,solidTextColor:b,contentFontSize:a,contentFontSizeSM:c,contentFontSizeLG:s,contentLineHeight:d,contentLineHeightSM:u,contentLineHeightLG:g,paddingBlock:Math.max((o.controlHeight-a*d)/2-o.lineWidth,0),paddingBlockSM:Math.max((o.controlHeightSM-c*u)/2-o.lineWidth,0),paddingBlockLG:Math.max((o.controlHeightLG-s*g)/2-o.lineWidth,0)})},J=o=>{const{componentCls:e,iconCls:t,fontWeight:r,opacityLoading:n,motionDurationSlow:l,motionEaseInOut:i,marginXS:a,calc:c}=o;return{[e]:{outline:"none",position:"relative",display:"inline-flex",gap:o.marginXS,alignItems:"center",justifyContent:"center",fontWeight:r,whiteSpace:"nowrap",textAlign:"center",backgroundImage:"none",background:"transparent",border:`${(0,E.zA)(o.lineWidth)} ${o.lineType} transparent`,cursor:"pointer",transition:`all ${o.motionDurationMid} ${o.motionEaseInOut}`,userSelect:"none",touchAction:"manipulation",color:o.colorText,"&:disabled > *":{pointerEvents:"none"},[`${e}-icon > svg`]:(0,A.Nk)(),"> a":{color:"currentColor"},"&:not(:disabled)":(0,A.K8)(o),[`&${e}-two-chinese-chars::first-letter`]:{letterSpacing:"0.34em"},[`&${e}-two-chinese-chars > *:not(${t})`]:{marginInlineEnd:"-0.34em",letterSpacing:"0.34em"},[`&${e}-icon-only`]:{paddingInline:0,[`&${e}-compact-item`]:{flex:"none"},[`&${e}-round`]:{width:"auto"}},[`&${e}-loading`]:{opacity:n,cursor:"default"},[`${e}-loading-icon`]:{transition:["width","opacity","margin"].map(o=>`${o} ${l} ${i}`).join(",")},[`&:not(${e}-icon-end)`]:{[`${e}-loading-icon-motion`]:{"&-appear-start, &-enter-start":{marginInlineEnd:c(a).mul(-1).equal()},"&-appear-active, &-enter-active":{marginInlineEnd:0},"&-leave-start":{marginInlineEnd:0},"&-leave-active":{marginInlineEnd:c(a).mul(-1).equal()}}},"&-icon-end":{flexDirection:"row-reverse",[`${e}-loading-icon-motion`]:{"&-appear-start, &-enter-start":{marginInlineStart:c(a).mul(-1).equal()},"&-appear-active, &-enter-active":{marginInlineStart:0},"&-leave-start":{marginInlineStart:0},"&-leave-active":{marginInlineStart:c(a).mul(-1).equal()}}}}}},Z=(o,e,t)=>({[`&:not(:disabled):not(${o}-disabled)`]:{"&:hover":e,"&:active":t}}),oo=o=>({minWidth:o.controlHeight,paddingInlineStart:0,paddingInlineEnd:0,borderRadius:"50%"}),eo=o=>({borderRadius:o.controlHeight,paddingInlineStart:o.calc(o.controlHeight).div(2).equal(),paddingInlineEnd:o.calc(o.controlHeight).div(2).equal()}),to=o=>({cursor:"not-allowed",borderColor:o.borderColorDisabled,color:o.colorTextDisabled,background:o.colorBgContainerDisabled,boxShadow:"none"}),ro=(o,e,t,r,n,l,i,a)=>({[`&${o}-background-ghost`]:Object.assign(Object.assign({color:t||void 0,background:e,borderColor:r||void 0,boxShadow:"none"},Z(o,Object.assign({background:e},i),Object.assign({background:e},a))),{"&:disabled":{cursor:"not-allowed",color:n||void 0,borderColor:l||void 0}})}),no=o=>({[`&:disabled, &${o.componentCls}-disabled`]:Object.assign({},to(o))}),lo=o=>({[`&:disabled, &${o.componentCls}-disabled`]:{cursor:"not-allowed",color:o.colorTextDisabled}}),io=(o,e,t,r)=>{const n=r&&["link","text"].includes(r)?lo:no;return Object.assign(Object.assign({},n(o)),Z(o.componentCls,e,t))},ao=(o,e,t,r,n)=>({[`&${o.componentCls}-variant-solid`]:Object.assign({color:e,background:t},io(o,r,n))}),co=(o,e,t,r,n)=>({[`&${o.componentCls}-variant-outlined, &${o.componentCls}-variant-dashed`]:Object.assign({borderColor:e,background:t},io(o,r,n))}),so=o=>({[`&${o.componentCls}-variant-dashed`]:{borderStyle:"dashed"}}),uo=(o,e,t,r)=>({[`&${o.componentCls}-variant-filled`]:Object.assign({boxShadow:"none",background:e},io(o,t,r))}),go=(o,e,t,r,n)=>({[`&${o.componentCls}-variant-${t}`]:Object.assign({color:e,boxShadow:"none"},io(o,r,n,t))}),bo=o=>Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({color:o.defaultColor,boxShadow:o.defaultShadow},ao(o,o.solidTextColor,o.colorBgSolid,{color:o.solidTextColor,background:o.colorBgSolidHover},{color:o.solidTextColor,background:o.colorBgSolidActive})),so(o)),uo(o,o.colorFillTertiary,{background:o.colorFillSecondary},{background:o.colorFill})),ro(o.componentCls,o.ghostBg,o.defaultGhostColor,o.defaultGhostBorderColor,o.colorTextDisabled,o.colorBorder)),go(o,o.textTextColor,"link",{color:o.colorLinkHover,background:o.linkHoverBg},{color:o.colorLinkActive})),mo=o=>Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({color:o.colorPrimary,boxShadow:o.primaryShadow},co(o,o.colorPrimary,o.colorBgContainer,{color:o.colorPrimaryTextHover,borderColor:o.colorPrimaryHover,background:o.colorBgContainer},{color:o.colorPrimaryTextActive,borderColor:o.colorPrimaryActive,background:o.colorBgContainer})),so(o)),uo(o,o.colorPrimaryBg,{background:o.colorPrimaryBgHover},{background:o.colorPrimaryBorder})),go(o,o.colorPrimaryText,"text",{color:o.colorPrimaryTextHover,background:o.colorPrimaryBg},{color:o.colorPrimaryTextActive,background:o.colorPrimaryBorder})),go(o,o.colorPrimaryText,"link",{color:o.colorPrimaryTextHover,background:o.linkHoverBg},{color:o.colorPrimaryTextActive})),ro(o.componentCls,o.ghostBg,o.colorPrimary,o.colorPrimary,o.colorTextDisabled,o.colorBorder,{color:o.colorPrimaryHover,borderColor:o.colorPrimaryHover},{color:o.colorPrimaryActive,borderColor:o.colorPrimaryActive})),fo=o=>Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({color:o.colorError,boxShadow:o.dangerShadow},ao(o,o.dangerColor,o.colorError,{background:o.colorErrorHover},{background:o.colorErrorActive})),co(o,o.colorError,o.colorBgContainer,{color:o.colorErrorHover,borderColor:o.colorErrorBorderHover},{color:o.colorErrorActive,borderColor:o.colorErrorActive})),so(o)),uo(o,o.colorErrorBg,{background:o.colorErrorBgFilledHover},{background:o.colorErrorBgActive})),go(o,o.colorError,"text",{color:o.colorErrorHover,background:o.colorErrorBg},{color:o.colorErrorHover,background:o.colorErrorBgActive})),go(o,o.colorError,"link",{color:o.colorErrorHover},{color:o.colorErrorActive})),ro(o.componentCls,o.ghostBg,o.colorError,o.colorError,o.colorTextDisabled,o.colorBorder,{color:o.colorErrorHover,borderColor:o.colorErrorHover},{color:o.colorErrorActive,borderColor:o.colorErrorActive})),po=o=>Object.assign(Object.assign({},go(o,o.colorLink,"link",{color:o.colorLinkHover},{color:o.colorLinkActive})),ro(o.componentCls,o.ghostBg,o.colorInfo,o.colorInfo,o.colorTextDisabled,o.colorBorder,{color:o.colorInfoHover,borderColor:o.colorInfoHover},{color:o.colorInfoActive,borderColor:o.colorInfoActive})),vo=o=>{const{componentCls:e}=o;return Object.assign({[`${e}-color-default`]:bo(o),[`${e}-color-primary`]:mo(o),[`${e}-color-dangerous`]:fo(o),[`${e}-color-link`]:po(o)},(o=>{const{componentCls:e}=o;return H.s.reduce((t,r)=>{const n=o[`${r}6`],l=o[`${r}1`],i=o[`${r}5`],a=o[`${r}2`],c=o[`${r}3`],s=o[`${r}7`];return Object.assign(Object.assign({},t),{[`&${e}-color-${r}`]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({color:n,boxShadow:o[`${r}ShadowColor`]},ao(o,o.colorTextLightSolid,n,{background:i},{background:s})),co(o,n,o.colorBgContainer,{color:i,borderColor:i,background:o.colorBgContainer},{color:s,borderColor:s,background:o.colorBgContainer})),so(o)),uo(o,l,{background:a},{background:c})),go(o,n,"link",{color:i},{color:s})),go(o,n,"text",{color:i,background:l},{color:s,background:c}))})},{})})(o))},ho=o=>Object.assign(Object.assign(Object.assign(Object.assign({},co(o,o.defaultBorderColor,o.defaultBg,{color:o.defaultHoverColor,borderColor:o.defaultHoverBorderColor,background:o.defaultHoverBg},{color:o.defaultActiveColor,borderColor:o.defaultActiveBorderColor,background:o.defaultActiveBg})),go(o,o.textTextColor,"text",{color:o.textTextHoverColor,background:o.textHoverBg},{color:o.textTextActiveColor,background:o.colorBgTextActive})),ao(o,o.primaryColor,o.colorPrimary,{background:o.colorPrimaryHover,color:o.primaryColor},{background:o.colorPrimaryActive,color:o.primaryColor})),go(o,o.colorLink,"link",{color:o.colorLinkHover,background:o.linkHoverBg},{color:o.colorLinkActive})),Co=(o,e="")=>{const{componentCls:t,controlHeight:r,fontSize:n,borderRadius:l,buttonPaddingHorizontal:i,iconCls:a,buttonPaddingVertical:c,buttonIconOnlyFontSize:s}=o;return[{[e]:{fontSize:n,height:r,padding:`${(0,E.zA)(c)} ${(0,E.zA)(i)}`,borderRadius:l,[`&${t}-icon-only`]:{width:r,[a]:{fontSize:s}}}},{[`${t}${t}-circle${e}`]:oo(o)},{[`${t}${t}-round${e}`]:eo(o)}]},yo=o=>{const e=(0,j.oX)(o,{fontSize:o.contentFontSize});return Co(e,o.componentCls)},So=o=>{const e=(0,j.oX)(o,{controlHeight:o.controlHeightSM,fontSize:o.contentFontSizeSM,padding:o.paddingXS,buttonPaddingHorizontal:o.paddingInlineSM,buttonPaddingVertical:0,borderRadius:o.borderRadiusSM,buttonIconOnlyFontSize:o.onlyIconSizeSM});return Co(e,`${o.componentCls}-sm`)},$o=o=>{const e=(0,j.oX)(o,{controlHeight:o.controlHeightLG,fontSize:o.contentFontSizeLG,buttonPaddingHorizontal:o.paddingInlineLG,buttonPaddingVertical:0,borderRadius:o.borderRadiusLG,buttonIconOnlyFontSize:o.onlyIconSizeLG});return Co(e,`${o.componentCls}-lg`)},xo=o=>{const{componentCls:e}=o;return{[e]:{[`&${e}-block`]:{width:"100%"}}}};var ko=(0,B.OF)("Button",o=>{const e=_(o);return[J(e),yo(e),So(e),$o(e),xo(e),vo(e),ho(e),I(e)]},Y,{unitless:{fontWeight:!0,contentLineHeight:!0,contentLineHeightSM:!0,contentLineHeightLG:!0}}),Oo=t(5974);function Eo(o,e){return{[`&-item:not(${e}-last-item)`]:{marginBottom:o.calc(o.lineWidth).mul(-1).equal()},"&-item":{"&:hover,&:focus,&:active":{zIndex:2},"&[disabled]":{zIndex:0}}}}function Ao(o){const e=`${o.componentCls}-compact-vertical`;return{[e]:Object.assign(Object.assign({},Eo(o,e)),(t=o.componentCls,r=e,{[`&-item:not(${r}-first-item):not(${r}-last-item)`]:{borderRadius:0},[`&-item${r}-first-item:not(${r}-last-item)`]:{[`&, &${t}-sm, &${t}-lg`]:{borderEndEndRadius:0,borderEndStartRadius:0}},[`&-item${r}-last-item:not(${r}-first-item)`]:{[`&, &${t}-sm, &${t}-lg`]:{borderStartStartRadius:0,borderStartEndRadius:0}}}))};var t,r}const Ho=o=>{const{componentCls:e,colorPrimaryHover:t,lineWidth:r,calc:n}=o,l=n(r).mul(-1).equal(),i=o=>{const n=`${e}-compact${o?"-vertical":""}-item${e}-primary:not([disabled])`;return{[`${n} + ${n}::before`]:{position:"absolute",top:o?l:0,insetInlineStart:o?0:l,backgroundColor:t,content:'""',width:o?"100%":r,height:o?r:"100%"}}};return Object.assign(Object.assign({},i()),i(!0))};var jo=(0,B.bf)(["Button","compact"],o=>{const e=_(o);return[(0,Oo.G)(e),Ao(e),Ho(e)]},Y),Bo=function(o,e){var t={};for(var r in o)Object.prototype.hasOwnProperty.call(o,r)&&e.indexOf(r)<0&&(t[r]=o[r]);if(null!=o&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(r=Object.getOwnPropertySymbols(o);n<r.length;n++)e.indexOf(r[n])<0&&Object.prototype.propertyIsEnumerable.call(o,r[n])&&(t[r[n]]=o[r[n]])}return t};const wo={default:["default","outlined"],primary:["primary","solid"],dashed:["default","dashed"],link:["link","link"],text:["default","text"]},Io=r.forwardRef((o,e)=>{var t,n;const{loading:b=!1,prefixCls:m,color:p,variant:h,type:C,danger:y=!1,shape:$="default",size:x,styles:k,disabled:E,className:A,rootClassName:H,children:j,icon:B,iconPosition:w="start",ghost:I=!1,block:z=!1,htmlType:P="button",classNames:T,style:L={},autoInsertSpace:R,autoFocus:N}=o,F=Bo(o,["loading","prefixCls","color","variant","type","danger","shape","size","styles","disabled","className","rootClassName","children","icon","iconPosition","ghost","block","htmlType","classNames","style","autoInsertSpace","autoFocus"]),G=C||"default",{button:W}=r.useContext(s.QO),[M,D]=(0,r.useMemo)(()=>{if(p&&h)return[p,h];if(C||y){const o=wo[G]||[];return y?["danger",o[1]]:o}return(null==W?void 0:W.color)&&(null==W?void 0:W.variant)?[W.color,W.variant]:["default","outlined"]},[C,p,h,y,null==W?void 0:W.variant,null==W?void 0:W.color]),q="danger"===M?"dangerous":M,{getPrefixCls:X,direction:Q,autoInsertSpace:V,className:K,style:U,classNames:_,styles:Y}=(0,s.TP)("button"),J=null===(t=null!=R?R:V)||void 0===t||t,Z=X("btn",m),[oo,eo,to]=ko(Z),ro=(0,r.useContext)(d.A),no=null!=E?E:ro,lo=(0,r.useContext)(f),io=(0,r.useMemo)(()=>function(o){if("object"==typeof o&&o){let e=null==o?void 0:o.delay;return e=Number.isNaN(e)||"number"!=typeof e?0:e,{loading:e<=0,delay:e}}return{loading:!!o,delay:0}}(b),[b]),[ao,co]=(0,r.useState)(io.loading),[so,uo]=(0,r.useState)(!1),go=(0,r.useRef)(null),bo=(0,a.xK)(e,go),mo=1===r.Children.count(j)&&!B&&!(0,v.u1)(D),fo=(0,r.useRef)(!0);r.useEffect(()=>(fo.current=!1,()=>{fo.current=!0}),[]),(0,r.useLayoutEffect)(()=>{let o=null;return io.delay>0?o=setTimeout(()=>{o=null,co(!0)},io.delay):co(io.loading),function(){o&&(clearTimeout(o),o=null)}},[io.delay,io.loading]),(0,r.useEffect)(()=>{if(!go.current||!J)return;const o=go.current.textContent||"";mo&&(0,v.Ap)(o)?so||uo(!0):so&&uo(!1)}),(0,r.useEffect)(()=>{N&&go.current&&go.current.focus()},[]);const po=r.useCallback(e=>{var t;ao||no?e.preventDefault():null===(t=o.onClick)||void 0===t||t.call(o,e)},[o.onClick,ao,no]);const{compactSize:vo,compactItemClassnames:ho}=(0,g.RQ)(Z,Q),Co={large:"lg",small:"sm",middle:void 0},yo=(0,u.A)(o=>{var e,t;return null!==(t=null!==(e=null!=x?x:vo)&&void 0!==e?e:lo)&&void 0!==t?t:o}),So=yo&&null!==(n=Co[yo])&&void 0!==n?n:"",$o=ao?"loading":B,xo=(0,i.A)(F,["navigate"]),Oo=l()(Z,eo,to,{[`${Z}-${$}`]:"default"!==$&&$,[`${Z}-${G}`]:G,[`${Z}-dangerous`]:y,[`${Z}-color-${q}`]:q,[`${Z}-variant-${D}`]:D,[`${Z}-${So}`]:So,[`${Z}-icon-only`]:!j&&0!==j&&!!$o,[`${Z}-background-ghost`]:I&&!(0,v.u1)(D),[`${Z}-loading`]:ao,[`${Z}-two-chinese-chars`]:so&&J&&!ao,[`${Z}-block`]:z,[`${Z}-rtl`]:"rtl"===Q,[`${Z}-icon-end`]:"end"===w},ho,A,H,K),Eo=Object.assign(Object.assign({},U),L),Ao=l()(null==T?void 0:T.icon,_.icon),Ho=Object.assign(Object.assign({},(null==k?void 0:k.icon)||{}),Y.icon||{}),Io=B&&!ao?r.createElement(S,{prefixCls:Z,className:Ao,style:Ho},B):b&&"object"==typeof b&&b.icon?r.createElement(S,{prefixCls:Z,className:Ao,style:Ho},b.icon):r.createElement(O,{existIcon:!!B,prefixCls:Z,loading:ao,mount:fo.current}),zo=j||0===j?(0,v.uR)(j,mo&&J):null;if(void 0!==xo.href)return oo(r.createElement("a",Object.assign({},xo,{className:l()(Oo,{[`${Z}-disabled`]:no}),href:no?void 0:xo.href,style:Eo,onClick:po,ref:bo,tabIndex:no?-1:0}),Io,zo));let Po=r.createElement("button",Object.assign({},F,{type:P,className:Oo,style:Eo,onClick:po,disabled:no,ref:bo}),Io,zo,ho&&r.createElement(jo,{prefixCls:Z}));return(0,v.u1)(D)||(Po=r.createElement(c.A,{component:"Button",disabled:ao},Po)),oo(Po)});Io.Group=p,Io.__ANT_BUTTON=!0;var zo=Io},4424:function(o,e,t){t.d(e,{D:function(){return r}});const r=`${t(2279).yH}-wave-target`},9449:function(o,e,t){t.d(e,{Ap:function(){return c},DU:function(){return s},u1:function(){return u},uR:function(){return g}});var r=t(436),n=t(6540),l=t(682),i=t(3950);const a=/^[\u4E00-\u9FA5]{2}$/,c=a.test.bind(a);function s(o){return"danger"===o?{danger:!0}:{type:o}}function d(o){return"string"==typeof o}function u(o){return"text"===o||"link"===o}function g(o,e){let t=!1;const r=[];return n.Children.forEach(o,o=>{const e=typeof o,n="string"===e||"number"===e;if(t&&n){const e=r.length-1,t=r[e];r[e]=`${t}${o}`}else r.push(o);t=n}),n.Children.map(r,o=>function(o,e){if(null==o)return;const t=e?" ":"";return"string"!=typeof o&&"number"!=typeof o&&d(o.type)&&c(o.props.children)?(0,l.Ob)(o,{children:o.props.children.split("").join(t)}):d(o)?c(o)?n.createElement("span",null,o.split("").join(t)):n.createElement("span",null,o):(0,l.zv)(o)?n.createElement("span",null,o):o}(o,e))}["default","primary","danger"].concat((0,r.A)(i.s))}}]);
//# sourceMappingURL=ec7bbf74804a310b8f8f51dc229d43a709238ec2-57c8056ffe85b7b51ee4.js.map