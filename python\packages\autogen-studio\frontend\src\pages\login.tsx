import React, { useEffect } from "react";
import { useAuth } from "../auth/context";
import { navigate } from "gatsby";
import { Typography, Spin } from "antd";
import Layout from "../components/layout";
import { graphql } from "gatsby";
import Icon from "../components/icons";

const { Title, Text } = Typography;

// Use the same token key as in context.tsx
const TOKEN_KEY = "auth_token";

const LoginPage = ({ data }: any) => {
  const { isAuthenticated, isLoading, authType } = useAuth();

  useEffect(() => {
    // If user is already authenticated, redirect to home
    if (isAuthenticated && !isLoading) {
      navigate("/");
    }
  }, [isAuthenticated, isLoading]);

  // If auth type is 'none', redirect to home
  useEffect(() => {
    if (authType === "none" && !isLoading) {
      navigate("/");
    }
  }, [authType, isLoading]);



  if (isLoading) {
    return (
      <Layout meta={data.site.siteMetadata} title="Login" link="/login">
        <div className="flex items-center justify-center h-screen">
          <Spin size="large" tip="加载中..." />
        </div>
      </Layout>
    );
  }

  return (
    <Layout
      meta={data.site.siteMetadata}
      title="登录"
      link="/login"
      showHeader={true}
      restricted={false}
    >
      <div className="flex items-center justify-center h-[calc(100vh-164px)]">
        <div className="w-full rounded bg-secondary max-w-md p-8 sxhadow-sm">
          <div className="text-center mb-8">
            <div className="mb-3">
              <Icon icon="app" size={12} />
            </div>
            <div className="text-2xl mb-1 font-semibold text-primary">
              登录到 {data.site.siteMetadata.title}
            </div>
            <div className="text-secondary text-sm">
              请使用token登录或联系管理员
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export const query = graphql`
  query LoginPageQuery {
    site {
      siteMetadata {
        description
        title
      }
    }
  }
`;

export default LoginPage;
