{"version": 3, "file": "component---src-pages-mcp-tsx-7f442b7a1612826fd54d.js", "mappings": ";oJASA,MAAMA,GAAQ,E,QAAA,GAAiB,QAAS,CACtC,CAAC,SAAU,CAAEC,GAAI,KAAMC,GAAI,KAAMC,EAAG,KAAMC,IAAK,WAC/C,CAAC,OAAQ,CAAEC,EAAG,kDAAmDD,IAAK,WACtE,CAAC,OAAQ,CAAEC,EAAG,WAAYD,IAAK,Y,uDCHjC,MAAME,GAAY,E,QAAA,GAAiB,YAAa,CAC9C,CAAC,OAAQ,CAAED,EAAG,qDAAsDD,IAAK,WACzE,CAAC,OAAQ,CAAEC,EAAG,aAAcD,IAAK,WACjC,CAAC,OAAQ,CAAEC,EAAG,sDAAuDD,IAAK,WAC1E,CAAC,OAAQ,CAAEC,EAAG,YAAaD,IAAK,Y,qCCZlC,SAASG,GAAwB,MAC/BC,EAAK,QACLC,KACGC,GACFC,GACD,OAAoB,gBAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKR,EACL,kBAAmBF,GAClBC,GAAQF,EAAqB,gBAAoB,QAAS,CAC3DY,GAAIX,GACHD,GAAS,KAAmB,gBAAoB,OAAQ,CACzDa,cAAe,QACfC,eAAgB,QAChBjB,EAAG,qLAEP,CACA,MAAMkB,EAA2B,aAAiBhB,GAClD,K,gPCJA,MAAM,OAAEiB,GAAWC,EAAAA,EA4VnB,MA7UqDC,IAO9C,IAP+C,OACpDC,EAAM,SACNC,EAAQ,kBACRC,EAAiB,UACjBC,GAAY,EAAK,iBACjBC,EAAgB,gBAChBC,GACDN,EACC,MAAM,EAACO,EAAU,EAACC,IAAgBC,EAAAA,EAAAA,UAAoB,KAChD,EAACC,EAAgB,EAACC,IAAsBF,EAAAA,EAAAA,UAAyB,OACjE,EAACG,EAAe,EAACC,IAAqBJ,EAAAA,EAAAA,UAE1C,KACI,EAACK,EAAuB,EAACC,IAC7BN,EAAAA,EAAAA,WAAkB,IACd,EAACO,EAAiB,EAACC,IAAuBR,EAAAA,EAAAA,WAAS,IAEnD,KAAES,IAASC,EAAAA,EAAAA,YAAWC,EAAAA,GAetBC,GAAsBC,EAAAA,EAAAA,aAAaC,IACvCZ,EAAmBY,GACfA,EAAQ7B,IACV8B,aAAaC,QAAQ,mBAAoBF,EAAQ7B,GAAGgC,YAItD,MAAMC,EAAcC,EAAAA,GAAOC,sBAAsBN,GACjDV,EAAkBc,GAElBZ,GAA2B,IAC1B,IAGGe,GAAgBR,EAAAA,EAAAA,aAAYS,UAChC,GAAKb,SAAAA,EAAMxB,GAEX,IACEuB,GAAoB,GACpB,MAAMe,QAAsBJ,EAAAA,GAAOK,cAAcf,EAAKxB,IACtDc,EAAawB,GAGb,MACME,EADS,IAAIC,gBAAgBC,OAAOC,SAASC,QACvBC,IAAI,aAC1BC,EAAiBhB,aAAaiB,QAAQ,oBAC5C,IAAIC,EAAkBV,EAAc,GAEpC,GAAIE,EAAc,CAEhB,MAAMS,EAAaX,EAAcY,KAC9BC,IAAC,IAAAC,EAAA,OAAS,QAAJA,EAAAD,EAAEnD,UAAE,IAAAoD,OAAA,EAAJA,EAAMpB,cAAeQ,IAE1BS,IACFD,EAAkBC,EAEtB,MAAO,GAAIH,EAAgB,CAEzB,MAAMO,EAAef,EAAcY,KAChCC,IAAC,IAAAG,EAAA,OAAS,QAAJA,EAAAH,EAAEnD,UAAE,IAAAsD,OAAA,EAAJA,EAAMtB,cAAec,IAE1BO,IACFL,EAAkBK,EAEtB,CAEA,GAAIL,EAAiB,CAEnB/B,EAAmB+B,GACfA,EAAgBhD,IAClB8B,aAAaC,QACX,mBACAiB,EAAgBhD,GAAGgC,YAIvB,MAAMC,EAAcC,EAAAA,GAAOC,sBAAsBa,GACjD7B,EAAkBc,EAGpB,CACF,CAAE,MAAOsB,GACPC,QAAQD,MAAM,4BAA6BA,EAC7C,CAAC,QACChC,GAAoB,EACtB,GACC,CAACC,aAAI,EAAJA,EAAMxB,KAiDV,OA/CAyD,EAAAA,EAAAA,WAAU,KACRrB,KACC,CAACA,KAGJqB,EAAAA,EAAAA,WAAU,KACR,GAAIvC,EAAewC,OAAS,GAAK1C,IAAoBM,EAAkB,CAAC,IAADqC,EAErE,MAAMC,EAAS,IAAInB,gBAAgBC,OAAOC,SAASC,QAC7CJ,EAAeoB,EAAOf,IAAI,aAC1BgB,EAAoBD,EAAOf,IAAI,kBAErC,IAAIiB,EAAgB,EAGpB,GACEtB,GACAqB,IACkB,QAAlBF,EAAA3C,EAAgBhB,UAAE,IAAA2D,OAAA,EAAlBA,EAAoB3B,cAAeQ,EACnC,CACA,MAAMuB,EAAWC,SAASH,GACtBE,GAAY,GAAKA,EAAW7C,EAAewC,SAC7CI,EAAgBC,EAEpB,CAGI3C,IAA2B0C,IAC7BzC,EAA0ByC,GAC1BrD,EACES,EAAe4C,GACf9C,EAAgBhB,GAChB8D,GAGN,MAC4B,IAA1B5C,EAAewC,SACa,IAA5BtC,GACCE,IAGDD,GAA2B,GAC3BZ,EAAkB,QAEnB,CAACS,EAAgBF,EAAiBP,EAAmBa,IAGnDf,EAkBH0D,EAAAA,cAAA,OAAKC,UAAU,+CAEbD,EAAAA,cAAA,OAAKC,UAAU,kFACbD,EAAAA,cAAA,OAAKC,UAAU,kCACbD,EAAAA,cAAA,QAAMC,UAAU,4BAA2B,kBAC3CD,EAAAA,cAAA,QAAMC,UAAU,wDACbhD,EAAewC,SAGpBO,EAAAA,cAACE,EAAAA,EAAO,CAAC/E,MAAM,iBACb6E,EAAAA,cAAA,UACEG,QAAS5D,EACT0D,UAAU,gKAEVD,EAAAA,cAACI,EAAAA,EAAc,CAACxE,YAAa,IAAKqE,UAAU,eAMlDD,EAAAA,cAAA,OAAKC,UAAU,sCACbD,EAAAA,cAAA,OAAKC,UAAU,0CACbD,EAAAA,cAAA,QAAMC,UAAU,0BAAyB,WACzCD,EAAAA,cAACE,EAAAA,EAAO,CAAC/E,MAAM,qBACb6E,EAAAA,cAACK,EAAAA,GAAM,CACLC,KAAK,QACLC,KACElD,EACE2C,EAAAA,cAAC/E,EAAAA,EAAS,CAACgF,UAAU,yBAErBD,EAAAA,cAAC/E,EAAAA,EAAS,CAACgF,UAAU,YAGzBA,UAAU,8BACVE,QAAShC,EACTqC,SAAUnD,MAIhB2C,EAAAA,cAAC5D,EAAAA,EAAM,CACL6D,UAAU,SACVQ,YAAY,mBACZC,MAAO3D,aAAe,EAAfA,EAAiBhB,GACxB4E,SAAWC,IACT,MAAMhD,EAAUhB,EAAUqC,KAAMC,GAAMA,EAAEnD,KAAO6E,GAC3ChD,GAASF,EAAoBE,IAEnCiD,QAASxD,GAERT,EAAUkE,IAAKlD,GACdoC,EAAAA,cAAC7D,EAAM,CAACpB,IAAK6C,EAAQ7B,GAAI2E,MAAO9C,EAAQ7B,IACtCiE,EAAAA,cAAA,OAAKC,UAAU,2BACbD,EAAAA,cAACe,EAAAA,EAAO,CAACd,UAAU,YACnBD,EAAAA,cAAA,YAAOpC,EAAQoD,OAAOC,MACrBrD,EAAQoD,OAAOE,KACdlB,EAAAA,cAACrF,EAAAA,EAAK,CAACsF,UAAU,gCAS7BD,EAAAA,cAAA,OAAKC,UAAU,yCACbD,EAAAA,cAAA,OAAKC,UAAU,QAAO,kBAEnBxD,GAAauD,EAAAA,cAAC/E,EAAAA,EAAS,CAACgF,UAAU,iCAKrClD,GACAiD,EAAAA,cAAA,OAAKC,UAAU,iFACbD,EAAAA,cAACmB,EAAAA,EAAI,CAAClB,UAAU,wCAAwC,4CAK3DlD,GAA6C,IAA1BE,EAAewC,QACjCO,EAAAA,cAAA,OAAKC,UAAU,iFACbD,EAAAA,cAACmB,EAAAA,EAAI,CAAClB,UAAU,wCAAwC,4CAK5DD,EAAAA,cAAA,OAAKC,UAAU,+CACZhD,EAAe6D,IAAI,CAACM,EAAWC,KAAW,IAADC,EAAAC,EACxC,MAAMC,EAzOUC,EACtBL,EACAC,KAGA,MAAMK,EAAYC,KAAKC,UAAUR,EAAUJ,QAC3C,MAAO,GAAGI,EAAUS,YAClBT,EAAUU,OAAS,aACjBJ,EAAUK,MAAM,EAAG,OAAOV,KAiOHI,CAAgBL,EAAWC,GAC1CW,EAAaX,IAAUlE,EAE7B,OACE6C,EAAAA,cAAA,OAAKjF,IAAKyG,EAAcvB,UAAU,6BAChCD,EAAAA,cAAA,OACEC,UAAW,8EACT+B,EAAa,YAAc,iBAG/BhC,EAAAA,cAAA,OACEC,UAAW,6EACT+B,EACI,6BACA,sBAEN7B,QAASA,KACP/C,EAA0BiE,GAC1B7E,EAAkB4E,EAAWrE,aAAe,EAAfA,EAAiBhB,GAAIsF,KAIpDrB,EAAAA,cAAA,OAAKC,UAAU,6CACbD,EAAAA,cAAA,OAAKC,UAAU,0CACbD,EAAAA,cAACiC,EAAAA,EAAI,CACH1B,KAAK,MACLD,KAAM,EACNL,UAAU,sCAEZD,EAAAA,cAAA,OAAKC,UAAU,2BACbD,EAAAA,cAAA,QAAMC,UAAU,eAAemB,EAAUU,UAM/C9B,EAAAA,cAAA,OAAKC,UAAU,+BACbD,EAAAA,cAAA,OAAKC,UAAU,2BACbD,EAAAA,cAAA,QAAMC,UAAU,qBACiB,QAA9BqB,EAAAF,EAAUJ,OAAOkB,qBAAa,IAAAZ,GAAM,QAANC,EAA9BD,EAAgCa,YAAI,IAAAZ,OAAN,EAA9BA,EAAsCa,QACrC,eACA,MACG,uBAgBpBrF,GACCiD,EAAAA,cAAA,OAAKC,UAAU,wDACbD,EAAAA,cAAA,OAAKC,UAAU,qCACbD,EAAAA,cAAA,QAAMC,UAAU,mBACblD,EAAgBiE,OAAOC,MAE1BjB,EAAAA,cAAA,QAAMC,UAAU,sBACbhD,EAAewC,OAAO,iBACI,IAA1BxC,EAAewC,OAAe,KAAO,KAGzC1C,EAAgBsF,YACfrC,EAAAA,cAAA,OAAKC,UAAU,8BAA6B,YACjCqC,EAAAA,EAAAA,IAAsBvF,EAAgBsF,eA/KvDrC,EAAAA,cAAA,OAAKC,UAAU,oCACbD,EAAAA,cAAA,OAAKC,UAAU,aACbD,EAAAA,cAACE,EAAAA,EAAO,CAAC/E,MAAO,oBAAoB8B,EAAewC,WACjDO,EAAAA,cAAA,UACEG,QAAS5D,EACT0D,UAAU,gKAEVD,EAAAA,cAACuC,EAAAA,EAAa,CAAC3G,YAAa,IAAKqE,UAAU,iB,UCvJzD,MAvB4C5D,IAGrC,IAADmG,EAAA,IAHuC,UAC3CpB,EAAS,iBACTqB,GACDpG,EACC,MAAMqG,EAAetB,EAAUJ,OAAOkB,cAEpCQ,SAAkB,QAANF,EAAZE,EAAcP,YAAI,IAAAK,GAAlBA,EAAoBJ,QAAQ,eAAgB,IAE9C,OACEpC,EAAAA,cAAA,OAAKC,UAAU,MACbD,EAAAA,cAAC2C,EAAAA,GAAe,CACdC,UAAWxB,EACXyB,gBAAiB,CAAC,WAClBC,UAAQ,EACRnC,SAAUA,W,oBCsLlB,MAvM6BoC,KAC3B,MAAM,EAACtG,EAAU,EAACuG,IAAgBlG,EAAAA,EAAAA,WAAS,IACrC,EAACJ,EAAiB,EAACuG,IACvBnG,EAAAA,EAAAA,UAA+C,OAC3C,EAACoG,EAAc,EAACC,IAAoBrG,EAAAA,EAAAA,UAAS,KACjD,GAAsB,oBAAX2B,OAAwB,CACjC,MAAM2E,EAASvF,aAAaiB,QAAQ,cACpC,OAAkB,OAAXsE,GAAkBzB,KAAK0B,MAAMD,EACtC,CACA,OAAO,KAGH,KAAE7F,IAASC,EAAAA,EAAAA,YAAWC,EAAAA,IACrB6F,EAAYC,GAAiBC,EAAAA,GAAQC,cAG5CjE,EAAAA,EAAAA,WAAU,KACc,oBAAXf,QACTZ,aAAaC,QAAQ,aAAc6D,KAAKC,UAAUsB,KAEnD,CAACA,KAGJ1D,EAAAA,EAAAA,WAAU,KACR,MAAMG,EAAS,IAAInB,gBAAgBC,OAAOC,SAASC,QACjCgB,EAAOf,IAAI,aACNe,EAAOf,IAAI,mBAMjC,CAAClC,KAGJ8C,EAAAA,EAAAA,WAAU,KACR,MAAMkE,EAAuBA,KAC3B,MAAM/D,EAAS,IAAInB,gBAAgBC,OAAOC,SAASC,QAC7CiC,EAAYjB,EAAOf,IAAI,aACvB+E,EAAiBhE,EAAOf,IAAI,kBAE7BgC,GAAc+C,GAEbjH,GACFuG,EAAoB,OAM1B,OADAxE,OAAOmF,iBAAiB,WAAYF,GAC7B,IAAMjF,OAAOoF,oBAAoB,WAAYH,IACnD,CAAChH,IAkEJ,OAAKa,SAAAA,EAAMxB,GASTiE,EAAAA,cAAA,OAAKC,UAAU,+BACZsD,EAGDvD,EAAAA,cAAA,OACEC,UAAW,yEACTiD,EAAgB,OAAS,SAG3BlD,EAAAA,cAAC8D,EAAU,CACTxH,OAAQ4G,EACR3G,SAAUA,IAAM4G,GAAkBD,GAClC1G,kBArFsBuH,CAC5B3C,EACAR,EACA+C,KAKA,GAHAV,EAAoB7B,GAGhBA,QAA2B4C,IAAdpD,QAA8CoD,IAAnBL,EAA8B,CACxE,MAAMhE,EAAS,IAAInB,gBAAgBC,OAAOC,SAASC,QACnDgB,EAAOsE,IAAI,YAAarD,EAAU7C,YAClC4B,EAAOsE,IAAI,iBAAkBN,EAAe5F,YAC5CU,OAAOyF,QAAQC,UAAU,CAAC,EAAG,GAAI,IAAIxE,EAAO5B,aAC9C,MAAYqD,GAEV3C,OAAOyF,QAAQC,UAAU,CAAC,EAAG,GAAI1F,OAAOC,SAAS0F,WAuE7C3H,UAAWA,EACXC,iBAAkBA,EAClBC,gBArEoByB,UAC1B,GAAKb,SAAAA,EAAMxB,IAAOsI,EAAetI,GAEjC,IACEiH,GAAa,GAEb,MAAMsB,EAAmB,IACpBD,EACHE,gBAAYP,EACZ3B,gBAAY2B,SAERQ,EAAAA,EAAWC,cACfJ,EAAetI,GACfuI,EACA/G,EAAKxB,IAEPuH,EAAWoB,QAAQ,+BACrB,CAAE,MAAOpF,GACPC,QAAQD,MAAM,4BAA6BA,GAC3CgE,EAAWhE,MAAM,2BACnB,CAAC,QACC0D,GAAa,EACf,MAoDEhD,EAAAA,cAAA,OACEC,UAAW,6CACTiD,EAAgB,QAAU,UAG5BlD,EAAAA,cAAA,OAAKC,UAAU,iCACbD,EAAAA,cAAA,OAAKC,UAAU,oEACbD,EAAAA,cAAC9E,EAAAA,EAAuB,CAAC+E,UAAU,2DAA4D,IAAI,iFAEtF,KAGfD,EAAAA,cAAA,OAAKC,UAAU,wCACbD,EAAAA,cAAA,QAAMC,UAAU,4BAA2B,kBAC1CvD,GACCsD,EAAAA,cAAAA,EAAAA,SAAA,KACEA,EAAAA,cAAC2E,EAAAA,EAAY,CAAC1E,UAAU,2BACxBD,EAAAA,cAAA,QAAMC,UAAU,kBAAkBvD,EAAiBoF,SAMxDrF,IAAcC,EACbsD,EAAAA,cAAA,OAAKC,UAAU,yEAAwE,cAGrFvD,EACFsD,EAAAA,cAAA,OAAKC,UAAU,kDACZ,IACDD,EAAAA,cAAC4E,EAAS,CACRxD,UAAW1E,EACX+F,iBAAkBA,IAjFHrE,WAG3B,IACE4E,GAAa,SACa/E,EAAAA,GAAO4G,kBAAkBzD,GAGjDkC,EAAWoB,QAAQ,8BAEnBpB,EAAWhE,MAAM,yBAErB,CAAE,MAAOA,GACPC,QAAQD,MAAM,0BAA2BA,GACzCgE,EAAWhE,MAAM,yBACnB,CAAC,QACC0D,GAAa,EACf,GAgEoC8B,CAAqBpI,MAIjDsD,EAAAA,cAAA,OAAKC,UAAU,yEACbD,EAAAA,cAAA,OAAKC,UAAU,eACbD,EAAAA,cAAA,MAAIC,UAAU,4BAA2B,6BAGzCD,EAAAA,cAAA,KAAGC,UAAU,uBAAsB,kEApE7CD,EAAAA,cAAA,OAAKC,UAAU,yEAAwE,4CC3G7F,MArBgB5D,IAAmB,IAAlB,KAAE0I,GAAW1I,EAC5B,OACE2D,EAAAA,cAACgF,EAAAA,EAAM,CAACC,KAAMF,EAAKG,KAAKC,aAAchK,MAAM,UAAUiK,KAAM,QAC1DpF,EAAAA,cAAA,QAAMqF,MAAO,CAAEC,OAAQ,QAAUrF,UAAU,UACzCD,EAAAA,cAAC+C,EAAU,Q", "sources": ["webpack://autogentstudio/./node_modules/lucide-react/dist/esm/icons/globe.js", "webpack://autogentstudio/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js", "webpack://autogentstudio/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js", "webpack://autogentstudio/./src/components/views/mcp/sidebar.tsx", "webpack://autogentstudio/./src/components/views/mcp/detail.tsx", "webpack://autogentstudio/./src/components/views/mcp/manager.tsx", "webpack://autogentstudio/./src/pages/mcp.tsx"], "sourcesContent": ["/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst Globe = createLucideIcon(\"Globe\", [\n  [\"circle\", { cx: \"12\", cy: \"12\", r: \"10\", key: \"1mglay\" }],\n  [\"path\", { d: \"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20\", key: \"13o1zl\" }],\n  [\"path\", { d: \"M2 12h20\", key: \"9i4pu4\" }]\n]);\n\nexport { Globe as default };\n//# sourceMappingURL=globe.js.map\n", "/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst RefreshCw = createLucideIcon(\"RefreshCw\", [\n  [\"path\", { d: \"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8\", key: \"v9h5vc\" }],\n  [\"path\", { d: \"M21 3v5h-5\", key: \"1q7to0\" }],\n  [\"path\", { d: \"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16\", key: \"3uifl3\" }],\n  [\"path\", { d: \"M8 16H3v5\", key: \"1cv678\" }]\n]);\n\nexport { RefreshCw as default };\n//# sourceMappingURL=refresh-cw.js.map\n", "import * as React from \"react\";\nfunction ExclamationTriangleIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ExclamationTriangleIcon);\nexport default ForwardRef;", "import React, { useState, useEffect, useContext, useCallback } from \"react\";\r\nimport { Button, Tooltip, Select } from \"antd\";\r\nimport {\r\n  PanelLeftClose,\r\n  PanelLeftOpen,\r\n  Package,\r\n  RefreshCw,\r\n  Info,\r\n  Globe,\r\n} from \"lucide-react\";\r\nimport { appContext } from \"../../../hooks/provider\";\r\nimport { mcpAPI } from \"./api\";\r\nimport type {\r\n  Gallery,\r\n  Component,\r\n  McpWorkbenchConfig,\r\n} from \"../../types/datamodel\";\r\nimport { getRelativeTimeString } from \"../atoms\";\r\nimport Icon from \"../../icons\";\r\nimport { ExclamationTriangleIcon } from \"@heroicons/react/24/outline\";\r\n\r\nconst { Option } = Select;\r\n\r\ninterface McpSidebarProps {\r\n  isOpen: boolean;\r\n  onToggle: () => void;\r\n  onSelectWorkbench: (\r\n    workbench: Component<McpWorkbenchConfig> | null,\r\n    galleryId?: number,\r\n    workbenchIndex?: number\r\n  ) => void;\r\n  isLoading?: boolean;\r\n  currentWorkbench: Component<McpWorkbenchConfig> | null;\r\n  onGalleryUpdate?: (gallery: Gallery) => void;\r\n}\r\n\r\nexport const McpSidebar: React.FC<McpSidebarProps> = ({\r\n  isOpen,\r\n  onToggle,\r\n  onSelectWorkbench,\r\n  isLoading = false,\r\n  currentWorkbench,\r\n  onGalleryUpdate,\r\n}) => {\r\n  const [galleries, setGalleries] = useState<Gallery[]>([]);\r\n  const [selectedGallery, setSelectedGallery] = useState<Gallery | null>(null);\r\n  const [mcpWorkbenches, setMcpWorkbenches] = useState<\r\n    Component<McpWorkbenchConfig>[]\r\n  >([]);\r\n  const [selectedWorkbenchIndex, setSelectedWorkbenchIndex] =\r\n    useState<number>(-1);\r\n  const [loadingGalleries, setLoadingGalleries] = useState(false);\r\n\r\n  const { user } = useContext(appContext);\r\n\r\n  // Helper function to get a unique React key for rendering\r\n  const getWorkbenchKey = (\r\n    workbench: Component<McpWorkbenchConfig>,\r\n    index: number\r\n  ) => {\r\n    // Use a hash of the workbench config plus index to ensure uniqueness\r\n    const configStr = JSON.stringify(workbench.config);\r\n    return `${workbench.provider}-${\r\n      workbench.label || \"unnamed\"\r\n    }-${configStr.slice(0, 20)}-${index}`;\r\n  };\r\n\r\n  // Handle gallery selection\r\n  const handleGallerySelect = useCallback((gallery: Gallery) => {\r\n    setSelectedGallery(gallery);\r\n    if (gallery.id) {\r\n      localStorage.setItem(\"mcp-view-gallery\", gallery.id.toString());\r\n    }\r\n\r\n    // Extract MCP workbenches from the selected gallery\r\n    const workbenches = mcpAPI.extractMcpWorkbenches(gallery);\r\n    setMcpWorkbenches(workbenches);\r\n    // Reset selection - let the effect handle workbench selection\r\n    setSelectedWorkbenchIndex(-1);\r\n  }, []);\r\n\r\n  // Load galleries on component mount\r\n  const loadGalleries = useCallback(async () => {\r\n    if (!user?.id) return;\r\n\r\n    try {\r\n      setLoadingGalleries(true);\r\n      const galleriesData = await mcpAPI.listGalleries(user.id);\r\n      setGalleries(galleriesData);\r\n\r\n      // Select gallery based on URL params first, then localStorage, then first gallery\r\n      const params = new URLSearchParams(window.location.search);\r\n      const urlGalleryId = params.get(\"galleryId\");\r\n      const savedGalleryId = localStorage.getItem(\"mcp-view-gallery\");\r\n      let galleryToSelect = galleriesData[0];\r\n\r\n      if (urlGalleryId) {\r\n        // Prioritize URL parameter\r\n        const urlGallery = galleriesData.find(\r\n          (g) => g.id?.toString() === urlGalleryId\r\n        );\r\n        if (urlGallery) {\r\n          galleryToSelect = urlGallery;\r\n        }\r\n      } else if (savedGalleryId) {\r\n        // Fall back to localStorage\r\n        const savedGallery = galleriesData.find(\r\n          (g) => g.id?.toString() === savedGalleryId\r\n        );\r\n        if (savedGallery) {\r\n          galleryToSelect = savedGallery;\r\n        }\r\n      }\r\n\r\n      if (galleryToSelect) {\r\n        // Set gallery and workbenches, but don't trigger selection here\r\n        setSelectedGallery(galleryToSelect);\r\n        if (galleryToSelect.id) {\r\n          localStorage.setItem(\r\n            \"mcp-view-gallery\",\r\n            galleryToSelect.id.toString()\r\n          );\r\n        }\r\n\r\n        const workbenches = mcpAPI.extractMcpWorkbenches(galleryToSelect);\r\n        setMcpWorkbenches(workbenches);\r\n\r\n        // Let the effect below handle the workbench selection\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Failed to load galleries:\", error);\r\n    } finally {\r\n      setLoadingGalleries(false);\r\n    }\r\n  }, [user?.id]);\r\n\r\n  useEffect(() => {\r\n    loadGalleries();\r\n  }, [loadGalleries]);\r\n\r\n  // Handle workbench selection after galleries/workbenches are loaded\r\n  useEffect(() => {\r\n    if (mcpWorkbenches.length > 0 && selectedGallery && !loadingGalleries) {\r\n      // Check for URL parameters to restore workbench selection\r\n      const params = new URLSearchParams(window.location.search);\r\n      const urlGalleryId = params.get(\"galleryId\");\r\n      const urlWorkbenchIndex = params.get(\"workbenchIndex\");\r\n\r\n      let indexToSelect = 0; // Default to first workbench\r\n\r\n      // If URL params match current gallery, try to select the specified workbench\r\n      if (\r\n        urlGalleryId &&\r\n        urlWorkbenchIndex &&\r\n        selectedGallery.id?.toString() === urlGalleryId\r\n      ) {\r\n        const urlIndex = parseInt(urlWorkbenchIndex);\r\n        if (urlIndex >= 0 && urlIndex < mcpWorkbenches.length) {\r\n          indexToSelect = urlIndex;\r\n        }\r\n      }\r\n\r\n      // Only update if selection has changed\r\n      if (selectedWorkbenchIndex !== indexToSelect) {\r\n        setSelectedWorkbenchIndex(indexToSelect);\r\n        onSelectWorkbench(\r\n          mcpWorkbenches[indexToSelect],\r\n          selectedGallery.id,\r\n          indexToSelect\r\n        );\r\n      }\r\n    } else if (\r\n      mcpWorkbenches.length === 0 &&\r\n      selectedWorkbenchIndex !== -1 &&\r\n      !loadingGalleries\r\n    ) {\r\n      // No workbenches available, clear selection\r\n      setSelectedWorkbenchIndex(-1);\r\n      onSelectWorkbench(null);\r\n    }\r\n  }, [mcpWorkbenches, selectedGallery, onSelectWorkbench, loadingGalleries]); // Add loadingGalleries to prevent premature execution\r\n\r\n  // Render collapsed state\r\n  if (!isOpen) {\r\n    return (\r\n      <div className=\"h-full border-r border-secondary\">\r\n        <div className=\"p-2 -ml-2\">\r\n          <Tooltip title={`MCP Workbenches (${mcpWorkbenches.length})`}>\r\n            <button\r\n              onClick={onToggle}\r\n              className=\"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50\"\r\n            >\r\n              <PanelLeftOpen strokeWidth={1.5} className=\"h-6 w-6\" />\r\n            </button>\r\n          </Tooltip>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"h-full border-r border-secondary bg-primary\">\r\n      {/* Header */}\r\n      <div className=\"flex items-center justify-between pt-0 p-4 pl-2 pr-2 border-b border-secondary\">\r\n        <div className=\"flex sticky items-center gap-2\">\r\n          <span className=\"text-primary font-medium\">MCP Playground</span>\r\n          <span className=\"px-2 py-0.5 text-xs bg-accent/10 text-accent rounded\">\r\n            {mcpWorkbenches.length}\r\n          </span>\r\n        </div>\r\n        <Tooltip title=\"Close Sidebar\">\r\n          <button\r\n            onClick={onToggle}\r\n            className=\"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50\"\r\n          >\r\n            <PanelLeftClose strokeWidth={1.5} className=\"h-6 w-6\" />\r\n          </button>\r\n        </Tooltip>\r\n      </div>\r\n\r\n      {/* Gallery Selection */}\r\n      <div className=\"p-4 pl-2 border-b border-secondary\">\r\n        <div className=\"flex items-center justify-between mb-2\">\r\n          <span className=\"text-sm text-secondary\">Gallery</span>\r\n          <Tooltip title=\"Refresh galleries\">\r\n            <Button\r\n              size=\"small\"\r\n              icon={\r\n                loadingGalleries ? (\r\n                  <RefreshCw className=\"w-3 h-3 animate-spin\" />\r\n                ) : (\r\n                  <RefreshCw className=\"w-3 h-3\" />\r\n                )\r\n              }\r\n              className=\"border-0 hover:bg-secondary\"\r\n              onClick={loadGalleries}\r\n              disabled={loadingGalleries}\r\n            />\r\n          </Tooltip>\r\n        </div>\r\n        <Select\r\n          className=\"w-full\"\r\n          placeholder=\"Select a gallery\"\r\n          value={selectedGallery?.id}\r\n          onChange={(galleryId) => {\r\n            const gallery = galleries.find((g) => g.id === galleryId);\r\n            if (gallery) handleGallerySelect(gallery);\r\n          }}\r\n          loading={loadingGalleries}\r\n        >\r\n          {galleries.map((gallery) => (\r\n            <Option key={gallery.id} value={gallery.id}>\r\n              <div className=\"flex items-center gap-2\">\r\n                <Package className=\"w-3 h-3\" />\r\n                <span>{gallery.config.name}</span>\r\n                {gallery.config.url && (\r\n                  <Globe className=\"w-3 h-3 text-secondary\" />\r\n                )}\r\n              </div>\r\n            </Option>\r\n          ))}\r\n        </Select>\r\n      </div>\r\n\r\n      {/* Section Label */}\r\n      <div className=\"py-2 flex text-sm text-secondary px-4\">\r\n        <div className=\"flex\">\r\n          MCP Workbenches\r\n          {isLoading && <RefreshCw className=\"w-4 h-4 ml-2 animate-spin\" />}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Workbenches List */}\r\n      {!selectedGallery && (\r\n        <div className=\"p-2 mr-2 text-center text-secondary text-sm border border-dashed rounded mx-4\">\r\n          <Info className=\"w-4 h-4 inline-block mr-1.5 -mt-0.5\" />\r\n          Select a gallery to view MCP workbenches\r\n        </div>\r\n      )}\r\n\r\n      {selectedGallery && mcpWorkbenches.length === 0 && (\r\n        <div className=\"p-2 mr-2 text-center text-secondary text-sm border border-dashed rounded ml-2\">\r\n          <Info className=\"w-4 h-4 inline-block mr-1.5 -mt-0.5\" />\r\n          No MCP workbenches found in this gallery\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"scroll overflow-y-auto h-[calc(100%-230px)]\">\r\n        {mcpWorkbenches.map((workbench, index) => {\r\n          const workbenchKey = getWorkbenchKey(workbench, index);\r\n          const isSelected = index === selectedWorkbenchIndex;\r\n\r\n          return (\r\n            <div key={workbenchKey} className=\"relative border-secondary\">\r\n              <div\r\n                className={`absolute top-1 left-0.5 z-50 h-[calc(100%-8px)] w-1 bg-opacity-80 rounded ${\r\n                  isSelected ? \"bg-accent\" : \"bg-tertiary\"\r\n                }`}\r\n              />\r\n              <div\r\n                className={`group ml-1 flex flex-col p-3 rounded-l cursor-pointer hover:bg-secondary ${\r\n                  isSelected\r\n                    ? \"border-accent bg-secondary\"\r\n                    : \"border-transparent\"\r\n                }`}\r\n                onClick={() => {\r\n                  setSelectedWorkbenchIndex(index);\r\n                  onSelectWorkbench(workbench, selectedGallery?.id, index);\r\n                }}\r\n              >\r\n                {/* Workbench Name and Actions Row */}\r\n                <div className=\"flex items-center justify-between min-w-0\">\r\n                  <div className=\"flex items-center gap-2 min-w-0 flex-1\">\r\n                    <Icon\r\n                      icon=\"mcp\"\r\n                      size={4}\r\n                      className=\"w-4 h-4 text-accent flex-shrink-0\"\r\n                    />\r\n                    <div className=\"truncate flex-1 text-sm\">\r\n                      <span className=\"font-medium\">{workbench.label}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Workbench Details */}\r\n                <div className=\"mt-1 text-sm text-secondary\">\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <span className=\"truncate text-xs\">\r\n                      {workbench.config.server_params?.type?.replace(\r\n                        \"ServerParams\",\r\n                        \"\"\r\n                      ) || \"Unknown Type\"}\r\n                    </span>\r\n                  </div>\r\n                  {/* {workbench.description && (\r\n                    <div className=\"mt-1 text-xs text-secondary truncate\">\r\n                      {workbench.description}\r\n                    </div>\r\n                  )} */}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          );\r\n        })}\r\n      </div>\r\n\r\n      {/* Footer */}\r\n      {selectedGallery && (\r\n        <div className=\"p-3 border-t border-secondary text-sm text-secondary\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <span className=\"truncate flex-1\">\r\n              {selectedGallery.config.name}\r\n            </span>\r\n            <span className=\"ml-2 flex-shrink-0\">\r\n              {mcpWorkbenches.length} MCP workbench\r\n              {mcpWorkbenches.length !== 1 ? \"es\" : \"\"}\r\n            </span>\r\n          </div>\r\n          {selectedGallery.updated_at && (\r\n            <div className=\"text-xs text-tertiary mt-1\">\r\n              Updated {getRelativeTimeString(selectedGallery.updated_at)}\r\n            </div>\r\n          )}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default McpSidebar;\r\n", "import React from \"react\";\r\nimport { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ge, Descriptions } from \"antd\";\r\nimport {\r\n  Server,\r\n  Play,\r\n  Settings,\r\n  CheckCircle,\r\n  XCircle,\r\n  Info,\r\n} from \"lucide-react\";\r\nimport type { Component, McpWorkbenchConfig } from \"../../types/datamodel\";\r\nimport { WorkbenchFields } from \"../teambuilder/builder/component-editor/fields/workbench\";\r\n\r\ninterface McpDetailProps {\r\n  workbench: Component<McpWorkbenchConfig>;\r\n  onTestConnection: () => void;\r\n}\r\n\r\nconst McpDetail: React.FC<McpDetailProps> = ({\r\n  workbench,\r\n  onTestConnection,\r\n}) => {\r\n  const serverParams = workbench.config.server_params;\r\n  const serverType =\r\n    serverParams?.type?.replace(\"ServerParams\", \"\") || \"Unknown\";\r\n\r\n  return (\r\n    <div className=\"  \">\r\n      <WorkbenchFields\r\n        component={workbench}\r\n        defaultPanelKey={[\"testing\"]}\r\n        readonly\r\n        onChange={() => {\r\n          // In the playground, we don't allow editing - this is read-only\r\n          // The user would need to go to the Team Builder to make changes\r\n        }}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default McpDetail;\r\n", "import React, { useCallback, useEffect, useState, useContext } from \"react\";\r\nimport { message } from \"antd\";\r\nimport { ChevronRight } from \"lucide-react\";\r\nimport { appContext } from \"../../../hooks/provider\";\r\nimport McpSidebar from \"./sidebar\";\r\nimport McpDetail from \"./detail\";\r\nimport { mcpAPI } from \"./api\";\r\nimport { galleryAPI } from \"../gallery/api\";\r\nimport type {\r\n  Gallery,\r\n  Component,\r\n  McpWorkbenchConfig,\r\n} from \"../../types/datamodel\";\r\nimport { ExclamationTriangleIcon } from \"@heroicons/react/24/outline\";\r\n\r\nconst McpManager: React.FC = () => {\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [currentWorkbench, setCurrentWorkbench] =\r\n    useState<Component<McpWorkbenchConfig> | null>(null);\r\n  const [isSidebarOpen, setIsSidebarOpen] = useState(() => {\r\n    if (typeof window !== \"undefined\") {\r\n      const stored = localStorage.getItem(\"mcpSidebar\");\r\n      return stored !== null ? JSON.parse(stored) : true;\r\n    }\r\n    return true;\r\n  });\r\n\r\n  const { user } = useContext(appContext);\r\n  const [messageApi, contextHolder] = message.useMessage();\r\n\r\n  // Persist sidebar state\r\n  useEffect(() => {\r\n    if (typeof window !== \"undefined\") {\r\n      localStorage.setItem(\"mcpSidebar\", JSON.stringify(isSidebarOpen));\r\n    }\r\n  }, [isSidebarOpen]);\r\n\r\n  // Handle initial URL params\r\n  useEffect(() => {\r\n    const params = new URLSearchParams(window.location.search);\r\n    const galleryId = params.get(\"galleryId\");\r\n    const workbenchIndex = params.get(\"workbenchIndex\");\r\n\r\n    if (galleryId && workbenchIndex && !currentWorkbench) {\r\n      // Pass URL params to sidebar to handle initial selection\r\n      // The sidebar will handle the actual workbench loading based on these params\r\n    }\r\n  }, [currentWorkbench]);\r\n\r\n  // Handle browser back/forward\r\n  useEffect(() => {\r\n    const handleLocationChange = () => {\r\n      const params = new URLSearchParams(window.location.search);\r\n      const galleryId = params.get(\"galleryId\");\r\n      const workbenchIndex = params.get(\"workbenchIndex\");\r\n\r\n      if (!galleryId || !workbenchIndex) {\r\n        // No URL params, clear current workbench\r\n        if (currentWorkbench) {\r\n          setCurrentWorkbench(null);\r\n        }\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"popstate\", handleLocationChange);\r\n    return () => window.removeEventListener(\"popstate\", handleLocationChange);\r\n  }, [currentWorkbench]);\r\n\r\n  const handleSelectWorkbench = (\r\n    workbench: Component<McpWorkbenchConfig> | null,\r\n    galleryId?: number,\r\n    workbenchIndex?: number\r\n  ) => {\r\n    setCurrentWorkbench(workbench);\r\n\r\n    // Update URL when a workbench is selected\r\n    if (workbench && galleryId !== undefined && workbenchIndex !== undefined) {\r\n      const params = new URLSearchParams(window.location.search);\r\n      params.set(\"galleryId\", galleryId.toString());\r\n      params.set(\"workbenchIndex\", workbenchIndex.toString());\r\n      window.history.pushState({}, \"\", `?${params.toString()}`);\r\n    } else if (!workbench) {\r\n      // Clear URL params when no workbench is selected\r\n      window.history.pushState({}, \"\", window.location.pathname);\r\n    }\r\n  };\r\n\r\n  const handleGalleryUpdate = async (updatedGallery: Gallery) => {\r\n    if (!user?.id || !updatedGallery.id) return;\r\n\r\n    try {\r\n      setIsLoading(true);\r\n      // Sanitize the gallery data by removing timestamps that shouldn't be updated\r\n      const sanitizedUpdates = {\r\n        ...updatedGallery,\r\n        created_at: undefined,\r\n        updated_at: undefined,\r\n      };\r\n      await galleryAPI.updateGallery(\r\n        updatedGallery.id,\r\n        sanitizedUpdates,\r\n        user.id\r\n      );\r\n      messageApi.success(\"Gallery updated successfully\");\r\n    } catch (error) {\r\n      console.error(\"Failed to update gallery:\", error);\r\n      messageApi.error(\"Failed to update gallery\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleTestConnection = async (\r\n    workbench: Component<McpWorkbenchConfig>\r\n  ) => {\r\n    try {\r\n      setIsLoading(true);\r\n      const isConnected = await mcpAPI.testMcpConnection(workbench);\r\n\r\n      if (isConnected) {\r\n        messageApi.success(\"Connection test successful\");\r\n      } else {\r\n        messageApi.error(\"Connection test failed\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Connection test failed:\", error);\r\n      messageApi.error(\"Connection test failed\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  if (!user?.id) {\r\n    return (\r\n      <div className=\"flex items-center justify-center h-[calc(100vh-120px)] text-secondary\">\r\n        Please log in to use the MCP Playground\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"relative flex h-full w-full\">\r\n      {contextHolder}\r\n\r\n      {/* Sidebar */}\r\n      <div\r\n        className={`absolute left-0 top-0 h-full transition-all duration-200 ease-in-out ${\r\n          isSidebarOpen ? \"w-64\" : \"w-12\"\r\n        }`}\r\n      >\r\n        <McpSidebar\r\n          isOpen={isSidebarOpen}\r\n          onToggle={() => setIsSidebarOpen(!isSidebarOpen)}\r\n          onSelectWorkbench={handleSelectWorkbench}\r\n          isLoading={isLoading}\r\n          currentWorkbench={currentWorkbench}\r\n          onGalleryUpdate={handleGalleryUpdate}\r\n        />\r\n      </div>\r\n\r\n      {/* Main Content */}\r\n      <div\r\n        className={`flex-1 transition-all -mr-6 duration-200 ${\r\n          isSidebarOpen ? \"ml-64\" : \"ml-12\"\r\n        }`}\r\n      >\r\n        <div className=\"p-4 pt-2  h-[calc(100%-60px)]\">\r\n          <div className=\"text-xs text-secondary mb-4 border border-dashed rounded-md p-2 \">\r\n            <ExclamationTriangleIcon className=\"w-4 h-4 inline-block mr-1 text-warning text-orange-500\" />{\" \"}\r\n            MCP Playground is an experimental view for testing MCP Servers in\r\n            your Gallery{\" \"}\r\n          </div>\r\n          {/* Breadcrumb */}\r\n          <div className=\"flex items-center gap-2 mb-4 text-sm\">\r\n            <span className=\"text-primary font-medium\">MCP Playground</span>\r\n            {currentWorkbench && (\r\n              <>\r\n                <ChevronRight className=\"w-4 h-4 text-secondary\" />\r\n                <span className=\"text-secondary\">{currentWorkbench.label}</span>\r\n              </>\r\n            )}\r\n          </div>\r\n\r\n          {/* Content Area */}\r\n          {isLoading && !currentWorkbench ? (\r\n            <div className=\"flex items-center justify-center h-[calc(100vh-120px)] text-secondary\">\r\n              Loading...\r\n            </div>\r\n          ) : currentWorkbench ? (\r\n            <div className=\" h-[calc(100vh-235px)]    scroll overflow-auto\">\r\n              {\" \"}\r\n              <McpDetail\r\n                workbench={currentWorkbench}\r\n                onTestConnection={() => handleTestConnection(currentWorkbench)}\r\n              />\r\n            </div>\r\n          ) : (\r\n            <div className=\"flex items-center justify-center h-[calc(100vh-120px)] text-secondary\">\r\n              <div className=\"text-center\">\r\n                <h3 className=\"text-lg font-medium mb-2\">\r\n                  Welcome to MCP Playground\r\n                </h3>\r\n                <p className=\"text-secondary mb-4\">\r\n                  Select an MCP workbench from the sidebar to start testing\r\n                </p>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default McpManager;\r\n", "import * as React from \"react\";\r\nimport Layout from \"../components/layout\";\r\nimport { graphql } from \"gatsby\";\r\nimport McpManager from \"../components/views/mcp/manager\";\r\n\r\n// markup\r\nconst McpPage = ({ data }: any) => {\r\n  return (\r\n    <Layout meta={data.site.siteMetadata} title=\"MCP 游乐场\" link={\"/mcp\"}>\r\n      <main style={{ height: \"100%\" }} className=\"h-full\">\r\n        <McpManager />\r\n      </main>\r\n    </Layout>\r\n  );\r\n};\r\n\r\nexport const query = graphql`\r\n  query McpPageQuery {\r\n    site {\r\n      siteMetadata {\r\n        description\r\n        title\r\n      }\r\n    }\r\n  }\r\n`;\r\n\r\nexport default McpPage;\r\n"], "names": ["Globe", "cx", "cy", "r", "key", "d", "RefreshCw", "ExclamationTriangleIcon", "title", "titleId", "props", "svgRef", "Object", "assign", "xmlns", "fill", "viewBox", "strokeWidth", "stroke", "ref", "id", "strokeLinecap", "strokeLinejoin", "ForwardRef", "Option", "Select", "_ref", "isOpen", "onToggle", "onSelectWorkbench", "isLoading", "currentWorkbench", "onGalleryUpdate", "galleries", "setGalleries", "useState", "selectedGallery", "setSelectedGallery", "mcpWorkbenches", "setMcpWorkbenches", "selectedWorkbenchIndex", "setSelectedWorkbenchIndex", "loadingGalleries", "setLoadingGalleries", "user", "useContext", "appContext", "handleGallerySelect", "useCallback", "gallery", "localStorage", "setItem", "toString", "workbenches", "mcpAPI", "extractMcpWorkbenches", "loadGalleries", "async", "galleriesData", "listGalleries", "urlGalleryId", "URLSearchParams", "window", "location", "search", "get", "savedGalleryId", "getItem", "galleryToSelect", "urlGallery", "find", "g", "_g$id", "savedGallery", "_g$id2", "error", "console", "useEffect", "length", "_selectedGallery$id", "params", "urlWorkbenchIndex", "indexToSelect", "urlIndex", "parseInt", "React", "className", "<PERSON><PERSON><PERSON>", "onClick", "PanelLeftClose", "<PERSON><PERSON>", "size", "icon", "disabled", "placeholder", "value", "onChange", "galleryId", "loading", "map", "Package", "config", "name", "url", "Info", "workbench", "index", "_workbench$config$ser", "_workbench$config$ser2", "workbenchKey", "getWorkbenchKey", "configStr", "JSON", "stringify", "provider", "label", "slice", "isSelected", "Icon", "server_params", "type", "replace", "updated_at", "getRelativeTimeString", "PanelLeftOpen", "_serverParams$type", "onTestConnection", "serverParams", "WorkbenchFields", "component", "defaultPanelKey", "readonly", "McpManager", "setIsLoading", "setCurrentWorkbench", "isSidebarOpen", "setIsSidebarOpen", "stored", "parse", "messageApi", "contextHolder", "message", "useMessage", "handleLocationChange", "workbenchIndex", "addEventListener", "removeEventListener", "McpSidebar", "handleSelectWorkbench", "undefined", "set", "history", "pushState", "pathname", "updatedGallery", "sanitizedUpdates", "created_at", "galleryAPI", "updateGallery", "success", "ChevronRight", "McpDetail", "testMcpConnection", "handleTestConnection", "data", "Layout", "meta", "site", "siteMetadata", "link", "style", "height"], "sourceRoot": ""}