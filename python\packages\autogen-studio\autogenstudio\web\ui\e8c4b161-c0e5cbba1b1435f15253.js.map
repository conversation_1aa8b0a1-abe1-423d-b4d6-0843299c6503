{"version": 3, "file": "e8c4b161-c0e5cbba1b1435f15253.js", "mappings": "ydAUA,MAAMA,GAAe,IAAAC,eAAc,MAC7BC,EAAaF,EAAaG,SAE1BC,EAAsB,KAAwB,WAwBpD,SAASC,EAASC,EAAUC,GACxB,MAAMC,GAAQ,IAAAC,YAAWT,GACzB,GAAc,OAAVQ,EACA,MAAM,IAAIE,MAAMN,GAEpB,OAAO,OAAuBI,EAAOF,EAAUC,EACnD,CAcA,SAASI,IACL,MAAMH,GAAQ,IAAAC,YAAWT,GACzB,GAAc,OAAVQ,EACA,MAAM,IAAIE,MAAMN,GAEpB,OAAO,IAAAQ,SAAQ,KAAM,CACjBC,SAAUL,EAAMK,SAChBC,SAAUN,EAAMM,SAChBC,UAAWP,EAAMO,YACjB,CAACP,GACT,CAEA,MAAMQ,EAAQ,CAAEC,QAAS,QACnBC,EAAgB,CAClBC,SAAU,WACVC,MAAO,EACPC,OAAQ,EACRC,QAAS,EACTC,OAAQ,EACRC,QAAS,EACTC,SAAU,SACVC,KAAM,2BACNC,SAAU,eAERC,EAAqB,wBACrBC,EAAqB,wBAErBC,EAAoBC,GAAMA,EAAEC,gBAC5BC,EAA2BF,GAAMA,EAAEG,gBACzC,SAASC,GAAgB,KAAEC,IACvB,MAAMJ,EAAkB3B,EAASyB,GACjC,OAAQ,IAAAO,KAAI,MAAO,CAAEC,GAAI,yBAAwBF,IAAQ,YAAa,YAAa,cAAe,OAAQpB,MAAOE,EAAeqB,SAAUP,GAC9I,CACA,SAASQ,GAAiB,KAAEJ,EAAI,oBAAEK,IAC9B,MAAMP,EAAkB7B,EAAS4B,GACjC,OAAQ,IAAAS,MAAK,EAAAC,SAAU,CAAEJ,SAAU,EAAC,IAAAF,KAAI,MAAO,CAAEC,GAAI,GAAGV,KAAsBQ,IAAQpB,MAAOA,EAAOuB,SAAUE,EAC5FP,EAAgB,gCAChBA,EAAgB,4CAA6C,IAAAG,KAAI,MAAO,CAAEC,GAAI,GAAGT,KAAsBO,IAAQpB,MAAOA,EAAOuB,SAAUL,EAAgB,mCAAqCO,IAAuB,IAAAJ,KAAIF,EAAiB,CAAEC,KAAMA,MACtQ,CA2BA,MAAMQ,GAAQ,IAAAC,YAAW,EAAG1B,WAAW,WAAYoB,WAAUO,YAAW9B,WAAU+B,GAAQC,KACtF,MAAMC,EAAkB,GAAG9B,IAAW+B,MAAM,KAC5C,OAAQ,IAAAb,KAAI,MAAO,CAAES,WAAW,OAAG,CAAC,oBAAqBA,KAAcG,IAAmBjC,MAAOA,EAAOgC,IAAKA,KAAQD,EAAMR,SAAUA,MAIzI,SAASY,GAAY,WAAEC,EAAU,SAAEjC,EAAW,iBAC1C,OAAIiC,GAAYC,gBACL,MAEH,IAAAhB,KAAIO,EAAO,CAAEzB,SAAUA,EAAU2B,UAAW,0BAA2B,eAAgB,yGAA0GP,UAAU,IAAAF,KAAI,IAAK,CAAEiB,KAAM,wBAAyBC,OAAQ,SAAUC,IAAK,sBAAuB,aAAc,yBAA0BjB,SAAU,gBACjW,CAPAK,EAAMa,YAAc,QASpB,MAAMC,EAAc3B,IAChB,MAAM4B,EAAgB,GAChBC,EAAgB,GACtB,IAAK,MAAO,CAAEC,KAAS9B,EAAE+B,WACjBD,EAAKE,UACLJ,EAAcK,KAAKH,EAAKI,UAAUC,UAG1C,IAAK,MAAO,CAAEC,KAASpC,EAAEqC,WACjBD,EAAKJ,UACLH,EAAcI,KAAKG,GAG3B,MAAO,CAAER,gBAAeC,kBAEtBS,EAAYC,GAAQA,EAAIhC,GAC9B,SAASiC,EAASC,EAAGC,GACjB,OAAQ,OAAQD,EAAEb,cAAce,IAAIL,GAAWI,EAAEd,cAAce,IAAIL,MAC/D,OAAQG,EAAEZ,cAAcc,IAAIL,GAAWI,EAAEb,cAAcc,IAAIL,GACnE,CACA,SAASM,GAAuB,kBAAEC,IAC9B,MAAMpE,EAAQG,KACR,cAAEgD,EAAa,cAAEC,GAAkBvD,EAASqD,EAAYa,GAM9D,OALA,IAAAM,WAAU,KACN,MAAMC,EAAS,CAAEC,MAAOpB,EAAeqB,MAAOpB,GAC9CgB,IAAoBE,GACpBtE,EAAMK,WAAWoE,0BAA0BC,QAASC,GAAOA,EAAGL,KAC/D,CAACnB,EAAeC,EAAegB,IAC3B,IACX,CACA,MAAMQ,EAAkBrD,KAAQA,EAAEkD,0BAClC,SAASI,GAAkB,kBAAET,IACzB,MAAMU,EAAkCjF,EAAS+E,GACjD,OAAIR,GAAqBU,GACd,IAAAjD,KAAIsC,EAAwB,CAAEC,kBAAmBA,IAErD,IACX,CAEA,MAAMW,EAAoB,CAAC,EAAG,GACxBC,EAAkB,CAAEC,EAAG,EAAGC,EAAG,EAAGC,KAAM,GAqEtCC,EAAgB,CA5DlB,QACA,QACA,eACA,eACA,YACA,iBACA,eACA,sBACA,oBACA,iBACA,qBACA,mBACA,iBACA,iBACA,qBACA,uBACA,uBACA,UACA,UACA,aACA,gBACA,gBACA,qBACA,iBACA,WACA,aACA,kBACA,iBACA,qBACA,UACA,iBACA,gBACA,gBACA,WACA,aACA,kBACA,iBACA,kBACA,uBACA,sBACA,cACA,SACA,YACA,iBACA,aACA,mBACA,oBACA,UACA,mBACA,oBACA,oBACA,oBACA,0BACA,iBACA,QACA,eACA,oBACA,kBAG8C,QAC5CC,EAAc9D,IAAM,CACtB+D,SAAU/D,EAAE+D,SACZC,SAAUhE,EAAEgE,SACZC,WAAYjE,EAAEiE,WACdC,WAAYlE,EAAEkE,WACdC,mBAAoBnE,EAAEmE,mBACtBC,cAAepE,EAAEoE,cACjBC,MAAOrE,EAAEqE,MACTC,wBAAyBtE,EAAEsE,wBAC3BC,qBAAsBvE,EAAEuE,uBAEtBC,EAAiB,CAMnBC,gBAAiB,KACjBC,WAAYlB,EACZmB,QAAS,GACTC,QAAS,EACTC,oBAAoB,EACpBC,eAAgB,QAChBzE,KAAM,IACN0E,kBAAmB,GAEvB,SAASC,EAAaC,GAClB,MAAM,SAAElB,EAAQ,SAAEC,EAAQ,WAAEC,EAAU,WAAEC,EAAU,mBAAEC,EAAkB,cAAEC,EAAa,MAAEC,EAAK,wBAAEC,EAAuB,qBAAEC,GAA0BjG,EAASwF,EAAY,KAChKrF,EAAQG,KACd,IAAAkE,WAAU,KACNwB,EAAwBW,EAAMC,aAAcD,EAAME,cAC3C,KAEHC,EAAeC,QAAUb,EACzBH,MAEL,IACH,MAAMe,GAAiB,IAAAE,QAAOd,GAuC9B,OAtCA,IAAA1B,WAAU,KACN,IAAK,MAAMyC,KAAa1B,EAAe,CACnC,MAAM2B,EAAaP,EAAMM,GAErBC,IADuBJ,EAAeC,QAAQE,UAGlB,IAArBN,EAAMM,KAGC,UAAdA,EACAxB,EAASyB,GACU,UAAdD,EACLvB,EAASwB,GACU,YAAdD,EACLtB,EAAWuB,GACQ,YAAdD,EACLrB,EAAWsB,GACQ,oBAAdD,EACLpB,EAAmBqB,GACA,eAAdD,EACLnB,EAAcoB,GACK,sBAAdD,EACLhB,EAAqBiB,GACF,oBAAdD,EACL9G,EAAMM,SAAS,CAAEoB,iBAAiB,QAAqBqF,KAEpC,YAAdD,EACL9G,EAAMM,SAAS,CAAE0G,cAAeD,IACb,mBAAdD,EACL9G,EAAMM,SAAS,CAAE2G,eAAgBF,IAGjC/G,EAAMM,SAAS,CAAE,CAACwG,GAAYC,KACtC,CACAJ,EAAeC,QAAUJ,GAG7BpB,EAAclB,IAAK4C,GAAcN,EAAMM,KAChC,IACX,CAEA,SAASI,IACL,MAAsB,oBAAXC,QAA2BA,OAAOC,WAGtCD,OAAOC,WAAW,gCAFd,IAGf,CAyBA,MAAMC,EAAiC,oBAAbC,SAA2BA,SAAW,KAyBhE,SAASC,EAaTC,EAAU,KAAMC,EAAU,CAAE1E,OAAQsE,EAAYK,4BAA4B,IACxE,MAAOC,EAAYC,IAAiB,IAAAC,WAAS,GAEvCC,GAAkB,IAAAjB,SAAO,GAEzBkB,GAAc,IAAAlB,QAAO,IAAImB,IAAI,MAS5BC,EAAUC,IAAe,IAAA9H,SAAQ,KACpC,GAAgB,OAAZoH,EAAkB,CAClB,MACMW,GADaC,MAAMC,QAAQb,GAAWA,EAAU,CAACA,IAElDc,OAAQC,GAAqB,iBAAPA,GAMtBrE,IAAKqE,GAAOA,EAAGC,QAAQ,IAAK,MAAMA,QAAQ,OAAQ,OAAO9F,MAAM,OAC9D+F,EAAWN,EAAKO,OAAO,CAACC,EAAKC,IAASD,EAAIE,UAAUD,GAAO,IACjE,MAAO,CAACT,EAAMM,EAClB,CACA,MAAO,CAAC,GAAI,KACb,CAACjB,IAsDJ,OArDA,IAAAnD,WAAU,KACN,MAAMtB,EAAS0E,GAAS1E,QAAUsE,EAC5BK,EAA6BD,GAASC,6BAA8B,EAC1E,GAAgB,OAAZF,EAAkB,CAClB,MAAMsB,EAAeC,IACjBjB,EAAgBlB,QAAUmC,EAAMC,SAAWD,EAAME,SAAWF,EAAMG,UAAYH,EAAMI,OAGpF,KAFwBrB,EAAgBlB,SAAYkB,EAAgBlB,UAAYc,KAC5E,QAAeqB,GAEf,OAAO,EAEX,MAAMK,EAAYC,EAAaN,EAAMO,KAAMpB,GAE3C,GADAH,EAAYnB,QAAQ2C,IAAIR,EAAMK,IAC1BI,EAAcvB,EAAUF,EAAYnB,SAAS,GAAQ,CACrD,MAAM7D,EAAUgG,EAAMU,mBAAmB,IAAMV,EAAMhG,OAC/C2G,EAA4C,WAArB3G,GAAQ4G,UAA8C,MAArB5G,GAAQ4G,UACvC,IAA3BlC,EAAQmC,iBAA6B9B,EAAgBlB,SAAY8C,GACjEX,EAAMa,iBAEVhC,GAAc,EAClB,GAEEiC,EAAad,IACf,MAAMK,EAAYC,EAAaN,EAAMO,KAAMpB,GACvCsB,EAAcvB,EAAUF,EAAYnB,SAAS,IAC7CgB,GAAc,GACdG,EAAYnB,QAAQkD,SAGpB/B,EAAYnB,QAAQmD,OAAOhB,EAAMK,IAGnB,SAAdL,EAAMiB,KACNjC,EAAYnB,QAAQkD,QAExBhC,EAAgBlB,SAAU,GAExBqD,EAAe,KACjBlC,EAAYnB,QAAQkD,QACpBlC,GAAc,IAMlB,OAJA7E,GAAQmH,iBAAiB,UAAWpB,GACpC/F,GAAQmH,iBAAiB,QAASL,GAClC1C,OAAO+C,iBAAiB,OAAQD,GAChC9C,OAAO+C,iBAAiB,cAAeD,GAChC,KACHlH,GAAQoH,oBAAoB,UAAWrB,GACvC/F,GAAQoH,oBAAoB,QAASN,GACrC1C,OAAOgD,oBAAoB,OAAQF,GACnC9C,OAAOgD,oBAAoB,cAAeF,GAElD,GACD,CAACzC,EAASI,IACND,CACX,CAEA,SAAS6B,EAAcvB,EAAUF,EAAaqC,GAC1C,OAAQnC,EAMHK,OAAQH,GAASiC,GAAQjC,EAAKkC,SAAWtC,EAAYuC,MAKrDC,KAAMpC,GAASA,EAAKqC,MAAOC,GAAM1C,EAAY2C,IAAID,IAC1D,CACA,SAASpB,EAAasB,EAAWzC,GAC7B,OAAOA,EAAY0C,SAASD,GAAa,OAAS,KACtD,CAQA,MAAME,EAAoB,KACtB,MAAM7K,EAAQG,IACd,OAAO,IAAAC,SAAQ,KACJ,CACH0K,OAASrD,IACL,MAAM,QAAEsD,GAAY/K,EAAMK,WAC1B,OAAO0K,EAAUA,EAAQC,QAAQ,IAAK,CAAEC,SAAUxD,GAASwD,WAAcC,QAAQC,SAAQ,IAE7FC,QAAU3D,IACN,MAAM,QAAEsD,GAAY/K,EAAMK,WAC1B,OAAO0K,EAAUA,EAAQC,QAAQ,EAAI,IAAK,CAAEC,SAAUxD,GAASwD,WAAcC,QAAQC,SAAQ,IAEjGE,OAAQ,CAACC,EAAW7D,KAChB,MAAM,QAAEsD,GAAY/K,EAAMK,WAC1B,OAAO0K,EAAUA,EAAQQ,QAAQD,EAAW,CAAEL,SAAUxD,GAASwD,WAAcC,QAAQC,SAAQ,IAEnGK,QAAS,IAAMxL,EAAMK,WAAWoL,UAAU,GAC1CC,YAAaC,MAAOC,EAAUnE,KAC1B,MAAQgE,WAAYI,EAAIC,EAAIC,GAAM,QAAEhB,GAAa/K,EAAMK,WACvD,OAAK0K,SAGCA,EAAQW,YAAY,CACtBzG,EAAG2G,EAAS3G,GAAK4G,EACjB3G,EAAG0G,EAAS1G,GAAK4G,EACjB3G,KAAMyG,EAASzG,MAAQ4G,GACxBtE,GACIyD,QAAQC,SAAQ,IAPZD,QAAQC,SAAQ,IAS/Ba,YAAa,KACT,MAAO/G,EAAGC,EAAGC,GAAQnF,EAAMK,WAAWoL,UACtC,MAAO,CAAExG,IAAGC,IAAGC,SAEnB8G,UAAWN,MAAO1G,EAAGC,EAAGuC,IACbzH,EAAMK,WAAW4L,UAAUhH,EAAGC,EAAGuC,GAE5CyE,UAAWP,MAAOQ,EAAQ1E,KACtB,MAAM,MAAE7G,EAAK,OAAEC,EAAM,QAAEqF,EAAO,QAAEC,EAAO,QAAE4E,GAAY/K,EAAMK,WACrDuL,GAAW,QAAqBO,EAAQvL,EAAOC,EAAQqF,EAASC,EAASsB,GAASzG,SAAW,IACnG,OAAK+J,SAGCA,EAAQW,YAAYE,EAAU,CAChCX,SAAUxD,GAASwD,SACnBmB,KAAM3E,GAAS2E,KACfC,YAAa5E,GAAS4E,cAEnBnB,QAAQC,SAAQ,IAPZD,QAAQC,SAAQ,IAS/BmB,qBAAsB,CAACC,EAAgB9E,EAAU,CAAC,KAC9C,MAAM,UAAEgE,EAAS,SAAEe,EAAQ,WAAEC,EAAU,QAAEC,GAAY1M,EAAMK,WAC3D,IAAKqM,EACD,OAAOH,EAEX,MAAQtH,EAAG0H,EAAMzH,EAAG0H,GAASF,EAAQG,wBAC/BC,EAAoB,CACtB7H,EAAGsH,EAAetH,EAAI0H,EACtBzH,EAAGqH,EAAerH,EAAI0H,GAEpBG,EAAYtF,EAAQ+E,UAAYA,EAChCQ,EAAcvF,EAAQgF,YAAcA,EAC1C,OAAO,QAAqBK,EAAmBrB,EAAWuB,EAAaD,IAE3EE,qBAAuBC,IACnB,MAAM,UAAEzB,EAAS,QAAEiB,GAAY1M,EAAMK,WACrC,IAAKqM,EACD,OAAOQ,EAEX,MAAQjI,EAAG0H,EAAMzH,EAAG0H,GAASF,EAAQG,wBAC/BM,GAAmB,QAAqBD,EAAczB,GAC5D,MAAO,CACHxG,EAAGkI,EAAiBlI,EAAI0H,EACxBzH,EAAGiI,EAAiBjI,EAAI0H,MAIrC,KAQP,SAASQ,EAAaC,EAASC,GAC3B,MAAMC,EAAkB,GAKlBC,EAAa,IAAIC,IACjBC,EAAiB,GACvB,IAAK,MAAMC,KAAUN,EACjB,GAAoB,QAAhBM,EAAOC,KAIN,GAAoB,WAAhBD,EAAOC,MAAqC,YAAhBD,EAAOC,KAKxCJ,EAAWK,IAAIF,EAAO7L,GAAI,CAAC6L,QAE1B,CACD,MAAMG,EAAiBN,EAAWO,IAAIJ,EAAO7L,IACzCgM,EAKAA,EAAetK,KAAKmK,GAGpBH,EAAWK,IAAIF,EAAO7L,GAAI,CAAC6L,GAEnC,MAtBID,EAAelK,KAAKmK,GAwB5B,IAAK,MAAMK,KAAWV,EAAU,CAC5B,MAAMD,EAAUG,EAAWO,IAAIC,EAAQlM,IAKvC,IAAKuL,EAAS,CACVE,EAAgB/J,KAAKwK,GACrB,QACJ,CAEA,GAAwB,WAApBX,EAAQ,GAAGO,KACX,SAEJ,GAAwB,YAApBP,EAAQ,GAAGO,KAAoB,CAC/BL,EAAgB/J,KAAK,IAAK6J,EAAQ,GAAGzE,OACrC,QACJ,CAMA,MAAMqF,EAAiB,IAAKD,GAC5B,IAAK,MAAML,KAAUN,EACjBa,EAAYP,EAAQM,GAExBV,EAAgB/J,KAAKyK,EACzB,CAeA,OAVIP,EAAerD,QACfqD,EAAehJ,QAASiJ,SACCQ,IAAjBR,EAAOS,MACPb,EAAgBc,OAAOV,EAAOS,MAAO,EAAG,IAAKT,EAAO/E,OAGpD2E,EAAgB/J,KAAK,IAAKmK,EAAO/E,SAItC2E,CACX,CAEA,SAASW,EAAYP,EAAQK,GACzB,OAAQL,EAAOC,MACX,IAAK,SACDI,EAAQzK,SAAWoK,EAAOpK,SAC1B,MAEJ,IAAK,gBAC8B,IAApBoK,EAAOhN,WACdqN,EAAQrN,SAAWgN,EAAOhN,eAEC,IAApBgN,EAAOW,WACdN,EAAQM,SAAWX,EAAOW,UAE9B,MAEJ,IAAK,kBACgC,IAAtBX,EAAOY,aACdP,EAAQQ,WAAa,CAAC,EACtBR,EAAQQ,SAAS5N,MAAQ+M,EAAOY,WAAW3N,MAC3CoN,EAAQQ,SAAS3N,OAAS8M,EAAOY,WAAW1N,OACxC8M,EAAOc,iBACsB,IAAzBd,EAAOc,eAAmD,UAAzBd,EAAOc,gBACxCT,EAAQpN,MAAQ+M,EAAOY,WAAW3N,QAET,IAAzB+M,EAAOc,eAAmD,WAAzBd,EAAOc,gBACxCT,EAAQnN,OAAS8M,EAAOY,WAAW1N,UAIhB,kBAApB8M,EAAOe,WACdV,EAAQU,SAAWf,EAAOe,UAK1C,CAgCA,SAASC,EAAiBtB,EAAS9I,GAC/B,OAAO6I,EAAaC,EAAS9I,EACjC,CAgCA,SAASqK,EAAiBvB,EAAS7I,GAC/B,OAAO4I,EAAaC,EAAS7I,EACjC,CACA,SAASqK,EAAsB/M,EAAIyB,GAC/B,MAAO,CACHzB,KACA8L,KAAM,SACNrK,WAER,CACA,SAASuL,EAAoBC,EAAOC,EAAc,IAAIhH,IAAOiH,GAAa,GACtE,MAAM5B,EAAU,GAChB,IAAK,MAAOvL,EAAI8G,KAASmG,EAAO,CAC5B,MAAMG,EAAiBF,EAAYtE,IAAI5I,QAEfqM,IAAlBvF,EAAKrF,WAA2B2L,GAAmBtG,EAAKrF,WAAa2L,IACnED,IAMArG,EAAKrF,SAAW2L,GAEpB7B,EAAQ7J,KAAKqL,EAAsBjG,EAAK9G,GAAIoN,IAEpD,CACA,OAAO7B,CACX,CACA,SAAS8B,GAAuB,MAAEJ,EAAQ,GAAE,OAAEK,IAC1C,MAAM/B,EAAU,GACVgC,EAAc,IAAI5B,IAAIsB,EAAM7K,IAAK0E,GAAS,CAACA,EAAK9G,GAAI8G,KAC1D,IAAK,MAAOwF,EAAOxF,KAASmG,EAAMO,UAAW,CACzC,MAAMC,EAAaH,EAAOrB,IAAInF,EAAK9G,IAC7B0N,EAAYD,GAAY9L,WAAWC,UAAY6L,OACnCpB,IAAdqB,GAA2BA,IAAc5G,GACzCyE,EAAQ7J,KAAK,CAAE1B,GAAI8G,EAAK9G,GAAI8G,KAAMA,EAAMgF,KAAM,iBAEhCO,IAAdqB,GACAnC,EAAQ7J,KAAK,CAAEoF,KAAMA,EAAMgF,KAAM,MAAOQ,SAEhD,CACA,IAAK,MAAOtM,KAAOsN,EAAQ,MAENjB,IADAkB,EAAYtB,IAAIjM,IAE7BuL,EAAQ7J,KAAK,CAAE1B,KAAI8L,KAAM,UAEjC,CACA,OAAOP,CACX,CACA,SAASoC,EAAsB7G,GAC3B,MAAO,CACH9G,GAAI8G,EAAK9G,GACT8L,KAAM,SAEd,CAuBA,MAAM8B,EAAU1B,IAAY,QAAWA,GAsBjC2B,EAAU3B,IAAY,QAAWA,GAEvC,SAAS4B,EAAgBC,GAErB,OAAO,IAAAxN,YAAWwN,EACtB,CAGA,MAAMC,GAA8C,oBAAX3I,OAAyB,EAAA4I,gBAAkB,EAAA1L,UAUpF,SAAS2L,GAASC,GAQd,MAAOC,EAAQC,IAAa,IAAAtI,UAASuI,OAAO,KAMrCC,IAAS,IAAAxI,UAAS,IAe7B,SAAqByI,GACjB,IAAID,EAAQ,GACZ,MAAO,CACHtC,IAAK,IAAMsC,EACXzK,MAAO,KACHyK,EAAQ,IAEZ7M,KAAOoF,IACHyH,EAAM7M,KAAKoF,GACX0H,KAGZ,CA3BmCC,CAAY,IAAMJ,EAAUK,GAAKA,EAAIJ,OAAO,MAa3E,OAPAN,GAA0B,KACtB,MAAMW,EAAaJ,EAAMtC,MACrB0C,EAAWpG,SACX4F,EAASQ,GACTJ,EAAMzK,UAEX,CAACsK,IACGG,CACX,CAeA,MAAMK,IAAe,IAAAjR,eAAc,MAOnC,SAASkR,IAAc,SAAE5O,IACrB,MAAM/B,EAAQG,IAkCRyQ,EAAYZ,IAjCO,IAAAa,aAAaJ,IAClC,MAAM,MAAElM,EAAQ,GAAE,SAAEe,EAAQ,gBAAEwL,EAAe,cAAEC,EAAa,WAAEzN,EAAU,cAAE0D,GAAkBhH,EAAMK,WAMlG,IAAI2Q,EAAOzM,EACX,IAAK,MAAM0M,KAAWR,EAClBO,EAA0B,mBAAZC,EAAyBA,EAAQD,GAAQC,EAE3D,MAAM5D,EAAU8B,EAAuB,CACnCJ,MAAOiC,EACP5B,OAAQ9L,IAERwN,GACAxL,EAAS0L,GAGT3D,EAAQhD,OAAS,EACjB0G,IAAgB1D,GAEXrG,GAGLG,OAAO+J,sBAAsB,KACzB,MAAM,cAAElK,EAAa,MAAEzC,EAAK,SAAEe,GAAatF,EAAMK,WAC7C2G,GACA1B,EAASf,MAItB,KAkBG4M,EAAYnB,IAhBO,IAAAa,aAAaJ,IAClC,MAAM,MAAEjM,EAAQ,GAAE,SAAEe,EAAQ,gBAAE6L,EAAe,cAAEC,EAAa,WAAEzN,GAAe5D,EAAMK,WACnF,IAAI2Q,EAAOxM,EACX,IAAK,MAAMyM,KAAWR,EAClBO,EAA0B,mBAAZC,EAAyBA,EAAQD,GAAQC,EAEvDG,EACA7L,EAASyL,GAEJK,GACLA,EAAclC,EAAuB,CACjCJ,MAAOiC,EACP5B,OAAQxL,MAGjB,KAEG0N,GAAQ,IAAAlR,SAAQ,KAAM,CAAGwQ,YAAWO,cAAc,IACxD,OAAO,IAAAtP,KAAI6O,GAAa/Q,SAAU,CAAE2R,MAAOA,EAAOvP,SAAUA,GAChE,CASA,MAAMwP,GAAchQ,KAAQA,EAAEwJ,QA4B9B,SAASyG,KACL,MAAMC,EAAiB5G,IACjB7K,EAAQG,IACRuR,EAvCV,WACI,MAAMA,GAAe,IAAAzR,YAAWyQ,IAChC,IAAKgB,EACD,MAAM,IAAIxR,MAAM,uDAEpB,OAAOwR,CACX,CAiCyBC,GACfC,EAAsB/R,EAAS0R,IAC/BM,GAAgB,IAAAzR,SAAQ,KAC1B,MAAM0R,EAAmBhQ,GAAO9B,EAAMK,WAAWiD,WAAWyK,IAAIjM,GAC1DwD,EAAY2L,IACdS,EAAad,UAAUpN,KAAKyN,IAE1B1L,EAAY0L,IACdS,EAAaP,UAAU3N,KAAKyN,IAE1Bc,EAAe1O,IACjB,MAAM,WAAEC,EAAU,WAAE2C,GAAejG,EAAMK,WACnC2R,EAAYtC,EAAOrM,GAAQA,EAAOC,EAAWyK,IAAI1K,EAAKvB,IACtDnB,EAAWqR,EAAUC,UACrB,QAAyBD,EAAUrR,SAAUqR,EAAUxD,SAAUwD,EAAUC,SAAU3O,EAAY2C,GACjG+L,EAAUrR,SACVuR,EAAmB,IAClBF,EACHrR,WACAC,MAAOoR,EAAUxD,UAAU5N,OAASoR,EAAUpR,MAC9CC,OAAQmR,EAAUxD,UAAU3N,QAAUmR,EAAUnR,QAEpD,OAAO,QAAWqR,IAEhBC,EAAa,CAACrQ,EAAIsQ,EAAY3K,EAAU,CAAEe,SAAS,MACrDlD,EAAU+M,GAAcA,EAAUnO,IAAKb,IACnC,GAAIA,EAAKvB,KAAOA,EAAI,CAChB,MAAMwQ,EAAiC,mBAAfF,EAA4BA,EAAW/O,GAAQ+O,EACvE,OAAO3K,EAAQe,SAAWkH,EAAO4C,GAAYA,EAAW,IAAKjP,KAASiP,EAC1E,CACA,OAAOjP,MAGTkP,EAAa,CAACzQ,EAAI0Q,EAAY/K,EAAU,CAAEe,SAAS,MACrDjD,EAAUkN,GAAcA,EAAUvO,IAAKP,IACnC,GAAIA,EAAK7B,KAAOA,EAAI,CAChB,MAAM4Q,EAAiC,mBAAfF,EAA4BA,EAAW7O,GAAQ6O,EACvE,OAAO/K,EAAQe,SAAWmH,EAAO+C,GAAYA,EAAW,IAAK/O,KAAS+O,EAC1E,CACA,OAAO/O,MAGf,MAAO,CACHgP,SAAU,IAAM3S,EAAMK,WAAWkE,MAAML,IAAKsM,IAAM,IAAMA,KACxDoC,QAAU9Q,GAAOgQ,EAAgBhQ,IAAK2B,UAAUC,SAChDoO,kBACAe,SAAU,KACN,MAAM,MAAErO,EAAQ,IAAOxE,EAAMK,WAC7B,OAAOmE,EAAMN,IAAK4O,IAAM,IAAMA,MAElCC,QAAUjR,GAAO9B,EAAMK,WAAWuD,WAAWmK,IAAIjM,GACjDwD,WACAC,WACAyN,SAAW/B,IACP,MAAMgC,EAAW7K,MAAMC,QAAQ4I,GAAWA,EAAU,CAACA,GACrDS,EAAad,UAAUpN,KAAMe,GAAU,IAAIA,KAAU0O,KAEzDC,SAAWjC,IACP,MAAMkC,EAAW/K,MAAMC,QAAQ4I,GAAWA,EAAU,CAACA,GACrDS,EAAaP,UAAU3N,KAAMgB,GAAU,IAAIA,KAAU2O,KAEzDC,SAAU,KACN,MAAM,MAAE7O,EAAQ,GAAE,MAAEC,EAAQ,GAAE,UAAEiH,GAAczL,EAAMK,YAC7C4E,EAAGC,EAAGC,GAAQsG,EACrB,MAAO,CACHlH,MAAOA,EAAML,IAAKsM,IAAM,IAAMA,KAC9BhM,MAAOA,EAAMN,IAAK4O,IAAM,IAAMA,KAC9BlH,SAAU,CACN3G,IACAC,IACAC,UAIZkO,eAAgB1H,OAASpH,MAAO+O,EAAgB,GAAI9O,MAAO+O,EAAgB,OACvE,MAAM,MAAEhP,EAAK,MAAEC,EAAK,cAAEgP,EAAa,cAAEC,EAAa,mBAAEC,EAAkB,mBAAEC,EAAkB,SAAEC,EAAQ,eAAEC,GAAoB7T,EAAMK,YACxHkE,MAAOuP,EAAetP,MAAOuP,SAAwB,QAAoB,CAC7ET,gBACAC,gBACAhP,QACAC,QACAqP,mBAEEG,EAAmBD,EAAc1J,OAAS,EAC1C4J,EAAmBH,EAAczJ,OAAS,EAChD,GAAI2J,EAAkB,CAClB,MAAME,EAAcH,EAAc7P,IAAIuL,GACtCgE,IAAgBM,GAChBJ,EAAmBO,EACvB,CACA,GAAID,EAAkB,CAClB,MAAME,EAAcL,EAAc5P,IAAIuL,GACtC+D,IAAgBM,GAChBJ,EAAmBS,EACvB,CAIA,OAHIF,GAAoBD,IACpBJ,IAAW,CAAErP,MAAOuP,EAAetP,MAAOuP,IAEvC,CAAEK,aAAcN,EAAeO,aAAcN,IAExDO,qBAAsB,CAACC,EAAYC,GAAY,EAAMjQ,KACjD,MAAMkQ,GAAS,QAAaF,GACtBG,EAAWD,EAASF,EAAaxC,EAAYwC,GAC7CI,OAA2BxG,IAAV5J,EACvB,OAAKmQ,GAGGnQ,GAASvE,EAAMK,WAAWkE,OAAO+D,OAAQkI,IAC7C,MAAMoE,EAAe5U,EAAMK,WAAWiD,WAAWyK,IAAIyC,EAAE1O,IACvD,GAAI8S,IAAiBH,IAAWjE,EAAE1O,KAAOyS,EAAWzS,KAAO8S,EAAanR,UAAUoR,kBAC9E,OAAO,EAEX,MAAMC,GAAe,QAAWH,EAAiBnE,EAAIoE,GAC/CG,GAAkB,QAAmBD,EAAcJ,GAEzD,OADyBF,GAAaO,EAAkB,GAEpDA,GAAmBD,EAAalU,MAAQkU,EAAajU,QACrDkU,GAAmBL,EAAS9T,MAAQ8T,EAAS7T,SAZ1C,IAefmU,mBAAoB,CAACT,EAAYU,EAAMT,GAAY,KAC/C,MACME,GADS,QAAaH,GACFA,EAAaxC,EAAYwC,GACnD,IAAKG,EACD,OAAO,EAEX,MAAMK,GAAkB,QAAmBL,EAAUO,GAErD,OADyBT,GAAaO,EAAkB,GAC7BA,GAAmBL,EAAS9T,MAAQ8T,EAAS7T,QAE5EsR,aACA+C,eAAgB,CAACpT,EAAIqT,EAAY1N,EAAU,CAAEe,SAAS,MAClD2J,EAAWrQ,EAAKuB,IACZ,MAAM+R,EAAiC,mBAAfD,EAA4BA,EAAW9R,GAAQ8R,EACvE,OAAO1N,EAAQe,QAAU,IAAKnF,EAAMgS,KAAMD,GAAa,IAAK/R,EAAMgS,KAAM,IAAKhS,EAAKgS,QAASD,KAC5F3N,IAEP8K,aACA+C,eAAgB,CAACxT,EAAIqT,EAAY1N,EAAU,CAAEe,SAAS,MAClD+J,EAAWzQ,EAAK6B,IACZ,MAAMyR,EAAiC,mBAAfD,EAA4BA,EAAWxR,GAAQwR,EACvE,OAAO1N,EAAQe,QAAU,IAAK7E,EAAM0R,KAAMD,GAAa,IAAKzR,EAAM0R,KAAM,IAAK1R,EAAK0R,QAASD,KAC5F3N,IAEP8N,eAAiBhR,IACb,MAAM,WAAEjB,EAAU,WAAE2C,GAAejG,EAAMK,WACzC,OAAO,QAAekE,EAAO,CAAEjB,aAAY2C,gBAE/CuP,qBAAsB,EAAG5H,OAAM9L,KAAI2T,YAAarN,MAAMsN,KAAK1V,EACtDK,WACAsV,iBAAiB5H,IAAI,GAAG0H,KAAU7H,IAAO9L,EAAK,IAAIA,IAAO,OACxD8T,UAAY,IAClBC,mBAAoB,EAAGjI,OAAMkI,WAAUL,YAAarN,MAAMsN,KAAK1V,EAC1DK,WACAsV,iBAAiB5H,IAAI,GAAG0H,IAAS7H,EAAQkI,EAAW,IAAIlI,KAAQkI,IAAa,IAAIlI,IAAU,OAC1FgI,UAAY,IAClBG,QAASpK,MAAOlE,IAGZ,MAAMuO,EAAkBhW,EAAMK,WAAW2V,kBAAmB,UAI5D,OAFAhW,EAAMM,SAAS,CAAE0G,eAAe,EAAMC,eAAgBQ,EAASuO,oBAC/DtE,EAAad,UAAUpN,KAAMe,GAAU,IAAIA,IACpCyR,EAAgBC,WAGhC,IACH,OAAO,IAAA7V,SAAQ,KACJ,IACAyR,KACAJ,EACHG,wBAEL,CAACA,GACR,CAEA,MAAMrO,GAAYqF,GAASA,EAAKrF,SAC1B2S,GAA0B,oBAAX/O,OAAyBA,YAASgH,EAwDvD,MAAMgI,GAAiB,CACnBxV,SAAU,WACVC,MAAO,OACPC,OAAQ,OACRuV,IAAK,EACLC,KAAM,GAGJC,GAAc/U,IAAM,CACtBgV,oBAAqBhV,EAAEgV,oBACvBC,IAAKjV,EAAEiV,MAEX,SAASC,IAAS,kBAAEC,EAAiB,aAAEC,GAAe,EAAI,YAAEC,GAAc,EAAI,YAAEC,GAAc,EAAK,iBAAEC,EAAmB,GAAG,gBAAEC,EAAkB,KAAgBC,KAAI,kBAAEC,GAAoB,EAAI,UAAEC,GAAY,EAAI,gBAAElS,EAAe,gBAAEgB,EAAe,QAAEE,EAAO,QAAEC,EAAO,sBAAEgR,EAAqB,iBAAEC,GAAmB,EAAI,SAAErV,EAAQ,iBAAEsV,EAAgB,eAAEhR,EAAc,iBAAEiR,EAAgB,qBAAEC,EAAoB,kBAAEjR,IACrY,MAAMtG,EAAQG,IACRqX,GAAW,IAAA3Q,QAAO,OAClB,oBAAE0P,EAAmB,IAAEC,GAAQ3W,EAASyW,GAAY,KACpDmB,EAA2BlQ,EAAY4P,GACvCpM,GAAU,IAAAlE,WA7CpB,SAA0B6F,GACtB,MAAM1M,EAAQG,KACd,IAAAkE,WAAU,KACN,MAAMqT,EAAmB,KACrB,IAAKhL,EAAQ9F,QACT,OAAO,EAEX,MAAM0D,GAAO,QAAcoC,EAAQ9F,SACf,IAAhB0D,EAAKzJ,QAA+B,IAAfyJ,EAAK1J,OAC1BZ,EAAMK,WAAWsX,UAAU,MAAO,KAAwB,YAE9D3X,EAAMM,SAAS,CAAEM,MAAO0J,EAAK1J,OAAS,IAAKC,OAAQyJ,EAAKzJ,QAAU,OAEtE,GAAI6L,EAAQ9F,QAAS,CACjB8Q,IACAvQ,OAAO+C,iBAAiB,SAAUwN,GAClC,MAAME,EAAiB,IAAIC,eAAe,IAAMH,KAEhD,OADAE,EAAeE,QAAQpL,EAAQ9F,SACxB,KACHO,OAAOgD,oBAAoB,SAAUuN,GACjCE,GAAkBlL,EAAQ9F,SAC1BgR,EAAeG,UAAUrL,EAAQ9F,SAG7C,GACD,GACP,CAoBIoR,CAAiBR,GACjB,MAAMS,GAAoB,IAAApH,aAAapF,IACnC6L,IAAmB,CAAErS,EAAGwG,EAAU,GAAIvG,EAAGuG,EAAU,GAAItG,KAAMsG,EAAU,KAClE8L,GACDvX,EAAMM,SAAS,CAAEmL,eAEtB,CAAC6L,EAAkBC,IAyEtB,OAxEA,IAAAlT,WAAU,KACN,GAAImT,EAAS5Q,QAAS,CAClBmE,EAAQnE,SAAU,QAAU,CACxB8F,QAAS8K,EAAS5Q,QAClBV,UACAC,UACAH,kBACA4F,SAAU5G,EACVsB,oBACA4R,iBAAmBC,GAAiBnY,EAAMM,SAAS,CAAE6X,iBACrDC,eAAgB,CAACrP,EAAOsP,KACpB,MAAM,sBAAEC,EAAqB,YAAEC,GAAgBvY,EAAMK,WACrDkY,IAAcxP,EAAOsP,GACrBC,IAAwBD,IAE5BG,UAAW,CAACzP,EAAOsP,KACf,MAAM,iBAAEf,EAAgB,OAAEmB,GAAWzY,EAAMK,WAC3CoY,IAAS1P,EAAOsP,GAChBf,IAAmBe,IAEvBK,aAAc,CAAC3P,EAAOsP,KAClB,MAAM,oBAAEM,EAAmB,UAAEC,GAAc5Y,EAAMK,WACjDuY,IAAY7P,EAAOsP,GACnBM,IAAsBN,MAG9B,MAAM,EAAEpT,EAAC,EAAEC,EAAC,KAAEC,GAAS4F,EAAQnE,QAAQoF,cAMvC,OALAhM,EAAMM,SAAS,CACXyK,QAASA,EAAQnE,QACjB6E,UAAW,CAACxG,EAAGC,EAAGC,GAClBuH,QAAS8K,EAAS5Q,QAAQiS,QAAQ,iBAE/B,KACH9N,EAAQnE,SAASkS,UAEzB,GACD,KACH,IAAAzU,WAAU,KACN0G,EAAQnE,SAASmS,OAAO,CACpBrC,oBACAC,eACAC,cACAC,cACAC,mBACAC,kBACAE,oBACAC,YACAO,2BACAL,mBACA/Q,iBACAkQ,sBACAc,mBACAb,MACAyB,uBAEL,CACCvB,EACAC,EACAC,EACAC,EACAC,EACAC,EACAE,EACAC,EACAO,EACAL,EACA/Q,EACAkQ,EACAc,EACAb,EACAyB,KAEI,IAAApW,KAAI,MAAO,CAAES,UAAW,uBAAwBE,IAAKgV,EAAUhX,MAAO2V,GAAgBpU,SAAUA,GAC5G,CAEA,MAAMiX,GAAczX,IAAM,CACtBgV,oBAAqBhV,EAAEgV,oBACvB0C,kBAAmB1X,EAAE0X,oBAEzB,SAASC,KACL,MAAM,oBAAE3C,EAAmB,kBAAE0C,GAAsBpZ,EAASmZ,GAAY,KAExE,OADiBzC,GAAuB0C,GAIhC,IAAApX,KAAI,MAAO,CAAES,UAAW,8CAA+C9B,MAAO,CAC9EI,MAAOqY,EAAkBrY,MACzBC,OAAQoY,EAAkBpY,OAC1B4K,UAAW,aAAawN,EAAkBhU,QAAQgU,EAAkB/T,UALjE,IAOf,CAEA,MAAMiU,GAAc,CAACC,EAASC,IAClBtQ,IACAA,EAAMhG,SAAWsW,EAAazS,SAGlCwS,IAAUrQ,IAGZuQ,GAAc/X,IAAM,CACtBgV,oBAAqBhV,EAAEgV,oBACvBnQ,mBAAoB7E,EAAE6E,mBACtBmT,qBAAsBhY,EAAEiY,WAAWC,WACnCnL,SAAU/M,EAAE4W,eAEhB,SAASuB,IAAK,YAAEC,EAAW,oBAAEC,EAAmB,cAAEC,EAAgB,KAAcC,KAAI,UAAE5C,EAAS,gBAAE6C,EAAe,iBAAEC,EAAgB,eAAEC,EAAc,YAAEC,EAAW,kBAAExD,EAAiB,aAAEyD,EAAY,iBAAEC,EAAgB,gBAAEC,EAAe,iBAAEC,EAAgB,SAAEvY,IACnP,MAAM/B,EAAQG,KACR,oBAAEoW,EAAmB,mBAAEnQ,EAAkB,SAAEkI,EAAQ,qBAAEiL,GAAyB1Z,EAASyZ,GAAY,KACnGiB,EAAqBnU,IAAuBuT,GAAepD,GAC3DiE,GAAY,IAAA3T,QAAO,MACnB4T,GAAkB,IAAA5T,UAClB6T,GAAkB,IAAA7T,QAAO,IAAImB,KAC7B2S,GAAkB,IAAA9T,QAAO,IAAImB,KAE7B4S,GAAsB,IAAA/T,SAAO,GAC7BgU,GAAmB,IAAAhU,SAAO,GAC1BiU,EAAW/R,IAGT6R,EAAoBhU,SAAW2S,EAC/BqB,EAAoBhU,SAAU,GAGlCsT,IAAcnR,GACd/I,EAAMK,WAAW0a,wBACjB/a,EAAMM,SAAS,CAAE0a,sBAAsB,MASrCC,EAAUd,EAAgBpR,GAAUoR,EAAapR,QAASoF,EAuG1D+M,GAA0B,IAAdhE,GAAuB9O,MAAMC,QAAQ6O,IAAcA,EAAUtM,SAAS,GACxF,OAAQ,IAAA1I,MAAK,MAAO,CAAEI,WAAW,OAAG,CAAC,mBAAoB,CAAE4Y,YAAW5M,WAAU6M,UAAWxB,KAAiBmB,QAASP,OAAqBpM,EAAYgL,GAAY2B,EAASN,GAAYY,cAAejC,GA/G/KpQ,IACfX,MAAMC,QAAQ6O,IAAcA,GAAWtM,SAAS,GAChD7B,EAAMa,iBAGV8M,IAAoB3N,IA0GyMyR,GAAYS,QAAS9B,GAAY8B,EAAST,GAAYa,eAAgBd,OAAqBpM,EAAYiM,EAAkBkB,cAAef,EAvGlVxR,IACnB,MAAM,sBAAEgS,EAAqB,QAAErO,GAAY1M,EAAMK,WAEjD,GADAoa,EAAgB7T,QAAU8F,GAASG,yBAC9BzG,IACAuT,GACgB,IAAjB5Q,EAAMwS,QACNxS,EAAMhG,SAAWyX,EAAU5T,UAC1B6T,EAAgB7T,QACjB,OAEJmC,EAAMhG,QAAQyY,oBAAoBzS,EAAM0S,WACxCZ,EAAiBjU,SAAU,EAC3BgU,EAAoBhU,SAAU,EAC9B,MAAM,EAAE3B,EAAC,EAAEC,IAAM,QAAiB6D,EAAM2S,YAAajB,EAAgB7T,SACrEmU,IACA/a,EAAMM,SAAS,CACX2Y,kBAAmB,CACfrY,MAAO,EACPC,OAAQ,EACR8a,OAAQ1W,EACR2W,OAAQ1W,EACRD,IACAC,OAGR8U,IAAmBjR,IA8EuXsR,EAAiBwB,cAAetB,EA5EvZxR,IACnB,MAAM,kBAAEkQ,EAAiB,UAAExN,EAAS,WAAEnI,EAAU,WAAEM,EAAU,iBAAE+R,EAAgB,mBAAEjC,EAAkB,mBAAEC,EAAkB,mBAAEmI,GAAwB9b,EAAMK,WACtJ,IAAKoa,EAAgB7T,UAAYqS,EAC7B,OAEJ2B,EAAoBhU,SAAU,EAC9B,MAAQ3B,EAAG8W,EAAQ7W,EAAG8W,IAAW,QAAiBjT,EAAM2S,YAAajB,EAAgB7T,UAC/E,OAAE+U,EAAM,OAAEC,GAAW3C,EACrBgD,EAAqB,CACvBN,SACAC,SACA3W,EAAG8W,EAASJ,EAASI,EAASJ,EAC9BzW,EAAG8W,EAASJ,EAASI,EAASJ,EAC9Bhb,MAAOsb,KAAKC,IAAIJ,EAASJ,GACzB9a,OAAQqb,KAAKC,IAAIH,EAASJ,IAExBQ,EAAsB1B,EAAgB9T,QACtCyV,EAAsB1B,EAAgB/T,QAC5C8T,EAAgB9T,QAAU,IAAIoB,KAAI,QAAe1E,EAAY2Y,EAAoBxQ,EAAWoO,IAAkB,KAAcyC,SAAS,GAAMpY,IAAKb,GAASA,EAAKvB,KAC9J6Y,EAAgB/T,QAAU,IAAIoB,IAC9B,MAAMuU,EAAkBT,GAAoBU,aAAc,EAE1D,IAAK,MAAM/G,KAAUiF,EAAgB9T,QAAS,CAC1C,MAAM6V,EAAc9G,EAAiB5H,IAAI0H,GACzC,GAAKgH,EAEL,IAAK,MAAM,OAAEC,KAAYD,EAAY7G,SAAU,CAC3C,MAAMjS,EAAOC,EAAWmK,IAAI2O,GACxB/Y,IAASA,EAAK6Y,YAAcD,IAC5B5B,EAAgB/T,QAAQ2C,IAAImT,EAEpC,CACJ,CACA,KAAK,QAAaN,EAAqB1B,EAAgB9T,SAAU,CAE7D8M,EADgB5E,EAAoBxL,EAAYoX,EAAgB9T,SAAS,GAE7E,CACA,KAAK,QAAayV,EAAqB1B,EAAgB/T,SAAU,CAE7D+M,EADgB7E,EAAoBlL,EAAY+W,EAAgB/T,SAEpE,CACA5G,EAAMM,SAAS,CACX2Y,kBAAmBgD,EACnB1F,qBAAqB,EACrByE,sBAAsB,KAgCqbX,EAAiBsC,YAAapC,EA7B5dxR,IACjB,GAAqB,IAAjBA,EAAMwS,SAAiBV,EAAiBjU,QACxC,OAEJmC,EAAMhG,QAAQ6Z,wBAAwB7T,EAAM0S,WAC5C,MAAM,kBAAExC,GAAsBjZ,EAAMK,YAK/BkW,GAAuB0C,GAAqBlQ,EAAMhG,SAAWyX,EAAU5T,SACxEkU,IAAU/R,GAEd/I,EAAMM,SAAS,CACXiW,qBAAqB,EACrB0C,kBAAmB,KACnB+B,qBAAsBN,EAAgB9T,QAAQ0D,KAAO,IAEzD2P,IAAiBlR,IAKb6Q,GAAuBG,KACvBa,EAAoBhU,SAAU,GAElCiU,EAAiBjU,SAAU,QAGqfuH,EAAW0O,eAAgBvC,EAAkB9X,IAAKgY,EAAWha,MAAO2V,GAAgBpU,SAAU,CAACA,GAAU,IAAAF,KAAIqX,GAAe,CAAC,KACrpB,CAQA,SAAS4D,IAAgB,GAAEhb,EAAE,MAAE9B,EAAK,SAAE+c,GAAW,EAAK,QAAEC,IACpD,MAAM,iBAAEC,EAAgB,sBAAEC,EAAqB,qBAAEC,EAAoB,WAAE7Z,EAAU,QAAEqU,GAAY3X,EAAMK,WAC/FgD,EAAOC,EAAWyK,IAAIjM,GACvBuB,GAILrD,EAAMM,SAAS,CAAE0a,sBAAsB,IAClC3X,EAAKE,UAGDwZ,GAAa1Z,EAAKE,UAAY4Z,KACnCD,EAAsB,CAAE3Y,MAAO,CAAClB,GAAOmB,MAAO,KAC9C0M,sBAAsB,IAAM8L,GAASpW,SAASwW,SAJ9CH,EAAiB,CAACnb,KALlB6V,IAAU,MAAO,KAAwB,SAAE7V,GAWnD,CAOA,SAASub,IAAQ,QAAEL,EAAO,SAAEM,GAAW,EAAK,gBAAEC,EAAe,eAAEC,EAAc,OAAE/H,EAAM,aAAEgI,EAAY,kBAAEC,IACjG,MAAM1d,EAAQG,KACPmO,EAAUqP,IAAe,IAAA9V,WAAS,GACnC+V,GAAS,IAAA/W,UAqCf,OApCA,IAAAxC,WAAU,KACNuZ,EAAOhX,SAAU,QAAO,CACpBiX,cAAe,IAAM7d,EAAMK,WAC3Byd,gBAAkBhc,IACdgb,GAAgB,CACZhb,KACA9B,QACAgd,aAGRe,YAAa,KACTJ,GAAY,IAEhBK,WAAY,KACRL,GAAY,OAGrB,KACH,IAAAtZ,WAAU,KACN,GAAIiZ,EACAM,EAAOhX,SAASkS,eAEf,GAAIkE,EAAQpW,QASb,OARAgX,EAAOhX,SAASmS,OAAO,CACnBwE,kBACAC,iBACA9Q,QAASsQ,EAAQpW,QACjB6W,eACAhI,SACAiI,sBAEG,KACHE,EAAOhX,SAASkS,YAGzB,CAACyE,EAAiBC,EAAgBF,EAAUG,EAAcT,EAASvH,IAC/DnH,CACX,CASA,SAAS2P,KACL,MAAMje,EAAQG,IAsCd,OArC0B,IAAA0Q,aAAavM,IACnC,MAAM,WAAE4Z,EAAU,WAAEzR,EAAU,SAAED,EAAQ,eAAE2R,EAAc,QAAExG,EAAO,oBAAEyG,EAAmB,WAAE9a,EAAU,WAAE2C,GAAejG,EAAMK,WACnHge,EAAc,IAAI5Q,IAClB6Q,EAZe,CAACH,GAAoB3N,GAAMA,EAAEjN,WAAaiN,EAAE0K,WAAciD,QAAyC,IAAhB3N,EAAE0K,WAYvFqD,CAAqBJ,GAKlCK,EAAQ/R,EAAaD,EAAS,GAAK,EACnCiS,EAAQhS,EAAaD,EAAS,GAAK,EACnCkS,EAAQpa,EAAOqa,UAAU1Z,EAAIuZ,EAAQla,EAAOsa,OAC5CC,EAAQva,EAAOqa,UAAUzZ,EAAIuZ,EAAQna,EAAOsa,OAClD,IAAK,MAAO,CAAEvb,KAASC,EAAY,CAC/B,IAAKgb,EAAWjb,GACZ,SAEJ,IAAIyb,EAAe,CACf7Z,EAAG5B,EAAKI,UAAUoR,iBAAiB5P,EAAIyZ,EACvCxZ,EAAG7B,EAAKI,UAAUoR,iBAAiB3P,EAAI2Z,GAEvCpS,IACAqS,GAAe,QAAaA,EAActS,IAE9C,MAAM,SAAE7L,EAAQ,iBAAEkU,IAAqB,QAAsB,CACzDY,OAAQpS,EAAKvB,GACbgd,eACAxb,aACA4a,aACAjY,aACA0R,YAEJtU,EAAK1C,SAAWA,EAChB0C,EAAKI,UAAUoR,iBAAmBA,EAClCwJ,EAAYxQ,IAAIxK,EAAKvB,GAAIuB,EAC7B,CACA+a,EAAoBC,IACrB,GAEP,CAEA,MAAMU,IAAgB,IAAAtf,eAAc,MAC9BE,GAAWof,GAAcpf,SAC/Bof,GAAcC,SA6Bd,MAAMC,GAAY,KACC,IAAAhf,YAAW8e,IAIxBG,GAAc3d,IAAM,CACtB4d,eAAgB5d,EAAE4d,eAClB9Y,eAAgB9E,EAAE8E,eAClBzE,KAAML,EAAEK,OAwKZ,MAAMwd,IAAS,IAAAC,MAAKzP,EAtJpB,UAAyB,KAAEhC,EAAO,SAAQ,SAAEjN,EAAW,KAAS2e,IAAG,kBAAEC,EAAiB,cAAEC,GAAgB,EAAI,mBAAEC,GAAqB,EAAI,iBAAEC,GAAmB,EAAI,GAAE5d,EAAE,UAAE6d,EAAS,SAAE5d,EAAQ,UAAEO,EAAS,YAAEsd,EAAW,aAAEC,KAAiBtd,GAAQC,GACxO,MAAMsT,EAAWhU,GAAM,KACjBge,EAAoB,WAATlS,EACX5N,EAAQG,IACRsV,EAASwJ,MACT,eAAEE,EAAc,eAAE9Y,EAAc,KAAEzE,GAAS/B,EAASqf,GAAY,MAChE,eAAEa,EAAc,aAAEC,EAAY,gBAAEC,EAAe,oBAAEC,EAAmB,oBAAEC,EAAmB,yBAAEC,EAAwB,MAAEC,GAAWxgB,EAtB/G,EAAC4V,EAAQK,EAAUlI,IAAU0S,IACpD,MAAQC,2BAA4BC,EAAW,eAAEC,EAAc,WAAEjH,GAAe8G,GAC1E,WAAEI,EAAU,SAAEC,EAAQ,QAAEC,GAAYpH,EACpCwG,EAAeW,GAAUlL,SAAWA,GAAUkL,GAAU7e,KAAOgU,GAAY6K,GAAU/S,OAASA,EACpG,MAAO,CACHmS,eAAgBW,GAAYjL,SAAWA,GAAUiL,GAAY5e,KAAOgU,GAAY4K,GAAY9S,OAASA,EACrGoS,eACAC,gBAAiBO,GAAa/K,SAAWA,GAAU+K,GAAa1e,KAAOgU,GAAY0K,GAAa5S,OAASA,EACzGsS,oBAAqBO,IAAmB,KAAeI,OACjDH,GAAY9S,OAASA,EACrB6H,IAAWiL,GAAYjL,QAAUK,IAAa4K,GAAY5e,GAChEqe,sBAAuBO,EACvBN,2BAA4BI,EAC5BH,MAAOL,GAAgBY,IASoHE,CAAmBrL,EAAQK,EAAUlI,GAAO,KACtL6H,GACDzV,EAAMK,WAAWsX,UAAU,MAAO,KAAwB,YAE9D,MAAMoJ,EAAqBzc,IACvB,MAAM,mBAAEwX,EAAoB6D,UAAWqB,EAAe,gBAAE5P,GAAoBpR,EAAMK,WAC5E4gB,EAAa,IACZnF,KACAxX,GAEP,GAAI8M,EAAiB,CACjB,MAAM,MAAE5M,EAAK,SAAEe,GAAavF,EAAMK,WAClCkF,GAAS,QAAQ0b,EAAYzc,GACjC,CACAwc,IAAkBC,GAClBtB,IAAYsB,IAEV3F,EAAiBvS,IACnB,IAAK0M,EACD,OAEJ,MAAMyL,GAAmB,QAAanY,EAAM2S,aAC5C,GAAI+D,IACEyB,GAAqC,IAAjBnY,EAAMwS,SAAkB2F,GAAmB,CACjE,MAAMC,EAAenhB,EAAMK,WAC3B,KAASib,cAAcvS,EAAM2S,YAAa,CACtC0F,iBAAkBD,EAAaC,iBAC/BX,eAAgBU,EAAaV,eAC7BY,iBAAkBF,EAAaE,iBAC/B3U,QAASyU,EAAazU,QACtBpJ,WAAY6d,EAAa7d,WACzBkT,IAAK2K,EAAa3K,IAClBsJ,WACAhK,WACAL,SACA6L,OAAQH,EAAavf,KACrB2f,MAAOJ,EAAaI,MACpBC,iBAAkBL,EAAaK,iBAC/BC,eAAgBN,EAAaM,eAC7BC,aAAcP,EAAaO,aAC3BC,iBAAkBR,EAAaQ,iBAC/BhC,UAAWoB,EACXxB,kBAAmBA,GAAqB4B,EAAa5B,kBACrDqC,aAAc,IAAM5hB,EAAMK,WAAWoL,UACrCoW,cAAe,IAAM7hB,EAAMK,WAAWmZ,WAAWkH,WACjDoB,aAAcX,EAAaW,aAC3BC,cAAeZ,EAAaa,yBAEpC,CACId,EACAtB,IAAc7W,GAGd8W,IAAe9W,IAwCvB,OAAQ,IAAAlH,KAAI,MAAO,CAAE,gBAAiBiU,EAAU,cAAeL,EAAQ,iBAAkB9U,EAAU,UAAW,GAAGiB,KAAQ6T,KAAUK,KAAYlI,IAAQtL,WAAW,OAAG,CAC7J,qBACA,sBAAsB3B,IACtB,SACA0F,EACA/D,EACA,CACI2f,QAASnC,EACT/c,OAAQ+c,EACRoC,YAAa1C,EACb2C,iBAAkB1C,EAClB2C,eAAgB1C,EAChB2C,gBAAiBpC,EACjBqC,eAAgBvC,EAChBwC,aAAcvC,EACdK,QAKAmC,oBAAqBhD,KACfW,GAAuBD,KACxBC,GAAuBC,EAA2BV,EAAmBD,MAE9EG,YAAatE,EAAeuE,aAAcvE,EAAeR,QAASqE,EA7DzDpW,IACb,MAAM,oBAAE0Z,EAAmB,kBAAEC,EAAiB,2BAAEnC,EAA0B,eAAEE,EAAgBlB,kBAAmBoD,EAAsB,IAAEnM,EAAK5U,KAAM0f,EAAM,WAAEhe,EAAYkW,WAAYoJ,GAAqB5iB,EAAMK,WAC7M,IAAKoV,IAAY8K,IAA+Bd,EAC5C,OAEJ,IAAKc,EAGD,OAFAkC,IAAsB1Z,EAAM2S,YAAa,CAAEjG,SAAQK,WAAU+M,WAAYjV,SACzE5N,EAAMM,SAAS,CAAEigB,2BAA4B,CAAE9K,SAAQ7H,OAAM9L,GAAIgU,KAGrE,MAAMgN,GAAM,QAAkB/Z,EAAMhG,QAC9BggB,EAA2BxD,GAAqBoD,GAChD,WAAEnJ,EAAU,QAAEoH,GAAY,KAASA,QAAQ7X,EAAM2S,YAAa,CAChEsH,OAAQ,CACJvN,SACA3T,GAAIgU,EACJlI,QAEJ6S,iBACAwC,WAAY1C,EAA2B9K,OACvCyN,aAAc3C,EAA2Bze,IAAM,KAC/CqhB,SAAU5C,EAA2B3S,KACrC2R,kBAAmBwD,EACnBzB,SACAwB,MACAtM,MACAlT,eAEAsd,GAAWpH,GACXuH,EAAkBvH,GAEtB,MAAM4J,EAAkBC,gBAAgBT,UACjCQ,EAAgB3J,WACvB2J,EAAgBE,WAAaF,EAAgBzC,SAAWyC,EAAgBzC,SAAShgB,SAAW,KAC5F+hB,IAAoB3Z,EAAOqa,GAC3BpjB,EAAMM,SAAS,CAAEigB,2BAA4B,aA0BoDpS,EAAW3L,IAAKA,KAAQD,EAAMR,SAAUA,GACjJ,IA4CA,MAAMwhB,GAAgB,CAClBC,QAAS,CAAEve,EAAG,EAAGC,GAAI,GACrBue,UAAW,CAAExe,EAAG,EAAGC,EAAG,GACtBwe,UAAW,CAAEze,GAAI,EAAGC,EAAG,GACvBye,WAAY,CAAE1e,EAAG,EAAGC,EAAG,IAErB0e,GAAmB,CACrBC,MAvBJ,UAAmB,KAAExO,EAAI,cAAEmK,EAAa,eAAEsE,EAAiB,KAASC,SAChE,OAAQ,IAAA7hB,MAAK,EAAAC,SAAU,CAAEJ,SAAU,CAACsT,GAAM2O,OAAO,IAAAniB,KAAIud,GAAQ,CAAExR,KAAM,SAAUjN,SAAUmjB,EAAgBtE,cAAeA,MAC5H,EAsBIyE,QApBJ,UAAqB,KAAE5O,EAAI,cAAEmK,EAAa,eAAE0E,EAAiB,KAAS5E,IAAG,eAAEwE,EAAiB,KAASC,SACjG,OAAQ,IAAA7hB,MAAK,EAAAC,SAAU,CAAEJ,SAAU,EAAC,IAAAF,KAAIud,GAAQ,CAAExR,KAAM,SAAUjN,SAAUujB,EAAgB1E,cAAeA,IAAkBnK,GAAM2O,OAAO,IAAAniB,KAAIud,GAAQ,CAAExR,KAAM,SAAUjN,SAAUmjB,EAAgBtE,cAAeA,MACrN,EAmBI2E,OAbJ,UAAoB,KAAE9O,EAAI,cAAEmK,EAAa,eAAE0E,EAAiB,KAAS5E,MACjE,OAAQ,IAAApd,MAAK,EAAAC,SAAU,CAAEJ,SAAU,EAAC,IAAAF,KAAIud,GAAQ,CAAExR,KAAM,SAAUjN,SAAUujB,EAAgB1E,cAAeA,IAAkBnK,GAAM2O,QACvI,EAYII,MAlBJ,WACI,OAAO,IACX,GA+BA,MAAMC,GAAc9iB,IAChB,MAAM,MAAEX,EAAK,OAAEC,EAAM,EAAEoE,EAAC,EAAEC,IAAM,QAAuB3D,EAAE+B,WAAY,CACjEgF,OAASjF,KAAWA,EAAKE,WAE7B,MAAO,CACH3C,OAAO,QAAUA,GAASA,EAAQ,KAClCC,QAAQ,QAAUA,GAAUA,EAAS,KACrC0V,oBAAqBhV,EAAEgV,oBACvB+N,gBAAiB,aAAa/iB,EAAEkK,UAAU,QAAQlK,EAAEkK,UAAU,eAAelK,EAAEkK,UAAU,iBAAiBxG,OAAOC,SAGzH,SAASqf,IAAe,uBAAEC,EAAsB,eAAEne,EAAc,oBAAEpE,IAC9D,MAAMjC,EAAQG,KACR,MAAES,EAAK,OAAEC,EAAM,gBAAEyjB,EAAe,oBAAE/N,GAAwB1W,EAASwkB,GAAY,KAC/EI,EAAoBxG,KACpBjB,GAAU,IAAAnW,QAAO,MAWvB,IAVA,IAAAxC,WAAU,KACDpC,GACD+a,EAAQpW,SAAS8d,MAAM,CACnBC,eAAe,KAGxB,CAAC1iB,IACJob,GAAQ,CACJL,YAEAzG,IAAwB3V,IAAUC,EAClC,OAAO,KAEX,MAAMua,EAAgBoJ,EACfzb,IACC,MAAM5F,EAAgBnD,EAAMK,WAAWkE,MAAM+D,OAAQkI,GAAMA,EAAEjN,UAC7DihB,EAAuBzb,EAAO5F,SAEhCgL,EAUN,OAAQ,IAAAtM,KAAI,MAAO,CAAES,WAAW,OAAG,CAAC,6BAA8B,wBAAyB+D,IAAkB7F,MAAO,CAC5GiL,UAAW6Y,GACZviB,UAAU,IAAAF,KAAI,MAAO,CAAEW,IAAKwa,EAAS1a,UAAW,kCAAmC8Y,cAAeA,EAAewJ,SAAU3iB,OAAsBkM,GAAa,EAAG0W,UAAW5iB,OAAsBkM,EAXtLpF,IACX+b,OAAOC,UAAUC,eAAeC,KAAK1B,GAAexa,EAAMiB,OAC1DjB,EAAMa,iBACN6a,EAAkB,CACd9F,UAAW4E,GAAcxa,EAAMiB,KAC/B4U,OAAQ7V,EAAMG,SAAW,EAAI,MAMuL1I,MAAO,CAC3NI,QACAC,aAEhB,CAEA,MAAMqkB,GAAwB,oBAAX/d,OAAyBA,YAASgH,EAC/CgX,GAAc5jB,IACT,CAAEyZ,qBAAsBzZ,EAAEyZ,qBAAsBzE,oBAAqBhV,EAAEgV,sBAElF,SAAS6O,IAAsB,SAAErjB,EAAQ,YAAEmY,EAAW,iBAAEE,EAAgB,gBAAEC,EAAe,iBAAEC,EAAgB,kBAAE5D,EAAiB,aAAEyD,EAAY,kBAAE7T,EAAiB,cAAE+e,EAAa,iBAAEC,EAAgB,gBAAEvL,EAAe,cAAEF,EAAa,iBAAEG,EAAgB,eAAEC,EAAc,sBAAEsL,EAAqB,qBAAEC,EAAoB,sBAAErO,EAAqB,mBAAE/Q,EAAkB,aAAEuQ,EAAY,YAAEC,EAAaC,YAAa4O,EAAY,iBAAE3O,EAAgB,gBAAEC,EAAe,kBAAEE,EAAmBC,UAAWwO,EAAU,gBAAE1gB,EAAe,gBAAEgB,EAAe,QAAEE,EAAO,QAAEC,EAAO,iBAAEiR,EAAgB,uBAAEoN,EAAsB,iBAAEnN,EAAgB,eAAEhR,EAAc,oBAAEpE,EAAmB,iBAAEqV,EAAgB,qBAAEC,IACloB,MAAM,qBAAEyD,EAAoB,oBAAEzE,GAAwB1W,EAASslB,IACzDvL,EAAsBrS,EAAY+d,EAAkB,CAAEviB,OAAQmiB,KAC9DS,EAA0Bpe,EAAYie,EAAsB,CAAEziB,OAAQmiB,KACtEhO,EAAYyO,GAA2BD,EACvC7O,EAAc8O,GAA2BF,EACzCG,EAAmB7L,IAAiC,IAAd7C,EACtCyC,EAAcC,GAAuBrD,GAAuBqP,EAElE,OAhvBJ,UAA6B,cAAEP,EAAa,sBAAEE,IAC1C,MAAMvlB,EAAQG,KACR,eAAEkT,GAAmB7B,KACrBqU,EAAmBte,EAAY8d,EAAe,CAAE3d,4BAA4B,IAC5Eoe,EAA2Bve,EAAYge,EAAuB,CAAExiB,OAAQmT,MAC9E,IAAA7R,WAAU,KACN,GAAIwhB,EAAkB,CAClB,MAAM,MAAErhB,EAAK,MAAED,GAAUvE,EAAMK,WAC/BgT,EAAe,CAAE9O,MAAOA,EAAM+D,OAAO/E,IAAWiB,MAAOA,EAAM8D,OAAO/E,MACpEvD,EAAMM,SAAS,CAAE0a,sBAAsB,GAC3C,GACD,CAAC6K,KACJ,IAAAxhB,WAAU,KACNrE,EAAMM,SAAS,CAAE6c,qBAAsB2I,KACxC,CAACA,GACR,CAguBIC,CAAoB,CAAEV,gBAAeE,2BAC7B,IAAA1jB,KAAI4U,GAAU,CAAEC,kBAAmBA,EAAmBtQ,mBAAoBA,EAAoBuQ,aAAcA,EAAcC,YAAaA,EAAaC,YAAaA,EAAaC,iBAAkBA,EAAkBC,gBAAiBA,EAAiBE,kBAAmBA,EAAmBC,WAAY0C,GAAuB1C,EAAWlS,gBAAiBA,EAAiBgB,gBAAiBA,EAAiBE,QAASA,EAASC,QAASA,EAASgR,sBAAuBA,EAAuBC,iBAAkBA,EAAkBC,iBAAkBA,EAAkBhR,eAAgBA,EAAgBiR,iBAAkBA,EAAkBC,qBAAsBA,EAAsBjR,kBAAmBA,EAAmBvE,UAAU,IAAAG,MAAKwX,GAAM,CAAEM,iBAAkBA,EAAkBC,eAAgBA,EAAgBC,YAAaA,EAAaE,iBAAkBA,EAAkBC,gBAAiBA,EAAiBC,iBAAkBA,EAAkB5D,kBAAmBA,EAAmByD,aAAcA,EAAcjD,UAAWA,EAAWyC,cAAeA,EAAaE,cAAeA,EAAeD,oBAAqBA,EAAqBG,gBAAiB6L,EAAkB7jB,SAAU,CAACA,EAAUiZ,IAAyB,IAAAnZ,KAAI0iB,GAAgB,CAAEC,uBAAwBA,EAAwBne,eAAgBA,EAAgBpE,oBAAqBA,QAC/yC,CACAmjB,GAAsBniB,YAAc,eACpC,MAAM+iB,IAAe,IAAA3G,MAAK+F,IAc1B,SAASa,GAAkBC,GAEvB,OADgBrmB,GAAS,IAAAgR,aAbV,CAACqV,GAAuB3kB,GAChC2kB,GACD,QAAe3kB,EAAE+B,WAAY,CAAE2B,EAAG,EAAGC,EAAG,EAAGtE,MAAOW,EAAEX,MAAOC,OAAQU,EAAEV,QAAUU,EAAEkK,WAAW,GAAMvH,IAAKb,GAASA,EAAKvB,IACrHsG,MAAMsN,KAAKnU,EAAE+B,WAAW6E,QAUOge,CAAWD,GAAoB,CAACA,IAAqB,IAE9F,CAEA,MAAME,GAAc7kB,GAAMA,EAAE8kB,oBAiF5B,SAASC,IAAY,GAAExkB,EAAE,QAAEgZ,EAAO,aAAEyL,EAAY,YAAEC,EAAW,aAAEC,EAAY,cAAErL,EAAa,cAAEsL,EAAa,eAAEvI,EAAc,mBAAE/X,EAAkB,iBAAEugB,EAAgB,eAAEC,EAAc,eAAEhP,EAAc,gBAAE2F,EAAe,eAAElX,EAAc,oBAAEpE,EAAmB,KAAEL,EAAI,UAAEilB,EAAS,kBAAEnJ,EAAiB,QAAE/F,IACvR,MAAM,KAAEtU,EAAI,UAAEI,EAAS,SAAEqjB,GAAajnB,EAAU0B,IAC5C,MAAM8B,EAAO9B,EAAE+B,WAAWyK,IAAIjM,GACxBglB,EAAWvlB,EAAEwlB,aAAarc,IAAI5I,GACpC,MAAO,CACHuB,OACAI,UAAWJ,EAAKI,UAChBqjB,aAEL,KACH,IAAIE,EAAW3jB,EAAKuK,MAAQ,UACxBqZ,EAAgBJ,IAAYG,IAAapD,GAAiBoD,QACxC7Y,IAAlB8Y,IACAtP,IAAU,MAAO,KAAwB,SAAEqP,IAC3CA,EAAW,UACXC,EAAgBJ,GAAqB,SAAKjD,GAAiBK,SAE/D,MAAMiD,KAAiB7jB,EAAK6X,WAAciD,QAA4C,IAAnB9a,EAAK6X,WAClEuC,KAAkBpa,EAAKmZ,YAAepW,QAAiD,IAApB/C,EAAKmZ,YACxEgD,KAAmBnc,EAAK6e,aAAgByE,QAAgD,IAArBtjB,EAAK6e,aACxEiF,KAAiB9jB,EAAK+jB,WAAcR,QAA4C,IAAnBvjB,EAAK+jB,WAClEpnB,EAAQG,IACRknB,GAAgB,QAAkBhkB,GAClC2Z,EAtEV,UAAyB,KAAE3Z,EAAI,SAAE2jB,EAAQ,cAAEK,EAAa,eAAEzP,IACtD,MAAM5X,EAAQG,IACR6c,GAAU,IAAAnW,QAAO,MACjBygB,GAAe,IAAAzgB,QAAO,MACtB0gB,GAAqB,IAAA1gB,QAAOxD,EAAKygB,gBACjC0D,GAAqB,IAAA3gB,QAAOxD,EAAK6gB,gBACjCuD,GAAW,IAAA5gB,QAAOmgB,GAClBU,EAAgBL,KAAmBhkB,EAAKI,UAAUkkB,aAqCxD,OApCA,IAAAtjB,WAAU,MACF2Y,EAAQpW,SAAYvD,EAAKukB,QAAYF,GAAiBJ,EAAa1gB,UAAYoW,EAAQpW,UACnF0gB,EAAa1gB,SACbgR,GAAgBG,UAAUuP,EAAa1gB,SAE3CgR,GAAgBE,QAAQkF,EAAQpW,SAChC0gB,EAAa1gB,QAAUoW,EAAQpW,UAEpC,CAAC8gB,EAAerkB,EAAKukB,UACxB,IAAAvjB,WAAU,IACC,KACCijB,EAAa1gB,UACbgR,GAAgBG,UAAUuP,EAAa1gB,SACvC0gB,EAAa1gB,QAAU,OAGhC,KACH,IAAAvC,WAAU,KACN,GAAI2Y,EAAQpW,QAAS,CAKjB,MAAMihB,EAAcJ,EAAS7gB,UAAYogB,EACnCc,EAAmBP,EAAmB3gB,UAAYvD,EAAKygB,eACvDiE,EAAmBP,EAAmB5gB,UAAYvD,EAAK6gB,gBACzD2D,GAAeC,GAAoBC,KACnCN,EAAS7gB,QAAUogB,EACnBO,EAAmB3gB,QAAUvD,EAAKygB,eAClC0D,EAAmB5gB,QAAUvD,EAAK6gB,eAClClkB,EACKK,WACAgmB,oBAAoB,IAAI5Y,IAAI,CAAC,CAACpK,EAAKvB,GAAI,CAAEA,GAAIuB,EAAKvB,GAAIkmB,YAAahL,EAAQpW,QAASqhB,OAAO,OAExG,GACD,CAAC5kB,EAAKvB,GAAIklB,EAAU3jB,EAAKygB,eAAgBzgB,EAAK6gB,iBAC1ClH,CACX,CAyBoBkL,CAAgB,CAAE7kB,OAAM2jB,WAAUK,gBAAezP,mBAC3DtJ,EAAW+O,GAAQ,CACrBL,UACAM,SAAUja,EAAKukB,SAAWV,EAC1B3J,kBACAC,eAAgBna,EAAK8kB,WACrB1S,OAAQ3T,EACR2b,eACAC,sBAEE+G,EAAoBxG,KAC1B,GAAI5a,EAAKukB,OACL,OAAO,KAEX,MAAMQ,GAAiB,QAAkB/kB,GACnCglB,EA3NV,SAAsChlB,GAClC,YAAoC8K,IAAhC9K,EAAKI,UAAUkkB,aACR,CACH/mB,MAAOyC,EAAKzC,OAASyC,EAAKilB,cAAgBjlB,EAAK7C,OAAOI,MACtDC,OAAQwC,EAAKxC,QAAUwC,EAAKklB,eAAiBllB,EAAK7C,OAAOK,QAG1D,CACHD,MAAOyC,EAAKzC,OAASyC,EAAK7C,OAAOI,MACjCC,OAAQwC,EAAKxC,QAAUwC,EAAK7C,OAAOK,OAE3C,CAgN6B2nB,CAA6BnlB,GAChDolB,EAAmBhL,GAAgByJ,GAAepM,GAAWyL,GAAgBC,GAAeC,EAC5FiC,EAAsBnC,EACrBxd,GAAUwd,EAAaxd,EAAO,IAAKtF,EAAUC,gBAC9CyK,EACAwa,EAAqBnC,EACpBzd,GAAUyd,EAAYzd,EAAO,IAAKtF,EAAUC,gBAC7CyK,EACAya,EAAsBnC,EACrB1d,GAAU0d,EAAa1d,EAAO,IAAKtF,EAAUC,gBAC9CyK,EACA0a,EAAuBzN,EACtBrS,GAAUqS,EAAcrS,EAAO,IAAKtF,EAAUC,gBAC/CyK,EACA2a,EAAuBpC,EACtB3d,GAAU2d,EAAc3d,EAAO,IAAKtF,EAAUC,gBAC/CyK,EA+DN,OAAQ,IAAAtM,KAAI,MAAO,CAAES,WAAW,OAAG,CAC3B,mBACA,oBAAoB0kB,IACpB,CAEI,CAAC3gB,GAAiB6gB,GAEtB7jB,EAAKf,UACL,CACIiB,SAAUF,EAAKE,SACfiZ,WAAYiB,EACZsL,OAAQjC,EACR5L,UAAWgM,EACX5Y,cAEJ9L,IAAKwa,EAASxc,MAAO,CACrBwoB,OAAQvlB,EAAUwlB,EAClBxd,UAAW,aAAahI,EAAUoR,iBAAiB5P,OAAOxB,EAAUoR,iBAAiB3P,OACrFgkB,cAAeT,EAAmB,MAAQ,OAC1CU,WAAY9B,EAAgB,UAAY,YACrChkB,EAAK7C,SACL6nB,GACJ,UAAWvmB,EAAI,cAAe,YAAYA,IAAMykB,aAAcmC,EAAqBlC,YAAamC,EAAoBlC,aAAcmC,EAAqBxN,cAAeyN,EAAsB/N,QApFtK/R,IACzB,MAAM,kBAAEqgB,EAAiB,kBAAEC,GAAsBrpB,EAAMK,WACnDod,KAAkB2L,IAAsBlC,GAAemC,EAAoB,IAK3EvM,GAAgB,CACZhb,KACA9B,QACAgd,YAGJlC,GACAA,EAAQ/R,EAAO,IAAKtF,EAAUC,YAsE2LgjB,cAAeoC,EAAsBjE,UAAWsC,EAnE9Ppe,IACf,KAAI,QAAeA,EAAM2S,eAAgBzZ,EAGzC,GAAI,KAAqB2I,SAAS7B,EAAMiB,MAAQyT,EAAc,CAC1D,MAAMV,EAAyB,WAAdhU,EAAMiB,IACvB8S,GAAgB,CACZhb,KACA9B,QACA+c,WACAC,WAER,MACK,GAAIkK,GAAe7jB,EAAKE,UAAYuhB,OAAOC,UAAUC,eAAeC,KAAK1B,GAAexa,EAAMiB,KAAM,CAErGjB,EAAMa,iBACN,MAAM,gBAAElI,GAAoB1B,EAAMK,WAClCL,EAAMM,SAAS,CACXkB,gBAAiBE,EAAgB,wCAAwC,CACrEid,UAAW5V,EAAMiB,IAAIxB,QAAQ,QAAS,IAAI8gB,cAC1CrkB,IAAKxB,EAAUoR,iBAAiB5P,EAChCC,IAAKzB,EAAUoR,iBAAiB3P,MAGxCuf,EAAkB,CACd9F,UAAW4E,GAAcxa,EAAMiB,KAC/B4U,OAAQ7V,EAAMG,SAAW,EAAI,GAErC,QAuCuSiF,EAAWyW,SAAUuC,EAAc,OAAIhZ,EAAWob,QAASpC,EArCtV,KACZ,GAAIllB,IAAwB+a,EAAQpW,SAAS4iB,QAAQ,kBACjD,OAEJ,MAAM,UAAE/d,EAAS,MAAE7K,EAAK,OAAEC,EAAM,mBAAE4oB,EAAkB,UAAExd,GAAcjM,EAAMK,WAC1E,IAAKopB,EACD,QAEmB,QAAe,IAAIhc,IAAI,CAAC,CAAC3L,EAAIuB,KAAS,CAAE4B,EAAG,EAAGC,EAAG,EAAGtE,QAAOC,UAAU4K,GAAW,GAAMpB,OAAS,GAElH4B,EAAU5I,EAAK1C,SAASsE,EAAImjB,EAAexnB,MAAQ,EAAGyC,EAAK1C,SAASuE,EAAIkjB,EAAevnB,OAAS,EAAG,CAC/FsE,KAAMsG,EAAU,WA0BkW0C,EAAWub,KAAMrmB,EAAKsmB,WAAaxC,EAAc,aAAUhZ,GAAY,uBAAwB,OAAQ,mBAAoBlM,OAAsBkM,EAAY,GAAG/M,KAAsBQ,IAAQ,aAAcyB,EAAKumB,aAAcvmB,EAAKwmB,cAAe9nB,UAAU,IAAAF,KAAIlC,GAAU,CAAE2R,MAAOxP,EAAIC,UAAU,IAAAF,KAAIolB,EAAe,CAAEnlB,GAAIA,EAAIuT,KAAMhS,EAAKgS,KAAMzH,KAAMoZ,EAAU8C,kBAAmBrmB,EAAUoR,iBAAiB5P,EAAG8kB,kBAAmBtmB,EAAUoR,iBAAiB3P,EAAG3B,SAAUF,EAAKE,WAAY,EAAOiZ,WAAYiB,EAAcvC,UAAWgM,EAAa8C,UAAW3mB,EAAK2mB,YAAa,EAAMxK,cAAeA,EAAesE,eAAgBzgB,EAAKygB,eAAgBI,eAAgB7gB,EAAK6gB,eAAgB5V,SAAUA,EAAU6Z,WAAY9kB,EAAK8kB,WAAYa,OAAQvlB,EAAUwlB,EAAGhX,SAAU5O,EAAK4O,YAAamW,OACroC,CAEA,MAAM6B,GAAc1oB,IAAM,CACtB4c,eAAgB5c,EAAE4c,eAClBwI,iBAAkBplB,EAAEolB,iBACpBC,eAAgBrlB,EAAEqlB,eAClBxgB,mBAAoB7E,EAAE6E,mBACtBuR,QAASpW,EAAEoW,UAEf,SAASuS,GAAsB1jB,GAC3B,MAAM,eAAE2X,EAAc,iBAAEwI,EAAgB,eAAEC,EAAc,mBAAExgB,EAAkB,QAAEuR,GAAY9X,EAASoqB,GAAY,KACzGE,EAAUlE,GAAkBzf,EAAM4jB,2BAClCxS,EAxOV,WACI,MAAMyO,EAAsBxmB,EAASumB,KAC9BxO,IAAkB,IAAA/P,UAAS,IACA,oBAAnBgQ,eACA,KAEJ,IAAIA,eAAgBvI,IACvB,MAAM+a,EAAU,IAAI5c,IACpB6B,EAAQ5K,QAAS4lB,IACb,MAAMxoB,EAAKwoB,EAAMvnB,OAAOwnB,aAAa,WACrCF,EAAQxc,IAAI/L,EAAI,CACZA,KACAkmB,YAAasC,EAAMvnB,OACnBklB,OAAO,MAGf5B,EAAoBgE,MAQ5B,OALA,IAAAhmB,WAAU,IACC,KACHuT,GAAgB4S,cAErB,CAAC5S,IACGA,CACX,CA+M2B6S,GACvB,OAAQ,IAAA5oB,KAAI,MAAO,CAAES,UAAW,oBAAqB9B,MAAO2V,GAAgBpU,SAAUooB,EAAQjmB,IAAKuR,IA2B3F,IAAA5T,KAAIykB,GAAa,CAAExkB,GAAI2T,EAAQoR,UAAWrgB,EAAMqgB,UAAW3I,WAAY1X,EAAM0X,WAAYpD,QAAStU,EAAMkkB,YAAanE,aAAc/f,EAAMmkB,iBAAkBnE,YAAahgB,EAAMokB,gBAAiBnE,aAAcjgB,EAAMqkB,iBAAkBzP,cAAe5U,EAAMskB,kBAAmBpE,cAAelgB,EAAMukB,kBAAmBxN,gBAAiB/W,EAAM+W,gBAAiBlX,eAAgBG,EAAMH,eAAgBzE,KAAM4E,EAAM5E,KAAMK,oBAAqBuE,EAAMvE,oBAAqB2V,eAAgBA,EAAgBuG,eAAgBA,EAAgBwI,iBAAkBA,EAAkBC,eAAgBA,EAAgBxgB,mBAAoBA,EAAoBsX,kBAAmBlX,EAAMkX,kBAAmB/F,QAASA,GAAWlC,KAE7rB,CACAyU,GAAsBjnB,YAAc,eACpC,MAAM+nB,IAAe,IAAA3L,MAAK6K,IAqC1B,MAaMe,GAAgB,CAClB,CAAC,KAAWC,OAdI,EAAGC,QAAQ,OAAQC,cAAc,MACzC,IAAAvpB,KAAI,WAAY,CAAErB,MAAO,CACzB6qB,OAAQF,EACRC,eACDE,cAAe,QAASC,eAAgB,QAASC,KAAM,OAAQC,OAAQ,mBAW9E,CAAC,KAAWC,aATU,EAAGP,QAAQ,OAAQC,cAAc,MAC/C,IAAAvpB,KAAI,WAAY,CAAErB,MAAO,CACzB6qB,OAAQF,EACRK,KAAML,EACNC,eACDE,cAAe,QAASC,eAAgB,QAASE,OAAQ,0BAmBpE,MAAME,GAAS,EAAG7pB,KAAI8L,OAAMud,QAAOvqB,QAAQ,KAAMC,SAAS,KAAM+qB,cAAc,cAAeR,cAAaS,SAAS,yBAC/G,MAAMC,EAdV,SAAyBle,GACrB,MAAM5N,EAAQG,IASd,OARe,IAAAC,SAAQ,IACE0kB,OAAOC,UAAUC,eAAeC,KAAKgG,GAAerd,GAKlEqd,GAAcrd,IAHjB5N,EAAMK,WAAWsX,UAAU,MAAO,KAAwB,SAAE/J,IACrD,MAGZ,CAACA,GAER,CAGmBme,CAAgBne,GAC/B,OAAKke,GAGG,IAAAjqB,KAAI,SAAU,CAAES,UAAW,wBAAyBR,GAAIA,EAAIkqB,YAAa,GAAGprB,IAASqrB,aAAc,GAAGprB,IAAUqrB,QAAS,gBAAiBN,YAAaA,EAAaC,OAAQA,EAAQM,KAAM,IAAKC,KAAM,IAAKrqB,UAAU,IAAAF,KAAIiqB,EAAQ,CAAEX,MAAOA,EAAOC,YAAaA,MAF1P,MASTiB,GAAoB,EAAGC,eAAc1qB,WACvC,MAAM4C,EAAQ3E,EAAU0B,GAAMA,EAAEiD,OAC1BsX,EAAqBjc,EAAU0B,GAAMA,EAAEua,oBACvCyQ,GAAU,IAAAnsB,SAAQ,KACJ,QAAgBoE,EAAO,CACnC1C,GAAIF,EACJ0qB,eACAE,mBAAoB1Q,GAAoB2Q,YACxCC,iBAAkB5Q,GAAoB6Q,YAG3C,CAACnoB,EAAOsX,EAAoBla,EAAM0qB,IACrC,OAAKC,EAAQliB,QAGL,IAAAxI,KAAI,MAAO,CAAES,UAAW,qBAAsB,cAAe,OAAQP,UAAU,IAAAF,KAAI,OAAQ,CAAEE,SAAUwqB,EAAQroB,IAAK0oB,IAAY,IAAA/qB,KAAI8pB,GAAQ,CAAE7pB,GAAI8qB,EAAO9qB,GAAI8L,KAAMgf,EAAOhf,KAAMud,MAAOyB,EAAOzB,MAAOvqB,MAAOgsB,EAAOhsB,MAAOC,OAAQ+rB,EAAO/rB,OAAQ+qB,YAAagB,EAAOhB,YAAaR,YAAawB,EAAOxB,YAAaS,OAAQe,EAAOf,QAAUe,EAAO9qB,SAFhV,MAIfuqB,GAAkBppB,YAAc,oBAChC,IAAI4pB,IAAsB,IAAAxN,MAAKgN,IAE/B,SAASS,IAAkB,EAAE7nB,EAAC,EAAEC,EAAC,MAAE8e,EAAK,WAAE+I,EAAU,YAAEC,GAAc,EAAI,aAAEC,EAAY,eAAEC,EAAiB,CAAC,EAAG,GAAE,oBAAEC,EAAsB,EAAC,SAAEprB,EAAQ,UAAEO,KAAcC,IAC9J,MAAO6qB,EAAcC,IAAmB,IAAAxlB,UAAS,CAAE5C,EAAG,EAAGC,EAAG,EAAGtE,MAAO,EAAGC,OAAQ,IAC3EysB,GAAkB,OAAG,CAAC,+BAAgChrB,IACtDirB,GAAc,IAAA1mB,QAAO,MAY3B,OAXA,IAAAxC,WAAU,KACN,GAAIkpB,EAAY3mB,QAAS,CACrB,MAAM4mB,EAAWD,EAAY3mB,QAAQ6mB,UACrCJ,EAAgB,CACZpoB,EAAGuoB,EAASvoB,EACZC,EAAGsoB,EAAStoB,EACZtE,MAAO4sB,EAAS5sB,MAChBC,OAAQ2sB,EAAS3sB,QAEzB,GACD,CAACmjB,IACCA,GAGG,IAAA9hB,MAAK,IAAK,CAAEuJ,UAAW,aAAaxG,EAAImoB,EAAaxsB,MAAQ,KAAKsE,EAAIkoB,EAAavsB,OAAS,KAAMyB,UAAWgrB,EAAiBnE,WAAYiE,EAAaxsB,MAAQ,UAAY,YAAa2B,EAAMR,SAAU,CAACirB,IAAgB,IAAAnrB,KAAI,OAAQ,CAAEjB,MAAOwsB,EAAaxsB,MAAQ,EAAIssB,EAAe,GAAIjoB,GAAIioB,EAAe,GAAIhoB,GAAIgoB,EAAe,GAAIrsB,OAAQusB,EAAavsB,OAAS,EAAIqsB,EAAe,GAAI5qB,UAAW,0BAA2B9B,MAAOysB,EAAcS,GAAIP,EAAqBQ,GAAIR,KAAyB,IAAAtrB,KAAI,OAAQ,CAAES,UAAW,wBAAyB4C,EAAGkoB,EAAavsB,OAAS,EAAG+sB,GAAI,QAASprB,IAAK+qB,EAAa/sB,MAAOusB,EAAYhrB,SAAUiiB,IAAUjiB,KAF/nB,IAGf,CACA+qB,GAAkB7pB,YAAc,WA2BhC,MAAM4qB,IAAW,IAAAxO,MAAKyN,IA6BtB,SAASgB,IAAS,KAAEC,EAAI,OAAEC,EAAM,OAAEC,EAAM,MAAEjK,EAAK,WAAE+I,EAAU,YAAEC,EAAW,aAAEC,EAAY,eAAEC,EAAc,oBAAEC,EAAmB,iBAAEe,EAAmB,MAAO1nB,IACnJ,OAAQ,IAAAtE,MAAK,EAAAC,SAAU,CAAEJ,SAAU,EAAC,IAAAF,KAAI,OAAQ,IAAK2E,EAAO2nB,EAAGJ,EAAMvC,KAAM,OAAQlpB,WAAW,OAAG,CAAC,wBAAyBkE,EAAMlE,cAAgB4rB,IAAqB,IAAArsB,KAAI,OAAQ,CAAEssB,EAAGJ,EAAMvC,KAAM,OAAQ4C,cAAe,EAAGhD,YAAa8C,EAAkB5rB,UAAW,iCAAoC0hB,IAAS,QAAUgK,KAAW,QAAUC,IAAW,IAAApsB,KAAIgsB,GAAU,CAAE5oB,EAAG+oB,EAAQ9oB,EAAG+oB,EAAQjK,MAAOA,EAAO+I,WAAYA,EAAYC,YAAaA,EAAaC,aAAcA,EAAcC,eAAgBA,EAAgBC,oBAAqBA,IAA0B,OACnjB,CAEA,SAASkB,IAAW,IAAEC,EAAG,GAAEC,EAAE,GAAEC,EAAE,GAAEC,EAAE,GAAEC,IACnC,OAAIJ,IAAQ,KAASK,MAAQL,IAAQ,KAASM,MACnC,CAAC,IAAOL,EAAKE,GAAKD,GAEtB,CAACD,EAAI,IAAOC,EAAKE,GAC5B,CAcA,SAASG,IAAoB,QAAEC,EAAO,QAAEC,EAAO,eAAEjL,EAAiB,KAASC,OAAM,QAAEiL,EAAO,QAAEC,EAAO,eAAE/K,EAAiB,KAAS5E,MAC3H,MAAO4P,EAAgBC,GAAkBd,GAAW,CAChDC,IAAKxK,EACLyK,GAAIO,EACJN,GAAIO,EACJN,GAAIO,EACJN,GAAIO,KAEDG,EAAgBC,GAAkBhB,GAAW,CAChDC,IAAKpK,EACLqK,GAAIS,EACJR,GAAIS,EACJR,GAAIK,EACJJ,GAAIK,KAEDf,EAAQC,EAAQqB,EAASC,IAAW,QAAoB,CAC3DT,UACAC,UACAC,UACAC,UACAC,iBACAC,iBACAC,iBACAC,mBAEJ,MAAO,CACH,IAAIP,KAAWC,MAAYG,KAAkBC,KAAkBC,KAAkBC,KAAkBL,KAAWC,IAC9GjB,EACAC,EACAqB,EACAC,EAER,CACA,SAASC,GAAuBlrB,GAE5B,OAAO,IAAA+a,MAAK,EAAGvd,KAAIgtB,UAASC,UAASC,UAASC,UAASnL,iBAAgBI,iBAAgBF,QAAO+I,aAAYC,cAAaC,eAAcC,iBAAgBC,sBAAqB3sB,QAAOmsB,YAAWF,cAAayB,uBACrM,MAAOH,EAAMC,EAAQC,GAAUY,GAAoB,CAC/CC,UACAC,UACAjL,iBACAkL,UACAC,UACA/K,mBAEEuL,EAAMnrB,EAAOorB,gBAAavhB,EAAYrM,EAC5C,OAAQ,IAAAD,KAAIisB,GAAU,CAAEhsB,GAAI2tB,EAAK1B,KAAMA,EAAMC,OAAQA,EAAQC,OAAQA,EAAQjK,MAAOA,EAAO+I,WAAYA,EAAYC,YAAaA,EAAaC,aAAcA,EAAcC,eAAgBA,EAAgBC,oBAAqBA,EAAqB3sB,MAAOA,EAAOmsB,UAAWA,EAAWF,YAAaA,EAAayB,iBAAkBA,KAE3U,CACA,MAAMyB,GAAmBH,GAAuB,CAAEE,YAAY,IACxDE,GAA2BJ,GAAuB,CAAEE,YAAY,IAItE,SAASG,GAAqBvrB,GAE1B,OAAO,IAAA+a,MAAK,EAAGvd,KAAIgtB,UAASC,UAASC,UAASC,UAASjL,QAAO+I,aAAYC,cAAaC,eAAcC,iBAAgBC,sBAAqB3sB,QAAOsjB,iBAAiB,KAASC,OAAQG,iBAAiB,KAAS5E,IAAKqN,YAAWF,cAAaqD,cAAa5B,uBACnP,MAAOH,EAAMC,EAAQC,IAAU,QAAkB,CAC7Ca,UACAC,UACAjL,iBACAkL,UACAC,UACA/K,iBACA6L,aAAcD,GAAaC,aAC3BC,OAAQF,GAAaE,OACrBC,aAAcH,GAAaG,eAEzBR,EAAMnrB,EAAOorB,gBAAavhB,EAAYrM,EAC5C,OAAQ,IAAAD,KAAIisB,GAAU,CAAEhsB,GAAI2tB,EAAK1B,KAAMA,EAAMC,OAAQA,EAAQC,OAAQA,EAAQjK,MAAOA,EAAO+I,WAAYA,EAAYC,YAAaA,EAAaC,aAAcA,EAAcC,eAAgBA,EAAgBC,oBAAqBA,EAAqB3sB,MAAOA,EAAOmsB,UAAWA,EAAWF,YAAaA,EAAayB,iBAAkBA,KAE3U,CApBAyB,GAAiB1sB,YAAc,mBAC/B2sB,GAAyB3sB,YAAc,2BA2CvC,MAAMitB,GAAiBL,GAAqB,CAAEH,YAAY,IAIpDS,GAAyBN,GAAqB,CAAEH,YAAY,IAIlE,SAASU,GAAe9rB,GAEpB,OAAO,IAAA+a,MAAK,EAAGvd,QAAO0E,MAClB,MAAMipB,EAAMnrB,EAAOorB,gBAAavhB,EAAYrM,EAC5C,OAAQ,IAAAD,KAAIquB,GAAgB,IAAK1pB,EAAO1E,GAAI2tB,EAAKK,aAAa,IAAA1vB,SAAQ,KAAM,CAAG2vB,aAAc,EAAGC,OAAQxpB,EAAMspB,aAAaE,SAAW,CAACxpB,EAAMspB,aAAaE,YAElK,CATAE,GAAejtB,YAAc,iBAC7BktB,GAAuBltB,YAAc,yBAgCrC,MAAMotB,GAAWD,GAAe,CAAEV,YAAY,IAIxCY,GAAmBF,GAAe,CAAEV,YAAY,IAItD,SAASa,GAAmBjsB,GAExB,OAAO,IAAA+a,MAAK,EAAGvd,KAAIgtB,UAASC,UAASC,UAASC,UAASjL,QAAO+I,aAAYC,cAAaC,eAAcC,iBAAgBC,sBAAqB3sB,QAAOmsB,YAAWF,cAAayB,uBACrK,MAAOH,EAAMC,EAAQC,IAAU,QAAgB,CAAEa,UAASC,UAASC,UAASC,YACtEQ,EAAMnrB,EAAOorB,gBAAavhB,EAAYrM,EAC5C,OAAQ,IAAAD,KAAIisB,GAAU,CAAEhsB,GAAI2tB,EAAK1B,KAAMA,EAAMC,OAAQA,EAAQC,OAAQA,EAAQjK,MAAOA,EAAO+I,WAAYA,EAAYC,YAAaA,EAAaC,aAAcA,EAAcC,eAAgBA,EAAgBC,oBAAqBA,EAAqB3sB,MAAOA,EAAOmsB,UAAWA,EAAWF,YAAaA,EAAayB,iBAAkBA,KAE3U,CAVAmC,GAASptB,YAAc,WACvBqtB,GAAiBrtB,YAAc,mBA+B/B,MAAMutB,GAAeD,GAAmB,CAAEb,YAAY,IAIhDe,GAAuBF,GAAmB,CAAEb,YAAY,IAI9D,SAASgB,GAAiBpsB,GAEtB,OAAO,IAAA+a,MAAK,EAAGvd,KAAIgtB,UAASC,UAASC,UAASC,UAASnL,iBAAiB,KAASC,OAAQG,iBAAiB,KAAS5E,IAAK0E,QAAO+I,aAAYC,cAAaC,eAAcC,iBAAgBC,sBAAqB3sB,QAAOmsB,YAAWF,cAAaqD,cAAa5B,uBACnP,MAAOH,EAAMC,EAAQC,IAAU,QAAc,CACzCa,UACAC,UACAjL,iBACAkL,UACAC,UACA/K,iBACAyM,UAAWb,GAAaa,YAEtBlB,EAAMnrB,EAAOorB,gBAAavhB,EAAYrM,EAC5C,OAAQ,IAAAD,KAAIisB,GAAU,CAAEhsB,GAAI2tB,EAAK1B,KAAMA,EAAMC,OAAQA,EAAQC,OAAQA,EAAQjK,MAAOA,EAAO+I,WAAYA,EAAYC,YAAaA,EAAaC,aAAcA,EAAcC,eAAgBA,EAAgBC,oBAAqBA,EAAqB3sB,MAAOA,EAAOmsB,UAAWA,EAAWF,YAAaA,EAAayB,iBAAkBA,KAE3U,CAlBAsC,GAAavtB,YAAc,eAC3BwtB,GAAqBxtB,YAAc,uBAyCnC,MAAM2tB,GAAaF,GAAiB,CAAEhB,YAAY,IAI5CmB,GAAqBH,GAAiB,CAAEhB,YAAY,IAC1DkB,GAAW3tB,YAAc,aACzB4tB,GAAmB5tB,YAAc,qBAEjC,MAAM6tB,GAAmB,CACrB7M,QAAS4M,GACTE,SAAUN,GACVO,KAAMV,GACNW,WAAYd,GACZe,aAActB,IAEZuB,GAAe,CACjBrC,QAAS,KACTC,QAAS,KACTC,QAAS,KACTC,QAAS,KACTnL,eAAgB,KAChBI,eAAgB,MAGdkN,GAAS,CAACnsB,EAAGosB,EAAO1wB,IAClBA,IAAa,KAASguB,KACf1pB,EAAIosB,EACX1wB,IAAa,KAASiuB,MACf3pB,EAAIosB,EACRpsB,EAELqsB,GAAS,CAACpsB,EAAGmsB,EAAO1wB,IAClBA,IAAa,KAAS2e,IACfpa,EAAImsB,EACX1wB,IAAa,KAASojB,OACf7e,EAAImsB,EACRnsB,EAELqsB,GAAuB,0BAI7B,SAASC,IAAW,SAAE7wB,EAAQ,QAAE8wB,EAAO,QAAEC,EAAO,OAAEC,EAAS,GAAE,YAAE/R,EAAW,aAAE2G,EAAY,WAAEqL,EAAU,KAAEhkB,IAClG,OAAQ,IAAA/L,KAAI,SAAU,CAAE+d,YAAaA,EAAa2G,aAAcA,EAAcqL,WAAYA,EAAYtvB,WAAW,OAAG,CAACivB,GAAsB,GAAGA,MAAwB3jB,MAAUikB,GAAIT,GAAOK,EAASE,EAAQhxB,GAAWmxB,GAAIR,GAAOI,EAASC,EAAQhxB,GAAWoxB,EAAGJ,EAAQtG,OAAQ,cAAeG,KAAM,eAC1S,CAEA,SAASwG,IAAkB,gBAAEC,EAAe,gBAAEC,EAAe,KAAEvuB,EAAI,QAAEmrB,EAAO,QAAEC,EAAO,QAAEC,EAAO,QAAEC,EAAO,eAAEnL,EAAc,eAAEI,EAAc,YAAEiO,EAAW,iBAAEC,EAAgB,eAAEC,EAAc,gBAAEC,EAAe,eAAEC,IACrM,MAAMvyB,EAAQG,IACRqyB,EAAoB,CAACzpB,EAAO0pB,KAE9B,GAAqB,IAAjB1pB,EAAMwS,OACN,OAEJ,MAAM,iBAAE6F,EAAgB,QAAE1U,EAAO,kBAAE6S,EAAiB,eAAEkB,EAAc,iBAAEY,EAAgB,IAAE7K,EAAG,eAAEiL,EAAc,aAAEC,EAAY,iBAAEF,EAAgB,WAAEle,EAAY1B,KAAM0f,EAAM,MAAEC,EAAK,iBAAEI,GAAsB3hB,EAAMK,WACpMyf,EAAmC,WAAxB2S,EAAe7kB,KAWhC,KAAS0N,cAAcvS,EAAM2S,YAAa,CACtC0F,mBACAX,iBACAY,mBACA3U,UACAoJ,SAAU2c,EAAe3wB,GACzB2T,OAAQgd,EAAehd,OACvBnS,aACAwc,WACA4S,gBAAiBD,EAAe7kB,KAChC4I,MACA8K,SACAE,mBACAD,QACAhC,oBACAI,UArBmBnG,GAAe2Y,IAAcxuB,EAAM6V,GAsBtDiI,eArBoB,CAACkR,EAAQruB,KAC7BguB,GAAgB,GAChBF,IAAmBrpB,EAAOpF,EAAM8uB,EAAe7kB,MAC/C6T,IAAiBkR,EAAQruB,IAmBzBod,eACA2Q,eA5BoB,CAACO,EAAKhQ,KAC1B0P,GAAgB,GAChBD,IAAiBO,EAAKjvB,EAAM8uB,EAAe7kB,KAAMgV,IA2BjDjB,mBACAC,aAAc,IAAM5hB,EAAMK,WAAWoL,UACrCoW,cAAe,IAAM7hB,EAAMK,WAAWmZ,WAAWkH,WACjDqB,cAAe/hB,EAAMK,WAAW2hB,2BAKlC6Q,EAAwB,IAAMN,GAAe,GAC7CO,EAAsB,IAAMP,GAAe,GACjD,OAAQ,IAAArwB,MAAK,EAAAC,SAAU,CAAEJ,SAAU,GAAsB,IAApBkwB,GAAgD,WAApBA,KAAkC,IAAApwB,KAAI2vB,GAAY,CAAE7wB,SAAUmjB,EAAgB2N,QAAS3C,EAAS4C,QAAS3C,EAAS4C,OAAQO,EAAiBtS,YAJxK7W,GAAUypB,EAAkBzpB,EAAO,CAAE0M,OAAQ9R,EAAKZ,OAAQjB,GAAI6B,EAAKovB,cAAgB,KAAMnlB,KAAM,WAIkH2Y,aAAcsM,EAAuBjB,WAAYkB,EAAqBllB,KAAM,aAAmC,IAApBqkB,GAAgD,WAApBA,KAAkC,IAAApwB,KAAI2vB,GAAY,CAAE7wB,SAAUujB,EAAgBuN,QAASzC,EAAS0C,QAASzC,EAAS0C,OAAQO,EAAiBtS,YAHnd7W,GAAUypB,EAAkBzpB,EAAO,CAAE0M,OAAQ9R,EAAKse,OAAQngB,GAAI6B,EAAKqvB,cAAgB,KAAMplB,KAAM,WAG6Z2Y,aAAcsM,EAAuBjB,WAAYkB,EAAqBllB,KAAM,aAChnB,CAEA,SAASqlB,IAAY,GAAEnxB,EAAE,eAAEoxB,EAAc,mBAAEC,EAAkB,mBAAE/sB,EAAkB,QAAE0U,EAAO,cAAE4L,EAAa,cAAEtL,EAAa,aAAEmL,EAAY,YAAEC,EAAW,aAAEC,EAAY,gBAAEyL,EAAe,YAAEC,EAAW,iBAAEC,EAAgB,eAAEC,EAAc,KAAEzwB,EAAI,UAAEwxB,EAAS,eAAE/sB,EAAc,QAAEsR,EAAO,oBAAE1V,IACzQ,IAAI0B,EAAO9D,EAAU0B,GAAMA,EAAEqC,WAAWmK,IAAIjM,IAC5C,MAAMga,EAAqBjc,EAAU0B,GAAMA,EAAEua,oBAC7CnY,EAAOmY,EAAqB,IAAKA,KAAuBnY,GAASA,EACjE,IAAI0vB,EAAW1vB,EAAKiK,MAAQ,UACxB0lB,EAAgBF,IAAYC,IAAavC,GAAiBuC,QACxCllB,IAAlBmlB,IACA3b,IAAU,MAAO,KAAwB,SAAE0b,IAC3CA,EAAW,UACXC,EAAgBF,GAAqB,SAAKtC,GAAiB7M,SAE/D,MAAMkD,KAAiBxjB,EAAKyjB,WAAc8L,QAA4C,IAAnBvvB,EAAKyjB,WAClE6K,OAAyC,IAAhBE,IAC1BxuB,EAAK4vB,eAAkBJ,QAAoD,IAAvBxvB,EAAK4vB,eACxD9V,KAAkB9Z,EAAK6Y,YAAepW,QAAiD,IAApBzC,EAAK6Y,YACxEgX,GAAU,IAAA3sB,QAAO,OAChB4sB,EAAalB,IAAkB,IAAA1qB,WAAS,IACxC6rB,EAAcpB,IAAmB,IAAAzqB,WAAS,GAC3C7H,EAAQG,KACR,OAAE6oB,EAAM,QAAE8F,EAAO,QAAEC,EAAO,QAAEC,EAAO,QAAEC,EAAO,eAAEnL,EAAc,eAAEI,GAAmBrkB,GAAS,IAAAgR,aAAa7Q,IACzG,MAAM2zB,EAAa3zB,EAAMsD,WAAWyK,IAAIpK,EAAKse,QACvC2R,EAAa5zB,EAAMsD,WAAWyK,IAAIpK,EAAKZ,QAC7C,IAAK4wB,IAAeC,EAChB,MAAO,CACH5K,OAAQrlB,EAAKqlB,UACVmI,IAGX,MAAM0C,GAAe,QAAgB,CACjC/xB,KACA6xB,aACAC,aACAZ,aAAcrvB,EAAKqvB,cAAgB,KACnCD,aAAcpvB,EAAKovB,cAAgB,KACnCtS,eAAgBzgB,EAAMygB,eACtB9I,YASJ,MAAO,CACHqR,QARW,QAAsB,CACjCzlB,SAAUI,EAAKJ,SACfylB,OAAQrlB,EAAKqlB,OACb2K,aACAC,aACAE,gBAAiB9zB,EAAM+zB,0BAInBF,GAAgB1C,KAEzB,CAACxtB,EAAKse,OAAQte,EAAKZ,OAAQY,EAAKqvB,aAAcrvB,EAAKovB,aAAcpvB,EAAKJ,SAAUI,EAAKqlB,SAAU,KAC5FgL,GAAiB,IAAA5zB,SAAQ,IAAOuD,EAAK8oB,YAAc,UAAS,QAAY9oB,EAAK8oB,YAAa7qB,YAAYuM,EAAY,CAACxK,EAAK8oB,YAAa7qB,IACrIqyB,GAAe,IAAA7zB,SAAQ,IAAOuD,EAAKgpB,UAAY,UAAS,QAAYhpB,EAAKgpB,UAAW/qB,YAAYuM,EAAY,CAACxK,EAAKgpB,UAAW/qB,IACnI,GAAI+B,EAAKikB,QAAsB,OAAZkH,GAAgC,OAAZC,GAAgC,OAAZC,GAAgC,OAAZC,EAC3E,OAAO,KAEX,MAgBMiF,EAAoBxN,EACnB3d,IACC2d,EAAc3d,EAAO,IAAKpF,UAE5BwK,EACAgmB,EAAoB/Y,EACnBrS,IACCqS,EAAcrS,EAAO,IAAKpF,UAE5BwK,EACAimB,EAAmB7N,EAClBxd,IACCwd,EAAaxd,EAAO,IAAKpF,UAE3BwK,EACAkmB,EAAkB7N,EACjBzd,IACCyd,EAAYzd,EAAO,IAAKpF,UAE1BwK,EACAmmB,EAAmB7N,EAClB1d,IACC0d,EAAa1d,EAAO,IAAKpF,UAE3BwK,EAcN,OAAQ,IAAAtM,KAAI,MAAO,CAAErB,MAAO,CAAEwoB,UAAUjnB,UAAU,IAAAG,MAAK,IAAK,CAAEI,WAAW,OAAG,CAChE,mBACA,oBAAoB+wB,IACpB1vB,EAAKrB,UACL+D,EACA,CACI9C,SAAUI,EAAKJ,SACfgxB,SAAU5wB,EAAK4wB,SACfC,UAAW/W,IAAiB3C,EAC5B2Z,SAAUhB,EACVjX,WAAYiB,KAEhB3C,QAlES/R,IACjB,MAAM,iBAAE2rB,EAAgB,sBAAExX,EAAqB,qBAAEC,GAAyBnd,EAAMK,WAC5Eod,IACAzd,EAAMM,SAAS,CAAE0a,sBAAsB,IACnCrX,EAAKJ,UAAY4Z,GACjBD,EAAsB,CAAE3Y,MAAO,GAAIC,MAAO,CAACb,KAC3C6vB,EAAQ5sB,SAASwW,QAGjBsX,EAAiB,CAAC5yB,KAGtBgZ,GACAA,EAAQ/R,EAAOpF,IAqDW+iB,cAAewN,EAAmB9Y,cAAe+Y,EAAmB5N,aAAc6N,EAAkB5N,YAAa6N,EAAiB5N,aAAc6N,EAAkBzP,UAAWsC,EAzB5Lpe,IACf,IAAK9G,GAAuB,KAAqB2I,SAAS7B,EAAMiB,MAAQyT,EAAc,CAClF,MAAM,sBAAEP,EAAqB,iBAAEwX,GAAqB10B,EAAMK,WAC3B,WAAd0I,EAAMiB,KAEnBwpB,EAAQ5sB,SAASwW,OACjBF,EAAsB,CAAE1Y,MAAO,CAACb,MAGhC+wB,EAAiB,CAAC5yB,GAE1B,QAcqOqM,EAAWyW,SAAUuC,EAAc,OAAIhZ,EAAWub,KAAM/lB,EAAKgmB,WAAaxC,EAAc,QAAU,OAAQ,uBAAwB,OAAQ,UAAWrlB,EAAI,cAAe,YAAYA,IAAM,aAAiC,OAAnB6B,EAAKimB,eAAqBzb,EAAYxK,EAAKimB,WAAa,aAAajmB,EAAKse,aAAate,EAAKZ,SAAU,mBAAoBokB,EAAc,GAAG9lB,KAAsBO,SAASuM,EAAW3L,IAAKgxB,KAAY7vB,EAAKkmB,cAAe9nB,SAAU,EAAE2xB,IAAiB,IAAA7xB,KAAIyxB,EAAe,CAAExxB,GAAIA,EAAImgB,OAAQte,EAAKse,OAAQlf,OAAQY,EAAKZ,OAAQ6K,KAAMjK,EAAKiK,KAAMrK,SAAUI,EAAKJ,SAAUgxB,SAAU5wB,EAAK4wB,SAAU/X,WAAYiB,EAAcuM,UAAWrmB,EAAKqmB,YAAa,EAAMhG,MAAOrgB,EAAKqgB,MAAO+I,WAAYppB,EAAKopB,WAAYC,YAAarpB,EAAKqpB,YAAaC,aAActpB,EAAKspB,aAAcC,eAAgBvpB,EAAKupB,eAAgBC,oBAAqBxpB,EAAKwpB,oBAAqB2B,QAASA,EAASC,QAASA,EAASC,QAASA,EAASC,QAASA,EAASnL,eAAgBA,EAAgBI,eAAgBA,EAAgB7O,KAAM1R,EAAK0R,KAAM7U,MAAOmD,EAAKnD,MAAOm0B,eAAgBhxB,EAAKqvB,aAAc4B,eAAgBjxB,EAAKovB,aAActG,YAAauH,EAAgBrH,UAAWsH,EAAcnE,YAAa,gBAAiBnsB,EAAOA,EAAKmsB,iBAAc3hB,EAAW+f,iBAAkBvqB,EAAKuqB,mBAAsB+D,IAAoB,IAAApwB,KAAImwB,GAAmB,CAAEruB,KAAMA,EAAMsuB,gBAAiBA,EAAiBC,gBAAiBA,EAAiBC,YAAaA,EAAaC,iBAAkBA,EAAkBC,eAAgBA,EAAgBvD,QAASA,EAASC,QAASA,EAASC,QAASA,EAASC,QAASA,EAASnL,eAAgBA,EAAgBI,eAAgBA,EAAgBqO,eAAgBA,EAAgBD,gBAAiBA,QACn2D,CAEA,MAAMuC,GAActzB,IAAM,CACtB2xB,eAAgB3xB,EAAE2xB,eAClBC,mBAAoB5xB,EAAE4xB,mBACtB/sB,mBAAoB7E,EAAE6E,mBACtBqa,eAAgBlf,EAAEkf,eAClB9I,QAASpW,EAAEoW,UAEf,SAASmd,IAAsB,mBAAEC,EAAkB,0BAAE3K,EAAyB,KAAExoB,EAAI,UAAEwxB,EAAS,eAAE/sB,EAAc,YAAE8rB,EAAW,kBAAEgC,EAAiB,iBAAEC,EAAgB,gBAAEC,EAAe,iBAAEC,EAAgB,YAAEU,EAAW,gBAAE9C,EAAe,kBAAEgC,EAAiB,iBAAE9B,EAAgB,eAAEC,EAAc,oBAAEpwB,IACrR,MAAM,eAAEixB,EAAc,mBAAEC,EAAkB,mBAAE/sB,EAAkB,QAAEuR,GAAY9X,EAASg1B,GAAY,KAC3FI,GA1nBiB/O,EA0nBWkE,EAznBlBvqB,GAAS,IAAAgR,aAAatP,IAClC,IAAK2kB,EACD,OAAO3kB,EAAEiD,MAAMN,IAAKP,GAASA,EAAK7B,IAEtC,MAAMozB,EAAiB,GACvB,GAAI3zB,EAAEX,OAASW,EAAEV,OACb,IAAK,MAAM8C,KAAQpC,EAAEiD,MAAO,CACxB,MAAMmvB,EAAapyB,EAAE+B,WAAWyK,IAAIpK,EAAKse,QACnC2R,EAAaryB,EAAE+B,WAAWyK,IAAIpK,EAAKZ,QACrC4wB,GACAC,IACA,QAAc,CACVD,aACAC,aACAhzB,MAAOW,EAAEX,MACTC,OAAQU,EAAEV,OACV4K,UAAWlK,EAAEkK,aAEjBypB,EAAe1xB,KAAKG,EAAK7B,GAEjC,CAEJ,OAAOozB,GACR,CAAChP,IAAqB,MAxB7B,IAA2BA,EA2nBvB,OAAQ,IAAAhkB,MAAK,MAAO,CAAEI,UAAW,oBAAqBP,SAAU,EAAC,IAAAF,KAAIgrB,GAAqB,CAAEP,aAAcyI,EAAoBnzB,KAAMA,IAASqzB,EAAQ/wB,IAAKpC,IACtI,IAAAD,KAAIoxB,GAAa,CAAEnxB,GAAIA,EAAIoxB,eAAgBA,EAAgBC,mBAAoBA,EAAoB/sB,mBAAoBA,EAAoBC,eAAgBA,EAAgB8rB,YAAaA,EAAa/W,cAAe+Y,EAAmB5N,aAAc6N,EAAkB5N,YAAa6N,EAAiB5N,aAAc6N,EAAkBxZ,QAASka,EAAa9C,gBAAiBA,EAAiBxL,cAAewN,EAAmB9B,iBAAkBA,EAAkBC,eAAgBA,EAAgBzwB,KAAMA,EAAM+V,QAASA,EAASyb,UAAWA,EAAWnxB,oBAAqBA,GAAuBH,MAE3lB,CACAgzB,GAAsB7xB,YAAc,eACpC,MAAMkyB,IAAe,IAAA9V,MAAKyV,IAEpBM,GAAc7zB,GAAM,aAAaA,EAAEkK,UAAU,QAAQlK,EAAEkK,UAAU,eAAelK,EAAEkK,UAAU,MAClG,SAAS4pB,IAAS,SAAEtzB,IAChB,MAAM0J,EAAY5L,EAASu1B,IAC3B,OAAQ,IAAAvzB,KAAI,MAAO,CAAES,UAAW,8DAA+D9B,MAAO,CAAEiL,aAAa1J,SAAUA,GACnI,CAkBA,MAAMuzB,GAAchV,GAAUA,EAAMvV,SAASwqB,aAmB7C,SAASC,GAAgBj0B,GACrB,OAAOA,EAAEiY,WAAWC,WACd,IAAKlY,EAAEiY,WAAYic,IAAI,QAAqBl0B,EAAEiY,WAAWic,GAAIl0B,EAAEkK,YAC/D,IAAKlK,EAAEiY,WACjB,CAwCA,SAASkc,GAAcC,GACnB,MAAMC,EAxCV,SAAqBD,GACjB,GAAIA,EAKA,OAJ0Bp0B,IACtB,MAAMiY,EAAagc,GAAgBj0B,GACnC,OAAOo0B,EAAmBnc,IAIlC,OAAOgc,EACX,CA+B6BK,CAAYF,GACrC,OAAO91B,EAAS+1B,EAAkB,IACtC,CAEA,MAAME,GAAcv0B,IAAM,CACtBolB,iBAAkBplB,EAAEolB,iBACpB/F,QAASrf,EAAEiY,WAAWoH,QACtBnH,WAAYlY,EAAEiY,WAAWC,WACzB7Y,MAAOW,EAAEX,MACTC,OAAQU,EAAEV,SAEd,SAASk1B,IAAsB,eAAE5f,EAAc,MAAE3V,EAAK,KAAEoN,EAAI,UAAEooB,IAC1D,MAAM,iBAAErP,EAAgB,MAAE/lB,EAAK,OAAEC,EAAM,QAAE+f,EAAO,WAAEnH,GAAe5Z,EAASi2B,GAAY,KAEtF,SAD4Bl1B,GAAS+lB,GAAoBlN,IAIjD,IAAA5X,KAAI,MAAO,CAAErB,MAAO2V,EAAgBvV,MAAOA,EAAOC,OAAQA,EAAQyB,UAAW,mDAAoDP,UAAU,IAAAF,KAAI,IAAK,CAAES,WAAW,OAAG,CAAC,0BAA0B,QAAoBse,KAAY7e,UAAU,IAAAF,KAAIo0B,GAAgB,CAAEz1B,MAAOA,EAAOoN,KAAMA,EAAMsoB,gBAAiBF,EAAWpV,QAASA,QAF3T,IAGf,CACA,MAAMqV,GAAiB,EAAGz1B,QAAOoN,OAAO,KAAmBuoB,OAAQD,kBAAiBtV,cAChF,MAAM,WAAEnH,EAAU,KAAE/D,EAAI,SAAE0gB,EAAQ,WAAE1V,EAAU,aAAE2V,EAAY,GAAEZ,EAAE,OAAEa,EAAM,SAAE3V,EAAQ,WAAE2C,GAAeoS,KACnG,IAAKjc,EACD,OAEJ,GAAIyc,EACA,OAAQ,IAAAr0B,KAAIq0B,EAAiB,CAAEK,mBAAoB3oB,EAAM4oB,oBAAqBh2B,EAAO41B,SAAUA,EAAU1V,WAAYA,EAAY+V,MAAO/gB,EAAKzQ,EAAGyxB,MAAOhhB,EAAKxQ,EAAGyxB,IAAKlB,EAAGxwB,EAAG2xB,IAAKnB,EAAGvwB,EAAGmxB,aAAcA,EAAc/S,WAAYA,EAAYuT,kBAAkB,QAAoBjW,GAAU0V,OAAQA,EAAQ3V,SAAUA,IAEvT,IAAIoN,EAAO,GACX,MAAM+I,EAAa,CACfhI,QAASpZ,EAAKzQ,EACd8pB,QAASrZ,EAAKxQ,EACd4e,eAAgBuS,EAChBrH,QAASyG,EAAGxwB,EACZgqB,QAASwG,EAAGvwB,EACZgf,eAAgBZ,GAEpB,OAAQ1V,GACJ,KAAK,KAAmBuoB,QACnBpI,IAAQ,QAAc+I,GACvB,MACJ,KAAK,KAAmBC,cACnBhJ,GAAQc,GAAoBiI,GAC7B,MACJ,KAAK,KAAmBE,MACnBjJ,IAAQ,QAAkB,IACpB+I,EACH/G,aAAc,IAElB,MACJ,KAAK,KAAmBkH,YACnBlJ,IAAQ,QAAkB+I,GAC3B,MACJ,SACK/I,IAAQ,QAAgB+I,GAEjC,OAAO,IAAAj1B,KAAI,OAAQ,CAAEssB,EAAGJ,EAAMvC,KAAM,OAAQlpB,UAAW,8BAA+B9B,MAAOA,KAEjGy1B,GAAehzB,YAAc,iBAE7B,MAAMi0B,GAAa,CAAC,EAEpB,SAASC,GAA0BC,EAAkBF,KAChC,IAAArwB,QAAOuwB,GACVj3B,KACd,IAAAkE,WAAU,KACF,GAUL,CAAC+yB,GACR,CAkBA,SAASC,IAAmB,UAAExQ,EAAS,UAAEuM,EAAS,OAAEkE,EAAM,YAAE5M,EAAW,YAAEsK,EAAW,kBAAEjK,EAAiB,kBAAEmJ,EAAiB,iBAAEvJ,EAAgB,gBAAEC,EAAe,iBAAEC,EAAgB,kBAAEC,EAAiB,uBAAEtG,EAAsB,iBAAExK,EAAgB,eAAEC,EAAc,mBAAEsc,EAAkB,oBAAEC,EAAmB,wBAAEe,EAAuB,6BAAEC,EAA4B,iBAAElS,EAAgB,gBAAEvL,EAAe,cAAEF,EAAa,sBAAE0L,EAAqB,qBAAEC,EAAoB,sBAAErO,EAAqB,cAAEkO,EAAa,0BAAE+E,EAAyB,mBAAEhkB,EAAkB,gBAAEpB,EAAe,gBAAEgB,EAAe,QAAEE,EAAO,QAAEC,EAAO,iBAAEiR,EAAgB,mBAAE2d,EAAkB,aAAEpe,EAAY,YAAEC,EAAW,YAAEC,EAAW,iBAAEC,EAAgB,gBAAEC,EAAe,kBAAEE,EAAiB,UAAEC,EAAS,YAAEgD,EAAW,iBAAEE,EAAgB,gBAAEC,EAAe,iBAAEC,EAAgB,aAAEH,EAAY,kBAAEzD,EAAiB,kBAAEpQ,EAAiB,kBAAEoX,EAAiB,kBAAEyW,EAAiB,iBAAEC,EAAgB,gBAAEC,GAAe,iBAAEC,GAAgB,gBAAEpC,GAAe,YAAEC,GAAW,iBAAEC,GAAgB,eAAEC,GAAc,gBAAE9U,GAAe,iBAAElG,GAAgB,eAAEhR,GAAc,oBAAEpE,GAAmB,WAAEic,GAAU,KAAEtc,GAAI,SAAEgK,GAAQ,iBAAE0L,KAMtkC,OALA6f,GAA0BtQ,GAC1BsQ,GAA0B/D,GAjBZjzB,KACE,IAAA0G,SAAO,IACvB,IAAAxC,WAAU,OAUP,IAtKP,SAA0BizB,GACtB,MAAMG,EAAajmB,KACbkW,GAAgB,IAAA7gB,SAAO,IAC7B,IAAAxC,WAAU,MACDqjB,EAAc9gB,SAAW6wB,EAAW7lB,qBAAuB0lB,IAC5DI,WAAW,IAAMJ,EAAOG,GAAa,GACrC/P,EAAc9gB,SAAU,IAE7B,CAAC0wB,EAAQG,EAAW7lB,qBAC3B,CAoKI+lB,CAAiBL,GA3JrB,SAAyB1rB,GACrB,MAAM2pB,EAAe11B,EAASy1B,IACxBt1B,EAAQG,KACd,IAAAkE,WAAU,KACFuH,IACA2pB,IAAe3pB,GACf5L,EAAMM,SAAS,CAAEmL,UAAW,CAACG,EAAS3G,EAAG2G,EAAS1G,EAAG0G,EAASzG,UAEnE,CAACyG,EAAU2pB,GAElB,CAkJIqC,CAAgBhsB,KACR,IAAA/J,KAAImkB,GAAc,CAAE9L,YAAaA,EAAaE,iBAAkBA,EAAkBC,gBAAiBA,EAAiBC,iBAAkBA,EAAkB5D,kBAAmBA,EAAmByD,aAAcA,EAAc7T,kBAAmBA,EAAmB+e,cAAeA,EAAeC,iBAAkBA,EAAkBvL,gBAAiBA,EAAiBF,cAAeA,EAAeG,iBAAkBA,EAAkBC,eAAgBA,EAAgBsL,sBAAuBA,EAAuBC,qBAAsBA,EAAsBrO,sBAAuBA,EAAuB/Q,mBAAoBA,EAAoBuQ,aAAcA,EAAcC,YAAaA,EAAaK,kBAAmBA,EAAmBJ,YAAaA,EAAaC,iBAAkBA,EAAkBC,gBAAiBA,EAAiBG,UAAWA,EAAWlS,gBAAiBA,EAAiBgB,gBAAiBA,EAAiBE,QAASA,EAASC,QAASA,EAASqe,uBAAwBA,EAAwBpN,iBAAkBA,EAAkBmG,gBAAiBA,GAAiBlG,iBAAkBA,GAAkBhR,eAAgBA,GAAgBpE,oBAAqBA,GAAqBqV,iBAAkBA,GAAkBC,uBAAwB3L,GAAU7J,UAAU,IAAAG,MAAKmzB,GAAU,CAAEtzB,SAAU,EAAC,IAAAF,KAAIszB,GAAc,CAAE/B,UAAWA,EAAW4B,YAAaA,EAAad,kBAAmBA,EAAmB/B,YAAaA,GAAaC,iBAAkBA,GAAkBC,eAAgBA,GAAgBjI,0BAA2BA,EAA2B+J,kBAAmBA,EAAmBC,iBAAkBA,EAAkBC,gBAAiBA,GAAiBC,iBAAkBA,GAAkBpC,gBAAiBA,GAAiB6C,mBAAoBA,EAAoB1uB,eAAgBA,GAAgBpE,oBAAqBA,GAAqBL,KAAMA,MAAS,IAAAC,KAAIk0B,GAAuB,CAAEv1B,MAAOg2B,EAAqB5oB,KAAM2oB,EAAoBP,UAAWuB,EAAyBphB,eAAgBqhB,KAAiC,IAAA31B,KAAI,MAAO,CAAES,UAAW,oCAAqC,IAAAT,KAAImpB,GAAc,CAAEnE,UAAWA,EAAW6D,YAAaA,EAAaK,kBAAmBA,EAAmBJ,iBAAkBA,EAAkBC,gBAAiBA,EAAiBC,iBAAkBA,EAAkBC,kBAAmBA,EAAmBpN,kBAAmBA,EAAmB0M,0BAA2BA,EAA2B/jB,eAAgBA,GAAgBkX,gBAAiBA,GAAiBtb,oBAAqBA,GAAqBic,WAAYA,GAAYtc,KAAMA,MAAS,IAAAC,KAAI,MAAO,CAAES,UAAW,oCACniF,CACA+0B,GAAmBp0B,YAAc,YACjC,MAAM40B,IAAY,IAAAxY,MAAKgY,IAEjBS,GAAkB,EAAGvzB,QAAOC,QAAOiC,eAAcC,eAAc9F,QAAOC,SAAQkV,UAAS9O,iBAAgBf,UAAU,GAAKC,UAAU,EAAGF,aAAYiY,cAAgB,CAAC,KAClK,MAAM5a,EAAa,IAAImK,IACjBsZ,EAAe,IAAItZ,IACnBkI,EAAmB,IAAIlI,IACvB7J,EAAa,IAAI6J,IACjBsqB,EAAarxB,GAAgBlC,GAAS,GACtCwzB,EAAavxB,GAAgBlC,GAAS,GACtC0zB,EAAkBhyB,GAAc,CAAC,EAAG,GACpCiyB,EAAkBha,GAAc,MACtC,QAAuBvI,EAAkB/R,EAAYm0B,GACrD,MAAMI,GAAmB,QAAeH,EAAY10B,EAAYyjB,EAAc,CAC1E9gB,WAAYgyB,EACZ/Z,WAAYga,EACZE,sBAAsB,IAE1B,IAAI3sB,EAAY,CAAC,EAAG,EAAG,GACvB,GAAIsK,GAAWnV,GAASC,EAAQ,CAC5B,MAAMsL,GAAS,QAAuB7I,EAAY,CAC9CgF,OAASjF,MAAaA,EAAKzC,QAASyC,EAAKilB,eAAkBjlB,EAAKxC,SAAUwC,EAAKklB,kBAE7E,EAAEtjB,EAAC,EAAEC,EAAC,KAAEC,IAAS,QAAqBgH,EAAQvL,EAAOC,EAAQqF,EAASC,EAASc,GAAgBjG,SAAW,IAChHyK,EAAY,CAACxG,EAAGC,EAAGC,EACvB,CACA,MAAO,CACHvD,KAAM,IACNhB,MAAO,EACPC,OAAQ,EACR4K,YACAlH,MAAOyzB,EACPG,mBACA70B,aACAyjB,eACAviB,MAAOuzB,EACPn0B,aACA+R,mBACA5E,cAAe,KACfM,cAAe,KACfP,qBAAkC3C,IAAjB1H,EACjB2K,qBAAkCjD,IAAjBzH,EACjBqE,QAAS,KACT7E,UACAC,UACAH,gBAAiB,KACjBkY,WAAYga,EACZld,sBAAsB,EACtBzE,qBAAqB,EACrB0C,kBAAmB,KACnBwH,eAAgB,KAAeI,OAC/BnU,QAAS,KACTyL,cAAc,EACd9R,eAAgB,QAChBJ,WAAYgyB,EACZ5O,kBAAmB,EACnBrH,wBAAyB,EACzBxV,SAAU,CAAC,GAAI,IACfC,YAAY,EACZ0R,gBAAgB,EAChBwI,kBAAkB,EAClBC,gBAAgB,EAChBsM,gBAAgB,EAChBC,oBAAoB,EACpB/sB,oBAAoB,EACpBgyB,sBAAsB,EACtBrE,sBAAsB,EACtB3K,mBAAmB,EACnBjM,sBAAsB,EACtBnW,cAAe+O,IAAW,EAC1B9O,iBACA+O,gBAAiB,KACjBwD,WAAY,IAAK,MACjB+G,2BAA4B,KAC5BpB,gBAAgB,EAChB3d,gBAAiB,GACjB4f,kBAAkB,EAClBiX,mBAAmB,EACnB5O,oBAAoB,EACpB3H,aAAc,GACdT,iBAAkB,GAClB1J,QAAS,KACT4H,uBAAmBpR,EACnB1J,0BAA2B,GAC3B+R,IAAK,QACL8hB,OAAO,EACP52B,gBAAiB,OAInB62B,GAAc,EAAGh0B,QAAOC,QAAOiC,eAAcC,eAAc9F,QAAOC,SAAQkV,UAAS9O,iBAAgBf,UAASC,UAASF,aAAYiY,iBAAkB,OAAqB,CAACrQ,EAAKE,KAChLpC,eAAe6sB,IACX,MAAM,WAAEl1B,EAAU,QAAEyH,EAAO,eAAE9D,EAAc,gBAAE+O,EAAe,MAAEpV,EAAK,OAAEC,EAAM,QAAEqF,EAAO,QAAEC,GAAY4H,IAC7FhD,UAGC,QAAY,CACdxG,MAAOjB,EACP1C,QACAC,SACAkK,UACA7E,UACAC,WACDc,GACH+O,GAAiB7K,SAAQ,GAKzB0C,EAAI,CAAEmI,gBAAiB,OAC3B,CACA,MAAO,IACA8hB,GAAgB,CACfvzB,QACAC,QACA5D,QACAC,SACAkV,UACA9O,iBACAf,UACAC,UACAF,aACAiY,aACAzX,eACAC,iBAEJpB,SAAWf,IACP,MAAM,WAAEjB,EAAU,aAAEyjB,EAAY,WAAE9gB,EAAU,qBAAEmyB,EAAoB,cAAEpxB,GAAkB+G,IAShFoqB,GAAmB,QAAe5zB,EAAOjB,EAAYyjB,EAAc,CACrE9gB,aACAiY,aACAka,uBACAK,eAAe,IAEfzxB,GAAiBmxB,GACjBK,IACA3qB,EAAI,CAAEtJ,QAAO4zB,mBAAkBnxB,eAAe,EAAOC,oBAAgBkH,KAGrEN,EAAI,CAAEtJ,QAAO4zB,sBAGrB5yB,SAAWf,IACP,MAAM,iBAAEmR,EAAgB,WAAE/R,GAAemK,KACzC,QAAuB4H,EAAkB/R,EAAYY,GACrDqJ,EAAI,CAAErJ,WAEVqB,wBAAyB,CAACtB,EAAOC,KAC7B,GAAID,EAAO,CACP,MAAM,SAAEe,GAAayI,IACrBzI,EAASf,GACTsJ,EAAI,CAAEiD,iBAAiB,GAC3B,CACA,GAAItM,EAAO,CACP,MAAM,SAAEe,GAAawI,IACrBxI,EAASf,GACTqJ,EAAI,CAAEuD,iBAAiB,GAC3B,GAOJiV,oBAAsBgE,IAClB,MAAM,mBAAE3W,EAAkB,WAAEpQ,EAAU,aAAEyjB,EAAY,QAAEra,EAAO,WAAEzG,EAAU,WAAEiY,EAAU,MAAEoa,EAAK,cAAEtxB,GAAkB+G,KAC1G,QAAEV,EAAO,iBAAEqrB,IAAqB,QAAoBrO,EAAS/mB,EAAYyjB,EAAcra,EAASzG,EAAYiY,GAC7Gwa,KAGL,QAAwBp1B,EAAYyjB,EAAc,CAAE9gB,aAAYiY,eAC5DlX,GACAwxB,IACA3qB,EAAI,CAAE7G,eAAe,EAAOC,oBAAgBkH,KAI5CN,EAAI,CAAC,GAELR,GAAShD,OAAS,IACdiuB,GACAK,QAAQC,IAAI,mCAAoCvrB,GAEpDqG,IAAqBrG,MAG7B+Q,oBAAqB,CAACya,EAAevqB,GAAW,KAC5C,MAAMwqB,EAAuB,GACvBzrB,EAAU,IACV,WAAE/J,EAAU,mBAAEoQ,GAAuB3F,IAC3C,IAAK,MAAOjM,EAAIi3B,KAAaF,EAAe,CAExC,MAAMx1B,EAAOC,EAAWyK,IAAIjM,GACtBk3B,KAAkB31B,GAAM21B,cAAgB31B,GAAM4O,UAAY8mB,GAAUp4B,UACpEgN,EAAS,CACX7L,KACA8L,KAAM,WACNjN,SAAUq4B,EACJ,CACE/zB,EAAGiX,KAAK+c,IAAI,EAAGF,EAASp4B,SAASsE,GACjCC,EAAGgX,KAAK+c,IAAI,EAAGF,EAASp4B,SAASuE,IAEnC6zB,EAASp4B,SACf2N,YAEA0qB,GAAgB31B,EAAK4O,UACrB6mB,EAAqBt1B,KAAK,CACtB1B,KACAmQ,SAAU5O,EAAK4O,SACfinB,KAAM,IACCH,EAASt1B,UAAUoR,iBACtBjU,MAAOm4B,EAASvqB,SAAS5N,OAAS,EAClCC,OAAQk4B,EAASvqB,SAAS3N,QAAU,KAIhDwM,EAAQ7J,KAAKmK,EACjB,CACA,GAAImrB,EAAqBzuB,OAAS,EAAG,CACjC,MAAM,aAAE0c,EAAY,WAAE9gB,GAAe8H,IAC/BorB,GAAsB,QAAmBL,EAAsBx1B,EAAYyjB,EAAc9gB,GAC/FoH,EAAQ7J,QAAQ21B,EACpB,CACAzlB,EAAmBrG,IAEvBqG,mBAAqBrG,IACjB,MAAM,cAAE0D,EAAa,SAAEzL,EAAQ,MAAEf,EAAK,gBAAEuM,EAAe,MAAEwnB,GAAUvqB,IACnE,GAAIV,GAAShD,OAAQ,CACjB,GAAIyG,EAAiB,CAEjBxL,EADqBqJ,EAAiBtB,EAAS9I,GAEnD,CACI+zB,GACAK,QAAQC,IAAI,mCAAoCvrB,GAEpD0D,IAAgB1D,EACpB,GAEJsG,mBAAqBtG,IACjB,MAAM,cAAEgE,EAAa,SAAE9L,EAAQ,MAAEf,EAAK,gBAAE4M,EAAe,MAAEknB,GAAUvqB,IACnE,GAAIV,GAAShD,OAAQ,CACjB,GAAI+G,EAAiB,CAEjB7L,EADqBqJ,EAAiBvB,EAAS7I,GAEnD,CACI8zB,GACAK,QAAQC,IAAI,mCAAoCvrB,GAEpDgE,IAAgBhE,EACpB,GAEJ4P,iBAAmBvC,IACf,MAAM,qBAAEyC,EAAoB,WAAEvZ,EAAU,WAAEN,EAAU,mBAAEoQ,EAAkB,mBAAEC,GAAuB5F,IACjG,GAAIoP,EAAsB,CAGtB,YADAzJ,EADoBgH,EAAgBxW,IAAKuR,GAAW5G,EAAsB4G,GAAQ,IAGtF,CACA/B,EAAmB5E,EAAoBxL,EAAY,IAAI0E,IAAI,IAAI0S,KAAmB,IAClF/G,EAAmB7E,EAAoBlL,KAE3C8wB,iBAAmB/Z,IACf,MAAM,qBAAEwC,EAAoB,WAAEvZ,EAAU,WAAEN,EAAU,mBAAEoQ,EAAkB,mBAAEC,GAAuB5F,IACjG,GAAIoP,EAAsB,CAGtB,YADAxJ,EADqBgH,EAAgBzW,IAAKwY,GAAW7N,EAAsB6N,GAAQ,IAGvF,CACA/I,EAAmB7E,EAAoBlL,EAAY,IAAIoE,IAAI,IAAI2S,MAC/DjH,EAAmB5E,EAAoBxL,EAAY,IAAI0E,KAAO,KAElEkV,sBAAuB,EAAG3Y,QAAOC,SAAU,CAAC,KACxC,MAAQA,MAAOuzB,EAAYxzB,MAAOyzB,EAAU,WAAE10B,EAAU,mBAAEoQ,EAAkB,mBAAEC,GAAuB5F,IAE/FqrB,EAAkB50B,GAAgBuzB,EAClC5jB,GAFkB5P,GAAgByzB,GAEJ9zB,IAAKsM,IACrC,MAAMoE,EAAetR,EAAWyK,IAAIyC,EAAE1O,IAQtC,OAPI8S,IAKAA,EAAarR,UAAW,GAErBsL,EAAsB2B,EAAE1O,IAAI,KAEjCoS,EAAcklB,EAAgBl1B,IAAKP,GAASkL,EAAsBlL,EAAK7B,IAAI,IACjF4R,EAAmBS,GACnBR,EAAmBO,IAEvB1O,WAAaU,IACT,MAAM,QAAE6E,EAAO,QAAE5E,GAAY4H,IAC7BhD,GAASsuB,eAAe,CAACnzB,EAASC,IAClC0H,EAAI,CAAE3H,aAEVT,WAAaU,IACT,MAAM,QAAE4E,EAAO,QAAE7E,GAAY6H,IAC7BhD,GAASsuB,eAAe,CAACnzB,EAASC,IAClC0H,EAAI,CAAE1H,aAEVT,mBAAqBM,IACjB+H,IAAMhD,SAASrF,mBAAmBM,GAClC6H,EAAI,CAAE7H,qBAEVF,qBAAuBwzB,IACnBvrB,IAAMhD,SAASwuB,iBAAiBD,IAEpCve,sBAAuB,KACnB,MAAM,MAAEvW,EAAK,MAAED,EAAK,mBAAEmP,EAAkB,mBAAEC,EAAkB,mBAAEvN,GAAuB2H,IACrF,IAAK3H,EACD,OAEJ,MAAM+N,EAAc5P,EAAMmE,OAAO,CAACC,EAAKtF,IAAUA,EAAKE,SAAW,IAAIoF,EAAKkG,EAAsBxL,EAAKvB,IAAI,IAAU6G,EAAM,IACnHuL,EAAc1P,EAAMkE,OAAO,CAACC,EAAKhF,IAAUA,EAAKJ,SAAW,IAAIoF,EAAKkG,EAAsBlL,EAAK7B,IAAI,IAAU6G,EAAM,IACzH+K,EAAmBS,GACnBR,EAAmBO,IAEvBvO,cAAgB6zB,IACZ,MAAM,MAAEj1B,EAAK,WAAEjB,EAAU,aAAEyjB,EAAY,WAAE9gB,EAAU,qBAAEmyB,EAAoB,WAAEla,GAAenQ,IACtFyrB,EAAe,GAAG,KAAOtb,EAAW,GAAG,IACvCsb,EAAe,GAAG,KAAOtb,EAAW,GAAG,IACvCsb,EAAe,GAAG,KAAOtb,EAAW,GAAG,IACvCsb,EAAe,GAAG,KAAOtb,EAAW,GAAG,MAG3C,QAAe3Z,EAAOjB,EAAYyjB,EAAc,CAC5C9gB,aACAiY,WAAYsb,EACZpB,uBACAK,eAAe,IAEnB5qB,EAAI,CAAEqQ,WAAYsb,MAEtBjY,MAAQkY,IACJ,MAAM,UAAEhuB,EAAS,MAAE7K,EAAK,OAAEC,EAAM,QAAEkK,EAAO,gBAAE/E,GAAoB+H,IAC/D,OAAO,QAAM,CAAE0rB,QAAO1uB,UAASU,YAAWzF,kBAAiBpF,QAAOC,YAEtEoL,UAAWN,MAAO1G,EAAGC,EAAGuC,KACpB,MAAM,MAAE7G,EAAK,OAAEC,EAAM,QAAEsF,EAAO,QAAE4E,GAAYgD,IAC5C,IAAKhD,EACD,OAAOG,QAAQC,SAAQ,GAE3B,MAAMuuB,OAAoC,IAAlBjyB,GAAStC,KAAuBsC,EAAQtC,KAAOgB,EAMvE,aALM4E,EAAQW,YAAY,CACtBzG,EAAGrE,EAAQ,EAAIqE,EAAIy0B,EACnBx0B,EAAGrE,EAAS,EAAIqE,EAAIw0B,EACpBv0B,KAAMu0B,GACP,CAAEzuB,SAAUxD,GAASwD,SAAUmB,KAAM3E,GAAS2E,KAAMC,YAAa5E,GAAS4E,cACtEnB,QAAQC,SAAQ,IAE3BqW,iBAAkB,KACd3T,EAAI,CACA2L,WAAY,IAAK,SAGzBmI,iBAAmBnI,IACf3L,EAAI,CAAE2L,gBAEV5T,MAAO,IAAMiI,EAAI,IAAKiqB,SAE3BhT,OAAO6U,IAoCV,SAASC,IAAoBC,aAAct1B,EAAOu1B,aAAct1B,EAAK,aAAEiC,EAAY,aAAEC,EAAc4hB,aAAc1nB,EAAO2nB,cAAe1nB,EAAQk5B,eAAgB7zB,EAAS8zB,eAAgB7zB,EAAS8zB,sBAAuBhzB,EAAc,QAAE8O,EAAO,WAAE9P,EAAU,WAAEiY,EAAU,SAAEnc,IACrQ,MAAO/B,IAAS,IAAA6H,UAAS,IAAM0wB,GAAY,CACvCh0B,QACAC,QACAiC,eACAC,eACA9F,QACAC,SACAkV,UACA7P,UACAC,UACAc,iBACAhB,aACAiY,gBAEJ,OAAQ,IAAArc,KAAInC,EAAY,CAAE4R,MAAOtR,EAAO+B,UAAU,IAAAF,KAAI8O,GAAe,CAAE5O,SAAUA,KACrF,CAEA,SAASm4B,IAAQ,SAAEn4B,EAAQ,MAAEwC,EAAK,MAAEC,EAAK,aAAEiC,EAAY,aAAEC,EAAY,MAAE9F,EAAK,OAAEC,EAAM,QAAEkV,EAAO,eAAE9O,EAAc,QAAEf,EAAO,QAAEC,EAAO,WAAEF,EAAU,WAAEiY,IAEzI,OADkB,IAAAje,YAAWT,IAMlB,IAAAqC,KAAI,EAAAM,SAAU,CAAEJ,SAAUA,KAE7B,IAAAF,KAAI+3B,GAAmB,CAAEC,aAAct1B,EAAOu1B,aAAct1B,EAAOiC,aAAcA,EAAcC,aAAcA,EAAc4hB,aAAc1nB,EAAO2nB,cAAe1nB,EAAQkV,QAASA,EAASkkB,sBAAuBhzB,EAAgB8yB,eAAgB7zB,EAAS8zB,eAAgB7zB,EAASF,WAAYA,EAAYiY,WAAYA,EAAYnc,SAAUA,GACxV,CAEA,MAAMo4B,GAAe,CACjBv5B,MAAO,OACPC,OAAQ,OACRI,SAAU,SACVN,SAAU,WACVqoB,OAAQ,GAgCZ,IAAI5a,GAAQwB,EA9BZ,UAAmB,MAAErL,EAAK,MAAEC,EAAK,aAAEiC,EAAY,aAAEC,EAAY,UAAEpE,EAAS,UAAEukB,EAAS,UAAEuM,EAAS,YAAE1I,EAAW,YAAEsK,EAAW,OAAEsC,EAAM,OAAE7e,EAAM,YAAEF,EAAW,UAAEK,EAAS,UAAE+G,EAAS,eAAE8B,EAAc,aAAEC,EAAY,oBAAEe,EAAmB,kBAAEC,EAAiB,iBAAEiI,EAAgB,gBAAEC,EAAe,iBAAEC,EAAgB,kBAAEC,EAAiB,kBAAEC,EAAiB,gBAAEqP,EAAe,WAAEC,EAAU,eAAEC,EAAc,cAAE9mB,EAAa,cAAEC,EAAa,SAAEG,EAAQ,kBAAExP,EAAiB,qBAAEm2B,EAAoB,gBAAEC,EAAe,oBAAEC,EAAmB,uBAAEjW,EAAsB,iBAAExK,EAAgB,eAAEC,EAAc,eAAEpG,EAAc,eAAE4M,EAAc,mBAAE8V,EAAqB,KAAmBJ,OAAM,oBAAEK,EAAmB,wBAAEe,EAAuB,6BAAEC,EAA4B,cAAEnS,EAAgB,YAAW,iBAAEC,GAAmB,QAAO,gBAAEvL,IAAkB,EAAK,cAAEF,GAAgB,KAAcC,KAAI,qBAAE0L,GAAuB,QAAO,sBAAED,KAAwB,UAAY,OAAS,WAAS,sBAAEpO,KAAwB,UAAY,OAAS,WAAS,WAAE1K,GAAU,SAAED,GAAQ,0BAAE4d,IAA4B,EAAK,kBAAEhB,GAAiB,eAAEjL,GAAc,mBAAEsL,GAAkB,iBAAE9C,GAAgB,eAAEC,GAAc,WAAE3gB,GAAalB,EAAiB,eAAEmuB,GAAc,mBAAEC,GAAkB,mBAAE/sB,IAAqB,EAAMpB,gBAAiB01B,GAAoB11B,EAAe,QAAEkB,GAAU,GAAG,QAAEC,GAAU,EAAC,gBAAEH,GAAkB,KAAc,iBAAEoR,IAAmB,EAAI,WAAE8G,GAAU,mBAAE6W,GAAqB,UAAS,aAAEpe,IAAe,EAAI,YAAEC,IAAc,EAAI,YAAEC,IAAc,EAAK,iBAAEC,GAAmB,GAAG,gBAAEC,GAAkB,KAAgBC,KAAI,kBAAEC,IAAoB,EAAI,UAAEC,IAAY,EAAI,YAAEgD,GAAW,iBAAEE,GAAgB,gBAAEC,GAAe,iBAAEC,GAAgB,aAAEH,GAAY,kBAAEzD,GAAiB,kBAAEpQ,GAAoB,EAAC,kBAAEoX,GAAoB,EAAC,SAAE3b,GAAQ,YAAEowB,GAAW,iBAAEC,GAAgB,eAAEC,GAAc,kBAAE8B,GAAiB,kBAAED,GAAiB,iBAAEE,GAAgB,gBAAEC,GAAe,iBAAEC,GAAgB,gBAAEpC,GAAkB,GAAE,cAAEnhB,GAAa,cAAEM,GAAa,gBAAEkM,GAAkB,SAAQ,iBAAElG,GAAmB,UAAS,eAAEhR,GAAiB,QAAO,QAAE0P,GAAO,eAAE9O,GAAc,eAAEkY,GAAc,oBAAEwb,GAAmB,WAAE/3B,GAAU,mBAAEkZ,GAAkB,qBAAEsc,GAAoB,qBAAErE,GAAoB,oBAAE9xB,IAAsB,EAAK,iBAAEmf,GAAgB,kBAAEiX,GAAiB,aAAEvW,GAAY,iBAAET,GAAgB,kBAAE9B,GAAiB,QAAE5H,GAAO,MAAEnX,GAAK,GAAEsB,GAAE,kBAAEunB,GAAiB,wBAAErH,GAAuB,SAAEpW,GAAQ,iBAAE0L,GAAgB,MAAE1W,GAAK,OAAEC,GAAM,UAAE+5B,GAAY,QAAO,MAAEtC,GAAK,SAAEuC,GAAQ,gBAAEn5B,MAAoBa,IAAQC,IACv3E,MAAMZ,GAAOE,IAAM,IACbg5B,GA9nGV,SAA2BF,GACvB,MAAOG,EAAgBC,IAAqB,IAAAnzB,UAAuB,WAAd+yB,EAAyB,KAAOA,GAcrF,OAbA,IAAAv2B,WAAU,KACN,GAAkB,WAAdu2B,EAEA,YADAI,EAAkBJ,GAGtB,MAAMK,EAAa/zB,IACbg0B,EAAuB,IAAMF,EAAkBC,GAAYzR,QAAU,OAAS,SAGpF,OAFA0R,IACAD,GAAY/wB,iBAAiB,SAAUgxB,GAChC,KACHD,GAAY9wB,oBAAoB,SAAU+wB,KAE/C,CAACN,IACsB,OAAnBG,EAA0BA,EAAiB7zB,KAAiBsiB,QAAU,OAAS,OAC1F,CA8mG+B2R,CAAkBP,IAEvCQ,IAAkB,IAAAvqB,aAAaiC,IACjCA,EAAEuoB,cAAcC,SAAS,CAAEllB,IAAK,EAAGC,KAAM,EAAGklB,SAAU,YACtDV,KAAW/nB,IACZ,CAAC+nB,KACJ,OAAQ,IAAAh5B,KAAI,MAAO,CAAE,cAAe,iBAAkBU,GAAMs4B,SAAUO,GAAiB56B,MAAO,IAAKA,MAAU25B,IAAgB33B,IAAKA,GAAKF,WAAW,OAAG,CAAC,aAAcA,EAAWw4B,KAAsBh5B,GAAIA,GAAI4nB,KAAM,cAAe3nB,UAAU,IAAAG,MAAKg4B,GAAS,CAAE31B,MAAOA,EAAOC,MAAOA,EAAO5D,MAAOA,GAAOC,OAAQA,GAAQkV,QAASA,GAAS9O,eAAgBA,GAAgBf,QAASA,GAASC,QAASA,GAASF,WAAYA,GAAYiY,WAAYA,GAAYnc,SAAU,EAAC,IAAAF,KAAIg2B,GAAW,CAAEP,OAAQA,EAAQ5M,YAAaA,EAAasK,YAAaA,EAAarK,iBAAkBA,EAAkBC,gBAAiBA,EAAiBC,iBAAkBA,EAAkBC,kBAAmBA,EAAmBC,kBAAmBA,EAAmBlE,UAAWA,EAAWuM,UAAWA,EAAWmD,mBAAoBA,EAAoBC,oBAAqBA,EAAqBe,wBAAyBA,EAAyBC,6BAA8BA,EAA8BlS,iBAAkBA,GAAkBvL,gBAAiBA,GAAiBF,cAAeA,GAAewL,cAAeA,EAAeE,sBAAuBA,GAAuBC,qBAAsBA,GAAsBrO,sBAAuBA,GAAuBiT,0BAA2BA,GAA2BplB,gBAAiB01B,GAAmB10B,gBAAiBA,GAAiBE,QAASA,GAASC,QAASA,GAASiR,iBAAkBA,GAAkBT,aAAcA,GAAcC,YAAaA,GAAaK,kBAAmBA,GAAmBJ,YAAaA,GAAaC,iBAAkBA,GAAkBC,gBAAiBA,GAAiBG,UAAWA,GAAWgD,YAAaA,GAAaE,iBAAkBA,GAAkBC,gBAAiBA,GAAiBC,iBAAkBA,GAAkBH,aAAcA,GAAczD,kBAAmBA,GAAmBpQ,kBAAmBA,GAAmBoX,kBAAmBA,GAAmB8G,uBAAwBA,EAAwBxK,iBAAkBA,EAAkBC,eAAgBA,EAAgBkY,YAAaA,GAAaC,iBAAkBA,GAAkBC,eAAgBA,GAAgB8B,kBAAmBA,GAAmBD,kBAAmBA,GAAmBE,iBAAkBA,GAAkBC,gBAAiBA,GAAiBC,iBAAkBA,GAAkBpC,gBAAiBA,GAAiB6C,mBAAoBA,GAAoBxX,gBAAiBA,GAAiBlG,iBAAkBA,GAAkBhR,eAAgBA,GAAgBzE,KAAMA,GAAMK,oBAAqBA,GAAqBic,WAAYA,GAAYtS,SAAUA,GAAU0L,iBAAkBA,MAAqB,IAAAzV,KAAI0E,EAAc,CAAEhC,MAAOA,EAAOC,MAAOA,EAAOiC,aAAcA,EAAcC,aAAcA,EAAciZ,UAAWA,EAAW8B,eAAgBA,EAAgBC,aAAcA,EAAce,oBAAqBA,EAAqBC,kBAAmBA,EAAmBvE,eAAgBA,GAAgBsL,mBAAoBA,GAAoB9C,iBAAkBA,GAAkBC,eAAgBA,GAAgBsM,eAAgBA,GAAgBC,mBAAoBA,GAAoB/sB,mBAAoBA,GAAoBgyB,qBAAsBA,GAAsBrE,qBAAsBA,GAAsB7tB,QAASA,GAASC,QAASA,GAAS+X,WAAYA,GAAYnN,cAAeA,GAAeM,cAAeA,GAAe5E,WAAYA,GAAYD,SAAUA,GAAUiU,eAAgBA,EAAgBza,gBAAiBA,GAAiBmZ,eAAgBA,GAAgBrD,mBAAoBA,GAAoB/F,QAASA,GAAS9O,eAAgBA,GAAgBuM,cAAeA,EAAeC,cAAeA,EAAeG,SAAUA,EAAUwmB,gBAAiBA,EAAiBC,WAAYA,EAAYC,eAAgBA,EAAgBE,gBAAiBA,EAAiBD,qBAAsBA,EAAsBE,oBAAqBA,EAAqBhiB,OAAQA,EAAQF,YAAaA,EAAaK,UAAWA,EAAWvS,eAAgBA,GAAgBJ,WAAYA,GAAYrE,KAAMA,GAAMwf,iBAAkBA,GAAkBiX,kBAAmBA,GAAmBvW,aAAcA,GAAcnK,QAASA,GAAS0J,iBAAkBA,GAAkB9B,kBAAmBA,GAAmB6J,kBAAmBA,GAAmBC,kBAAmBA,GAAmBrH,wBAAyBA,GAAyBnO,eAAgBA,EAAgBvN,kBAAmBA,GAAmBgyB,MAAOA,GAAO52B,gBAAiBA,MAAoB,IAAAG,KAAIgD,EAAmB,CAAET,kBAAmBA,IAAsBrC,IAAU,IAAAF,KAAIc,EAAa,CAAEC,WAAYA,GAAYjC,SAAUg6B,MAAwB,IAAA94B,KAAIG,EAAkB,CAAEJ,KAAMA,GAAMK,oBAAqBA,SACpiJ,GAuBA,MAAMu5B,GAAcj6B,GAAMA,EAAEmL,SAAS+uB,cAAc,mCA0CnD,SAASC,IAAkB,SAAE35B,IACzB,MAAM45B,EAAoB97B,EAAS27B,IACnC,OAAKG,GAGE,IAAAC,cAAa75B,EAAU45B,GAFnB,IAGf,CAsOA,SAASE,GAAchC,GACnB,MAAOt1B,EAAOe,IAAY,IAAAuC,UAASgyB,GAC7B9oB,GAAgB,IAAAF,aAAaxD,GAAY/H,EAAUw2B,GAAQntB,EAAiBtB,EAASyuB,IAAO,IAClG,MAAO,CAACv3B,EAAOe,EAAUyL,EAC7B,CAgDA,SAASgrB,GAAcjC,GACnB,MAAOt1B,EAAOe,IAAY,IAAAsC,UAASiyB,GAC7BzoB,GAAgB,IAAAR,aAAaxD,GAAY9H,EAAUy2B,GAAQptB,EAAiBvB,EAAS2uB,IAAO,IAClG,MAAO,CAACx3B,EAAOe,EAAU8L,EAC7B,CAoKiB,KAAwB,WAgGzC,SAAS4qB,IAAY,WAAE1tB,EAAU,UAAE2tB,EAAS,QAAEC,EAAO,UAAE75B,IACnD,OAAQ,IAAAT,KAAI,OAAQ,CAAEupB,YAAa8Q,EAAW/N,EAAG,IAAI5f,EAAW,GAAK,QAAQA,EAAW,SAASA,EAAW,GAAK,MAAMA,EAAW,KAAMjM,WAAW,OAAG,CAAC,iCAAkC65B,EAAS75B,KACtM,CACA,SAAS85B,IAAW,OAAEzK,EAAM,UAAErvB,IAC1B,OAAQ,IAAAT,KAAI,SAAU,CAAEgwB,GAAIF,EAAQG,GAAIH,EAAQI,EAAGJ,EAAQrvB,WAAW,OAAG,CAAC,iCAAkC,OAAQA,KACxH,CAQA,IAAI+5B,IACJ,SAAWA,GACPA,EAAyB,MAAI,QAC7BA,EAAwB,KAAI,OAC5BA,EAAyB,MAAI,OAChC,CAJD,CAIGA,KAAsBA,GAAoB,CAAC,IAE9C,MAAMC,GAAc,CAChB,CAACD,GAAkBE,MAAO,EAC1B,CAACF,GAAkBG,OAAQ,EAC3B,CAACH,GAAkBI,OAAQ,GAEzBC,GAAcn7B,IAAM,CAAGkK,UAAWlK,EAAEkK,UAAWkxB,UAAW,WAAWp7B,EAAEK,SAC7E,SAASg7B,IAAoB,GAAE96B,EAAE,QAAEq6B,EAAUE,GAAkBE,KAAI,IAEnEM,EAAM,GAAE,KAERvyB,EAAI,UAAE4xB,EAAY,EAAC,OAAElM,EAAS,EAAC,MAAE7E,EAAK,QAAE2R,EAAO,MAAEt8B,EAAK,UAAE8B,EAAS,iBAAEy6B,IAC/D,MAAMv6B,GAAM,IAAAqE,QAAO,OACb,UAAE4E,EAAS,UAAEkxB,GAAc98B,EAAS68B,GAAY,KAChDM,EAAc1yB,GAAQgyB,GAAYH,GAClCc,EAASd,IAAYE,GAAkBE,KACvCW,EAAUf,IAAYE,GAAkBI,MACxCU,EAAQ/0B,MAAMC,QAAQw0B,GAAOA,EAAM,CAACA,EAAKA,GACzCO,EAAY,CAACD,EAAM,GAAK1xB,EAAU,IAAM,EAAG0xB,EAAM,GAAK1xB,EAAU,IAAM,GACtE4xB,EAAaL,EAAcvxB,EAAU,GACrC6xB,EAAWl1B,MAAMC,QAAQ2nB,GAAUA,EAAS,CAACA,EAAQA,GACrDuN,EAAoBL,EAAU,CAACG,EAAYA,GAAcD,EACzDI,EAAe,CACjBF,EAAS,GAAK7xB,EAAU,IAAM,EAAI8xB,EAAkB,GAAK,EACzDD,EAAS,GAAK7xB,EAAU,IAAM,EAAI8xB,EAAkB,GAAK,GAEvDE,EAAa,GAAGd,IAAY76B,GAAU,KAC5C,OAAQ,IAAAI,MAAK,MAAO,CAAEI,WAAW,OAAG,CAAC,yBAA0BA,IAAa9B,MAAO,IACxEA,KACA2V,GACH,8BAA+B2mB,EAC/B,sCAAuC3R,GACxC3oB,IAAKA,EAAK,cAAe,iBAAkBT,SAAU,EAAC,IAAAF,KAAI,UAAW,CAAEC,GAAI27B,EAAYx4B,EAAGwG,EAAU,GAAK2xB,EAAU,GAAIl4B,EAAGuG,EAAU,GAAK2xB,EAAU,GAAIx8B,MAAOw8B,EAAU,GAAIv8B,OAAQu8B,EAAU,GAAIM,aAAc,iBAAkBC,iBAAkB,cAAcH,EAAa,OAAOA,EAAa,MAAOz7B,SAAUk7B,GAAU,IAAAp7B,KAAIu6B,GAAY,CAAEzK,OAAQ0L,EAAa,EAAG/6B,UAAWy6B,KAAwB,IAAAl7B,KAAIo6B,GAAa,CAAE1tB,WAAYgvB,EAAmBrB,UAAWA,EAAWC,QAASA,EAAS75B,UAAWy6B,OAAyB,IAAAl7B,KAAI,OAAQ,CAAEoD,EAAG,IAAKC,EAAG,IAAKtE,MAAO,OAAQC,OAAQ,OAAQ2qB,KAAM,QAAQiS,SAC/lB,CACAb,GAAoB35B,YAAc,aAsDlC,MAAM26B,IAAa,IAAAve,MAAKud,IAExB,SAASiB,KACL,OAAQ,IAAAh8B,KAAI,MAAO,CAAEi8B,MAAO,6BAA8B5R,QAAS,YAAanqB,UAAU,IAAAF,KAAI,OAAQ,CAAEssB,EAAG,2EAC/G,CAEA,SAAS4P,KACL,OAAQ,IAAAl8B,KAAI,MAAO,CAAEi8B,MAAO,6BAA8B5R,QAAS,WAAYnqB,UAAU,IAAAF,KAAI,OAAQ,CAAEssB,EAAG,oBAC9G,CAEA,SAAS6P,KACL,OAAQ,IAAAn8B,KAAI,MAAO,CAAEi8B,MAAO,6BAA8B5R,QAAS,YAAanqB,UAAU,IAAAF,KAAI,OAAQ,CAAEssB,EAAG,iYAC/G,CAEA,SAAS8P,KACL,OAAQ,IAAAp8B,KAAI,MAAO,CAAEi8B,MAAO,6BAA8B5R,QAAS,YAAanqB,UAAU,IAAAF,KAAI,OAAQ,CAAEssB,EAAG,ocAC/G,CAEA,SAAS+P,KACL,OAAQ,IAAAr8B,KAAI,MAAO,CAAEi8B,MAAO,6BAA8B5R,QAAS,YAAanqB,UAAU,IAAAF,KAAI,OAAQ,CAAEssB,EAAG,0YAC/G,CAyBA,SAASgQ,IAAc,SAAEp8B,EAAQ,UAAEO,KAAcC,IAC7C,OAAQ,IAAAV,KAAI,SAAU,CAAE+L,KAAM,SAAUtL,WAAW,OAAG,CAAC,8BAA+BA,OAAgBC,EAAMR,SAAUA,GAC1H,CAEA,MAAMq8B,GAAc78B,IAAM,CACtB88B,cAAe98B,EAAE4c,gBAAkB5c,EAAEolB,kBAAoBplB,EAAE6E,mBAC3Dk4B,eAAgB/8B,EAAEkK,UAAU,IAAMlK,EAAE2E,QACpCq4B,eAAgBh9B,EAAEkK,UAAU,IAAMlK,EAAE4E,QACpCzE,gBAAiBH,EAAEG,kBAEvB,SAAS88B,IAAkB,MAAEh+B,EAAK,SAAEi+B,GAAW,EAAI,YAAEC,GAAc,EAAI,gBAAEC,GAAkB,EAAI,eAAE13B,EAAc,SAAE23B,EAAQ,UAAEC,EAAS,UAAEC,EAAS,oBAAEC,EAAmB,UAAEz8B,EAAS,SAAEP,EAAQ,SAAEpB,EAAW,cAAa,YAAEq+B,EAAc,WAAY,aAAcpV,IACzP,MAAM5pB,EAAQG,KACR,cAAEk+B,EAAa,eAAEC,EAAc,eAAEC,EAAc,gBAAE78B,GAAoB7B,EAASu+B,GAAY,MAC1F,OAAEtzB,EAAM,QAAEM,EAAO,QAAE2K,GAAYvE,KAqB/BytB,EAAmC,eAAhBD,EAA+B,aAAe,WACvE,OAAQ,IAAA98B,MAAKE,EAAO,CAAEE,WAAW,OAAG,CAAC,uBAAwB28B,EAAkB38B,IAAa3B,SAAUA,EAAUH,MAAOA,EAAO,cAAe,eAAgB,aAAcopB,GAAaloB,EAAgB,sBAAuBK,SAAU,CAAC08B,IAAa,IAAAv8B,MAAK,EAAAC,SAAU,CAAEJ,SAAU,EAAC,IAAAF,KAAIs8B,GAAe,CAAErjB,QArBhR,KACpBhQ,IACA8zB,OAmB8Tt8B,UAAW,8BAA+B48B,MAAOx9B,EAAgB,6BAA8B,aAAcA,EAAgB,6BAA8B4b,SAAUihB,EAAgBx8B,UAAU,IAAAF,KAAIg8B,GAAU,CAAC,MAAO,IAAAh8B,KAAIs8B,GAAe,CAAErjB,QAjBnhB,KACrB1P,IACAyzB,OAemkBv8B,UAAW,+BAAgC48B,MAAOx9B,EAAgB,8BAA+B,aAAcA,EAAgB,8BAA+B4b,SAAUghB,EAAgBv8B,UAAU,IAAAF,KAAIk8B,GAAW,CAAC,QAAYW,IAAgB,IAAA78B,KAAIs8B,GAAe,CAAE77B,UAAW,+BAAgCwY,QAb51B,KACrB/E,EAAQ9O,GACR63B,OAW44BI,MAAOx9B,EAAgB,8BAA+B,aAAcA,EAAgB,8BAA+BK,UAAU,IAAAF,KAAIm8B,GAAa,CAAC,KAAQW,IAAoB,IAAA98B,KAAIs8B,GAAe,CAAE77B,UAAW,mCAAoCwY,QATjmC,KAC1B9a,EAAMM,SAAS,CACX6d,gBAAiBkgB,EACjB1X,kBAAmB0X,EACnBj4B,oBAAqBi4B,IAEzBU,KAAuBV,IAGooCa,MAAOx9B,EAAgB,kCAAmC,aAAcA,EAAgB,kCAAmCK,SAAUs8B,GAAgB,IAAAx8B,KAAIq8B,GAAY,CAAC,IAAK,IAAAr8B,KAAIo8B,GAAU,CAAC,KAAQl8B,IACr2C,CACAy8B,GAAkBv7B,YAAc,YAsBf,IAAAoc,MAAKmf,IAWtB,MAAMW,IAAc,IAAA9f,MATpB,UAA8B,GAAEvd,EAAE,EAAEmD,EAAC,EAAEC,EAAC,MAAEtE,EAAK,OAAEC,EAAM,MAAEL,EAAK,MAAE2qB,EAAK,YAAEiU,EAAW,YAAEhU,EAAW,UAAE9oB,EAAS,aAAEytB,EAAY,eAAEsP,EAAc,SAAE97B,EAAQ,QAAEuX,IAChJ,MAAM,WAAEwkB,EAAU,gBAAEC,GAAoB/+B,GAAS,CAAC,EAC5CgrB,EAAQL,GAASmU,GAAcC,EACrC,OAAQ,IAAA19B,KAAI,OAAQ,CAAES,WAAW,OAAG,CAAC,2BAA4B,CAAEiB,YAAYjB,IAAa2C,EAAGA,EAAGC,EAAGA,EAAGwoB,GAAIqC,EAAcpC,GAAIoC,EAAcnvB,MAAOA,EAAOC,OAAQA,EAAQL,MAAO,CACzKgrB,OACAH,OAAQ+T,EACRhU,eACDiU,eAAgBA,EAAgBvkB,QAASA,EAAW/R,GAAU+R,EAAQ/R,EAAOjH,QAAMqM,GAC9F,GAGMqxB,GAAmBj+B,GAAMA,EAAEgD,MAAML,IAAKb,GAASA,EAAKvB,IACpD29B,GAAmBC,GAASA,aAAgBC,SAAWD,EAAO,IAAMA,EAyC1E,MAAME,IAAuB,IAAAvgB,MAnB7B,UAAmC,GAAEvd,EAAE,cAAE+9B,EAAa,oBAAEC,EAAmB,kBAAEC,EAAiB,iBAAEC,EAAgB,gBAAEC,EAAe,eAAEZ,EAAc,cAAEpY,EAAa,QAAEnM,IAC9J,MAAM,KAAEzX,EAAI,EAAE4B,EAAC,EAAEC,EAAC,MAAEtE,EAAK,OAAEC,GAAWhB,EAAU0B,IAC5C,MAAM,UAAEkC,GAAclC,EAAE+B,WAAWyK,IAAIjM,GACjCuB,EAAOI,EAAUC,UACjB,EAAEuB,EAAC,EAAEC,GAAMzB,EAAUoR,kBACrB,MAAEjU,EAAK,OAAEC,IAAW,QAAkBwC,GAC5C,MAAO,CACHA,OACA4B,IACAC,IACAtE,QACAC,WAEL,KACH,OAAKwC,IAAQA,EAAKukB,SAAW,QAAkBvkB,IAGvC,IAAAxB,KAAIolB,EAAe,CAAEhiB,EAAGA,EAAGC,EAAGA,EAAGtE,MAAOA,EAAOC,OAAQA,EAAQL,MAAO6C,EAAK7C,MAAO+C,WAAYF,EAAKE,SAAUjB,UAAWy9B,EAAkB18B,GAAO8nB,MAAO0U,EAAcx8B,GAAO0sB,aAAciQ,EAAkBZ,YAAaU,EAAoBz8B,GAAO+nB,YAAa6U,EAAiBZ,eAAgBA,EAAgBvkB,QAASA,EAAShZ,GAAIuB,EAAKvB,KAF3U,IAGf,GAEA,IAAIo+B,IAAiB,IAAA7gB,MAzCrB,UAAsB,gBAAE8gB,EAAe,UAAEC,EAAS,cAAEC,EAAgB,GAAE,iBAAEL,EAAmB,EAAC,gBAAEC,EAK9FK,cAAerZ,EAAgBkY,GAAW,QAAErkB,IACxC,MAAMqP,EAAUtqB,EAAS2/B,GAAiB,KACpCK,EAAgBJ,GAAgBW,GAChCN,EAAsBL,GAAgBU,GACtCJ,EAAoBN,GAAgBY,GACpChB,EAAmC,oBAAXl4B,QAA4BA,OAAOo5B,OAAS,aAAe,qBACzF,OAAQ,IAAA1+B,KAAI,EAAAM,SAAU,CAAEJ,SAAUooB,EAAQjmB,IAAKuR,IAQ3C,IAAA5T,KAAI+9B,GAAsB,CAAE99B,GAAI2T,EAAQoqB,cAAeA,EAAeC,oBAAqBA,EAAqBC,kBAAmBA,EAAmBC,iBAAkBA,EAAkBC,gBAAiBA,EAAiBhZ,cAAeA,EAAenM,QAASA,EAASukB,eAAgBA,GAAkB5pB,KACtT,GAuBA,MAEM+qB,GAAgBn9B,IAAUA,EAAKukB,OAC/B6Y,GAAcl/B,IAChB,MAAMm/B,EAAS,CACXz7B,GAAI1D,EAAEkK,UAAU,GAAKlK,EAAEkK,UAAU,GACjCvG,GAAI3D,EAAEkK,UAAU,GAAKlK,EAAEkK,UAAU,GACjC7K,MAAOW,EAAEX,MAAQW,EAAEkK,UAAU,GAC7B5K,OAAQU,EAAEV,OAASU,EAAEkK,UAAU,IAEnC,MAAO,CACHi1B,SACAC,aAAcp/B,EAAE+B,WAAWgH,KAAO,GAC5B,SAAiB,QAAuB/I,EAAE+B,WAAY,CAAEgF,OAAQk4B,KAAiBE,GACjFA,EACN9+B,KAAML,EAAEK,KACRmJ,QAASxJ,EAAEwJ,QACX/E,gBAAiBzE,EAAEyE,gBACnB46B,UAAWr/B,EAAEX,MACbigC,WAAYt/B,EAAEV,OACda,gBAAiBH,EAAEG,kBAI3B,SAASo/B,IAAiB,MAAEtgC,EAAK,UAAE8B,EAAS,gBAAE69B,EAAe,UAAEC,EAAS,cAAEC,EAAgB,GAAE,iBAAEL,EAAmB,EAAC,gBAAEC,EAAe,cAKnIK,EAAa,QAAExD,EAAO,UAAEiE,EAAS,gBAAEC,EAAe,gBAAEC,EAAe,SAAEtgC,EAAW,eAAc,QAAEma,EAAO,YAAE4P,EAAW,SAAEwW,GAAW,EAAK,SAAEC,GAAW,EAAK,UAAEvX,EAAS,WAAEwX,EAAU,SAAEC,EAAW,GAAE,YAAEC,EAAc,IAC1M,MAAMthC,EAAQG,IACRohC,GAAM,IAAA16B,QAAO,OACb,aAAE85B,EAAY,OAAED,EAAM,KAAE9+B,EAAI,QAAEmJ,EAAO,gBAAE/E,EAAe,UAAE46B,EAAS,WAAEC,EAAU,gBAAEn/B,GAAoB7B,EAAS4gC,GAAY,KACxHe,EAAehhC,GAAOI,OAjCX,IAkCX6gC,EAAgBjhC,GAAOK,QAjCX,IAkCZ6gC,EAAcf,EAAa//B,MAAQ4gC,EACnCG,EAAehB,EAAa9/B,OAAS4gC,EACrCG,EAAY1lB,KAAK+c,IAAIyI,EAAaC,GAClCE,EAAYD,EAAYJ,EACxBM,EAAaF,EAAYH,EACzBzR,EAASsR,EAAcM,EACvB38B,EAAI07B,EAAa17B,GAAK48B,EAAYlB,EAAa//B,OAAS,EAAIovB,EAC5D9qB,EAAIy7B,EAAaz7B,GAAK48B,EAAanB,EAAa9/B,QAAU,EAAImvB,EAC9DpvB,EAAQihC,EAAqB,EAAT7R,EACpBnvB,EAASihC,EAAsB,EAAT9R,EACtB+R,EAAa,4BAAqBngC,IAClCogC,GAAe,IAAAn7B,QAAO,GACtBo7B,GAAkB,IAAAp7B,UACxBm7B,EAAap7B,QAAUg7B,GACvB,IAAAv9B,WAAU,KACN,GAAIk9B,EAAI36B,SAAWmE,EAOf,OANAk3B,EAAgBr7B,SAAU,QAAU,CAChC8F,QAAS60B,EAAI36B,QACbmE,UACA6W,aAAc,IAAM5hB,EAAMK,WAAWoL,UACrCy2B,aAAc,IAAMF,EAAap7B,UAE9B,KACHq7B,EAAgBr7B,SAASkS,YAGlC,CAAC/N,KACJ,IAAA1G,WAAU,KACN49B,EAAgBr7B,SAASmS,OAAO,CAC5B/S,kBACApF,MAAOggC,EACP//B,OAAQggC,EACRO,aACAF,WACAG,WACAF,cAEL,CAACD,EAAUC,EAAUC,EAAYC,EAAUr7B,EAAiB46B,EAAWC,IAC1E,MAAMsB,GAAarnB,EACZ/R,IACC,MAAO9D,EAAGC,GAAK+8B,EAAgBr7B,SAASw7B,QAAQr5B,IAAU,CAAC,EAAG,GAC9D+R,EAAQ/R,EAAO,CAAE9D,IAAGC,YAEtBiJ,EACAk0B,GAAiB3X,GACjB,IAAA7Z,aAAY,CAAC9H,EAAO0M,KAClB,MAAMpS,EAAOrD,EAAMK,WAAWiD,WAAWyK,IAAI0H,GAAQhS,UAAUC,SAC/DgnB,EAAY3hB,EAAO1F,IACpB,SACD8K,EACAm0B,GAAa1Y,GAAaloB,EAAgB,qBAChD,OAAQ,IAAAG,KAAIO,EAAO,CAAEzB,SAAUA,EAAUH,MAAO,IACrCA,EACH,sCAA0D,iBAAZs8B,EAAuBA,OAAU3uB,EAC/E,2CAAiE,iBAAd4yB,EAAyBA,OAAY5yB,EACxF,uCAAmE,iBAApB6yB,EAA+BA,OAAkB7yB,EAChG,uCAAmE,iBAApB8yB,EAA+BA,EAAkBW,OAAYzzB,EAC5G,2CAAiE,iBAAdiyB,EAAyBA,OAAYjyB,EACxF,uCAAmE,iBAApBgyB,EAA+BA,OAAkBhyB,EAChG,uCAAmE,iBAApB8xB,EAA+BA,OAAkB9xB,GACjG7L,WAAW,OAAG,CAAC,sBAAuBA,IAAa,cAAe,cAAeP,UAAU,IAAAG,MAAK,MAAO,CAAEtB,MAAO4gC,EAAc3gC,OAAQ4gC,EAAevV,QAAS,GAAGjnB,KAAKC,KAAKtE,KAASC,IAAUyB,UAAW,0BAA2BonB,KAAM,MAAO,kBAAmBqY,EAAYv/B,IAAK++B,EAAKzmB,QAASqnB,GAAYpgC,SAAU,CAACugC,KAAc,IAAAzgC,KAAI,QAAS,CAAEC,GAAIigC,EAAYhgC,SAAUugC,MAAe,IAAAzgC,KAAIq+B,GAAgB,CAAEplB,QAASunB,GAAgBjC,UAAWA,EAAWD,gBAAiBA,EAAiBH,iBAAkBA,EAAkBK,cAAeA,EAAeJ,gBAAiBA,EAAiBK,cAAeA,KAAkB,IAAAz+B,KAAI,OAAQ,CAAES,UAAW,2BAA4B6rB,EAAG,IAAIlpB,EAAI+qB,KAAU9qB,EAAI8qB,KAAUpvB,EAAiB,EAATovB,KAAcnvB,EAAkB,EAATmvB,MAAepvB,EAAiB,EAATovB,gBAC1vB0Q,EAAOz7B,KAAKy7B,EAAOx7B,KAAKw7B,EAAO9/B,SAAS8/B,EAAO7/B,WAAW6/B,EAAO9/B,SAAU2hC,SAAU,UAAWrZ,cAAe,aAC1H,CACA4X,GAAiB79B,YAAc,UAqB/B,MAAMu/B,IAAU,IAAAnjB,MAAKyhB,IAGf2B,GAAmB,CACrB,CAAC,KAAqBC,MAAO,QAC7B,CAAC,KAAqBtjB,QAAS,iBAmJT,IAAAC,MAjJ1B,UAAuB,OAAE5J,EAAM,SAAE9U,EAAQ,QAAEw7B,EAAU,KAAqB/c,OAAM,UAAE9c,EAAS,MAAE9B,EAAiB,SAAEuB,EAAQ,MAAEopB,EAAK,SAAEwX,EAAW,GAAE,UAAEC,EAAY,GAAE,SAAEC,EAAWC,OAAOC,UAAS,UAAEC,EAAYF,OAAOC,UAAS,gBAAEE,GAAkB,EAAK,gBAAEC,EAAe,UAAEC,GAAY,EAAI,aAAEC,EAAY,cAAEC,EAAa,SAAEC,EAAQ,YAAEC,IAC1T,MAAMC,EAAgBvkB,KAChBnd,EAAuB,iBAAX2T,EAAsBA,EAAS+tB,EAC3CxjC,EAAQG,IACRsjC,GAAmB,IAAA58B,QAAO,MAC1B68B,EAAkBvH,IAAY,KAAqB/c,OACnDukB,EAAQ9jC,GAAS,IAAAgR,cAXJ+yB,EAW8BF,GAAmBP,EAX7BnjC,GAAU4jC,EAAiB,GAAG1nB,KAAK+c,IAAI,EAAIj5B,EAAMyL,UAAU,GAAI,UAAO0C,GAW7B,CAACu1B,EAAiBP,IAAa,KAX7F,IAACS,EAYnB,MAAMC,GAAU,IAAAh9B,QAAO,MACjBi9B,EAAkBnjC,GAAY8hC,GAAiBtG,IACrD,IAAA93B,WAAU,KACN,GAAKo/B,EAAiB78B,SAAY9E,EA4GlC,OAzGK+hC,EAAQj9B,UACTi9B,EAAQj9B,SAAU,QAAU,CACxB8F,QAAS+2B,EAAiB78B,QAC1B6O,OAAQ3T,EACR+b,cAAe,KACX,MAAM,WAAEva,EAAU,UAAEmI,EAAS,SAAEe,EAAQ,WAAEC,EAAU,WAAExG,EAAU,QAAEyG,GAAY1M,EAAMK,WACnF,MAAO,CACHiD,aACAmI,YACAe,WACAC,aACAxG,aACA89B,YAAar3B,IAGrBs3B,SAAU,CAACr2B,EAAQs2B,KACf,MAAM,mBAAEvwB,EAAkB,WAAEpQ,EAAU,aAAEyjB,EAAY,WAAE9gB,GAAejG,EAAMK,WACrEgN,EAAU,GACVyR,EAAe,CAAE7Z,EAAG0I,EAAO1I,EAAGC,EAAGyI,EAAOzI,GACxC7B,EAAOC,EAAWyK,IAAIjM,GAC5B,GAAIuB,GAAQA,EAAK21B,cAAgB31B,EAAK4O,SAAU,CAC5C,MAAMiyB,EAAS7gC,EAAK6gC,QAAUj+B,EACxBrF,EAAQ+M,EAAO/M,OAASyC,EAAKmL,SAAS5N,OAAS,EAC/CC,EAAS8M,EAAO9M,QAAUwC,EAAKmL,SAAS3N,QAAU,EAClDsjC,EAAQ,CACVriC,GAAIuB,EAAKvB,GACTmQ,SAAU5O,EAAK4O,SACfinB,KAAM,CACFt4B,QACAC,aACG,QAAyB,CACxBoE,EAAG0I,EAAO1I,GAAK5B,EAAK1C,SAASsE,EAC7BC,EAAGyI,EAAOzI,GAAK7B,EAAK1C,SAASuE,GAC9B,CAAEtE,QAAOC,UAAUwC,EAAK4O,SAAU3O,EAAY4gC,KAGnD/K,GAAsB,QAAmB,CAACgL,GAAQ7gC,EAAYyjB,EAAc9gB,GAClFoH,EAAQ7J,QAAQ21B,GAKhBra,EAAa7Z,EAAI0I,EAAO1I,EAAIiX,KAAK+c,IAAIiL,EAAO,GAAKtjC,EAAO+M,EAAO1I,QAAKkJ,EACpE2Q,EAAa5Z,EAAIyI,EAAOzI,EAAIgX,KAAK+c,IAAIiL,EAAO,GAAKrjC,EAAQ8M,EAAOzI,QAAKiJ,CACzE,CACA,QAAuBA,IAAnB2Q,EAAa7Z,QAAsCkJ,IAAnB2Q,EAAa5Z,EAAiB,CAC9D,MAAMk/B,EAAiB,CACnBtiC,KACA8L,KAAM,WACNjN,SAAU,IAAKme,IAEnBzR,EAAQ7J,KAAK4gC,EACjB,CACA,QAAqBj2B,IAAjBR,EAAO/M,YAAyCuN,IAAlBR,EAAO9M,OAAsB,CAC3D,MACMwjC,EAAkB,CACpBviC,KACA8L,KAAM,aACNc,UAAU,EACVD,eALmBy0B,IAA6C,eAApBA,EAAmC,QAAU,UAMzF30B,WAAY,CACR3N,MAAO+M,EAAO/M,MACdC,OAAQ8M,EAAO9M,SAGvBwM,EAAQ7J,KAAK6gC,EACjB,CACA,IAAK,MAAMC,KAAeL,EAAc,CACpC,MAAMG,EAAiB,IAChBE,EACH12B,KAAM,YAEVP,EAAQ7J,KAAK4gC,EACjB,CACA1wB,EAAmBrG,IAEvBk3B,MAAO,EAAG3jC,QAAOC,aACb,MAAMwjC,EAAkB,CACpBviC,GAAIA,EACJ8L,KAAM,aACNc,UAAU,EACVH,WAAY,CACR3N,QACAC,WAGRb,EAAMK,WAAWqT,mBAAmB,CAAC2wB,QAIjDR,EAAQj9B,QAAQmS,OAAO,CACnB+qB,kBACAU,WAAY,CACR7B,WACAC,YACAC,WACAG,aAEJC,kBACAC,kBACAG,gBACAC,WACAC,cACAH,iBAEG,KACHS,EAAQj9B,SAASkS,YAEtB,CACCgrB,EACAnB,EACAC,EACAC,EACAG,EACAC,EACAI,EACAC,EACAC,EACAH,IAEJ,MAAMqB,EAAqBX,EAAgBphC,MAAM,KACjD,OAAQ,IAAAb,KAAI,MAAO,CAAES,WAAW,OAAG,CAAC,6BAA8B,YAAamiC,EAAoBtI,EAAS75B,IAAaE,IAAKihC,EAAkBjjC,MAAO,IAC5IA,EACHmjC,WACIxY,GAAS,CAAE,CAACuY,EAAkB,kBAAoB,eAAgBvY,IACvEppB,SAAUA,GACrB,E", "sources": ["webpack://autogentstudio/./node_modules/@xyflow/react/dist/esm/index.js"], "sourcesContent": ["\"use client\"\nimport { jsxs, Fragment, jsx } from 'react/jsx-runtime';\nimport { createContext, useContext, useMemo, forwardRef, useEffect, useRef, useState, useLayoutEffect, useCallback, memo } from 'react';\nimport cc from 'classcat';\nimport { errorMessages, mergeAriaLabelConfig, infiniteExtent, isInputDOMNode, getViewportForBounds, pointToRendererPoint, rendererPointToPoint, isNodeBase, isEdgeBase, getElementsToRemove, isRectObject, nodeToRect, getOverlappingArea, getNodesBounds, withResolvers, evaluateAbsolutePosition, getDimensions, XYPanZoom, PanOnScrollMode, SelectionMode, getEventPosition, getNodesInside, areSetsEqual, XYDrag, snapPosition, calculateNodePosition, Position, ConnectionMode, isMouseEvent, XYHandle, getHostForElement, addEdge, getInternalNodesBounds, isNumeric, nodeHasDimensions, getNodeDimensions, elementSelectionKeys, isEdgeVisible, MarkerType, createMarkerIds, getBezierEdgeCenter, getSmoothStepPath, getStraightPath, getBezierPath, getEdgePosition, getElevatedEdgeZIndex, getMarkerId, getConnectionStatus, ConnectionLineType, updateConnectionLookup, adoptUserNodes, initialConnection, devWarn, defaultAriaLabelConfig, updateNodeInternals, updateAbsolutePositions, handleExpandParent, panBy, fitViewport, isMacOs, areConnectionMapsEqual, handleConnectionChange, shallowNodeData, XYMinimap, getBoundsOfRects, ResizeControlVariant, XYResizer, XY_RESIZER_LINE_POSITIONS, XY_RESIZER_HANDLE_POSITIONS, getNodeToolbarTransform } from '@xyflow/system';\nexport { ConnectionLineType, ConnectionMode, MarkerType, PanOnScrollMode, Position, ResizeControlVariant, SelectionMode, addEdge, getBezierEdgeCenter, getBezierPath, getConnectedEdges, getEdgeCenter, getIncomers, getNodesBounds, getOutgoers, getSmoothStepPath, getStraightPath, getViewportForBounds, reconnectEdge } from '@xyflow/system';\nimport { useStoreWithEqualityFn, createWithEqualityFn } from 'zustand/traditional';\nimport { shallow } from 'zustand/shallow';\nimport { createPortal } from 'react-dom';\n\nconst StoreContext = createContext(null);\nconst Provider$1 = StoreContext.Provider;\n\nconst zustandErrorMessage = errorMessages['error001']();\n/**\n * This hook can be used to subscribe to internal state changes of the React Flow\n * component. The `useStore` hook is re-exported from the [Zustand](https://github.com/pmndrs/zustand)\n * state management library, so you should check out their docs for more details.\n *\n * @public\n * @param selector - A selector function that returns a slice of the flow's internal state.\n * Extracting or transforming just the state you need is a good practice to avoid unnecessary\n * re-renders.\n * @param equalityFn - A function to compare the previous and next value. This is incredibly useful\n * for preventing unnecessary re-renders. Good sensible defaults are using `Object.is` or importing\n * `zustand/shallow`, but you can be as granular as you like.\n * @returns The selected state slice.\n *\n * @example\n * ```ts\n * const nodes = useStore((state) => state.nodes);\n * ```\n *\n * @remarks This hook should only be used if there is no other way to access the internal\n * state. For many of the common use cases, there are dedicated hooks available\n * such as {@link useReactFlow}, {@link useViewport}, etc.\n */\nfunction useStore(selector, equalityFn) {\n    const store = useContext(StoreContext);\n    if (store === null) {\n        throw new Error(zustandErrorMessage);\n    }\n    return useStoreWithEqualityFn(store, selector, equalityFn);\n}\n/**\n * In some cases, you might need to access the store directly. This hook returns the store object which can be used on demand to access the state or dispatch actions.\n *\n * @returns The store object.\n * @example\n * ```ts\n * const store = useStoreApi();\n * ```\n *\n * @remarks This hook should only be used if there is no other way to access the internal\n * state. For many of the common use cases, there are dedicated hooks available\n * such as {@link useReactFlow}, {@link useViewport}, etc.\n */\nfunction useStoreApi() {\n    const store = useContext(StoreContext);\n    if (store === null) {\n        throw new Error(zustandErrorMessage);\n    }\n    return useMemo(() => ({\n        getState: store.getState,\n        setState: store.setState,\n        subscribe: store.subscribe,\n    }), [store]);\n}\n\nconst style = { display: 'none' };\nconst ariaLiveStyle = {\n    position: 'absolute',\n    width: 1,\n    height: 1,\n    margin: -1,\n    border: 0,\n    padding: 0,\n    overflow: 'hidden',\n    clip: 'rect(0px, 0px, 0px, 0px)',\n    clipPath: 'inset(100%)',\n};\nconst ARIA_NODE_DESC_KEY = 'react-flow__node-desc';\nconst ARIA_EDGE_DESC_KEY = 'react-flow__edge-desc';\nconst ARIA_LIVE_MESSAGE = 'react-flow__aria-live';\nconst ariaLiveSelector = (s) => s.ariaLiveMessage;\nconst ariaLabelConfigSelector = (s) => s.ariaLabelConfig;\nfunction AriaLiveMessage({ rfId }) {\n    const ariaLiveMessage = useStore(ariaLiveSelector);\n    return (jsx(\"div\", { id: `${ARIA_LIVE_MESSAGE}-${rfId}`, \"aria-live\": \"assertive\", \"aria-atomic\": \"true\", style: ariaLiveStyle, children: ariaLiveMessage }));\n}\nfunction A11yDescriptions({ rfId, disableKeyboardA11y }) {\n    const ariaLabelConfig = useStore(ariaLabelConfigSelector);\n    return (jsxs(Fragment, { children: [jsx(\"div\", { id: `${ARIA_NODE_DESC_KEY}-${rfId}`, style: style, children: disableKeyboardA11y\n                    ? ariaLabelConfig['node.a11yDescription.default']\n                    : ariaLabelConfig['node.a11yDescription.keyboardDisabled'] }), jsx(\"div\", { id: `${ARIA_EDGE_DESC_KEY}-${rfId}`, style: style, children: ariaLabelConfig['edge.a11yDescription.default'] }), !disableKeyboardA11y && jsx(AriaLiveMessage, { rfId: rfId })] }));\n}\n\n/**\n * The `<Panel />` component helps you position content above the viewport.\n * It is used internally by the [`<MiniMap />`](/api-reference/components/minimap)\n * and [`<Controls />`](/api-reference/components/controls) components.\n *\n * @public\n *\n * @example\n * ```jsx\n *import { ReactFlow, Background, Panel } from '@xyflow/react';\n *\n *export default function Flow() {\n *  return (\n *    <ReactFlow nodes={[]} fitView>\n *      <Panel position=\"top-left\">top-left</Panel>\n *      <Panel position=\"top-center\">top-center</Panel>\n *      <Panel position=\"top-right\">top-right</Panel>\n *      <Panel position=\"bottom-left\">bottom-left</Panel>\n *      <Panel position=\"bottom-center\">bottom-center</Panel>\n *      <Panel position=\"bottom-right\">bottom-right</Panel>\n *    </ReactFlow>\n *  );\n *}\n *```\n */\nconst Panel = forwardRef(({ position = 'top-left', children, className, style, ...rest }, ref) => {\n    const positionClasses = `${position}`.split('-');\n    return (jsx(\"div\", { className: cc(['react-flow__panel', className, ...positionClasses]), style: style, ref: ref, ...rest, children: children }));\n});\nPanel.displayName = 'Panel';\n\nfunction Attribution({ proOptions, position = 'bottom-right' }) {\n    if (proOptions?.hideAttribution) {\n        return null;\n    }\n    return (jsx(Panel, { position: position, className: \"react-flow__attribution\", \"data-message\": \"Please only hide this attribution when you are subscribed to React Flow Pro: https://pro.reactflow.dev\", children: jsx(\"a\", { href: \"https://reactflow.dev\", target: \"_blank\", rel: \"noopener noreferrer\", \"aria-label\": \"React Flow attribution\", children: \"React Flow\" }) }));\n}\n\nconst selector$m = (s) => {\n    const selectedNodes = [];\n    const selectedEdges = [];\n    for (const [, node] of s.nodeLookup) {\n        if (node.selected) {\n            selectedNodes.push(node.internals.userNode);\n        }\n    }\n    for (const [, edge] of s.edgeLookup) {\n        if (edge.selected) {\n            selectedEdges.push(edge);\n        }\n    }\n    return { selectedNodes, selectedEdges };\n};\nconst selectId = (obj) => obj.id;\nfunction areEqual(a, b) {\n    return (shallow(a.selectedNodes.map(selectId), b.selectedNodes.map(selectId)) &&\n        shallow(a.selectedEdges.map(selectId), b.selectedEdges.map(selectId)));\n}\nfunction SelectionListenerInner({ onSelectionChange, }) {\n    const store = useStoreApi();\n    const { selectedNodes, selectedEdges } = useStore(selector$m, areEqual);\n    useEffect(() => {\n        const params = { nodes: selectedNodes, edges: selectedEdges };\n        onSelectionChange?.(params);\n        store.getState().onSelectionChangeHandlers.forEach((fn) => fn(params));\n    }, [selectedNodes, selectedEdges, onSelectionChange]);\n    return null;\n}\nconst changeSelector = (s) => !!s.onSelectionChangeHandlers;\nfunction SelectionListener({ onSelectionChange, }) {\n    const storeHasSelectionChangeHandlers = useStore(changeSelector);\n    if (onSelectionChange || storeHasSelectionChangeHandlers) {\n        return jsx(SelectionListenerInner, { onSelectionChange: onSelectionChange });\n    }\n    return null;\n}\n\nconst defaultNodeOrigin = [0, 0];\nconst defaultViewport = { x: 0, y: 0, zoom: 1 };\n\n/*\n * This component helps us to update the store with the values coming from the user.\n * We distinguish between values we can update directly with `useDirectStoreUpdater` (like `snapGrid`)\n * and values that have a dedicated setter function in the store (like `setNodes`).\n */\n// These fields exist in the global store, and we need to keep them up to date\nconst reactFlowFieldsToTrack = [\n    'nodes',\n    'edges',\n    'defaultNodes',\n    'defaultEdges',\n    'onConnect',\n    'onConnectStart',\n    'onConnectEnd',\n    'onClickConnectStart',\n    'onClickConnectEnd',\n    'nodesDraggable',\n    'autoPanOnNodeFocus',\n    'nodesConnectable',\n    'nodesFocusable',\n    'edgesFocusable',\n    'edgesReconnectable',\n    'elevateNodesOnSelect',\n    'elevateEdgesOnSelect',\n    'minZoom',\n    'maxZoom',\n    'nodeExtent',\n    'onNodesChange',\n    'onEdgesChange',\n    'elementsSelectable',\n    'connectionMode',\n    'snapGrid',\n    'snapToGrid',\n    'translateExtent',\n    'connectOnClick',\n    'defaultEdgeOptions',\n    'fitView',\n    'fitViewOptions',\n    'onNodesDelete',\n    'onEdgesDelete',\n    'onDelete',\n    'onNodeDrag',\n    'onNodeDragStart',\n    'onNodeDragStop',\n    'onSelectionDrag',\n    'onSelectionDragStart',\n    'onSelectionDragStop',\n    'onMoveStart',\n    'onMove',\n    'onMoveEnd',\n    'noPanClassName',\n    'nodeOrigin',\n    'autoPanOnConnect',\n    'autoPanOnNodeDrag',\n    'onError',\n    'connectionRadius',\n    'isValidConnection',\n    'selectNodesOnDrag',\n    'nodeDragThreshold',\n    'connectionDragThreshold',\n    'onBeforeDelete',\n    'debug',\n    'autoPanSpeed',\n    'paneClickDistance',\n    'ariaLabelConfig',\n];\n// rfId doesn't exist in ReactFlowProps, but it's one of the fields we want to update\nconst fieldsToTrack = [...reactFlowFieldsToTrack, 'rfId'];\nconst selector$l = (s) => ({\n    setNodes: s.setNodes,\n    setEdges: s.setEdges,\n    setMinZoom: s.setMinZoom,\n    setMaxZoom: s.setMaxZoom,\n    setTranslateExtent: s.setTranslateExtent,\n    setNodeExtent: s.setNodeExtent,\n    reset: s.reset,\n    setDefaultNodesAndEdges: s.setDefaultNodesAndEdges,\n    setPaneClickDistance: s.setPaneClickDistance,\n});\nconst initPrevValues = {\n    /*\n     * these are values that are also passed directly to other components\n     * than the StoreUpdater. We can reduce the number of setStore calls\n     * by setting the same values here as prev fields.\n     */\n    translateExtent: infiniteExtent,\n    nodeOrigin: defaultNodeOrigin,\n    minZoom: 0.5,\n    maxZoom: 2,\n    elementsSelectable: true,\n    noPanClassName: 'nopan',\n    rfId: '1',\n    paneClickDistance: 0,\n};\nfunction StoreUpdater(props) {\n    const { setNodes, setEdges, setMinZoom, setMaxZoom, setTranslateExtent, setNodeExtent, reset, setDefaultNodesAndEdges, setPaneClickDistance, } = useStore(selector$l, shallow);\n    const store = useStoreApi();\n    useEffect(() => {\n        setDefaultNodesAndEdges(props.defaultNodes, props.defaultEdges);\n        return () => {\n            // when we reset the store we also need to reset the previous fields\n            previousFields.current = initPrevValues;\n            reset();\n        };\n    }, []);\n    const previousFields = useRef(initPrevValues);\n    useEffect(() => {\n        for (const fieldName of fieldsToTrack) {\n            const fieldValue = props[fieldName];\n            const previousFieldValue = previousFields.current[fieldName];\n            if (fieldValue === previousFieldValue)\n                continue;\n            if (typeof props[fieldName] === 'undefined')\n                continue;\n            // Custom handling with dedicated setters for some fields\n            if (fieldName === 'nodes')\n                setNodes(fieldValue);\n            else if (fieldName === 'edges')\n                setEdges(fieldValue);\n            else if (fieldName === 'minZoom')\n                setMinZoom(fieldValue);\n            else if (fieldName === 'maxZoom')\n                setMaxZoom(fieldValue);\n            else if (fieldName === 'translateExtent')\n                setTranslateExtent(fieldValue);\n            else if (fieldName === 'nodeExtent')\n                setNodeExtent(fieldValue);\n            else if (fieldName === 'paneClickDistance')\n                setPaneClickDistance(fieldValue);\n            else if (fieldName === 'ariaLabelConfig')\n                store.setState({ ariaLabelConfig: mergeAriaLabelConfig(fieldValue) });\n            // Renamed fields\n            else if (fieldName === 'fitView')\n                store.setState({ fitViewQueued: fieldValue });\n            else if (fieldName === 'fitViewOptions')\n                store.setState({ fitViewOptions: fieldValue });\n            // General case\n            else\n                store.setState({ [fieldName]: fieldValue });\n        }\n        previousFields.current = props;\n    }, \n    // Only re-run the effect if one of the fields we track changes\n    fieldsToTrack.map((fieldName) => props[fieldName]));\n    return null;\n}\n\nfunction getMediaQuery() {\n    if (typeof window === 'undefined' || !window.matchMedia) {\n        return null;\n    }\n    return window.matchMedia('(prefers-color-scheme: dark)');\n}\n/**\n * Hook for receiving the current color mode class 'dark' or 'light'.\n *\n * @internal\n * @param colorMode - The color mode to use ('dark', 'light' or 'system')\n */\nfunction useColorModeClass(colorMode) {\n    const [colorModeClass, setColorModeClass] = useState(colorMode === 'system' ? null : colorMode);\n    useEffect(() => {\n        if (colorMode !== 'system') {\n            setColorModeClass(colorMode);\n            return;\n        }\n        const mediaQuery = getMediaQuery();\n        const updateColorModeClass = () => setColorModeClass(mediaQuery?.matches ? 'dark' : 'light');\n        updateColorModeClass();\n        mediaQuery?.addEventListener('change', updateColorModeClass);\n        return () => {\n            mediaQuery?.removeEventListener('change', updateColorModeClass);\n        };\n    }, [colorMode]);\n    return colorModeClass !== null ? colorModeClass : getMediaQuery()?.matches ? 'dark' : 'light';\n}\n\nconst defaultDoc = typeof document !== 'undefined' ? document : null;\n/**\n * This hook lets you listen for specific key codes and tells you whether they are\n * currently pressed or not.\n *\n * @public\n * @param options - Options\n *\n * @example\n * ```tsx\n *import { useKeyPress } from '@xyflow/react';\n *\n *export default function () {\n *  const spacePressed = useKeyPress('Space');\n *  const cmdAndSPressed = useKeyPress(['Meta+s', 'Strg+s']);\n *\n *  return (\n *    <div>\n *     {spacePressed && <p>Space pressed!</p>}\n *     {cmdAndSPressed && <p>Cmd + S pressed!</p>}\n *    </div>\n *  );\n *}\n *```\n */\nfunction useKeyPress(\n/**\n * The key code (string or array of strings) specifies which key(s) should trigger\n * an action.\n *\n * A **string** can represent:\n * - A **single key**, e.g. `'a'`\n * - A **key combination**, using `'+'` to separate keys, e.g. `'a+d'`\n *\n * An  **array of strings** represents **multiple possible key inputs**. For example, `['a', 'd+s']`\n * means the user can press either the single key `'a'` or the combination of `'d'` and `'s'`.\n * @default null\n */\nkeyCode = null, options = { target: defaultDoc, actInsideInputWithModifier: true }) {\n    const [keyPressed, setKeyPressed] = useState(false);\n    // we need to remember if a modifier key is pressed in order to track it\n    const modifierPressed = useRef(false);\n    // we need to remember the pressed keys in order to support combinations\n    const pressedKeys = useRef(new Set([]));\n    /*\n     * keyCodes = array with single keys [['a']] or key combinations [['a', 's']]\n     * keysToWatch = array with all keys flattened ['a', 'd', 'ShiftLeft']\n     * used to check if we store event.code or event.key. When the code is in the list of keysToWatch\n     * we use the code otherwise the key. Explainer: When you press the left \"command\" key, the code is \"MetaLeft\"\n     * and the key is \"Meta\". We want users to be able to pass keys and codes so we assume that the key is meant when\n     * we can't find it in the list of keysToWatch.\n     */\n    const [keyCodes, keysToWatch] = useMemo(() => {\n        if (keyCode !== null) {\n            const keyCodeArr = Array.isArray(keyCode) ? keyCode : [keyCode];\n            const keys = keyCodeArr\n                .filter((kc) => typeof kc === 'string')\n                /*\n                 * we first replace all '+' with '\\n'  which we will use to split the keys on\n                 * then we replace '\\n\\n' with '\\n+', this way we can also support the combination 'key++'\n                 * in the end we simply split on '\\n' to get the key array\n                 */\n                .map((kc) => kc.replace('+', '\\n').replace('\\n\\n', '\\n+').split('\\n'));\n            const keysFlat = keys.reduce((res, item) => res.concat(...item), []);\n            return [keys, keysFlat];\n        }\n        return [[], []];\n    }, [keyCode]);\n    useEffect(() => {\n        const target = options?.target ?? defaultDoc;\n        const actInsideInputWithModifier = options?.actInsideInputWithModifier ?? true;\n        if (keyCode !== null) {\n            const downHandler = (event) => {\n                modifierPressed.current = event.ctrlKey || event.metaKey || event.shiftKey || event.altKey;\n                const preventAction = (!modifierPressed.current || (modifierPressed.current && !actInsideInputWithModifier)) &&\n                    isInputDOMNode(event);\n                if (preventAction) {\n                    return false;\n                }\n                const keyOrCode = useKeyOrCode(event.code, keysToWatch);\n                pressedKeys.current.add(event[keyOrCode]);\n                if (isMatchingKey(keyCodes, pressedKeys.current, false)) {\n                    const target = (event.composedPath?.()?.[0] || event.target);\n                    const isInteractiveElement = target?.nodeName === 'BUTTON' || target?.nodeName === 'A';\n                    if (options.preventDefault !== false && (modifierPressed.current || !isInteractiveElement)) {\n                        event.preventDefault();\n                    }\n                    setKeyPressed(true);\n                }\n            };\n            const upHandler = (event) => {\n                const keyOrCode = useKeyOrCode(event.code, keysToWatch);\n                if (isMatchingKey(keyCodes, pressedKeys.current, true)) {\n                    setKeyPressed(false);\n                    pressedKeys.current.clear();\n                }\n                else {\n                    pressedKeys.current.delete(event[keyOrCode]);\n                }\n                // fix for Mac: when cmd key is pressed, keyup is not triggered for any other key, see: https://stackoverflow.com/questions/27380018/when-cmd-key-is-kept-pressed-keyup-is-not-triggered-for-any-other-key\n                if (event.key === 'Meta') {\n                    pressedKeys.current.clear();\n                }\n                modifierPressed.current = false;\n            };\n            const resetHandler = () => {\n                pressedKeys.current.clear();\n                setKeyPressed(false);\n            };\n            target?.addEventListener('keydown', downHandler);\n            target?.addEventListener('keyup', upHandler);\n            window.addEventListener('blur', resetHandler);\n            window.addEventListener('contextmenu', resetHandler);\n            return () => {\n                target?.removeEventListener('keydown', downHandler);\n                target?.removeEventListener('keyup', upHandler);\n                window.removeEventListener('blur', resetHandler);\n                window.removeEventListener('contextmenu', resetHandler);\n            };\n        }\n    }, [keyCode, setKeyPressed]);\n    return keyPressed;\n}\n// utils\nfunction isMatchingKey(keyCodes, pressedKeys, isUp) {\n    return (keyCodes\n        /*\n         * we only want to compare same sizes of keyCode definitions\n         * and pressed keys. When the user specified 'Meta' as a key somewhere\n         * this would also be truthy without this filter when user presses 'Meta' + 'r'\n         */\n        .filter((keys) => isUp || keys.length === pressedKeys.size)\n        /*\n         * since we want to support multiple possibilities only one of the\n         * combinations need to be part of the pressed keys\n         */\n        .some((keys) => keys.every((k) => pressedKeys.has(k))));\n}\nfunction useKeyOrCode(eventCode, keysToWatch) {\n    return keysToWatch.includes(eventCode) ? 'code' : 'key';\n}\n\n/**\n * Hook for getting viewport helper functions.\n *\n * @internal\n * @returns viewport helper functions\n */\nconst useViewportHelper = () => {\n    const store = useStoreApi();\n    return useMemo(() => {\n        return {\n            zoomIn: (options) => {\n                const { panZoom } = store.getState();\n                return panZoom ? panZoom.scaleBy(1.2, { duration: options?.duration }) : Promise.resolve(false);\n            },\n            zoomOut: (options) => {\n                const { panZoom } = store.getState();\n                return panZoom ? panZoom.scaleBy(1 / 1.2, { duration: options?.duration }) : Promise.resolve(false);\n            },\n            zoomTo: (zoomLevel, options) => {\n                const { panZoom } = store.getState();\n                return panZoom ? panZoom.scaleTo(zoomLevel, { duration: options?.duration }) : Promise.resolve(false);\n            },\n            getZoom: () => store.getState().transform[2],\n            setViewport: async (viewport, options) => {\n                const { transform: [tX, tY, tZoom], panZoom, } = store.getState();\n                if (!panZoom) {\n                    return Promise.resolve(false);\n                }\n                await panZoom.setViewport({\n                    x: viewport.x ?? tX,\n                    y: viewport.y ?? tY,\n                    zoom: viewport.zoom ?? tZoom,\n                }, options);\n                return Promise.resolve(true);\n            },\n            getViewport: () => {\n                const [x, y, zoom] = store.getState().transform;\n                return { x, y, zoom };\n            },\n            setCenter: async (x, y, options) => {\n                return store.getState().setCenter(x, y, options);\n            },\n            fitBounds: async (bounds, options) => {\n                const { width, height, minZoom, maxZoom, panZoom } = store.getState();\n                const viewport = getViewportForBounds(bounds, width, height, minZoom, maxZoom, options?.padding ?? 0.1);\n                if (!panZoom) {\n                    return Promise.resolve(false);\n                }\n                await panZoom.setViewport(viewport, {\n                    duration: options?.duration,\n                    ease: options?.ease,\n                    interpolate: options?.interpolate,\n                });\n                return Promise.resolve(true);\n            },\n            screenToFlowPosition: (clientPosition, options = {}) => {\n                const { transform, snapGrid, snapToGrid, domNode } = store.getState();\n                if (!domNode) {\n                    return clientPosition;\n                }\n                const { x: domX, y: domY } = domNode.getBoundingClientRect();\n                const correctedPosition = {\n                    x: clientPosition.x - domX,\n                    y: clientPosition.y - domY,\n                };\n                const _snapGrid = options.snapGrid ?? snapGrid;\n                const _snapToGrid = options.snapToGrid ?? snapToGrid;\n                return pointToRendererPoint(correctedPosition, transform, _snapToGrid, _snapGrid);\n            },\n            flowToScreenPosition: (flowPosition) => {\n                const { transform, domNode } = store.getState();\n                if (!domNode) {\n                    return flowPosition;\n                }\n                const { x: domX, y: domY } = domNode.getBoundingClientRect();\n                const rendererPosition = rendererPointToPoint(flowPosition, transform);\n                return {\n                    x: rendererPosition.x + domX,\n                    y: rendererPosition.y + domY,\n                };\n            },\n        };\n    }, []);\n};\n\n/*\n * This function applies changes to nodes or edges that are triggered by React Flow internally.\n * When you drag a node for example, React Flow will send a position change update.\n * This function then applies the changes and returns the updated elements.\n */\nfunction applyChanges(changes, elements) {\n    const updatedElements = [];\n    /*\n     * By storing a map of changes for each element, we can a quick lookup as we\n     * iterate over the elements array!\n     */\n    const changesMap = new Map();\n    const addItemChanges = [];\n    for (const change of changes) {\n        if (change.type === 'add') {\n            addItemChanges.push(change);\n            continue;\n        }\n        else if (change.type === 'remove' || change.type === 'replace') {\n            /*\n             * For a 'remove' change we can safely ignore any other changes queued for\n             * the same element, it's going to be removed anyway!\n             */\n            changesMap.set(change.id, [change]);\n        }\n        else {\n            const elementChanges = changesMap.get(change.id);\n            if (elementChanges) {\n                /*\n                 * If we have some changes queued already, we can do a mutable update of\n                 * that array and save ourselves some copying.\n                 */\n                elementChanges.push(change);\n            }\n            else {\n                changesMap.set(change.id, [change]);\n            }\n        }\n    }\n    for (const element of elements) {\n        const changes = changesMap.get(element.id);\n        /*\n         * When there are no changes for an element we can just push it unmodified,\n         * no need to copy it.\n         */\n        if (!changes) {\n            updatedElements.push(element);\n            continue;\n        }\n        // If we have a 'remove' change queued, it'll be the only change in the array\n        if (changes[0].type === 'remove') {\n            continue;\n        }\n        if (changes[0].type === 'replace') {\n            updatedElements.push({ ...changes[0].item });\n            continue;\n        }\n        /**\n         * For other types of changes, we want to start with a shallow copy of the\n         * object so React knows this element has changed. Sequential changes will\n         * each _mutate_ this object, so there's only ever one copy.\n         */\n        const updatedElement = { ...element };\n        for (const change of changes) {\n            applyChange(change, updatedElement);\n        }\n        updatedElements.push(updatedElement);\n    }\n    /*\n     * we need to wait for all changes to be applied before adding new items\n     * to be able to add them at the correct index\n     */\n    if (addItemChanges.length) {\n        addItemChanges.forEach((change) => {\n            if (change.index !== undefined) {\n                updatedElements.splice(change.index, 0, { ...change.item });\n            }\n            else {\n                updatedElements.push({ ...change.item });\n            }\n        });\n    }\n    return updatedElements;\n}\n// Applies a single change to an element. This is a *mutable* update.\nfunction applyChange(change, element) {\n    switch (change.type) {\n        case 'select': {\n            element.selected = change.selected;\n            break;\n        }\n        case 'position': {\n            if (typeof change.position !== 'undefined') {\n                element.position = change.position;\n            }\n            if (typeof change.dragging !== 'undefined') {\n                element.dragging = change.dragging;\n            }\n            break;\n        }\n        case 'dimensions': {\n            if (typeof change.dimensions !== 'undefined') {\n                element.measured ??= {};\n                element.measured.width = change.dimensions.width;\n                element.measured.height = change.dimensions.height;\n                if (change.setAttributes) {\n                    if (change.setAttributes === true || change.setAttributes === 'width') {\n                        element.width = change.dimensions.width;\n                    }\n                    if (change.setAttributes === true || change.setAttributes === 'height') {\n                        element.height = change.dimensions.height;\n                    }\n                }\n            }\n            if (typeof change.resizing === 'boolean') {\n                element.resizing = change.resizing;\n            }\n            break;\n        }\n    }\n}\n/**\n * Drop in function that applies node changes to an array of nodes.\n * @public\n * @param changes - Array of changes to apply.\n * @param nodes - Array of nodes to apply the changes to.\n * @returns Array of updated nodes.\n * @example\n *```tsx\n *import { useState, useCallback } from 'react';\n *import { ReactFlow, applyNodeChanges, type Node, type Edge, type OnNodesChange } from '@xyflow/react';\n *\n *export default function Flow() {\n *  const [nodes, setNodes] = useState<Node[]>([]);\n *  const [edges, setEdges] = useState<Edge[]>([]);\n *  const onNodesChange: OnNodesChange = useCallback(\n *    (changes) => {\n *      setNodes((oldNodes) => applyNodeChanges(changes, oldNodes));\n *    },\n *    [setNodes],\n *  );\n *\n *  return (\n *    <ReactFlow nodes={nodes} edges={edges} onNodesChange={onNodesChange} />\n *  );\n *}\n *```\n * @remarks Various events on the <ReactFlow /> component can produce an {@link NodeChange}\n * that describes how to update the edges of your flow in some way.\n * If you don't need any custom behaviour, this util can be used to take an array\n * of these changes and apply them to your edges.\n */\nfunction applyNodeChanges(changes, nodes) {\n    return applyChanges(changes, nodes);\n}\n/**\n * Drop in function that applies edge changes to an array of edges.\n * @public\n * @param changes - Array of changes to apply.\n * @param edges - Array of edge to apply the changes to.\n * @returns Array of updated edges.\n * @example\n * ```tsx\n *import { useState, useCallback } from 'react';\n *import { ReactFlow, applyEdgeChanges } from '@xyflow/react';\n *\n *export default function Flow() {\n *  const [nodes, setNodes] = useState([]);\n *  const [edges, setEdges] = useState([]);\n *  const onEdgesChange = useCallback(\n *    (changes) => {\n *      setEdges((oldEdges) => applyEdgeChanges(changes, oldEdges));\n *    },\n *    [setEdges],\n *  );\n *\n *  return (\n *    <ReactFlow nodes={nodes} edges={edges} onEdgesChange={onEdgesChange} />\n *  );\n *}\n *```\n * @remarks Various events on the <ReactFlow /> component can produce an {@link EdgeChange}\n * that describes how to update the edges of your flow in some way.\n * If you don't need any custom behaviour, this util can be used to take an array\n * of these changes and apply them to your edges.\n */\nfunction applyEdgeChanges(changes, edges) {\n    return applyChanges(changes, edges);\n}\nfunction createSelectionChange(id, selected) {\n    return {\n        id,\n        type: 'select',\n        selected,\n    };\n}\nfunction getSelectionChanges(items, selectedIds = new Set(), mutateItem = false) {\n    const changes = [];\n    for (const [id, item] of items) {\n        const willBeSelected = selectedIds.has(id);\n        // we don't want to set all items to selected=false on the first selection\n        if (!(item.selected === undefined && !willBeSelected) && item.selected !== willBeSelected) {\n            if (mutateItem) {\n                /*\n                 * this hack is needed for nodes. When the user dragged a node, it's selected.\n                 * When another node gets dragged, we need to deselect the previous one,\n                 * in order to have only one selected node at a time - the onNodesChange callback comes too late here :/\n                 */\n                item.selected = willBeSelected;\n            }\n            changes.push(createSelectionChange(item.id, willBeSelected));\n        }\n    }\n    return changes;\n}\nfunction getElementsDiffChanges({ items = [], lookup, }) {\n    const changes = [];\n    const itemsLookup = new Map(items.map((item) => [item.id, item]));\n    for (const [index, item] of items.entries()) {\n        const lookupItem = lookup.get(item.id);\n        const storeItem = lookupItem?.internals?.userNode ?? lookupItem;\n        if (storeItem !== undefined && storeItem !== item) {\n            changes.push({ id: item.id, item: item, type: 'replace' });\n        }\n        if (storeItem === undefined) {\n            changes.push({ item: item, type: 'add', index });\n        }\n    }\n    for (const [id] of lookup) {\n        const nextNode = itemsLookup.get(id);\n        if (nextNode === undefined) {\n            changes.push({ id, type: 'remove' });\n        }\n    }\n    return changes;\n}\nfunction elementToRemoveChange(item) {\n    return {\n        id: item.id,\n        type: 'remove',\n    };\n}\n\n/**\n * Test whether an object is usable as an [`Node`](/api-reference/types/node).\n * In TypeScript this is a type guard that will narrow the type of whatever you pass in to\n * [`Node`](/api-reference/types/node) if it returns `true`.\n *\n * @public\n * @remarks In TypeScript this is a type guard that will narrow the type of whatever you pass in to Node if it returns true\n * @param element - The element to test.\n * @returns Tests whether the provided value can be used as a `Node`. If you're using TypeScript,\n * this function acts as a type guard and will narrow the type of the value to `Node` if it returns\n * `true`.\n *\n * @example\n * ```js\n *import { isNode } from '@xyflow/react';\n *\n *if (isNode(node)) {\n * // ...\n *}\n *```\n */\nconst isNode = (element) => isNodeBase(element);\n/**\n * Test whether an object is usable as an [`Edge`](/api-reference/types/edge).\n * In TypeScript this is a type guard that will narrow the type of whatever you pass in to\n * [`Edge`](/api-reference/types/edge) if it returns `true`.\n *\n * @public\n * @remarks In TypeScript this is a type guard that will narrow the type of whatever you pass in to Edge if it returns true\n * @param element - The element to test\n * @returns Tests whether the provided value can be used as an `Edge`. If you're using TypeScript,\n * this function acts as a type guard and will narrow the type of the value to `Edge` if it returns\n * `true`.\n *\n * @example\n * ```js\n *import { isEdge } from '@xyflow/react';\n *\n *if (isEdge(edge)) {\n * // ...\n *}\n *```\n */\nconst isEdge = (element) => isEdgeBase(element);\n// eslint-disable-next-line @typescript-eslint/no-empty-object-type\nfunction fixedForwardRef(render) {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return forwardRef(render);\n}\n\n// we need this hook to prevent a warning when using react-flow in SSR\nconst useIsomorphicLayoutEffect = typeof window !== 'undefined' ? useLayoutEffect : useEffect;\n\n/**\n * This hook returns a queue that can be used to batch updates.\n *\n * @param runQueue - a function that gets called when the queue is flushed\n * @internal\n *\n * @returns a Queue object\n */\nfunction useQueue(runQueue) {\n    /*\n     * Because we're using a ref above, we need some way to let React know when to\n     * actually process the queue. We increment this number any time we mutate the\n     * queue, creating a new state to trigger the layout effect below.\n     * Using a boolean dirty flag here instead would lead to issues related to\n     * automatic batching. (https://github.com/xyflow/xyflow/issues/4779)\n     */\n    const [serial, setSerial] = useState(BigInt(0));\n    /*\n     * A reference of all the batched updates to process before the next render. We\n     * want a reference here so multiple synchronous calls to `setNodes` etc can be\n     * batched together.\n     */\n    const [queue] = useState(() => createQueue(() => setSerial(n => n + BigInt(1))));\n    /*\n     * Layout effects are guaranteed to run before the next render which means we\n     * shouldn't run into any issues with stale state or weird issues that come from\n     * rendering things one frame later than expected (we used to use `setTimeout`).\n     */\n    useIsomorphicLayoutEffect(() => {\n        const queueItems = queue.get();\n        if (queueItems.length) {\n            runQueue(queueItems);\n            queue.reset();\n        }\n    }, [serial]);\n    return queue;\n}\nfunction createQueue(cb) {\n    let queue = [];\n    return {\n        get: () => queue,\n        reset: () => {\n            queue = [];\n        },\n        push: (item) => {\n            queue.push(item);\n            cb();\n        },\n    };\n}\n\nconst BatchContext = createContext(null);\n/**\n * This is a context provider that holds and processes the node and edge update queues\n * that are needed to handle setNodes, addNodes, setEdges and addEdges.\n *\n * @internal\n */\nfunction BatchProvider({ children, }) {\n    const store = useStoreApi();\n    const nodeQueueHandler = useCallback((queueItems) => {\n        const { nodes = [], setNodes, hasDefaultNodes, onNodesChange, nodeLookup, fitViewQueued } = store.getState();\n        /*\n         * This is essentially an `Array.reduce` in imperative clothing. Processing\n         * this queue is a relatively hot path so we'd like to avoid the overhead of\n         * array methods where we can.\n         */\n        let next = nodes;\n        for (const payload of queueItems) {\n            next = typeof payload === 'function' ? payload(next) : payload;\n        }\n        const changes = getElementsDiffChanges({\n            items: next,\n            lookup: nodeLookup,\n        });\n        if (hasDefaultNodes) {\n            setNodes(next);\n        }\n        // We only want to fire onNodesChange if there are changes to the nodes\n        if (changes.length > 0) {\n            onNodesChange?.(changes);\n        }\n        else if (fitViewQueued) {\n            // If there are no changes to the nodes, we still need to call setNodes\n            // to trigger a re-render and fitView.\n            window.requestAnimationFrame(() => {\n                const { fitViewQueued, nodes, setNodes } = store.getState();\n                if (fitViewQueued) {\n                    setNodes(nodes);\n                }\n            });\n        }\n    }, []);\n    const nodeQueue = useQueue(nodeQueueHandler);\n    const edgeQueueHandler = useCallback((queueItems) => {\n        const { edges = [], setEdges, hasDefaultEdges, onEdgesChange, edgeLookup } = store.getState();\n        let next = edges;\n        for (const payload of queueItems) {\n            next = typeof payload === 'function' ? payload(next) : payload;\n        }\n        if (hasDefaultEdges) {\n            setEdges(next);\n        }\n        else if (onEdgesChange) {\n            onEdgesChange(getElementsDiffChanges({\n                items: next,\n                lookup: edgeLookup,\n            }));\n        }\n    }, []);\n    const edgeQueue = useQueue(edgeQueueHandler);\n    const value = useMemo(() => ({ nodeQueue, edgeQueue }), []);\n    return jsx(BatchContext.Provider, { value: value, children: children });\n}\nfunction useBatchContext() {\n    const batchContext = useContext(BatchContext);\n    if (!batchContext) {\n        throw new Error('useBatchContext must be used within a BatchProvider');\n    }\n    return batchContext;\n}\n\nconst selector$k = (s) => !!s.panZoom;\n/**\n * This hook returns a ReactFlowInstance that can be used to update nodes and edges, manipulate the viewport, or query the current state of the flow.\n *\n * @public\n * @example\n * ```jsx\n *import { useCallback, useState } from 'react';\n *import { useReactFlow } from '@xyflow/react';\n *\n *export function NodeCounter() {\n *  const reactFlow = useReactFlow();\n *  const [count, setCount] = useState(0);\n *  const countNodes = useCallback(() => {\n *    setCount(reactFlow.getNodes().length);\n *    // you need to pass it as a dependency if you are using it with useEffect or useCallback\n *    // because at the first render, it's not initialized yet and some functions might not work.\n *  }, [reactFlow]);\n *\n *  return (\n *    <div>\n *      <button onClick={countNodes}>Update count</button>\n *      <p>There are {count} nodes in the flow.</p>\n *    </div>\n *  );\n *}\n *```\n */\nfunction useReactFlow() {\n    const viewportHelper = useViewportHelper();\n    const store = useStoreApi();\n    const batchContext = useBatchContext();\n    const viewportInitialized = useStore(selector$k);\n    const generalHelper = useMemo(() => {\n        const getInternalNode = (id) => store.getState().nodeLookup.get(id);\n        const setNodes = (payload) => {\n            batchContext.nodeQueue.push(payload);\n        };\n        const setEdges = (payload) => {\n            batchContext.edgeQueue.push(payload);\n        };\n        const getNodeRect = (node) => {\n            const { nodeLookup, nodeOrigin } = store.getState();\n            const nodeToUse = isNode(node) ? node : nodeLookup.get(node.id);\n            const position = nodeToUse.parentId\n                ? evaluateAbsolutePosition(nodeToUse.position, nodeToUse.measured, nodeToUse.parentId, nodeLookup, nodeOrigin)\n                : nodeToUse.position;\n            const nodeWithPosition = {\n                ...nodeToUse,\n                position,\n                width: nodeToUse.measured?.width ?? nodeToUse.width,\n                height: nodeToUse.measured?.height ?? nodeToUse.height,\n            };\n            return nodeToRect(nodeWithPosition);\n        };\n        const updateNode = (id, nodeUpdate, options = { replace: false }) => {\n            setNodes((prevNodes) => prevNodes.map((node) => {\n                if (node.id === id) {\n                    const nextNode = typeof nodeUpdate === 'function' ? nodeUpdate(node) : nodeUpdate;\n                    return options.replace && isNode(nextNode) ? nextNode : { ...node, ...nextNode };\n                }\n                return node;\n            }));\n        };\n        const updateEdge = (id, edgeUpdate, options = { replace: false }) => {\n            setEdges((prevEdges) => prevEdges.map((edge) => {\n                if (edge.id === id) {\n                    const nextEdge = typeof edgeUpdate === 'function' ? edgeUpdate(edge) : edgeUpdate;\n                    return options.replace && isEdge(nextEdge) ? nextEdge : { ...edge, ...nextEdge };\n                }\n                return edge;\n            }));\n        };\n        return {\n            getNodes: () => store.getState().nodes.map((n) => ({ ...n })),\n            getNode: (id) => getInternalNode(id)?.internals.userNode,\n            getInternalNode,\n            getEdges: () => {\n                const { edges = [] } = store.getState();\n                return edges.map((e) => ({ ...e }));\n            },\n            getEdge: (id) => store.getState().edgeLookup.get(id),\n            setNodes,\n            setEdges,\n            addNodes: (payload) => {\n                const newNodes = Array.isArray(payload) ? payload : [payload];\n                batchContext.nodeQueue.push((nodes) => [...nodes, ...newNodes]);\n            },\n            addEdges: (payload) => {\n                const newEdges = Array.isArray(payload) ? payload : [payload];\n                batchContext.edgeQueue.push((edges) => [...edges, ...newEdges]);\n            },\n            toObject: () => {\n                const { nodes = [], edges = [], transform } = store.getState();\n                const [x, y, zoom] = transform;\n                return {\n                    nodes: nodes.map((n) => ({ ...n })),\n                    edges: edges.map((e) => ({ ...e })),\n                    viewport: {\n                        x,\n                        y,\n                        zoom,\n                    },\n                };\n            },\n            deleteElements: async ({ nodes: nodesToRemove = [], edges: edgesToRemove = [] }) => {\n                const { nodes, edges, onNodesDelete, onEdgesDelete, triggerNodeChanges, triggerEdgeChanges, onDelete, onBeforeDelete, } = store.getState();\n                const { nodes: matchingNodes, edges: matchingEdges } = await getElementsToRemove({\n                    nodesToRemove,\n                    edgesToRemove,\n                    nodes,\n                    edges,\n                    onBeforeDelete,\n                });\n                const hasMatchingEdges = matchingEdges.length > 0;\n                const hasMatchingNodes = matchingNodes.length > 0;\n                if (hasMatchingEdges) {\n                    const edgeChanges = matchingEdges.map(elementToRemoveChange);\n                    onEdgesDelete?.(matchingEdges);\n                    triggerEdgeChanges(edgeChanges);\n                }\n                if (hasMatchingNodes) {\n                    const nodeChanges = matchingNodes.map(elementToRemoveChange);\n                    onNodesDelete?.(matchingNodes);\n                    triggerNodeChanges(nodeChanges);\n                }\n                if (hasMatchingNodes || hasMatchingEdges) {\n                    onDelete?.({ nodes: matchingNodes, edges: matchingEdges });\n                }\n                return { deletedNodes: matchingNodes, deletedEdges: matchingEdges };\n            },\n            getIntersectingNodes: (nodeOrRect, partially = true, nodes) => {\n                const isRect = isRectObject(nodeOrRect);\n                const nodeRect = isRect ? nodeOrRect : getNodeRect(nodeOrRect);\n                const hasNodesOption = nodes !== undefined;\n                if (!nodeRect) {\n                    return [];\n                }\n                return (nodes || store.getState().nodes).filter((n) => {\n                    const internalNode = store.getState().nodeLookup.get(n.id);\n                    if (internalNode && !isRect && (n.id === nodeOrRect.id || !internalNode.internals.positionAbsolute)) {\n                        return false;\n                    }\n                    const currNodeRect = nodeToRect(hasNodesOption ? n : internalNode);\n                    const overlappingArea = getOverlappingArea(currNodeRect, nodeRect);\n                    const partiallyVisible = partially && overlappingArea > 0;\n                    return (partiallyVisible ||\n                        overlappingArea >= currNodeRect.width * currNodeRect.height ||\n                        overlappingArea >= nodeRect.width * nodeRect.height);\n                });\n            },\n            isNodeIntersecting: (nodeOrRect, area, partially = true) => {\n                const isRect = isRectObject(nodeOrRect);\n                const nodeRect = isRect ? nodeOrRect : getNodeRect(nodeOrRect);\n                if (!nodeRect) {\n                    return false;\n                }\n                const overlappingArea = getOverlappingArea(nodeRect, area);\n                const partiallyVisible = partially && overlappingArea > 0;\n                return partiallyVisible || overlappingArea >= nodeRect.width * nodeRect.height;\n            },\n            updateNode,\n            updateNodeData: (id, dataUpdate, options = { replace: false }) => {\n                updateNode(id, (node) => {\n                    const nextData = typeof dataUpdate === 'function' ? dataUpdate(node) : dataUpdate;\n                    return options.replace ? { ...node, data: nextData } : { ...node, data: { ...node.data, ...nextData } };\n                }, options);\n            },\n            updateEdge,\n            updateEdgeData: (id, dataUpdate, options = { replace: false }) => {\n                updateEdge(id, (edge) => {\n                    const nextData = typeof dataUpdate === 'function' ? dataUpdate(edge) : dataUpdate;\n                    return options.replace ? { ...edge, data: nextData } : { ...edge, data: { ...edge.data, ...nextData } };\n                }, options);\n            },\n            getNodesBounds: (nodes) => {\n                const { nodeLookup, nodeOrigin } = store.getState();\n                return getNodesBounds(nodes, { nodeLookup, nodeOrigin });\n            },\n            getHandleConnections: ({ type, id, nodeId }) => Array.from(store\n                .getState()\n                .connectionLookup.get(`${nodeId}-${type}${id ? `-${id}` : ''}`)\n                ?.values() ?? []),\n            getNodeConnections: ({ type, handleId, nodeId }) => Array.from(store\n                .getState()\n                .connectionLookup.get(`${nodeId}${type ? (handleId ? `-${type}-${handleId}` : `-${type}`) : ''}`)\n                ?.values() ?? []),\n            fitView: async (options) => {\n                // We either create a new Promise or reuse the existing one\n                // Even if fitView is called multiple times in a row, we only end up with a single Promise\n                const fitViewResolver = store.getState().fitViewResolver ?? withResolvers();\n                // We schedule a fitView by setting fitViewQueued and triggering a setNodes\n                store.setState({ fitViewQueued: true, fitViewOptions: options, fitViewResolver });\n                batchContext.nodeQueue.push((nodes) => [...nodes]);\n                return fitViewResolver.promise;\n            },\n        };\n    }, []);\n    return useMemo(() => {\n        return {\n            ...generalHelper,\n            ...viewportHelper,\n            viewportInitialized,\n        };\n    }, [viewportInitialized]);\n}\n\nconst selected = (item) => item.selected;\nconst win$1 = typeof window !== 'undefined' ? window : undefined;\n/**\n * Hook for handling global key events.\n *\n * @internal\n */\nfunction useGlobalKeyHandler({ deleteKeyCode, multiSelectionKeyCode, }) {\n    const store = useStoreApi();\n    const { deleteElements } = useReactFlow();\n    const deleteKeyPressed = useKeyPress(deleteKeyCode, { actInsideInputWithModifier: false });\n    const multiSelectionKeyPressed = useKeyPress(multiSelectionKeyCode, { target: win$1 });\n    useEffect(() => {\n        if (deleteKeyPressed) {\n            const { edges, nodes } = store.getState();\n            deleteElements({ nodes: nodes.filter(selected), edges: edges.filter(selected) });\n            store.setState({ nodesSelectionActive: false });\n        }\n    }, [deleteKeyPressed]);\n    useEffect(() => {\n        store.setState({ multiSelectionActive: multiSelectionKeyPressed });\n    }, [multiSelectionKeyPressed]);\n}\n\n/**\n * Hook for handling resize events.\n *\n * @internal\n */\nfunction useResizeHandler(domNode) {\n    const store = useStoreApi();\n    useEffect(() => {\n        const updateDimensions = () => {\n            if (!domNode.current) {\n                return false;\n            }\n            const size = getDimensions(domNode.current);\n            if (size.height === 0 || size.width === 0) {\n                store.getState().onError?.('004', errorMessages['error004']());\n            }\n            store.setState({ width: size.width || 500, height: size.height || 500 });\n        };\n        if (domNode.current) {\n            updateDimensions();\n            window.addEventListener('resize', updateDimensions);\n            const resizeObserver = new ResizeObserver(() => updateDimensions());\n            resizeObserver.observe(domNode.current);\n            return () => {\n                window.removeEventListener('resize', updateDimensions);\n                if (resizeObserver && domNode.current) {\n                    resizeObserver.unobserve(domNode.current);\n                }\n            };\n        }\n    }, []);\n}\n\nconst containerStyle = {\n    position: 'absolute',\n    width: '100%',\n    height: '100%',\n    top: 0,\n    left: 0,\n};\n\nconst selector$j = (s) => ({\n    userSelectionActive: s.userSelectionActive,\n    lib: s.lib,\n});\nfunction ZoomPane({ onPaneContextMenu, zoomOnScroll = true, zoomOnPinch = true, panOnScroll = false, panOnScrollSpeed = 0.5, panOnScrollMode = PanOnScrollMode.Free, zoomOnDoubleClick = true, panOnDrag = true, defaultViewport, translateExtent, minZoom, maxZoom, zoomActivationKeyCode, preventScrolling = true, children, noWheelClassName, noPanClassName, onViewportChange, isControlledViewport, paneClickDistance, }) {\n    const store = useStoreApi();\n    const zoomPane = useRef(null);\n    const { userSelectionActive, lib } = useStore(selector$j, shallow);\n    const zoomActivationKeyPressed = useKeyPress(zoomActivationKeyCode);\n    const panZoom = useRef();\n    useResizeHandler(zoomPane);\n    const onTransformChange = useCallback((transform) => {\n        onViewportChange?.({ x: transform[0], y: transform[1], zoom: transform[2] });\n        if (!isControlledViewport) {\n            store.setState({ transform });\n        }\n    }, [onViewportChange, isControlledViewport]);\n    useEffect(() => {\n        if (zoomPane.current) {\n            panZoom.current = XYPanZoom({\n                domNode: zoomPane.current,\n                minZoom,\n                maxZoom,\n                translateExtent,\n                viewport: defaultViewport,\n                paneClickDistance,\n                onDraggingChange: (paneDragging) => store.setState({ paneDragging }),\n                onPanZoomStart: (event, vp) => {\n                    const { onViewportChangeStart, onMoveStart } = store.getState();\n                    onMoveStart?.(event, vp);\n                    onViewportChangeStart?.(vp);\n                },\n                onPanZoom: (event, vp) => {\n                    const { onViewportChange, onMove } = store.getState();\n                    onMove?.(event, vp);\n                    onViewportChange?.(vp);\n                },\n                onPanZoomEnd: (event, vp) => {\n                    const { onViewportChangeEnd, onMoveEnd } = store.getState();\n                    onMoveEnd?.(event, vp);\n                    onViewportChangeEnd?.(vp);\n                },\n            });\n            const { x, y, zoom } = panZoom.current.getViewport();\n            store.setState({\n                panZoom: panZoom.current,\n                transform: [x, y, zoom],\n                domNode: zoomPane.current.closest('.react-flow'),\n            });\n            return () => {\n                panZoom.current?.destroy();\n            };\n        }\n    }, []);\n    useEffect(() => {\n        panZoom.current?.update({\n            onPaneContextMenu,\n            zoomOnScroll,\n            zoomOnPinch,\n            panOnScroll,\n            panOnScrollSpeed,\n            panOnScrollMode,\n            zoomOnDoubleClick,\n            panOnDrag,\n            zoomActivationKeyPressed,\n            preventScrolling,\n            noPanClassName,\n            userSelectionActive,\n            noWheelClassName,\n            lib,\n            onTransformChange,\n        });\n    }, [\n        onPaneContextMenu,\n        zoomOnScroll,\n        zoomOnPinch,\n        panOnScroll,\n        panOnScrollSpeed,\n        panOnScrollMode,\n        zoomOnDoubleClick,\n        panOnDrag,\n        zoomActivationKeyPressed,\n        preventScrolling,\n        noPanClassName,\n        userSelectionActive,\n        noWheelClassName,\n        lib,\n        onTransformChange,\n    ]);\n    return (jsx(\"div\", { className: \"react-flow__renderer\", ref: zoomPane, style: containerStyle, children: children }));\n}\n\nconst selector$i = (s) => ({\n    userSelectionActive: s.userSelectionActive,\n    userSelectionRect: s.userSelectionRect,\n});\nfunction UserSelection() {\n    const { userSelectionActive, userSelectionRect } = useStore(selector$i, shallow);\n    const isActive = userSelectionActive && userSelectionRect;\n    if (!isActive) {\n        return null;\n    }\n    return (jsx(\"div\", { className: \"react-flow__selection react-flow__container\", style: {\n            width: userSelectionRect.width,\n            height: userSelectionRect.height,\n            transform: `translate(${userSelectionRect.x}px, ${userSelectionRect.y}px)`,\n        } }));\n}\n\nconst wrapHandler = (handler, containerRef) => {\n    return (event) => {\n        if (event.target !== containerRef.current) {\n            return;\n        }\n        handler?.(event);\n    };\n};\nconst selector$h = (s) => ({\n    userSelectionActive: s.userSelectionActive,\n    elementsSelectable: s.elementsSelectable,\n    connectionInProgress: s.connection.inProgress,\n    dragging: s.paneDragging,\n});\nfunction Pane({ isSelecting, selectionKeyPressed, selectionMode = SelectionMode.Full, panOnDrag, selectionOnDrag, onSelectionStart, onSelectionEnd, onPaneClick, onPaneContextMenu, onPaneScroll, onPaneMouseEnter, onPaneMouseMove, onPaneMouseLeave, children, }) {\n    const store = useStoreApi();\n    const { userSelectionActive, elementsSelectable, dragging, connectionInProgress } = useStore(selector$h, shallow);\n    const hasActiveSelection = elementsSelectable && (isSelecting || userSelectionActive);\n    const container = useRef(null);\n    const containerBounds = useRef();\n    const selectedNodeIds = useRef(new Set());\n    const selectedEdgeIds = useRef(new Set());\n    // Used to prevent click events when the user lets go of the selectionKey during a selection\n    const selectionInProgress = useRef(false);\n    const selectionStarted = useRef(false);\n    const onClick = (event) => {\n        // We prevent click events when the user let go of the selectionKey during a selection\n        // We also prevent click events when a connection is in progress\n        if (selectionInProgress.current || connectionInProgress) {\n            selectionInProgress.current = false;\n            return;\n        }\n        onPaneClick?.(event);\n        store.getState().resetSelectedElements();\n        store.setState({ nodesSelectionActive: false });\n    };\n    const onContextMenu = (event) => {\n        if (Array.isArray(panOnDrag) && panOnDrag?.includes(2)) {\n            event.preventDefault();\n            return;\n        }\n        onPaneContextMenu?.(event);\n    };\n    const onWheel = onPaneScroll ? (event) => onPaneScroll(event) : undefined;\n    const onPointerDown = (event) => {\n        const { resetSelectedElements, domNode } = store.getState();\n        containerBounds.current = domNode?.getBoundingClientRect();\n        if (!elementsSelectable ||\n            !isSelecting ||\n            event.button !== 0 ||\n            event.target !== container.current ||\n            !containerBounds.current) {\n            return;\n        }\n        event.target?.setPointerCapture?.(event.pointerId);\n        selectionStarted.current = true;\n        selectionInProgress.current = false;\n        const { x, y } = getEventPosition(event.nativeEvent, containerBounds.current);\n        resetSelectedElements();\n        store.setState({\n            userSelectionRect: {\n                width: 0,\n                height: 0,\n                startX: x,\n                startY: y,\n                x,\n                y,\n            },\n        });\n        onSelectionStart?.(event);\n    };\n    const onPointerMove = (event) => {\n        const { userSelectionRect, transform, nodeLookup, edgeLookup, connectionLookup, triggerNodeChanges, triggerEdgeChanges, defaultEdgeOptions, } = store.getState();\n        if (!containerBounds.current || !userSelectionRect) {\n            return;\n        }\n        selectionInProgress.current = true;\n        const { x: mouseX, y: mouseY } = getEventPosition(event.nativeEvent, containerBounds.current);\n        const { startX, startY } = userSelectionRect;\n        const nextUserSelectRect = {\n            startX,\n            startY,\n            x: mouseX < startX ? mouseX : startX,\n            y: mouseY < startY ? mouseY : startY,\n            width: Math.abs(mouseX - startX),\n            height: Math.abs(mouseY - startY),\n        };\n        const prevSelectedNodeIds = selectedNodeIds.current;\n        const prevSelectedEdgeIds = selectedEdgeIds.current;\n        selectedNodeIds.current = new Set(getNodesInside(nodeLookup, nextUserSelectRect, transform, selectionMode === SelectionMode.Partial, true).map((node) => node.id));\n        selectedEdgeIds.current = new Set();\n        const edgesSelectable = defaultEdgeOptions?.selectable ?? true;\n        // We look for all edges connected to the selected nodes\n        for (const nodeId of selectedNodeIds.current) {\n            const connections = connectionLookup.get(nodeId);\n            if (!connections)\n                continue;\n            for (const { edgeId } of connections.values()) {\n                const edge = edgeLookup.get(edgeId);\n                if (edge && (edge.selectable ?? edgesSelectable)) {\n                    selectedEdgeIds.current.add(edgeId);\n                }\n            }\n        }\n        if (!areSetsEqual(prevSelectedNodeIds, selectedNodeIds.current)) {\n            const changes = getSelectionChanges(nodeLookup, selectedNodeIds.current, true);\n            triggerNodeChanges(changes);\n        }\n        if (!areSetsEqual(prevSelectedEdgeIds, selectedEdgeIds.current)) {\n            const changes = getSelectionChanges(edgeLookup, selectedEdgeIds.current);\n            triggerEdgeChanges(changes);\n        }\n        store.setState({\n            userSelectionRect: nextUserSelectRect,\n            userSelectionActive: true,\n            nodesSelectionActive: false,\n        });\n    };\n    const onPointerUp = (event) => {\n        if (event.button !== 0 || !selectionStarted.current) {\n            return;\n        }\n        event.target?.releasePointerCapture?.(event.pointerId);\n        const { userSelectionRect } = store.getState();\n        /*\n         * We only want to trigger click functions when in selection mode if\n         * the user did not move the mouse.\n         */\n        if (!userSelectionActive && userSelectionRect && event.target === container.current) {\n            onClick?.(event);\n        }\n        store.setState({\n            userSelectionActive: false,\n            userSelectionRect: null,\n            nodesSelectionActive: selectedNodeIds.current.size > 0,\n        });\n        onSelectionEnd?.(event);\n        /*\n         * If the user kept holding the selectionKey during the selection,\n         * we need to reset the selectionInProgress, so the next click event is not prevented\n         */\n        if (selectionKeyPressed || selectionOnDrag) {\n            selectionInProgress.current = false;\n        }\n        selectionStarted.current = false;\n    };\n    const draggable = panOnDrag === true || (Array.isArray(panOnDrag) && panOnDrag.includes(0));\n    return (jsxs(\"div\", { className: cc(['react-flow__pane', { draggable, dragging, selection: isSelecting }]), onClick: hasActiveSelection ? undefined : wrapHandler(onClick, container), onContextMenu: wrapHandler(onContextMenu, container), onWheel: wrapHandler(onWheel, container), onPointerEnter: hasActiveSelection ? undefined : onPaneMouseEnter, onPointerDown: hasActiveSelection ? onPointerDown : onPaneMouseMove, onPointerMove: hasActiveSelection ? onPointerMove : onPaneMouseMove, onPointerUp: hasActiveSelection ? onPointerUp : undefined, onPointerLeave: onPaneMouseLeave, ref: container, style: containerStyle, children: [children, jsx(UserSelection, {})] }));\n}\n\n/*\n * this handler is called by\n * 1. the click handler when node is not draggable or selectNodesOnDrag = false\n * or\n * 2. the on drag start handler when node is draggable and selectNodesOnDrag = true\n */\nfunction handleNodeClick({ id, store, unselect = false, nodeRef, }) {\n    const { addSelectedNodes, unselectNodesAndEdges, multiSelectionActive, nodeLookup, onError } = store.getState();\n    const node = nodeLookup.get(id);\n    if (!node) {\n        onError?.('012', errorMessages['error012'](id));\n        return;\n    }\n    store.setState({ nodesSelectionActive: false });\n    if (!node.selected) {\n        addSelectedNodes([id]);\n    }\n    else if (unselect || (node.selected && multiSelectionActive)) {\n        unselectNodesAndEdges({ nodes: [node], edges: [] });\n        requestAnimationFrame(() => nodeRef?.current?.blur());\n    }\n}\n\n/**\n * Hook for calling XYDrag helper from @xyflow/system.\n *\n * @internal\n */\nfunction useDrag({ nodeRef, disabled = false, noDragClassName, handleSelector, nodeId, isSelectable, nodeClickDistance, }) {\n    const store = useStoreApi();\n    const [dragging, setDragging] = useState(false);\n    const xyDrag = useRef();\n    useEffect(() => {\n        xyDrag.current = XYDrag({\n            getStoreItems: () => store.getState(),\n            onNodeMouseDown: (id) => {\n                handleNodeClick({\n                    id,\n                    store,\n                    nodeRef,\n                });\n            },\n            onDragStart: () => {\n                setDragging(true);\n            },\n            onDragStop: () => {\n                setDragging(false);\n            },\n        });\n    }, []);\n    useEffect(() => {\n        if (disabled) {\n            xyDrag.current?.destroy();\n        }\n        else if (nodeRef.current) {\n            xyDrag.current?.update({\n                noDragClassName,\n                handleSelector,\n                domNode: nodeRef.current,\n                isSelectable,\n                nodeId,\n                nodeClickDistance,\n            });\n            return () => {\n                xyDrag.current?.destroy();\n            };\n        }\n    }, [noDragClassName, handleSelector, disabled, isSelectable, nodeRef, nodeId]);\n    return dragging;\n}\n\nconst selectedAndDraggable = (nodesDraggable) => (n) => n.selected && (n.draggable || (nodesDraggable && typeof n.draggable === 'undefined'));\n/**\n * Hook for updating node positions by passing a direction and factor\n *\n * @internal\n * @returns function for updating node positions\n */\nfunction useMoveSelectedNodes() {\n    const store = useStoreApi();\n    const moveSelectedNodes = useCallback((params) => {\n        const { nodeExtent, snapToGrid, snapGrid, nodesDraggable, onError, updateNodePositions, nodeLookup, nodeOrigin } = store.getState();\n        const nodeUpdates = new Map();\n        const isSelected = selectedAndDraggable(nodesDraggable);\n        /*\n         * by default a node moves 5px on each key press\n         * if snap grid is enabled, we use that for the velocity\n         */\n        const xVelo = snapToGrid ? snapGrid[0] : 5;\n        const yVelo = snapToGrid ? snapGrid[1] : 5;\n        const xDiff = params.direction.x * xVelo * params.factor;\n        const yDiff = params.direction.y * yVelo * params.factor;\n        for (const [, node] of nodeLookup) {\n            if (!isSelected(node)) {\n                continue;\n            }\n            let nextPosition = {\n                x: node.internals.positionAbsolute.x + xDiff,\n                y: node.internals.positionAbsolute.y + yDiff,\n            };\n            if (snapToGrid) {\n                nextPosition = snapPosition(nextPosition, snapGrid);\n            }\n            const { position, positionAbsolute } = calculateNodePosition({\n                nodeId: node.id,\n                nextPosition,\n                nodeLookup,\n                nodeExtent,\n                nodeOrigin,\n                onError,\n            });\n            node.position = position;\n            node.internals.positionAbsolute = positionAbsolute;\n            nodeUpdates.set(node.id, node);\n        }\n        updateNodePositions(nodeUpdates);\n    }, []);\n    return moveSelectedNodes;\n}\n\nconst NodeIdContext = createContext(null);\nconst Provider = NodeIdContext.Provider;\nNodeIdContext.Consumer;\n/**\n * You can use this hook to get the id of the node it is used inside. It is useful\n * if you need the node's id deeper in the render tree but don't want to manually\n * drill down the id as a prop.\n *\n * @public\n * @returns The id for a node in the flow.\n *\n * @example\n *```jsx\n *import { useNodeId } from '@xyflow/react';\n *\n *export default function CustomNode() {\n *  return (\n *    <div>\n *      <span>This node has an id of </span>\n *      <NodeIdDisplay />\n *    </div>\n *  );\n *}\n *\n *function NodeIdDisplay() {\n *  const nodeId = useNodeId();\n *\n *  return <span>{nodeId}</span>;\n *}\n *```\n */\nconst useNodeId = () => {\n    const nodeId = useContext(NodeIdContext);\n    return nodeId;\n};\n\nconst selector$g = (s) => ({\n    connectOnClick: s.connectOnClick,\n    noPanClassName: s.noPanClassName,\n    rfId: s.rfId,\n});\nconst connectingSelector = (nodeId, handleId, type) => (state) => {\n    const { connectionClickStartHandle: clickHandle, connectionMode, connection } = state;\n    const { fromHandle, toHandle, isValid } = connection;\n    const connectingTo = toHandle?.nodeId === nodeId && toHandle?.id === handleId && toHandle?.type === type;\n    return {\n        connectingFrom: fromHandle?.nodeId === nodeId && fromHandle?.id === handleId && fromHandle?.type === type,\n        connectingTo,\n        clickConnecting: clickHandle?.nodeId === nodeId && clickHandle?.id === handleId && clickHandle?.type === type,\n        isPossibleEndHandle: connectionMode === ConnectionMode.Strict\n            ? fromHandle?.type !== type\n            : nodeId !== fromHandle?.nodeId || handleId !== fromHandle?.id,\n        connectionInProcess: !!fromHandle,\n        clickConnectionInProcess: !!clickHandle,\n        valid: connectingTo && isValid,\n    };\n};\nfunction HandleComponent({ type = 'source', position = Position.Top, isValidConnection, isConnectable = true, isConnectableStart = true, isConnectableEnd = true, id, onConnect, children, className, onMouseDown, onTouchStart, ...rest }, ref) {\n    const handleId = id || null;\n    const isTarget = type === 'target';\n    const store = useStoreApi();\n    const nodeId = useNodeId();\n    const { connectOnClick, noPanClassName, rfId } = useStore(selector$g, shallow);\n    const { connectingFrom, connectingTo, clickConnecting, isPossibleEndHandle, connectionInProcess, clickConnectionInProcess, valid, } = useStore(connectingSelector(nodeId, handleId, type), shallow);\n    if (!nodeId) {\n        store.getState().onError?.('010', errorMessages['error010']());\n    }\n    const onConnectExtended = (params) => {\n        const { defaultEdgeOptions, onConnect: onConnectAction, hasDefaultEdges } = store.getState();\n        const edgeParams = {\n            ...defaultEdgeOptions,\n            ...params,\n        };\n        if (hasDefaultEdges) {\n            const { edges, setEdges } = store.getState();\n            setEdges(addEdge(edgeParams, edges));\n        }\n        onConnectAction?.(edgeParams);\n        onConnect?.(edgeParams);\n    };\n    const onPointerDown = (event) => {\n        if (!nodeId) {\n            return;\n        }\n        const isMouseTriggered = isMouseEvent(event.nativeEvent);\n        if (isConnectableStart &&\n            ((isMouseTriggered && event.button === 0) || !isMouseTriggered)) {\n            const currentStore = store.getState();\n            XYHandle.onPointerDown(event.nativeEvent, {\n                autoPanOnConnect: currentStore.autoPanOnConnect,\n                connectionMode: currentStore.connectionMode,\n                connectionRadius: currentStore.connectionRadius,\n                domNode: currentStore.domNode,\n                nodeLookup: currentStore.nodeLookup,\n                lib: currentStore.lib,\n                isTarget,\n                handleId,\n                nodeId,\n                flowId: currentStore.rfId,\n                panBy: currentStore.panBy,\n                cancelConnection: currentStore.cancelConnection,\n                onConnectStart: currentStore.onConnectStart,\n                onConnectEnd: currentStore.onConnectEnd,\n                updateConnection: currentStore.updateConnection,\n                onConnect: onConnectExtended,\n                isValidConnection: isValidConnection || currentStore.isValidConnection,\n                getTransform: () => store.getState().transform,\n                getFromHandle: () => store.getState().connection.fromHandle,\n                autoPanSpeed: currentStore.autoPanSpeed,\n                dragThreshold: currentStore.connectionDragThreshold,\n            });\n        }\n        if (isMouseTriggered) {\n            onMouseDown?.(event);\n        }\n        else {\n            onTouchStart?.(event);\n        }\n    };\n    const onClick = (event) => {\n        const { onClickConnectStart, onClickConnectEnd, connectionClickStartHandle, connectionMode, isValidConnection: isValidConnectionStore, lib, rfId: flowId, nodeLookup, connection: connectionState, } = store.getState();\n        if (!nodeId || (!connectionClickStartHandle && !isConnectableStart)) {\n            return;\n        }\n        if (!connectionClickStartHandle) {\n            onClickConnectStart?.(event.nativeEvent, { nodeId, handleId, handleType: type });\n            store.setState({ connectionClickStartHandle: { nodeId, type, id: handleId } });\n            return;\n        }\n        const doc = getHostForElement(event.target);\n        const isValidConnectionHandler = isValidConnection || isValidConnectionStore;\n        const { connection, isValid } = XYHandle.isValid(event.nativeEvent, {\n            handle: {\n                nodeId,\n                id: handleId,\n                type,\n            },\n            connectionMode,\n            fromNodeId: connectionClickStartHandle.nodeId,\n            fromHandleId: connectionClickStartHandle.id || null,\n            fromType: connectionClickStartHandle.type,\n            isValidConnection: isValidConnectionHandler,\n            flowId,\n            doc,\n            lib,\n            nodeLookup,\n        });\n        if (isValid && connection) {\n            onConnectExtended(connection);\n        }\n        const connectionClone = structuredClone(connectionState);\n        delete connectionClone.inProgress;\n        connectionClone.toPosition = connectionClone.toHandle ? connectionClone.toHandle.position : null;\n        onClickConnectEnd?.(event, connectionClone);\n        store.setState({ connectionClickStartHandle: null });\n    };\n    return (jsx(\"div\", { \"data-handleid\": handleId, \"data-nodeid\": nodeId, \"data-handlepos\": position, \"data-id\": `${rfId}-${nodeId}-${handleId}-${type}`, className: cc([\n            'react-flow__handle',\n            `react-flow__handle-${position}`,\n            'nodrag',\n            noPanClassName,\n            className,\n            {\n                source: !isTarget,\n                target: isTarget,\n                connectable: isConnectable,\n                connectablestart: isConnectableStart,\n                connectableend: isConnectableEnd,\n                clickconnecting: clickConnecting,\n                connectingfrom: connectingFrom,\n                connectingto: connectingTo,\n                valid,\n                /*\n                 * shows where you can start a connection from\n                 * and where you can end it while connecting\n                 */\n                connectionindicator: isConnectable &&\n                    (!connectionInProcess || isPossibleEndHandle) &&\n                    (connectionInProcess || clickConnectionInProcess ? isConnectableEnd : isConnectableStart),\n            },\n        ]), onMouseDown: onPointerDown, onTouchStart: onPointerDown, onClick: connectOnClick ? onClick : undefined, ref: ref, ...rest, children: children }));\n}\n/**\n * The `<Handle />` component is used in your [custom nodes](/learn/customization/custom-nodes)\n * to define connection points.\n *\n *@public\n *\n *@example\n *\n *```jsx\n *import { Handle, Position } from '@xyflow/react';\n *\n *export function CustomNode({ data }) {\n *  return (\n *    <>\n *      <div style={{ padding: '10px 20px' }}>\n *        {data.label}\n *      </div>\n *\n *      <Handle type=\"target\" position={Position.Left} />\n *      <Handle type=\"source\" position={Position.Right} />\n *    </>\n *  );\n *};\n *```\n */\nconst Handle = memo(fixedForwardRef(HandleComponent));\n\nfunction InputNode({ data, isConnectable, sourcePosition = Position.Bottom }) {\n    return (jsxs(Fragment, { children: [data?.label, jsx(Handle, { type: \"source\", position: sourcePosition, isConnectable: isConnectable })] }));\n}\n\nfunction DefaultNode({ data, isConnectable, targetPosition = Position.Top, sourcePosition = Position.Bottom, }) {\n    return (jsxs(Fragment, { children: [jsx(Handle, { type: \"target\", position: targetPosition, isConnectable: isConnectable }), data?.label, jsx(Handle, { type: \"source\", position: sourcePosition, isConnectable: isConnectable })] }));\n}\n\nfunction GroupNode() {\n    return null;\n}\n\nfunction OutputNode({ data, isConnectable, targetPosition = Position.Top }) {\n    return (jsxs(Fragment, { children: [jsx(Handle, { type: \"target\", position: targetPosition, isConnectable: isConnectable }), data?.label] }));\n}\n\nconst arrowKeyDiffs = {\n    ArrowUp: { x: 0, y: -1 },\n    ArrowDown: { x: 0, y: 1 },\n    ArrowLeft: { x: -1, y: 0 },\n    ArrowRight: { x: 1, y: 0 },\n};\nconst builtinNodeTypes = {\n    input: InputNode,\n    default: DefaultNode,\n    output: OutputNode,\n    group: GroupNode,\n};\nfunction getNodeInlineStyleDimensions(node) {\n    if (node.internals.handleBounds === undefined) {\n        return {\n            width: node.width ?? node.initialWidth ?? node.style?.width,\n            height: node.height ?? node.initialHeight ?? node.style?.height,\n        };\n    }\n    return {\n        width: node.width ?? node.style?.width,\n        height: node.height ?? node.style?.height,\n    };\n}\n\nconst selector$f = (s) => {\n    const { width, height, x, y } = getInternalNodesBounds(s.nodeLookup, {\n        filter: (node) => !!node.selected,\n    });\n    return {\n        width: isNumeric(width) ? width : null,\n        height: isNumeric(height) ? height : null,\n        userSelectionActive: s.userSelectionActive,\n        transformString: `translate(${s.transform[0]}px,${s.transform[1]}px) scale(${s.transform[2]}) translate(${x}px,${y}px)`,\n    };\n};\nfunction NodesSelection({ onSelectionContextMenu, noPanClassName, disableKeyboardA11y, }) {\n    const store = useStoreApi();\n    const { width, height, transformString, userSelectionActive } = useStore(selector$f, shallow);\n    const moveSelectedNodes = useMoveSelectedNodes();\n    const nodeRef = useRef(null);\n    useEffect(() => {\n        if (!disableKeyboardA11y) {\n            nodeRef.current?.focus({\n                preventScroll: true,\n            });\n        }\n    }, [disableKeyboardA11y]);\n    useDrag({\n        nodeRef,\n    });\n    if (userSelectionActive || !width || !height) {\n        return null;\n    }\n    const onContextMenu = onSelectionContextMenu\n        ? (event) => {\n            const selectedNodes = store.getState().nodes.filter((n) => n.selected);\n            onSelectionContextMenu(event, selectedNodes);\n        }\n        : undefined;\n    const onKeyDown = (event) => {\n        if (Object.prototype.hasOwnProperty.call(arrowKeyDiffs, event.key)) {\n            event.preventDefault();\n            moveSelectedNodes({\n                direction: arrowKeyDiffs[event.key],\n                factor: event.shiftKey ? 4 : 1,\n            });\n        }\n    };\n    return (jsx(\"div\", { className: cc(['react-flow__nodesselection', 'react-flow__container', noPanClassName]), style: {\n            transform: transformString,\n        }, children: jsx(\"div\", { ref: nodeRef, className: \"react-flow__nodesselection-rect\", onContextMenu: onContextMenu, tabIndex: disableKeyboardA11y ? undefined : -1, onKeyDown: disableKeyboardA11y ? undefined : onKeyDown, style: {\n                width,\n                height,\n            } }) }));\n}\n\nconst win = typeof window !== 'undefined' ? window : undefined;\nconst selector$e = (s) => {\n    return { nodesSelectionActive: s.nodesSelectionActive, userSelectionActive: s.userSelectionActive };\n};\nfunction FlowRendererComponent({ children, onPaneClick, onPaneMouseEnter, onPaneMouseMove, onPaneMouseLeave, onPaneContextMenu, onPaneScroll, paneClickDistance, deleteKeyCode, selectionKeyCode, selectionOnDrag, selectionMode, onSelectionStart, onSelectionEnd, multiSelectionKeyCode, panActivationKeyCode, zoomActivationKeyCode, elementsSelectable, zoomOnScroll, zoomOnPinch, panOnScroll: _panOnScroll, panOnScrollSpeed, panOnScrollMode, zoomOnDoubleClick, panOnDrag: _panOnDrag, defaultViewport, translateExtent, minZoom, maxZoom, preventScrolling, onSelectionContextMenu, noWheelClassName, noPanClassName, disableKeyboardA11y, onViewportChange, isControlledViewport, }) {\n    const { nodesSelectionActive, userSelectionActive } = useStore(selector$e);\n    const selectionKeyPressed = useKeyPress(selectionKeyCode, { target: win });\n    const panActivationKeyPressed = useKeyPress(panActivationKeyCode, { target: win });\n    const panOnDrag = panActivationKeyPressed || _panOnDrag;\n    const panOnScroll = panActivationKeyPressed || _panOnScroll;\n    const _selectionOnDrag = selectionOnDrag && panOnDrag !== true;\n    const isSelecting = selectionKeyPressed || userSelectionActive || _selectionOnDrag;\n    useGlobalKeyHandler({ deleteKeyCode, multiSelectionKeyCode });\n    return (jsx(ZoomPane, { onPaneContextMenu: onPaneContextMenu, elementsSelectable: elementsSelectable, zoomOnScroll: zoomOnScroll, zoomOnPinch: zoomOnPinch, panOnScroll: panOnScroll, panOnScrollSpeed: panOnScrollSpeed, panOnScrollMode: panOnScrollMode, zoomOnDoubleClick: zoomOnDoubleClick, panOnDrag: !selectionKeyPressed && panOnDrag, defaultViewport: defaultViewport, translateExtent: translateExtent, minZoom: minZoom, maxZoom: maxZoom, zoomActivationKeyCode: zoomActivationKeyCode, preventScrolling: preventScrolling, noWheelClassName: noWheelClassName, noPanClassName: noPanClassName, onViewportChange: onViewportChange, isControlledViewport: isControlledViewport, paneClickDistance: paneClickDistance, children: jsxs(Pane, { onSelectionStart: onSelectionStart, onSelectionEnd: onSelectionEnd, onPaneClick: onPaneClick, onPaneMouseEnter: onPaneMouseEnter, onPaneMouseMove: onPaneMouseMove, onPaneMouseLeave: onPaneMouseLeave, onPaneContextMenu: onPaneContextMenu, onPaneScroll: onPaneScroll, panOnDrag: panOnDrag, isSelecting: !!isSelecting, selectionMode: selectionMode, selectionKeyPressed: selectionKeyPressed, selectionOnDrag: _selectionOnDrag, children: [children, nodesSelectionActive && (jsx(NodesSelection, { onSelectionContextMenu: onSelectionContextMenu, noPanClassName: noPanClassName, disableKeyboardA11y: disableKeyboardA11y }))] }) }));\n}\nFlowRendererComponent.displayName = 'FlowRenderer';\nconst FlowRenderer = memo(FlowRendererComponent);\n\nconst selector$d = (onlyRenderVisible) => (s) => {\n    return onlyRenderVisible\n        ? getNodesInside(s.nodeLookup, { x: 0, y: 0, width: s.width, height: s.height }, s.transform, true).map((node) => node.id)\n        : Array.from(s.nodeLookup.keys());\n};\n/**\n * Hook for getting the visible node ids from the store.\n *\n * @internal\n * @param onlyRenderVisible\n * @returns array with visible node ids\n */\nfunction useVisibleNodeIds(onlyRenderVisible) {\n    const nodeIds = useStore(useCallback(selector$d(onlyRenderVisible), [onlyRenderVisible]), shallow);\n    return nodeIds;\n}\n\nconst selector$c = (s) => s.updateNodeInternals;\nfunction useResizeObserver() {\n    const updateNodeInternals = useStore(selector$c);\n    const [resizeObserver] = useState(() => {\n        if (typeof ResizeObserver === 'undefined') {\n            return null;\n        }\n        return new ResizeObserver((entries) => {\n            const updates = new Map();\n            entries.forEach((entry) => {\n                const id = entry.target.getAttribute('data-id');\n                updates.set(id, {\n                    id,\n                    nodeElement: entry.target,\n                    force: true,\n                });\n            });\n            updateNodeInternals(updates);\n        });\n    });\n    useEffect(() => {\n        return () => {\n            resizeObserver?.disconnect();\n        };\n    }, [resizeObserver]);\n    return resizeObserver;\n}\n\n/**\n * Hook to handle the resize observation + internal updates for the passed node.\n *\n * @internal\n * @returns nodeRef - reference to the node element\n */\nfunction useNodeObserver({ node, nodeType, hasDimensions, resizeObserver, }) {\n    const store = useStoreApi();\n    const nodeRef = useRef(null);\n    const observedNode = useRef(null);\n    const prevSourcePosition = useRef(node.sourcePosition);\n    const prevTargetPosition = useRef(node.targetPosition);\n    const prevType = useRef(nodeType);\n    const isInitialized = hasDimensions && !!node.internals.handleBounds;\n    useEffect(() => {\n        if (nodeRef.current && !node.hidden && (!isInitialized || observedNode.current !== nodeRef.current)) {\n            if (observedNode.current) {\n                resizeObserver?.unobserve(observedNode.current);\n            }\n            resizeObserver?.observe(nodeRef.current);\n            observedNode.current = nodeRef.current;\n        }\n    }, [isInitialized, node.hidden]);\n    useEffect(() => {\n        return () => {\n            if (observedNode.current) {\n                resizeObserver?.unobserve(observedNode.current);\n                observedNode.current = null;\n            }\n        };\n    }, []);\n    useEffect(() => {\n        if (nodeRef.current) {\n            /*\n             * when the user programmatically changes the source or handle position, we need to update the internals\n             * to make sure the edges are updated correctly\n             */\n            const typeChanged = prevType.current !== nodeType;\n            const sourcePosChanged = prevSourcePosition.current !== node.sourcePosition;\n            const targetPosChanged = prevTargetPosition.current !== node.targetPosition;\n            if (typeChanged || sourcePosChanged || targetPosChanged) {\n                prevType.current = nodeType;\n                prevSourcePosition.current = node.sourcePosition;\n                prevTargetPosition.current = node.targetPosition;\n                store\n                    .getState()\n                    .updateNodeInternals(new Map([[node.id, { id: node.id, nodeElement: nodeRef.current, force: true }]]));\n            }\n        }\n    }, [node.id, nodeType, node.sourcePosition, node.targetPosition]);\n    return nodeRef;\n}\n\nfunction NodeWrapper({ id, onClick, onMouseEnter, onMouseMove, onMouseLeave, onContextMenu, onDoubleClick, nodesDraggable, elementsSelectable, nodesConnectable, nodesFocusable, resizeObserver, noDragClassName, noPanClassName, disableKeyboardA11y, rfId, nodeTypes, nodeClickDistance, onError, }) {\n    const { node, internals, isParent } = useStore((s) => {\n        const node = s.nodeLookup.get(id);\n        const isParent = s.parentLookup.has(id);\n        return {\n            node,\n            internals: node.internals,\n            isParent,\n        };\n    }, shallow);\n    let nodeType = node.type || 'default';\n    let NodeComponent = nodeTypes?.[nodeType] || builtinNodeTypes[nodeType];\n    if (NodeComponent === undefined) {\n        onError?.('003', errorMessages['error003'](nodeType));\n        nodeType = 'default';\n        NodeComponent = nodeTypes?.['default'] || builtinNodeTypes.default;\n    }\n    const isDraggable = !!(node.draggable || (nodesDraggable && typeof node.draggable === 'undefined'));\n    const isSelectable = !!(node.selectable || (elementsSelectable && typeof node.selectable === 'undefined'));\n    const isConnectable = !!(node.connectable || (nodesConnectable && typeof node.connectable === 'undefined'));\n    const isFocusable = !!(node.focusable || (nodesFocusable && typeof node.focusable === 'undefined'));\n    const store = useStoreApi();\n    const hasDimensions = nodeHasDimensions(node);\n    const nodeRef = useNodeObserver({ node, nodeType, hasDimensions, resizeObserver });\n    const dragging = useDrag({\n        nodeRef,\n        disabled: node.hidden || !isDraggable,\n        noDragClassName,\n        handleSelector: node.dragHandle,\n        nodeId: id,\n        isSelectable,\n        nodeClickDistance,\n    });\n    const moveSelectedNodes = useMoveSelectedNodes();\n    if (node.hidden) {\n        return null;\n    }\n    const nodeDimensions = getNodeDimensions(node);\n    const inlineDimensions = getNodeInlineStyleDimensions(node);\n    const hasPointerEvents = isSelectable || isDraggable || onClick || onMouseEnter || onMouseMove || onMouseLeave;\n    const onMouseEnterHandler = onMouseEnter\n        ? (event) => onMouseEnter(event, { ...internals.userNode })\n        : undefined;\n    const onMouseMoveHandler = onMouseMove\n        ? (event) => onMouseMove(event, { ...internals.userNode })\n        : undefined;\n    const onMouseLeaveHandler = onMouseLeave\n        ? (event) => onMouseLeave(event, { ...internals.userNode })\n        : undefined;\n    const onContextMenuHandler = onContextMenu\n        ? (event) => onContextMenu(event, { ...internals.userNode })\n        : undefined;\n    const onDoubleClickHandler = onDoubleClick\n        ? (event) => onDoubleClick(event, { ...internals.userNode })\n        : undefined;\n    const onSelectNodeHandler = (event) => {\n        const { selectNodesOnDrag, nodeDragThreshold } = store.getState();\n        if (isSelectable && (!selectNodesOnDrag || !isDraggable || nodeDragThreshold > 0)) {\n            /*\n             * this handler gets called by XYDrag on drag start when selectNodesOnDrag=true\n             * here we only need to call it when selectNodesOnDrag=false\n             */\n            handleNodeClick({\n                id,\n                store,\n                nodeRef,\n            });\n        }\n        if (onClick) {\n            onClick(event, { ...internals.userNode });\n        }\n    };\n    const onKeyDown = (event) => {\n        if (isInputDOMNode(event.nativeEvent) || disableKeyboardA11y) {\n            return;\n        }\n        if (elementSelectionKeys.includes(event.key) && isSelectable) {\n            const unselect = event.key === 'Escape';\n            handleNodeClick({\n                id,\n                store,\n                unselect,\n                nodeRef,\n            });\n        }\n        else if (isDraggable && node.selected && Object.prototype.hasOwnProperty.call(arrowKeyDiffs, event.key)) {\n            // prevent default scrolling behavior on arrow key press when node is moved\n            event.preventDefault();\n            const { ariaLabelConfig } = store.getState();\n            store.setState({\n                ariaLiveMessage: ariaLabelConfig['node.a11yDescription.ariaLiveMessage']({\n                    direction: event.key.replace('Arrow', '').toLowerCase(),\n                    x: ~~internals.positionAbsolute.x,\n                    y: ~~internals.positionAbsolute.y,\n                }),\n            });\n            moveSelectedNodes({\n                direction: arrowKeyDiffs[event.key],\n                factor: event.shiftKey ? 4 : 1,\n            });\n        }\n    };\n    const onFocus = () => {\n        if (disableKeyboardA11y || !nodeRef.current?.matches(':focus-visible')) {\n            return;\n        }\n        const { transform, width, height, autoPanOnNodeFocus, setCenter } = store.getState();\n        if (!autoPanOnNodeFocus) {\n            return;\n        }\n        const withinViewport = getNodesInside(new Map([[id, node]]), { x: 0, y: 0, width, height }, transform, true).length > 0;\n        if (!withinViewport) {\n            setCenter(node.position.x + nodeDimensions.width / 2, node.position.y + nodeDimensions.height / 2, {\n                zoom: transform[2],\n            });\n        }\n    };\n    return (jsx(\"div\", { className: cc([\n            'react-flow__node',\n            `react-flow__node-${nodeType}`,\n            {\n                // this is overwritable by passing `nopan` as a class name\n                [noPanClassName]: isDraggable,\n            },\n            node.className,\n            {\n                selected: node.selected,\n                selectable: isSelectable,\n                parent: isParent,\n                draggable: isDraggable,\n                dragging,\n            },\n        ]), ref: nodeRef, style: {\n            zIndex: internals.z,\n            transform: `translate(${internals.positionAbsolute.x}px,${internals.positionAbsolute.y}px)`,\n            pointerEvents: hasPointerEvents ? 'all' : 'none',\n            visibility: hasDimensions ? 'visible' : 'hidden',\n            ...node.style,\n            ...inlineDimensions,\n        }, \"data-id\": id, \"data-testid\": `rf__node-${id}`, onMouseEnter: onMouseEnterHandler, onMouseMove: onMouseMoveHandler, onMouseLeave: onMouseLeaveHandler, onContextMenu: onContextMenuHandler, onClick: onSelectNodeHandler, onDoubleClick: onDoubleClickHandler, onKeyDown: isFocusable ? onKeyDown : undefined, tabIndex: isFocusable ? 0 : undefined, onFocus: isFocusable ? onFocus : undefined, role: node.ariaRole ?? (isFocusable ? 'group' : undefined), \"aria-roledescription\": \"node\", \"aria-describedby\": disableKeyboardA11y ? undefined : `${ARIA_NODE_DESC_KEY}-${rfId}`, \"aria-label\": node.ariaLabel, ...node.domAttributes, children: jsx(Provider, { value: id, children: jsx(NodeComponent, { id: id, data: node.data, type: nodeType, positionAbsoluteX: internals.positionAbsolute.x, positionAbsoluteY: internals.positionAbsolute.y, selected: node.selected ?? false, selectable: isSelectable, draggable: isDraggable, deletable: node.deletable ?? true, isConnectable: isConnectable, sourcePosition: node.sourcePosition, targetPosition: node.targetPosition, dragging: dragging, dragHandle: node.dragHandle, zIndex: internals.z, parentId: node.parentId, ...nodeDimensions }) }) }));\n}\n\nconst selector$b = (s) => ({\n    nodesDraggable: s.nodesDraggable,\n    nodesConnectable: s.nodesConnectable,\n    nodesFocusable: s.nodesFocusable,\n    elementsSelectable: s.elementsSelectable,\n    onError: s.onError,\n});\nfunction NodeRendererComponent(props) {\n    const { nodesDraggable, nodesConnectable, nodesFocusable, elementsSelectable, onError } = useStore(selector$b, shallow);\n    const nodeIds = useVisibleNodeIds(props.onlyRenderVisibleElements);\n    const resizeObserver = useResizeObserver();\n    return (jsx(\"div\", { className: \"react-flow__nodes\", style: containerStyle, children: nodeIds.map((nodeId) => {\n            return (\n            /*\n             * The split of responsibilities between NodeRenderer and\n             * NodeComponentWrapper may appear weird. However, it’s designed to\n             * minimize the cost of updates when individual nodes change.\n             *\n             * For example, when you’re dragging a single node, that node gets\n             * updated multiple times per second. If `NodeRenderer` were to update\n             * every time, it would have to re-run the `nodes.map()` loop every\n             * time. This gets pricey with hundreds of nodes, especially if every\n             * loop cycle does more than just rendering a JSX element!\n             *\n             * As a result of this choice, we took the following implementation\n             * decisions:\n             * - NodeRenderer subscribes *only* to node IDs – and therefore\n             *   rerender *only* when visible nodes are added or removed.\n             * - NodeRenderer performs all operations the result of which can be\n             *   shared between nodes (such as creating the `ResizeObserver`\n             *   instance, or subscribing to `selector`). This means extra prop\n             *   drilling into `NodeComponentWrapper`, but it means we need to run\n             *   these operations only once – instead of once per node.\n             * - Any operations that you’d normally write inside `nodes.map` are\n             *   moved into `NodeComponentWrapper`. This ensures they are\n             *   memorized – so if `NodeRenderer` *has* to rerender, it only\n             *   needs to regenerate the list of nodes, nothing else.\n             */\n            jsx(NodeWrapper, { id: nodeId, nodeTypes: props.nodeTypes, nodeExtent: props.nodeExtent, onClick: props.onNodeClick, onMouseEnter: props.onNodeMouseEnter, onMouseMove: props.onNodeMouseMove, onMouseLeave: props.onNodeMouseLeave, onContextMenu: props.onNodeContextMenu, onDoubleClick: props.onNodeDoubleClick, noDragClassName: props.noDragClassName, noPanClassName: props.noPanClassName, rfId: props.rfId, disableKeyboardA11y: props.disableKeyboardA11y, resizeObserver: resizeObserver, nodesDraggable: nodesDraggable, nodesConnectable: nodesConnectable, nodesFocusable: nodesFocusable, elementsSelectable: elementsSelectable, nodeClickDistance: props.nodeClickDistance, onError: onError }, nodeId));\n        }) }));\n}\nNodeRendererComponent.displayName = 'NodeRenderer';\nconst NodeRenderer = memo(NodeRendererComponent);\n\n/**\n * Hook for getting the visible edge ids from the store.\n *\n * @internal\n * @param onlyRenderVisible\n * @returns array with visible edge ids\n */\nfunction useVisibleEdgeIds(onlyRenderVisible) {\n    const edgeIds = useStore(useCallback((s) => {\n        if (!onlyRenderVisible) {\n            return s.edges.map((edge) => edge.id);\n        }\n        const visibleEdgeIds = [];\n        if (s.width && s.height) {\n            for (const edge of s.edges) {\n                const sourceNode = s.nodeLookup.get(edge.source);\n                const targetNode = s.nodeLookup.get(edge.target);\n                if (sourceNode &&\n                    targetNode &&\n                    isEdgeVisible({\n                        sourceNode,\n                        targetNode,\n                        width: s.width,\n                        height: s.height,\n                        transform: s.transform,\n                    })) {\n                    visibleEdgeIds.push(edge.id);\n                }\n            }\n        }\n        return visibleEdgeIds;\n    }, [onlyRenderVisible]), shallow);\n    return edgeIds;\n}\n\nconst ArrowSymbol = ({ color = 'none', strokeWidth = 1 }) => {\n    return (jsx(\"polyline\", { style: {\n            stroke: color,\n            strokeWidth,\n        }, strokeLinecap: \"round\", strokeLinejoin: \"round\", fill: \"none\", points: \"-5,-4 0,0 -5,4\" }));\n};\nconst ArrowClosedSymbol = ({ color = 'none', strokeWidth = 1 }) => {\n    return (jsx(\"polyline\", { style: {\n            stroke: color,\n            fill: color,\n            strokeWidth,\n        }, strokeLinecap: \"round\", strokeLinejoin: \"round\", points: \"-5,-4 0,0 -5,4 -5,-4\" }));\n};\nconst MarkerSymbols = {\n    [MarkerType.Arrow]: ArrowSymbol,\n    [MarkerType.ArrowClosed]: ArrowClosedSymbol,\n};\nfunction useMarkerSymbol(type) {\n    const store = useStoreApi();\n    const symbol = useMemo(() => {\n        const symbolExists = Object.prototype.hasOwnProperty.call(MarkerSymbols, type);\n        if (!symbolExists) {\n            store.getState().onError?.('009', errorMessages['error009'](type));\n            return null;\n        }\n        return MarkerSymbols[type];\n    }, [type]);\n    return symbol;\n}\n\nconst Marker = ({ id, type, color, width = 12.5, height = 12.5, markerUnits = 'strokeWidth', strokeWidth, orient = 'auto-start-reverse', }) => {\n    const Symbol = useMarkerSymbol(type);\n    if (!Symbol) {\n        return null;\n    }\n    return (jsx(\"marker\", { className: \"react-flow__arrowhead\", id: id, markerWidth: `${width}`, markerHeight: `${height}`, viewBox: \"-10 -10 20 20\", markerUnits: markerUnits, orient: orient, refX: \"0\", refY: \"0\", children: jsx(Symbol, { color: color, strokeWidth: strokeWidth }) }));\n};\n/*\n * when you have multiple flows on a page and you hide the first one, the other ones have no markers anymore\n * when they do have markers with the same ids. To prevent this the user can pass a unique id to the react flow wrapper\n * that we can then use for creating our unique marker ids\n */\nconst MarkerDefinitions = ({ defaultColor, rfId }) => {\n    const edges = useStore((s) => s.edges);\n    const defaultEdgeOptions = useStore((s) => s.defaultEdgeOptions);\n    const markers = useMemo(() => {\n        const markers = createMarkerIds(edges, {\n            id: rfId,\n            defaultColor,\n            defaultMarkerStart: defaultEdgeOptions?.markerStart,\n            defaultMarkerEnd: defaultEdgeOptions?.markerEnd,\n        });\n        return markers;\n    }, [edges, defaultEdgeOptions, rfId, defaultColor]);\n    if (!markers.length) {\n        return null;\n    }\n    return (jsx(\"svg\", { className: \"react-flow__marker\", \"aria-hidden\": \"true\", children: jsx(\"defs\", { children: markers.map((marker) => (jsx(Marker, { id: marker.id, type: marker.type, color: marker.color, width: marker.width, height: marker.height, markerUnits: marker.markerUnits, strokeWidth: marker.strokeWidth, orient: marker.orient }, marker.id))) }) }));\n};\nMarkerDefinitions.displayName = 'MarkerDefinitions';\nvar MarkerDefinitions$1 = memo(MarkerDefinitions);\n\nfunction EdgeTextComponent({ x, y, label, labelStyle, labelShowBg = true, labelBgStyle, labelBgPadding = [2, 4], labelBgBorderRadius = 2, children, className, ...rest }) {\n    const [edgeTextBbox, setEdgeTextBbox] = useState({ x: 1, y: 0, width: 0, height: 0 });\n    const edgeTextClasses = cc(['react-flow__edge-textwrapper', className]);\n    const edgeTextRef = useRef(null);\n    useEffect(() => {\n        if (edgeTextRef.current) {\n            const textBbox = edgeTextRef.current.getBBox();\n            setEdgeTextBbox({\n                x: textBbox.x,\n                y: textBbox.y,\n                width: textBbox.width,\n                height: textBbox.height,\n            });\n        }\n    }, [label]);\n    if (!label) {\n        return null;\n    }\n    return (jsxs(\"g\", { transform: `translate(${x - edgeTextBbox.width / 2} ${y - edgeTextBbox.height / 2})`, className: edgeTextClasses, visibility: edgeTextBbox.width ? 'visible' : 'hidden', ...rest, children: [labelShowBg && (jsx(\"rect\", { width: edgeTextBbox.width + 2 * labelBgPadding[0], x: -labelBgPadding[0], y: -labelBgPadding[1], height: edgeTextBbox.height + 2 * labelBgPadding[1], className: \"react-flow__edge-textbg\", style: labelBgStyle, rx: labelBgBorderRadius, ry: labelBgBorderRadius })), jsx(\"text\", { className: \"react-flow__edge-text\", y: edgeTextBbox.height / 2, dy: \"0.3em\", ref: edgeTextRef, style: labelStyle, children: label }), children] }));\n}\nEdgeTextComponent.displayName = 'EdgeText';\n/**\n * You can use the `<EdgeText />` component as a helper component to display text\n * within your custom edges.\n *\n * @public\n *\n * @example\n * ```jsx\n * import { EdgeText } from '@xyflow/react';\n *\n * export function CustomEdgeLabel({ label }) {\n *   return (\n *     <EdgeText\n *       x={100}\n *       y={100}\n *       label={label}\n *       labelStyle={{ fill: 'white' }}\n *       labelShowBg\n *       labelBgStyle={{ fill: 'red' }}\n *       labelBgPadding={[2, 4]}\n *       labelBgBorderRadius={2}\n *     />\n *   );\n * }\n *```\n */\nconst EdgeText = memo(EdgeTextComponent);\n\n/**\n * The `<BaseEdge />` component gets used internally for all the edges. It can be\n * used inside a custom edge and handles the invisible helper edge and the edge label\n * for you.\n *\n * @public\n * @example\n * ```jsx\n *import { BaseEdge } from '@xyflow/react';\n *\n *export function CustomEdge({ sourceX, sourceY, targetX, targetY, ...props }) {\n *  const [edgePath] = getStraightPath({\n *    sourceX,\n *    sourceY,\n *    targetX,\n *    targetY,\n *  });\n *\n *  return <BaseEdge path={edgePath} {...props} />;\n *}\n *```\n *\n * @remarks If you want to use an edge marker with the [`<BaseEdge />`](/api-reference/components/base-edge) component,\n * you can pass the `markerStart` or `markerEnd` props passed to your custom edge\n * through to the [`<BaseEdge />`](/api-reference/components/base-edge) component.\n * You can see all the props passed to a custom edge by looking at the [`EdgeProps`](/api-reference/types/edge-props) type.\n */\nfunction BaseEdge({ path, labelX, labelY, label, labelStyle, labelShowBg, labelBgStyle, labelBgPadding, labelBgBorderRadius, interactionWidth = 20, ...props }) {\n    return (jsxs(Fragment, { children: [jsx(\"path\", { ...props, d: path, fill: \"none\", className: cc(['react-flow__edge-path', props.className]) }), interactionWidth && (jsx(\"path\", { d: path, fill: \"none\", strokeOpacity: 0, strokeWidth: interactionWidth, className: \"react-flow__edge-interaction\" })), label && isNumeric(labelX) && isNumeric(labelY) ? (jsx(EdgeText, { x: labelX, y: labelY, label: label, labelStyle: labelStyle, labelShowBg: labelShowBg, labelBgStyle: labelBgStyle, labelBgPadding: labelBgPadding, labelBgBorderRadius: labelBgBorderRadius })) : null] }));\n}\n\nfunction getControl({ pos, x1, y1, x2, y2 }) {\n    if (pos === Position.Left || pos === Position.Right) {\n        return [0.5 * (x1 + x2), y1];\n    }\n    return [x1, 0.5 * (y1 + y2)];\n}\n/**\n * The `getSimpleBezierPath` util returns everything you need to render a simple\n * bezier edge between two nodes.\n * @public\n * @returns\n * - `path`: the path to use in an SVG `<path>` element.\n * - `labelX`: the `x` position you can use to render a label for this edge.\n * - `labelY`: the `y` position you can use to render a label for this edge.\n * - `offsetX`: the absolute difference between the source `x` position and the `x` position of the\n * middle of this path.\n * - `offsetY`: the absolute difference between the source `y` position and the `y` position of the\n * middle of this path.\n */\nfunction getSimpleBezierPath({ sourceX, sourceY, sourcePosition = Position.Bottom, targetX, targetY, targetPosition = Position.Top, }) {\n    const [sourceControlX, sourceControlY] = getControl({\n        pos: sourcePosition,\n        x1: sourceX,\n        y1: sourceY,\n        x2: targetX,\n        y2: targetY,\n    });\n    const [targetControlX, targetControlY] = getControl({\n        pos: targetPosition,\n        x1: targetX,\n        y1: targetY,\n        x2: sourceX,\n        y2: sourceY,\n    });\n    const [labelX, labelY, offsetX, offsetY] = getBezierEdgeCenter({\n        sourceX,\n        sourceY,\n        targetX,\n        targetY,\n        sourceControlX,\n        sourceControlY,\n        targetControlX,\n        targetControlY,\n    });\n    return [\n        `M${sourceX},${sourceY} C${sourceControlX},${sourceControlY} ${targetControlX},${targetControlY} ${targetX},${targetY}`,\n        labelX,\n        labelY,\n        offsetX,\n        offsetY,\n    ];\n}\nfunction createSimpleBezierEdge(params) {\n    // eslint-disable-next-line react/display-name\n    return memo(({ id, sourceX, sourceY, targetX, targetY, sourcePosition, targetPosition, label, labelStyle, labelShowBg, labelBgStyle, labelBgPadding, labelBgBorderRadius, style, markerEnd, markerStart, interactionWidth, }) => {\n        const [path, labelX, labelY] = getSimpleBezierPath({\n            sourceX,\n            sourceY,\n            sourcePosition,\n            targetX,\n            targetY,\n            targetPosition,\n        });\n        const _id = params.isInternal ? undefined : id;\n        return (jsx(BaseEdge, { id: _id, path: path, labelX: labelX, labelY: labelY, label: label, labelStyle: labelStyle, labelShowBg: labelShowBg, labelBgStyle: labelBgStyle, labelBgPadding: labelBgPadding, labelBgBorderRadius: labelBgBorderRadius, style: style, markerEnd: markerEnd, markerStart: markerStart, interactionWidth: interactionWidth }));\n    });\n}\nconst SimpleBezierEdge = createSimpleBezierEdge({ isInternal: false });\nconst SimpleBezierEdgeInternal = createSimpleBezierEdge({ isInternal: true });\nSimpleBezierEdge.displayName = 'SimpleBezierEdge';\nSimpleBezierEdgeInternal.displayName = 'SimpleBezierEdgeInternal';\n\nfunction createSmoothStepEdge(params) {\n    // eslint-disable-next-line react/display-name\n    return memo(({ id, sourceX, sourceY, targetX, targetY, label, labelStyle, labelShowBg, labelBgStyle, labelBgPadding, labelBgBorderRadius, style, sourcePosition = Position.Bottom, targetPosition = Position.Top, markerEnd, markerStart, pathOptions, interactionWidth, }) => {\n        const [path, labelX, labelY] = getSmoothStepPath({\n            sourceX,\n            sourceY,\n            sourcePosition,\n            targetX,\n            targetY,\n            targetPosition,\n            borderRadius: pathOptions?.borderRadius,\n            offset: pathOptions?.offset,\n            stepPosition: pathOptions?.stepPosition,\n        });\n        const _id = params.isInternal ? undefined : id;\n        return (jsx(BaseEdge, { id: _id, path: path, labelX: labelX, labelY: labelY, label: label, labelStyle: labelStyle, labelShowBg: labelShowBg, labelBgStyle: labelBgStyle, labelBgPadding: labelBgPadding, labelBgBorderRadius: labelBgBorderRadius, style: style, markerEnd: markerEnd, markerStart: markerStart, interactionWidth: interactionWidth }));\n    });\n}\n/**\n * Component that can be used inside a custom edge to render a smooth step edge.\n *\n * @public\n * @example\n *\n * ```tsx\n * import { SmoothStepEdge } from '@xyflow/react';\n *\n * function CustomEdge({ sourceX, sourceY, targetX, targetY, sourcePosition, targetPosition }) {\n *   return (\n *     <SmoothStepEdge\n *       sourceX={sourceX}\n *       sourceY={sourceY}\n *       targetX={targetX}\n *       targetY={targetY}\n *       sourcePosition={sourcePosition}\n *       targetPosition={targetPosition}\n *     />\n *   );\n * }\n * ```\n */\nconst SmoothStepEdge = createSmoothStepEdge({ isInternal: false });\n/**\n * @internal\n */\nconst SmoothStepEdgeInternal = createSmoothStepEdge({ isInternal: true });\nSmoothStepEdge.displayName = 'SmoothStepEdge';\nSmoothStepEdgeInternal.displayName = 'SmoothStepEdgeInternal';\n\nfunction createStepEdge(params) {\n    // eslint-disable-next-line react/display-name\n    return memo(({ id, ...props }) => {\n        const _id = params.isInternal ? undefined : id;\n        return (jsx(SmoothStepEdge, { ...props, id: _id, pathOptions: useMemo(() => ({ borderRadius: 0, offset: props.pathOptions?.offset }), [props.pathOptions?.offset]) }));\n    });\n}\n/**\n * Component that can be used inside a custom edge to render a step edge.\n *\n * @public\n * @example\n *\n * ```tsx\n * import { StepEdge } from '@xyflow/react';\n *\n * function CustomEdge({ sourceX, sourceY, targetX, targetY, sourcePosition, targetPosition }) {\n *   return (\n *     <StepEdge\n *       sourceX={sourceX}\n *       sourceY={sourceY}\n *       targetX={targetX}\n *       targetY={targetY}\n *       sourcePosition={sourcePosition}\n *       targetPosition={targetPosition}\n *     />\n *   );\n * }\n * ```\n */\nconst StepEdge = createStepEdge({ isInternal: false });\n/**\n * @internal\n */\nconst StepEdgeInternal = createStepEdge({ isInternal: true });\nStepEdge.displayName = 'StepEdge';\nStepEdgeInternal.displayName = 'StepEdgeInternal';\n\nfunction createStraightEdge(params) {\n    // eslint-disable-next-line react/display-name\n    return memo(({ id, sourceX, sourceY, targetX, targetY, label, labelStyle, labelShowBg, labelBgStyle, labelBgPadding, labelBgBorderRadius, style, markerEnd, markerStart, interactionWidth, }) => {\n        const [path, labelX, labelY] = getStraightPath({ sourceX, sourceY, targetX, targetY });\n        const _id = params.isInternal ? undefined : id;\n        return (jsx(BaseEdge, { id: _id, path: path, labelX: labelX, labelY: labelY, label: label, labelStyle: labelStyle, labelShowBg: labelShowBg, labelBgStyle: labelBgStyle, labelBgPadding: labelBgPadding, labelBgBorderRadius: labelBgBorderRadius, style: style, markerEnd: markerEnd, markerStart: markerStart, interactionWidth: interactionWidth }));\n    });\n}\n/**\n * Component that can be used inside a custom edge to render a straight line.\n *\n * @public\n * @example\n *\n * ```tsx\n * import { StraightEdge } from '@xyflow/react';\n *\n * function CustomEdge({ sourceX, sourceY, targetX, targetY }) {\n *   return (\n *     <StraightEdge\n *       sourceX={sourceX}\n *       sourceY={sourceY}\n *       targetX={targetX}\n *       targetY={targetY}\n *     />\n *   );\n * }\n * ```\n */\nconst StraightEdge = createStraightEdge({ isInternal: false });\n/**\n * @internal\n */\nconst StraightEdgeInternal = createStraightEdge({ isInternal: true });\nStraightEdge.displayName = 'StraightEdge';\nStraightEdgeInternal.displayName = 'StraightEdgeInternal';\n\nfunction createBezierEdge(params) {\n    // eslint-disable-next-line react/display-name\n    return memo(({ id, sourceX, sourceY, targetX, targetY, sourcePosition = Position.Bottom, targetPosition = Position.Top, label, labelStyle, labelShowBg, labelBgStyle, labelBgPadding, labelBgBorderRadius, style, markerEnd, markerStart, pathOptions, interactionWidth, }) => {\n        const [path, labelX, labelY] = getBezierPath({\n            sourceX,\n            sourceY,\n            sourcePosition,\n            targetX,\n            targetY,\n            targetPosition,\n            curvature: pathOptions?.curvature,\n        });\n        const _id = params.isInternal ? undefined : id;\n        return (jsx(BaseEdge, { id: _id, path: path, labelX: labelX, labelY: labelY, label: label, labelStyle: labelStyle, labelShowBg: labelShowBg, labelBgStyle: labelBgStyle, labelBgPadding: labelBgPadding, labelBgBorderRadius: labelBgBorderRadius, style: style, markerEnd: markerEnd, markerStart: markerStart, interactionWidth: interactionWidth }));\n    });\n}\n/**\n * Component that can be used inside a custom edge to render a bezier curve.\n *\n * @public\n * @example\n *\n * ```tsx\n * import { BezierEdge } from '@xyflow/react';\n *\n * function CustomEdge({ sourceX, sourceY, targetX, targetY, sourcePosition, targetPosition }) {\n *   return (\n *     <BezierEdge\n *       sourceX={sourceX}\n *       sourceY={sourceY}\n *       targetX={targetX}\n *       targetY={targetY}\n *       sourcePosition={sourcePosition}\n *       targetPosition={targetPosition}\n *     />\n *   );\n * }\n * ```\n */\nconst BezierEdge = createBezierEdge({ isInternal: false });\n/**\n * @internal\n */\nconst BezierEdgeInternal = createBezierEdge({ isInternal: true });\nBezierEdge.displayName = 'BezierEdge';\nBezierEdgeInternal.displayName = 'BezierEdgeInternal';\n\nconst builtinEdgeTypes = {\n    default: BezierEdgeInternal,\n    straight: StraightEdgeInternal,\n    step: StepEdgeInternal,\n    smoothstep: SmoothStepEdgeInternal,\n    simplebezier: SimpleBezierEdgeInternal,\n};\nconst nullPosition = {\n    sourceX: null,\n    sourceY: null,\n    targetX: null,\n    targetY: null,\n    sourcePosition: null,\n    targetPosition: null,\n};\n\nconst shiftX = (x, shift, position) => {\n    if (position === Position.Left)\n        return x - shift;\n    if (position === Position.Right)\n        return x + shift;\n    return x;\n};\nconst shiftY = (y, shift, position) => {\n    if (position === Position.Top)\n        return y - shift;\n    if (position === Position.Bottom)\n        return y + shift;\n    return y;\n};\nconst EdgeUpdaterClassName = 'react-flow__edgeupdater';\n/**\n * @internal\n */\nfunction EdgeAnchor({ position, centerX, centerY, radius = 10, onMouseDown, onMouseEnter, onMouseOut, type, }) {\n    return (jsx(\"circle\", { onMouseDown: onMouseDown, onMouseEnter: onMouseEnter, onMouseOut: onMouseOut, className: cc([EdgeUpdaterClassName, `${EdgeUpdaterClassName}-${type}`]), cx: shiftX(centerX, radius, position), cy: shiftY(centerY, radius, position), r: radius, stroke: \"transparent\", fill: \"transparent\" }));\n}\n\nfunction EdgeUpdateAnchors({ isReconnectable, reconnectRadius, edge, sourceX, sourceY, targetX, targetY, sourcePosition, targetPosition, onReconnect, onReconnectStart, onReconnectEnd, setReconnecting, setUpdateHover, }) {\n    const store = useStoreApi();\n    const handleEdgeUpdater = (event, oppositeHandle) => {\n        // avoid triggering edge updater if mouse btn is not left\n        if (event.button !== 0) {\n            return;\n        }\n        const { autoPanOnConnect, domNode, isValidConnection, connectionMode, connectionRadius, lib, onConnectStart, onConnectEnd, cancelConnection, nodeLookup, rfId: flowId, panBy, updateConnection, } = store.getState();\n        const isTarget = oppositeHandle.type === 'target';\n        const _onReconnectEnd = (evt, connectionState) => {\n            setReconnecting(false);\n            onReconnectEnd?.(evt, edge, oppositeHandle.type, connectionState);\n        };\n        const onConnectEdge = (connection) => onReconnect?.(edge, connection);\n        const _onConnectStart = (_event, params) => {\n            setReconnecting(true);\n            onReconnectStart?.(event, edge, oppositeHandle.type);\n            onConnectStart?.(_event, params);\n        };\n        XYHandle.onPointerDown(event.nativeEvent, {\n            autoPanOnConnect,\n            connectionMode,\n            connectionRadius,\n            domNode,\n            handleId: oppositeHandle.id,\n            nodeId: oppositeHandle.nodeId,\n            nodeLookup,\n            isTarget,\n            edgeUpdaterType: oppositeHandle.type,\n            lib,\n            flowId,\n            cancelConnection,\n            panBy,\n            isValidConnection,\n            onConnect: onConnectEdge,\n            onConnectStart: _onConnectStart,\n            onConnectEnd,\n            onReconnectEnd: _onReconnectEnd,\n            updateConnection,\n            getTransform: () => store.getState().transform,\n            getFromHandle: () => store.getState().connection.fromHandle,\n            dragThreshold: store.getState().connectionDragThreshold,\n        });\n    };\n    const onReconnectSourceMouseDown = (event) => handleEdgeUpdater(event, { nodeId: edge.target, id: edge.targetHandle ?? null, type: 'target' });\n    const onReconnectTargetMouseDown = (event) => handleEdgeUpdater(event, { nodeId: edge.source, id: edge.sourceHandle ?? null, type: 'source' });\n    const onReconnectMouseEnter = () => setUpdateHover(true);\n    const onReconnectMouseOut = () => setUpdateHover(false);\n    return (jsxs(Fragment, { children: [(isReconnectable === true || isReconnectable === 'source') && (jsx(EdgeAnchor, { position: sourcePosition, centerX: sourceX, centerY: sourceY, radius: reconnectRadius, onMouseDown: onReconnectSourceMouseDown, onMouseEnter: onReconnectMouseEnter, onMouseOut: onReconnectMouseOut, type: \"source\" })), (isReconnectable === true || isReconnectable === 'target') && (jsx(EdgeAnchor, { position: targetPosition, centerX: targetX, centerY: targetY, radius: reconnectRadius, onMouseDown: onReconnectTargetMouseDown, onMouseEnter: onReconnectMouseEnter, onMouseOut: onReconnectMouseOut, type: \"target\" }))] }));\n}\n\nfunction EdgeWrapper({ id, edgesFocusable, edgesReconnectable, elementsSelectable, onClick, onDoubleClick, onContextMenu, onMouseEnter, onMouseMove, onMouseLeave, reconnectRadius, onReconnect, onReconnectStart, onReconnectEnd, rfId, edgeTypes, noPanClassName, onError, disableKeyboardA11y, }) {\n    let edge = useStore((s) => s.edgeLookup.get(id));\n    const defaultEdgeOptions = useStore((s) => s.defaultEdgeOptions);\n    edge = defaultEdgeOptions ? { ...defaultEdgeOptions, ...edge } : edge;\n    let edgeType = edge.type || 'default';\n    let EdgeComponent = edgeTypes?.[edgeType] || builtinEdgeTypes[edgeType];\n    if (EdgeComponent === undefined) {\n        onError?.('011', errorMessages['error011'](edgeType));\n        edgeType = 'default';\n        EdgeComponent = edgeTypes?.['default'] || builtinEdgeTypes.default;\n    }\n    const isFocusable = !!(edge.focusable || (edgesFocusable && typeof edge.focusable === 'undefined'));\n    const isReconnectable = typeof onReconnect !== 'undefined' &&\n        (edge.reconnectable || (edgesReconnectable && typeof edge.reconnectable === 'undefined'));\n    const isSelectable = !!(edge.selectable || (elementsSelectable && typeof edge.selectable === 'undefined'));\n    const edgeRef = useRef(null);\n    const [updateHover, setUpdateHover] = useState(false);\n    const [reconnecting, setReconnecting] = useState(false);\n    const store = useStoreApi();\n    const { zIndex, sourceX, sourceY, targetX, targetY, sourcePosition, targetPosition } = useStore(useCallback((store) => {\n        const sourceNode = store.nodeLookup.get(edge.source);\n        const targetNode = store.nodeLookup.get(edge.target);\n        if (!sourceNode || !targetNode) {\n            return {\n                zIndex: edge.zIndex,\n                ...nullPosition,\n            };\n        }\n        const edgePosition = getEdgePosition({\n            id,\n            sourceNode,\n            targetNode,\n            sourceHandle: edge.sourceHandle || null,\n            targetHandle: edge.targetHandle || null,\n            connectionMode: store.connectionMode,\n            onError,\n        });\n        const zIndex = getElevatedEdgeZIndex({\n            selected: edge.selected,\n            zIndex: edge.zIndex,\n            sourceNode,\n            targetNode,\n            elevateOnSelect: store.elevateEdgesOnSelect,\n        });\n        return {\n            zIndex,\n            ...(edgePosition || nullPosition),\n        };\n    }, [edge.source, edge.target, edge.sourceHandle, edge.targetHandle, edge.selected, edge.zIndex]), shallow);\n    const markerStartUrl = useMemo(() => (edge.markerStart ? `url('#${getMarkerId(edge.markerStart, rfId)}')` : undefined), [edge.markerStart, rfId]);\n    const markerEndUrl = useMemo(() => (edge.markerEnd ? `url('#${getMarkerId(edge.markerEnd, rfId)}')` : undefined), [edge.markerEnd, rfId]);\n    if (edge.hidden || sourceX === null || sourceY === null || targetX === null || targetY === null) {\n        return null;\n    }\n    const onEdgeClick = (event) => {\n        const { addSelectedEdges, unselectNodesAndEdges, multiSelectionActive } = store.getState();\n        if (isSelectable) {\n            store.setState({ nodesSelectionActive: false });\n            if (edge.selected && multiSelectionActive) {\n                unselectNodesAndEdges({ nodes: [], edges: [edge] });\n                edgeRef.current?.blur();\n            }\n            else {\n                addSelectedEdges([id]);\n            }\n        }\n        if (onClick) {\n            onClick(event, edge);\n        }\n    };\n    const onEdgeDoubleClick = onDoubleClick\n        ? (event) => {\n            onDoubleClick(event, { ...edge });\n        }\n        : undefined;\n    const onEdgeContextMenu = onContextMenu\n        ? (event) => {\n            onContextMenu(event, { ...edge });\n        }\n        : undefined;\n    const onEdgeMouseEnter = onMouseEnter\n        ? (event) => {\n            onMouseEnter(event, { ...edge });\n        }\n        : undefined;\n    const onEdgeMouseMove = onMouseMove\n        ? (event) => {\n            onMouseMove(event, { ...edge });\n        }\n        : undefined;\n    const onEdgeMouseLeave = onMouseLeave\n        ? (event) => {\n            onMouseLeave(event, { ...edge });\n        }\n        : undefined;\n    const onKeyDown = (event) => {\n        if (!disableKeyboardA11y && elementSelectionKeys.includes(event.key) && isSelectable) {\n            const { unselectNodesAndEdges, addSelectedEdges } = store.getState();\n            const unselect = event.key === 'Escape';\n            if (unselect) {\n                edgeRef.current?.blur();\n                unselectNodesAndEdges({ edges: [edge] });\n            }\n            else {\n                addSelectedEdges([id]);\n            }\n        }\n    };\n    return (jsx(\"svg\", { style: { zIndex }, children: jsxs(\"g\", { className: cc([\n                'react-flow__edge',\n                `react-flow__edge-${edgeType}`,\n                edge.className,\n                noPanClassName,\n                {\n                    selected: edge.selected,\n                    animated: edge.animated,\n                    inactive: !isSelectable && !onClick,\n                    updating: updateHover,\n                    selectable: isSelectable,\n                },\n            ]), onClick: onEdgeClick, onDoubleClick: onEdgeDoubleClick, onContextMenu: onEdgeContextMenu, onMouseEnter: onEdgeMouseEnter, onMouseMove: onEdgeMouseMove, onMouseLeave: onEdgeMouseLeave, onKeyDown: isFocusable ? onKeyDown : undefined, tabIndex: isFocusable ? 0 : undefined, role: edge.ariaRole ?? (isFocusable ? 'group' : 'img'), \"aria-roledescription\": \"edge\", \"data-id\": id, \"data-testid\": `rf__edge-${id}`, \"aria-label\": edge.ariaLabel === null ? undefined : edge.ariaLabel || `Edge from ${edge.source} to ${edge.target}`, \"aria-describedby\": isFocusable ? `${ARIA_EDGE_DESC_KEY}-${rfId}` : undefined, ref: edgeRef, ...edge.domAttributes, children: [!reconnecting && (jsx(EdgeComponent, { id: id, source: edge.source, target: edge.target, type: edge.type, selected: edge.selected, animated: edge.animated, selectable: isSelectable, deletable: edge.deletable ?? true, label: edge.label, labelStyle: edge.labelStyle, labelShowBg: edge.labelShowBg, labelBgStyle: edge.labelBgStyle, labelBgPadding: edge.labelBgPadding, labelBgBorderRadius: edge.labelBgBorderRadius, sourceX: sourceX, sourceY: sourceY, targetX: targetX, targetY: targetY, sourcePosition: sourcePosition, targetPosition: targetPosition, data: edge.data, style: edge.style, sourceHandleId: edge.sourceHandle, targetHandleId: edge.targetHandle, markerStart: markerStartUrl, markerEnd: markerEndUrl, pathOptions: 'pathOptions' in edge ? edge.pathOptions : undefined, interactionWidth: edge.interactionWidth })), isReconnectable && (jsx(EdgeUpdateAnchors, { edge: edge, isReconnectable: isReconnectable, reconnectRadius: reconnectRadius, onReconnect: onReconnect, onReconnectStart: onReconnectStart, onReconnectEnd: onReconnectEnd, sourceX: sourceX, sourceY: sourceY, targetX: targetX, targetY: targetY, sourcePosition: sourcePosition, targetPosition: targetPosition, setUpdateHover: setUpdateHover, setReconnecting: setReconnecting }))] }) }));\n}\n\nconst selector$a = (s) => ({\n    edgesFocusable: s.edgesFocusable,\n    edgesReconnectable: s.edgesReconnectable,\n    elementsSelectable: s.elementsSelectable,\n    connectionMode: s.connectionMode,\n    onError: s.onError,\n});\nfunction EdgeRendererComponent({ defaultMarkerColor, onlyRenderVisibleElements, rfId, edgeTypes, noPanClassName, onReconnect, onEdgeContextMenu, onEdgeMouseEnter, onEdgeMouseMove, onEdgeMouseLeave, onEdgeClick, reconnectRadius, onEdgeDoubleClick, onReconnectStart, onReconnectEnd, disableKeyboardA11y, }) {\n    const { edgesFocusable, edgesReconnectable, elementsSelectable, onError } = useStore(selector$a, shallow);\n    const edgeIds = useVisibleEdgeIds(onlyRenderVisibleElements);\n    return (jsxs(\"div\", { className: \"react-flow__edges\", children: [jsx(MarkerDefinitions$1, { defaultColor: defaultMarkerColor, rfId: rfId }), edgeIds.map((id) => {\n                return (jsx(EdgeWrapper, { id: id, edgesFocusable: edgesFocusable, edgesReconnectable: edgesReconnectable, elementsSelectable: elementsSelectable, noPanClassName: noPanClassName, onReconnect: onReconnect, onContextMenu: onEdgeContextMenu, onMouseEnter: onEdgeMouseEnter, onMouseMove: onEdgeMouseMove, onMouseLeave: onEdgeMouseLeave, onClick: onEdgeClick, reconnectRadius: reconnectRadius, onDoubleClick: onEdgeDoubleClick, onReconnectStart: onReconnectStart, onReconnectEnd: onReconnectEnd, rfId: rfId, onError: onError, edgeTypes: edgeTypes, disableKeyboardA11y: disableKeyboardA11y }, id));\n            })] }));\n}\nEdgeRendererComponent.displayName = 'EdgeRenderer';\nconst EdgeRenderer = memo(EdgeRendererComponent);\n\nconst selector$9 = (s) => `translate(${s.transform[0]}px,${s.transform[1]}px) scale(${s.transform[2]})`;\nfunction Viewport({ children }) {\n    const transform = useStore(selector$9);\n    return (jsx(\"div\", { className: \"react-flow__viewport xyflow__viewport react-flow__container\", style: { transform }, children: children }));\n}\n\n/**\n * Hook for calling onInit handler.\n *\n * @internal\n */\nfunction useOnInitHandler(onInit) {\n    const rfInstance = useReactFlow();\n    const isInitialized = useRef(false);\n    useEffect(() => {\n        if (!isInitialized.current && rfInstance.viewportInitialized && onInit) {\n            setTimeout(() => onInit(rfInstance), 1);\n            isInitialized.current = true;\n        }\n    }, [onInit, rfInstance.viewportInitialized]);\n}\n\nconst selector$8 = (state) => state.panZoom?.syncViewport;\n/**\n * Hook for syncing the viewport with the panzoom instance.\n *\n * @internal\n * @param viewport\n */\nfunction useViewportSync(viewport) {\n    const syncViewport = useStore(selector$8);\n    const store = useStoreApi();\n    useEffect(() => {\n        if (viewport) {\n            syncViewport?.(viewport);\n            store.setState({ transform: [viewport.x, viewport.y, viewport.zoom] });\n        }\n    }, [viewport, syncViewport]);\n    return null;\n}\n\nfunction storeSelector$1(s) {\n    return s.connection.inProgress\n        ? { ...s.connection, to: pointToRendererPoint(s.connection.to, s.transform) }\n        : { ...s.connection };\n}\nfunction getSelector(connectionSelector) {\n    if (connectionSelector) {\n        const combinedSelector = (s) => {\n            const connection = storeSelector$1(s);\n            return connectionSelector(connection);\n        };\n        return combinedSelector;\n    }\n    return storeSelector$1;\n}\n/**\n * The `useConnection` hook returns the current connection when there is an active\n * connection interaction. If no connection interaction is active, it returns null\n * for every property. A typical use case for this hook is to colorize handles\n * based on a certain condition (e.g. if the connection is valid or not).\n *\n * @public\n * @param connectionSelector - An optional selector function used to extract a slice of the\n * `ConnectionState` data. Using a selector can prevent component re-renders where data you don't\n * otherwise care about might change. If a selector is not provided, the entire `ConnectionState`\n * object is returned unchanged.\n * @example\n *\n * ```tsx\n *import { useConnection } from '@xyflow/react';\n *\n *function App() {\n *  const connection = useConnection();\n *\n *  return (\n *    <div> {connection ? `Someone is trying to make a connection from ${connection.fromNode} to this one.` : 'There are currently no incoming connections!'}\n *\n *   </div>\n *   );\n * }\n * ```\n *\n * @returns ConnectionState\n */\nfunction useConnection(connectionSelector) {\n    const combinedSelector = getSelector(connectionSelector);\n    return useStore(combinedSelector, shallow);\n}\n\nconst selector$7 = (s) => ({\n    nodesConnectable: s.nodesConnectable,\n    isValid: s.connection.isValid,\n    inProgress: s.connection.inProgress,\n    width: s.width,\n    height: s.height,\n});\nfunction ConnectionLineWrapper({ containerStyle, style, type, component, }) {\n    const { nodesConnectable, width, height, isValid, inProgress } = useStore(selector$7, shallow);\n    const renderConnection = !!(width && nodesConnectable && inProgress);\n    if (!renderConnection) {\n        return null;\n    }\n    return (jsx(\"svg\", { style: containerStyle, width: width, height: height, className: \"react-flow__connectionline react-flow__container\", children: jsx(\"g\", { className: cc(['react-flow__connection', getConnectionStatus(isValid)]), children: jsx(ConnectionLine, { style: style, type: type, CustomComponent: component, isValid: isValid }) }) }));\n}\nconst ConnectionLine = ({ style, type = ConnectionLineType.Bezier, CustomComponent, isValid, }) => {\n    const { inProgress, from, fromNode, fromHandle, fromPosition, to, toNode, toHandle, toPosition } = useConnection();\n    if (!inProgress) {\n        return;\n    }\n    if (CustomComponent) {\n        return (jsx(CustomComponent, { connectionLineType: type, connectionLineStyle: style, fromNode: fromNode, fromHandle: fromHandle, fromX: from.x, fromY: from.y, toX: to.x, toY: to.y, fromPosition: fromPosition, toPosition: toPosition, connectionStatus: getConnectionStatus(isValid), toNode: toNode, toHandle: toHandle }));\n    }\n    let path = '';\n    const pathParams = {\n        sourceX: from.x,\n        sourceY: from.y,\n        sourcePosition: fromPosition,\n        targetX: to.x,\n        targetY: to.y,\n        targetPosition: toPosition,\n    };\n    switch (type) {\n        case ConnectionLineType.Bezier:\n            [path] = getBezierPath(pathParams);\n            break;\n        case ConnectionLineType.SimpleBezier:\n            [path] = getSimpleBezierPath(pathParams);\n            break;\n        case ConnectionLineType.Step:\n            [path] = getSmoothStepPath({\n                ...pathParams,\n                borderRadius: 0,\n            });\n            break;\n        case ConnectionLineType.SmoothStep:\n            [path] = getSmoothStepPath(pathParams);\n            break;\n        default:\n            [path] = getStraightPath(pathParams);\n    }\n    return jsx(\"path\", { d: path, fill: \"none\", className: \"react-flow__connection-path\", style: style });\n};\nConnectionLine.displayName = 'ConnectionLine';\n\nconst emptyTypes = {};\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction useNodeOrEdgeTypesWarning(nodeOrEdgeTypes = emptyTypes) {\n    const typesRef = useRef(nodeOrEdgeTypes);\n    const store = useStoreApi();\n    useEffect(() => {\n        if (process.env.NODE_ENV === 'development') {\n            const usedKeys = new Set([...Object.keys(typesRef.current), ...Object.keys(nodeOrEdgeTypes)]);\n            for (const key of usedKeys) {\n                if (typesRef.current[key] !== nodeOrEdgeTypes[key]) {\n                    store.getState().onError?.('002', errorMessages['error002']());\n                    break;\n                }\n            }\n            typesRef.current = nodeOrEdgeTypes;\n        }\n    }, [nodeOrEdgeTypes]);\n}\n\nfunction useStylesLoadedWarning() {\n    const store = useStoreApi();\n    const checked = useRef(false);\n    useEffect(() => {\n        if (process.env.NODE_ENV === 'development') {\n            if (!checked.current) {\n                const pane = document.querySelector('.react-flow__pane');\n                if (pane && !(window.getComputedStyle(pane).zIndex === '1')) {\n                    store.getState().onError?.('013', errorMessages['error013']('react'));\n                }\n                checked.current = true;\n            }\n        }\n    }, []);\n}\n\nfunction GraphViewComponent({ nodeTypes, edgeTypes, onInit, onNodeClick, onEdgeClick, onNodeDoubleClick, onEdgeDoubleClick, onNodeMouseEnter, onNodeMouseMove, onNodeMouseLeave, onNodeContextMenu, onSelectionContextMenu, onSelectionStart, onSelectionEnd, connectionLineType, connectionLineStyle, connectionLineComponent, connectionLineContainerStyle, selectionKeyCode, selectionOnDrag, selectionMode, multiSelectionKeyCode, panActivationKeyCode, zoomActivationKeyCode, deleteKeyCode, onlyRenderVisibleElements, elementsSelectable, defaultViewport, translateExtent, minZoom, maxZoom, preventScrolling, defaultMarkerColor, zoomOnScroll, zoomOnPinch, panOnScroll, panOnScrollSpeed, panOnScrollMode, zoomOnDoubleClick, panOnDrag, onPaneClick, onPaneMouseEnter, onPaneMouseMove, onPaneMouseLeave, onPaneScroll, onPaneContextMenu, paneClickDistance, nodeClickDistance, onEdgeContextMenu, onEdgeMouseEnter, onEdgeMouseMove, onEdgeMouseLeave, reconnectRadius, onReconnect, onReconnectStart, onReconnectEnd, noDragClassName, noWheelClassName, noPanClassName, disableKeyboardA11y, nodeExtent, rfId, viewport, onViewportChange, }) {\n    useNodeOrEdgeTypesWarning(nodeTypes);\n    useNodeOrEdgeTypesWarning(edgeTypes);\n    useStylesLoadedWarning();\n    useOnInitHandler(onInit);\n    useViewportSync(viewport);\n    return (jsx(FlowRenderer, { onPaneClick: onPaneClick, onPaneMouseEnter: onPaneMouseEnter, onPaneMouseMove: onPaneMouseMove, onPaneMouseLeave: onPaneMouseLeave, onPaneContextMenu: onPaneContextMenu, onPaneScroll: onPaneScroll, paneClickDistance: paneClickDistance, deleteKeyCode: deleteKeyCode, selectionKeyCode: selectionKeyCode, selectionOnDrag: selectionOnDrag, selectionMode: selectionMode, onSelectionStart: onSelectionStart, onSelectionEnd: onSelectionEnd, multiSelectionKeyCode: multiSelectionKeyCode, panActivationKeyCode: panActivationKeyCode, zoomActivationKeyCode: zoomActivationKeyCode, elementsSelectable: elementsSelectable, zoomOnScroll: zoomOnScroll, zoomOnPinch: zoomOnPinch, zoomOnDoubleClick: zoomOnDoubleClick, panOnScroll: panOnScroll, panOnScrollSpeed: panOnScrollSpeed, panOnScrollMode: panOnScrollMode, panOnDrag: panOnDrag, defaultViewport: defaultViewport, translateExtent: translateExtent, minZoom: minZoom, maxZoom: maxZoom, onSelectionContextMenu: onSelectionContextMenu, preventScrolling: preventScrolling, noDragClassName: noDragClassName, noWheelClassName: noWheelClassName, noPanClassName: noPanClassName, disableKeyboardA11y: disableKeyboardA11y, onViewportChange: onViewportChange, isControlledViewport: !!viewport, children: jsxs(Viewport, { children: [jsx(EdgeRenderer, { edgeTypes: edgeTypes, onEdgeClick: onEdgeClick, onEdgeDoubleClick: onEdgeDoubleClick, onReconnect: onReconnect, onReconnectStart: onReconnectStart, onReconnectEnd: onReconnectEnd, onlyRenderVisibleElements: onlyRenderVisibleElements, onEdgeContextMenu: onEdgeContextMenu, onEdgeMouseEnter: onEdgeMouseEnter, onEdgeMouseMove: onEdgeMouseMove, onEdgeMouseLeave: onEdgeMouseLeave, reconnectRadius: reconnectRadius, defaultMarkerColor: defaultMarkerColor, noPanClassName: noPanClassName, disableKeyboardA11y: disableKeyboardA11y, rfId: rfId }), jsx(ConnectionLineWrapper, { style: connectionLineStyle, type: connectionLineType, component: connectionLineComponent, containerStyle: connectionLineContainerStyle }), jsx(\"div\", { className: \"react-flow__edgelabel-renderer\" }), jsx(NodeRenderer, { nodeTypes: nodeTypes, onNodeClick: onNodeClick, onNodeDoubleClick: onNodeDoubleClick, onNodeMouseEnter: onNodeMouseEnter, onNodeMouseMove: onNodeMouseMove, onNodeMouseLeave: onNodeMouseLeave, onNodeContextMenu: onNodeContextMenu, nodeClickDistance: nodeClickDistance, onlyRenderVisibleElements: onlyRenderVisibleElements, noPanClassName: noPanClassName, noDragClassName: noDragClassName, disableKeyboardA11y: disableKeyboardA11y, nodeExtent: nodeExtent, rfId: rfId }), jsx(\"div\", { className: \"react-flow__viewport-portal\" })] }) }));\n}\nGraphViewComponent.displayName = 'GraphView';\nconst GraphView = memo(GraphViewComponent);\n\nconst getInitialState = ({ nodes, edges, defaultNodes, defaultEdges, width, height, fitView, fitViewOptions, minZoom = 0.5, maxZoom = 2, nodeOrigin, nodeExtent, } = {}) => {\n    const nodeLookup = new Map();\n    const parentLookup = new Map();\n    const connectionLookup = new Map();\n    const edgeLookup = new Map();\n    const storeEdges = defaultEdges ?? edges ?? [];\n    const storeNodes = defaultNodes ?? nodes ?? [];\n    const storeNodeOrigin = nodeOrigin ?? [0, 0];\n    const storeNodeExtent = nodeExtent ?? infiniteExtent;\n    updateConnectionLookup(connectionLookup, edgeLookup, storeEdges);\n    const nodesInitialized = adoptUserNodes(storeNodes, nodeLookup, parentLookup, {\n        nodeOrigin: storeNodeOrigin,\n        nodeExtent: storeNodeExtent,\n        elevateNodesOnSelect: false,\n    });\n    let transform = [0, 0, 1];\n    if (fitView && width && height) {\n        const bounds = getInternalNodesBounds(nodeLookup, {\n            filter: (node) => !!((node.width || node.initialWidth) && (node.height || node.initialHeight)),\n        });\n        const { x, y, zoom } = getViewportForBounds(bounds, width, height, minZoom, maxZoom, fitViewOptions?.padding ?? 0.1);\n        transform = [x, y, zoom];\n    }\n    return {\n        rfId: '1',\n        width: 0,\n        height: 0,\n        transform,\n        nodes: storeNodes,\n        nodesInitialized,\n        nodeLookup,\n        parentLookup,\n        edges: storeEdges,\n        edgeLookup,\n        connectionLookup,\n        onNodesChange: null,\n        onEdgesChange: null,\n        hasDefaultNodes: defaultNodes !== undefined,\n        hasDefaultEdges: defaultEdges !== undefined,\n        panZoom: null,\n        minZoom,\n        maxZoom,\n        translateExtent: infiniteExtent,\n        nodeExtent: storeNodeExtent,\n        nodesSelectionActive: false,\n        userSelectionActive: false,\n        userSelectionRect: null,\n        connectionMode: ConnectionMode.Strict,\n        domNode: null,\n        paneDragging: false,\n        noPanClassName: 'nopan',\n        nodeOrigin: storeNodeOrigin,\n        nodeDragThreshold: 1,\n        connectionDragThreshold: 1,\n        snapGrid: [15, 15],\n        snapToGrid: false,\n        nodesDraggable: true,\n        nodesConnectable: true,\n        nodesFocusable: true,\n        edgesFocusable: true,\n        edgesReconnectable: true,\n        elementsSelectable: true,\n        elevateNodesOnSelect: true,\n        elevateEdgesOnSelect: false,\n        selectNodesOnDrag: true,\n        multiSelectionActive: false,\n        fitViewQueued: fitView ?? false,\n        fitViewOptions,\n        fitViewResolver: null,\n        connection: { ...initialConnection },\n        connectionClickStartHandle: null,\n        connectOnClick: true,\n        ariaLiveMessage: '',\n        autoPanOnConnect: true,\n        autoPanOnNodeDrag: true,\n        autoPanOnNodeFocus: true,\n        autoPanSpeed: 15,\n        connectionRadius: 20,\n        onError: devWarn,\n        isValidConnection: undefined,\n        onSelectionChangeHandlers: [],\n        lib: 'react',\n        debug: false,\n        ariaLabelConfig: defaultAriaLabelConfig,\n    };\n};\n\nconst createStore = ({ nodes, edges, defaultNodes, defaultEdges, width, height, fitView, fitViewOptions, minZoom, maxZoom, nodeOrigin, nodeExtent, }) => createWithEqualityFn((set, get) => {\n    async function resolveFitView() {\n        const { nodeLookup, panZoom, fitViewOptions, fitViewResolver, width, height, minZoom, maxZoom } = get();\n        if (!panZoom) {\n            return;\n        }\n        await fitViewport({\n            nodes: nodeLookup,\n            width,\n            height,\n            panZoom,\n            minZoom,\n            maxZoom,\n        }, fitViewOptions);\n        fitViewResolver?.resolve(true);\n        /**\n         * wait for the fitViewport to resolve before deleting the resolver,\n         * we want to reuse the old resolver if the user calls fitView again in the mean time\n         */\n        set({ fitViewResolver: null });\n    }\n    return {\n        ...getInitialState({\n            nodes,\n            edges,\n            width,\n            height,\n            fitView,\n            fitViewOptions,\n            minZoom,\n            maxZoom,\n            nodeOrigin,\n            nodeExtent,\n            defaultNodes,\n            defaultEdges,\n        }),\n        setNodes: (nodes) => {\n            const { nodeLookup, parentLookup, nodeOrigin, elevateNodesOnSelect, fitViewQueued } = get();\n            /*\n             * setNodes() is called exclusively in response to user actions:\n             * - either when the `<ReactFlow nodes>` prop is updated in the controlled ReactFlow setup,\n             * - or when the user calls something like `reactFlowInstance.setNodes()` in an uncontrolled ReactFlow setup.\n             *\n             * When this happens, we take the note objects passed by the user and extend them with fields\n             * relevant for internal React Flow operations.\n             */\n            const nodesInitialized = adoptUserNodes(nodes, nodeLookup, parentLookup, {\n                nodeOrigin,\n                nodeExtent,\n                elevateNodesOnSelect,\n                checkEquality: true,\n            });\n            if (fitViewQueued && nodesInitialized) {\n                resolveFitView();\n                set({ nodes, nodesInitialized, fitViewQueued: false, fitViewOptions: undefined });\n            }\n            else {\n                set({ nodes, nodesInitialized });\n            }\n        },\n        setEdges: (edges) => {\n            const { connectionLookup, edgeLookup } = get();\n            updateConnectionLookup(connectionLookup, edgeLookup, edges);\n            set({ edges });\n        },\n        setDefaultNodesAndEdges: (nodes, edges) => {\n            if (nodes) {\n                const { setNodes } = get();\n                setNodes(nodes);\n                set({ hasDefaultNodes: true });\n            }\n            if (edges) {\n                const { setEdges } = get();\n                setEdges(edges);\n                set({ hasDefaultEdges: true });\n            }\n        },\n        /*\n         * Every node gets registerd at a ResizeObserver. Whenever a node\n         * changes its dimensions, this function is called to measure the\n         * new dimensions and update the nodes.\n         */\n        updateNodeInternals: (updates) => {\n            const { triggerNodeChanges, nodeLookup, parentLookup, domNode, nodeOrigin, nodeExtent, debug, fitViewQueued } = get();\n            const { changes, updatedInternals } = updateNodeInternals(updates, nodeLookup, parentLookup, domNode, nodeOrigin, nodeExtent);\n            if (!updatedInternals) {\n                return;\n            }\n            updateAbsolutePositions(nodeLookup, parentLookup, { nodeOrigin, nodeExtent });\n            if (fitViewQueued) {\n                resolveFitView();\n                set({ fitViewQueued: false, fitViewOptions: undefined });\n            }\n            else {\n                // we always want to trigger useStore calls whenever updateNodeInternals is called\n                set({});\n            }\n            if (changes?.length > 0) {\n                if (debug) {\n                    console.log('React Flow: trigger node changes', changes);\n                }\n                triggerNodeChanges?.(changes);\n            }\n        },\n        updateNodePositions: (nodeDragItems, dragging = false) => {\n            const parentExpandChildren = [];\n            const changes = [];\n            const { nodeLookup, triggerNodeChanges } = get();\n            for (const [id, dragItem] of nodeDragItems) {\n                // we are using the nodelookup to be sure to use the current expandParent and parentId value\n                const node = nodeLookup.get(id);\n                const expandParent = !!(node?.expandParent && node?.parentId && dragItem?.position);\n                const change = {\n                    id,\n                    type: 'position',\n                    position: expandParent\n                        ? {\n                            x: Math.max(0, dragItem.position.x),\n                            y: Math.max(0, dragItem.position.y),\n                        }\n                        : dragItem.position,\n                    dragging,\n                };\n                if (expandParent && node.parentId) {\n                    parentExpandChildren.push({\n                        id,\n                        parentId: node.parentId,\n                        rect: {\n                            ...dragItem.internals.positionAbsolute,\n                            width: dragItem.measured.width ?? 0,\n                            height: dragItem.measured.height ?? 0,\n                        },\n                    });\n                }\n                changes.push(change);\n            }\n            if (parentExpandChildren.length > 0) {\n                const { parentLookup, nodeOrigin } = get();\n                const parentExpandChanges = handleExpandParent(parentExpandChildren, nodeLookup, parentLookup, nodeOrigin);\n                changes.push(...parentExpandChanges);\n            }\n            triggerNodeChanges(changes);\n        },\n        triggerNodeChanges: (changes) => {\n            const { onNodesChange, setNodes, nodes, hasDefaultNodes, debug } = get();\n            if (changes?.length) {\n                if (hasDefaultNodes) {\n                    const updatedNodes = applyNodeChanges(changes, nodes);\n                    setNodes(updatedNodes);\n                }\n                if (debug) {\n                    console.log('React Flow: trigger node changes', changes);\n                }\n                onNodesChange?.(changes);\n            }\n        },\n        triggerEdgeChanges: (changes) => {\n            const { onEdgesChange, setEdges, edges, hasDefaultEdges, debug } = get();\n            if (changes?.length) {\n                if (hasDefaultEdges) {\n                    const updatedEdges = applyEdgeChanges(changes, edges);\n                    setEdges(updatedEdges);\n                }\n                if (debug) {\n                    console.log('React Flow: trigger edge changes', changes);\n                }\n                onEdgesChange?.(changes);\n            }\n        },\n        addSelectedNodes: (selectedNodeIds) => {\n            const { multiSelectionActive, edgeLookup, nodeLookup, triggerNodeChanges, triggerEdgeChanges } = get();\n            if (multiSelectionActive) {\n                const nodeChanges = selectedNodeIds.map((nodeId) => createSelectionChange(nodeId, true));\n                triggerNodeChanges(nodeChanges);\n                return;\n            }\n            triggerNodeChanges(getSelectionChanges(nodeLookup, new Set([...selectedNodeIds]), true));\n            triggerEdgeChanges(getSelectionChanges(edgeLookup));\n        },\n        addSelectedEdges: (selectedEdgeIds) => {\n            const { multiSelectionActive, edgeLookup, nodeLookup, triggerNodeChanges, triggerEdgeChanges } = get();\n            if (multiSelectionActive) {\n                const changedEdges = selectedEdgeIds.map((edgeId) => createSelectionChange(edgeId, true));\n                triggerEdgeChanges(changedEdges);\n                return;\n            }\n            triggerEdgeChanges(getSelectionChanges(edgeLookup, new Set([...selectedEdgeIds])));\n            triggerNodeChanges(getSelectionChanges(nodeLookup, new Set(), true));\n        },\n        unselectNodesAndEdges: ({ nodes, edges } = {}) => {\n            const { edges: storeEdges, nodes: storeNodes, nodeLookup, triggerNodeChanges, triggerEdgeChanges } = get();\n            const nodesToUnselect = nodes ? nodes : storeNodes;\n            const edgesToUnselect = edges ? edges : storeEdges;\n            const nodeChanges = nodesToUnselect.map((n) => {\n                const internalNode = nodeLookup.get(n.id);\n                if (internalNode) {\n                    /*\n                     * we need to unselect the internal node that was selected previously before we\n                     * send the change to the user to prevent it to be selected while dragging the new node\n                     */\n                    internalNode.selected = false;\n                }\n                return createSelectionChange(n.id, false);\n            });\n            const edgeChanges = edgesToUnselect.map((edge) => createSelectionChange(edge.id, false));\n            triggerNodeChanges(nodeChanges);\n            triggerEdgeChanges(edgeChanges);\n        },\n        setMinZoom: (minZoom) => {\n            const { panZoom, maxZoom } = get();\n            panZoom?.setScaleExtent([minZoom, maxZoom]);\n            set({ minZoom });\n        },\n        setMaxZoom: (maxZoom) => {\n            const { panZoom, minZoom } = get();\n            panZoom?.setScaleExtent([minZoom, maxZoom]);\n            set({ maxZoom });\n        },\n        setTranslateExtent: (translateExtent) => {\n            get().panZoom?.setTranslateExtent(translateExtent);\n            set({ translateExtent });\n        },\n        setPaneClickDistance: (clickDistance) => {\n            get().panZoom?.setClickDistance(clickDistance);\n        },\n        resetSelectedElements: () => {\n            const { edges, nodes, triggerNodeChanges, triggerEdgeChanges, elementsSelectable } = get();\n            if (!elementsSelectable) {\n                return;\n            }\n            const nodeChanges = nodes.reduce((res, node) => (node.selected ? [...res, createSelectionChange(node.id, false)] : res), []);\n            const edgeChanges = edges.reduce((res, edge) => (edge.selected ? [...res, createSelectionChange(edge.id, false)] : res), []);\n            triggerNodeChanges(nodeChanges);\n            triggerEdgeChanges(edgeChanges);\n        },\n        setNodeExtent: (nextNodeExtent) => {\n            const { nodes, nodeLookup, parentLookup, nodeOrigin, elevateNodesOnSelect, nodeExtent } = get();\n            if (nextNodeExtent[0][0] === nodeExtent[0][0] &&\n                nextNodeExtent[0][1] === nodeExtent[0][1] &&\n                nextNodeExtent[1][0] === nodeExtent[1][0] &&\n                nextNodeExtent[1][1] === nodeExtent[1][1]) {\n                return;\n            }\n            adoptUserNodes(nodes, nodeLookup, parentLookup, {\n                nodeOrigin,\n                nodeExtent: nextNodeExtent,\n                elevateNodesOnSelect,\n                checkEquality: false,\n            });\n            set({ nodeExtent: nextNodeExtent });\n        },\n        panBy: (delta) => {\n            const { transform, width, height, panZoom, translateExtent } = get();\n            return panBy({ delta, panZoom, transform, translateExtent, width, height });\n        },\n        setCenter: async (x, y, options) => {\n            const { width, height, maxZoom, panZoom } = get();\n            if (!panZoom) {\n                return Promise.resolve(false);\n            }\n            const nextZoom = typeof options?.zoom !== 'undefined' ? options.zoom : maxZoom;\n            await panZoom.setViewport({\n                x: width / 2 - x * nextZoom,\n                y: height / 2 - y * nextZoom,\n                zoom: nextZoom,\n            }, { duration: options?.duration, ease: options?.ease, interpolate: options?.interpolate });\n            return Promise.resolve(true);\n        },\n        cancelConnection: () => {\n            set({\n                connection: { ...initialConnection },\n            });\n        },\n        updateConnection: (connection) => {\n            set({ connection });\n        },\n        reset: () => set({ ...getInitialState() }),\n    };\n}, Object.is);\n\n/**\n * The `<ReactFlowProvider />` component is a [context provider](https://react.dev/learn/passing-data-deeply-with-context#)\n * that makes it possible to access a flow's internal state outside of the\n * [`<ReactFlow />`](/api-reference/react-flow) component. Many of the hooks we\n * provide rely on this component to work.\n * @public\n *\n * @example\n * ```tsx\n *import { ReactFlow, ReactFlowProvider, useNodes } from '@xyflow/react'\n *\n *export default function Flow() {\n *  return (\n *    <ReactFlowProvider>\n *      <ReactFlow nodes={...} edges={...} />\n *      <Sidebar />\n *    </ReactFlowProvider>\n *  );\n *}\n *\n *function Sidebar() {\n *  // This hook will only work if the component it's used in is a child of a\n *  // <ReactFlowProvider />.\n *  const nodes = useNodes()\n *\n *  return <aside>do something with nodes</aside>;\n *}\n *```\n *\n * @remarks If you're using a router and want your flow's state to persist across routes,\n * it's vital that you place the `<ReactFlowProvider />` component _outside_ of\n * your router. If you have multiple flows on the same page you will need to use a separate\n * `<ReactFlowProvider />` for each flow.\n */\nfunction ReactFlowProvider({ initialNodes: nodes, initialEdges: edges, defaultNodes, defaultEdges, initialWidth: width, initialHeight: height, initialMinZoom: minZoom, initialMaxZoom: maxZoom, initialFitViewOptions: fitViewOptions, fitView, nodeOrigin, nodeExtent, children, }) {\n    const [store] = useState(() => createStore({\n        nodes,\n        edges,\n        defaultNodes,\n        defaultEdges,\n        width,\n        height,\n        fitView,\n        minZoom,\n        maxZoom,\n        fitViewOptions,\n        nodeOrigin,\n        nodeExtent,\n    }));\n    return (jsx(Provider$1, { value: store, children: jsx(BatchProvider, { children: children }) }));\n}\n\nfunction Wrapper({ children, nodes, edges, defaultNodes, defaultEdges, width, height, fitView, fitViewOptions, minZoom, maxZoom, nodeOrigin, nodeExtent, }) {\n    const isWrapped = useContext(StoreContext);\n    if (isWrapped) {\n        /*\n         * we need to wrap it with a fragment because it's not allowed for children to be a ReactNode\n         * https://github.com/DefinitelyTyped/DefinitelyTyped/issues/18051\n         */\n        return jsx(Fragment, { children: children });\n    }\n    return (jsx(ReactFlowProvider, { initialNodes: nodes, initialEdges: edges, defaultNodes: defaultNodes, defaultEdges: defaultEdges, initialWidth: width, initialHeight: height, fitView: fitView, initialFitViewOptions: fitViewOptions, initialMinZoom: minZoom, initialMaxZoom: maxZoom, nodeOrigin: nodeOrigin, nodeExtent: nodeExtent, children: children }));\n}\n\nconst wrapperStyle = {\n    width: '100%',\n    height: '100%',\n    overflow: 'hidden',\n    position: 'relative',\n    zIndex: 0,\n};\nfunction ReactFlow({ nodes, edges, defaultNodes, defaultEdges, className, nodeTypes, edgeTypes, onNodeClick, onEdgeClick, onInit, onMove, onMoveStart, onMoveEnd, onConnect, onConnectStart, onConnectEnd, onClickConnectStart, onClickConnectEnd, onNodeMouseEnter, onNodeMouseMove, onNodeMouseLeave, onNodeContextMenu, onNodeDoubleClick, onNodeDragStart, onNodeDrag, onNodeDragStop, onNodesDelete, onEdgesDelete, onDelete, onSelectionChange, onSelectionDragStart, onSelectionDrag, onSelectionDragStop, onSelectionContextMenu, onSelectionStart, onSelectionEnd, onBeforeDelete, connectionMode, connectionLineType = ConnectionLineType.Bezier, connectionLineStyle, connectionLineComponent, connectionLineContainerStyle, deleteKeyCode = 'Backspace', selectionKeyCode = 'Shift', selectionOnDrag = false, selectionMode = SelectionMode.Full, panActivationKeyCode = 'Space', multiSelectionKeyCode = isMacOs() ? 'Meta' : 'Control', zoomActivationKeyCode = isMacOs() ? 'Meta' : 'Control', snapToGrid, snapGrid, onlyRenderVisibleElements = false, selectNodesOnDrag, nodesDraggable, autoPanOnNodeFocus, nodesConnectable, nodesFocusable, nodeOrigin = defaultNodeOrigin, edgesFocusable, edgesReconnectable, elementsSelectable = true, defaultViewport: defaultViewport$1 = defaultViewport, minZoom = 0.5, maxZoom = 2, translateExtent = infiniteExtent, preventScrolling = true, nodeExtent, defaultMarkerColor = '#b1b1b7', zoomOnScroll = true, zoomOnPinch = true, panOnScroll = false, panOnScrollSpeed = 0.5, panOnScrollMode = PanOnScrollMode.Free, zoomOnDoubleClick = true, panOnDrag = true, onPaneClick, onPaneMouseEnter, onPaneMouseMove, onPaneMouseLeave, onPaneScroll, onPaneContextMenu, paneClickDistance = 0, nodeClickDistance = 0, children, onReconnect, onReconnectStart, onReconnectEnd, onEdgeContextMenu, onEdgeDoubleClick, onEdgeMouseEnter, onEdgeMouseMove, onEdgeMouseLeave, reconnectRadius = 10, onNodesChange, onEdgesChange, noDragClassName = 'nodrag', noWheelClassName = 'nowheel', noPanClassName = 'nopan', fitView, fitViewOptions, connectOnClick, attributionPosition, proOptions, defaultEdgeOptions, elevateNodesOnSelect, elevateEdgesOnSelect, disableKeyboardA11y = false, autoPanOnConnect, autoPanOnNodeDrag, autoPanSpeed, connectionRadius, isValidConnection, onError, style, id, nodeDragThreshold, connectionDragThreshold, viewport, onViewportChange, width, height, colorMode = 'light', debug, onScroll, ariaLabelConfig, ...rest }, ref) {\n    const rfId = id || '1';\n    const colorModeClassName = useColorModeClass(colorMode);\n    // Undo scroll events, preventing viewport from shifting when nodes outside of it are focused\n    const wrapperOnScroll = useCallback((e) => {\n        e.currentTarget.scrollTo({ top: 0, left: 0, behavior: 'instant' });\n        onScroll?.(e);\n    }, [onScroll]);\n    return (jsx(\"div\", { \"data-testid\": \"rf__wrapper\", ...rest, onScroll: wrapperOnScroll, style: { ...style, ...wrapperStyle }, ref: ref, className: cc(['react-flow', className, colorModeClassName]), id: id, role: \"application\", children: jsxs(Wrapper, { nodes: nodes, edges: edges, width: width, height: height, fitView: fitView, fitViewOptions: fitViewOptions, minZoom: minZoom, maxZoom: maxZoom, nodeOrigin: nodeOrigin, nodeExtent: nodeExtent, children: [jsx(GraphView, { onInit: onInit, onNodeClick: onNodeClick, onEdgeClick: onEdgeClick, onNodeMouseEnter: onNodeMouseEnter, onNodeMouseMove: onNodeMouseMove, onNodeMouseLeave: onNodeMouseLeave, onNodeContextMenu: onNodeContextMenu, onNodeDoubleClick: onNodeDoubleClick, nodeTypes: nodeTypes, edgeTypes: edgeTypes, connectionLineType: connectionLineType, connectionLineStyle: connectionLineStyle, connectionLineComponent: connectionLineComponent, connectionLineContainerStyle: connectionLineContainerStyle, selectionKeyCode: selectionKeyCode, selectionOnDrag: selectionOnDrag, selectionMode: selectionMode, deleteKeyCode: deleteKeyCode, multiSelectionKeyCode: multiSelectionKeyCode, panActivationKeyCode: panActivationKeyCode, zoomActivationKeyCode: zoomActivationKeyCode, onlyRenderVisibleElements: onlyRenderVisibleElements, defaultViewport: defaultViewport$1, translateExtent: translateExtent, minZoom: minZoom, maxZoom: maxZoom, preventScrolling: preventScrolling, zoomOnScroll: zoomOnScroll, zoomOnPinch: zoomOnPinch, zoomOnDoubleClick: zoomOnDoubleClick, panOnScroll: panOnScroll, panOnScrollSpeed: panOnScrollSpeed, panOnScrollMode: panOnScrollMode, panOnDrag: panOnDrag, onPaneClick: onPaneClick, onPaneMouseEnter: onPaneMouseEnter, onPaneMouseMove: onPaneMouseMove, onPaneMouseLeave: onPaneMouseLeave, onPaneScroll: onPaneScroll, onPaneContextMenu: onPaneContextMenu, paneClickDistance: paneClickDistance, nodeClickDistance: nodeClickDistance, onSelectionContextMenu: onSelectionContextMenu, onSelectionStart: onSelectionStart, onSelectionEnd: onSelectionEnd, onReconnect: onReconnect, onReconnectStart: onReconnectStart, onReconnectEnd: onReconnectEnd, onEdgeContextMenu: onEdgeContextMenu, onEdgeDoubleClick: onEdgeDoubleClick, onEdgeMouseEnter: onEdgeMouseEnter, onEdgeMouseMove: onEdgeMouseMove, onEdgeMouseLeave: onEdgeMouseLeave, reconnectRadius: reconnectRadius, defaultMarkerColor: defaultMarkerColor, noDragClassName: noDragClassName, noWheelClassName: noWheelClassName, noPanClassName: noPanClassName, rfId: rfId, disableKeyboardA11y: disableKeyboardA11y, nodeExtent: nodeExtent, viewport: viewport, onViewportChange: onViewportChange }), jsx(StoreUpdater, { nodes: nodes, edges: edges, defaultNodes: defaultNodes, defaultEdges: defaultEdges, onConnect: onConnect, onConnectStart: onConnectStart, onConnectEnd: onConnectEnd, onClickConnectStart: onClickConnectStart, onClickConnectEnd: onClickConnectEnd, nodesDraggable: nodesDraggable, autoPanOnNodeFocus: autoPanOnNodeFocus, nodesConnectable: nodesConnectable, nodesFocusable: nodesFocusable, edgesFocusable: edgesFocusable, edgesReconnectable: edgesReconnectable, elementsSelectable: elementsSelectable, elevateNodesOnSelect: elevateNodesOnSelect, elevateEdgesOnSelect: elevateEdgesOnSelect, minZoom: minZoom, maxZoom: maxZoom, nodeExtent: nodeExtent, onNodesChange: onNodesChange, onEdgesChange: onEdgesChange, snapToGrid: snapToGrid, snapGrid: snapGrid, connectionMode: connectionMode, translateExtent: translateExtent, connectOnClick: connectOnClick, defaultEdgeOptions: defaultEdgeOptions, fitView: fitView, fitViewOptions: fitViewOptions, onNodesDelete: onNodesDelete, onEdgesDelete: onEdgesDelete, onDelete: onDelete, onNodeDragStart: onNodeDragStart, onNodeDrag: onNodeDrag, onNodeDragStop: onNodeDragStop, onSelectionDrag: onSelectionDrag, onSelectionDragStart: onSelectionDragStart, onSelectionDragStop: onSelectionDragStop, onMove: onMove, onMoveStart: onMoveStart, onMoveEnd: onMoveEnd, noPanClassName: noPanClassName, nodeOrigin: nodeOrigin, rfId: rfId, autoPanOnConnect: autoPanOnConnect, autoPanOnNodeDrag: autoPanOnNodeDrag, autoPanSpeed: autoPanSpeed, onError: onError, connectionRadius: connectionRadius, isValidConnection: isValidConnection, selectNodesOnDrag: selectNodesOnDrag, nodeDragThreshold: nodeDragThreshold, connectionDragThreshold: connectionDragThreshold, onBeforeDelete: onBeforeDelete, paneClickDistance: paneClickDistance, debug: debug, ariaLabelConfig: ariaLabelConfig }), jsx(SelectionListener, { onSelectionChange: onSelectionChange }), children, jsx(Attribution, { proOptions: proOptions, position: attributionPosition }), jsx(A11yDescriptions, { rfId: rfId, disableKeyboardA11y: disableKeyboardA11y })] }) }));\n}\n/**\n * The `<ReactFlow />` component is the heart of your React Flow application.\n * It renders your nodes and edges and handles user interaction\n *\n * @public\n *\n * @example\n * ```tsx\n *import { ReactFlow } from '@xyflow/react'\n *\n *export default function Flow() {\n *  return (<ReactFlow\n *    nodes={...}\n *    edges={...}\n *    onNodesChange={...}\n *    ...\n *  />);\n *}\n *```\n */\nvar index = fixedForwardRef(ReactFlow);\n\nconst selector$6 = (s) => s.domNode?.querySelector('.react-flow__edgelabel-renderer');\n/**\n * Edges are SVG-based. If you want to render more complex labels you can use the\n * `<EdgeLabelRenderer />` component to access a div based renderer. This component\n * is a portal that renders the label in a `<div />` that is positioned on top of\n * the edges. You can see an example usage of the component in the\n * [edge label renderer example](/examples/edges/edge-label-renderer).\n * @public\n *\n * @example\n * ```jsx\n * import React from 'react';\n * import { getBezierPath, EdgeLabelRenderer, BaseEdge } from '@xyflow/react';\n *\n * export function CustomEdge({ id, data, ...props }) {\n *   const [edgePath, labelX, labelY] = getBezierPath(props);\n *\n *   return (\n *     <>\n *       <BaseEdge id={id} path={edgePath} />\n *       <EdgeLabelRenderer>\n *         <div\n *           style={{\n *             position: 'absolute',\n *             transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,\n *             background: '#ffcc00',\n *             padding: 10,\n *         }}\n *           className=\"nodrag nopan\"\n *         >\n *          {data.label}\n *         </div>\n *       </EdgeLabelRenderer>\n *     </>\n *   );\n * };\n * ```\n *\n * @remarks The `<EdgeLabelRenderer />` has no pointer events by default. If you want to\n * add mouse interactions you need to set the style `pointerEvents: all` and add\n * the `nopan` class on the label or the element you want to interact with.\n */\nfunction EdgeLabelRenderer({ children }) {\n    const edgeLabelRenderer = useStore(selector$6);\n    if (!edgeLabelRenderer) {\n        return null;\n    }\n    return createPortal(children, edgeLabelRenderer);\n}\n\nconst selector$5 = (s) => s.domNode?.querySelector('.react-flow__viewport-portal');\n/**\n * The `<ViewportPortal />` component can be used to add components to the same viewport\n * of the flow where nodes and edges are rendered. This is useful when you want to render\n * your own components that are adhere to the same coordinate system as the nodes & edges\n * and are also affected by zooming and panning\n * @public\n * @example\n *\n * ```jsx\n *import React from 'react';\n *import { ViewportPortal } from '@xyflow/react';\n *\n *export default function () {\n *  return (\n *    <ViewportPortal>\n *      <div\n *        style={{ transform: 'translate(100px, 100px)', position: 'absolute' }}\n *      >\n *        This div is positioned at [100, 100] on the flow.\n *      </div>\n *    </ViewportPortal>\n *  );\n *}\n *```\n */\nfunction ViewportPortal({ children }) {\n    const viewPortalDiv = useStore(selector$5);\n    if (!viewPortalDiv) {\n        return null;\n    }\n    return createPortal(children, viewPortalDiv);\n}\n\n/**\n * When you programmatically add or remove handles to a node or update a node's\n * handle position, you need to let React Flow know about it using this hook. This\n * will update the internal dimensions of the node and properly reposition handles\n * on the canvas if necessary.\n *\n * @public\n * @returns Use this function to tell React Flow to update the internal state of one or more nodes\n * that you have changed programmatically.\n *\n * @example\n * ```jsx\n *import { useCallback, useState } from 'react';\n *import { Handle, useUpdateNodeInternals } from '@xyflow/react';\n *\n *export default function RandomHandleNode({ id }) {\n *  const updateNodeInternals = useUpdateNodeInternals();\n *  const [handleCount, setHandleCount] = useState(0);\n *  const randomizeHandleCount = useCallback(() => {\n *   setHandleCount(Math.floor(Math.random() * 10));\n *    updateNodeInternals(id);\n *  }, [id, updateNodeInternals]);\n *\n *  return (\n *    <>\n *      {Array.from({ length: handleCount }).map((_, index) => (\n *        <Handle\n *          key={index}\n *          type=\"target\"\n *          position=\"left\"\n *          id={`handle-${index}`}\n *        />\n *      ))}\n *\n *      <div>\n *        <button onClick={randomizeHandleCount}>Randomize handle count</button>\n *        <p>There are {handleCount} handles on this node.</p>\n *      </div>\n *    </>\n *  );\n *}\n *```\n * @remarks This hook can only be used in a component that is a child of a\n *{@link ReactFlowProvider} or a {@link ReactFlow} component.\n */\nfunction useUpdateNodeInternals() {\n    const store = useStoreApi();\n    return useCallback((id) => {\n        const { domNode, updateNodeInternals } = store.getState();\n        const updateIds = Array.isArray(id) ? id : [id];\n        const updates = new Map();\n        updateIds.forEach((updateId) => {\n            const nodeElement = domNode?.querySelector(`.react-flow__node[data-id=\"${updateId}\"]`);\n            if (nodeElement) {\n                updates.set(updateId, { id: updateId, nodeElement, force: true });\n            }\n        });\n        requestAnimationFrame(() => updateNodeInternals(updates, { triggerFitView: false }));\n    }, []);\n}\n\nconst nodesSelector = (state) => state.nodes;\n/**\n * This hook returns an array of the current nodes. Components that use this hook\n * will re-render **whenever any node changes**, including when a node is selected\n * or moved.\n *\n * @public\n * @returns An array of all nodes currently in the flow.\n *\n * @example\n * ```jsx\n *import { useNodes } from '@xyflow/react';\n *\n *export default function() {\n *  const nodes = useNodes();\n *\n *  return <div>There are currently {nodes.length} nodes!</div>;\n *}\n *```\n */\nfunction useNodes() {\n    const nodes = useStore(nodesSelector, shallow);\n    return nodes;\n}\n\nconst edgesSelector = (state) => state.edges;\n/**\n * This hook returns an array of the current edges. Components that use this hook\n * will re-render **whenever any edge changes**.\n *\n * @public\n * @returns An array of all edges currently in the flow.\n *\n * @example\n * ```tsx\n *import { useEdges } from '@xyflow/react';\n *\n *export default function () {\n *  const edges = useEdges();\n *\n *  return <div>There are currently {edges.length} edges!</div>;\n *}\n *```\n */\nfunction useEdges() {\n    const edges = useStore(edgesSelector, shallow);\n    return edges;\n}\n\nconst viewportSelector = (state) => ({\n    x: state.transform[0],\n    y: state.transform[1],\n    zoom: state.transform[2],\n});\n/**\n * The `useViewport` hook is a convenient way to read the current state of the\n * {@link Viewport} in a component. Components that use this hook\n * will re-render **whenever the viewport changes**.\n *\n * @public\n * @returns The current viewport.\n *\n * @example\n *\n *```jsx\n *import { useViewport } from '@xyflow/react';\n *\n *export default function ViewportDisplay() {\n *  const { x, y, zoom } = useViewport();\n *\n *  return (\n *    <div>\n *      <p>\n *        The viewport is currently at ({x}, {y}) and zoomed to {zoom}.\n *      </p>\n *    </div>\n *  );\n *}\n *```\n *\n * @remarks This hook can only be used in a component that is a child of a\n *{@link ReactFlowProvider} or a {@link ReactFlow} component.\n */\nfunction useViewport() {\n    const viewport = useStore(viewportSelector, shallow);\n    return viewport;\n}\n\n/**\n * This hook makes it easy to prototype a controlled flow where you manage the\n * state of nodes and edges outside the `ReactFlowInstance`. You can think of it\n * like React's `useState` hook with an additional helper callback.\n *\n * @public\n * @returns\n * - `nodes`: The current array of nodes. You might pass this directly to the `nodes` prop of your\n * `<ReactFlow />` component, or you may want to manipulate it first to perform some layouting,\n * for example.\n * - `setNodes`: A function that you can use to update the nodes. You can pass it a new array of\n * nodes or a callback that receives the current array of nodes and returns a new array of nodes.\n * This is the same as the second element of the tuple returned by React's `useState` hook.\n * - `onNodesChange`: A handy callback that can take an array of `NodeChanges` and update the nodes\n * state accordingly. You'll typically pass this directly to the `onNodesChange` prop of your\n * `<ReactFlow />` component.\n * @example\n *\n *```tsx\n *import { ReactFlow, useNodesState, useEdgesState } from '@xyflow/react';\n *\n *const initialNodes = [];\n *const initialEdges = [];\n *\n *export default function () {\n *  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);\n *  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);\n *\n *  return (\n *    <ReactFlow\n *      nodes={nodes}\n *      edges={edges}\n *      onNodesChange={onNodesChange}\n *      onEdgesChange={onEdgesChange}\n *    />\n *  );\n *}\n *```\n *\n * @remarks This hook was created to make prototyping easier and our documentation\n * examples clearer. Although it is OK to use this hook in production, in\n * practice you may want to use a more sophisticated state management solution\n * like Zustand {@link https://reactflow.dev/docs/guides/state-management/} instead.\n *\n */\nfunction useNodesState(initialNodes) {\n    const [nodes, setNodes] = useState(initialNodes);\n    const onNodesChange = useCallback((changes) => setNodes((nds) => applyNodeChanges(changes, nds)), []);\n    return [nodes, setNodes, onNodesChange];\n}\n/**\n * This hook makes it easy to prototype a controlled flow where you manage the\n * state of nodes and edges outside the `ReactFlowInstance`. You can think of it\n * like React's `useState` hook with an additional helper callback.\n *\n * @public\n * @returns\n * - `edges`: The current array of edges. You might pass this directly to the `edges` prop of your\n * `<ReactFlow />` component, or you may want to manipulate it first to perform some layouting,\n * for example.\n *\n * - `setEdges`: A function that you can use to update the edges. You can pass it a new array of\n * edges or a callback that receives the current array of edges and returns a new array of edges.\n * This is the same as the second element of the tuple returned by React's `useState` hook.\n *\n * - `onEdgesChange`: A handy callback that can take an array of `EdgeChanges` and update the edges\n * state accordingly. You'll typically pass this directly to the `onEdgesChange` prop of your\n * `<ReactFlow />` component.\n * @example\n *\n *```tsx\n *import { ReactFlow, useNodesState, useEdgesState } from '@xyflow/react';\n *\n *const initialNodes = [];\n *const initialEdges = [];\n *\n *export default function () {\n *  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);\n *  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);\n *\n *  return (\n *    <ReactFlow\n *      nodes={nodes}\n *      edges={edges}\n *      onNodesChange={onNodesChange}\n *      onEdgesChange={onEdgesChange}\n *    />\n *  );\n *}\n *```\n *\n * @remarks This hook was created to make prototyping easier and our documentation\n * examples clearer. Although it is OK to use this hook in production, in\n * practice you may want to use a more sophisticated state management solution\n * like Zustand {@link https://reactflow.dev/docs/guides/state-management/} instead.\n *\n */\nfunction useEdgesState(initialEdges) {\n    const [edges, setEdges] = useState(initialEdges);\n    const onEdgesChange = useCallback((changes) => setEdges((eds) => applyEdgeChanges(changes, eds)), []);\n    return [edges, setEdges, onEdgesChange];\n}\n\n/**\n * The `useOnViewportChange` hook lets you listen for changes to the viewport such\n * as panning and zooming. You can provide a callback for each phase of a viewport\n * change: `onStart`, `onChange`, and `onEnd`.\n *\n * @public\n * @example\n * ```jsx\n *import { useCallback } from 'react';\n *import { useOnViewportChange } from '@xyflow/react';\n *\n *function ViewportChangeLogger() {\n *  useOnViewportChange({\n *    onStart: (viewport: Viewport) => console.log('start', viewport),\n *    onChange: (viewport: Viewport) => console.log('change', viewport),\n *    onEnd: (viewport: Viewport) => console.log('end', viewport),\n *  });\n *\n *  return null;\n *}\n *```\n */\nfunction useOnViewportChange({ onStart, onChange, onEnd }) {\n    const store = useStoreApi();\n    useEffect(() => {\n        store.setState({ onViewportChangeStart: onStart });\n    }, [onStart]);\n    useEffect(() => {\n        store.setState({ onViewportChange: onChange });\n    }, [onChange]);\n    useEffect(() => {\n        store.setState({ onViewportChangeEnd: onEnd });\n    }, [onEnd]);\n}\n\n/**\n * This hook lets you listen for changes to both node and edge selection. As the\n *name implies, the callback you provide will be called whenever the selection of\n *_either_ nodes or edges changes.\n *\n * @public\n * @example\n * ```jsx\n *import { useState } from 'react';\n *import { ReactFlow, useOnSelectionChange } from '@xyflow/react';\n *\n *function SelectionDisplay() {\n *  const [selectedNodes, setSelectedNodes] = useState([]);\n *  const [selectedEdges, setSelectedEdges] = useState([]);\n *\n *  // the passed handler has to be memoized, otherwise the hook will not work correctly\n *  const onChange = useCallback(({ nodes, edges }) => {\n *    setSelectedNodes(nodes.map((node) => node.id));\n *    setSelectedEdges(edges.map((edge) => edge.id));\n *  }, []);\n *\n *  useOnSelectionChange({\n *    onChange,\n *  });\n *\n *  return (\n *    <div>\n *      <p>Selected nodes: {selectedNodes.join(', ')}</p>\n *      <p>Selected edges: {selectedEdges.join(', ')}</p>\n *    </div>\n *  );\n *}\n *```\n *\n * @remarks You need to memoize the passed `onChange` handler, otherwise the hook will not work correctly.\n */\nfunction useOnSelectionChange({ onChange, }) {\n    const store = useStoreApi();\n    useEffect(() => {\n        const nextOnSelectionChangeHandlers = [...store.getState().onSelectionChangeHandlers, onChange];\n        store.setState({ onSelectionChangeHandlers: nextOnSelectionChangeHandlers });\n        return () => {\n            const nextHandlers = store.getState().onSelectionChangeHandlers.filter((fn) => fn !== onChange);\n            store.setState({ onSelectionChangeHandlers: nextHandlers });\n        };\n    }, [onChange]);\n}\n\nconst selector$4 = (options) => (s) => {\n    if (!options.includeHiddenNodes) {\n        return s.nodesInitialized;\n    }\n    if (s.nodeLookup.size === 0) {\n        return false;\n    }\n    for (const [, { internals }] of s.nodeLookup) {\n        if (internals.handleBounds === undefined || !nodeHasDimensions(internals.userNode)) {\n            return false;\n        }\n    }\n    return true;\n};\n/**\n * This hook tells you whether all the nodes in a flow have been measured and given\n *a width and height. When you add a node to the flow, this hook will return\n *`false` and then `true` again once the node has been measured.\n *\n * @public\n * @returns Whether or not the nodes have been initialized by the `<ReactFlow />` component and\n * given a width and height.\n *\n * @example\n * ```jsx\n *import { useReactFlow, useNodesInitialized } from '@xyflow/react';\n *import { useEffect, useState } from 'react';\n *\n *const options = {\n *  includeHiddenNodes: false,\n *};\n *\n *export default function useLayout() {\n *  const { getNodes } = useReactFlow();\n *  const nodesInitialized = useNodesInitialized(options);\n *  const [layoutedNodes, setLayoutedNodes] = useState(getNodes());\n *\n *  useEffect(() => {\n *    if (nodesInitialized) {\n *      setLayoutedNodes(yourLayoutingFunction(getNodes()));\n *    }\n *  }, [nodesInitialized]);\n *\n *  return layoutedNodes;\n *}\n *```\n */\nfunction useNodesInitialized(options = {\n    includeHiddenNodes: false,\n}) {\n    const initialized = useStore(selector$4(options));\n    return initialized;\n}\n\n/**\n * Hook to check if a <Handle /> is connected to another <Handle /> and get the connections.\n *\n * @public\n * @deprecated Use `useNodeConnections` instead.\n * @returns An array with handle connections.\n */\nfunction useHandleConnections({ type, id, nodeId, onConnect, onDisconnect, }) {\n    console.warn('[DEPRECATED] `useHandleConnections` is deprecated. Instead use `useNodeConnections` https://reactflow.dev/api-reference/hooks/useNodeConnections');\n    const _nodeId = useNodeId();\n    const currentNodeId = nodeId ?? _nodeId;\n    const prevConnections = useRef(null);\n    const connections = useStore((state) => state.connectionLookup.get(`${currentNodeId}-${type}${id ? `-${id}` : ''}`), areConnectionMapsEqual);\n    useEffect(() => {\n        // @todo dicuss if onConnect/onDisconnect should be called when the component mounts/unmounts\n        if (prevConnections.current && prevConnections.current !== connections) {\n            const _connections = connections ?? new Map();\n            handleConnectionChange(prevConnections.current, _connections, onDisconnect);\n            handleConnectionChange(_connections, prevConnections.current, onConnect);\n        }\n        prevConnections.current = connections ?? new Map();\n    }, [connections, onConnect, onDisconnect]);\n    return useMemo(() => Array.from(connections?.values() ?? []), [connections]);\n}\n\nconst error014 = errorMessages['error014']();\n/**\n * This hook returns an array of connections on a specific node, handle type ('source', 'target') or handle ID.\n *\n * @public\n * @returns An array with connections.\n *\n * @example\n * ```jsx\n *import { useNodeConnections } from '@xyflow/react';\n *\n *export default function () {\n *  const connections = useNodeConnections({\n *    handleType: 'target',\n *    handleId: 'my-handle',\n *  });\n *\n *  return (\n *    <div>There are currently {connections.length} incoming connections!</div>\n *  );\n *}\n *```\n */\nfunction useNodeConnections({ id, handleType, handleId, onConnect, onDisconnect, } = {}) {\n    const nodeId = useNodeId();\n    const currentNodeId = id ?? nodeId;\n    if (!currentNodeId) {\n        throw new Error(error014);\n    }\n    const prevConnections = useRef(null);\n    const connections = useStore((state) => state.connectionLookup.get(`${currentNodeId}${handleType ? (handleId ? `-${handleType}-${handleId}` : `-${handleType}`) : ''}`), areConnectionMapsEqual);\n    useEffect(() => {\n        // @todo discuss if onConnect/onDisconnect should be called when the component mounts/unmounts\n        if (prevConnections.current && prevConnections.current !== connections) {\n            const _connections = connections ?? new Map();\n            handleConnectionChange(prevConnections.current, _connections, onDisconnect);\n            handleConnectionChange(_connections, prevConnections.current, onConnect);\n        }\n        prevConnections.current = connections ?? new Map();\n    }, [connections, onConnect, onDisconnect]);\n    return useMemo(() => Array.from(connections?.values() ?? []), [connections]);\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction useNodesData(nodeIds) {\n    const nodesData = useStore(useCallback((s) => {\n        const data = [];\n        const isArrayOfIds = Array.isArray(nodeIds);\n        const _nodeIds = isArrayOfIds ? nodeIds : [nodeIds];\n        for (const nodeId of _nodeIds) {\n            const node = s.nodeLookup.get(nodeId);\n            if (node) {\n                data.push({\n                    id: node.id,\n                    type: node.type,\n                    data: node.data,\n                });\n            }\n        }\n        return isArrayOfIds ? data : data[0] ?? null;\n    }, [nodeIds]), shallowNodeData);\n    return nodesData;\n}\n\n/**\n * This hook returns the internal representation of a specific node.\n * Components that use this hook will re-render **whenever the node changes**,\n * including when a node is selected or moved.\n *\n * @public\n * @param id - The ID of a node you want to observe.\n * @returns The `InternalNode` object for the node with the given ID.\n *\n * @example\n * ```tsx\n *import { useInternalNode } from '@xyflow/react';\n *\n *export default function () {\n *  const internalNode = useInternalNode('node-1');\n *  const absolutePosition = internalNode.internals.positionAbsolute;\n *\n *  return (\n *    <div>\n *      The absolute position of the node is at:\n *      <p>x: {absolutePosition.x}</p>\n *      <p>y: {absolutePosition.y}</p>\n *    </div>\n *  );\n *}\n *```\n */\nfunction useInternalNode(id) {\n    const node = useStore(useCallback((s) => s.nodeLookup.get(id), [id]), shallow);\n    return node;\n}\n\nfunction LinePattern({ dimensions, lineWidth, variant, className }) {\n    return (jsx(\"path\", { strokeWidth: lineWidth, d: `M${dimensions[0] / 2} 0 V${dimensions[1]} M0 ${dimensions[1] / 2} H${dimensions[0]}`, className: cc(['react-flow__background-pattern', variant, className]) }));\n}\nfunction DotPattern({ radius, className }) {\n    return (jsx(\"circle\", { cx: radius, cy: radius, r: radius, className: cc(['react-flow__background-pattern', 'dots', className]) }));\n}\n\n/**\n * The three variants are exported as an enum for convenience. You can either import\n * the enum and use it like `BackgroundVariant.Lines` or you can use the raw string\n * value directly.\n * @public\n */\nvar BackgroundVariant;\n(function (BackgroundVariant) {\n    BackgroundVariant[\"Lines\"] = \"lines\";\n    BackgroundVariant[\"Dots\"] = \"dots\";\n    BackgroundVariant[\"Cross\"] = \"cross\";\n})(BackgroundVariant || (BackgroundVariant = {}));\n\nconst defaultSize = {\n    [BackgroundVariant.Dots]: 1,\n    [BackgroundVariant.Lines]: 1,\n    [BackgroundVariant.Cross]: 6,\n};\nconst selector$3 = (s) => ({ transform: s.transform, patternId: `pattern-${s.rfId}` });\nfunction BackgroundComponent({ id, variant = BackgroundVariant.Dots, \n// only used for dots and cross\ngap = 20, \n// only used for lines and cross\nsize, lineWidth = 1, offset = 0, color, bgColor, style, className, patternClassName, }) {\n    const ref = useRef(null);\n    const { transform, patternId } = useStore(selector$3, shallow);\n    const patternSize = size || defaultSize[variant];\n    const isDots = variant === BackgroundVariant.Dots;\n    const isCross = variant === BackgroundVariant.Cross;\n    const gapXY = Array.isArray(gap) ? gap : [gap, gap];\n    const scaledGap = [gapXY[0] * transform[2] || 1, gapXY[1] * transform[2] || 1];\n    const scaledSize = patternSize * transform[2];\n    const offsetXY = Array.isArray(offset) ? offset : [offset, offset];\n    const patternDimensions = isCross ? [scaledSize, scaledSize] : scaledGap;\n    const scaledOffset = [\n        offsetXY[0] * transform[2] || 1 + patternDimensions[0] / 2,\n        offsetXY[1] * transform[2] || 1 + patternDimensions[1] / 2,\n    ];\n    const _patternId = `${patternId}${id ? id : ''}`;\n    return (jsxs(\"svg\", { className: cc(['react-flow__background', className]), style: {\n            ...style,\n            ...containerStyle,\n            '--xy-background-color-props': bgColor,\n            '--xy-background-pattern-color-props': color,\n        }, ref: ref, \"data-testid\": \"rf__background\", children: [jsx(\"pattern\", { id: _patternId, x: transform[0] % scaledGap[0], y: transform[1] % scaledGap[1], width: scaledGap[0], height: scaledGap[1], patternUnits: \"userSpaceOnUse\", patternTransform: `translate(-${scaledOffset[0]},-${scaledOffset[1]})`, children: isDots ? (jsx(DotPattern, { radius: scaledSize / 2, className: patternClassName })) : (jsx(LinePattern, { dimensions: patternDimensions, lineWidth: lineWidth, variant: variant, className: patternClassName })) }), jsx(\"rect\", { x: \"0\", y: \"0\", width: \"100%\", height: \"100%\", fill: `url(#${_patternId})` })] }));\n}\nBackgroundComponent.displayName = 'Background';\n/**\n * The `<Background />` component makes it convenient to render different types of backgrounds common in node-based UIs. It comes with three variants: lines, dots and cross.\n *\n * @example\n *\n * A simple example of how to use the Background component.\n *\n * ```tsx\n * import { useState } from 'react';\n * import { ReactFlow, Background, BackgroundVariant } from '@xyflow/react';\n *\n * export default function Flow() {\n *   return (\n *     <ReactFlow defaultNodes={[...]} defaultEdges={[...]}>\n *       <Background color=\"#ccc\" variant={BackgroundVariant.Dots} />\n *     </ReactFlow>\n *   );\n * }\n * ```\n *\n * @example\n *\n * In this example you can see how to combine multiple backgrounds\n *\n * ```tsx\n * import { ReactFlow, Background, BackgroundVariant } from '@xyflow/react';\n * import '@xyflow/react/dist/style.css';\n *\n * export default function Flow() {\n *   return (\n *     <ReactFlow defaultNodes={[...]} defaultEdges={[...]}>\n *       <Background\n *         id=\"1\"\n *         gap={10}\n *         color=\"#f1f1f1\"\n *         variant={BackgroundVariant.Lines}\n *       />\n *       <Background\n *         id=\"2\"\n *         gap={100}\n *         color=\"#ccc\"\n *         variant={BackgroundVariant.Lines}\n *       />\n *     </ReactFlow>\n *   );\n * }\n * ```\n *\n * @remarks\n *\n * When combining multiple <Background /> components it’s important to give each of them a unique id prop!\n *\n */\nconst Background = memo(BackgroundComponent);\n\nfunction PlusIcon() {\n    return (jsx(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", viewBox: \"0 0 32 32\", children: jsx(\"path\", { d: \"M32 18.133H18.133V32h-4.266V18.133H0v-4.266h13.867V0h4.266v13.867H32z\" }) }));\n}\n\nfunction MinusIcon() {\n    return (jsx(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", viewBox: \"0 0 32 5\", children: jsx(\"path\", { d: \"M0 0h32v4.2H0z\" }) }));\n}\n\nfunction FitViewIcon() {\n    return (jsx(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", viewBox: \"0 0 32 30\", children: jsx(\"path\", { d: \"M3.692 4.63c0-.53.4-.938.939-.938h5.215V0H4.708C2.13 0 0 2.054 0 4.63v5.216h3.692V4.631zM27.354 0h-5.2v3.692h5.17c.53 0 .984.4.984.939v5.215H32V4.631A4.624 4.624 0 0027.354 0zm.954 24.83c0 .532-.4.94-.939.94h-5.215v3.768h5.215c2.577 0 4.631-2.13 4.631-4.707v-5.139h-3.692v5.139zm-23.677.94c-.531 0-.939-.4-.939-.94v-5.138H0v5.139c0 2.577 2.13 4.707 4.708 4.707h5.138V25.77H4.631z\" }) }));\n}\n\nfunction LockIcon() {\n    return (jsx(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", viewBox: \"0 0 25 32\", children: jsx(\"path\", { d: \"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0 8 0 4.571 3.429 4.571 7.619v3.048H3.048A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047zm4.724-13.866H7.467V7.619c0-2.59 2.133-4.724 4.723-4.724 2.591 0 4.724 2.133 4.724 4.724v3.048z\" }) }));\n}\n\nfunction UnlockIcon() {\n    return (jsx(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", viewBox: \"0 0 25 32\", children: jsx(\"path\", { d: \"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0c-4.114 1.828-1.37 2.133.305 2.438 1.676.305 4.42 2.59 4.42 5.181v3.048H3.047A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047z\" }) }));\n}\n\n/**\n * You can add buttons to the control panel by using the `<ControlButton />` component\n * and pass it as a child to the [`<Controls />`](/api-reference/components/controls) component.\n *\n * @public\n * @example\n *```jsx\n *import { MagicWand } from '@radix-ui/react-icons'\n *import { ReactFlow, Controls, ControlButton } from '@xyflow/react'\n *\n *export default function Flow() {\n *  return (\n *    <ReactFlow nodes={[...]} edges={[...]}>\n *      <Controls>\n *        <ControlButton onClick={() => alert('Something magical just happened. ✨')}>\n *          <MagicWand />\n *        </ControlButton>\n *      </Controls>\n *    </ReactFlow>\n *  )\n *}\n *```\n */\nfunction ControlButton({ children, className, ...rest }) {\n    return (jsx(\"button\", { type: \"button\", className: cc(['react-flow__controls-button', className]), ...rest, children: children }));\n}\n\nconst selector$2 = (s) => ({\n    isInteractive: s.nodesDraggable || s.nodesConnectable || s.elementsSelectable,\n    minZoomReached: s.transform[2] <= s.minZoom,\n    maxZoomReached: s.transform[2] >= s.maxZoom,\n    ariaLabelConfig: s.ariaLabelConfig,\n});\nfunction ControlsComponent({ style, showZoom = true, showFitView = true, showInteractive = true, fitViewOptions, onZoomIn, onZoomOut, onFitView, onInteractiveChange, className, children, position = 'bottom-left', orientation = 'vertical', 'aria-label': ariaLabel, }) {\n    const store = useStoreApi();\n    const { isInteractive, minZoomReached, maxZoomReached, ariaLabelConfig } = useStore(selector$2, shallow);\n    const { zoomIn, zoomOut, fitView } = useReactFlow();\n    const onZoomInHandler = () => {\n        zoomIn();\n        onZoomIn?.();\n    };\n    const onZoomOutHandler = () => {\n        zoomOut();\n        onZoomOut?.();\n    };\n    const onFitViewHandler = () => {\n        fitView(fitViewOptions);\n        onFitView?.();\n    };\n    const onToggleInteractivity = () => {\n        store.setState({\n            nodesDraggable: !isInteractive,\n            nodesConnectable: !isInteractive,\n            elementsSelectable: !isInteractive,\n        });\n        onInteractiveChange?.(!isInteractive);\n    };\n    const orientationClass = orientation === 'horizontal' ? 'horizontal' : 'vertical';\n    return (jsxs(Panel, { className: cc(['react-flow__controls', orientationClass, className]), position: position, style: style, \"data-testid\": \"rf__controls\", \"aria-label\": ariaLabel ?? ariaLabelConfig['controls.ariaLabel'], children: [showZoom && (jsxs(Fragment, { children: [jsx(ControlButton, { onClick: onZoomInHandler, className: \"react-flow__controls-zoomin\", title: ariaLabelConfig['controls.zoomIn.ariaLabel'], \"aria-label\": ariaLabelConfig['controls.zoomIn.ariaLabel'], disabled: maxZoomReached, children: jsx(PlusIcon, {}) }), jsx(ControlButton, { onClick: onZoomOutHandler, className: \"react-flow__controls-zoomout\", title: ariaLabelConfig['controls.zoomOut.ariaLabel'], \"aria-label\": ariaLabelConfig['controls.zoomOut.ariaLabel'], disabled: minZoomReached, children: jsx(MinusIcon, {}) })] })), showFitView && (jsx(ControlButton, { className: \"react-flow__controls-fitview\", onClick: onFitViewHandler, title: ariaLabelConfig['controls.fitView.ariaLabel'], \"aria-label\": ariaLabelConfig['controls.fitView.ariaLabel'], children: jsx(FitViewIcon, {}) })), showInteractive && (jsx(ControlButton, { className: \"react-flow__controls-interactive\", onClick: onToggleInteractivity, title: ariaLabelConfig['controls.interactive.ariaLabel'], \"aria-label\": ariaLabelConfig['controls.interactive.ariaLabel'], children: isInteractive ? jsx(UnlockIcon, {}) : jsx(LockIcon, {}) })), children] }));\n}\nControlsComponent.displayName = 'Controls';\n/**\n * The `<Controls />` component renders a small panel that contains convenient\n * buttons to zoom in, zoom out, fit the view, and lock the viewport.\n *\n * @public\n * @example\n *```tsx\n *import { ReactFlow, Controls } from '@xyflow/react'\n *\n *export default function Flow() {\n *  return (\n *    <ReactFlow nodes={[...]} edges={[...]}>\n *      <Controls />\n *    </ReactFlow>\n *  )\n *}\n *```\n *\n * @remarks To extend or customise the controls, you can use the [`<ControlButton />`](/api-reference/components/control-button) component\n *\n */\nconst Controls = memo(ControlsComponent);\n\nfunction MiniMapNodeComponent({ id, x, y, width, height, style, color, strokeColor, strokeWidth, className, borderRadius, shapeRendering, selected, onClick, }) {\n    const { background, backgroundColor } = style || {};\n    const fill = (color || background || backgroundColor);\n    return (jsx(\"rect\", { className: cc(['react-flow__minimap-node', { selected }, className]), x: x, y: y, rx: borderRadius, ry: borderRadius, width: width, height: height, style: {\n            fill,\n            stroke: strokeColor,\n            strokeWidth,\n        }, shapeRendering: shapeRendering, onClick: onClick ? (event) => onClick(event, id) : undefined }));\n}\nconst MiniMapNode = memo(MiniMapNodeComponent);\n\nconst selectorNodeIds = (s) => s.nodes.map((node) => node.id);\nconst getAttrFunction = (func) => func instanceof Function ? func : () => func;\nfunction MiniMapNodes({ nodeStrokeColor, nodeColor, nodeClassName = '', nodeBorderRadius = 5, nodeStrokeWidth, \n/*\n * We need to rename the prop to be `CapitalCase` so that JSX will render it as\n * a component properly.\n */\nnodeComponent: NodeComponent = MiniMapNode, onClick, }) {\n    const nodeIds = useStore(selectorNodeIds, shallow);\n    const nodeColorFunc = getAttrFunction(nodeColor);\n    const nodeStrokeColorFunc = getAttrFunction(nodeStrokeColor);\n    const nodeClassNameFunc = getAttrFunction(nodeClassName);\n    const shapeRendering = typeof window === 'undefined' || !!window.chrome ? 'crispEdges' : 'geometricPrecision';\n    return (jsx(Fragment, { children: nodeIds.map((nodeId) => (\n        /*\n         * The split of responsibilities between MiniMapNodes and\n         * NodeComponentWrapper may appear weird. However, it’s designed to\n         * minimize the cost of updates when individual nodes change.\n         *\n         * For more details, see a similar commit in `NodeRenderer/index.tsx`.\n         */\n        jsx(NodeComponentWrapper, { id: nodeId, nodeColorFunc: nodeColorFunc, nodeStrokeColorFunc: nodeStrokeColorFunc, nodeClassNameFunc: nodeClassNameFunc, nodeBorderRadius: nodeBorderRadius, nodeStrokeWidth: nodeStrokeWidth, NodeComponent: NodeComponent, onClick: onClick, shapeRendering: shapeRendering }, nodeId))) }));\n}\nfunction NodeComponentWrapperInner({ id, nodeColorFunc, nodeStrokeColorFunc, nodeClassNameFunc, nodeBorderRadius, nodeStrokeWidth, shapeRendering, NodeComponent, onClick, }) {\n    const { node, x, y, width, height } = useStore((s) => {\n        const { internals } = s.nodeLookup.get(id);\n        const node = internals.userNode;\n        const { x, y } = internals.positionAbsolute;\n        const { width, height } = getNodeDimensions(node);\n        return {\n            node,\n            x,\n            y,\n            width,\n            height,\n        };\n    }, shallow);\n    if (!node || node.hidden || !nodeHasDimensions(node)) {\n        return null;\n    }\n    return (jsx(NodeComponent, { x: x, y: y, width: width, height: height, style: node.style, selected: !!node.selected, className: nodeClassNameFunc(node), color: nodeColorFunc(node), borderRadius: nodeBorderRadius, strokeColor: nodeStrokeColorFunc(node), strokeWidth: nodeStrokeWidth, shapeRendering: shapeRendering, onClick: onClick, id: node.id }));\n}\nconst NodeComponentWrapper = memo(NodeComponentWrapperInner);\nvar MiniMapNodes$1 = memo(MiniMapNodes);\n\nconst defaultWidth = 200;\nconst defaultHeight = 150;\nconst filterHidden = (node) => !node.hidden;\nconst selector$1 = (s) => {\n    const viewBB = {\n        x: -s.transform[0] / s.transform[2],\n        y: -s.transform[1] / s.transform[2],\n        width: s.width / s.transform[2],\n        height: s.height / s.transform[2],\n    };\n    return {\n        viewBB,\n        boundingRect: s.nodeLookup.size > 0\n            ? getBoundsOfRects(getInternalNodesBounds(s.nodeLookup, { filter: filterHidden }), viewBB)\n            : viewBB,\n        rfId: s.rfId,\n        panZoom: s.panZoom,\n        translateExtent: s.translateExtent,\n        flowWidth: s.width,\n        flowHeight: s.height,\n        ariaLabelConfig: s.ariaLabelConfig,\n    };\n};\nconst ARIA_LABEL_KEY = 'react-flow__minimap-desc';\nfunction MiniMapComponent({ style, className, nodeStrokeColor, nodeColor, nodeClassName = '', nodeBorderRadius = 5, nodeStrokeWidth, \n/*\n * We need to rename the prop to be `CapitalCase` so that JSX will render it as\n * a component properly.\n */\nnodeComponent, bgColor, maskColor, maskStrokeColor, maskStrokeWidth, position = 'bottom-right', onClick, onNodeClick, pannable = false, zoomable = false, ariaLabel, inversePan, zoomStep = 10, offsetScale = 5, }) {\n    const store = useStoreApi();\n    const svg = useRef(null);\n    const { boundingRect, viewBB, rfId, panZoom, translateExtent, flowWidth, flowHeight, ariaLabelConfig } = useStore(selector$1, shallow);\n    const elementWidth = style?.width ?? defaultWidth;\n    const elementHeight = style?.height ?? defaultHeight;\n    const scaledWidth = boundingRect.width / elementWidth;\n    const scaledHeight = boundingRect.height / elementHeight;\n    const viewScale = Math.max(scaledWidth, scaledHeight);\n    const viewWidth = viewScale * elementWidth;\n    const viewHeight = viewScale * elementHeight;\n    const offset = offsetScale * viewScale;\n    const x = boundingRect.x - (viewWidth - boundingRect.width) / 2 - offset;\n    const y = boundingRect.y - (viewHeight - boundingRect.height) / 2 - offset;\n    const width = viewWidth + offset * 2;\n    const height = viewHeight + offset * 2;\n    const labelledBy = `${ARIA_LABEL_KEY}-${rfId}`;\n    const viewScaleRef = useRef(0);\n    const minimapInstance = useRef();\n    viewScaleRef.current = viewScale;\n    useEffect(() => {\n        if (svg.current && panZoom) {\n            minimapInstance.current = XYMinimap({\n                domNode: svg.current,\n                panZoom,\n                getTransform: () => store.getState().transform,\n                getViewScale: () => viewScaleRef.current,\n            });\n            return () => {\n                minimapInstance.current?.destroy();\n            };\n        }\n    }, [panZoom]);\n    useEffect(() => {\n        minimapInstance.current?.update({\n            translateExtent,\n            width: flowWidth,\n            height: flowHeight,\n            inversePan,\n            pannable,\n            zoomStep,\n            zoomable,\n        });\n    }, [pannable, zoomable, inversePan, zoomStep, translateExtent, flowWidth, flowHeight]);\n    const onSvgClick = onClick\n        ? (event) => {\n            const [x, y] = minimapInstance.current?.pointer(event) || [0, 0];\n            onClick(event, { x, y });\n        }\n        : undefined;\n    const onSvgNodeClick = onNodeClick\n        ? useCallback((event, nodeId) => {\n            const node = store.getState().nodeLookup.get(nodeId).internals.userNode;\n            onNodeClick(event, node);\n        }, [])\n        : undefined;\n    const _ariaLabel = ariaLabel ?? ariaLabelConfig['minimap.ariaLabel'];\n    return (jsx(Panel, { position: position, style: {\n            ...style,\n            '--xy-minimap-background-color-props': typeof bgColor === 'string' ? bgColor : undefined,\n            '--xy-minimap-mask-background-color-props': typeof maskColor === 'string' ? maskColor : undefined,\n            '--xy-minimap-mask-stroke-color-props': typeof maskStrokeColor === 'string' ? maskStrokeColor : undefined,\n            '--xy-minimap-mask-stroke-width-props': typeof maskStrokeWidth === 'number' ? maskStrokeWidth * viewScale : undefined,\n            '--xy-minimap-node-background-color-props': typeof nodeColor === 'string' ? nodeColor : undefined,\n            '--xy-minimap-node-stroke-color-props': typeof nodeStrokeColor === 'string' ? nodeStrokeColor : undefined,\n            '--xy-minimap-node-stroke-width-props': typeof nodeStrokeWidth === 'number' ? nodeStrokeWidth : undefined,\n        }, className: cc(['react-flow__minimap', className]), \"data-testid\": \"rf__minimap\", children: jsxs(\"svg\", { width: elementWidth, height: elementHeight, viewBox: `${x} ${y} ${width} ${height}`, className: \"react-flow__minimap-svg\", role: \"img\", \"aria-labelledby\": labelledBy, ref: svg, onClick: onSvgClick, children: [_ariaLabel && jsx(\"title\", { id: labelledBy, children: _ariaLabel }), jsx(MiniMapNodes$1, { onClick: onSvgNodeClick, nodeColor: nodeColor, nodeStrokeColor: nodeStrokeColor, nodeBorderRadius: nodeBorderRadius, nodeClassName: nodeClassName, nodeStrokeWidth: nodeStrokeWidth, nodeComponent: nodeComponent }), jsx(\"path\", { className: \"react-flow__minimap-mask\", d: `M${x - offset},${y - offset}h${width + offset * 2}v${height + offset * 2}h${-width - offset * 2}z\n        M${viewBB.x},${viewBB.y}h${viewBB.width}v${viewBB.height}h${-viewBB.width}z`, fillRule: \"evenodd\", pointerEvents: \"none\" })] }) }));\n}\nMiniMapComponent.displayName = 'MiniMap';\n/**\n * The `<MiniMap />` component can be used to render an overview of your flow. It\n * renders each node as an SVG element and visualizes where the current viewport is\n * in relation to the rest of the flow.\n *\n * @public\n * @example\n *\n * ```jsx\n *import { ReactFlow, MiniMap } from '@xyflow/react';\n *\n *export default function Flow() {\n *  return (\n *    <ReactFlow nodes={[...]]} edges={[...]]}>\n *      <MiniMap nodeStrokeWidth={3} />\n *    </ReactFlow>\n *  );\n *}\n *```\n */\nconst MiniMap = memo(MiniMapComponent);\n\nconst scaleSelector = (calculateScale) => (store) => calculateScale ? `${Math.max(1 / store.transform[2], 1)}` : undefined;\nconst defaultPositions = {\n    [ResizeControlVariant.Line]: 'right',\n    [ResizeControlVariant.Handle]: 'bottom-right',\n};\nfunction ResizeControl({ nodeId, position, variant = ResizeControlVariant.Handle, className, style = undefined, children, color, minWidth = 10, minHeight = 10, maxWidth = Number.MAX_VALUE, maxHeight = Number.MAX_VALUE, keepAspectRatio = false, resizeDirection, autoScale = true, shouldResize, onResizeStart, onResize, onResizeEnd, }) {\n    const contextNodeId = useNodeId();\n    const id = typeof nodeId === 'string' ? nodeId : contextNodeId;\n    const store = useStoreApi();\n    const resizeControlRef = useRef(null);\n    const isHandleControl = variant === ResizeControlVariant.Handle;\n    const scale = useStore(useCallback(scaleSelector(isHandleControl && autoScale), [isHandleControl, autoScale]), shallow);\n    const resizer = useRef(null);\n    const controlPosition = position ?? defaultPositions[variant];\n    useEffect(() => {\n        if (!resizeControlRef.current || !id) {\n            return;\n        }\n        if (!resizer.current) {\n            resizer.current = XYResizer({\n                domNode: resizeControlRef.current,\n                nodeId: id,\n                getStoreItems: () => {\n                    const { nodeLookup, transform, snapGrid, snapToGrid, nodeOrigin, domNode } = store.getState();\n                    return {\n                        nodeLookup,\n                        transform,\n                        snapGrid,\n                        snapToGrid,\n                        nodeOrigin,\n                        paneDomNode: domNode,\n                    };\n                },\n                onChange: (change, childChanges) => {\n                    const { triggerNodeChanges, nodeLookup, parentLookup, nodeOrigin } = store.getState();\n                    const changes = [];\n                    const nextPosition = { x: change.x, y: change.y };\n                    const node = nodeLookup.get(id);\n                    if (node && node.expandParent && node.parentId) {\n                        const origin = node.origin ?? nodeOrigin;\n                        const width = change.width ?? node.measured.width ?? 0;\n                        const height = change.height ?? node.measured.height ?? 0;\n                        const child = {\n                            id: node.id,\n                            parentId: node.parentId,\n                            rect: {\n                                width,\n                                height,\n                                ...evaluateAbsolutePosition({\n                                    x: change.x ?? node.position.x,\n                                    y: change.y ?? node.position.y,\n                                }, { width, height }, node.parentId, nodeLookup, origin),\n                            },\n                        };\n                        const parentExpandChanges = handleExpandParent([child], nodeLookup, parentLookup, nodeOrigin);\n                        changes.push(...parentExpandChanges);\n                        /*\n                         * when the parent was expanded by the child node, its position will be clamped at\n                         * 0,0 when node origin is 0,0 and to width, height if it's 1,1\n                         */\n                        nextPosition.x = change.x ? Math.max(origin[0] * width, change.x) : undefined;\n                        nextPosition.y = change.y ? Math.max(origin[1] * height, change.y) : undefined;\n                    }\n                    if (nextPosition.x !== undefined && nextPosition.y !== undefined) {\n                        const positionChange = {\n                            id,\n                            type: 'position',\n                            position: { ...nextPosition },\n                        };\n                        changes.push(positionChange);\n                    }\n                    if (change.width !== undefined && change.height !== undefined) {\n                        const setAttributes = !resizeDirection ? true : resizeDirection === 'horizontal' ? 'width' : 'height';\n                        const dimensionChange = {\n                            id,\n                            type: 'dimensions',\n                            resizing: true,\n                            setAttributes,\n                            dimensions: {\n                                width: change.width,\n                                height: change.height,\n                            },\n                        };\n                        changes.push(dimensionChange);\n                    }\n                    for (const childChange of childChanges) {\n                        const positionChange = {\n                            ...childChange,\n                            type: 'position',\n                        };\n                        changes.push(positionChange);\n                    }\n                    triggerNodeChanges(changes);\n                },\n                onEnd: ({ width, height }) => {\n                    const dimensionChange = {\n                        id: id,\n                        type: 'dimensions',\n                        resizing: false,\n                        dimensions: {\n                            width,\n                            height,\n                        },\n                    };\n                    store.getState().triggerNodeChanges([dimensionChange]);\n                },\n            });\n        }\n        resizer.current.update({\n            controlPosition,\n            boundaries: {\n                minWidth,\n                minHeight,\n                maxWidth,\n                maxHeight,\n            },\n            keepAspectRatio,\n            resizeDirection,\n            onResizeStart,\n            onResize,\n            onResizeEnd,\n            shouldResize,\n        });\n        return () => {\n            resizer.current?.destroy();\n        };\n    }, [\n        controlPosition,\n        minWidth,\n        minHeight,\n        maxWidth,\n        maxHeight,\n        keepAspectRatio,\n        onResizeStart,\n        onResize,\n        onResizeEnd,\n        shouldResize,\n    ]);\n    const positionClassNames = controlPosition.split('-');\n    return (jsx(\"div\", { className: cc(['react-flow__resize-control', 'nodrag', ...positionClassNames, variant, className]), ref: resizeControlRef, style: {\n            ...style,\n            scale,\n            ...(color && { [isHandleControl ? 'backgroundColor' : 'borderColor']: color }),\n        }, children: children }));\n}\n/**\n * To create your own resizing UI, you can use the `NodeResizeControl` component where you can pass children (such as icons).\n * @public\n *\n */\nconst NodeResizeControl = memo(ResizeControl);\n\n/**\n * The `<NodeResizer />` component can be used to add a resize functionality to your\n * nodes. It renders draggable controls around the node to resize in all directions.\n * @public\n *\n * @example\n *```jsx\n *import { memo } from 'react';\n *import { Handle, Position, NodeResizer } from '@xyflow/react';\n *\n *function ResizableNode({ data }) {\n *  return (\n *    <>\n *      <NodeResizer minWidth={100} minHeight={30} />\n *      <Handle type=\"target\" position={Position.Left} />\n *      <div style={{ padding: 10 }}>{data.label}</div>\n *      <Handle type=\"source\" position={Position.Right} />\n *    </>\n *  );\n *};\n *\n *export default memo(ResizableNode);\n *```\n */\nfunction NodeResizer({ nodeId, isVisible = true, handleClassName, handleStyle, lineClassName, lineStyle, color, minWidth = 10, minHeight = 10, maxWidth = Number.MAX_VALUE, maxHeight = Number.MAX_VALUE, keepAspectRatio = false, autoScale = true, shouldResize, onResizeStart, onResize, onResizeEnd, }) {\n    if (!isVisible) {\n        return null;\n    }\n    return (jsxs(Fragment, { children: [XY_RESIZER_LINE_POSITIONS.map((position) => (jsx(NodeResizeControl, { className: lineClassName, style: lineStyle, nodeId: nodeId, position: position, variant: ResizeControlVariant.Line, color: color, minWidth: minWidth, minHeight: minHeight, maxWidth: maxWidth, maxHeight: maxHeight, onResizeStart: onResizeStart, keepAspectRatio: keepAspectRatio, autoScale: autoScale, shouldResize: shouldResize, onResize: onResize, onResizeEnd: onResizeEnd }, position))), XY_RESIZER_HANDLE_POSITIONS.map((position) => (jsx(NodeResizeControl, { className: handleClassName, style: handleStyle, nodeId: nodeId, position: position, color: color, minWidth: minWidth, minHeight: minHeight, maxWidth: maxWidth, maxHeight: maxHeight, onResizeStart: onResizeStart, keepAspectRatio: keepAspectRatio, autoScale: autoScale, shouldResize: shouldResize, onResize: onResize, onResizeEnd: onResizeEnd }, position)))] }));\n}\n\nconst selector = (state) => state.domNode?.querySelector('.react-flow__renderer');\nfunction NodeToolbarPortal({ children }) {\n    const wrapperRef = useStore(selector);\n    if (!wrapperRef) {\n        return null;\n    }\n    return createPortal(children, wrapperRef);\n}\n\nconst nodeEqualityFn = (a, b) => a?.internals.positionAbsolute.x !== b?.internals.positionAbsolute.x ||\n    a?.internals.positionAbsolute.y !== b?.internals.positionAbsolute.y ||\n    a?.measured.width !== b?.measured.width ||\n    a?.measured.height !== b?.measured.height ||\n    a?.selected !== b?.selected ||\n    a?.internals.z !== b?.internals.z;\nconst nodesEqualityFn = (a, b) => {\n    if (a.size !== b.size) {\n        return false;\n    }\n    for (const [key, node] of a) {\n        if (nodeEqualityFn(node, b.get(key))) {\n            return false;\n        }\n    }\n    return true;\n};\nconst storeSelector = (state) => ({\n    x: state.transform[0],\n    y: state.transform[1],\n    zoom: state.transform[2],\n    selectedNodesCount: state.nodes.filter((node) => node.selected).length,\n});\n/**\n * This component can render a toolbar or tooltip to one side of a custom node. This\n * toolbar doesn't scale with the viewport so that the content is always visible.\n *\n * @public\n * @example\n * ```jsx\n *import { memo } from 'react';\n *import { Handle, Position, NodeToolbar } from '@xyflow/react';\n *\n *function CustomNode({ data }) {\n *  return (\n *    <>\n *      <NodeToolbar isVisible={data.toolbarVisible} position={data.toolbarPosition}>\n *        <button>delete</button>\n *        <button>copy</button>\n *        <button>expand</button>\n *      </NodeToolbar>\n *\n *      <div style={{ padding: '10px 20px' }}>\n *        {data.label}\n *      </div>\n *\n *      <Handle type=\"target\" position={Position.Left} />\n *      <Handle type=\"source\" position={Position.Right} />\n *    </>\n *  );\n *};\n *\n *export default memo(CustomNode);\n *```\n * @remarks By default, the toolbar is only visible when a node is selected. If multiple\n * nodes are selected it will not be visible to prevent overlapping toolbars or\n * clutter. You can override this behavior by setting the `isVisible` prop to `true`.\n */\nfunction NodeToolbar({ nodeId, children, className, style, isVisible, position = Position.Top, offset = 10, align = 'center', ...rest }) {\n    const contextNodeId = useNodeId();\n    const nodesSelector = useCallback((state) => {\n        const nodeIds = Array.isArray(nodeId) ? nodeId : [nodeId || contextNodeId || ''];\n        const internalNodes = nodeIds.reduce((res, id) => {\n            const node = state.nodeLookup.get(id);\n            if (node) {\n                res.set(node.id, node);\n            }\n            return res;\n        }, new Map());\n        return internalNodes;\n    }, [nodeId, contextNodeId]);\n    const nodes = useStore(nodesSelector, nodesEqualityFn);\n    const { x, y, zoom, selectedNodesCount } = useStore(storeSelector, shallow);\n    // if isVisible is not set, we show the toolbar only if its node is selected and no other node is selected\n    const isActive = typeof isVisible === 'boolean'\n        ? isVisible\n        : nodes.size === 1 && nodes.values().next().value?.selected && selectedNodesCount === 1;\n    if (!isActive || !nodes.size) {\n        return null;\n    }\n    const nodeRect = getInternalNodesBounds(nodes);\n    const nodesArray = Array.from(nodes.values());\n    const zIndex = Math.max(...nodesArray.map((node) => node.internals.z + 1));\n    const wrapperStyle = {\n        position: 'absolute',\n        transform: getNodeToolbarTransform(nodeRect, { x, y, zoom }, position, offset, align),\n        zIndex,\n        ...style,\n    };\n    return (jsx(NodeToolbarPortal, { children: jsx(\"div\", { style: wrapperStyle, className: cc(['react-flow__node-toolbar', className]), ...rest, \"data-id\": nodesArray.reduce((acc, node) => `${acc}${node.id} `, '').trim(), children: children }) }));\n}\n\nexport { Background, BackgroundVariant, BaseEdge, BezierEdge, ControlButton, Controls, EdgeLabelRenderer, EdgeText, Handle, MiniMap, NodeResizeControl, NodeResizer, NodeToolbar, Panel, index as ReactFlow, ReactFlowProvider, SimpleBezierEdge, SmoothStepEdge, StepEdge, StraightEdge, ViewportPortal, applyEdgeChanges, applyNodeChanges, getSimpleBezierPath, isEdge, isNode, useConnection, useEdges, useEdgesState, useHandleConnections, useInternalNode, useKeyPress, useNodeConnections, useNodeId, useNodes, useNodesData, useNodesInitialized, useNodesState, useOnSelectionChange, useOnViewportChange, useReactFlow, useStore, useStoreApi, useUpdateNodeInternals, useViewport };\n"], "names": ["StoreContext", "createContext", "Provider$1", "Provider", "zustandErrorMessage", "useStore", "selector", "equalityFn", "store", "useContext", "Error", "useStoreApi", "useMemo", "getState", "setState", "subscribe", "style", "display", "ariaLiveStyle", "position", "width", "height", "margin", "border", "padding", "overflow", "clip", "clipPath", "ARIA_NODE_DESC_KEY", "ARIA_EDGE_DESC_KEY", "ariaLiveSelector", "s", "ariaLiveMessage", "ariaLabelConfigSelector", "ariaLabelConfig", "AriaLiveMessage", "rfId", "jsx", "id", "children", "A11yDescriptions", "disableKeyboardA11y", "jsxs", "Fragment", "Panel", "forwardRef", "className", "rest", "ref", "positionClasses", "split", "Attribution", "proOptions", "hideAttribution", "href", "target", "rel", "displayName", "selector$m", "selectedNodes", "<PERSON><PERSON><PERSON>", "node", "nodeLookup", "selected", "push", "internals", "userNode", "edge", "edgeLookup", "selectId", "obj", "areEqual", "a", "b", "map", "SelectionListenerInner", "onSelectionChange", "useEffect", "params", "nodes", "edges", "onSelectionChangeHandlers", "for<PERSON>ach", "fn", "changeSelector", "SelectionListener", "storeHasSelectionChangeHandlers", "defaultNodeOrigin", "defaultViewport", "x", "y", "zoom", "fieldsToTrack", "selector$l", "setNodes", "set<PERSON><PERSON>", "setMinZoom", "setMaxZoom", "setTranslateExtent", "setNodeExtent", "reset", "setDefaultNodesAndEdges", "setPaneClickDistance", "initPrevValues", "translateExtent", "node<PERSON><PERSON><PERSON>", "minZoom", "max<PERSON><PERSON>", "elementsSelectable", "noPanClassName", "paneClickDistance", "StoreUpdater", "props", "defaultNodes", "defaultEdges", "<PERSON><PERSON><PERSON>s", "current", "useRef", "fieldName", "fieldValue", "fitViewQueued", "fitViewOptions", "getMediaQuery", "window", "matchMedia", "defaultDoc", "document", "useKeyPress", "keyCode", "options", "actInsideInputWithModifier", "keyPressed", "setKeyPressed", "useState", "modifierPressed", "pressedKeys", "Set", "keyCodes", "keysToWatch", "keys", "Array", "isArray", "filter", "kc", "replace", "<PERSON><PERSON><PERSON>", "reduce", "res", "item", "concat", "downHandler", "event", "ctrl<PERSON>ey", "metaKey", "shift<PERSON>ey", "altKey", "keyOrCode", "useKeyOrCode", "code", "add", "isMatchingKey", "<PERSON><PERSON><PERSON>", "isInteractiveElement", "nodeName", "preventDefault", "up<PERSON><PERSON><PERSON>", "clear", "delete", "key", "re<PERSON><PERSON><PERSON><PERSON>", "addEventListener", "removeEventListener", "isUp", "length", "size", "some", "every", "k", "has", "eventCode", "includes", "useViewportHelper", "zoomIn", "panZoom", "scaleBy", "duration", "Promise", "resolve", "zoomOut", "zoomTo", "zoomLevel", "scaleTo", "getZoom", "transform", "setViewport", "async", "viewport", "tX", "tY", "tZoom", "getViewport", "setCenter", "fitBounds", "bounds", "ease", "interpolate", "screenToFlowPosition", "clientPosition", "snapGrid", "snapToGrid", "domNode", "domX", "domY", "getBoundingClientRect", "correctedPosition", "_snapGrid", "_snapToGrid", "flowToScreenPosition", "flowPosition", "rendererPosition", "applyChanges", "changes", "elements", "updatedElements", "changesMap", "Map", "addItemChanges", "change", "type", "set", "elementChanges", "get", "element", "updatedElement", "applyChange", "undefined", "index", "splice", "dragging", "dimensions", "measured", "setAttributes", "resizing", "applyNodeChanges", "applyEdgeChanges", "createSelectionChange", "getSelectionChanges", "items", "selectedIds", "mutateItem", "willBeSelected", "getElements<PERSON><PERSON><PERSON><PERSON><PERSON>", "lookup", "itemsLookup", "entries", "lookupItem", "storeItem", "elementToRemoveChange", "isNode", "isEdge", "fixedForwardRef", "render", "useIsomorphicLayoutEffect", "useLayoutEffect", "useQueue", "runQueue", "serial", "setSerial", "BigInt", "queue", "cb", "createQueue", "n", "queueItems", "BatchContext", "BatchProvider", "nodeQueue", "useCallback", "hasDefaultNodes", "onNodesChange", "next", "payload", "requestAnimationFrame", "edgeQueue", "hasDefaultEdges", "onEdgesChange", "value", "selector$k", "useReactFlow", "viewportHelper", "batchContext", "useBatchContext", "viewportInitialized", "<PERSON><PERSON><PERSON><PERSON>", "getInternalNode", "getNodeRect", "nodeToUse", "parentId", "nodeWithPosition", "updateNode", "nodeUpdate", "prevNodes", "nextNode", "updateEdge", "edgeUpdate", "prevEdges", "nextEdge", "getNodes", "getNode", "get<PERSON>dges", "e", "getEdge", "addNodes", "newNodes", "addEdges", "newEdges", "toObject", "deleteElements", "nodesToRemove", "edgesToRemove", "onNodesDelete", "onEdgesDelete", "triggerNodeChanges", "triggerEdgeChanges", "onDelete", "onBeforeDelete", "matchingNodes", "matching<PERSON><PERSON>", "hasMatchingEdges", "hasMatchingNodes", "edgeChanges", "nodeChanges", "deletedNodes", "deleted<PERSON>dges", "getIntersectingNodes", "nodeOrRect", "partially", "isRect", "nodeRect", "hasNodesOption", "internalNode", "positionAbsolute", "currNodeRect", "overlappingArea", "isNodeIntersecting", "area", "updateNodeData", "dataUpdate", "nextData", "data", "updateEdgeData", "getNodesBounds", "getHandleConnections", "nodeId", "from", "connectionLookup", "values", "getNodeConnections", "handleId", "<PERSON><PERSON><PERSON><PERSON>", "fitViewResolver", "promise", "win$1", "containerStyle", "top", "left", "selector$j", "userSelectionActive", "lib", "ZoomPane", "onPaneContextMenu", "zoomOnScroll", "zoomOnPinch", "panOnScroll", "panOnScrollSpeed", "panOnScrollMode", "Free", "zoomOnDoubleClick", "panOnDrag", "zoomActivationKeyCode", "preventScrolling", "noWheelClassName", "onViewportChange", "isControlledViewport", "zoomPane", "zoomActivationKeyPressed", "updateDimensions", "onError", "resizeObserver", "ResizeObserver", "observe", "unobserve", "useResizeHandler", "onTransformChange", "onDraggingChange", "paneDragging", "onPanZoomStart", "vp", "onViewportChangeStart", "onMoveStart", "onPanZoom", "onMove", "onPanZoomEnd", "onViewportChangeEnd", "onMoveEnd", "closest", "destroy", "update", "selector$i", "userSelectionRect", "UserSelection", "wrapHandler", "handler", "containerRef", "selector$h", "connectionInProgress", "connection", "inProgress", "Pane", "isSelecting", "selectionKeyPressed", "selectionMode", "Full", "selectionOnDrag", "onSelectionStart", "onSelectionEnd", "onPaneClick", "onPaneScroll", "onPaneMouseEnter", "onPaneMouseMove", "onPaneMouseLeave", "hasActiveSelection", "container", "containerBounds", "selectedNodeIds", "selectedEdgeIds", "selectionInProgress", "selectionStarted", "onClick", "resetSelectedElements", "nodesSelectionActive", "onWheel", "draggable", "selection", "onContextMenu", "onPointerEnter", "onPointerDown", "button", "setPointerCapture", "pointerId", "nativeEvent", "startX", "startY", "onPointerMove", "defaultEdgeOptions", "mouseX", "mouseY", "nextUserSelectRect", "Math", "abs", "prevSelectedNodeIds", "prevSelectedEdgeIds", "Partial", "edgesSelectable", "selectable", "connections", "edgeId", "onPointerUp", "releasePointerCapture", "onPointerLeave", "handleNodeClick", "unselect", "nodeRef", "addSelectedNodes", "unselectNodesAndEdges", "multiSelectionActive", "blur", "useDrag", "disabled", "noDragClassName", "handleSelector", "isSelectable", "nodeClickDistance", "setDragging", "xyDrag", "getStoreItems", "onNodeMouseDown", "onDragStart", "onDragStop", "useMoveSelectedNodes", "nodeExtent", "nodesDraggable", "updateNodePositions", "nodeUpdates", "isSelected", "selectedAndDraggable", "xVelo", "<PERSON><PERSON><PERSON>", "xDiff", "direction", "factor", "yDiff", "nextPosition", "NodeIdContext", "Consumer", "useNodeId", "selector$g", "connectOnClick", "<PERSON><PERSON>", "memo", "Top", "isValidConnection", "isConnectable", "isConnectableStart", "isConnectableEnd", "onConnect", "onMouseDown", "onTouchStart", "<PERSON><PERSON><PERSON><PERSON>", "connectingFrom", "connectingTo", "clickConnecting", "isPossibleEndHandle", "connectionInProcess", "clickConnectionInProcess", "valid", "state", "connectionClickStartHandle", "clickHandle", "connectionMode", "fromHandle", "toHandle", "<PERSON><PERSON><PERSON><PERSON>", "Strict", "connectingSelector", "onConnectExtended", "onConnectAction", "edgeParams", "isMouseTriggered", "currentStore", "autoPanOnConnect", "connectionRadius", "flowId", "panBy", "cancelConnection", "onConnectStart", "onConnectEnd", "updateConnection", "getTransform", "getFromHandle", "autoPanSpeed", "drag<PERSON><PERSON><PERSON><PERSON>", "connectionDragThreshold", "source", "connectable", "connectablestart", "connectableend", "clickconnecting", "connectingfrom", "connectingto", "connectionindicator", "onClickConnectStart", "onClickConnectEnd", "isValidConnectionStore", "connectionState", "handleType", "doc", "isValidConnectionHandler", "handle", "fromNodeId", "fromHandleId", "fromType", "connectionClone", "structuredClone", "toPosition", "arrowKeyDiffs", "ArrowUp", "ArrowDown", "ArrowLeft", "ArrowRight", "builtinNodeTypes", "input", "sourcePosition", "Bottom", "label", "default", "targetPosition", "output", "group", "selector$f", "transformString", "NodesSelection", "onSelectionContextMenu", "moveSelectedNodes", "focus", "preventScroll", "tabIndex", "onKeyDown", "Object", "prototype", "hasOwnProperty", "call", "win", "selector$e", "FlowRendererComponent", "deleteKeyCode", "selectionKeyCode", "multiSelectionKeyCode", "panActivationKeyCode", "_panOnScroll", "_panOnDrag", "panActivationKeyPressed", "_selectionOnDrag", "deleteKeyPressed", "multiSelectionKeyPressed", "useGlobalKeyHandler", "<PERSON><PERSON><PERSON><PERSON>", "useVisibleNodeIds", "onlyRenderVisible", "selector$d", "selector$c", "updateNodeInternals", "NodeWrapper", "onMouseEnter", "onMouseMove", "onMouseLeave", "onDoubleClick", "nodesConnectable", "nodesFocusable", "nodeTypes", "isParent", "parentLookup", "nodeType", "NodeComponent", "isDraggable", "isFocusable", "focusable", "hasDimensions", "observedNode", "prevSourcePosition", "prevTargetPosition", "prevType", "isInitialized", "handleBounds", "hidden", "typeChanged", "sourcePosChanged", "targetPosChanged", "nodeElement", "force", "useNodeObserver", "dragHandle", "nodeDimensions", "inlineDimensions", "initialWidth", "initialHeight", "getNodeInlineStyleDimensions", "hasPointerEvents", "onMouseEnterHandler", "onMouseMoveHandler", "onMouseLeaveHandler", "onContextMenuHandler", "onDoubleClickHandler", "parent", "zIndex", "z", "pointerEvents", "visibility", "selectNodesOnDrag", "nodeDragThreshold", "toLowerCase", "onFocus", "matches", "autoPanOnNodeFocus", "role", "ariaRole", "aria<PERSON><PERSON><PERSON>", "domAttributes", "positionAbsoluteX", "positionAbsoluteY", "deletable", "selector$b", "NodeRendererComponent", "nodeIds", "onlyRenderVisibleElements", "updates", "entry", "getAttribute", "disconnect", "useResizeObserver", "onNodeClick", "onNodeMouseEnter", "onNodeMouseMove", "onNodeMouseLeave", "onNodeContextMenu", "onNodeDoubleClick", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MarkerSymbols", "Arrow", "color", "strokeWidth", "stroke", "strokeLinecap", "strokeLinejoin", "fill", "points", "ArrowClosed", "<PERSON><PERSON>", "markerUnits", "orient", "Symbol", "useMarkerSymbol", "marker<PERSON>id<PERSON>", "markerHeight", "viewBox", "refX", "refY", "MarkerDefinitions", "defaultColor", "markers", "defaultMarkerStart", "markerStart", "defaultMarkerEnd", "markerEnd", "marker", "MarkerDefinitions$1", "EdgeTextComponent", "labelStyle", "labelShowBg", "labelBgStyle", "labelBgPadding", "labelBgBorderRadius", "edgeTextBbox", "setEdgeTextBbox", "edgeTextClasses", "edgeTextRef", "textBbox", "getBBox", "rx", "ry", "dy", "EdgeText", "BaseEdge", "path", "labelX", "labelY", "interactionWidth", "d", "strokeOpacity", "getControl", "pos", "x1", "y1", "x2", "y2", "Left", "Right", "getSimpleBezierPath", "sourceX", "sourceY", "targetX", "targetY", "sourceControlX", "sourceControlY", "targetControlX", "targetControlY", "offsetX", "offsetY", "createSimpleBezierEdge", "_id", "isInternal", "SimpleBezierEdge", "SimpleBezierEdgeInternal", "createSmoothStepEdge", "pathOptions", "borderRadius", "offset", "stepPosition", "SmoothStepEdge", "SmoothStepEdgeInternal", "createStepEdge", "Step<PERSON><PERSON>", "StepEdgeInternal", "createStraightEdge", "StraightEdge", "StraightEdgeInternal", "createBezierEdge", "curvature", "<PERSON><PERSON><PERSON><PERSON>", "BezierEdgeInternal", "builtinEdgeTypes", "straight", "step", "smoothstep", "simplebezier", "nullPosition", "shiftX", "shift", "shiftY", "EdgeUpdaterClassName", "EdgeAnchor", "centerX", "centerY", "radius", "onMouseOut", "cx", "cy", "r", "EdgeUpdateAnchors", "isReconnectable", "reconnectRadius", "onReconnect", "onReconnectStart", "onReconnectEnd", "setReconnecting", "setUpdateHover", "handleEdgeUpdater", "<PERSON><PERSON><PERSON><PERSON>", "edgeUpdaterType", "_event", "evt", "onReconnectMouseEnter", "onReconnectMouseOut", "targetHandle", "sourceHandle", "EdgeWrapper", "edgesFocusable", "edgesReconnectable", "edgeTypes", "edgeType", "EdgeComponent", "reconnectable", "edgeRef", "updateHover", "reconnecting", "sourceNode", "targetNode", "edgePosition", "elevateOnSelect", "elevateEdgesOnSelect", "markerStartUrl", "markerEndUrl", "onEdgeDoubleClick", "onEdgeContextMenu", "onEdgeMouseEnter", "onEdgeMouseMove", "onEdgeMouseLeave", "animated", "inactive", "updating", "addSelectedEdges", "sourceHandleId", "targetHandleId", "selector$a", "EdgeRendererComponent", "defaultMarkerColor", "onEdgeClick", "edgeIds", "visibleEdgeIds", "<PERSON><PERSON><PERSON><PERSON>", "selector$9", "Viewport", "selector$8", "syncViewport", "storeSelector$1", "to", "useConnection", "connectionSelector", "combinedSelector", "getSelector", "selector$7", "ConnectionLineWrapper", "component", "ConnectionLine", "CustomComponent", "<PERSON><PERSON>", "fromNode", "fromPosition", "toNode", "connectionLineType", "connectionLineStyle", "fromX", "fromY", "toX", "toY", "connectionStatus", "pathParams", "SimpleBezier", "Step", "SmoothStep", "emptyTypes", "useNodeOrEdgeTypesWarning", "nodeOrEdgeTypes", "GraphViewComponent", "onInit", "connectionLineComponent", "connectionLineContainerStyle", "rfInstance", "setTimeout", "useOnInitHandler", "useViewportSync", "GraphView", "getInitialState", "storeEdges", "storeNodes", "storeNodeOrigin", "storeNodeExtent", "nodesInitialized", "elevateNodesOnSelect", "autoPanOnNodeDrag", "debug", "createStore", "resolveFitView", "checkEquality", "updatedInternals", "console", "log", "nodeDragItems", "parentExpandChildren", "dragItem", "expandParent", "max", "rect", "parentExpandChanges", "edgesToUnselect", "setScaleExtent", "clickDistance", "setClickDistance", "nextNodeExtent", "delta", "nextZoom", "is", "ReactFlowProvider", "initialNodes", "initialEdges", "initialMinZoom", "initialMaxZoom", "initialFitViewOptions", "Wrapper", "wrapperStyle", "onNodeDragStart", "onNodeDrag", "onNodeDragStop", "onSelectionDragStart", "onSelectionDrag", "onSelectionDragStop", "defaultViewport$1", "attributionPosition", "colorMode", "onScroll", "colorModeClassName", "colorModeClass", "setColorModeClass", "mediaQuery", "updateColorModeClass", "useColorModeClass", "wrapperOnScroll", "currentTarget", "scrollTo", "behavior", "selector$6", "querySelector", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createPortal", "useNodesState", "nds", "useEdgesState", "eds", "LinePattern", "lineWidth", "variant", "DotPattern", "<PERSON><PERSON><PERSON><PERSON>", "defaultSize", "Dots", "Lines", "Cross", "selector$3", "patternId", "BackgroundComponent", "gap", "bgColor", "patternClassName", "patternSize", "isDots", "isCross", "gapXY", "scaledGap", "scaledSize", "offsetXY", "patternDimensions", "scaledOffset", "_patternId", "patternUnits", "patternTransform", "Background", "PlusIcon", "xmlns", "MinusIcon", "FitViewIcon", "LockIcon", "UnlockIcon", "ControlButton", "selector$2", "isInteractive", "<PERSON><PERSON><PERSON>Reached", "max<PERSON><PERSON>Reached", "ControlsComponent", "showZoom", "showFitView", "showInteractive", "onZoomIn", "onZoomOut", "onFitView", "onInteractiveChange", "orientation", "orientationClass", "title", "MiniMapNode", "strokeColor", "shapeRendering", "background", "backgroundColor", "selectorNodeIds", "getAttrFunction", "func", "Function", "NodeComponentWrapper", "nodeColorFunc", "nodeStrokeColorFunc", "nodeClassNameFunc", "nodeBorderRadius", "nodeStrokeWidth", "MiniMapNodes$1", "nodeStrokeColor", "nodeColor", "nodeClassName", "nodeComponent", "chrome", "filterHidden", "selector$1", "viewBB", "boundingRect", "flowWidth", "flowHeight", "MiniMapComponent", "maskColor", "maskStrokeColor", "maskStrokeWidth", "pannable", "zoomable", "inversePan", "zoomStep", "offsetScale", "svg", "elementWidth", "elementHeight", "scaledWidth", "scaledHeight", "viewScale", "viewWidth", "viewHeight", "labelledBy", "viewScaleRef", "minimapInstance", "getViewScale", "onSvgClick", "pointer", "onSvgNodeClick", "_a<PERSON><PERSON><PERSON><PERSON>", "fillRule", "MiniMap", "defaultPositions", "Line", "min<PERSON><PERSON><PERSON>", "minHeight", "max<PERSON><PERSON><PERSON>", "Number", "MAX_VALUE", "maxHeight", "keepAspectRatio", "resizeDirection", "autoScale", "shouldResize", "onResizeStart", "onResize", "onResizeEnd", "contextNodeId", "resizeControlRef", "isHandleControl", "scale", "calculateScale", "resizer", "controlPosition", "paneDomNode", "onChange", "child<PERSON><PERSON><PERSON>", "origin", "child", "positionChange", "dimensionChange", "<PERSON><PERSON><PERSON><PERSON>", "onEnd", "boundaries", "positionClassNames"], "sourceRoot": ""}