"use strict";(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[626],{3652:function(e,t,a){a.r(t);var s=a(6540),i=a(226),n=a(4810),c=a(230),l=a(4716),m=a(1155),r=a(6647);const{Title:d,Text:o}=c.A;t.default=e=>{let{data:t}=e;const{isAuthenticated:a,isLoading:c,authType:d}=(0,i.A)();return(0,s.useEffect)(()=>{a&&!c&&(0,n.navigate)("/")},[a,c]),(0,s.useEffect)(()=>{"none"!==d||c||(0,n.navigate)("/")},[d,c]),c?s.createElement(m.A,{meta:t.site.siteMetadata,title:"Login",link:"/login"},s.createElement("div",{className:"flex items-center justify-center h-screen"},s.createElement(l.A,{size:"large",tip:"加载中..."}))):s.createElement(m.A,{meta:t.site.siteMetadata,title:"登录",link:"/login",showHeader:!0,restricted:!1},s.createElement("div",{className:"flex items-center justify-center h-[calc(100vh-164px)]"},s.createElement("div",{className:"w-full rounded bg-secondary max-w-md p-8 sxhadow-sm"},s.createElement("div",{className:"text-center mb-8"},s.createElement("div",{className:"mb-3"},s.createElement(r.A,{icon:"app",size:12})),s.createElement("div",{className:"text-2xl mb-1 font-semibold text-primary"},"登录到 ",t.site.siteMetadata.title),s.createElement("div",{className:"text-secondary text-sm"},"请使用token登录或联系管理员")))))}}}]);
//# sourceMappingURL=component---src-pages-login-tsx-9de13b3861f00f5d33fc.js.map