{"version": 3, "file": "component---src-pages-404-tsx-24eede75141e167b9bc2.js", "mappings": "sLAGA,MAAMA,EAAa,CACjBC,MAAO,UACPC,QAAS,OACTC,WAAY,4CAERC,EAAgB,CACpBC,UAAW,EACXC,aAAc,GACdC,SAAU,KAGNC,EAAkB,CACtBF,aAAc,IA+BhB,UArB0CG,IAEtCC,EAAAA,cAAA,QAAMC,MAAOX,GACXU,EAAAA,cAAA,MAAIC,MAAOP,GAAe,SAC1BM,EAAAA,cAAA,KAAGC,MAAOH,GAAiB,sBAEzBE,EAAAA,cAAA,WAOI,KACJA,EAAAA,cAAA,WACAA,EAAAA,cAACE,EAAAA,KAAI,CAACC,GAAG,KAAI,QAAW,MAQzB,MAAMC,EAAeA,IAAMJ,EAAAA,cAAA,aAAO,Q", "sources": ["webpack://autogentstudio/./src/pages/404.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Link, HeadFC, PageProps } from \"gatsby\"\n\nconst pageStyles = {\n  color: \"#232129\",\n  padding: \"96px\",\n  fontFamily: \"-apple-system, Roboto, sans-serif, serif\",\n}\nconst headingStyles = {\n  marginTop: 0,\n  marginBottom: 64,\n  maxWidth: 320,\n}\n\nconst paragraphStyles = {\n  marginBottom: 48,\n}\nconst codeStyles = {\n  color: \"#8A6534\",\n  padding: 4,\n  backgroundColor: \"#FFF4DB\",\n  fontSize: \"1.25rem\",\n  borderRadius: 4,\n}\n\nconst NotFoundPage: React.FC<PageProps> = () => {\n  return (\n    <main style={pageStyles}>\n      <h1 style={headingStyles}>页面未找到</h1>\n      <p style={paragraphStyles}>\n        抱歉 😔，我们找不到您要查找的内容。\n        <br />\n        {process.env.NODE_ENV === \"development\" ? (\n          <>\n            <br />\n            尝试在 <code style={codeStyles}>src/pages/</code> 中创建页面。\n            <br />\n          </>\n        ) : null}\n        <br />\n        <Link to=\"/\">返回首页</Link>。\n      </p>\n    </main>\n  )\n}\n\nexport default NotFoundPage\n\nexport const Head: HeadFC = () => <title>页面未找到</title>\n"], "names": ["pageStyles", "color", "padding", "fontFamily", "headingStyles", "marginTop", "marginBottom", "max<PERSON><PERSON><PERSON>", "paragraphStyles", "NotFoundPage", "React", "style", "Link", "to", "Head"], "sourceRoot": ""}