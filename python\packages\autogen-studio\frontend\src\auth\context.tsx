import React, { createContext, useState, useEffect, useContext } from "react";
import { authAPI, User } from "./api";
import { message } from "antd";
import { navigate } from "gatsby";
import {
  sanitizeUrl,
  sanitizeRedirectUrl,
  isValidMessageOrigin,
  isValidUserObject,
} from "../components/utils/security-utils";

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  authType: string;
  loginWithToken: (token: string) => Promise<void>;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

const TOKEN_KEY = "auth_token";

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [authType, setAuthType] = useState<string>("none");

  // Function to get token from localStorage
  const getToken = (): string | null => {
    return localStorage.getItem(TOKEN_KEY);
  };

  // Function to save token to localStorage
  const saveToken = (token: string): void => {
    localStorage.setItem(TOKEN_KEY, token);
  };

  // Function to remove token from localStorage
  const removeToken = (): void => {
    localStorage.removeItem(TOKEN_KEY);
  };

  // Load user on initial render if token exists
  useEffect(() => {
    const loadUser = async () => {
      try {
        console.log("🔄 AuthProvider: Starting user initialization...");

        // Check for injected auth state first (from POST token login)
        const injectedAuthState = (window as any).__AUTOGEN_AUTH_STATE__;
        if (injectedAuthState && injectedAuthState.isAuthenticated) {
          console.log("🔍 AuthProvider: Found injected auth state from POST login");
          console.log("✅ AuthProvider: Using injected user data:", injectedAuthState.user);

          // Use the injected user data directly
          setUser(injectedAuthState.user);
          setIsLoading(false);

          // Clean up the injected state to avoid conflicts
          delete (window as any).__AUTOGEN_AUTH_STATE__;
          return;
        }

        // Check auth type first
        const { type } = await authAPI.checkAuthType();
        setAuthType(type);
        console.log(`🔍 AuthProvider: Auth type detected: ${type}`);

        // Always check for stored token first, regardless of auth type
        const token = getToken();
        console.log(`🔍 AuthProvider: Stored token: ${token ? 'found' : 'not found'}`);

        if (token) {
          try {
            console.log("🔄 AuthProvider: Attempting to load user with token...");
            // Try to load user data with token
            const userData = await authAPI.getCurrentUser(token);
            setUser(userData);
            console.log(`✅ AuthProvider: User loaded successfully: ${userData.name} (${userData.id})`);
            setIsLoading(false);
            return;
          } catch (error) {
            console.error("❌ AuthProvider: Failed to load user with token:", error);
            removeToken(); // Clear invalid token
            // Continue to set default user below
          }
        }

        // If no token or token is invalid, set default user for "none" auth type
        if (type === "none") {
          console.log("🔄 AuthProvider: Setting default user for 'none' auth type");
          setUser({
            id: "<EMAIL>",
            name: "Guest User",
          });
          setIsLoading(false);
          return;
        }

        // For other auth types without token, just finish loading
        console.log(`🔄 AuthProvider: No token for auth type '${type}', finishing loading`);
        setIsLoading(false);
      } catch (error) {
        console.error("❌ AuthProvider: Failed to load user:", error);
        // Set default user as fallback
        setUser({
          id: "<EMAIL>",
          name: "Guest User",
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadUser();

    // Listen for injected auth state events
    const handleAuthInjected = (event: CustomEvent) => {
      console.log("🔄 AuthProvider: Received auth injection event:", event.detail);
      if (event.detail && event.detail.isAuthenticated && event.detail.user) {
        console.log("✅ AuthProvider: Setting user from injection event");
        setUser(event.detail.user);
        setIsLoading(false);
      }
    };

    window.addEventListener('autogen-auth-injected', handleAuthInjected as EventListener);

    // Cleanup event listener
    return () => {
      window.removeEventListener('autogen-auth-injected', handleAuthInjected as EventListener);
    };
  }, []);



  // Token login function - directly login with token and redirect to home
  const loginWithToken = async (token: string): Promise<void> => {
    try {
      console.log("🔄 AuthProvider: Starting token login...");
      setIsLoading(true);

      const result = await authAPI.loginWithToken(token);
      console.log("✅ AuthProvider: Token login API call successful");

      // Store token
      saveToken(result.token);
      console.log(`✅ AuthProvider: Token stored to localStorage: ${result.token.substring(0, 30)}...`);

      // Update user state
      setUser(result.user);
      console.log(`✅ AuthProvider: User state updated: ${result.user.name} (${result.user.id})`);

      // Show success message
      message.success("Successfully logged in");

      // Redirect to home or specified redirect URL
      const redirectUrl = (result as any).redirect_url || "/";
      console.log(`🔄 AuthProvider: Redirecting to: ${redirectUrl}`);
      navigate(sanitizeRedirectUrl(redirectUrl));
    } catch (error) {
      message.error("Token login failed");
      console.error("❌ AuthProvider: Token login error:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };



  // Logout function
  const logout = (): void => {
    removeToken();
    setUser(null);
    message.info("Successfully logged out");
    navigate("/login");
  };

  const value = {
    user,
    isAuthenticated: !!user,
    isLoading,
    authType,
    loginWithToken,
    logout,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Hook to use auth context
export const useAuth = (): AuthContextType => {
  if (typeof window === "undefined") {
    // Return default values or empty implementation
    return {
      user: null,
      isAuthenticated: false,
      isLoading: true,
      authType: "none",
      loginWithToken: async () => {},
      logout: () => {},
    } as AuthContextType;
  }
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
