#!/usr/bin/env python3
"""
验证JWT配置的脚本
"""

import os
import jwt
from datetime import datetime, timedelta, timezone
from pathlib import Path
import os
# os.environ["OPENAI_API_KEY"] = "sk-aOnHdMDqN5SXgxwAC50298EbCcA54345B906AfC7F5D7B384"
# os.environ["OPENAI_BASE_URL"] = "https://gnomic.nengyongai.cn/v1"
# os.environ["AUTOGENSTUDIO_JWT_SECRET"] = "xingchen-chatbot-secret-key-2025"
# os.environ["AUTOGENSTUDIO_TOKEN_EXPIRY"] = "6000000000"


def check_environment_variable():
    """检查环境变量"""
    jwt_secret = os.environ.get('AUTOGENSTUDIO_JWT_SECRET')
    token_expiry = os.environ.get('AUTOGENSTUDIO_TOKEN_EXPIRY', '60')
    
    print(f"\n🔍 检查环境变量:")
    print(f"AUTOGENSTUDIO_JWT_SECRET: {'已设置' if jwt_secret else '未设置'}")
    if jwt_secret:
        print(f"  长度: {len(jwt_secret)} 字符")
        print(f"  前10字符: {jwt_secret[:10]}...")
    
    print(f"AUTOGENSTUDIO_TOKEN_EXPIRY: {token_expiry}")
    
    return jwt_secret

def test_jwt_functionality(secret):
    """测试JWT功能"""
    if not secret:
        print("❌ 无法测试JWT功能：未找到密钥")
        return False
    
    try:
        print(f"\n🧪 测试JWT功能:")
        
        # 创建测试payload
        payload = {
            "sub": "<EMAIL>",
            "name": "Test User",
            "email": "<EMAIL>",
            "provider": "jwt",
            "roles": ["user"],
            "exp": datetime.now(timezone.utc) + timedelta(minutes=60)
        }
        
        # 生成token
        token = jwt.encode(payload, secret, algorithm="HS256")
        print(f"✓ Token生成成功: {token[:30]}...")
        
        # 验证token
        decoded = jwt.decode(token, secret, algorithms=["HS256"])
        print(f"✓ Token验证成功")
        print(f"  用户ID: {decoded['sub']}")
        print(f"  用户名: {decoded['name']}")
        print(f"  过期时间: {datetime.fromtimestamp(decoded['exp'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ JWT测试失败: {e}")
        return False

def test_autogen_studio_auth():
    """测试AutoGen Studio认证系统"""
    try:
        print(f"\n🔧 测试AutoGen Studio认证系统:")
        
        # 导入认证管理器
        from autogenstudio.web.auth.manager import AuthManager
        
        # 从环境变量创建认证管理器
        auth_manager = AuthManager.from_env()
        
        print(f"✓ 认证管理器创建成功")
        print(f"  认证类型: {auth_manager.config.type}")
        print(f"  JWT密钥: {'已配置' if auth_manager.config.jwt_secret else '未配置'}")
        print(f"  Token过期时间: {auth_manager.config.token_expiry_minutes} 分钟")
        auth_manager.config.jwt_secret = "xingchen-chatbot-secret-key-2025"
        # 测试token验证
        if auth_manager.config.jwt_secret:
            test_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************.jQbjzmjPWVmIoc7qUHWjZlQtPGwBdgHOqxjIG81eFuA"
            payload = jwt.decode(test_token, auth_manager.config.jwt_secret, algorithms=["HS256"])
            print(f"✓ Token解码成功: {payload}")
            is_valid = auth_manager.is_valid_token(test_token)
            print(f"  Token验证功能: {'正常' if not is_valid else '异常'}")  # 无效token应该返回False
        
        return True
        
    except Exception as e:
        print(f"❌ AutoGen Studio认证系统测试失败: {e}")
        return False

def create_sample_env_file():
    """创建示例.env文件"""
    print(f"\n📝 创建示例.env文件:")
    
    # 生成示例密钥
    import secrets
    import string
    alphabet = string.ascii_letters + string.digits + "!@#$%^&*+-=?_"
    sample_secret = ''.join(secrets.choice(alphabet) for _ in range(64))
    
    env_content = f"""# AutoGen Studio 环境变量配置
# 此文件包含敏感信息，请勿提交到版本控制

# JWT认证配置
AUTOGENSTUDIO_JWT_SECRET={sample_secret}
AUTOGENSTUDIO_TOKEN_EXPIRY=60

# 其他配置示例
# AUTOGENSTUDIO_AUTH_TYPE=none
# AUTOGENSTUDIO_HOST=127.0.0.1
# AUTOGENSTUDIO_PORT=8081
"""
    
    # 尝试在当前目录创建
    env_file = Path.cwd() / ".env"
    try:
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(env_content)
        print(f"✓ 示例.env文件已创建: {env_file}")
        print(f"✓ 请根据需要修改配置")
        return env_file
    except Exception as e:
        print(f"❌ 创建示例.env文件失败: {e}")
        return None

def test_jwt_with_request():
    token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************.jQbjzmjPWVmIoc7qUHWjZlQtPGwBdgHOqxjIG81eFuA"
    import requests as req
    url = "http://localhost:8081/"
    res = req.post(url, json={"token": token})
    print(res.json())

def main():
    print("🔐 AutoGen Studio JWT配置验证工具")
    print("=" * 60)
    

    # 检查环境变量
    jwt_secret = check_environment_variable()
    
    # 测试JWT功能
    jwt_test_passed = test_jwt_functionality(jwt_secret)
    
    # 测试AutoGen Studio认证系统
    auth_test_passed = test_autogen_studio_auth()
    
    # 总结
    print(f"\n" + "=" * 60)
    print(f"📊 配置验证总结:")
    print(f"  环境变量: {'✓ 已配置' if jwt_secret else '✗ 未配置'}")
    print(f"  JWT功能: {'✓ 正常' if jwt_test_passed else '✗ 异常'}")
    print(f"  认证系统: {'✓ 正常' if auth_test_passed else '✗ 异常'}")
    
    if not jwt_secret:
        print(f"\n💡 建议:")
        print(f"1. 运行 'python generate_jwt_secret.py' 生成JWT密钥")
        print(f"2. 或手动设置环境变量 AUTOGENSTUDIO_JWT_SECRET")
        
        create_sample = input(f"\n是否创建示例.env文件? (y/n): ").lower().strip()
        if create_sample in ['y', 'yes', '是']:
            create_sample_env_file()
    print(test_jwt_with_request())

if __name__ == "__main__":
    main()
