
define([], function () {
/*!-----------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)
 * Released under the MIT license
 * https://github.com/microsoft/vscode/blob/main/LICENSE.txt
 *-----------------------------------------------------------*/globalThis._VSCODE_NLS_MESSAGES=["{0} ({1})","Eingabe","Gro\xDF-/Kleinschreibung beachten","Nur ganzes Wort suchen","Regul\xE4ren Ausdruck verwenden","Eingabe","Gro\xDF-/Kleinschreibung beibehalten",'\xDCberpr\xFCfen Sie dies in der barrierefreien Ansicht mit "{0}".','\xDCberpr\xFCfen Sie dies in der barrierefreien Ansicht \xFCber den Befehl "Barrierefreie Ansicht \xF6ffnen", der zurzeit nicht \xFCber eine Tastenzuordnung ausgel\xF6st werden kann.',"Fehler: {0}","Warnung: {0}","Info: {0}"," oder {0} f\xFCr Verlauf"," ({0} f\xFCr Verlauf)","Gel\xF6schte Eingabe","Ungebunden","Auswahlfeld","Weitere Aktionen...","Filter","Fuzzy\xFCbereinstimmung","Zum Filtern Text eingeben","Zum Suchen eingeben","Zum Suchen eingeben","Schlie\xDFen","Keine Ergebnisse","Keine Ergebnisse gefunden.",null,"(leer)","{0}: {1}","Ein Systemfehler ist aufgetreten ({0}).","Ein unbekannter Fehler ist aufgetreten. Weitere Details dazu finden Sie im Protokoll.","Ein unbekannter Fehler ist aufgetreten. Weitere Details dazu finden Sie im Protokoll.","{0} ({1} Fehler gesamt)","Ein unbekannter Fehler ist aufgetreten. Weitere Details dazu finden Sie im Protokoll.","STRG","UMSCHALTTASTE","ALT","Windows","STRG","UMSCHALTTASTE","ALT","Super","Steuern","UMSCHALTTASTE","Option","Befehl","Steuern","UMSCHALTTASTE","ALT","Windows","Steuern","UMSCHALTTASTE","ALT","Super",null,null,null,null,null,"Auch bei l\xE4ngeren Zeilen am Ende bleiben","Auch bei l\xE4ngeren Zeilen am Ende bleiben","Sekund\xE4re Cursor entfernt","&&R\xFCckg\xE4ngig","R\xFCckg\xE4ngig","&&Wiederholen","Wiederholen","&&Alles ausw\xE4hlen","Alle ausw\xE4hlen","Halten Sie die {0}-Taste gedr\xFCckt, um mit der Maus darauf zu zeigen.","Wird geladen...","Die Anzahl der Cursor wurde auf {0} beschr\xE4nkt. Erw\xE4gen Sie die Verwendung von [Suchen und Ersetzen](https://code.visualstudio.com/docs/editor/codebasics#_find-und-ersetzen) f\xFCr gr\xF6\xDFere \xC4nderungen, oder erh\xF6hen Sie die Multicursorbegrenzungseinstellung des Editors.","Erh\xF6hen des Grenzwerts f\xFCr mehrere Cursor",'"Unver\xE4nderte Bereiche reduzieren" umschalten','"Verschobene Codebl\xF6cke anzeigen" umschalten','"Bei eingeschr\xE4nktem Speicherplatz Inlineansicht verwenden" umschalten',"Diff-Editor","Seite wechseln","Vergleichsmodus beenden","Alle unver\xE4nderten Regionen reduzieren","Alle unver\xE4nderten Regionen anzeigen","Zur\xFCcksetzen","Barrierefreier Diff-Viewer","Zum n\xE4chsten Unterschied wechseln","Zum vorherigen Unterschied wechseln",'Symbol f\xFCr "Einf\xFCgen" im barrierefreien Diff-Viewer.','Symbol f\xFCr "Entfernen" im barrierefreien Diff-Viewer.','Symbol f\xFCr "Schlie\xDFen" im barrierefreien Diff-Viewer.',"Schlie\xDFen","Barrierefreier Diff-Viewer. Verwenden Sie den Pfeil nach oben und unten, um zu navigieren.","keine ge\xE4nderten Zeilen","1 Zeile ge\xE4ndert","{0} Zeilen ge\xE4ndert","Unterschied {0} von {1}: urspr\xFCngliche Zeile {2}, {3}, ge\xE4nderte Zeile {4}, {5}","leer","{0}: unver\xE4nderte Zeile {1}","{0} urspr\xFCngliche Zeile {1} ge\xE4nderte Zeile {2}","+ {0} ge\xE4nderte Zeile(n) {1}","\u2013 {0} Originalzeile {1}"," verwenden Sie {0}, um die Hilfe zur Barrierefreiheit zu \xF6ffnen.","Gel\xF6schte Zeilen kopieren","Gel\xF6schte Zeile kopieren","Ge\xE4nderte Zeilen kopieren","Ge\xE4nderte Zeile kopieren","Gel\xF6schte Zeile kopieren ({0})","Ge\xE4nderte Zeile ({0}) kopieren","Diese \xC4nderung r\xFCckg\xE4ngig machen","Bei eingeschr\xE4nktem Speicherplatz Inlineansicht verwenden","Verschobene Codebl\xF6cke anzeigen","Block wiederherstellen","Auswahl wiederherstellen","Barrierefreien Diff-Viewer \xF6ffnen","Unver\xE4nderten Bereich falten","{0} ausgeblendete Linien","Klicken oder ziehen Sie, um oben mehr anzuzeigen.","Unver\xE4nderte Regionen anzeigen","Klicken oder ziehen Sie, um unten mehr anzuzeigen.","{0} ausgeblendete Linien","Zum Auffalten doppelklicken","Code mit \xC4nderungen in Zeile {0}-{1} verschoben","Code mit \xC4nderungen aus Zeile {0}-{1} verschoben","Code in Zeile {0}-{1} verschoben","Code aus Zeile {0}-{1} verschoben","Ausgew\xE4hlte \xC4nderungen zur\xFCcksetzen","\xC4nderung zur\xFCcksetzen","Die Rahmenfarbe f\xFCr Text, der im Diff-Editor verschoben wurde.","Die aktive Rahmenfarbe f\xFCr Text, der im Diff-Editor verschoben wurde.","Die Farbe des Schattens um unver\xE4nderte Regionswidgets.","Zeilenformatierung f\xFCr Einf\xFCgungen im Diff-Editor","Zeilenformatierung f\xFCr Entfernungen im Diff-Editor","Die Hintergrundfarbe des Diff-Editor-Headers","Die Hintergrundfarbe des Diff-Editors f\xFCr mehrere Dateien","Die Rahmenfarbe des Diff-Editors f\xFCr mehrere Dateien","Keine ge\xE4nderten Dateien","Editor","Die Anzahl der Leerzeichen, denen ein Tabstopp entspricht. Diese Einstellung wird basierend auf dem Inhalt der Datei \xFCberschrieben, wenn {0} aktiviert ist.","Die Anzahl von Leerzeichen, die f\xFCr den Einzug oder \u201EtabSize\u201C verwendet werden, um den Wert aus \u201E#editor.tabSize#\u201C zu verwenden. Diese Einstellung wird basierend auf dem Dateiinhalt \xFCberschrieben, wenn \u201E#editor.detectIndentation#\u201C aktiviert ist.","F\xFCgt beim Dr\xFCcken der TAB-Taste Leerzeichen ein. Diese Einstellung wird basierend auf dem Inhalt der Datei \xFCberschrieben, wenn {0} aktiviert ist.","Steuert, ob {0} und {1} automatisch erkannt werden, wenn eine Datei basierend auf dem Dateiinhalt ge\xF6ffnet wird.","Nachfolgende automatisch eingef\xFCgte Leerzeichen entfernen","Spezielle Behandlung f\xFCr gro\xDFe Dateien zum Deaktivieren bestimmter speicherintensiver Funktionen.","Deaktivieren Sie Word-basierte Vorschl\xE4ge.","Nur W\xF6rter aus dem aktiven Dokument vorschlagen","W\xF6rter aus allen ge\xF6ffneten Dokumenten derselben Sprache vorschlagen","W\xF6rter aus allen ge\xF6ffneten Dokumenten vorschlagen","Steuert, ob Vervollst\xE4ndigungen auf Grundlage der W\xF6rter im Dokument berechnet werden sollen, und aus welchen Dokumenten sie berechnet werden sollen.","Die semantische Hervorhebung ist f\xFCr alle Farbdesigns aktiviert.","Die semantische Hervorhebung ist f\xFCr alle Farbdesigns deaktiviert.",'Die semantische Hervorhebung wird durch die Einstellung "semanticHighlighting" des aktuellen Farbdesigns konfiguriert.',"Steuert, ob die semantische Hervorhebung f\xFCr die Sprachen angezeigt wird, die sie unterst\xFCtzen.","Lassen Sie Peek-Editoren ge\xF6ffnet, auch wenn Sie auf ihren Inhalt doppelklicken oder auf die ESCAPETASTE klicken.","Zeilen, die diese L\xE4nge \xFCberschreiten, werden aus Leistungsgr\xFCnden nicht tokenisiert","Steuert, ob die Tokenisierung asynchron auf einem Webworker erfolgen soll.","Steuert, ob die asynchrone Tokenisierung protokolliert werden soll. Nur zum Debuggen.","Steuert, ob die asynchrone Tokenisierung anhand der Legacy-Hintergrundtokenisierung \xFCberpr\xFCft werden soll. Die Tokenisierung kann verlangsamt werden. Nur zum Debuggen.",'Steuert, ob die Struktursitteranalyse aktiviert und Telemetriedaten erfasst werden sollen. Das Festlegen von "editor.experimental.preferTreeSitter" f\xFCr bestimmte Sprachen hat Vorrang.',"Definiert die Klammersymbole, die den Einzug vergr\xF6\xDFern oder verkleinern.","Das \xF6ffnende Klammerzeichen oder die Zeichenfolgensequenz.","Das schlie\xDFende Klammerzeichen oder die Zeichenfolgensequenz.","Definiert die Klammerpaare, die durch ihre Schachtelungsebene farbig formatiert werden, wenn die Farbgebung f\xFCr das Klammerpaar aktiviert ist.","Das \xF6ffnende Klammerzeichen oder die Zeichenfolgensequenz.","Das schlie\xDFende Klammerzeichen oder die Zeichenfolgensequenz.","Timeout in Millisekunden, nach dem die Diff-Berechnung abgebrochen wird. Bei 0 wird kein Timeout verwendet.","Maximale Dateigr\xF6\xDFe in MB, f\xFCr die Diffs berechnet werden sollen. Verwenden Sie 0, um keinen Grenzwert zu setzen.","Steuert, ob der Diff-Editor die Unterschiede nebeneinander oder im Text anzeigt.","Wenn die Breite des Diff-Editors unter diesem Wert liegt, wird die Inlineansicht verwendet.","Wenn diese Option aktiviert ist und die Breite des Editors nicht ausreicht, wird die Inlineansicht verwendet.","Wenn diese Option aktiviert ist, zeigt der Diff-Editor Pfeile in seinem Glyphenrand an, um \xC4nderungen r\xFCckg\xE4ngig zu machen.","Wenn diese Option aktiviert ist, zeigt der Diff-Editor einen speziellen Bundsteg f\xFCr die Aktionen \u201EWiederherstellen\u201C und \u201EStagen\u201C an.","Wenn aktiviert, ignoriert der Diff-Editor \xC4nderungen an voran- oder nachgestellten Leerzeichen.",'Steuert, ob der Diff-Editor die Indikatoren "+" und "-" f\xFCr hinzugef\xFCgte/entfernte \xC4nderungen anzeigt.',"Steuert, ob der Editor CodeLens anzeigt.","Zeilenumbr\xFCche erfolgen nie.","Der Zeilenumbruch erfolgt an der Breite des Anzeigebereichs.","Zeilen werden gem\xE4\xDF der Einstellung \u201E{0}\u201C umbrochen.","Verwendet den Legacyvergleichsalgorithmus.","Verwendet den erweiterten Vergleichsalgorithmus.","Steuert, ob der Diff-Editor unver\xE4nderte Regionen anzeigt.","Steuert, wie viele Zeilen f\xFCr unver\xE4nderte Regionen verwendet werden.","Steuert, wie viele Zeilen als Mindestwert f\xFCr unver\xE4nderte Regionen verwendet werden.","Steuert, wie viele Zeilen beim Vergleich unver\xE4nderter Regionen als Kontext verwendet werden.","Steuert, ob der Diff-Editor erkannte Codeverschiebevorg\xE4nge anzeigen soll.","Steuert, ob der diff-Editor leere Dekorationen anzeigt, um anzuzeigen, wo Zeichen eingef\xFCgt oder gel\xF6scht wurden.","Wenn diese Option aktiviert ist und der Editor die Inlineansicht verwendet, werden Wort\xE4nderungen inline gerendert.","Verwenden Sie Plattform-APIs, um zu erkennen, wenn eine Sprachausgabe angef\xFCgt ist.","Optimieren Sie diese Option f\xFCr die Verwendung mit einer Sprachausgabe.","Hiermit wird angenommen, dass keine Sprachausgabe angef\xFCgt ist.","Steuert, ob die Benutzeroberfl\xE4che in einem Modus ausgef\xFChrt werden soll, in dem sie f\xFCr Sprachausgaben optimiert ist.","Steuert, ob beim Kommentieren ein Leerzeichen eingef\xFCgt wird.","Steuert, ob leere Zeilen bei Umschalt-, Hinzuf\xFCgungs- oder Entfernungsaktionen f\xFCr Zeilenkommentare ignoriert werden sollen.","Steuert, ob ein Kopiervorgang ohne Auswahl die aktuelle Zeile kopiert.","Steuert, ob der Cursor bei der Suche nach \xDCbereinstimmungen w\xE4hrend der Eingabe springt.","Suchzeichenfolge niemals aus der Editorauswahl seeden.","Suchzeichenfolge immer aus der Editorauswahl seeden, einschlie\xDFlich Wort an Cursorposition.","Suchzeichenfolge nur aus der Editorauswahl seeden.",'Steuert, ob f\xFCr die Suchzeichenfolge im Widget "Suche" ein Seeding aus der Auswahl des Editors ausgef\xFChrt wird.','"In Auswahl suchen" niemals automatisch aktivieren (Standard).','"In Auswahl suchen" immer automatisch aktivieren.','"In Auswahl suchen" automatisch aktivieren, wenn mehrere Inhaltszeilen ausgew\xE4hlt sind.','Steuert die Bedingung zum automatischen Aktivieren von "In Auswahl suchen".','Steuert, ob das Widget "Suche" die freigegebene Suchzwischenablage unter macOS lesen oder bearbeiten soll.','Steuert, ob das Suchwidget zus\xE4tzliche Zeilen im oberen Bereich des Editors hinzuf\xFCgen soll. Wenn die Option auf "true" festgelegt ist, k\xF6nnen Sie \xFCber die erste Zeile hinaus scrollen, wenn das Suchwidget angezeigt wird.',"Steuert, ob die Suche automatisch am Anfang (oder am Ende) neu gestartet wird, wenn keine weiteren \xDCbereinstimmungen gefunden werden.",'Hiermit werden Schriftligaturen (Schriftartfeatures "calt" und "liga") aktiviert/deaktiviert. \xC4ndern Sie diesen Wert in eine Zeichenfolge, um die CSS-Eigenschaft "font-feature-settings" detailliert zu steuern.','Explizite CSS-Eigenschaft "font-feature-settings". Stattdessen kann ein boolescher Wert \xFCbergeben werden, wenn nur Ligaturen aktiviert/deaktiviert werden m\xFCssen.','Hiermit werden Schriftligaturen oder Schriftartfeatures konfiguriert. Hierbei kann es sich entweder um einen booleschen Wert zum Aktivieren oder Deaktivieren von Ligaturen oder um eine Zeichenfolge f\xFCr den Wert der CSS-Eigenschaft "font-feature-settings" handeln.',"Aktiviert/deaktiviert die \xDCbersetzung von \u201Efont-weight\u201C in \u201Efont-variation-settings\u201C. \xC4ndern Sie dies in eine Zeichenfolge f\xFCr eine differenzierte Steuerung der CSS-Eigenschaft \u201Efont-variation-settings\u201C.","Explizite CSS-Eigenschaft \u201Efont-variation-settings\u201C. Stattdessen kann ein boolescher Wert eingeben werden, wenn nur \u201Efont-weight\u201C in \u201Efont-variation-settings\u201C \xFCbersetzt werden muss.","Konfiguriert Variationen der Schriftart. Kann entweder ein boolescher Wert zum Aktivieren/Deaktivieren der \xDCbersetzung von \u201Efont-weight\u201C in \u201Efont-variation-settings\u201C oder eine Zeichenfolge f\xFCr den Wert der CSS-Eigenschaft \u201Efont-variation-settings\u201C sein.","Legt die Schriftgr\xF6\xDFe in Pixeln fest.",'Es sind nur die Schl\xFCsselw\xF6rter "normal" und "bold" sowie Zahlen zwischen 1 und 1000 zul\xE4ssig.','Steuert die Schriftbreite. Akzeptiert die Schl\xFCsselw\xF6rter "normal" und "bold" sowie Zahlen zwischen 1 und 1000.',"Vorschauansicht der Ergebnisse anzeigen (Standardeinstellung)","Zum Hauptergebnis gehen und Vorschauansicht anzeigen","Wechseln Sie zum prim\xE4ren Ergebnis, und aktivieren Sie die Navigation ohne Vorschau zu anderen Ergebnissen.",'Diese Einstellung ist veraltet. Verwenden Sie stattdessen separate Einstellungen wie "editor.editor.gotoLocation.multipleDefinitions" oder "editor.editor.gotoLocation.multipleImplementations".','Legt das Verhalten des Befehls "Gehe zu Definition" fest, wenn mehrere Zielpositionen vorhanden sind','Legt das Verhalten des Befehls "Gehe zur Typdefinition" fest, wenn mehrere Zielpositionen vorhanden sind.','Legt das Verhalten des Befehls "Gehe zu Deklaration" fest, wenn mehrere Zielpositionen vorhanden sind.','Legt das Verhalten des Befehls "Gehe zu Implementierungen", wenn mehrere Zielspeicherorte vorhanden sind','Legt das Verhalten des Befehls "Gehe zu Verweisen" fest, wenn mehrere Zielpositionen vorhanden sind','Die alternative Befehls-ID, die ausgef\xFChrt wird, wenn das Ergebnis von "Gehe zu Definition" die aktuelle Position ist.','Die alternative Befehls-ID, die ausgef\xFChrt wird, wenn das Ergebnis von "Gehe zu Typdefinition" die aktuelle Position ist.','Die alternative Befehls-ID, die ausgef\xFChrt wird, wenn das Ergebnis von "Gehe zu Deklaration" der aktuelle Speicherort ist.','Die alternative Befehls-ID, die ausgef\xFChrt wird, wenn das Ergebnis von "Gehe zu Implementatierung" der aktuelle Speicherort ist.','Die alternative Befehls-ID, die ausgef\xFChrt wird, wenn das Ergebnis von "Gehe zu Verweis" die aktuelle Position ist.',"Steuert, ob die Hovermarkierung angezeigt wird.","Steuert die Verz\xF6gerung in Millisekunden, nach der die Hovermarkierung angezeigt wird.","Steuert, ob die Hovermarkierung sichtbar bleiben soll, wenn der Mauszeiger dar\xFCber bewegt wird.",'Steuert die Verz\xF6gerung in Millisekunden, nach der die Hovermarkierung ausgeblendet wird. Erfordert die Aktivierung von "editor.hover.sticky".',"Zeigen Sie den Mauszeiger lieber \xFCber der Linie an, wenn Platz vorhanden ist.","Es wird angenommen, dass alle Zeichen gleich breit sind. Dies ist ein schneller Algorithmus, der f\xFCr Festbreitenschriftarten und bestimmte Alphabete (wie dem lateinischen), bei denen die Glyphen gleich breit sind, korrekt funktioniert.","Delegiert die Berechnung von Umbruchpunkten an den Browser. Dies ist ein langsamer Algorithmus, der bei gro\xDFen Dateien Code Freezes verursachen kann, aber in allen F\xE4llen korrekt funktioniert.",'Steuert den Algorithmus, der Umbruchpunkte berechnet. Beachten Sie, dass "advanced" im Barrierefreiheitsmodus f\xFCr eine optimale Benutzererfahrung verwendet wird.',"Codeaktionsmen\xFC deaktivieren.","Zeigt das Codeaktionsmen\xFC an, wenn sich der Cursor in Zeilen mit Code befindet.","Zeigt das Codeaktionsmen\xFC an, wenn sich der Cursor in Zeilen mit Code oder in leeren Zeilen befindet.","Aktiviert das Gl\xFChlampensymbol f\xFCr Codeaktionen im Editor.","Zeigt die geschachtelten aktuellen Bereiche w\xE4hrend des Bildlaufs am oberen Rand des Editors an.","Definiert die maximale Anzahl fixierter Zeilen, die angezeigt werden sollen.","Legt das Modell fest, das zur Bestimmung der zu fixierenden Zeilen verwendet wird. Existiert das Gliederungsmodell nicht, wird auf das Modell des Folding Providers zur\xFCckgegriffen, der wiederum auf das Einr\xFCckungsmodell zur\xFCckgreift. Diese Reihenfolge wird in allen drei F\xE4llen beachtet.","Hiermit aktivieren Sie das Scrollen mit fixiertem Bildlauf mit der horizontalen Scrollleiste des Editors.","Aktiviert die Inlay-Hinweise im Editor.","Inlay-Hinweise sind aktiviert","Inlay-Hinweise werden standardm\xE4\xDFig angezeigt und ausgeblendet, wenn Sie {0} gedr\xFCckt halten","Inlayhinweise sind standardm\xE4\xDFig ausgeblendet. Sie werden angezeigt, wenn {0} gedr\xFCckt gehalten wird.","Inlay-Hinweise sind deaktiviert","Steuert den Schriftgrad von Einlapphinweisen im Editor. Standardm\xE4\xDFig wird die {0} verwendet, wenn der konfigurierte Wert kleiner als {1} oder gr\xF6\xDFer als der Schriftgrad des Editors ist.",'Steuert die Schriftartfamilie von Einlapphinweisen im Editor. Bei Festlegung auf "leer" wird die {0} verwendet.',"Aktiviert den Abstand um die Inlay-Hinweise im Editor.",`Steuert die Zeilenh\xF6he. \r
 \u2013 Verwenden Sie 0, um die Zeilenh\xF6he automatisch anhand des Schriftgrads zu berechnen.\r
 \u2013 Werte zwischen 0 und 8 werden als Multiplikator mit dem Schriftgrad verwendet.\r
 \u2013 Werte gr\xF6\xDFer oder gleich 8 werden als effektive Werte verwendet.`,"Steuert, ob die Minimap angezeigt wird.","Steuert, ob die Minimap automatisch ausgeblendet wird.","Die Minimap hat die gleiche Gr\xF6\xDFe wie der Editor-Inhalt (und kann scrollen).","Die Minimap wird bei Bedarf vergr\xF6\xDFert oder verkleinert, um die H\xF6he des Editors zu f\xFCllen (kein Scrollen).","Die Minimap wird bei Bedarf verkleinert, damit sie nicht gr\xF6\xDFer als der Editor ist (kein Scrollen).","Legt die Gr\xF6\xDFe der Minimap fest.","Steuert die Seite, wo die Minimap gerendert wird.","Steuert, wann der Schieberegler f\xFCr die Minimap angezeigt wird.","Ma\xDFstab des in der Minimap gezeichneten Inhalts: 1, 2 oder 3.","Die tats\xE4chlichen Zeichen in einer Zeile rendern im Gegensatz zu Farbbl\xF6cken.","Begrenzen Sie die Breite der Minimap, um nur eine bestimmte Anzahl von Spalten zu rendern.","Steuert, ob benannte Bereiche als Abschnittsheader in der Minimap angezeigt werden.","Steuert, ob \u201EMARK: Kommentare\u201C als Abschnittsheader in der Minimap angezeigt werden.","Steuert den Schriftgrad von Abschnitts\xFCberschriften in der Minimap.","Steuert den Abstand (in Pixeln) zwischen den Zeichen der Abschnitts\xFCberschrift. Dies erleichtert die Lesbarkeit der Kopfzeile bei kleinen Schriftgr\xF6\xDFen.","Steuert den Abstand zwischen dem oberen Rand des Editors und der ersten Zeile.","Steuert den Abstand zwischen dem unteren Rand des Editors und der letzten Zeile.","Aktiviert ein Pop-up, das Dokumentation und Typ eines Parameters anzeigt w\xE4hrend Sie tippen.","Steuert, ob das Men\xFC mit Parameterhinweisen zyklisch ist oder sich am Ende der Liste schlie\xDFt.","Schnelle Vorschl\xE4ge werden im Vorschlagswidget angezeigt","Schnelle Vorschl\xE4ge werden als inaktiver Text angezeigt","Schnelle Vorschl\xE4ge sind deaktiviert","Schnellvorschl\xE4ge innerhalb von Zeichenfolgen aktivieren.","Schnellvorschl\xE4ge innerhalb von Kommentaren aktivieren.","Schnellvorschl\xE4ge au\xDFerhalb von Zeichenfolgen und Kommentaren aktivieren.","Steuert, ob Vorschl\xE4ge w\xE4hrend des Tippens automatisch angezeigt werden sollen. Dies kann bei der Eingabe von Kommentaren, Zeichenketten und anderem Code kontrolliert werden. Schnellvorschl\xE4ge k\xF6nnen so konfiguriert werden, dass sie als Geistertext oder mit dem Vorschlags-Widget angezeigt werden. Beachten Sie auch die {0}-Einstellung, die steuert, ob Vorschl\xE4ge durch Sonderzeichen ausgel\xF6st werden.","Zeilennummern werden nicht dargestellt.","Zeilennummern werden als absolute Zahl dargestellt.","Zeilennummern werden als Abstand in Zeilen an Cursorposition dargestellt.","Zeilennummern werden alle 10 Zeilen dargestellt.","Steuert die Anzeige von Zeilennummern.","Anzahl der Zeichen aus Festbreitenschriftarten, ab der dieses Editor-Lineal gerendert wird.","Farbe dieses Editor-Lineals.","Vertikale Linien nach einer bestimmten Anzahl von Monospacezeichen rendern. Verwenden Sie mehrere Werte f\xFCr mehrere Linien. Wenn das Array leer ist, werden keine Linien gerendert.","Die vertikale Bildlaufleiste wird nur bei Bedarf angezeigt.","Die vertikale Bildlaufleiste ist immer sichtbar.","Die vertikale Bildlaufleiste wird immer ausgeblendet.","Steuert die Sichtbarkeit der vertikalen Bildlaufleiste.","Die horizontale Bildlaufleiste wird nur bei Bedarf angezeigt.","Die horizontale Bildlaufleiste ist immer sichtbar.","Die horizontale Bildlaufleiste wird immer ausgeblendet.","Steuert die Sichtbarkeit der horizontalen Bildlaufleiste.","Die Breite der vertikalen Bildlaufleiste.","Die H\xF6he der horizontalen Bildlaufleiste.","Steuert, ob Klicks nach Seite scrollen oder zur Klickposition springen.","Wenn diese Option festgelegt ist, wird die Gr\xF6\xDFe des Editorinhalts nicht durch die horizontale Scrollleiste vergr\xF6\xDFert.","Legt fest, ob alle nicht einfachen ASCII-Zeichen hervorgehoben werden. Nur Zeichen zwischen U+0020 und U+007E, Tabulator, Zeilenvorschub und Wagenr\xFCcklauf gelten als einfache ASCII-Zeichen.","Legt fest, ob Zeichen, die nur als Platzhalter dienen oder \xFCberhaupt keine Breite haben, hervorgehoben werden.","Legt fest, ob Zeichen hervorgehoben werden, die mit einfachen ASCII-Zeichen verwechselt werden k\xF6nnen, mit Ausnahme derjenigen, die im aktuellen Gebietsschema des Benutzers \xFCblich sind.","Steuert, ob Zeichen in Kommentaren auch mit Unicode-Hervorhebung versehen werden sollen.","Steuert, ob Zeichen in Zeichenfolgen auch mit Unicode-Hervorhebung versehen werden sollen.","Definiert zul\xE4ssige Zeichen, die nicht hervorgehoben werden.","Unicodezeichen, die in zul\xE4ssigen Gebietsschemas \xFCblich sind, werden nicht hervorgehoben.","Steuert, ob Inline-Vorschl\xE4ge automatisch im Editor angezeigt werden.","Die Symbolleiste \u201EInline-Vorschlag\u201C anzeigen, wenn ein Inline-Vorschlag angezeigt wird.","Die Symbolleiste \u201EInline-Vorschlag\u201C anzeigen, wenn Sie mit dem Mauszeiger auf einen Inline-Vorschlag zeigen.","Die Inlinevorschlagssymbolleiste nie anzeigen.","Steuert, wann die Inlinevorschlagssymbolleiste angezeigt werden soll.","Steuert, wie Inlinevorschl\xE4ge mit dem Vorschlagswidget interagieren. Wenn diese Option aktiviert ist, wird das Vorschlagswidget nicht automatisch angezeigt, wenn Inlinevorschl\xE4ge verf\xFCgbar sind.","Steuert die Schriftfamilie der Inlinevorschl\xE4ge.",null,null,null,null,null,null,"Steuert, ob die Klammerpaar-Farbgebung aktiviert ist oder nicht. Verwenden Sie {0}, um die Hervorhebungsfarben der Klammer zu \xFCberschreiben.","Steuert, ob jeder Klammertyp \xFCber einen eigenen unabh\xE4ngigen Farbpool verf\xFCgt.","Aktiviert Klammernpaarf\xFChrungslinien.","Aktiviert Klammernpaarf\xFChrungslinien nur f\xFCr das aktive Klammerpaar.","Deaktiviert Klammernpaarf\xFChrungslinien.","Steuert, ob F\xFChrungslinien f\xFCr Klammerpaare aktiviert sind oder nicht.","Aktiviert horizontale F\xFChrungslinien als Erg\xE4nzung zu vertikalen Klammernpaarf\xFChrungslinien.","Aktiviert horizontale F\xFChrungslinien nur f\xFCr das aktive Klammerpaar.","Deaktiviert horizontale F\xFChrungslinien f\xFCr Klammernpaare.","Steuert, ob horizontale F\xFChrungslinien f\xFCr Klammernpaare aktiviert sind oder nicht.","Steuert, ob der Editor das aktive Klammerpaar hervorheben soll.","Steuert, ob der Editor Einzugsf\xFChrungslinien rendern soll.","Hebt die aktive Einzugsf\xFChrung hervor.","Hebt die aktive Einzugshilfslinie hervor, selbst wenn Klammerhilfslinien hervorgehoben sind.","Heben Sie die aktive Einzugshilfslinie nicht hervor.","Steuert, ob der Editor die aktive Einzugsf\xFChrungslinie hevorheben soll.","Vorschlag einf\xFCgen, ohne den Text auf der rechten Seite des Cursors zu \xFCberschreiben","Vorschlag einf\xFCgen und Text auf der rechten Seite des Cursors \xFCberschreiben","Legt fest, ob W\xF6rter beim Akzeptieren von Vervollst\xE4ndigungen \xFCberschrieben werden. Beachten Sie, dass dies von Erweiterungen abh\xE4ngt, die f\xFCr dieses Features aktiviert sind.","Steuert, ob Filter- und Suchvorschl\xE4ge geringf\xFCgige Tippfehler ber\xFCcksichtigen.","Steuert, ob bei der Sortierung W\xF6rter priorisiert werden, die in der N\xE4he des Cursors stehen.",'Steuert, ob gespeicherte Vorschlagauswahlen in verschiedenen Arbeitsbereichen und Fenstern gemeinsam verwendet werden (daf\xFCr ist "#editor.suggestSelection#" erforderlich).',"W\xE4hlen Sie immer einen Vorschlag aus, wenn IntelliSense automatisch ausgel\xF6st wird.","W\xE4hlen Sie niemals einen Vorschlag aus, wenn IntelliSense automatisch ausgel\xF6st wird.","W\xE4hlen Sie einen Vorschlag nur aus, wenn IntelliSense aus einem Triggerzeichen ausgel\xF6st wird.","W\xE4hlen Sie einen Vorschlag nur aus, wenn Sie IntelliSense w\xE4hrend der Eingabe ausl\xF6sen.","Steuert, ob ein Vorschlag ausgew\xE4hlt wird, wenn das Widget angezeigt wird. Beachten Sie, dass dies nur f\xFCr automatisch ausgel\xF6ste Vorschl\xE4ge ({0} und {1}) gilt und dass ein Vorschlag immer ausgew\xE4hlt wird, wenn er explizit aufgerufen wird, z. B. \xFCber STRG+LEERTASTE.",'Steuert, ob ein aktiver Schnipsel verhindert, dass der Bereich "Schnelle Vorschl\xE4ge" angezeigt wird.',"Steuert, ob Symbole in Vorschl\xE4gen ein- oder ausgeblendet werden.","Steuert die Sichtbarkeit der Statusleiste unten im Vorschlagswidget.","Steuert, ob das Ergebnis des Vorschlags im Editor in der Vorschau angezeigt werden soll.","Steuert, ob Vorschlagsdetails inline mit der Bezeichnung oder nur im Detailwidget angezeigt werden.","Diese Einstellung ist veraltet. Die Gr\xF6\xDFe des Vorschlagswidgets kann jetzt ge\xE4ndert werden.",'Diese Einstellung ist veraltet. Verwenden Sie stattdessen separate Einstellungen wie "editor.suggest.showKeywords" oder "editor.suggest.showSnippets".','Wenn aktiviert, zeigt IntelliSense "method"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "funktions"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "constructor"-Vorschl\xE4ge an.',"Wenn IntelliSense aktiviert ist, werden \u201Everaltete\u201C Vorschl\xE4ge angezeigt.","Wenn dies aktiviert ist, erfordert die IntelliSense-Filterung, dass das erste Zeichen mit einem Wortanfang \xFCbereinstimmt, z.\xA0B. \u201Ec\u201C in \u201EConsole\u201C oder \u201EWebContext\u201C, aber _nicht_ bei \u201Edescription\u201C. Wenn diese Option deaktiviert ist, zeigt IntelliSense mehr Ergebnisse an, sortiert sie aber weiterhin nach der \xDCbereinstimmungsqualit\xE4t.",'Wenn aktiviert, zeigt IntelliSense "field"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "variable"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "class"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "struct"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "interface"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "module"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "property"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "event"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "operator"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "unit"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "value"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "constant"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "enum"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "enumMember"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "keyword"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "text"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "color"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "file"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "reference"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "customcolor"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "folder"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "typeParameter"-Vorschl\xE4ge an.','Wenn aktiviert, zeigt IntelliSense "snippet"-Vorschl\xE4ge an.',"Wenn aktiviert, zeigt IntelliSense user-Vorschl\xE4ge an.","Wenn aktiviert, zeigt IntelliSense issues-Vorschl\xE4ge an.","Gibt an, ob f\xFChrende und nachstehende Leerzeichen immer ausgew\xE4hlt werden sollen.",'Gibt an, ob Unterw\xF6rter (z.\xA0B. "foo" in "fooBar" oder "foo_bar") ausgew\xE4hlt werden sollen.',"Gebietsschemas, die f\xFCr die Wortsegmentierung verwendet werden sollen, wenn wortbezogene Navigationen oder Vorg\xE4nge ausgef\xFChrt werden. Geben Sie das BCP 47-Sprachtag des Worts an, das Sie erkennen m\xF6chten (z. B. ja, zh-CN, zh-Hant-TW usw.).","Gebietsschemas, die f\xFCr die Wortsegmentierung verwendet werden sollen, wenn wortbezogene Navigationen oder Vorg\xE4nge ausgef\xFChrt werden. Geben Sie das BCP 47-Sprachtag des Worts an, das Sie erkennen m\xF6chten (z. B. ja, zh-CN, zh-Hant-TW usw.).","Kein Einzug. Umbrochene Zeilen beginnen bei Spalte 1.","Umbrochene Zeilen erhalten den gleichen Einzug wie das \xFCbergeordnete Element.","Umbrochene Zeilen erhalten + 1 Einzug auf das \xFCbergeordnete Element.","Umgebrochene Zeilen werden im Vergleich zum \xFCbergeordneten Element +2 einger\xFCckt.","Steuert die Einr\xFCckung der umbrochenen Zeilen.","Steuert, ob Sie eine Datei per Drag & Drop in einen Text-Editor ziehen k\xF6nnen, indem Sie die UMSCHALTTASTE gedr\xFCckt halten (anstatt die Datei in einem Editor zu \xF6ffnen).","Steuert, ob beim Ablegen von Dateien im Editor ein Widget angezeigt wird. Mit diesem Widget k\xF6nnen Sie steuern, wie die Datei ablegt wird.","Zeigt das Widget f\xFCr die Dropdownauswahl an, nachdem eine Datei im Editor abgelegt wurde.","Das Widget f\xFCr die Ablageauswahl wird nie angezeigt. Stattdessen wird immer der Standardablageanbieter verwendet.","Steuert, ob Sie Inhalte auf unterschiedliche Weise einf\xFCgen k\xF6nnen.","Steuert, ob beim Einf\xFCgen von Inhalt im Editor ein Widget angezeigt wird. Mit diesem Widget k\xF6nnen Sie steuern, wie die Datei eingef\xFCgt wird.","Das Widget f\xFCr die Einf\xFCgeauswahl anzeigen, nachdem der Inhalt in den Editor eingef\xFCgt wurde.","Das Widget f\xFCr die Einf\xFCgeauswahl wird nie angezeigt. Stattdessen wird immer das Standardeinf\xFCgeverhalten verwendet.",'Steuert, ob Vorschl\xE4ge \xFCber Commitzeichen angenommen werden sollen. In JavaScript kann ein Semikolon (";") beispielsweise ein Commitzeichen sein, das einen Vorschlag annimmt und dieses Zeichen eingibt.',"Einen Vorschlag nur mit der EINGABETASTE akzeptieren, wenn dieser eine \xC4nderung am Text vornimmt.","Steuert, ob Vorschl\xE4ge mit der EINGABETASTE (zus\xE4tzlich zur TAB-Taste) akzeptiert werden sollen. Vermeidet Mehrdeutigkeit zwischen dem Einf\xFCgen neuer Zeilen oder dem Annehmen von Vorschl\xE4gen.","Steuert die Anzahl von Zeilen im Editor, die von einer Sprachausgabe in einem Arbeitsschritt gelesen werden k\xF6nnen. Wenn eine Sprachausgabe erkannt wird, wird der Standardwert automatisch auf 500 festgelegt. Warnung: Ein Wert h\xF6her als der Standardwert, kann sich auf die Leistung auswirken.","Editor-Inhalt","Steuern Sie, ob Inlinevorschl\xE4ge von einer Sprachausgabe angek\xFCndigt werden.","Verwenden Sie Sprachkonfigurationen, um zu bestimmen, wann Klammern automatisch geschlossen werden sollen.","Schlie\xDFe Klammern nur automatisch, wenn der Cursor sich links von einem Leerzeichen befindet.","Steuert, ob der Editor automatisch Klammern schlie\xDFen soll, nachdem der Benutzer eine \xF6ffnende Klammer hinzugef\xFCgt hat.","Verwenden Sie Sprachkonfigurationen, um festzulegen, wann Kommentare automatisch geschlossen werden sollen.","Kommentare werden nur dann automatisch geschlossen, wenn sich der Cursor links von einem Leerraum befindet.","Steuert, ob der Editor Kommentare automatisch schlie\xDFen soll, nachdem die Benutzer*innen einen ersten Kommentar hinzugef\xFCgt haben.","Angrenzende schlie\xDFende Anf\xFChrungszeichen oder Klammern werden nur \xFCberschrieben, wenn sie automatisch eingef\xFCgt wurden.","Steuert, ob der Editor angrenzende schlie\xDFende Anf\xFChrungszeichen oder Klammern beim L\xF6schen entfernen soll.","Schlie\xDFende Anf\xFChrungszeichen oder Klammern werden nur \xFCberschrieben, wenn sie automatisch eingef\xFCgt wurden.","Steuert, ob der Editor schlie\xDFende Anf\xFChrungszeichen oder Klammern \xFCberschreiben soll.","Verwende die Sprachkonfiguration, um zu ermitteln, wann Anf\xFChrungsstriche automatisch geschlossen werden.","Schlie\xDFende Anf\xFChrungszeichen nur dann automatisch erg\xE4nzen, wenn der Cursor sich links von einem Leerzeichen befindet.","Steuert, ob der Editor Anf\xFChrungszeichen automatisch schlie\xDFen soll, nachdem der Benutzer ein \xF6ffnendes Anf\xFChrungszeichen hinzugef\xFCgt hat.","Der Editor f\xFCgt den Einzug nicht automatisch ein.","Der Editor beh\xE4lt den Einzug der aktuellen Zeile bei.","Der Editor beh\xE4lt den in der aktuellen Zeile definierten Einzug bei und beachtet f\xFCr Sprachen definierte Klammern.","Der Editor beh\xE4lt den Einzug der aktuellen Zeile bei, beachtet von Sprachen definierte Klammern und ruft spezielle onEnterRules-Regeln auf, die von Sprachen definiert wurden.","Der Editor beh\xE4lt den Einzug der aktuellen Zeile bei, beachtet die von Sprachen definierten Klammern, ruft von Sprachen definierte spezielle onEnterRules-Regeln auf und beachtet von Sprachen definierte indentationRules-Regeln.","Legt fest, ob der Editor den Einzug automatisch anpassen soll, wenn Benutzer Zeilen eingeben, einf\xFCgen, verschieben oder einr\xFCcken","Sprachkonfigurationen verwenden, um zu bestimmen, wann eine Auswahl automatisch umschlossen werden soll.","Mit Anf\xFChrungszeichen, nicht mit Klammern umschlie\xDFen.","Mit Klammern, nicht mit Anf\xFChrungszeichen umschlie\xDFen.","Steuert, ob der Editor die Auswahl beim Eingeben von Anf\xFChrungszeichen oder Klammern automatisch umschlie\xDFt.","Emuliert das Auswahlverhalten von Tabstoppzeichen, wenn Leerzeichen f\xFCr den Einzug verwendet werden. Die Auswahl wird an Tabstopps ausgerichtet.","Steuert, ob der Editor CodeLens anzeigt.","Steuert die Schriftfamilie f\xFCr CodeLens.","Steuert den Schriftgrad in Pixeln f\xFCr CodeLens. Bei Festlegung auf \u201E0, 90\xA0% von \u201E#editor.fontSize#\u201C verwendet.","Steuert, ob der Editor die Inline-Farbdecorators und die Farbauswahl rendern soll.","Farbauswahl sowohl beim Klicken als auch beim Daraufzeigen des Farbdekorators anzeigen","Farbauswahl beim Draufzeigen auf den Farbdekorator anzeigen","Farbauswahl beim Klicken auf den Farbdekorator anzeigen","Steuert die Bedingung, damit eine Farbauswahl aus einem Farbdekorator angezeigt wird.","Steuert die maximale Anzahl von Farb-Decorators, die in einem Editor gleichzeitig gerendert werden k\xF6nnen.","Zulassen, dass die Auswahl per Maus und Tasten die Spaltenauswahl durchf\xFChrt.","Steuert, ob Syntax-Highlighting in die Zwischenablage kopiert wird.","Steuert den Cursoranimationsstil.","Die Smooth Caret-Animation ist deaktiviert.","Die Smooth Caret-Animation ist nur aktiviert, wenn der Benutzer den Cursor mit einer expliziten Geste bewegt.","Die Smooth Caret-Animation ist immer aktiviert.","Steuert, ob die weiche Cursoranimation aktiviert werden soll.","Steuert den Cursorstil im Einf\xFCgeeingabemodus.","Steuert die Mindestanzahl sichtbarer f\xFChrender Zeilen\xA0(mindestens\xA00) und nachfolgender Zeilen\xA0(mindestens\xA01) um den Cursor. Dies wird in einigen anderen Editoren als \u201EscrollOff\u201C oder \u201EscrollOffset\u201C bezeichnet.",'"cursorSurroundingLines" wird nur erzwungen, wenn die Ausl\xF6sung \xFCber die Tastatur oder API erfolgt.','"cursorSurroundingLines" wird immer erzwungen.',"Steuert, wann \u201E#editor.cursorSurroundingLines#\u201C erzwungen werden soll.","Steuert die Breite des Cursors, wenn `#editor.cursorStyle#` auf `line` festgelegt ist.","Steuert, ob der Editor das Verschieben einer Auswahl per Drag and Drop zul\xE4sst.","Verwenden Sie eine neue Rendering-Methode mit SVGs.","Verwenden Sie eine neue Rendering-Methode mit Schriftartzeichen.","Verwenden Sie die stabile Rendering-Methode.","Steuert, ob Leerzeichen mit einer neuen experimentellen Methode gerendert werden.","Multiplikator f\xFCr Scrollgeschwindigkeit bei Dr\xFCcken von ALT.","Steuert, ob Codefaltung im Editor aktiviert ist.","Verwenden Sie eine sprachspezifische Faltstrategie, falls verf\xFCgbar. Andernfalls wird eine einzugsbasierte verwendet.","Einzugsbasierte Faltstrategie verwenden.","Steuert die Strategie f\xFCr die Berechnung von Faltbereichen.","Steuert, ob der Editor eingefaltete Bereiche hervorheben soll.","Steuert, ob der Editor Importbereiche automatisch reduziert.","Die maximale Anzahl von faltbaren Regionen. Eine Erh\xF6hung dieses Werts kann dazu f\xFChren, dass der Editor weniger reaktionsf\xE4hig wird, wenn die aktuelle Quelle eine gro\xDFe Anzahl von faltbaren Regionen aufweist.","Steuert, ob eine Zeile aufgefaltet wird, wenn nach einer gefalteten Zeile auf den leeren Inhalt geklickt wird.","Steuert die Schriftfamilie.","Steuert, ob der Editor den eingef\xFCgten Inhalt automatisch formatieren soll. Es muss ein Formatierer vorhanden sein, der in der Lage ist, auch Dokumentbereiche zu formatieren.","Steuert, ob der Editor die Zeile nach der Eingabe automatisch formatieren soll.","Steuert, ob der Editor den vertikalen Glyphenrand rendert. Der Glyphenrand wird haupts\xE4chlich zum Debuggen verwendet.","Steuert, ob der Cursor im \xDCbersichtslineal ausgeblendet werden soll.","Legt den Abstand der Buchstaben in Pixeln fest.","Steuert, ob die verkn\xFCpfte Bearbeitung im Editor aktiviert ist. Abh\xE4ngig von der Sprache werden zugeh\xF6rige Symbole, z.\xA0B. HTML-Tags, w\xE4hrend der Bearbeitung aktualisiert.","Steuert, ob der Editor Links erkennen und anklickbar machen soll.","Passende Klammern hervorheben",'Ein Multiplikator, der f\xFCr die Mausrad-Bildlaufereignisse "deltaX" und "deltaY" verwendet werden soll.',"Schriftart des Editors vergr\xF6\xDFern, wenn das Mausrad verwendet und \u201ECmd\u201C gedr\xFCckt wird.","Schriftart des Editors vergr\xF6\xDFern, wenn das Mausrad verwendet und die STRG-TASTE gedr\xFCckt wird.","Mehrere Cursor zusammenf\xFChren, wenn sie sich \xFCberlappen.","Ist unter Windows und Linux der STRG-Taste und unter macOS der Befehlstaste zugeordnet.","Ist unter Windows und Linux der ALT-Taste und unter macOS der Wahltaste zugeordnet.",'Der Modifizierer, der zum Hinzuf\xFCgen mehrerer Cursor mit der Maus verwendet werden soll. Die Mausgesten "Gehe zu Definition" und "Link \xF6ffnen" werden so angepasst, dass sie nicht mit dem [Multicursormodifizierer](https://code.visualstudio.com/docs/editor/codebasics#_multicursor-Modifizierer) in Konflikt stehen.',"Jeder Cursor f\xFCgt eine Textzeile ein.","Jeder Cursor f\xFCgt den vollst\xE4ndigen Text ein.","Steuert das Einf\xFCgen, wenn die Zeilenanzahl des Einf\xFCgetexts der Cursor-Anzahl entspricht.","Steuert die maximale Anzahl von Cursorn, die sich gleichzeitig in einem aktiven Editor befindet.","Hebt keine Vorkommen hervor.","Hebt Vorkommen nur in der aktuellen Datei hervor.","Experimentell: Hebt Vorkommen in allen g\xFCltigen ge\xF6ffneten Dateien hervor.","Steuert, ob Vorkommen in ge\xF6ffneten Dateien hervorgehoben werden sollen.","Steuert, ob um das \xDCbersichtslineal ein Rahmen gezeichnet werden soll.","Struktur beim \xD6ffnen des Peek-Editors fokussieren","Editor fokussieren, wenn Sie den Peek-Editor \xF6ffnen","Steuert, ob der Inline-Editor oder die Struktur im Peek-Widget fokussiert werden soll.",'Steuert, ob die Mausgeste "Gehe zu Definition" immer das Vorschauwidget \xF6ffnet.',"Steuert die Verz\xF6gerung in Millisekunden nach der Schnellvorschl\xE4ge angezeigt werden.","Steuert, ob der Editor bei Eingabe automatisch eine Umbenennung vornimmt.",'Veraltet. Verwenden Sie stattdessen "editor.linkedEditing".',"Steuert, ob der Editor Steuerzeichen rendern soll.","Letzte Zeilennummer rendern, wenn die Datei mit einem Zeilenumbruch endet.","Hebt den Bundsteg und die aktuelle Zeile hervor.","Steuert, wie der Editor die aktuelle Zeilenhervorhebung rendern soll.","Steuert, ob der Editor die aktuelle Zeilenhervorhebung nur dann rendern soll, wenn der Fokus auf dem Editor liegt.","Leerraumzeichen werden gerendert mit Ausnahme der einzelnen Leerzeichen zwischen W\xF6rtern.","Hiermit werden Leerraumzeichen nur f\xFCr ausgew\xE4hlten Text gerendert.","Nur nachstehende Leerzeichen rendern","Steuert, wie der Editor Leerzeichen rendern soll.","Steuert, ob eine Auswahl abgerundete Ecken aufweisen soll.","Steuert die Anzahl der zus\xE4tzlichen Zeichen, nach denen der Editor horizontal scrollt.","Steuert, ob der Editor jenseits der letzten Zeile scrollen wird.","Nur entlang der vorherrschenden Achse scrollen, wenn gleichzeitig vertikal und horizontal gescrollt wird. Dadurch wird ein horizontaler Versatz beim vertikalen Scrollen auf einem Trackpad verhindert.","Steuert, ob die prim\xE4re Linux-Zwischenablage unterst\xFCtzt werden soll.","Steuert, ob der Editor \xDCbereinstimmungen hervorheben soll, die der Auswahl \xE4hneln.","Steuerelemente f\xFCr die Codefaltung immer anzeigen.","Zeigen Sie niemals die Faltungssteuerelemente an, und verringern Sie die Gr\xF6\xDFe des Bundstegs.","Steuerelemente f\xFCr die Codefaltung nur anzeigen, wenn sich die Maus \xFCber dem Bundsteg befindet.","Steuert, wann die Steuerungselemente f\xFCr die Codefaltung am Bundsteg angezeigt werden.","Steuert das Ausblenden von nicht verwendetem Code.","Steuert durchgestrichene veraltete Variablen.","Zeige Schnipselvorschl\xE4ge \xFCber den anderen Vorschl\xE4gen.","Schnipselvorschl\xE4ge unter anderen Vorschl\xE4gen anzeigen.","Zeige Schnipselvorschl\xE4ge mit anderen Vorschl\xE4gen.","Keine Schnipselvorschl\xE4ge anzeigen.","Steuert, ob Codeschnipsel mit anderen Vorschl\xE4gen angezeigt und wie diese sortiert werden.","Legt fest, ob der Editor Bildl\xE4ufe animiert ausf\xFChrt.","Steuert, ob f\xFCr Benutzer*innen, die eine Sprachausgabe nutzen, bei Anzeige einer Inlinevervollst\xE4ndigung ein Hinweis zur Barrierefreiheit angezeigt werden soll.","Schriftgrad f\xFCr das Vorschlagswidget. Bei Festlegung auf {0} wird der Wert von {1} verwendet.","Zeilenh\xF6he f\xFCr das Vorschlagswidget. Bei Festlegung auf {0} wird der Wert von {1} verwendet. Der Mindestwert ist 8.","Steuert, ob Vorschl\xE4ge automatisch angezeigt werden sollen, wenn Triggerzeichen eingegeben werden.","Immer den ersten Vorschlag ausw\xE4hlen.",'W\xE4hlen Sie die aktuellsten Vorschl\xE4ge aus, es sei denn, es wird ein Vorschlag durch eine weitere Eingabe ausgew\xE4hlt, z.B. "console.| -> console.log", weil "log" vor Kurzem abgeschlossen wurde.','W\xE4hlen Sie Vorschl\xE4ge basierend auf fr\xFCheren Pr\xE4fixen aus, die diese Vorschl\xE4ge abgeschlossen haben, z.B. "co -> console" und "con ->" const".',"Steuert, wie Vorschl\xE4ge bei Anzeige der Vorschlagsliste vorab ausgew\xE4hlt werden.","Die Tab-Vervollst\xE4ndigung f\xFCgt den passendsten Vorschlag ein, wenn auf Tab gedr\xFCckt wird.","Tab-Vervollst\xE4ndigungen deaktivieren.",'Codeschnipsel per Tab vervollst\xE4ndigen, wenn die Pr\xE4fixe \xFCbereinstimmen. Funktioniert am besten, wenn "quickSuggestions" deaktiviert sind.',"Tab-Vervollst\xE4ndigungen aktivieren.","Ungew\xF6hnliche Zeilenabschlusszeichen werden automatisch entfernt.","Ungew\xF6hnliche Zeilenabschlusszeichen werden ignoriert.","Zum Entfernen ungew\xF6hnlicher Zeilenabschlusszeichen wird eine Eingabeaufforderung angezeigt.","Entfernen Sie un\xFCbliche Zeilenabschlusszeichen, die Probleme verursachen k\xF6nnen.","Leerzeichen und Registerkarten werden in \xDCbereinstimmung mit Tabstopps eingef\xFCgt und gel\xF6scht.","Verwenden Sie die Standardregel f\xFCr Zeilenumbr\xFCche.","Trennstellen d\xFCrfen nicht f\xFCr Texte in Chinesisch/Japanisch/Koreanisch (CJK) verwendet werden. Das Verhalten von Nicht-CJK-Texten ist mit dem f\xFCr normales Verhalten identisch.","Steuert die Regeln f\xFCr Trennstellen, die f\xFCr Texte in Chinesisch/Japanisch/Koreanisch (CJK) verwendet werden.","Zeichen, die als Worttrennzeichen verwendet werden, wenn wortbezogene Navigationen oder Vorg\xE4nge ausgef\xFChrt werden.","Zeilenumbr\xFCche erfolgen nie.","Der Zeilenumbruch erfolgt an der Breite des Anzeigebereichs.",'Der Zeilenumbruch erfolgt bei "#editor.wordWrapColumn#".','Der Zeilenumbruch erfolgt beim Mindestanzeigebereich und "#editor.wordWrapColumn".',"Steuert, wie der Zeilenumbruch durchgef\xFChrt werden soll.",'Steuert die umschlie\xDFende Spalte des Editors, wenn "#editor.wordWrap#" den Wert "wordWrapColumn" oder "bounded" aufweist.',"Steuert, ob Inlinefarbdekorationen mithilfe des Standard-Dokumentfarbanbieters angezeigt werden sollen.","Steuert, ob der Editor Registerkarten empf\xE4ngt oder zur Navigation zur Workbench zur\xFCckgibt.","Hintergrundfarbe zur Hervorhebung der Zeile an der Cursorposition.","Hintergrundfarbe f\xFCr den Rahmen um die Zeile an der Cursorposition.","Hintergrundfarbe der markierten Bereiche, wie z.B. Quick Open oder die Suche. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Hintergrundfarbe f\xFCr den Rahmen um hervorgehobene Bereiche.",'Hintergrundfarbe des hervorgehobenen Symbols, z. B. "Gehe zu Definition" oder "Gehe zu n\xE4chster/vorheriger". Die Farbe darf nicht undurchsichtig sein, um zugrunde liegende Dekorationen nicht zu verbergen.',"Hintergrundfarbe des Rahmens um hervorgehobene Symbole","Farbe des Cursors im Editor.","Hintergrundfarbe vom Editor-Cursor. Erlaubt die Anpassung der Farbe von einem Zeichen, welches von einem Block-Cursor \xFCberdeckt wird.","Farbe des prim\xE4ren Editorcursors, wenn mehrere Cursor vorhanden sind.","Die Hintergrundfarbe des prim\xE4ren Editorcursors, wenn mehrere Cursor vorhanden sind. Erlaubt die Anpassung der Farbe von einem Zeichen, welches von einem Block-Cursor \xFCberdeckt wird.","Farbe sekund\xE4rer Editorcursor, wenn mehrere Cursor vorhanden sind.","Die Hintergrundfarbe sekund\xE4rer Editorcursor, wenn mehrere Cursor vorhanden sind. Erlaubt die Anpassung der Farbe von einem Zeichen, welches von einem Block-Cursor \xFCberdeckt wird.","Farbe der Leerzeichen im Editor.","Zeilennummernfarbe im Editor.","Farbe der F\xFChrungslinien f\xFCr Einz\xFCge im Editor.",'"editorIndentGuide.background" ist veraltet. Verwenden Sie stattdessen "editorIndentGuide.background1".',"Farbe der F\xFChrungslinien f\xFCr Einz\xFCge im aktiven Editor.",'"editorIndentGuide.activeBackground" ist veraltet. Verwenden Sie stattdessen "editorIndentGuide.activeBackground1".',"Farbe der F\xFChrungslinien f\xFCr Einz\xFCge im Editor (1).","Farbe der F\xFChrungslinien f\xFCr Einz\xFCge im Editor (2).","Farbe der F\xFChrungslinien f\xFCr Einz\xFCge im Editor (3).","Farbe der F\xFChrungslinien f\xFCr Einz\xFCge im Editor (4).","Farbe der F\xFChrungslinien f\xFCr Einz\xFCge im Editor (5).","Farbe der F\xFChrungslinien f\xFCr Einz\xFCge im Editor (6).","Farbe der F\xFChrungslinien f\xFCr Einz\xFCge im aktiven Editor (1).","Farbe der F\xFChrungslinien f\xFCr Einz\xFCge im aktiven Editor (2).","Farbe der F\xFChrungslinien f\xFCr Einz\xFCge im aktiven Editor (3).","Farbe der F\xFChrungslinien f\xFCr Einz\xFCge im aktiven Editor (4).","Farbe der F\xFChrungslinien f\xFCr Einz\xFCge im aktiven Editor (5).","Farbe der F\xFChrungslinien f\xFCr Einz\xFCge im aktiven Editor (6).","Zeilennummernfarbe der aktiven Editorzeile.",'Die ID ist veraltet. Verwenden Sie stattdessen "editorLineNumber.activeForeground".',"Zeilennummernfarbe der aktiven Editorzeile.","Die Farbe der letzten Editor-Zeile, wenn \u201Eeditor.renderFinalNewline\u201C auf \u201Eabgeblendet\u201C festgelegt ist.","Farbe des Editor-Lineals.","Vordergrundfarbe der CodeLens-Links im Editor","Hintergrundfarbe f\xFCr zusammengeh\xF6rige Klammern","Farbe f\xFCr zusammengeh\xF6rige Klammern","Farbe des Rahmens f\xFCr das \xDCbersicht-Lineal.","Hintergrundfarbe des Editor-\xDCbersichtslineals.","Hintergrundfarbe der Editorleiste. Die Leiste enth\xE4lt die Glyphenr\xE4nder und die Zeilennummern.","Rahmenfarbe unn\xF6tigen (nicht genutzten) Quellcodes im Editor.",'Deckkraft des unn\xF6tigen (nicht genutzten) Quellcodes im Editor. "#000000c0" rendert z.B. den Code mit einer Deckkraft von 75%. Verwenden Sie f\xFCr Designs mit hohem Kontrast das Farbdesign "editorUnnecessaryCode.border", um unn\xF6tigen Code zu unterstreichen statt ihn abzublenden.',"Rahmenfarbe des Ghost-Texts im Editor.","Vordergrundfarbe des Ghost-Texts im Editor.","Hintergrundfarbe des Ghost-Texts im Editor.","\xDCbersichtslinealmarkerfarbe f\xFCr das Hervorheben von Bereichen. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","\xDCbersichtslineal-Markierungsfarbe f\xFCr Fehler.","\xDCbersichtslineal-Markierungsfarbe f\xFCr Warnungen.","\xDCbersichtslineal-Markierungsfarbe f\xFCr Informationen.","Vordergrundfarbe der Klammern (1). Erfordert die Aktivierung der Farbgebung des Klammerpaars.","Vordergrundfarbe der Klammern (2). Erfordert die Aktivierung der Farbgebung des Klammerpaars.","Vordergrundfarbe der Klammern (3). Erfordert die Aktivierung der Farbgebung des Klammerpaars.","Vordergrundfarbe der Klammern (4). Erfordert die Aktivierung der Farbgebung des Klammerpaars.","Vordergrundfarbe der Klammern (5). Erfordert die Aktivierung der Farbgebung des Klammerpaars.","Vordergrundfarbe der Klammern (6). Erfordert die Aktivierung der Farbgebung des Klammerpaars.","Vordergrundfarbe der unerwarteten Klammern.","Hintergrundfarbe der inaktiven Klammerpaar-Hilfslinien (1). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.","Hintergrundfarbe der inaktiven Klammerpaar-Hilfslinien (2). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.","Hintergrundfarbe der inaktiven Klammerpaar-Hilfslinien (3). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.","Hintergrundfarbe der inaktiven Klammerpaar-Hilfslinien (4). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.","Hintergrundfarbe der inaktiven Klammerpaar-Hilfslinien (5). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.","Hintergrundfarbe der inaktiven Klammerpaar-Hilfslinien (6). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.","Hintergrundfarbe der aktiven Klammerpaar-Hilfslinien (1). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.","Hintergrundfarbe der aktiven Klammerpaar-Hilfslinien (2). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.","Hintergrundfarbe der aktiven Klammerpaar-Hilfslinien (3). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.","Hintergrundfarbe der aktiven Klammerpaar-Hilfslinien (4). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.","Hintergrundfarbe der aktiven Klammerpaar-Hilfslinien (5). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.","Hintergrundfarbe der aktiven Klammerpaar-Hilfslinien (6). Erfordert das Aktivieren von Klammerpaar-Hilfslinien.","Rahmenfarbe, die zum Hervorheben von Unicode-Zeichen verwendet wird.","Hintergrundfarbe, die zum Hervorheben von Unicode-Zeichen verwendet wird.","Gibt an, ob der Editor-Text den Fokus besitzt (Cursor blinkt).","Gibt an, ob der Editor oder ein Editor-Widget den Fokus besitzt (z.\xA0B. ob der Fokus sich im Suchwidget befindet).","Gibt an, ob ein Editor oder eine Rich-Text-Eingabe den Fokus besitzt (Cursor blinkt).","Gibt an, ob der Editor schreibgesch\xFCtzt ist","Gibt an, ob der Kontext ein Diff-Editor ist.","Gibt an, ob der Kontext ein eingebetteter Diff-Editor ist.",null,"Gibt an, ob alle Dateien im Multi-Diff-Editor reduziert sind","Gibt an, ob der Diff-Editor \xC4nderungen aufweist.","Gibt an, ob ein verschobener Codeblock f\xFCr den Vergleich ausgew\xE4hlt wird.","Gibt an, ob der barrierefreie Diff-Viewer sichtbar ist.",'Gibt an, ob f\xFCr den Diff-Editor der Breakpoint f\xFCr das Rendern im Modus "Parallel" oder "Inline" erreicht wurde.',"Gibt an, ob der Inlinemodus aktiv ist.","Gibt an, ob \u201Ege\xE4ndert\u201C im Diff-Editor beschreibbar ist.","Gibt an, ob \u201Ege\xE4ndert\u201C im Diff-Editor beschreibbar ist.","Der URI des urspr\xFCnglichen Dokuments","Der URI des ge\xE4nderten Dokuments",'Gibt an, ob "editor.columnSelection" aktiviert ist.',"Gibt an, ob im Editor Text ausgew\xE4hlt ist.","Gibt an, ob der Editor \xFCber Mehrfachauswahl verf\xFCgt.","Gibt an, ob die TAB-TASTE den Fokus aus dem Editor verschiebt.","Gibt an, ob Hover im Editor sichtbar ist.","Gibt an, ob Daraufzeigen im Editor fokussiert ist.","Gibt an, ob der Fokus auf dem Fixierten Bildlauf liegt.","Gibt an, ob der Fixierte Bildlauf sichtbar ist.","Gibt an, ob der eigenst\xE4ndige Farbw\xE4hler sichtbar ist.","Gibt an, ob der eigenst\xE4ndige Farbw\xE4hler fokussiert ist.","Gibt an, ob der Editor Bestandteil eines gr\xF6\xDFeren Editors ist (z.\xA0B. Notebooks).","Der Sprachbezeichner des Editors.","Gibt an, ob der Editor \xFCber einen Vervollst\xE4ndigungselementanbieter verf\xFCgt.","Gibt an, ob der Editor \xFCber einen Codeaktionsanbieter verf\xFCgt.","Gibt an, ob der Editor \xFCber einen CodeLens-Anbieter verf\xFCgt.","Gibt an, ob der Editor \xFCber einen Definitionsanbieter verf\xFCgt.","Gibt an, ob der Editor \xFCber einen Deklarationsanbieter verf\xFCgt.","Gibt an, ob der Editor \xFCber einen Implementierungsanbieter verf\xFCgt.","Gibt an, ob der Editor \xFCber einen Typdefinitionsanbieter verf\xFCgt.","Gibt an, ob der Editor \xFCber einen Hoveranbieter verf\xFCgt.","Gibt an, ob der Editor \xFCber einen Dokumenthervorhebungsanbieter verf\xFCgt.","Gibt an, ob der Editor \xFCber einen Dokumentsymbolanbieter verf\xFCgt.","Gibt an, ob der Editor \xFCber einen Verweisanbieter verf\xFCgt.","Gibt an, ob der Editor \xFCber einen Umbenennungsanbieter verf\xFCgt.","Gibt an, ob der Editor \xFCber einen Signaturhilfeanbieter verf\xFCgt.","Gibt an, ob der Editor \xFCber einen Inlinehinweisanbieter verf\xFCgt.","Gibt an, ob der Editor \xFCber einen Dokumentformatierungsanbieter verf\xFCgt.","Gibt an, ob der Editor \xFCber einen Anbieter f\xFCr Dokumentauswahlformatierung verf\xFCgt.","Gibt an, ob der Editor \xFCber mehrere Dokumentformatierungsanbieter verf\xFCgt.","Gibt an, ob der Editor \xFCber mehrere Anbieter f\xFCr Dokumentauswahlformatierung verf\xFCgt.","Array","Boolescher Wert","Klasse","Konstante","Konstruktor","Enumeration","Enumerationsmember","Ereignis","Feld","Datei","Funktion","Schnittstelle","Schl\xFCssel","Methode","Modul","Namespace","NULL","Zahl","Objekt","Operator","Paket","Eigenschaft","Zeichenfolge","Struktur","Typparameter","Variable","{0} ({1})","Nur-Text","Eingabe","Entwickler: Token \xFCberpr\xFCfen","Gehe zu Zeile/Spalte...","Alle Anbieter f\xFCr den Schnellzugriff anzeigen","Befehlspalette","Befehle anzeigen und ausf\xFChren","Gehe zu Symbol...","Gehe zu Symbol nach Kategorie...","Editor-Inhalt","Zu Design mit hohem Kontrast umschalten","{0} Bearbeitungen in {1} Dateien durchgef\xFChrt","Mehr anzeigen ({0})","{0} Zeichen","Auswahlanker",'Anker festgelegt bei "{0}:{1}"',"Auswahlanker festlegen","Zu Auswahlanker wechseln","Auswahl von Anker zu Cursor","Auswahlanker abbrechen","\xDCbersichtslineal-Markierungsfarbe f\xFCr zusammengeh\xF6rige Klammern.","Gehe zu Klammer","Ausw\xE4hlen bis Klammer","Klammern entfernen","Gehe zu &&Klammer","Text ausw\xE4hlen und Klammern oder geschweifte Klammern einschlie\xDFen","Ausgew\xE4hlten Text nach links verschieben","Ausgew\xE4hlten Text nach rechts verschieben","Buchstaben austauschen","&&Ausschneiden","Ausschneiden","Ausschneiden","Ausschneiden","&&Kopieren","Kopieren","Kopieren","Kopieren","&&Einf\xFCgen","Einf\xFCgen","Einf\xFCgen","Einf\xFCgen","Mit Syntaxhervorhebung kopieren","Kopieren als","Kopieren als","Freigeben","Freigeben","Beim Anwenden der Code-Aktion ist ein unbekannter Fehler aufgetreten","Art der auszuf\xFChrenden Codeaktion","Legt fest, wann die zur\xFCckgegebenen Aktionen angewendet werden","Die erste zur\xFCckgegebene Codeaktion immer anwenden","Die erste zur\xFCckgegebene Codeaktion anwenden, wenn nur eine vorhanden ist","Zur\xFCckgegebene Codeaktionen nicht anwenden","Legt fest, ob nur bevorzugte Codeaktionen zur\xFCckgegeben werden sollen","Schnelle Problembehebung ...","Keine Codeaktionen verf\xFCgbar",'Keine bevorzugten Codeaktionen f\xFCr "{0}" verf\xFCgbar','Keine Codeaktionen f\xFCr "{0}" verf\xFCgbar',"Keine bevorzugten Codeaktionen verf\xFCgbar","Keine Codeaktionen verf\xFCgbar","Refactoring durchf\xFChren...",'Keine bevorzugten Refactorings f\xFCr "{0}" verf\xFCgbar','Keine Refactorings f\xFCr "{0}" verf\xFCgbar',"Keine bevorzugten Refactorings verf\xFCgbar","Keine Refactorings verf\xFCgbar","Quellaktion...",'Keine bevorzugten Quellaktionen f\xFCr "{0}" verf\xFCgbar','Keine Quellaktionen f\xFCr "{0}" verf\xFCgbar',"Keine bevorzugten Quellaktionen verf\xFCgbar","Keine Quellaktionen verf\xFCgbar","Importe organisieren","Keine Aktion zum Organisieren von Importen verf\xFCgbar","Alle korrigieren",'Aktion "Alle korrigieren" nicht verf\xFCgbar',"Automatisch korrigieren...","Keine automatischen Korrekturen verf\xFCgbar","Aktivieren/Deaktivieren Sie die Anzeige von Gruppenheadern im Codeaktionsmen\xFC.","Hiermit aktivieren/deaktivieren Sie die Anzeige der n\xE4chstgelegenen schnellen Problembehebung innerhalb einer Zeile, wenn derzeit keine Diagnose durchgef\xFChrt wird.","Aktivieren Sie das Ausl\xF6sen von {0}, wenn {1} auf {2} festgelegt ist. Codeaktionen m\xFCssen auf {3} festgelegt werden, um f\xFCr Fenster- und Fokus\xE4nderungen ausgel\xF6st zu werden.","Kontext: {0} in Zeile {1} und Spalte {2}.","Deaktivierte Elemente ausblenden","Deaktivierte Elemente anzeigen","Weitere Aktionen...","Schnelle Problembehebung","Extrahieren","Inline","Erneut generieren","Verschieben","Umgeben mit","Quellaktion","Symbol, das das Men\xFC \u201ECodeaktionen\u201C aus dem Bundsteg erzeugt, wenn kein Platz im Editor vorhanden ist.","Symbol, das das Men\xFC \u201ECodeaktionen\u201C aus dem Bundsteg erzeugt, wenn kein Platz im Editor vorhanden ist und eine schnelle Korrektur verf\xFCgbar ist.","Symbol, das das Men\xFC \u201ECodeaktionen\u201C aus dem Bundsteg erzeugt, wenn kein Platz im Editor vorhanden ist und ein KI-Fix verf\xFCgbar ist.","Symbol, das das Men\xFC \u201ECodeaktionen\u201C aus dem Bundsteg erzeugt, wenn kein Platz im Editor vorhanden ist und ein KI-Fix und eine schnelle Korrektur verf\xFCgbar sind.","Symbol, das das Men\xFC \u201ECodeaktionen\u201C aus dem Bundsteg erzeugt, wenn kein Platz im Editor vorhanden ist und ein KI-Fix und eine schnelle Korrektur verf\xFCgbar sind.","Ausf\xFChren: {0}","Zeigt Codeaktionen an. Bevorzugte Schnellkorrektur verf\xFCgbar ({0})","Codeaktionen anzeigen ({0})","Codeaktionen anzeigen","CodeLens-Befehle f\xFCr aktuelle Zeile anzeigen","Befehl ausw\xE4hlen",null,null,null,null,null,null,null,null,null,"Zeilenkommentar umschalten","Zeilenkommen&&tar umschalten","Zeilenkommentar hinzuf\xFCgen","Zeilenkommentar entfernen","Blockkommentar umschalten","&&Blockkommentar umschalten","Minimap","Zeichen rendern","Vertikale Gr\xF6\xDFe","Proportional","Ausf\xFCllen","Anpassen","Schieberegler","Maus \xFCber","Immer","Editor-Kontextmen\xFC anzeigen","Mit Cursor r\xFCckg\xE4ngig machen","Wiederholen mit Cursor",`Die Art der Einf\xFCgebearbeitung, f\xFCr die Sie das Einf\xFCgen versuchen.\r
Wenn mehrere Bearbeitungen f\xFCr diese Art vorhanden sind, zeigt der Editor eine Auswahl an. Wenn keine Bearbeitungen dieser Art vorhanden sind, zeigt der Editor eine Fehlermeldung an.`,"Einf\xFCgen als...","Als Text einf\xFCgen","Gibt an, ob das Einf\xFCgewidget angezeigt wird.","Einf\xFCgeoptionen anzeigen...","Es wurden keine Einf\xFCgebearbeitungen f\xFCr \u201E{0}\u201C gefunden.","Die Einf\xFCgebearbeitung wird aufgel\xF6st. Zum Abbrechen klicken","Einf\xFCgehandler werden ausgef\xFChrt. Klicken Sie hier, um den Vorgang abzubrechen und ein einfaches Einf\xFCgen auszuf\xFChren.","Einf\xFCgeaktion ausw\xE4hlen","Einf\xFCgehandler werden ausgef\xFChrt","Nur-Text einf\xFCgen","URI einf\xFCgen","URI einf\xFCgen","Pfade einf\xFCgen","Pfad einf\xFCgen","Relative Pfade einf\xFCgen","Relativen Pfad einf\xFCgen","HTML einf\xFCgen",null,"Gibt an, ob das Ablagewidget angezeigt wird.","Ablageoptionen anzeigen...","Drophandler werden ausgef\xFChrt. Klicken Sie hier, um den Vorgang abzubrechen.",`Fehler beim Aufl\xF6sen der Bearbeitung "{0}":\r
{1}`,`Fehler beim Anwenden der Bearbeitung "{0}":\r
{1}`,'Gibt an, ob der Editor einen abbrechbaren Vorgang ausf\xFChrt, z.\xA0B. "Verweisvorschau".',"Die Datei ist zu gro\xDF, um einen Vorgang zum Ersetzen aller Elemente auszuf\xFChren.","Suchen","&&Suchen","Mit Argumenten suchen","Mit Auswahl suchen","Weitersuchen","Vorheriges Element suchen","Zu \xDCbereinstimmung wechseln\xA0...","Keine \xDCbereinstimmungen. Versuchen Sie, nach etwas anderem zu suchen.","Geben Sie eine Zahl ein, um zu einer bestimmten \xDCbereinstimmung zu wechseln (zwischen\xA01 und {0}).","Zahl zwischen\xA01 und {0} eingeben","Zahl zwischen\xA01 und {0} eingeben","N\xE4chste Auswahl suchen","Vorherige Auswahl suchen","Ersetzen","&&Ersetzen","Symbol f\xFCr die Anzeige, dass das Editor-Such-Widget zugeklappt wurde.","Symbol f\xFCr die Anzeige, dass das Editor-Such-Widget aufgeklappt wurde.",'Symbol f\xFCr "In Auswahl suchen" im Editor-Such-Widget.','Symbol f\xFCr "Ersetzen" im Editor-Such-Widget.','Symbol f\xFCr "Alle ersetzen" im Editor-Such-Widget.','Symbol f\xFCr "Vorheriges Element suchen" im Editor-Such-Widget.','Symbol f\xFCr "N\xE4chstes Element suchen" im Editor-Such-Widget.',"Suchen/Ersetzen","Suchen","Suchen","Vorherige \xDCbereinstimmung","N\xE4chste \xDCbereinstimmung","In Auswahl suchen","Schlie\xDFen","Ersetzen","Ersetzen","Ersetzen","Alle ersetzen","Ersetzen umschalten","Nur die ersten {0} Ergebnisse wurden hervorgehoben, aber alle Suchoperationen werden auf dem gesamten Text durchgef\xFChrt.","{0} von {1}","Keine Ergebnisse","{0} gefunden",'{0} f\xFCr "{1}" gefunden','{0} f\xFCr "{1}" gefunden, bei {2}','{0} f\xFCr "{1}" gefunden','STRG+EINGABE f\xFCgt jetzt einen Zeilenumbruch ein, statt alles zu ersetzen. Sie k\xF6nnen die Tastenzuordnung f\xFCr "editor.action.replaceAll" \xE4ndern, um dieses Verhalten au\xDFer Kraft zu setzen.',"Auffalten","Faltung rekursiv aufheben","Falten","Einklappung umschalten","Rekursiv falten","Rekursiv falten umschalten","Alle Blockkommentare falten","Alle Regionen falten","Alle Regionen auffalten","Alle bis auf ausgew\xE4hlte falten","Alle bis auf ausgew\xE4hlte auffalten","Alle falten","Alle auffalten","Zur \xFCbergeordneten Reduzierung wechseln","Zum vorherigen Faltbereich wechseln","Zum n\xE4chsten Faltbereich wechseln","Faltungsbereich aus Auswahl erstellen","Manuelle Faltbereiche entfernen","Faltebene {0}","Hintergrundfarbe hinter gefalteten Bereichen. Die Farbe darf nicht deckend sein, sodass zugrunde liegende Dekorationen nicht ausgeblendet werden.","Farbe des reduzierten Texts nach der ersten Zeile eines gefalteten Bereichs.","Farbe des Faltsteuerelements im Editor-Bundsteg.","Symbol f\xFCr aufgeklappte Bereiche im Editor-Glyphenrand.","Symbol f\xFCr zugeklappte Bereiche im Editor-Glyphenrand.","Symbol f\xFCr manuell reduzierte Bereiche im Glyphenrand des Editors.","Symbol f\xFCr manuell erweiterte Bereiche im Glyphenrand des Editors.","Klicken Sie hier, um den Bereich zu erweitern.","Klicken Sie hier, um den Bereich zu reduzieren.","Schriftgrad des Editors erh\xF6hen","Schriftgrad des Editors verringern","Editor-Schriftgrad zur\xFCcksetzen","Dokument formatieren","Auswahl formatieren","Gehe zu n\xE4chstem Problem (Fehler, Warnung, Information)","Symbol f\xFCr den Marker zum Wechseln zum n\xE4chsten Element.","Gehe zu vorigem Problem (Fehler, Warnung, Information)","Symbol f\xFCr den Marker zum Wechseln zum vorherigen Element.","Gehe zu dem n\xE4chsten Problem in den Dateien (Fehler, Warnung, Info)","N\xE4chstes &&Problem","Gehe zu dem vorherigen Problem in den Dateien (Fehler, Warnung, Info)","Vorheriges &&Problem","Fehler","Warnung","Info","Hinweis","{0} bei {1}. ","{0} von {1} Problemen","{0} von {1} Problemen","Editormarkierung: Farbe bei Fehler des Navigationswidgets.","Hintergrund der Fehler\xFCberschrift des Markernavigationswidgets im Editor.","Editormarkierung: Farbe bei Warnung des Navigationswidgets.","Hintergrund der Warnungs\xFCberschrift des Markernavigationswidgets im Editor.","Editormarkierung: Farbe bei Information des Navigationswidgets.","Hintergrund der Informations\xFCberschrift des Markernavigationswidgets im Editor.","Editormarkierung: Hintergrund des Navigationswidgets.","Vorschau","Definitionen",'Keine Definition gefunden f\xFCr "{0}".',"Keine Definition gefunden","Gehe &&zu Definition","Deklarationen",'Keine Deklaration f\xFCr "{0}" gefunden.',"Keine Deklaration gefunden.","Gehe zu &&Deklaration",'Keine Deklaration f\xFCr "{0}" gefunden.',"Keine Deklaration gefunden.","Typdefinitionen",'Keine Typendefinition gefunden f\xFCr "{0}"',"Keine Typendefinition gefunden","Zur &&Typdefinition wechseln","Implementierungen",'Keine Implementierung gefunden f\xFCr "{0}"',"Keine Implementierung gefunden","Gehe zu &&Implementierungen",'F\xFCr "{0}" wurden keine Verweise gefunden.',"Keine Referenzen gefunden","Gehe zu &&Verweisen","Verweise","Verweise","Speicherorte",'Keine Ergebnisse f\xFCr "{0}"',"Verweise","Gehe zu Definition","Definition an der Seite \xF6ffnen","Definition einsehen","Zur Deklaration wechseln","Vorschau f\xFCr Deklaration anzeigen","Zur Typdefinition wechseln","Vorschau der Typdefinition anzeigen","Gehe zu Implementierungen","Vorschau f\xFCr Implementierungen anzeigen","Gehe zu Verweisen","Vorschau f\xFCr Verweise anzeigen","Zum beliebigem Symbol wechseln","Klicken Sie, um {0} Definitionen anzuzeigen.",'Gibt an, ob die Verweisvorschau sichtbar ist, z.\xA0B. "Verweisvorschau" oder "Definition einsehen".',"Wird geladen...","{0} ({1})","{0} Verweise","{0} Verweis","Verweise","Keine Vorschau verf\xFCgbar.","Keine Ergebnisse","Verweise","in {0} in Zeile {1} in Spalte {2}","{0} in {1} in Zeile {2} in Spalte {3}","1 Symbol in {0}, vollst\xE4ndiger Pfad {1}","{0} Symbole in {1}, vollst\xE4ndiger Pfad {2}","Es wurden keine Ergebnisse gefunden.","1 Symbol in {0} gefunden","{0} Symbole in {1} gefunden","{0} Symbole in {1} Dateien gefunden","Gibt an, ob Symbolpositionen vorliegen, bei denen die Navigation nur \xFCber die Tastatur m\xF6glich ist.","Symbol {0} von {1}, {2} f\xFCr n\xE4chstes","Symbol {0} von {1}","Ausf\xFChrlichkeitsgrad beim Daraufzeigen erh\xF6hen","Ausf\xFChrlichkeitsgrad beim Daraufzeigen verringern","Anzeigen oder Fokus beim Daraufzeigen","Beim Daraufzeigen wird der Fokus nicht automatisch verwendet.","Beim Daraufzeigen wird nur dann den Fokus erhalten, wenn er bereits sichtbar ist.","Beim Daraufzeigen wird automatisch der Fokus erhalten, wenn er angezeigt wird.","Definitionsvorschauhover anzeigen","Bildlauf nach oben beim Daraufzeigen","Bildlauf nach unten beim Daraufzeigen","Bildlauf nach links beim Daraufzeigen","Bildlauf nach rechts beim Daraufzeigen","Eine Seite nach oben beim Daraufzeigen","Eine Seite nach unten beim Daraufzeigen","Gehe nach oben beim Daraufzeigen","Gehe nach unten beim Daraufzeigen","Editor-Mauszeiger anzeigen oder fokussieren, um Dokumentation, Verweise und anderen Inhalt f\xFCr ein Symbol an der aktuellen Cursorposition anzuzeigen oder zu fokussieren.","Definitionsvorschau-Mauszeiger im Editor anzeigen","Beim Daraufzeigen auf den Editor nach oben scrollen.","Beim Daraufzeigen auf den Editor nach unten. scrollen.","Bildlauf nach links beim Daraufzeigen auf den Editor.","Bildlauf nach rechts beim Daraufzeigen auf den Editor.","Eine Seite nach oben beim Daraufzeigen auf den Editor.","Eine Seite nach unten beim Daraufzeigen auf den Editor.","Beim Daraufzeigen auf den Editor zum oberen Rand navigieren.","Beim Daraufzeigen auf den Editor zum unteren Rand navigieren.","Symbol zum Erh\xF6hen der Ausf\xFChrlichkeit beim Daraufzeigen.","Symbol zum Verringern der Ausf\xFChrlichkeit beim Daraufzeigen.","Wird geladen...","Das Rendering langer Zeilen wurde aus Leistungsgr\xFCnden angehalten. Dies kann \xFCber \u201Eeditor.stopRenderingLineAfter\u201C konfiguriert werden.","Die Tokenisierung wird bei langen Zeilen aus Leistungsgr\xFCnden \xFCbersprungen. Dies kann \xFCber \u201Eeditor.maxTokenizationLineLength\u201C konfiguriert werden.","Ausf\xFChrlichkeit beim Daraufzeigen erh\xF6hen ({0})","Ausf\xFChrlichkeit beim Daraufzeigen erh\xF6hen","Ausf\xFChrlichkeit beim Daraufzeigen verringern ({0})","Ausf\xFChrlichkeit beim Daraufzeigen verringern","Problem anzeigen","Keine Schnellkorrekturen verf\xFCgbar","Es wird nach Schnellkorrekturen gesucht...","Keine Schnellkorrekturen verf\xFCgbar","Schnelle Problembehebung ...","Einzug in Leerzeichen konvertieren","Einzug in Tabstopps konvertieren","Konfigurierte Tabulatorgr\xF6\xDFe","Standardregisterkartengr\xF6\xDFe","Aktuelle Registerkartengr\xF6\xDFe","Tabulatorgr\xF6\xDFe f\xFCr aktuelle Datei ausw\xE4hlen","Einzug mithilfe von Tabstopps","Einzug mithilfe von Leerzeichen","Anzeigegr\xF6\xDFe der Registerkarte \xE4ndern","Einzug aus Inhalt erkennen","Neuen Einzug f\xFCr Zeilen festlegen","Gew\xE4hlte Zeilen zur\xFCckziehen","Tabulatoreinzug in Leerzeichen umwandeln.","Leerraumeinzug in Tabulator konvertieren.","Einzug mit Tabulator verwenden.","Einzug mit Leerzeichen verwenden.","Leerzeichengr\xF6\xDFe entsprechend der Tabulatorgr\xF6\xDFe \xE4ndern.","Einzug aus dem Inhalt erkennen.","Die Zeilen des Editors erneut einr\xFCcken.","Die ausgew\xE4hlten Zeilen des Editors erneut einr\xFCcken.","Zum Einf\xFCgen doppelklicken","BEFEHL + Klicken","STRG + Klicken","OPTION + Klicken","ALT + Klicken","Wechseln Sie zu Definition ({0}), klicken Sie mit der rechten Maustaste, um weitere Informationen zu finden.","Gehe zu Definition ({0})","Befehl ausf\xFChren","N\xE4chsten Inline-Vorschlag anzeigen","Vorherigen Inline-Vorschlag anzeigen","Inline-Vorschlag ausl\xF6sen","N\xE4chstes Wort des Inline-Vorschlags annehmen","Wort annehmen","N\xE4chste Zeile des Inlinevorschlags akzeptieren","Zeile annehmen","Inline-Vorschlag annehmen","Akzeptieren","Inlinevorschlag ausblenden","Symbolleiste immer anzeigen","Gibt an, ob ein Inline-Vorschlag sichtbar ist.","Gibt an, ob der Inline-Vorschlag mit Leerzeichen beginnt.","Ob der Inline-Vorschlag mit Leerzeichen beginnt, das kleiner ist als das, was durch die Tabulatortaste eingef\xFCgt werden w\xFCrde","Gibt an, ob Vorschl\xE4ge f\xFCr den aktuellen Vorschlag unterdr\xFCckt werden sollen","\xDCberpr\xFCfen Sie dies in der barrierefreien Ansicht ({0}).","Vorschlag:","Symbol f\xFCr die Anzeige des n\xE4chsten Parameterhinweises.","Symbol f\xFCr die Anzeige des vorherigen Parameterhinweises.","{0} ({1})","Zur\xFCck","Weiter",null,null,null,null,null,null,null,null,"Durch vorherigen Wert ersetzen","Durch n\xE4chsten Wert ersetzen","Zeilenauswahl erweitern","Zeile nach oben kopieren","Zeile nach oben &&kopieren","Zeile nach unten kopieren","Zeile nach unten ko&&pieren","Auswahl duplizieren","&&Auswahl duplizieren","Zeile nach oben verschieben","Zeile nach oben &&verschieben","Zeile nach unten verschieben","Zeile nach &&unten verschieben","Zeilen aufsteigend sortieren","Zeilen absteigend sortieren","Doppelte Zeilen l\xF6schen","Nachgestelltes Leerzeichen k\xFCrzen","Zeile l\xF6schen","Zeileneinzug","Zeile ausr\xFCcken","Zeile oben einf\xFCgen","Zeile unten einf\xFCgen","Alle \xFCbrigen l\xF6schen","Alle rechts l\xF6schen","Zeilen verkn\xFCpfen","Zeichen um den Cursor herum transponieren","In Gro\xDFbuchstaben umwandeln","In Kleinbuchstaben umwandeln","In gro\xDFe Anfangsbuchstaben umwandeln","In Snake Case umwandeln","In CamelCase umwandeln","In Pascal-Pascal-Schreibweise transformieren","In kebab-case umwandeln","Verkn\xFCpfte Bearbeitung starten","Hintergrundfarbe, wenn der Editor automatisch nach Typ umbenennt.","Fehler beim \xD6ffnen dieses Links, weil er nicht wohlgeformt ist: {0}","Fehler beim \xD6ffnen dieses Links, weil das Ziel fehlt.","Befehl ausf\xFChren","Link folgen","BEFEHL + Klicken","STRG + Klicken","OPTION + Klicken","alt + klicken",'F\xFChren Sie den Befehl "{0}" aus.',"Link \xF6ffnen","Gibt an, ob der Editor zurzeit eine Inlinenachricht anzeigt.","Hinzugef\xFCgter Cursor: {0}","Hinzugef\xFCgte Cursor: {0}","Cursor oberhalb hinzuf\xFCgen","Cursor oberh&&alb hinzuf\xFCgen","Cursor unterhalb hinzuf\xFCgen","Cursor unterhal&&b hinzuf\xFCgen","Cursor an Zeilenenden hinzuf\xFCgen","C&&ursor an Zeilenenden hinzuf\xFCgen","Cursor am Ende hinzuf\xFCgen","Cursor am Anfang hinzuf\xFCgen","Auswahl zur n\xE4chsten \xDCbereinstimmungssuche hinzuf\xFCgen","&&N\xE4chstes Vorkommen hinzuf\xFCgen","Letzte Auswahl zu vorheriger \xDCbereinstimmungssuche hinzuf\xFCgen","Vo&&rheriges Vorkommen hinzuf\xFCgen","Letzte Auswahl in n\xE4chste \xDCbereinstimmungssuche verschieben","Letzte Auswahl in vorherige \xDCbereinstimmungssuche verschieben","Alle Vorkommen ausw\xE4hlen und \xDCbereinstimmung suchen","Alle V&&orkommen ausw\xE4hlen","Alle Vorkommen \xE4ndern","Fokus auf n\xE4chsten Cursor","Fokussiert den n\xE4chsten Cursor","Fokus auf vorherigen Cursor","Fokussiert den vorherigen Cursor","Parameterhinweise ausl\xF6sen","Symbol f\xFCr die Anzeige des n\xE4chsten Parameterhinweises.","Symbol f\xFCr die Anzeige des vorherigen Parameterhinweises.","{0}, Hinweis","Vordergrundfarbe des aktiven Elements im Parameterhinweis.","Gibt an, ob der aktuelle Code-Editor in der Vorschau eingebettet ist.","Schlie\xDFen","Hintergrundfarbe des Titelbereichs der Peek-Ansicht.","Farbe des Titels in der Peek-Ansicht.","Farbe der Titelinformationen in der Peek-Ansicht.","Farbe der Peek-Ansichtsr\xE4nder und des Pfeils.","Hintergrundfarbe der Ergebnisliste in der Peek-Ansicht.","Vordergrundfarbe f\xFCr Zeilenknoten in der Ergebnisliste der Peek-Ansicht.","Vordergrundfarbe f\xFCr Dateiknoten in der Ergebnisliste der Peek-Ansicht.","Hintergrundfarbe des ausgew\xE4hlten Eintrags in der Ergebnisliste der Peek-Ansicht.","Vordergrundfarbe des ausgew\xE4hlten Eintrags in der Ergebnisliste der Peek-Ansicht.","Hintergrundfarbe des Peek-Editors.","Hintergrundfarbe der Leiste im Peek-Editor.","Die Hintergrundfarbe f\xFCr den \u201ESticky\u201C-Bildlaufeffekt im Editor f\xFCr die \u201EPeek\u201C-Ansicht.","Farbe f\xFCr \xDCbereinstimmungsmarkierungen in der Ergebnisliste der Peek-Ansicht.","Farbe f\xFCr \xDCbereinstimmungsmarkierungen im Peek-Editor.","Rahmen f\xFCr \xDCbereinstimmungsmarkierungen im Peek-Editor.","Vordergrundfarbe des Platzhaltertexts im Editor.","\xD6ffnen Sie zuerst einen Text-Editor, um zu einer Zeile zu wechseln.","Wechseln Sie zu Zeile {0} und Zeichen {1}.","Zu Zeile {0} wechseln.","Aktuelle Zeile: {0}, Zeichen: {1}. Geben Sie eine Zeilennummer zwischen 1 und {2} ein, zu der Sie navigieren m\xF6chten.","Aktuelle Zeile: {0}, Zeichen: {1}. Geben Sie eine Zeilennummer ein, zu der Sie navigieren m\xF6chten.","\xD6ffnen Sie zun\xE4chst einen Text-Editor mit Symbolinformationen, um zu einem Symbol zu navigieren.","Der aktive Text-Editor stellt keine Symbolinformationen bereit.","Keine \xFCbereinstimmenden Editorsymbole.","Keine Editorsymbole.","An der Seite \xF6ffnen","Unten \xF6ffnen","Symbole ({0})","Eigenschaften ({0})","Methoden ({0})","Funktionen ({0})","Konstruktoren ({0})","Variablen ({0})","Klassen ({0})","Strukturen ({0})","Ereignisse ({0})","Operatoren ({0})","Schnittstellen ({0})","Namespaces ({0})","Pakete ({0})","Typparameter ({0})","Module ({0})","Eigenschaften ({0})","Enumerationen ({0})","Enumerationsmember ({0})","Zeichenfolgen ({0})","Dateien ({0})","Arrays ({0})","Zahlen ({0})","Boolesche Werte ({0})","Objekte ({0})","Schl\xFCssel ({0})","Felder ({0})","Konstanten ({0})","Bearbeitung von schreibgesch\xFCtzter Eingabe nicht m\xF6glich","Ein Bearbeiten ist im schreibgesch\xFCtzten Editor nicht m\xF6glich","Kein Ergebnis.","Ein unbekannter Fehler ist beim Aufl\xF6sen der Umbenennung eines Ortes aufgetreten.","'{0}' wird in '{1}' umbenannt","{0} wird in {1} umbenannt.",'"{0}" erfolgreich in "{1}" umbenannt. Zusammenfassung: {2}',"Die rename-Funktion konnte die \xC4nderungen nicht anwenden.","Die rename-Funktion konnte die \xC4nderungen nicht berechnen.","Symbol umbenennen","M\xF6glichkeit aktivieren/deaktivieren, \xC4nderungen vor dem Umbenennen als Vorschau anzeigen zu lassen","N\xE4chsten Umbenennungsvorschlag fokussieren","Vorherigen Umbenennungsvorschlag fokussieren","Gibt an, ob das Widget zum Umbenennen der Eingabe sichtbar ist.","Gibt an, ob das Widget zum Umbenennen der Eingabe fokussiert ist.","{0} zur Umbenennung, {1} zur Vorschau","{0} Vorschl\xE4ge zum Umbenennen erhalten","Benennen Sie die Eingabe um. Geben Sie einen neuen Namen ein, und dr\xFCcken Sie die EINGABETASTE, um den Commit auszuf\xFChren.","Vorschl\xE4ge f\xFCr neuen Namen generieren","Abbrechen","Auswahl aufklappen","Auswahl &&erweitern","Markierung verkleinern","Au&&swahl verkleinern","Gibt an, ob der Editor sich zurzeit im Schnipselmodus befindet.","Gibt an, ob ein n\xE4chster Tabstopp im Schnipselmodus vorhanden ist.","Gibt an, ob ein vorheriger Tabstopp im Schnipselmodus vorhanden ist.","Zum n\xE4chsten Platzhalter wechseln...","Sonntag","Montag","Dienstag","Mittwoch","Donnerstag","Freitag","Samstag","So","Mo","Di","Mi","Do","Fr","Sa","Januar","Februar","M\xE4rz","April","Mai","Juni","Juli","August","September","Oktober","November","Dezember","Jan","Feb","M\xE4r","Apr","Mai","Jun","Jul","Aug","Sep","Okt","Nov","Dez","Fixierten Bildlauf des Editors &&umschalten","Fixierter Bildlauf","&&Fixierter Bildlauf","&&Fokus fixierter Bildlauf","Fixierten Bildlauf f\xFCr Editor umschalten","Fixierten Bildlauf des Editors umschalten/aktivieren, der die geschachtelten Bereiche am oberen Rand des Viewports anzeigt","Auf fixierten Bildlauf des Editors fokussieren","N\xE4chste fixierte Bildlaufzeile des Editors ausw\xE4hlen","Vorherige fixierte Bildlaufzeile ausw\xE4hlen","Zur fokussierten fixierten Bildlaufzeile wechseln","Editor ausw\xE4hlen","Gibt an, ob ein Vorschlag fokussiert ist","Gibt an, ob Vorschlagsdetails sichtbar sind.","Gibt an, ob mehrere Vorschl\xE4ge zur Auswahl stehen.","Gibt an, ob das Einf\xFCgen des aktuellen Vorschlags zu einer \xC4nderung f\xFChrt oder ob bereits alles eingegeben wurde.","Gibt an, ob Vorschl\xE4ge durch Dr\xFCcken der EINGABETASTE eingef\xFCgt werden.","Gibt an, ob der aktuelle Vorschlag Verhalten zum Einf\xFCgen und Ersetzen aufweist.","Gibt an, ob Einf\xFCgen oder Ersetzen als Standardverhalten verwendet wird.","Gibt an, ob der aktuelle Vorschlag die Aufl\xF6sung weiterer Details unterst\xFCtzt.",'Das Akzeptieren von "{0}" ergab {1} zus\xE4tzliche Bearbeitungen.',"Vorschlag ausl\xF6sen","Einf\xFCgen","Einf\xFCgen","Ersetzen","Ersetzen","Einf\xFCgen","Weniger anzeigen","Mehr anzeigen","Gr\xF6\xDFe des Vorschlagswidgets zur\xFCcksetzen","Hintergrundfarbe des Vorschlagswidgets.","Rahmenfarbe des Vorschlagswidgets.","Vordergrundfarbe des Vorschlagswidgets.","Die Vordergrundfarbe des ausgew\xE4hlten Eintrags im Vorschlagswidget.","Die Vordergrundfarbe des Symbols des ausgew\xE4hlten Eintrags im Vorschlagswidget.","Hintergrundfarbe des ausgew\xE4hlten Eintrags im Vorschlagswidget.","Farbe der Trefferhervorhebung im Vorschlagswidget.","Die Farbe des Treffers wird im Vorschlagswidget hervorgehoben, wenn ein Element fokussiert wird.","Vordergrundfarbe des Status des Vorschlagswidgets.","Wird geladen...","Keine Vorschl\xE4ge.","Vorschlagen","{0} {1}, {2}","{0} {1}","{0}, {1}","{0}, Dokumente: {1}","Schlie\xDFen","Wird geladen...","Symbol f\xFCr weitere Informationen im Vorschlags-Widget.","Weitere Informationen","Die Vordergrundfarbe f\xFCr Arraysymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr boolesche Symbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Klassensymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Farbsymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr konstante Symbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Konstruktorsymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Enumeratorsymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Enumeratormembersymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Ereignissymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Feldsymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Dateisymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Ordnersymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Funktionssymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Schnittstellensymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Schl\xFCsselsymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Schl\xFCsselwortsymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Methodensymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Modulsymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Namespacesymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr NULL-Symbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Zahlensymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Objektsymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Operatorsymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Paketsymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Eigenschaftensymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Referenzsymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Codeschnipselsymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Zeichenfolgensymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Struktursymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Textsymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Typparametersymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr Einheitensymbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Die Vordergrundfarbe f\xFCr variable Symbole. Diese Symbole werden in den Widgets f\xFCr Gliederung, Breadcrumbs und Vorschl\xE4ge angezeigt.","Beim Dr\xFCcken auf Tab wird der Fokus jetzt auf das n\xE4chste fokussierbare Element verschoben","Beim Dr\xFCcken von Tab wird jetzt das Tabulator-Zeichen eingef\xFCgt","TAB-Umschalttaste verschiebt Fokus","Bestimmt, ob die Tabulatortaste den Fokus um die Workbench verschiebt oder das Tabulatorzeichen im aktuellen Editor einf\xFCgt. Dies wird auch als Tabstopp, Tabulatornavigation oder Tabulatorfokusmodus bezeichnet.","Entwickler: Force Retokenize","Symbol, das mit einer Warnmeldung im Erweiterungs-Editor angezeigt wird.","Dieses Dokument enth\xE4lt viele nicht einfache ASCII-Unicode-Zeichen.","Dieses Dokument enth\xE4lt viele mehrdeutige Unicode-Zeichen.","Dieses Dokument enth\xE4lt viele unsichtbare Unicode-Zeichen.","Konfigurieren der Optionen f\xFCr die Unicode-Hervorhebung","Das Zeichen {0} kann mit dem Zeichen {1} verwechselt werden, was im Quellcode h\xE4ufiger vorkommt.","Das Zeichen {0} kann mit dem Zeichen {1} verwechselt werden, was im Quellcode h\xE4ufiger vorkommt.","Das Zeichen {0} ist nicht sichtbar.","Das Zeichen {0} ist kein einfaches ASCII-Zeichen.","Einstellungen anpassen","Hervorhebung in Kommentaren deaktivieren","Deaktivieren der Hervorhebung von Zeichen in Kommentaren","Hervorhebung in Zeichenfolgen deaktivieren","Deaktivieren der Hervorhebung von Zeichen in Zeichenfolgen","Mehrdeutige Hervorhebung deaktivieren","Deaktivieren der Hervorhebung von mehrdeutigen Zeichen","Unsichtbare Hervorhebung deaktivieren","Deaktivieren der Hervorhebung unsichtbarer Zeichen","Nicht-ASCII-Hervorhebung deaktivieren","Deaktivieren der Hervorhebung von nicht einfachen ASCII-Zeichen","Ausschlussoptionen anzeigen","{0} (unsichtbares Zeichen) von der Hervorhebung ausschlie\xDFen","{0} nicht hervorheben","Unicodezeichen zulassen, die in der Sprache \u201E{0}\u201C h\xE4ufiger vorkommen.","Ungew\xF6hnliche Zeilentrennzeichen","Ungew\xF6hnliche Zeilentrennzeichen erkannt",`Die Datei "{0}" enth\xE4lt mindestens ein ungew\xF6hnliches Zeilenabschlusszeichen, z. B. Zeilentrennzeichen (LS) oder Absatztrennzeichen (PS).\r
\r
Es wird empfohlen, sie aus der Datei zu entfernen. Dies kann \xFCber "editor.unusualLineTerminators" konfiguriert werden.`,"&&Ungew\xF6hnliche Zeilenabschlusszeichen entfernen","Ignorieren","Hintergrundfarbe eines Symbols beim Lesezugriff, z.B. beim Lesen einer Variablen. Die Farbe darf nicht deckend sein, damit sie nicht die zugrunde liegenden Dekorationen verdeckt.","Hintergrundfarbe eines Symbols bei Schreibzugriff, z.B. beim Schreiben in eine Variable. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Die Hintergrundfarbe eines Textteils f\xFCr ein Symbol. Die Farbe darf nicht deckend sein, um zugrunde liegende Dekorationen nicht auszublenden.","Randfarbe eines Symbols beim Lesezugriff, wie etwa beim Lesen einer Variablen.","Randfarbe eines Symbols beim Schreibzugriff, wie etwa beim Schreiben einer Variablen.","Die Rahmenfarbe eines Textteils f\xFCr ein Symbol.","\xDCbersichtslinealmarkerfarbd f\xFCr das Hervorheben von Symbolen. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","\xDCbersichtslinealmarkerfarbe f\xFCr Symbolhervorhebungen bei Schreibzugriff. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Die Markierungsfarbe des \xDCbersichtslineals eines Textteils f\xFCr ein Symbol. Die Farbe darf nicht deckend sein, um zugrunde liegende Dekorationen nicht auszublenden.","Gehe zur n\xE4chsten Symbolhervorhebungen","Gehe zur vorherigen Symbolhervorhebungen","Symbol-Hervorhebung ein-/ausschalten","Wort l\xF6schen","Fehler an Position","Fehler","Warnung an Position","Warnung","Fehler in der Zeile","Fehler in Zeile","Warnung in der Zeile","Warnung in Zeile","Gefalteter Bereich in der Zeile","Gefaltet","Haltepunkt in der Zeile","Haltepunkt","Inlinevorschlag in der Zeile","Terminale schnelle Problembehebung","Schnelle Problembehebung","Debugger auf Haltepunkt beendet","Haltepunkt","Keine Inlay-Hinweise in der Zeile","Keine Inlay-Hinweise","Aufgabe abgeschlossen","Aufgabe abgeschlossen","Aufgabe fehlgeschlagen","Fehler bei Aufgabe","Terminalbefehl fehlgeschlagen","Befehl fehlgeschlagen","Terminalbefehl erfolgreich","Befehl erfolgreich","Terminalglocke","Terminalglocke","Notebookzelle abgeschlossen","Notebookzelle abgeschlossen","Notebookzelle fehlgeschlagen","Notebookzelle fehlgeschlagen","Vergleichslinie eingef\xFCgt","Vergleichslinie gel\xF6scht","Vergleichslinie ge\xE4ndert","Chatanfrage gesendet","Chatanfrage gesendet","Chatantwort empfangen","Status","Status","L\xF6schen","L\xF6schen","Speichern","Speichern","Format","Formatieren","Sprachaufzeichnung gestartet","Sprachaufzeichnung beendet","Ansehen","Hilfe","Test","Datei","Einstellungen","Entwickler","{0} ({1})","{0} ({1})",`{0}\r
[{1}] {2}`,"{1} bis {0}","{0} ({1})","Ausblenden","Men\xFC zur\xFCcksetzen",'"{0}" ausblenden',"Tastenzuordnung konfigurieren","{0} zum Anwenden, {1} f\xFCr Vorschau","{0} zum Anwenden","{0} deaktiviert, Grund: {1}","Aktionswidget","Hintergrundfarbe f\xFCr umgeschaltete Aktionselemente in der Aktionsleiste.","Gibt an, ob die Aktionswidgetliste sichtbar ist.","Codeaktionswidget ausblenden","Vorherige Aktion ausw\xE4hlen","N\xE4chste Aktion ausw\xE4hlen","Ausgew\xE4hlte Aktion akzeptieren","Vorschau f\xFCr ausgew\xE4hlte Elemente anzeigen","Au\xDFerkraftsetzungen f\xFCr die Standardsprachkonfiguration","Konfigurieren Sie Einstellungen, die f\xFCr die Sprache {0} \xFCberschrieben werden sollen.","Zu \xFCberschreibende Editor-Einstellungen f\xFCr eine Sprache konfigurieren.","Diese Einstellung unterst\xFCtzt keine sprachspezifische Konfiguration.","Zu \xFCberschreibende Editor-Einstellungen f\xFCr eine Sprache konfigurieren.","Diese Einstellung unterst\xFCtzt keine sprachspezifische Konfiguration.","Eine leere Eigenschaft kann nicht registriert werden.",'"{0}" kann nicht registriert werden. Stimmt mit dem Eigenschaftsmuster "\\\\[.*\\\\]$" zum Beschreiben sprachspezifischer Editor-Einstellungen \xFCberein. Verwenden Sie den Beitrag "configurationDefaults".','{0}" kann nicht registriert werden. Diese Eigenschaft ist bereits registriert.','"{0}" kann nicht registriert werden. Die zugeordnete Richtlinie {1} ist bereits bei {2} registriert.',"Ein Befehl, der Informationen zu Kontextschl\xFCsseln zur\xFCckgibt","Leerer Kontextschl\xFCsselausdruck","Haben Sie vergessen, einen Ausdruck zu schreiben? Sie k\xF6nnen auch \u201Efalse\u201C oder \u201Etrue\u201C festlegen, um immer auf \u201Efalse\u201C oder \u201Etrue\u201C auszuwerten.","\u201Ein\u201C nach \u201Enot\u201C.","schlie\xDFende Klammer \u201E)\u201C","Unerwartetes Token","Haben Sie vergessen, && oder || vor dem Token einzuf\xFCgen?","Unerwartetes Ende des Ausdrucks.","Haben Sie vergessen, einen Kontextschl\xFCssel zu setzen?",`Erwartet: {0}\r
Empfangen: \u201E{1}\u201C.`,"Gibt an, ob macOS als Betriebssystem verwendet wird.","Gibt an, ob Linux als Betriebssystem verwendet wird.","Gibt an, ob Windows als Betriebssystem verwendet wird.","Gibt an, ob es sich bei der Plattform um einen Webbrowser handelt.","Gibt an, ob macOS auf einer Nicht-Browser-Plattform als Betriebssystem verwendet wird.","Gibt an, ob iOS als Betriebssystem verwendet wird.","Gibt an, ob es sich bei der Plattform um einen mobilen Webbrowser handelt.","Qualit\xE4tstyp des VS Codes","Gibt an, ob sich der Tastaturfokus in einem Eingabefeld befindet.","Meinten Sie {0}?","Meinten Sie {0} oder {1}?","Meinten Sie {0}, {1} oder {2}?","Haben Sie vergessen, das Anf\xFChrungszeichen zu \xF6ffnen oder zu schlie\xDFen?","Haben Sie vergessen, das Zeichen \u201E/\u201C (Schr\xE4gstrich) zu escapen? Setzen Sie zwei Backslashes davor, um es zu escapen, z. B. \u201E\\\\/\u201C.","Gibt an, ob Vorschl\xE4ge sichtbar sind.","({0}) wurde gedr\xFCckt. Es wird auf die zweite Taste in der Kombination gewartet...","({0}) wurde gedr\xFCckt. Es wird auf die zweite Taste in der Kombination gewartet...","Die Tastenkombination ({0}, {1}) ist kein Befehl.","Die Tastenkombination ({0}, {1}) ist kein Befehl.","Workbench","Ist unter Windows und Linux der STRG-Taste und unter macOS der Befehlstaste zugeordnet.","Ist unter Windows und Linux der ALT-Taste und unter macOS der Wahltaste zugeordnet.",'Der Modifizierer zum Hinzuf\xFCgen eines Elements in B\xE4umen und Listen zu einer Mehrfachauswahl mit der Maus (zum Beispiel im Explorer, in ge\xF6ffneten Editoren und in der SCM-Ansicht). Die Mausbewegung "Seitlich \xF6ffnen" wird \u2013 sofern unterst\xFCtzt \u2013 so angepasst, dass kein Konflikt mit dem Modifizierer f\xFCr Mehrfachauswahl entsteht.',"Steuert, wie Elemente in Strukturen und Listen mithilfe der Maus ge\xF6ffnet werden (sofern unterst\xFCtzt). Bei \xFCbergeordneten Elementen, deren untergeordnete Elemente sich in Strukturen befinden, steuert diese Einstellung, ob ein Einfachklick oder ein Doppelklick das \xFCbergeordnete Elemente erweitert. Beachten Sie, dass einige Strukturen und Listen diese Einstellung ggf. ignorieren, wenn sie nicht zutrifft.","Steuert, ob Listen und Strukturen ein horizontales Scrollen in der Workbench unterst\xFCtzen. Warnung: Das Aktivieren dieser Einstellung kann sich auf die Leistung auswirken.","Steuert, ob Klicks in der Bildlaufleiste Seite f\xFCr Seite scrollen.","Steuert den Struktureinzug in Pixeln.","Steuert, ob die Struktur Einzugsf\xFChrungslinien rendern soll.","Steuert, ob Listen und Strukturen einen optimierten Bildlauf verwenden.",'Ein Multiplikator, der f\xFCr die Mausrad-Bildlaufereignisse "deltaX" und "deltaY" verwendet werden soll.',"Multiplikator f\xFCr Scrollgeschwindigkeit bei Dr\xFCcken von ALT.","Elemente beim Suchen hervorheben. Die Navigation nach oben und unten durchl\xE4uft dann nur die markierten Elemente.","Filterelemente bei der Suche.","Steuert den Standardsuchmodus f\xFCr Listen und Strukturen in der Workbench.","Bei der einfachen Tastaturnavigation werden Elemente in den Fokus genommen, die mit der Tastatureingabe \xFCbereinstimmen. Die \xDCbereinstimmungen gelten nur f\xFCr Pr\xE4fixe.","Hervorheben von Tastaturnavigationshervorgebungselemente, die mit der Tastatureingabe \xFCbereinstimmen. Beim nach oben und nach unten Navigieren werden nur die hervorgehobenen Elemente durchlaufen.","Durch das Filtern der Tastaturnavigation werden alle Elemente herausgefiltert und ausgeblendet, die nicht mit der Tastatureingabe \xFCbereinstimmen.",'Steuert die Tastaturnavigation in Listen und Strukturen in der Workbench. Kann "simple" (einfach), "highlight" (hervorheben) und "filter" (filtern) sein.',"Bitte verwenden Sie stattdessen \u201Eworkbench.list.defaultFindMode\u201C und \u201Eworkbench.list.typeNavigationMode\u201C.","Verwenden Sie bei der Suche eine Fuzzy\xFCbereinstimmung.","Verwenden Sie bei der Suche eine zusammenh\xE4ngende \xDCbereinstimmung.","Steuert den Typ der \xDCbereinstimmung, der beim Durchsuchen von Listen und Strukturen in der Workbench verwendet wird.","Steuert, wie Strukturordner beim Klicken auf die Ordnernamen erweitert werden. Beachten Sie, dass einige Strukturen und Listen diese Einstellung ggf. ignorieren, wenn sie nicht zutrifft.","Steuert, ob fester Bildlauf in Strukturen aktiviert ist.","Steuert die Anzahl der festen Elemente, die in der Struktur angezeigt werden, wenn {0} aktiviert ist.",'Steuert die Funktionsweise der Typnavigation in Listen und Strukturen in der Workbench. Bei einer Festlegung auf "trigger" beginnt die Typnavigation, sobald der Befehl "list.triggerTypeNavigation" ausgef\xFChrt wird.',"Fehler","Warnung","Info","zuletzt verwendet","\xE4hnliche Befehle","h\xE4ufig verwendet","andere Befehle","\xE4hnliche Befehle","{0}, {1}",'Der Befehl "{0}" hat zu einem Fehler gef\xFChrt.',"{0}, {1}","Gibt an, ob sich der Tastaturfokus innerhalb des Steuerelements f\xFCr die Schnelleingabe befindet.","Der Typ der aktuell sichtbaren Schnelleingabe","Gibt an, ob sich der Cursor in der Schnelleingabe am Ende des Eingabefelds befindet.","Zur\xFCck","Dr\xFCcken Sie die EINGABETASTE, um Ihre Eingabe zu best\xE4tigen, oder ESC, um den Vorgang abzubrechen.","{0}/{1}","Nehmen Sie eine Eingabe vor, um die Ergebnisse einzugrenzen.","Wird im Kontext der Schnellauswahl verwendet. Wenn Sie eine Tastenzuordnung f\xFCr diesen Befehl \xE4ndern, sollten Sie auch alle anderen Tastenzuordnungen (Modifizierervarianten) dieses Befehls \xE4ndern.","Wenn wir uns im Schnellzugriffsmodus befinden, wird zum n\xE4chsten Element navigiert. Wenn wir uns nicht im Schnellzugriffsmodus befinden, wird zum n\xE4chsten Trennzeichen navigiert.","Wenn wir uns im Schnellzugriffsmodus befinden, wird zum vorherigen Element navigiert. Wenn wir uns nicht im Schnellzugriffsmodus befinden, wird zum vorherigen Trennzeichen navigiert.","Aktivieren Sie alle Kontrollk\xE4stchen","{0} Ergebnisse","{0} ausgew\xE4hlt","OK","Benutzerdefiniert","Zur\xFCck ({0})","Zur\xFCck","Schnelleingabe",'Klicken, um den Befehl "{0}" auszuf\xFChren',"Allgemeine Vordergrundfarbe. Diese Farbe wird nur verwendet, wenn sie nicht durch eine Komponente \xFCberschrieben wird.","Allgemeine Vordergrundfarbe. Diese Farbe wird nur verwendet, wenn sie nicht durch eine Komponente \xFCberschrieben wird.","Allgemeine Vordergrundfarbe f\xFCr Fehlermeldungen. Diese Farbe wird nur verwendet, wenn sie nicht durch eine Komponente \xFCberschrieben wird.","Vordergrundfarbe f\xFCr Beschreibungstexte, die weitere Informationen anzeigen, z.B. f\xFCr eine Beschriftung.","Die f\xFCr Symbole in der Workbench verwendete Standardfarbe.","Allgemeine Rahmenfarbe f\xFCr fokussierte Elemente. Diese Farbe wird nur verwendet, wenn sie nicht durch eine Komponente \xFCberschrieben wird.","Ein zus\xE4tzlicher Rahmen um Elemente, mit dem diese von anderen getrennt werden, um einen gr\xF6\xDFeren Kontrast zu erreichen.","Ein zus\xE4tzlicher Rahmen um aktive Elemente, mit dem diese von anderen getrennt werden, um einen gr\xF6\xDFeren Kontrast zu erreichen.","Hintergrundfarbe der Textauswahl in der Workbench (z.B. f\xFCr Eingabefelder oder Textbereiche). Diese Farbe gilt nicht f\xFCr die Auswahl im Editor.","Vordergrundfarbe f\xFCr Links im Text.","Vordergrundfarbe f\xFCr angeklickte Links im Text und beim Zeigen darauf mit der Maus.","Farbe f\xFCr Text-Trennzeichen.","Vordergrundfarbe f\xFCr vorformatierte Textsegmente.","Hintergrundfarbe f\xFCr vorformatierte Textsegmente.","Hintergrundfarbe f\xFCr Blockzitate im Text.","Rahmenfarbe f\xFCr blockquote-Elemente im Text.","Hintergrundfarbe f\xFCr Codebl\xF6cke im Text.","Die in Diagrammen verwendete Vordergrundfarbe.","Die f\xFCr horizontale Linien in Diagrammen verwendete Farbe.","Die in Diagrammvisualisierungen verwendete Farbe Rot.","Die in Diagrammvisualisierungen verwendete Farbe Blau.","Die in Diagrammvisualisierungen verwendete Farbe Gelb.","Die in Diagrammvisualisierungen verwendete Farbe Orange.","Die in Diagrammvisualisierungen verwendete Farbe Gr\xFCn.","Die in Diagrammvisualisierungen verwendete Farbe Violett.","Hintergrundfarbe des Editors.","Standardvordergrundfarbe des Editors.","Hintergrundfarbe des fixierten Bildlaufs im Editor","Hintergrundfarbe des fixierten Bildlaufs beim Daraufzeigen im Editor","Rahmenfarbe des fixierten Bildlaufs im Editor"," Schattenfarbe des fixierten Bildlaufs im Editor","Hintergrundfarbe von Editor-Widgets wie zum Beispiel Suchen/Ersetzen.","Vordergrundfarbe f\xFCr Editorwidgets wie Suchen/Ersetzen.","Rahmenfarbe von Editorwigdets. Die Farbe wird nur verwendet, wenn f\xFCr das Widget ein Rahmen verwendet wird und die Farbe nicht von einem Widget \xFCberschrieben wird.","Rahmenfarbe der Gr\xF6\xDFenanpassungsleiste von Editorwigdets. Die Farbe wird nur verwendet, wenn f\xFCr das Widget ein Gr\xF6\xDFenanpassungsrahmen verwendet wird und die Farbe nicht von einem Widget au\xDFer Kraft gesetzt wird.","Hintergrundfarbe f\xFCr Fehlertext im Editor. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Vordergrundfarbe von Fehlerunterstreichungen im Editor.","Wenn festgelegt, wird die Farbe doppelter Unterstreichungen f\xFCr Fehler im Editor angezeigt.","Hintergrundfarbe f\xFCr Warnungstext im Editor. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Vordergrundfarbe von Warnungsunterstreichungen im Editor.","Wenn festgelegt, wird die Farbe doppelter Unterstreichungen f\xFCr Warnungen im Editor angezeigt.","Hintergrundfarbe f\xFCr Infotext im Editor. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Vordergrundfarbe von Informationsunterstreichungen im Editor.","Wenn festgelegt, wird die Farbe doppelter Unterstreichungen f\xFCr Infos im Editor angezeigt.","Vordergrundfarbe der Hinweisunterstreichungen im Editor.","Wenn festgelegt, wird die Farbe doppelter Unterstreichungen f\xFCr Hinweise im Editor angezeigt.","Farbe der aktiven Links.","Farbe der Editor-Auswahl.","Farbe des gew\xE4hlten Text f\xFCr einen hohen Kontrast","Die Farbe der Auswahl befindet sich in einem inaktiven Editor. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegende Dekorationen verdeckt.","Farbe f\xFCr Bereiche mit dem gleichen Inhalt wie die Auswahl. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Randfarbe f\xFCr Bereiche, deren Inhalt der Auswahl entspricht.","Farbe des aktuellen Suchergebnisses.","Textfarbe der aktuellen Such\xFCbereinstimmung.","Farbe der anderen Suchergebnisse. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Vordergrundfarbe der anderen Such\xFCbereinstimmungen.","Farbe des Bereichs, der die Suche eingrenzt. Die Farbe darf nicht deckend sein, damit sie nicht die zugrunde liegenden Dekorationen verdeckt.","Randfarbe des aktuellen Suchergebnisses.","Randfarbe der anderen Suchtreffer.","Rahmenfarbe des Bereichs, der die Suche eingrenzt. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Hervorhebung unterhalb des Worts, f\xFCr das ein Hoverelement angezeigt wird. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Hintergrundfarbe des Editor-Mauszeigers.","Vordergrundfarbe des Editor-Mauszeigers","Rahmenfarbe des Editor-Mauszeigers.","Hintergrundfarbe der Hoverstatusleiste des Editors.","Vordergrundfarbe f\xFCr Inlinehinweise","Hintergrundfarbe f\xFCr Inlinehinweise","Vordergrundfarbe von Inlinehinweisen f\xFCr Typen","Hintergrundfarbe von Inlinehinweisen f\xFCr Typen","Vordergrundfarbe von Inlinehinweisen f\xFCr Parameter","Hintergrundfarbe von Inlinehinweisen f\xFCr Parameter",'Die f\xFCr das Aktionssymbol "Gl\xFChbirne" verwendete Farbe.','Die f\xFCr das Aktionssymbol "Automatische Gl\xFChbirnenkorrektur" verwendete Farbe.',"Die Farbe, die f\xFCr das KI-Symbol der Gl\xFChbirne verwendet wird.","Hervorhebungs-Hintergrundfarbe eines Codeschnipsel-Tabstopps.","Hervorhebungs-Rahmenfarbe eines Codeschnipsel-Tabstopps.","Hervorhebungs-Hintergrundfarbe des letzten Tabstopps eines Codeschnipsels.","Rahmenfarbe zur Hervorhebung des letzten Tabstopps eines Codeschnipsels.","Hintergrundfarbe f\xFCr eingef\xFCgten Text. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Hintergrundfarbe f\xFCr Text, der entfernt wurde. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Hintergrundfarbe f\xFCr eingef\xFCgte Zeilen. Die Farbe darf nicht deckend sein, um zugrunde liegende Dekorationen nicht auszublenden.","Hintergrundfarbe f\xFCr Zeilen, die entfernt wurden. Die Farbe darf nicht deckend sein, um zugrunde liegende Dekorationen nicht auszublenden.","Hintergrundfarbe f\xFCr den Rand, an dem Zeilen eingef\xFCgt wurden.","Hintergrundfarbe f\xFCr den Rand, an dem die Zeilen entfernt wurden.","Vordergrund des Diff-\xDCbersichtslineals f\xFCr eingef\xFCgten Inhalt.","Vordergrund des Diff-\xDCbersichtslineals f\xFCr entfernten Inhalt.","Konturfarbe f\xFCr eingef\xFCgten Text.","Konturfarbe f\xFCr entfernten Text.","Die Rahmenfarbe zwischen zwei Text-Editoren.","Farbe der diagonalen F\xFCllung des Vergleichs-Editors. Die diagonale F\xFCllung wird in Ansichten mit parallelem Vergleich verwendet.","Die Hintergrundfarbe von unver\xE4nderten Bl\xF6cken im Diff-Editor.","Die Vordergrundfarbe von unver\xE4nderten Bl\xF6cken im Diff-Editor.","Die Hintergrundfarbe des unver\xE4nderten Codes im Diff-Editor.","Schattenfarbe von Widgets wie zum Beispiel Suchen/Ersetzen innerhalb des Editors.","Die Rahmenfarbe von Widgets, z.\xA0B. Suchen/Ersetzen im Editor.","Symbolleistenhintergrund beim Bewegen der Maus \xFCber Aktionen","Symbolleistengliederung beim Bewegen der Maus \xFCber Aktionen","Symbolleistenhintergrund beim Halten der Maus \xFCber Aktionen","Farbe der Breadcrumb-Elemente, die den Fokus haben.","Hintergrundfarbe der Breadcrumb-Elemente.","Farbe der Breadcrumb-Elemente, die den Fokus haben.","Die Farbe der ausgew\xE4hlten Breadcrumb-Elemente.","Hintergrundfarbe des Breadcrumb-Auswahltools.","Hintergrund des aktuellen Headers in Inlinezusammenf\xFChrungskonflikten. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Hintergrund f\xFCr den aktuellen Inhalt in Inlinezusammenf\xFChrungskonflikten. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Hintergrund f\xFCr eingehende Header in Inlinezusammenf\xFChrungskonflikten. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Hintergrund f\xFCr eingehenden Inhalt in Inlinezusammenf\xFChrungskonflikten. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Headerhintergrund f\xFCr gemeinsame Vorg\xE4ngerelemente in Inlinezusammenf\xFChrungskonflikten. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Hintergrund des Inhalts gemeinsamer Vorg\xE4ngerelemente in Inlinezusammenf\xFChrungskonflikt. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Rahmenfarbe f\xFCr Kopfzeilen und die Aufteilung in Inline-Mergingkonflikten.","Aktueller \xDCbersichtslineal-Vordergrund f\xFCr Inline-Mergingkonflikte.","Eingehender \xDCbersichtslineal-Vordergrund f\xFCr Inline-Mergingkonflikte.","Hintergrund des \xDCbersichtslineals des gemeinsamen \xFCbergeordneten Elements bei Inlinezusammenf\xFChrungskonflikten.","\xDCbersichtslinealmarkerfarbe f\xFCr das Suchen von \xDCbereinstimmungen. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","\xDCbersichtslinealmarkerfarbe f\xFCr das Hervorheben der Auswahl. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Die Farbe, die f\xFCr das Problemfehlersymbol verwendet wird.","Die Farbe, die f\xFCr das Problemwarnsymbol verwendet wird.","Die Farbe, die f\xFCr das Probleminfosymbol verwendet wird.","Hintergrund f\xFCr Eingabefeld.","Vordergrund f\xFCr Eingabefeld.","Rahmen f\xFCr Eingabefeld.","Rahmenfarbe f\xFCr aktivierte Optionen in Eingabefeldern.","Hintergrundfarbe f\xFCr aktivierte Optionen in Eingabefeldern.","Hintergrundfarbe beim Daraufzeigen f\xFCr Optionen in Eingabefeldern.","Vordergrundfarbe f\xFCr aktivierte Optionen in Eingabefeldern.","Eingabefeld-Vordergrundfarbe f\xFCr Platzhaltertext.","Hintergrundfarbe bei der Eingabevalidierung f\xFCr den Schweregrad der Information.","Vordergrundfarbe bei der Eingabevalidierung f\xFCr den Schweregrad der Information.","Rahmenfarbe bei der Eingabevalidierung f\xFCr den Schweregrad der Information.","Hintergrundfarbe bei der Eingabevalidierung f\xFCr den Schweregrad der Warnung.","Vordergrundfarbe bei der Eingabevalidierung f\xFCr den Schweregrad der Warnung.","Rahmenfarbe bei der Eingabevalidierung f\xFCr den Schweregrad der Warnung.","Hintergrundfarbe bei der Eingabevalidierung f\xFCr den Schweregrad des Fehlers.","Vordergrundfarbe bei der Eingabevalidierung f\xFCr den Schweregrad des Fehlers.","Rahmenfarbe bei der Eingabevalidierung f\xFCr den Schweregrad des Fehlers.","Hintergrund f\xFCr Dropdown.","Hintergrund f\xFCr Dropdownliste.","Vordergrund f\xFCr Dropdown.","Rahmen f\xFCr Dropdown.","Vordergrundfarbe der Schaltfl\xE4che.","Farbe des Schaltfl\xE4chentrennzeichens.","Hintergrundfarbe der Schaltfl\xE4che.","Hintergrundfarbe der Schaltfl\xE4che, wenn darauf gezeigt wird.","Rahmenfarbe der Schaltfl\xE4che.","Sekund\xE4re Vordergrundfarbe der Schaltfl\xE4che.","Hintergrundfarbe der sekund\xE4ren Schaltfl\xE4che.","Hintergrundfarbe der sekund\xE4ren Schaltfl\xE4che beim Daraufzeigen.",'Vordergrundfarbe f\xFCr "aktive Radiooption"','Hintergrundfarbe f\xFCr "aktive Radiooption"',"Rahmenfarbe der aktiven Radiooption","Vordergrundfarbe f\xFCr \u201Einaktive Radiooption\u201C","Hintergrundfarbe f\xFCr \u201Einaktive Radiooption\u201C","Rahmenfarbe der inaktiven Radiooption","Hintergrundfarbe der inaktiven aktiven Radiooption, wenn darauf gezeigt wird.","Hintergrundfarbe von Kontrollk\xE4stchenwidget.","Hintergrundfarbe des Kontrollk\xE4stchenwidgets, wenn das Element ausgew\xE4hlt ist, in dem es sich befindet.","Vordergrundfarbe von Kontrollk\xE4stchenwidget.","Rahmenfarbe von Kontrollk\xE4stchenwidget.","Rahmenfarbe des Kontrollk\xE4stchenwidgets, wenn das Element ausgew\xE4hlt ist, in dem es sich befindet.","Die Hintergrundfarbe der Tastenbindungsbeschriftung. Die Tastenbindungsbeschriftung wird verwendet, um eine Tastenkombination darzustellen.","Die Vordergrundfarbe der Tastenbindungsbeschriftung. Die Tastenbindungsbeschriftung wird verwendet, um eine Tastenkombination darzustellen.","Die Rahmenfarbe der Tastenbindungsbeschriftung. Die Tastenbindungsbeschriftung wird verwendet, um eine Tastenkombination darzustellen.","Die Rahmenfarbe der Schaltfl\xE4che der Tastenbindungsbeschriftung. Die Tastenbindungsbeschriftung wird verwendet, um eine Tastenkombination darzustellen.","Hintergrundfarbe der Liste/Struktur f\xFCr das fokussierte Element, wenn die Liste/Struktur aktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.","Vordergrundfarbe der Liste/Struktur f\xFCr das fokussierte Element, wenn die Liste/Struktur aktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.","Konturfarbe der Liste/Struktur f\xFCr das fokussierte Element, wenn die Liste/Struktur aktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.","Umrissfarbe der Liste/des Baums f\xFCr das fokussierte Element, wenn die Liste/der Baum aktiv und ausgew\xE4hlt ist. Eine aktive Liste/Baum hat Tastaturfokus, eine inaktive nicht.","Hintergrundfarbe der Liste/Struktur f\xFCr das ausgew\xE4hlte Element, wenn die Liste/Struktur aktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.","Vordergrundfarbe der Liste/Struktur f\xFCr das ausgew\xE4hlte Element, wenn die Liste/Struktur aktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.","Vordergrundfarbe des Symbols der Liste/Struktur f\xFCr das ausgew\xE4hlte Element, wenn die Liste/Struktur aktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.","Hintergrundfarbe der Liste/Struktur f\xFCr das ausgew\xE4hlte Element, wenn die Liste/Struktur inaktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.","Vordergrundfarbe der Liste/Struktur f\xFCr das ausgew\xE4hlte Element, wenn die Liste/Baumstruktur inaktiv ist. Eine aktive Liste/Baumstruktur hat Tastaturfokus, eine inaktive hingegen nicht.","Vordergrundfarbe des Symbols der Liste/Struktur f\xFCr das ausgew\xE4hlte Element, wenn die Liste/Struktur inaktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.","Hintergrundfarbe der Liste/Struktur f\xFCr das fokussierte Element, wenn die Liste/Struktur inaktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.","Konturfarbe der Liste/Struktur f\xFCr das fokussierte Element, wenn die Liste/Struktur inaktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.","Hintergrund der Liste/Struktur, wenn mit der Maus auf Elemente gezeigt wird.","Vordergrund der Liste/Struktur, wenn mit der Maus auf Elemente gezeigt wird.","Hintergrund f\xFCr Drag & Drop auflisten/strukturieren, wenn Elemente bei Verwendung der Maus \xFCber Elemente verschoben werden.","Rahmenfarbe f\xFCr Drag & Drop auflisten/strukturieren, wenn Elemente bei Verwendung der Maus zwischen Elementen verschoben werden.","Vordergrundfarbe der Liste/Struktur zur Trefferhervorhebung beim Suchen innerhalb der Liste/Struktur.","Die Vordergrundfarbe der Liste/Struktur des Treffers hebt aktiv fokussierte Elemente hervor, wenn innerhalb der Liste / der Struktur gesucht wird.","Vordergrundfarbe einer Liste/Struktur f\xFCr ung\xFCltige Elemente, z.B. ein nicht ausgel\xF6ster Stamm im Explorer.","Vordergrundfarbe f\xFCr Listenelemente, die Fehler enthalten.","Vordergrundfarbe f\xFCr Listenelemente, die Warnungen enthalten.","Hintergrundfarbe des Typfilterwidgets in Listen und Strukturen.","Konturfarbe des Typfilterwidgets in Listen und Strukturen.","Konturfarbe des Typfilterwidgets in Listen und Strukturen, wenn es keine \xDCbereinstimmungen gibt.","Schattenfarbe des Typfilterwidgets in Listen und Strukturen.","Hintergrundfarbe der gefilterten \xDCbereinstimmung","Rahmenfarbe der gefilterten \xDCbereinstimmung","Hintergrundfarbe f\xFCr nicht hervorgehobene Listen-/Strukturelemente.","Strukturstrichfarbe f\xFCr die Einzugsf\xFChrungslinien.","Strukturstrichfarbe f\xFCr die Einzugslinien, die nicht aktiv sind.","Tabellenrahmenfarbe zwischen Spalten.","Hintergrundfarbe f\xFCr ungerade Tabellenzeilen.","Hintergrundfarbe der Aktionenliste.","Vordergrundfarbe der Aktionenliste.","Die Hintergrundfarbe der Aktionenliste f\xFCr das fokussierte Element.","Die Hintergrundfarbe der Aktionenliste f\xFCr das fokussierte Element.","Rahmenfarbe von Men\xFCs.","Vordergrundfarbe von Men\xFCelementen.","Hintergrundfarbe von Men\xFCelementen.","Vordergrundfarbe des ausgew\xE4hlten Men\xFCelements im Men\xFC.","Hintergrundfarbe des ausgew\xE4hlten Men\xFCelements im Men\xFC.","Rahmenfarbe des ausgew\xE4hlten Men\xFCelements im Men\xFC.","Farbe eines Trenner-Men\xFCelements in Men\xFCs.","Minimap-Markerfarbe f\xFCr gefundene \xDCbereinstimmungen.","Minimap-Markerfarbe f\xFCr wiederholte Editorauswahlen.","Minimap-Markerfarbe f\xFCr die Editorauswahl.","Minimapmarkerfarbe f\xFCr Informationen.","Minimapmarkerfarbe f\xFCr Warnungen","Minimapmarkerfarbe f\xFCr Fehler","Hintergrundfarbe der Minimap.","Deckkraft von Vordergrundelementen, die in der Minimap gerendert werden. Beispiel: \u201E#000000c0\u201C wird die Elemente mit einer Deckkraft von 75 % rendern.","Hintergrundfarbe des Minimap-Schiebereglers.","Hintergrundfarbe des Minimap-Schiebereglers beim Daraufzeigen.","Hintergrundfarbe des Minimap-Schiebereglers, wenn darauf geklickt wird.","Rahmenfarbe aktiver Trennleisten.","Hintergrundfarbe f\xFCr Badge. Badges sind kurze Info-Texte, z.B. f\xFCr Anzahl Suchergebnisse.","Vordergrundfarbe f\xFCr Badge. Badges sind kurze Info-Texte, z.B. f\xFCr Anzahl Suchergebnisse.","Schatten der Scrollleiste, um anzuzeigen, dass die Ansicht gescrollt wird.","Hintergrundfarbe vom Scrollbar-Schieber","Hintergrundfarbe des Schiebereglers, wenn darauf gezeigt wird.","Hintergrundfarbe des Schiebereglers, wenn darauf geklickt wird.","Hintergrundfarbe des Fortschrittbalkens, der f\xFCr zeitintensive Vorg\xE4nge angezeigt werden kann.","Schnellauswahl der Hintergrundfarbe. Im Widget f\xFCr die Schnellauswahl sind Auswahlelemente wie die Befehlspalette enthalten.","Vordergrundfarbe der Schnellauswahl. Im Widget f\xFCr die Schnellauswahl sind Auswahlelemente wie die Befehlspalette enthalten.","Hintergrundfarbe f\xFCr den Titel der Schnellauswahl. Im Widget f\xFCr die Schnellauswahl sind Auswahlelemente wie die Befehlspalette enthalten.","Schnellauswahlfarbe f\xFCr das Gruppieren von Bezeichnungen.","Schnellauswahlfarbe f\xFCr das Gruppieren von Rahmen.",'Verwenden Sie stattdessen "quickInputList.focusBackground".',"Die Hintergrundfarbe der Schnellauswahl f\xFCr das fokussierte Element.","Die Vordergrundfarbe des Symbols der Schnellauswahl f\xFCr das fokussierte Element.","Die Hintergrundfarbe der Schnellauswahl f\xFCr das fokussierte Element.","Farbe des Texts in der Abschlussmeldung des Such-Viewlets.","Farbe der Abfrage\xFCbereinstimmungen des Such-Editors","Rahmenfarbe der Abfrage\xFCbereinstimmungen des Such-Editors","Diese Farbe muss transparent sein, oder der Inhalt wird verdeckt.","Standardfarbe verwenden.","Die ID der zu verwendenden Schriftart. Sofern nicht festgelegt, wird die zuerst definierte Schriftart verwendet.","Das der Symboldefinition zugeordnete Schriftzeichen.","Symbol f\xFCr Aktion zum Schlie\xDFen in Widgets","Symbol f\xFCr den Wechsel zur vorherigen Editor-Position.","Symbol f\xFCr den Wechsel zur n\xE4chsten Editor-Position.","Die folgenden Dateien wurden geschlossen und auf dem Datentr\xE4ger ge\xE4ndert: {0}.","Die folgenden Dateien wurden auf inkompatible Weise ge\xE4ndert: {0}.",'"{0}" konnte nicht f\xFCr alle Dateien r\xFCckg\xE4ngig gemacht werden. {1}','"{0}" konnte nicht f\xFCr alle Dateien r\xFCckg\xE4ngig gemacht werden. {1}','"{0}" konnte nicht f\xFCr alle Dateien r\xFCckg\xE4ngig gemacht werden, da \xC4nderungen an {1} vorgenommen wurden.','"{0}" konnte nicht f\xFCr alle Dateien r\xFCckg\xE4ngig gemacht werden, weil bereits ein Vorgang zum R\xFCckg\xE4ngigmachen oder Wiederholen f\xFCr "{1}" durchgef\xFChrt wird.','"{0}" konnte nicht f\xFCr alle Dateien r\xFCckg\xE4ngig gemacht werden, weil in der Zwischenzeit bereits ein Vorgang zum R\xFCckg\xE4ngigmachen oder Wiederholen durchgef\xFChrt wurde.','M\xF6chten Sie "{0}" f\xFCr alle Dateien r\xFCckg\xE4ngig machen?',"&&In {0} Dateien r\xFCckg\xE4ngig machen","&&Datei r\xFCckg\xE4ngig machen",'"{0}" konnte nicht r\xFCckg\xE4ngig gemacht werden, weil bereits ein Vorgang zum R\xFCckg\xE4ngigmachen oder Wiederholen durchgef\xFChrt wird.','M\xF6chten Sie "{0}" r\xFCckg\xE4ngig machen?',"&&Ja","Nein",'"{0}" konnte nicht in allen Dateien wiederholt werden. {1}','"{0}" konnte nicht in allen Dateien wiederholt werden. {1}','"{0}" konnte nicht in allen Dateien wiederholt werden, da \xC4nderungen an {1} vorgenommen wurden.','"{0}" konnte nicht f\xFCr alle Dateien wiederholt werden, weil bereits ein Vorgang zum R\xFCckg\xE4ngigmachen oder Wiederholen f\xFCr "{1}" durchgef\xFChrt wird.','"{0}" konnte nicht f\xFCr alle Dateien wiederholt werden, weil in der Zwischenzeit bereits ein Vorgang zum R\xFCckg\xE4ngigmachen oder Wiederholen durchgef\xFChrt wurde.','"{0}" konnte nicht wiederholt werden, weil bereits ein Vorgang zum R\xFCckg\xE4ngigmachen oder Wiederholen durchgef\xFChrt wird.',"Codearbeitsbereich"],globalThis._VSCODE_NLS_LANGUAGE="de";

//# sourceMappingURL=../min-maps/nls.messages.de.js.map
});
