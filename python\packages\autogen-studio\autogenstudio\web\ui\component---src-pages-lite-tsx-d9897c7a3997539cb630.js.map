{"version": 3, "file": "component---src-pages-lite-tsx-d9897c7a3997539cb630.js", "mappings": "gMA4BA,UArBiBA,IAAmB,IAAlB,KAAEC,GAAWD,EAC7B,OACEE,EAAAA,cAACC,EAAAA,EAAU,KACTD,EAAAA,cAAA,QAAME,MAAO,CAAEC,OAAQ,QAAUC,UAAU,UACzCJ,EAAAA,cAACK,EAAAA,EAAc,SAmBhB,MAAMC,EAAOA,IAClBN,EAAAA,cAAAA,EAAAA,SAAA,KACEA,EAAAA,cAAA,aAAO,kBACPA,EAAAA,cAAA,QACEO,KAAK,cACLC,QAAQ,2B", "sources": ["webpack://autogentstudio/./src/pages/lite.tsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport { graphql } from \"gatsby\";\r\n\r\nimport { SessionManager } from \"../components/views/playground/manager\";\r\nimport { LiteLayout } from \"../components/layout\";\r\n\r\n// markup\r\nconst LitePage = ({ data }: any) => {\r\n  return (\r\n    <LiteLayout>\r\n      <main style={{ height: \"100%\" }} className=\"h-full\">\r\n        <SessionManager />\r\n      </main>\r\n    </LiteLayout>\r\n  );\r\n};\r\n\r\nexport const query = graphql`\r\n  query LitePageQuery {\r\n    site {\r\n      siteMetadata {\r\n        description\r\n        title\r\n      }\r\n    }\r\n  }\r\n`;\r\n\r\nexport default LitePage;\r\n\r\nexport const Head = () => (\r\n  <>\r\n    <title>多智能体工作室 - 轻量模式</title>\r\n    <meta\r\n      name=\"description\"\r\n      content=\"多智能体工作室 轻量模式 - 简化的聊天界面\"\r\n    />\r\n  </>\r\n);\r\n"], "names": ["_ref", "data", "React", "LiteLayout", "style", "height", "className", "Session<PERSON>anager", "Head", "name", "content"], "sourceRoot": ""}