import { Settings } from "../../types/datamodel";
import { BaseAPI } from "../../utils/baseapi";

export class SettingsAPI extends BaseAPI {
  async getSettings(userId: string): Promise<Settings> {
    console.log(`🔍 SettingsAPI: Getting settings for user: ${userId}`);
    
    const response = await fetch(
      `${this.getBaseUrl()}/settings/`,
      {
        headers: this.getHeaders(),
      }
    );
    
    console.log(`🔍 SettingsAPI: Response status: ${response.status}`);
    
    const data = await response.json();
    console.log(`🔍 SettingsAPI: Response data:`, data);
    
    if (!data.status) {
      const error = new Error(data.detail || data.message || "Failed to fetch settings");
      console.error("❌ SettingsAPI: Failed to fetch settings:", error.message);
      throw error;
    }
    
    return data.data;
  }

  async updateSettings(settings: Settings, userId: string): Promise<Settings> {
    const settingsData = {
      ...settings,
      user_id: settings.user_id || userId,
    };

    console.log("settingsData", settingsData);

    const response = await fetch(`${this.getBaseUrl()}/settings/`, {
      method: "PUT",
      headers: this.getHeaders(),
      body: JSON.stringify(settingsData),
    });
    const data = await response.json();
    if (!data.status)
      throw new Error(data.message || "Failed to update settings");
    return data.data;
  }
}

export const settingsAPI = new SettingsAPI();
