{"version": 3, "file": "0ba2ce643a859e05bf62bf3735bcfd01c435cab2-31e36603ea00f05b1d5b.js", "mappings": "6KAEA,EADuB,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,SAAW,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,iIAAqI,KAAQ,WAAY,MAAS,Y,UCM5T,EAAmB,SAA0BA,EAAOC,GACtD,OAAoB,gBAAoBC,EAAAC,GAAU,OAAS,CAAC,EAAGH,EAAO,CACpEC,IAAKA,EACLG,KAAM,IAEV,EAOA,MAJ2B,aAAiB,E,wKCZxCC,EAAMC,EAAA,EAAQD,IAChBE,EAAMD,EAAA,EAAQC,ICFhB,IAoBA,GApB2B,IAAAC,YAAW,SAAUR,EAAOC,GACrD,IAAIQ,EAAUT,EAAMS,QAClBC,EAAQV,EAAMU,MACdC,EAAYX,EAAMW,UAChBC,GAAc,IAAAC,SAAQ,WAOxB,MALuB,mBAAZJ,EACQA,IAEAA,CAGrB,EAAG,CAACA,IACAK,GAAc,QAAWb,GAAK,QAAWW,IAC7C,OAAoB,gBAAoB,WAAgB,KAAMF,GAAsB,gBAAoB,MAAO,CAC7GK,UAAW,GAAGC,OAAOL,EAAW,YACjB,eAAmBC,EAAa,CAC/CX,KAAK,QAAWW,GAAeE,OAAcG,IAEjD,GCrBIC,EAAqB,CACvBC,QAAS,EACTC,QAAS,GAEPC,EAAe,CAAC,EAAG,GAuCvB,EAtCiB,CACfC,QAAS,CACPC,OAAQ,CAAC,KAAM,MACfC,SAAUN,EACVO,OAAQ,CAAC,GAAI,GACbJ,aAAcA,GAEhBK,IAAK,CACHH,OAAQ,CAAC,KAAM,MACfC,SAAUN,EACVO,OAAQ,CAAC,GAAI,GACbJ,aAAcA,GAEhBM,SAAU,CACRJ,OAAQ,CAAC,KAAM,MACfC,SAAUN,EACVO,OAAQ,CAAC,GAAI,GACbJ,aAAcA,GAEhBO,WAAY,CACVL,OAAQ,CAAC,KAAM,MACfC,SAAUN,EACVO,OAAQ,CAAC,EAAG,GACZJ,aAAcA,GAEhBQ,OAAQ,CACNN,OAAQ,CAAC,KAAM,MACfC,SAAUN,EACVO,OAAQ,CAAC,EAAG,GACZJ,aAAcA,GAEhBS,YAAa,CACXP,OAAQ,CAAC,KAAM,MACfC,SAAUN,EACVO,OAAQ,CAAC,EAAG,GACZJ,aAAcA,ICpCdU,EAAY,CAAC,QAAS,YAAa,iBAAkB,YAAa,QAAS,YAAa,aAAc,oBAAqB,aAAc,aAAc,mBAAoB,eAAgB,UAAW,UAAW,YAAa,UAAW,WAAY,mBAQzP,SAASC,EAAShC,EAAOC,GACvB,IAAIgC,EACAC,EAAelC,EAAMU,MACvBA,OAAyB,IAAjBwB,GAAkCA,EAC1CC,EAAmBnC,EAAMW,UACzBA,OAAiC,IAArBwB,EAA8B,cAAgBA,EAC1DC,EAAiBpC,EAAMoC,eACvBC,EAAYrC,EAAMqC,UAClBC,EAAQtC,EAAMsC,MACdC,EAAmBvC,EAAMwC,UACzBA,OAAiC,IAArBD,EAA8B,aAAeA,EACzDE,EAAoBzC,EAAM0C,WAC1BA,OAAmC,IAAtBD,EAA+B,EAAaA,EACzDE,EAAoB3C,EAAM2C,kBAC1BC,EAAa5C,EAAM4C,WACnBC,EAAa7C,EAAM6C,WACnBC,EAAmB9C,EAAM8C,iBACzBC,EAAe/C,EAAM+C,aACrBC,EAAUhD,EAAMgD,QAChBC,EAAiBjD,EAAMkD,QACvBA,OAA6B,IAAnBD,EAA4B,CAAC,SAAWA,EAClDE,EAAYnD,EAAMmD,UAClB1C,EAAUT,EAAMS,QAChB2C,EAAWpD,EAAMoD,SACjBC,EAAkBrD,EAAMqD,gBACxBC,GAAa,OAAyBtD,EAAO+B,GAC3CwB,EAAkB,aACpBC,GAAmB,OAAeD,EAAiB,GACnDE,EAAiBD,EAAiB,GAClCE,EAAoBF,EAAiB,GACnCG,EAAgB,YAAa3D,EAAQgD,EAAUS,EAC/CG,EAAa,SAAa,MAC1BC,EAAa,SAAa,MAC1BC,EAAW,SAAa,MAC5B,sBAA0B7D,EAAK,WAC7B,OAAO2D,EAAWG,OACpB,GACA,IAAIC,EAAsB,SAA6BC,GACrDP,EAAkBO,GAClBZ,SAA0DA,EAAgBY,EAC5E,GH/Ca,SAA0BC,GACvC,IAAIlB,EAAUkB,EAAKlB,QACjBY,EAAaM,EAAKN,WAClBP,EAAkBa,EAAKb,gBACvBF,EAAYe,EAAKf,UACjBU,EAAaK,EAAKL,WAChBM,EAAe,UAAa,GAC5BC,EAAgC,WAEhC,IAAIC,EAAqBC,EADvBtB,IAE6C,QAA9CqB,EAAsBT,EAAWG,eAA6C,IAAxBM,GAA0F,QAAvDC,EAAwBD,EAAoBE,aAA6C,IAA1BD,GAAoCA,EAAsBE,KAAKH,GACxNhB,SAA0DA,GAAgB,GAE9E,EACIoB,EAAY,WACd,IAAIC,EACJ,QAAmD,QAA9CA,EAAsBb,EAAWE,eAA6C,IAAxBW,IAAkCA,EAAoBH,QAC/GV,EAAWE,QAAQQ,QACnBJ,EAAaJ,SAAU,EAChB,GAGX,EACIY,EAAgB,SAAuBC,GACzC,OAAQA,EAAMC,SACZ,KAAKxE,EACH+D,IACA,MACF,KAAK7D,EAED,IAAIuE,GAAc,EACbX,EAAaJ,UAChBe,EAAcL,KAEZK,EACFF,EAAMG,iBAENX,IAKV,EACA,YAAgB,WACd,OAAIpB,GACFgC,OAAOC,iBAAiB,UAAWN,GAC/BxB,IAEF,EAAA+B,EAAA,GAAIT,EAAW,GAEV,WACLO,OAAOG,oBAAoB,UAAWR,GACtCR,EAAaJ,SAAU,CACzB,GAEK,WACLI,EAAaJ,SAAU,CACzB,CACF,EAAG,CAACf,GACN,CGXEoC,CAAiB,CACfpC,QAASW,EACTC,WAAYE,EACZT,gBAAiBW,EACjBb,UAAWA,EACXU,WAAYA,IAEd,IA8BMwB,EARAC,EACFC,EAhBAC,EAAiB,WACnB,OAAoB,gBAAoB,EAAS,CAC/CvF,IAAK4D,EACLpD,QAASA,EACTE,UAAWA,EACXD,MAAOA,GAEX,EAsBI+E,GAA4B,eAAmBrC,EAAU,CAC3DrC,UAAW,IAAkD,QAAtCkB,EAAkBmB,EAASpD,aAAuC,IAApBiC,OAA6B,EAASA,EAAgBlB,UAAW4C,IAPlI0B,EAAgBrF,EAAMqF,mBACJpE,IAAlBoE,EACKA,EAEF,GAAGrE,OAAOL,EAAW,WAI5BV,KAAK,QAAWmD,IAAY,QAAWU,GAAU,QAAWV,SAAanC,IAEvEyE,GAAoB7C,EAIxB,OAHK6C,KAAyD,IAApCxC,EAAQyC,QAAQ,iBACxCD,GAAoB,CAAC,UAEH,gBAAoB,KAAS,OAAS,CACxDE,kBAAmBlD,GAClBY,EAAY,CACb3C,UAAWA,EACXV,IAAK2D,EACLiC,eAAgB,IAAW/C,GAAkB,OAAgB,CAAC,EAAG,GAAG9B,OAAOL,EAAW,eAAgBD,IACtGoF,WAAY/C,EACZgD,OAAQ7C,EACRN,WAAYA,EACZC,WAAY6C,GACZM,eAAgBxD,EAChByD,WAAY3D,EACZ4D,oBAAqB9D,EACrB+D,eAAgB9D,EAChB+D,aAAczC,EACd0C,SArCIf,EAA8BtF,EAAMsF,4BACtCC,EAAavF,EAAMuF,YACjB,gCAAiCvF,EAC5BsF,GAEDC,GAgCoC,WAAa,IACzDe,MA5CuB,mBAAZ7F,EACF+E,EAEFA,IA0CPe,qBAAsBvC,EACtBwC,aA9DY,SAAiBC,GAC7B,IAAIC,EAAiB1G,EAAM0G,eAC3BhD,GAAkB,GACdgD,GACFA,EAAeD,EAEnB,EAyDE9D,kBAAmBA,IACjB8C,GACN,CACA,IC7HA,ED6H4B,aAAiBzD,E,gLE5HtC,MAAM2E,EAAY,IAAI,KAAU,eAAgB,CACrD,KAAM,CACJC,UAAW,cACXC,gBAAiB,QACjBC,QAAS,GAEX,OAAQ,CACNF,UAAW,YACXC,gBAAiB,QACjBC,QAAS,KAGAC,EAAa,IAAI,KAAU,gBAAiB,CACvD,KAAM,CACJH,UAAW,YACXC,gBAAiB,QACjBC,QAAS,GAEX,OAAQ,CACNF,UAAW,cACXC,gBAAiB,QACjBC,QAAS,KAGAE,EAAc,IAAI,KAAU,iBAAkB,CACzD,KAAM,CACJJ,UAAW,cACXC,gBAAiB,YACjBC,QAAS,GAEX,OAAQ,CACNF,UAAW,YACXC,gBAAiB,YACjBC,QAAS,KAGAG,EAAe,IAAI,KAAU,kBAAmB,CAC3D,KAAM,CACJL,UAAW,YACXC,gBAAiB,YACjBC,QAAS,GAEX,OAAQ,CACNF,UAAW,cACXC,gBAAiB,YACjBC,QAAS,KAGAI,EAAc,IAAI,KAAU,iBAAkB,CACzD,KAAM,CACJN,UAAW,cACXC,gBAAiB,QACjBC,QAAS,GAEX,OAAQ,CACNF,UAAW,YACXC,gBAAiB,QACjBC,QAAS,KAGAK,EAAe,IAAI,KAAU,kBAAmB,CAC3D,KAAM,CACJP,UAAW,YACXC,gBAAiB,QACjBC,QAAS,GAEX,OAAQ,CACNF,UAAW,cACXC,gBAAiB,QACjBC,QAAS,KAGAM,EAAe,IAAI,KAAU,kBAAmB,CAC3D,KAAM,CACJR,UAAW,cACXC,gBAAiB,UACjBC,QAAS,GAEX,OAAQ,CACNF,UAAW,YACXC,gBAAiB,UACjBC,QAAS,KAGAO,EAAgB,IAAI,KAAU,mBAAoB,CAC7D,KAAM,CACJT,UAAW,YACXC,gBAAiB,UACjBC,QAAS,GAEX,OAAQ,CACNF,UAAW,cACXC,gBAAiB,UACjBC,QAAS,KAGPQ,EAAc,CAClB,WAAY,CACVC,YAAaZ,EACba,aAAcT,GAEhB,aAAc,CACZQ,YAAaP,EACbQ,aAAcP,GAEhB,aAAc,CACZM,YAAaL,EACbM,aAAcL,GAEhB,cAAe,CACbI,YAAaH,EACbI,aAAcH,IAGLI,EAAkB,CAACC,EAAOC,KACrC,MAAM,OACJC,GACEF,EACEG,EAAY,GAAGD,KAAUD,KACzB,YACJJ,EAAW,aACXC,GACEF,EAAYK,GAChB,MAAO,EAAC,OAAWE,EAAWN,EAAaC,EAAcE,EAAMI,mBAAoB,CACjF,CAAC,WACGD,mBACAA,kBACA,CACFjB,UAAW,WACXC,gBAAiB,QACjBC,QAAS,EACTiB,wBAAyBL,EAAMM,mBAC/B,YAAa,CACXpB,UAAW,aAGf,CAAC,GAAGiB,WAAoB,CACtBE,wBAAyBL,EAAMO,qB,2VC1I1BC,EAAyB,gBAAoB,MACjD,SAASC,EAAUC,EAAMC,GAC9B,YAAapH,IAATmH,EACK,KAEF,GAAGpH,OAAOoH,EAAM,KAAKpH,OAAOqH,EACrC,CAKO,SAASC,EAAUD,GAExB,OAAOF,EADE,aAAiBD,GACLG,EACvB,C,cCbItG,EAAY,CAAC,WAAY,UAIlBwG,EAA2B,gBAAoB,MAW3C,SAASC,EAA2BtE,GACjD,IAAId,EAAWc,EAAKd,SAClBqF,EAASvE,EAAKuE,OACdC,GAAY,OAAyBxE,EAAMnC,GACzC4G,EAAU,aAAiBJ,GAC3BK,GAAqB,EAAA/H,EAAA,GAAQ,WAC/B,OAhBgBgI,EAgBEF,EAhBMG,EAgBGJ,EAfzBK,GAAQ,OAAc,CAAC,EAAGF,GAC9BG,OAAOC,KAAKH,GAAQI,QAAQ,SAAUC,GACpC,IAAIC,EAAQN,EAAOK,QACLlI,IAAVmI,IACFL,EAAMI,GAAOC,EAEjB,GACOL,EART,IAAoBF,EAAQC,EACtBC,CAgBJ,EAAG,CAACJ,EAASD,GAAY,SAAUW,EAAMC,GACvC,QAAQb,GAAWY,EAAK,KAAOC,EAAK,KAAO,EAAAC,EAAA,GAAQF,EAAK,GAAIC,EAAK,IAAI,GACvE,GACA,OAAoB,gBAAoBf,EAAYiB,SAAU,CAC5DJ,MAAOR,GACNxF,EACL,CC5BA,IAAIqG,EAAY,GAILC,EAAmC,gBAAoB,MAC3D,SAASC,IACd,OAAO,aAAiBD,EAC1B,CAGO,IAAIE,EAAkC,gBAAoBH,GAC1D,SAASI,EAAYxB,GAC1B,IAAIyB,EAAgB,aAAiBF,GACrC,OAAO,UAAc,WACnB,YAAoB3I,IAAboH,EAAyB,GAAGrH,QAAO,OAAmB8I,GAAgB,CAACzB,IAAayB,CAC7F,EAAG,CAACA,EAAezB,GACrB,CAIO,IAAI0B,EAA+B,gBAAoB,MCpB9D,EADkC,gBAAoB,CAAC,G,UCCvD,SAASC,EAAUC,GACjB,IAAIC,EAAkBC,UAAUC,OAAS,QAAsBnJ,IAAjBkJ,UAAU,IAAmBA,UAAU,GACrF,IAAI,EAAAE,EAAA,GAAUJ,GAAO,CACnB,IAAIK,EAAWL,EAAKK,SAASC,cACzBC,EAEJ,CAAC,QAAS,SAAU,WAAY,UAAUC,SAASH,IAEnDL,EAAKS,mBAEQ,MAAbJ,KAAsBL,EAAKU,aAAa,QAGpCC,EAAeX,EAAKU,aAAa,YACjCE,EAAcC,OAAOF,GAGrBG,EAAW,KAWf,OAVIH,IAAiBE,OAAOE,MAAMH,GAChCE,EAAWF,EACFL,GAAmC,OAAbO,IAC/BA,EAAW,GAITP,GAAsBP,EAAKgB,WAC7BF,EAAW,MAEO,OAAbA,IAAsBA,GAAY,GAAKb,GAAmBa,EAAW,EAC9E,CACA,OAAO,CACT,CACO,SAASG,EAAiBjB,GAC/B,IAAIC,EAAkBC,UAAUC,OAAS,QAAsBnJ,IAAjBkJ,UAAU,IAAmBA,UAAU,GACjFgB,GAAM,OAAmBlB,EAAKmB,iBAAiB,MAAMC,OAAO,SAAUC,GACxE,OAAOtB,EAAUsB,EAAOpB,EAC1B,GAIA,OAHIF,EAAUC,EAAMC,IAClBiB,EAAII,QAAQtB,GAEPkB,CACT,C,wBCpCIK,EAAOlL,EAAA,EAAQkL,KACjBC,EAAQnL,EAAA,EAAQmL,MAChBC,EAAKpL,EAAA,EAAQoL,GACbC,EAAOrL,EAAA,EAAQqL,KACfC,EAAQtL,EAAA,EAAQsL,MAChBvL,EAAMC,EAAA,EAAQD,IACdwL,EAAOvL,EAAA,EAAQuL,KACfC,EAAMxL,EAAA,EAAQwL,IACZC,EAAY,CAACL,EAAIC,EAAMH,EAAMC,GAkF1B,SAASO,EAAqBC,EAAWC,GAE9C,OADWhB,EAAiBe,GAAW,GAC3BZ,OAAO,SAAUc,GAC3B,OAAOD,EAASE,IAAID,EACtB,EACF,CACA,SAASE,EAAoBC,EAAsBJ,EAAUK,GAC3D,IAAI9K,EAAS0I,UAAUC,OAAS,QAAsBnJ,IAAjBkJ,UAAU,GAAmBA,UAAU,GAAK,EAEjF,IAAKmC,EACH,OAAO,KAIT,IAAIE,EAAoCR,EAAqBM,EAAsBJ,GAG/EO,EAAQD,EAAkCpC,OAC1CsC,EAAaF,EAAkCG,UAAU,SAAUR,GACrE,OAAOI,IAAqBJ,CAC9B,GAaA,OAZI1K,EAAS,GACS,IAAhBiL,EACFA,EAAaD,EAAQ,EAErBC,GAAc,EAEPjL,EAAS,IAClBiL,GAAc,GAKTF,EAHPE,GAAcA,EAAaD,GAASA,EAItC,CACO,IAAIG,EAAkB,SAAyB3D,EAAM4D,GAC1D,IAAIX,EAAW,IAAIY,IACfC,EAAc,IAAIC,IAClBC,EAAc,IAAID,IAStB,OARA/D,EAAKC,QAAQ,SAAUC,GACrB,IAAI+D,EAAUC,SAASC,cAAc,kBAAkBpM,OAAOmH,EAAU0E,EAAI1D,GAAM,OAC9E+D,IACFhB,EAASmB,IAAIH,GACbD,EAAYK,IAAIJ,EAAS/D,GACzB4D,EAAYO,IAAInE,EAAK+D,GAEzB,GACO,CACLhB,SAAUA,EACVa,YAAaA,EACbE,YAAaA,EAEjB,EACO,SAAS7H,EAAiBmI,EAAMC,EAAWC,EAAOZ,EAAIa,EAAcC,EAASC,EAAYC,EAAkBC,EAA0BC,GAC1I,IAAIC,EAAS,WACTC,EAAY,WAChBA,EAAUlK,QAAUyJ,EACpB,IAAIU,EAAW,WACbhJ,EAAA,EAAIiJ,OAAOH,EAAOjK,QACpB,EAMA,OALA,YAAgB,WACd,OAAO,WACLmK,GACF,CACF,EAAG,IACI,SAAUzH,GACf,IAAI2H,EAAQ3H,EAAE2H,MACd,GAAI,GAAGpN,OAAO+K,EAAW,CAACH,EAAOvL,EAAKwL,EAAMC,IAAMrB,SAAS2D,GAAQ,CACjE,IAAInF,EAAO0E,IACPU,EAAoBzB,EAAgB3D,EAAM4D,GAC1CyB,EAAqBD,EACvBnC,EAAWoC,EAAmBpC,SAC9Ba,EAAcuB,EAAmBvB,YACjCE,EAAcqB,EAAmBrB,YAI/BV,EA3FV,SAAyBgC,EAAerC,GAEtC,IADA,IAAInI,EAAUwK,GAAiBpB,SAASoB,cACjCxK,GAAS,CACd,GAAImI,EAASE,IAAIrI,GACf,OAAOA,EAETA,EAAUA,EAAQyK,aACpB,CACA,OAAO,IACT,CAkF6BC,CADH1B,EAAY2B,IAAIlB,GACkBtB,GAClDyC,EAAe1B,EAAYyB,IAAInC,GAC/BqC,EAhKV,SAAmBrB,EAAMsB,EAAapB,EAAOW,GAC3C,IAAIU,EACAzF,EAAO,OACPC,EAAO,OACPlG,EAAW,WACX2L,EAAS,SAGb,GAAa,WAATxB,GAAqBa,IAAUxC,EACjC,MAAO,CACLoD,eAAe,GAGnB,IAAIC,GAAS,QAAgB,OAAgB,CAAC,EAAGvD,EAAIrC,GAAOsC,EAAMrC,GAC9D4F,GAAa,QAAgB,QAAgB,QAAgB,OAAgB,CAAC,EAAG1D,EAAMiC,EAAQnE,EAAOD,GAAOoC,EAAOgC,EAAQpE,EAAOC,GAAOqC,EAAMvI,GAAWwI,EAAOxI,GAClK+L,GAAW,QAAgB,QAAgB,QAAgB,QAAgB,QAAgB,OAAgB,CAAC,EAAGzD,EAAIrC,GAAOsC,EAAMrC,GAAOsC,EAAOxI,GAAW/C,EAAK0O,GAASvD,EAAMiC,EAAQrK,EAAW2L,GAAStD,EAAOgC,EAAQsB,EAAS3L,GAUrO,OADsF,QAA1E0L,EARE,CACZG,OAAQA,EACRC,WAAYA,EACZC,SAAUA,EACVC,UAAWH,EACXI,cAAeF,EACfG,YAAaH,GAEgB,GAAGnO,OAAOuM,GAAMvM,OAAO6N,EAAc,GAAK,eAAkC,IAAbC,OAAsB,EAASA,EAASV,IAEpI,KAAK/E,EACH,MAAO,CACL5H,QAAS,EACT8N,SAAS,GAEb,KAAKjG,EACH,MAAO,CACL7H,OAAQ,EACR8N,SAAS,GAEb,KAAKR,EACH,MAAO,CACLtN,QAAS,EACT8N,SAAS,GAEb,KAAKnM,EACH,MAAO,CACL3B,OAAQ,EACR8N,SAAS,GAEb,QACE,OAAO,KAEb,CA+GsBC,CAAUjC,EAAgD,IAA1CK,EAAWe,GAAc,GAAMvE,OAAcqD,EAAOW,GAGpF,IAAKQ,GAAaR,IAAUvC,GAAQuC,IAAUtC,EAC5C,QAIEC,EAAUtB,SAAS2D,IAAU,CAACvC,EAAMC,GAAKrB,SAAS2D,KACpD3H,EAAE1B,iBAEJ,IAAI0K,EAAW,SAAkBC,GAC/B,GAAIA,EAAa,CACf,IAAIC,EAAqBD,EAGrBE,EAAOF,EAAYtC,cAAc,KACjCwC,SAAoCA,EAAKjF,aAAa,UACxDgF,EAAqBC,GAEvB,IAAIC,EAAY5C,EAAYyB,IAAIgB,GAChC7B,EAAiBgC,GAOjB3B,IACAF,EAAOjK,SAAU,EAAAmB,EAAA,GAAI,WACf+I,EAAUlK,UAAY8L,GACxBF,EAAmBpL,OAEvB,EACF,CACF,EACA,GAAI,CAACsH,EAAMC,GAAKrB,SAAS2D,IAAUQ,EAAUW,UAAYhD,EAAkB,CAGzE,IAAID,EAQAwD,EACAC,EAAoB/D,EALtBM,EAHGC,GAA6B,WAATgB,EAtJjC,SAAyBL,GAEvB,IADA,IAAInJ,EAAUmJ,EACPnJ,GAAS,CACd,GAAIA,EAAQ4G,aAAa,kBACvB,OAAO5G,EAETA,EAAUA,EAAQyK,aACpB,CAIA,OAAO,IACT,CA6IiCwB,CAAgBzD,GAFhBmB,EAAa3J,QAO6BmI,GAEjE4D,EADE1B,IAAUvC,EACIkE,EAAkB,GACzB3B,IAAUtC,EACHiE,EAAkBA,EAAkB3F,OAAS,GAE7CiC,EAAoBC,EAAsBJ,EAAUK,EAAkBqC,EAAUnN,QAGlGgO,EAASK,EAGX,MAAO,GAAIlB,EAAUI,cAEnBlB,EAAyBa,QAEpB,GAAIC,EAAUnN,OAAS,EAC5BqM,EAAyBa,GAAc,GACvCT,IACAF,EAAOjK,SAAU,EAAAmB,EAAA,GAAI,WAEnBmJ,EAAoBzB,EAAgB3D,EAAM4D,GAC1C,IAAIoD,EAAY1D,EAAiB5B,aAAa,iBAI1CmF,EAAgBzD,EAHIc,SAAS+C,eAAeD,GAGW5B,EAAkBnC,UAG7EuD,EAASK,EACX,EAAG,QACE,GAAIlB,EAAUnN,OAAS,EAAG,CAC/B,IAAI0O,EAAUvC,EAAWe,GAAc,GACnCyB,EAAYD,EAAQA,EAAQ/F,OAAS,GACrCiG,EAAoBtD,EAAY2B,IAAI0B,GAGxCtC,EAAyBsC,GAAW,GACpCX,EAASY,EACX,CACF,CAGAtC,SAA0DA,EAAgBtH,EAC5E,CACF,CCvQA,IAAI6J,EAAa,yBACbC,EAAa,SAAoBJ,GACnC,OAAOA,EAAQK,KAAKF,EACtB,EAIWG,EAAe,eACX,SAASC,IACtB,IAAInN,EAAkB,WAAe,CAAC,GAEpCoN,GADmB,OAAepN,EAAiB,GACZ,GACrCqN,GAAc,IAAAC,QAAO,IAAI7D,KACzB8D,GAAc,IAAAD,QAAO,IAAI7D,KACzB+D,EAAmB,WAAe,IACpCC,GAAmB,OAAeD,EAAkB,GACpDE,EAAeD,EAAiB,GAChCE,EAAkBF,EAAiB,GACjCG,GAAY,IAAAN,QAAO,GACnBO,GAAa,IAAAP,SAAO,GAMpBQ,GAAe,IAAAC,aAAY,SAAUnI,EAAKgH,GAO5C,IAAIoB,EAAgBhB,EAAWJ,GAC/BW,EAAY/M,QAAQuJ,IAAIiE,EAAepI,GACvCyH,EAAY7M,QAAQuJ,IAAInE,EAAKoI,GAC7BJ,EAAUpN,SAAW,EACrB,IC1CsByN,ED0ClB3E,EAAKsE,EAAUpN,QC1CGyN,ED2CZ,WACJ3E,IAAOsE,EAAUpN,UAjBlBqN,EAAWrN,SACd4M,EAAoB,CAAC,GAmBvB,EC7CFc,QAAQC,UAAUC,KAAKH,ED8CvB,EAAG,IACCI,GAAiB,IAAAN,aAAY,SAAUnI,EAAKgH,GAC9C,IAAIoB,EAAgBhB,EAAWJ,GAC/BW,EAAY/M,QAAQ8N,OAAON,GAC3BX,EAAY7M,QAAQ8N,OAAO1I,EAC7B,EAAG,IACC2I,GAAsB,IAAAR,aAAY,SAAUrI,GAC9CiI,EAAgBjI,EAClB,EAAG,IACC2E,GAAa,IAAA0D,aAAY,SAAUjJ,EAAU0J,GAC/C,IAAIC,EAAWpB,EAAY7M,QAAQ2K,IAAIrG,IAAa,GAChDY,EAAmB+I,EAhDPC,MAAM3B,GAoDtB,OAHIyB,GAAmBd,EAAaxG,SAASxB,EAAK,KAChDA,EAAKsC,QAAQkF,GAERxH,CACT,EAAG,CAACgI,IACAiB,GAAe,IAAAZ,aAAY,SAAUa,EAAU9J,GACjD,OAAO8J,EAAS9G,OAAO,SAAU+G,GAC/B,YAAgBnR,IAATmR,CACT,GAAGC,KAAK,SAAUC,GAEhB,OADkB1E,EAAW0E,GAAS,GACnB7H,SAASpC,EAC9B,EACF,EAAG,CAACuF,IAYA2E,GAAiB,IAAAjB,aAAY,SAAUnI,GACzC,IAAIoI,EAAgB,GAAGvQ,OAAO4P,EAAY7M,QAAQ2K,IAAIvF,IAAMnI,OAAOsP,GAC/D6B,EAAW,IAAIrF,IAMnB,OALA,OAAmBgE,EAAY/M,QAAQkF,QAAQC,QAAQ,SAAUoJ,GAC3DA,EAAQE,WAAWjB,IACrBY,EAAS9E,IAAIyD,EAAY/M,QAAQ2K,IAAI4D,GAEzC,GACOH,CACT,EAAG,IAMH,OALA,YAAgB,WACd,OAAO,WACLf,EAAWrN,SAAU,CACvB,CACF,EAAG,IACI,CAELsN,aAAcA,EACdO,eAAgBA,EAChBE,oBAAqBA,EAErBI,aAAcA,EACdtE,WAAYA,EACZD,QAlCY,WACZ,IAAI1E,GAAO,OAAmB2H,EAAY7M,QAAQkF,QAIlD,OAHIgI,EAAa7G,QACfnB,EAAKwJ,KAAKhC,GAELxH,CACT,EA6BEsJ,eAAgBA,EAEpB,CExGe,SAASG,EAAgBC,GACtC,IAAIC,EAAS,SAAaD,GAC1BC,EAAO7O,QAAU4O,EACjB,IAAInB,EAAW,cAAkB,WAE/B,IADA,IAAIqB,EACKC,EAAO3I,UAAUC,OAAQ2I,EAAO,IAAIC,MAAMF,GAAOG,EAAO,EAAGA,EAAOH,EAAMG,IAC/EF,EAAKE,GAAQ9I,UAAU8I,GAEzB,OAA8C,QAAtCJ,EAAkBD,EAAO7O,eAAyC,IAApB8O,OAA6B,EAASA,EAAgBrO,KAAK0O,MAAML,EAAiB,CAACD,GAAQ5R,OAAO+R,GAC1J,EAAG,IACH,OAAOJ,EAAOnB,OAAWvQ,CAC3B,CCdA,IAAIkS,GAAeC,KAAKC,SAASC,QAAQ,GAAGC,WAAWC,MAAM,GACzDC,GAAa,E,sECFF,SAASC,GAAUrL,EAAU4C,EAAU0I,EAAcC,GAClE,IAAIC,EAAoB,aAAiBtL,GACvCiF,EAAYqG,EAAkBrG,UAC9BsG,EAAWD,EAAkBC,SAC7BC,EAAaF,EAAkBE,WAC7BC,EAAM,CACRC,OAAQzG,IAAcnF,GAoBxB,OAhBK4C,IACH+I,EAAIL,aAAe,SAAUO,GAC3BP,SAAoDA,EAAa,CAC/DxK,IAAKd,EACL6L,SAAUA,IAEZJ,EAASzL,EACX,EACA2L,EAAIJ,aAAe,SAAUM,GAC3BN,SAAoDA,EAAa,CAC/DzK,IAAKd,EACL6L,SAAUA,IAEZH,EAAW1L,EACb,GAEK2L,CACT,CC3Be,SAASG,GAAkBC,GACxC,IAAIP,EAAoB,aAAiBtL,GACvCgF,EAAOsG,EAAkBtG,KACzB8G,EAAMR,EAAkBQ,IACxBC,EAAeT,EAAkBS,aACnC,GAAa,WAAT/G,EACF,OAAO,KAGT,OAAO8G,EAAM,CACXE,aAFQH,EAEYE,GAClB,CACFE,YAJQJ,EAIWE,EAEvB,CCde,SAASG,GAAKvQ,GAC3B,IAGIwQ,EAHAtU,EAAO8D,EAAK9D,KACdJ,EAAQkE,EAAKlE,MACboD,EAAWc,EAAKd,SAElB,OAAa,OAAThD,IAA0B,IAATA,EACZ,MAEW,mBAATA,EACTsU,EAAwB,gBAAoBtU,GAAM,OAAc,CAAC,EAAGJ,IAC3C,kBAATI,IAEhBsU,EAAWtU,GAENsU,GAAYtR,GAAY,KACjC,CChBA,IAAI,GAAY,CAAC,QAOV,SAASuR,GAAazQ,GAC3B,IAAIkO,EAAOlO,EAAKkO,KACdwC,GAAW,OAAyB1Q,EAAM,IAO5C,OANA8E,OAAO6L,eAAeD,EAAU,OAAQ,CACtClG,IAAK,WAEH,OADA,EAAAoG,EAAA,KAAQ,EAAO,uHACR1C,CACT,IAEKwC,CACT,CCTA,IAAI,GAAY,CAAC,QAAS,YAAa,cACrCG,GAAa,CAAC,QAAS,YAAa,WAAY,UAAW,WAAY,WAAY,WAAY,OAAQ,eAAgB,eAAgB,UAAW,YAAa,WAC/JC,GAAa,CAAC,UAmBZC,GAA8B,SAAUC,IAC1C,QAAUD,EAAgBC,GAC1B,IAAIC,GAAS,QAAaF,GAC1B,SAASA,IAEP,OADA,QAAgBG,KAAMH,GACfE,EAAOjC,MAAMkC,KAAMjL,UAC5B,CAuBA,OAtBA,QAAa8K,EAAgB,CAAC,CAC5B9L,IAAK,SACLC,MAAO,WACL,IAAIiM,EAAcD,KAAKpV,MACrBsV,EAAQD,EAAYC,MACpBC,EAAYF,EAAYE,UACxBC,EAAaH,EAAYG,WACzB9M,GAAY,OAAyB2M,EAAa,IAMhDI,GAAc,EAAAC,GAAA,GAAKhN,EAAW,CAAC,WAAY,iBAAkB,cAAe,iBAEhF,OADA,EAAAoM,EAAA,KAASS,EAAW,2EACA,gBAAoB,IAASI,MAAM,OAAS,CAAC,EAAGJ,EAAW,CAC7ED,MAAwB,iBAAVA,EAAqBA,OAAQrU,GAC1CwU,EAAa,CACdxV,IAAKuV,IAET,KAEKP,CACT,CA9BkC,CA8BhC,aAIEW,GAAgC,aAAiB,SAAU5V,EAAOC,GACpE,IAAI4V,EAAQ7V,EAAM6V,MAChB9U,EAAYf,EAAMe,UAClBsH,EAAWrI,EAAMqI,SAEjB4C,GADUjL,EAAM8V,QACL9V,EAAMiL,UACjB8K,EAAW/V,EAAM+V,SACjB3S,EAAWpD,EAAMoD,SACjB4S,EAAOhW,EAAMgW,KACbrC,EAAe3T,EAAM2T,aACrBC,EAAe5T,EAAM4T,aACrBqC,EAAUjW,EAAMiW,QAChBC,EAAYlW,EAAMkW,UAClBC,EAAUnW,EAAMmW,QAChBzN,GAAY,OAAyB1I,EAAO+U,IAC1CqB,EAAY9N,EAAUD,GACtBwL,EAAoB,aAAiBtL,GACvC5H,EAAYkT,EAAkBlT,UAC9B0V,EAAcxC,EAAkBwC,YAChCC,EAAkBzC,EAAkB5I,SACpCsL,EAAmB1C,EAAkB0C,iBACrCC,EAAkB3C,EAAkBkC,SACpCU,EAAe5C,EAAkB4C,aACjC3C,EAAWD,EAAkBC,SAE7B4C,EADuB,aAAiB,GACKA,wBAC3CC,EAAU,GAAG3V,OAAOL,EAAW,SAC/BiW,EAAoB,WACpBpB,EAAa,WACbqB,EAAiBP,GAAmBrL,EACpC6L,GAAe,SAAc7W,EAAKuV,GAClCuB,EAAgBlN,EAAYxB,GAQhC,IAAI2O,EAAe,SAAsBvQ,GACvC,MAAO,CACL0C,IAAKd,EAEL8H,SAAS,OAAmB4G,GAAeE,UAC3C7E,KAAMwE,EAAkB7S,QACxBmQ,SAAUzN,EAEd,EAGIyQ,EAAiBnB,GAAYS,EAG7BW,EAAazD,GAAUrL,EAAUwO,EAAgBlD,EAAcC,GACjEK,EAASkD,EAAWlD,OACpBmD,GAAc,OAAyBD,EAAYnC,IAGjDqC,EAAWZ,EAAahM,SAASpC,GAGjCiP,EAAiBnD,GAAkB4C,EAAc3M,QAgCjDmN,EAAkB,CAAC,EACJ,WAAfvX,EAAMgW,OACRuB,EAAgB,iBAAmBF,GAErC,IAAIG,EAA0B,gBAAoBvC,IAAgB,OAAS,CACzEhV,IAAK2W,EACLpB,WAAYsB,EACZd,KAAe,OAATA,EAAgB,OAASA,GAAQ,WACvCjL,SAAUE,EAAW,MAAQ,EAC7B,eAAgBsL,GAAoBH,EAAY,KAAOA,IACtD,EAAAV,GAAA,GAAKhN,EAAW,CAAC,UAAW0O,EAAaG,EAAiB,CAC3DE,UAAW,KACX,gBAAiBxM,EACjB4K,OAAO,QAAc,OAAc,CAAC,EAAGyB,GAAiBzB,GACxD9U,UAAW,IAAW4V,GAAS,QAAgB,QAAgB,OAAgB,CAAC,EAAG,GAAG3V,OAAO2V,EAAS,WAAY1C,GAAS,GAAGjT,OAAO2V,EAAS,aAAcU,GAAW,GAAGrW,OAAO2V,EAAS,aAAcE,GAAiB9V,GACzNkV,QA5CoB,SAAyBxP,GAC7C,IAAIoQ,EAAJ,CAGA,IAAIa,EAAOV,EAAavQ,GACxBwP,SAA0CA,EAAQtB,GAAa+C,IAC/DrB,EAAYqB,EAHZ,CAIF,EAsCExB,UArCsB,SAA2BzP,GAEjD,GADAyP,SAA8CA,EAAUzP,GACpDA,EAAE2H,QAAU9N,EAAA,EAAQsL,MAAO,CAC7B,IAAI8L,EAAOV,EAAavQ,GAGxBwP,SAA0CA,EAAQtB,GAAa+C,IAC/DrB,EAAYqB,EACd,CACF,EA6BEvB,QAvBoB,SAAyB1P,GAC7CqN,EAASzL,GACT8N,SAA0CA,EAAQ1P,EACpD,IAqBIrD,EAAuB,gBAAoBqR,GAAM,CACnDzU,OAAO,QAAc,OAAc,CAAC,EAAGA,GAAQ,CAAC,EAAG,CACjD2X,WAAYN,IAEdjX,KAAM8W,KAOR,OALIR,IACFc,EAAad,EAAwBc,EAAYxX,EAAO,CACtDqX,SAAUA,KAGPG,CACT,GACA,SAASI,GAAS5X,EAAOC,GACvB,IAAIoI,EAAWrI,EAAMqI,SAGjBwP,EAAUlO,IACVmO,EAAmBjO,EAAYxB,GAWnC,OARA,YAAgB,WACd,GAAIwP,EAEF,OADAA,EAAQxG,aAAahJ,EAAUyP,GACxB,WACLD,EAAQjG,eAAevJ,EAAUyP,EACnC,CAEJ,EAAG,CAACA,IACAD,EACK,KAIW,gBAAoBjC,IAAkB,OAAS,CAAC,EAAG5V,EAAO,CAC5EC,IAAKA,IAET,CACA,OAA4B,aAAiB2X,ICnNzC,GAAY,CAAC,YAAa,YAI1BG,GAAsB,SAA6B7T,EAAMjE,GAC3D,IAAIc,EAAYmD,EAAKnD,UACnBqC,EAAWc,EAAKd,SAChBsF,GAAY,OAAyBxE,EAAM,IACzC2P,EAAoB,aAAiBtL,GACvC5H,EAAYkT,EAAkBlT,UAC9B4M,EAAOsG,EAAkBtG,KACzB8G,EAAMR,EAAkBQ,IAC1B,OAAoB,gBAAoB,MAAM,OAAS,CACrDtT,UAAW,IAAWJ,EAAW0T,GAAO,GAAGrT,OAAOL,EAAW,QAAS,GAAGK,OAAOL,EAAW,QAAS,GAAGK,OAAOL,EAAW,KAAKK,OAAgB,WAATuM,EAAoB,SAAW,YAAaxM,GACjLiV,KAAM,QACLtN,EAAW,CACZ,kBAAkB,EAClBzI,IAAKA,IACHmD,EACN,EACI4U,GAA2B,aAAiBD,IAChDC,GAAYC,YAAc,cAC1B,U,WCrBO,SAASC,GAAc9U,EAAU+M,GACtC,OAAO,EAAAgI,GAAA,GAAQ/U,GAAUgV,IAAI,SAAU9M,EAAO+M,GAC5C,GAAkB,iBAAqB/M,GAAQ,CAC7C,IAAIgN,EAAWC,EACXpP,EAAMmC,EAAMnC,IACZd,EAA+H,QAAnHiQ,EAA6C,QAAhCC,EAAejN,EAAMtL,aAAoC,IAAjBuY,OAA0B,EAASA,EAAalQ,gBAAoC,IAAdiQ,EAAuBA,EAAYnP,EAC/Jd,UAEbA,EAAW,WAAWrH,OAAO,GAAGA,QAAO,OAAmBmP,GAAU,CAACkI,IAAQ7H,KAAK,OAEpF,IAAIgI,EAAa,CACfrP,IAAKd,EACLA,SAAUA,GAKZ,OAAoB,eAAmBiD,EAAOkN,EAChD,CACA,OAAOlN,CACT,EACF,C,eCxBIpK,GAAqB,CACvBC,QAAS,EACTC,QAAS,GAEAsB,GAAa,CACtBpB,QAAS,CACPC,OAAQ,CAAC,KAAM,MACfC,SAAUN,IAEZS,SAAU,CACRJ,OAAQ,CAAC,KAAM,MACfC,SAAUN,IAEZU,WAAY,CACVL,OAAQ,CAAC,KAAM,MACfC,SAAUN,IAEZY,YAAa,CACXP,OAAQ,CAAC,KAAM,MACfC,SAAUN,IAEZuX,QAAS,CACPlX,OAAQ,CAAC,KAAM,MACfC,SAAUN,IAEZwX,WAAY,CACVnX,OAAQ,CAAC,KAAM,MACfC,SAAUN,IAEZyX,SAAU,CACRpX,OAAQ,CAAC,KAAM,MACfC,SAAUN,IAEZ0X,YAAa,CACXrX,OAAQ,CAAC,KAAM,MACfC,SAAUN,KAGH2X,GAAgB,CACzBvX,QAAS,CACPC,OAAQ,CAAC,KAAM,MACfC,SAAUN,IAEZS,SAAU,CACRJ,OAAQ,CAAC,KAAM,MACfC,SAAUN,IAEZU,WAAY,CACVL,OAAQ,CAAC,KAAM,MACfC,SAAUN,IAEZY,YAAa,CACXP,OAAQ,CAAC,KAAM,MACfC,SAAUN,IAEZyX,SAAU,CACRpX,OAAQ,CAAC,KAAM,MACfC,SAAUN,IAEZ0X,YAAa,CACXrX,OAAQ,CAAC,KAAM,MACfC,SAAUN,IAEZuX,QAAS,CACPlX,OAAQ,CAAC,KAAM,MACfC,SAAUN,IAEZwX,WAAY,CACVnX,OAAQ,CAAC,KAAM,MACfC,SAAUN,KCrEP,SAAS4X,GAAUvL,EAAMwL,EAAQC,GACtC,OAAID,IAGAC,EACKA,EAAezL,IAASyL,EAAeC,WADhD,EAIF,CCEA,IAAIC,GAAoB,CACtBhK,WAAY,aACZC,SAAU,WACV,gBAAiB,WACjB,iBAAkB,WAEL,SAASgK,GAAajV,GACnC,IAAIvD,EAAYuD,EAAKvD,UACnBqC,EAAUkB,EAAKlB,QACfI,EAAWc,EAAKd,SAChBkD,EAAQpC,EAAKoC,MACbR,EAAa5B,EAAK4B,WAClBD,EAAiB3B,EAAK2B,eACtBuT,EAAclV,EAAKkV,YACnBnO,EAAW/G,EAAK+G,SAChBsC,EAAOrJ,EAAKqJ,KACZlK,EAAkBa,EAAKb,gBACrBwQ,EAAoB,aAAiBtL,GACvC5F,EAAoBkR,EAAkBlR,kBACtC0R,EAAMR,EAAkBQ,IACxBgF,EAAmBxF,EAAkBwF,iBACrCC,EAAoBzF,EAAkByF,kBACtC1T,EAAoBiO,EAAkBjO,kBACtC2T,EAAuB1F,EAAkB0F,qBACzCC,EAAqB3F,EAAkB2F,mBACvCC,EAAgB5F,EAAkB4F,cAClCV,EAASlF,EAAkBkF,OAC3BC,EAAiBnF,EAAkBmF,eACjCzV,EAAkB,YAAe,GACnCC,GAAmB,OAAeD,EAAiB,GACnDmW,EAAelW,EAAiB,GAChCmW,EAAkBnW,EAAiB,GACjChB,EAAY6R,GAAM,QAAc,OAAc,CAAC,EAAGwE,IAAgBjT,IAAqB,QAAc,OAAc,CAAC,EAAGlD,IAAakD,GACpII,EAAiBkT,GAAkB3L,GACnCqM,EAAed,GAAUvL,EAAMwL,EAAQC,GACvCa,EAAkB,SAAaD,GACtB,WAATrM,IAKFsM,EAAgB9V,QAAU6V,GAE5B,IAAIE,GAAe,QAAc,OAAc,CAAC,EAAGD,EAAgB9V,SAAU,CAAC,EAAG,CAC/EgW,gBAAiB,GAAG/Y,OAAOL,EAAW,WACtCqZ,eAAe,EACfC,cAAc,IAIZC,EAAa,WASjB,OARA,YAAgB,WAId,OAHAA,EAAWnW,SAAU,EAAAmB,EAAA,GAAI,WACvByU,EAAgB3W,EAClB,GACO,WACLkC,EAAA,EAAIiJ,OAAO+L,EAAWnW,QACxB,CACF,EAAG,CAACf,IACgB,gBAAoB,KAAS,CAC/CrC,UAAWA,EACXkF,eAAgB,IAAW,GAAG7E,OAAOL,EAAW,WAAW,OAAgB,CAAC,EAAG,GAAGK,OAAOL,EAAW,QAAS0T,GAAMxO,EAAgB4T,GACnIpT,QAAkB,eAATkH,EAAwB,WAAa,KAC9C5K,kBAAmBA,EACnBiD,kBAAmBpD,EACnBwD,eAAgBA,EAChBI,aAAcsT,EACdpT,MAAOA,EACPR,WAAYA,EACZG,WAAYmT,GAAe,CACzB3X,OAAQ2X,GAEVrT,OAAQkF,EAAW,GAAK,CAACsO,GACzBY,gBAAiBd,EACjBe,gBAAiBd,EACjB/S,qBAAsBlD,EACtBgX,YAAab,EACbc,YAAaR,EACbS,OAAO,GACNnX,EACL,C,cClFe,SAASoX,GAAkBtW,GACxC,IAAI2I,EAAK3I,EAAK2I,GACZ4N,EAAOvW,EAAKuW,KACZtK,EAAUjM,EAAKiM,QACf/M,EAAWc,EAAKd,SACdsX,EAAY,SACZ7G,EAAoB,aAAiBtL,GACvC5H,EAAYkT,EAAkBlT,UAC9B6Y,EAAqB3F,EAAkB2F,mBACvCT,EAASlF,EAAkBkF,OAC3BC,EAAiBnF,EAAkBmF,eACnCzL,EAAOsG,EAAkBtG,KAGvBoN,EAAc,UAAa,GAC/BA,EAAY5W,QAAUwJ,IAASmN,EAI/B,IAAInX,EAAkB,YAAgBoX,EAAY5W,SAChDP,GAAmB,OAAeD,EAAiB,GACnDqX,EAAUpX,EAAiB,GAC3BqX,EAAarX,EAAiB,GAC5BsX,IAAaH,EAAY5W,SAAU0W,EAIvC,YAAgB,WACVE,EAAY5W,SACd8W,GAAW,EAEf,EAAG,CAACtN,IAGJ,IAAIuM,GAAe,OAAc,CAAC,EAAGhB,GAAU4B,EAAW3B,EAAQC,IAG9D7I,EAAQ/F,OAAS,IACnB0P,EAAaG,cAAe,GAI9B,IAAIc,EAAyBjB,EAAakB,iBAO1C,OANAlB,EAAakB,iBAAmB,SAAU/W,GAIxC,OAHK0W,EAAY5W,SAAYE,GAC3B4W,GAAW,GAENE,aAAuE,EAASA,EAAuB9W,EAChH,EACI2W,EACK,KAEW,gBAAoB,EAAqB,CAC3DrN,KAAMmN,EACNjS,QAASkS,EAAY5W,SACP,gBAAoB,OAAW,OAAS,CACtDf,QAAS8X,GACRhB,EAAc,CACfO,YAAab,EACbQ,eAAe,EACfD,gBAAiB,GAAG/Y,OAAOL,EAAW,aACpC,SAAUsa,GACZ,IAAIC,EAAkBD,EAAMla,UAC1Boa,EAAcF,EAAMpF,MACtB,OAAoB,gBAAoB,GAAa,CACnDhJ,GAAIA,EACJ9L,UAAWma,EACXrF,MAAOsF,GACN/X,EACL,GACF,CCzEA,IAAI,GAAY,CAAC,QAAS,YAAa,QAAS,WAAY,UAAW,WAAY,qBAAsB,WAAY,WAAY,aAAc,iBAAkB,cAAe,aAAc,UAAW,eAAgB,eAAgB,eAAgB,oBAAqB,qBAC5Q,GAAa,CAAC,UAkBZgY,GAA+B,aAAiB,SAAUpb,EAAOC,GACnE,IAAI4V,EAAQ7V,EAAM6V,MAChB9U,EAAYf,EAAMe,UAClBuU,EAAQtV,EAAMsV,MACdjN,EAAWrI,EAAMqI,SAEjB4C,GADUjL,EAAM8V,QACL9V,EAAMiL,UACjBoQ,EAAqBrb,EAAMqb,mBAC3BjY,EAAWpD,EAAMoD,SACjB2S,EAAW/V,EAAM+V,SACjBuF,EAAatb,EAAMsb,WACnBzV,EAAiB7F,EAAM6F,eACvBuT,EAAcpZ,EAAMoZ,YACpBtT,EAAa9F,EAAM8F,WACnBmQ,EAAUjW,EAAMiW,QAChBtC,EAAe3T,EAAM2T,aACrBC,EAAe5T,EAAM4T,aACrB2H,EAAevb,EAAMub,aACrBC,EAAoBxb,EAAMwb,kBAC1BC,EAAoBzb,EAAMyb,kBAC1B/S,GAAY,OAAyB1I,EAAO,IAC1CoW,EAAY9N,EAAUD,GACtBwL,EAAoB,aAAiBtL,GACvC5H,EAAYkT,EAAkBlT,UAC9B4M,EAAOsG,EAAkBtG,KACzBmO,EAAW7H,EAAkB6H,SAC7BpF,EAAkBzC,EAAkB5I,SACpCsL,EAAmB1C,EAAkB0C,iBACrC/I,EAAYqG,EAAkBrG,UAC9BiJ,EAAe5C,EAAkB4C,aACjCD,EAAkB3C,EAAkBkC,SACpC4F,EAAoB9H,EAAkByH,WACtCjF,EAAcxC,EAAkBwC,YAChCuF,EAAe/H,EAAkB+H,aACjC9H,EAAWD,EAAkBC,SAE7B+H,EADuB,aAAiB,GACQA,2BAEhD3J,EADuB,aAAiBnI,GACNmI,aAChCX,EAAgB1H,IAChBiS,EAAmB,GAAG9a,OAAOL,EAAW,YACxCkW,GAAiBP,GAAmBrL,EACpCuK,GAAa,WACbuG,GAAW,WAQf,IAAI7E,GAAiBnB,QAA2CA,EAAWS,EACvEwF,GAAmBV,QAA+CA,EAAaK,EAG/EM,GAAaP,EAASjR,SAASpC,GAC/BoS,IAAQlE,GAAoB0F,GAG5BC,GAAmBhK,EAAauE,EAAcpO,GAG9C8O,GAAazD,GAAUrL,EAAUwO,GAAgB2E,EAAmBC,GACtExH,GAASkD,GAAWlD,OACpBmD,IAAc,OAAyBD,GAAY,IAGjD5T,GAAkB,YAAe,GACnCC,IAAmB,OAAeD,GAAiB,GACnD4Y,GAAiB3Y,GAAiB,GAClC4Y,GAAoB5Y,GAAiB,GACnC6Y,GAAwB,SAA+BC,GACpDzF,IACHuF,GAAkBE,EAEtB,EAeIC,GAAe,UAAc,WAC/B,OAAItI,IAGS,WAAT1G,IACK4O,IAAkBjK,EAAa,CAAC1E,GAAYnF,GAGvD,EAAG,CAACkF,EAAM0G,GAAQzG,EAAW2O,GAAgB9T,EAAU6J,IAGnDoF,GAAiBnD,GAAkB5C,EAAcnH,QAqBjDoS,GAAoB9J,EAAgB,SAAUgF,GAChDzB,SAA0CA,EAAQtB,GAAa+C,IAC/DrB,EAAYqB,EACd,GAkBI+E,GAAUrG,GAAa,GAAGpV,OAAOoV,EAAW,UAC5CsG,GAAiB,UAAc,WACjC,OAAoB,gBAAoBjI,GAAM,CAC5CrU,KAAe,eAATmN,EAAwByO,QAAmB/a,EACjDjB,OAAO,QAAc,OAAc,CAAC,EAAGA,GAAQ,CAAC,EAAG,CACjD2c,OAAQlC,GAERmC,WAAW,KAEC,gBAAoB,IAAK,CACvC7b,UAAW,GAAGC,OAAO8a,EAAkB,YAE3C,EAAG,CAACvO,EAAMyO,GAAkBhc,EAAOya,GAAMqB,IAGrCe,GAAyB,gBAAoB,OAAO,OAAS,CAC/D7G,KAAM,WACNH,MAAOyB,GACPvW,UAAW,GAAGC,OAAO8a,EAAkB,UACvC/Q,SAAU8L,GAAiB,MAAQ,EACnC5W,IAAKuV,GACLF,MAAwB,iBAAVA,EAAqBA,EAAQ,KAC3C,eAAgBiB,GAAoBH,EAAY,KAAOA,EACvD,gBAAiBqE,GACjB,iBAAiB,EACjB,gBAAiBgC,GACjB,gBAAiB5F,GACjBZ,QAjEyB,SAA8BxP,GAEnDoQ,KAGJ0E,SAAoDA,EAAa,CAC/DpS,IAAKd,EACL6L,SAAUzN,IAIC,WAAT8G,GACFqO,EAAavT,GAAW4T,IAE5B,EAoDE9F,QAjCoB,WACpBrC,EAASzL,EACX,GAgCG+O,IAAc9B,EAAOoH,IAGpBI,GAAiB,SAAavP,GAMlC,GALa,WAATA,GAAqBgE,EAAcnH,OAAS,EAC9C0S,GAAe/Y,QAAU,WAEzB+Y,GAAe/Y,QAAUwJ,GAEtBgJ,EAAkB,CACrB,IAAIwG,GAAcD,GAAe/Y,QAIjC8Y,GAAyB,gBAAoB1D,GAAc,CACzD5L,KAAMwP,GACNpc,UAAWmb,EACX9Y,SAAUqY,GAAsBZ,IAAiB,WAATlN,EACxC1H,eAAgBA,EAChBuT,YAAaA,EACbtT,WAAYA,EACZQ,MAAoB,gBAAoB,EAEtC,CACAiH,KAAsB,eAAhBwP,GAA+B,WAAaA,IACpC,gBAAoB,GAAa,CAC/ClQ,GAAI4P,GACJxc,IAAK8b,IACJ3Y,IACH6H,SAAU4L,GACVxT,gBA1EuB,SAA8BY,GAC1C,WAATsJ,GACFqO,EAAavT,EAAUpE,EAE3B,GAuEK4Y,GACL,CAGA,IAAIG,GAAwB,gBAAoB,IAASrH,MAAM,OAAS,CACtE1V,IAAKA,EACL+V,KAAM,QACLtN,EAAW,CACZ+O,UAAW,KACX5B,MAAOA,EACP9U,UAAW,IAAW+a,EAAkB,GAAG9a,OAAO8a,EAAkB,KAAK9a,OAAOuM,GAAOxM,GAAW,QAAgB,QAAgB,QAAgB,OAAgB,CAAC,EAAG,GAAGC,OAAO8a,EAAkB,SAAUrB,IAAO,GAAGzZ,OAAO8a,EAAkB,WAAYS,IAAe,GAAGvb,OAAO8a,EAAkB,aAAcI,IAAmB,GAAGlb,OAAO8a,EAAkB,aAAcjF,KACjXlD,aA1IyB,SAA8BO,GACvDmI,IAAsB,GACtB1I,SAAoDA,EAAa,CAC/DxK,IAAKd,EACL6L,SAAUA,GAEd,EAqIEN,aApIyB,SAA8BM,GACvDmI,IAAsB,GACtBzI,SAAoDA,EAAa,CAC/DzK,IAAKd,EACL6L,SAAUA,GAEd,IA+HI2I,IAAYtG,GAAiC,gBAAoBiE,GAAmB,CACtF3N,GAAI4P,GACJhC,KAAMA,GACNtK,QAASoB,GACRnO,IAWH,OAVIyY,IACFmB,GAAWnB,EAA2BmB,GAAUhd,EAAO,CACrDqX,SAAU6E,GACVjI,OAAQsI,GACR9B,KAAMA,GACNxP,SAAU4L,MAKM,gBAAoB,EAAqB,CAC3DR,YAAamG,GACbjP,KAAe,eAATA,EAAwB,WAAaA,EAC3CwI,SAAUmB,GACVoE,WAAYU,IACXgB,GACL,GAoCA,OAnC2B,aAAiB,SAAUhd,EAAOC,GAC3D,IAiBIuX,EAjBAnP,EAAWrI,EAAMqI,SACnBjF,EAAWpD,EAAMoD,SACf0U,EAAmBjO,EAAYxB,GAC/B4U,EAAY/E,GAAc9U,EAAU0U,GAGpCD,EAAUlO,IAqBd,OAlBA,YAAgB,WACd,GAAIkO,EAEF,OADAA,EAAQxG,aAAahJ,EAAUyP,GACxB,WACLD,EAAQjG,eAAevJ,EAAUyP,EACnC,CAEJ,EAAG,CAACA,IAKFN,EADEK,EACWoF,EAEa,gBAAoB7B,IAAiB,OAAS,CACtEnb,IAAKA,GACJD,GAAQid,GAEO,gBAAoBrT,EAAmBJ,SAAU,CACnEJ,MAAO0O,GACNN,EACL,G,WCjSe,SAAS0F,GAAQhZ,GAC9B,IAAInD,EAAYmD,EAAKnD,UACnB8U,EAAQ3R,EAAK2R,MAEblV,EADsB,aAAiB4H,GACT5H,UAEhC,OADcgJ,IAEL,KAEW,gBAAoB,KAAM,CAC5CqM,KAAM,YACNjV,UAAW,IAAW,GAAGC,OAAOL,EAAW,iBAAkBI,GAC7D8U,MAAOA,GAEX,CChBA,IAAI,GAAY,CAAC,YAAa,QAAS,WAAY,YAO/CsH,GAAqC,aAAiB,SAAUnd,EAAOC,GACzE,IAAIc,EAAYf,EAAMe,UACpBuU,EAAQtV,EAAMsV,MAEdlS,GADWpD,EAAMqI,SACNrI,EAAMoD,UACjBsF,GAAY,OAAyB1I,EAAO,IAE5CW,EADsB,aAAiB4H,GACT5H,UAC5Byc,EAAiB,GAAGpc,OAAOL,EAAW,eAC1C,OAAoB,gBAAoB,MAAM,OAAS,CACrDV,IAAKA,EACL+V,KAAM,gBACLtN,EAAW,CACZuN,QAAS,SAAiBxP,GACxB,OAAOA,EAAE4W,iBACX,EACAtc,UAAW,IAAWqc,EAAgBrc,KACvB,gBAAoB,MAAO,CAC1CiV,KAAM,eACNjV,UAAW,GAAGC,OAAOoc,EAAgB,UACrC9H,MAAwB,iBAAVA,EAAqBA,OAAQrU,GAC1CqU,GAAqB,gBAAoB,KAAM,CAChDU,KAAM,QACNjV,UAAW,GAAGC,OAAOoc,EAAgB,UACpCha,GACL,GAiBA,OAhBiC,aAAiB,SAAUpD,EAAOC,GACjE,IAAIoI,EAAWrI,EAAMqI,SAGjB4U,EAAY/E,GAFHlY,EAAMoD,SACIyG,EAAYxB,IAGnC,OADcsB,IAELsT,EAEW,gBAAoBE,IAAuB,OAAS,CACtEld,IAAKA,IACJ,EAAAyV,GAAA,GAAK1V,EAAO,CAAC,aAAcid,EAChC,GC3CI,GAAY,CAAC,QAAS,WAAY,MAAO,OAAQ,SAOrD,SAASK,GAAoBC,EAAMC,EAAY7c,GAC7C,IAAI8c,EAAiBD,EAAWpL,KAC9BsL,EAAsBF,EAAWG,MACjCC,EAAgBJ,EAAWK,QAC3BC,EAAgBN,EAAWO,QAC7B,OAAQR,GAAQ,IAAInF,IAAI,SAAU4F,EAAK3F,GACrC,GAAI2F,GAAwB,YAAjB,QAAQA,GAAmB,CACpC,IAAI9Z,EAAO8Z,EACTC,EAAQ/Z,EAAK+Z,MACb7a,EAAWc,EAAKd,SAChB+F,EAAMjF,EAAKiF,IACX+U,EAAOha,EAAKga,KACZC,EAAQja,EAAKia,MACbzV,GAAY,OAAyBxE,EAAM,IACzCka,EAAYjV,QAAiCA,EAAM,OAAOnI,OAAOqX,GAGrE,OAAIjV,GAAqB,UAAT8a,EACD,UAATA,EAEkB,gBAAoBR,GAAqB,OAAS,CACpEvU,IAAKiV,GACJ1V,EAAW,CACZ4M,MAAO2I,IACLX,GAAoBla,EAAUoa,EAAY7c,IAI5B,gBAAoBid,GAAe,OAAS,CAC9DzU,IAAKiV,GACJ1V,EAAW,CACZ4M,MAAO2I,IACLX,GAAoBla,EAAUoa,EAAY7c,IAInC,YAATud,EACkB,gBAAoBJ,GAAe,OAAS,CAC9D3U,IAAKiV,GACJ1V,IAEe,gBAAoB+U,GAAgB,OAAS,CAC/DtU,IAAKiV,GACJ1V,EAAW,CACZyV,MAAOA,IACLF,KAAUE,GAAmB,IAAVA,IAA6B,gBAAoB,OAAQ,CAC9Epd,UAAW,GAAGC,OAAOL,EAAW,gBAC/Bwd,GACL,CACA,OAAO,IACT,GAAG9S,OAAO,SAAU2S,GAClB,OAAOA,CACT,EACF,CACO,SAASK,GAAWjb,EAAUkb,EAAOnO,EAASqN,EAAY7c,GAC/D,IAAI4d,EAAanb,EACbob,GAAmB,OAAc,CACnCT,QAASb,GACT9K,KAAM,GACNuL,MAAO,GACPE,QAAS,IACRL,GAIH,OAHIc,IACFC,EAAajB,GAAoBgB,EAAOE,EAAkB7d,IAErDuX,GAAcqG,EAAYpO,EACnC,CCvEA,IAAI,GAAY,CAAC,YAAa,gBAAiB,QAAS,YAAa,WAAY,QAAS,WAAY,YAAa,KAAM,OAAQ,kBAAmB,WAAY,mBAAoB,mBAAoB,oBAAqB,qBAAsB,kBAAmB,WAAY,YAAa,qBAAsB,aAAc,WAAY,sBAAuB,eAAgB,WAAY,aAAc,eAAgB,SAAU,iBAAkB,uBAAwB,oBAAqB,WAAY,aAAc,sBAAuB,oCAAqC,oBAAqB,UAAW,eAAgB,YAAa,gBAAiB,qBAAsB,0BAA2B,6BAA8B,uBAoCxtBsO,GAAa,GACbC,GAAoB,aAAiB,SAAU1e,EAAOC,GACxD,IAAI0e,EACAza,EAAOlE,EACT4e,EAAiB1a,EAAKvD,UACtBA,OAA+B,IAAnBie,EAA4B,UAAYA,EACpDnF,EAAgBvV,EAAKuV,cACrB5D,EAAQ3R,EAAK2R,MACb9U,EAAYmD,EAAKnD,UACjB8d,EAAgB3a,EAAK6G,SACrBA,OAA6B,IAAlB8T,EAA2B,EAAIA,EAC1CP,EAAQpa,EAAKoa,MACblb,EAAWc,EAAKd,SAChB0b,EAAY5a,EAAK4a,UACjBjS,EAAK3I,EAAK2I,GACVkS,EAAY7a,EAAKqJ,KACjBA,OAAqB,IAAdwR,EAAuB,WAAaA,EAC3CC,EAAkB9a,EAAK8a,gBACvB/T,EAAW/G,EAAK+G,SAChBgU,EAAmB/a,EAAK+a,iBACxBC,EAAwBhb,EAAKmV,iBAC7BA,OAA6C,IAA1B6F,EAAmC,GAAMA,EAC5DC,EAAwBjb,EAAKoV,kBAC7BA,OAA8C,IAA1B6F,EAAmC,GAAMA,EAC7D3F,EAAqBtV,EAAKsV,mBAC1B4F,EAAkBlb,EAAKkb,gBACvB1D,EAAWxX,EAAKwX,SAChBlO,EAAYtJ,EAAKsJ,UACjB6R,EAAqBnb,EAAKmb,mBAC1BC,EAAkBpb,EAAKqb,WACvBA,OAAiC,IAApBD,GAAoCA,EACjDE,GAAgBtb,EAAKub,SACrBA,QAA6B,IAAlBD,IAAmCA,GAC9CE,GAAsBxb,EAAKwb,oBAC3BjJ,GAAevS,EAAKuS,aACpBkJ,GAAWzb,EAAKyb,SAChBC,GAAa1b,EAAK0b,WAClBC,GAAoB3b,EAAKoQ,aACzBA,QAAqC,IAAtBuL,GAA+B,GAAKA,GACnD9G,GAAS7U,EAAK6U,OACdC,GAAiB9U,EAAK8U,eACtB8G,GAAwB5b,EAAKqV,qBAC7BA,QAAiD,IAA1BuG,GAAmC,QAAUA,GACpEla,GAAoB1B,EAAK0B,kBACzBmQ,GAAW7R,EAAK6R,SAChBuF,GAAapX,EAAKoX,WAClByE,GAAwB7b,EAAK8b,oBAC7BA,QAAgD,IAA1BD,GAAmC,MAAQA,GACjEE,GAAoC/b,EAAK+b,kCACzCtd,GAAoBuB,EAAKvB,kBACzBsT,GAAU/R,EAAK+R,QACf2F,GAAe1X,EAAK0X,aACpB1F,GAAYhS,EAAKgS,UAGjBQ,IAFgBxS,EAAKgc,cACAhc,EAAKic,mBACAjc,EAAKwS,yBAC/BmF,GAA6B3X,EAAK2X,2BAClCuE,GAAsBlc,EAAKkc,oBAC3B1X,IAAY,OAAyBxE,EAAM,IACzCmc,GAAiB,UAAc,WAC/B,MAAO,CAAChC,GAAWjb,EAAUkb,EAAOG,GAAY2B,GAAqBzf,GAAY0d,GAAWjb,EAAUkb,EAAOG,GAAY,CAAC,EAAG9d,GAC/H,EAAG,CAACyC,EAAUkb,EAAO8B,KACrBE,IAAkB,OAAeD,GAAgB,GACjDpD,GAAYqD,GAAgB,GAC5BC,GAAmBD,GAAgB,GACjC/c,GAAkB,YAAe,GACnCC,IAAmB,OAAeD,GAAiB,GACnDid,GAAUhd,GAAiB,GAC3Bid,GAAajd,GAAiB,GAC5BkK,GAAe,WACftF,GhB3GS,SAAiByE,GAC9B,IAAI6T,GAAkB,EAAAC,EAAA,GAAe9T,EAAI,CACrCzD,MAAOyD,IAET+T,GAAmB,OAAeF,EAAiB,GACnDtY,EAAOwY,EAAiB,GACxBC,EAAUD,EAAiB,GAM7B,OALA,YAAgB,WACdnN,IAAc,EACd,IAAIqN,EAAmD,GAAG9f,OAAOmS,GAAc,KAAKnS,OAAOyS,IAC3FoN,EAAQ,gBAAgB7f,OAAO8f,GACjC,EAAG,IACI1Y,CACT,CgB8Fa2Y,CAAQlU,GACfY,GAAsB,QAAdqR,EAQZ,IAAI4B,IAAkB,EAAAC,EAAA,GAAevB,EAAiB,CAClDhW,MAAOsS,EACPsF,UAAW,SAAmB/X,GAC5B,OAAOA,GAAQwV,EACjB,IAEFmC,IAAmB,OAAeF,GAAiB,GACnDO,GAAiBL,GAAiB,GAClCM,GAAoBN,GAAiB,GAInCO,GAAkB,SAAyBlY,GAE7C,SAASmY,IACPF,GAAkBjY,GAClB2S,UAAoDA,GAAa3S,EACnE,CAJiBkB,UAAUC,OAAS,QAAsBnJ,IAAjBkJ,UAAU,IAAmBA,UAAU,IAM9E,IAAAkX,WAAUD,GAEVA,GAEJ,EAGIrQ,GAAmB,WAAekQ,IACpCjQ,IAAmB,OAAeD,GAAkB,GACpDuQ,GAAsBtQ,GAAiB,GACvCuQ,GAAyBvQ,GAAiB,GACxCwQ,GAAW,UAAa,GAGxBC,GAAkB,UAAc,WAChC,MAAc,WAATlU,GAA8B,aAATA,IAAwByR,EAG3C,CAACzR,GAAM,GAFL,CAAC,WAAYyR,EAGxB,EAAG,CAACzR,EAAMyR,IACV0C,IAAkB,OAAeD,GAAiB,GAClDE,GAAaD,GAAgB,GAC7BE,GAAwBF,GAAgB,GACtCG,GAA8B,WAAfF,GACfG,GAAmB,WAAeH,IACpCI,IAAmB,OAAeD,GAAkB,GACpDE,GAAeD,GAAiB,GAChCE,GAAkBF,GAAiB,GACjCG,GAAmB,WAAeN,IACpCO,IAAmB,OAAeD,GAAkB,GACpDE,GAA0BD,GAAiB,GAC3CE,GAA6BF,GAAiB,GAChD,YAAgB,WACdF,GAAgBN,IAChBU,GAA2BT,IACtBJ,GAASzd,UAIV8d,GACFX,GAAkBI,IAGlBH,GAAgB1C,IAEpB,EAAG,CAACkD,GAAYC,KAGhB,IAAIU,GAAmB,WAAe,GACpCC,IAAoB,OAAeD,GAAkB,GACrDE,GAAmBD,GAAkB,GACrCE,GAAsBF,GAAkB,GACtCG,GAAaF,IAAoBvF,GAAU7S,OAAS,GAAsB,eAAjB4X,IAAiC/C,EAG9F,YAAgB,WACV4C,IACFN,GAAuBN,GAE3B,EAAG,CAACA,KACJ,YAAgB,WAEd,OADAO,GAASzd,SAAU,EACZ,WACLyd,GAASzd,SAAU,CACrB,CACF,EAAG,IAGH,IAAI4e,GAAiBjS,IACnBW,GAAesR,GAAetR,aAC9BO,GAAiB+Q,GAAe/Q,eAChCE,GAAsB6Q,GAAe7Q,oBACrCI,GAAeyQ,GAAezQ,aAC9BtE,GAAa+U,GAAe/U,WAC5BD,GAAUgV,GAAehV,QACzB4E,GAAiBoQ,GAAepQ,eAC9BqQ,GAAsB,UAAc,WACtC,MAAO,CACLvR,aAAcA,GACdO,eAAgBA,GAEpB,EAAG,CAACP,GAAcO,KACdiR,GAAkB,UAAc,WAClC,MAAO,CACL3Q,aAAcA,GAElB,EAAG,CAACA,KACJ,YAAgB,WACdJ,GAAoB4Q,GAAajE,GAAaxB,GAAUzJ,MAAMgP,GAAmB,GAAGpK,IAAI,SAAU9M,GAChG,OAAOA,EAAMnC,GACf,GACF,EAAG,CAACqZ,GAAkBE,KAGtB,IAAII,IAAmB,EAAAnC,EAAA,GAAenT,GAAa6R,IAAwD,QAAhCV,EAAc1B,GAAU,UAAgC,IAAhB0B,OAAyB,EAASA,EAAYxV,KAAM,CACnKC,MAAOoE,IAETuV,IAAmB,OAAeD,GAAkB,GACpDE,GAAkBD,GAAiB,GACnCE,GAAqBF,GAAiB,GACpCjP,GAAWpB,EAAgB,SAAUvJ,GACvC8Z,GAAmB9Z,EACrB,GACI4K,GAAarB,EAAgB,WAC/BuQ,QAAmBhiB,EACrB,IACA,IAAAiiB,qBAAoBjjB,EAAK,WACvB,MAAO,CACLsd,KAAM7P,GAAa3J,QACnBQ,MAAO,SAAe4e,GACpB,IAAIC,EAYEC,EAXFpa,EAAO0E,KACP2V,EAAmB1W,EAAgB3D,EAAMb,IAC3C8D,EAAWoX,EAAiBpX,SAC5Ba,EAAcuW,EAAiBvW,YAC/BE,EAAcqW,EAAiBrW,YAC7B8C,EAAoB/D,EAAqB0B,GAAa3J,QAASmI,GAC/DqX,EAAiBP,SAAyDA,GAAkBjT,EAAkB,GAAK9C,EAAYyB,IAAIqB,EAAkB,IAEjJ,QAFwJqT,EAAkBnG,GAAUuG,KAAK,SAAUvZ,GACzM,OAAQA,EAAKjK,MAAMiL,QACrB,UAAoC,IAApBmY,OAA6B,EAASA,EAAgBja,IAClEsa,EAAiB1W,EAAY2B,IAAI6U,GACjCA,GAAkBE,IAEpBA,SAA2G,QAAlDJ,EAAwBI,EAAelf,aAA6C,IAA1B8e,GAAoCA,EAAsB7e,KAAKif,EAAgBN,GAEtM,EAEJ,GAIA,IAAIO,IAAmB,EAAA/C,EAAA,GAAejB,IAAuB,GAAI,CAC7DtW,MAAOqN,GAEPuK,UAAW,SAAmB/X,GAC5B,OAAI+J,MAAM2Q,QAAQ1a,GACTA,EAELA,QACKwV,GAEF,CAACxV,EACV,IAEF2a,IAAmB,OAAeF,GAAkB,GACpDG,GAAmBD,GAAiB,GACpCE,GAAsBF,GAAiB,GA2CrCG,GAAkBrR,EAAgB,SAAUgF,GAC9CzB,UAA0CA,GAAQtB,GAAa+C,IAzC1C,SAA0BA,GAC/C,GAAI6H,EAAY,CAEd,IAEIyE,EAFAnU,EAAY6H,EAAKvO,IACjB8a,EAAQJ,GAAiBpZ,SAASoF,GAIlCmU,EAFAvE,GACEwE,EACcJ,GAAiBxY,OAAO,SAAUlC,GAChD,OAAOA,IAAQ0G,CACjB,GAEgB,GAAG7O,QAAO,OAAmB6iB,IAAmB,CAAChU,IAGnD,CAACA,GAEnBiU,GAAoBE,GAGpB,IAAIE,GAAa,QAAc,OAAc,CAAC,EAAGxM,GAAO,CAAC,EAAG,CAC1DjB,aAAcuN,IAEZC,EACFrE,UAAgDA,GAAWsE,GAE3DvE,UAA4CA,GAASuE,EAEzD,EAGKzE,IAAYwB,GAAe7W,QAA2B,WAAjB4X,IACxCb,GAAgB1C,GAEpB,CAQE0F,CAAiBzM,EACnB,GACI0M,GAAuB1R,EAAgB,SAAUvJ,EAAKsR,GACxD,IAAI4J,EAAcpD,GAAe5V,OAAO,SAAUiZ,GAChD,OAAOA,IAAMnb,CACf,GACA,GAAIsR,EACF4J,EAAY5R,KAAKtJ,QACZ,GAAqB,WAAjB6Y,GAA2B,CAEpC,IAAIuC,EAAchS,GAAepJ,GACjCkb,EAAcA,EAAYhZ,OAAO,SAAUiZ,GACzC,OAAQC,EAAYnY,IAAIkY,EAC1B,EACF,EACK,EAAA/a,EAAA,GAAQ0X,GAAgBoD,GAAa,IACxClD,GAAgBkD,GAAa,EAEjC,GAOIG,GAAoBpf,EAAiB4c,GAAcgB,GAAiBvV,GAAOrF,GAAMsF,GAAcC,GAASC,GAAYqV,GAJzF,SAAkC9Z,EAAKsR,GACpE,IAAIgK,EAAWhK,QAAmCA,GAAQwG,GAAexW,SAAStB,GAClFib,GAAqBjb,EAAKsb,EAC5B,EACsKvO,IAGtK,YAAgB,WACduK,IAAW,EACb,EAAG,IAGH,IAAIiE,GAAiB,UAAc,WACjC,MAAO,CACLhO,wBAAyBA,GACzBmF,2BAA4BA,GAEhC,EAAG,CAACnF,GAAyBmF,KAKzB8I,GAAoC,eAAjB3C,IAAiC/C,EAAmBhC,GAE3EA,GAAU7E,IAAI,SAAU9M,EAAO+M,GAC7B,OAGE,gBAAoB,EAAqB,CACvClP,IAAKmC,EAAMnC,IACXoN,iBAAkB8B,EAAQmK,IACzBlX,EAEP,GAGIW,GAAyB,gBAAoB,KAAU,OAAS,CAClEY,GAAIA,EACJ5M,IAAKyN,GACL/M,UAAW,GAAGK,OAAOL,EAAW,aAChC8W,UAAW,KACXmN,cAAe,GACf7jB,UAAW,IAAWJ,EAAW,GAAGK,OAAOL,EAAW,SAAU,GAAGK,OAAOL,EAAW,KAAKK,OAAOghB,IAAejhB,GAAW,QAAgB,OAAgB,CAAC,EAAG,GAAGC,OAAOL,EAAW,qBAAsByhB,IAA0B,GAAGphB,OAAOL,EAAW,QAAS8M,IAAQgM,GAC1QoL,IAAK/F,EACLjJ,MAAOA,EACPG,KAAM,OACNjL,SAAUA,EACV+Z,KAAMH,GACNI,cAAe,SAAuB9a,GACpC,OAAOA,CACT,EACA+a,cAAe,SAAuBC,GAEpC,IAAIC,EAAMD,EAAU7a,OAChB+a,EAAkBD,EAAMjI,GAAUzJ,OAAO0R,GAAO,KACpD,OAAoB,gBAAoB,GAAS,CAC/C7c,SAAUoI,EACV6E,MAAO0K,GACP/U,SAAUyX,GACVrH,mBAA4B,IAAR6J,EACpBrf,eAAgBoa,IACfkF,EACL,EACAC,SAA2B,eAAjBpD,IAAiC/C,EAAmB,IAASoG,WAAa,IAASC,WAC7FC,IAAK,OACL,kBAAkB,EAClBliB,gBAAiB,SAAyBmiB,GACxC/C,GAAoB+C,EACtB,EACAtP,UAAWsO,IACV9b,KAGH,OAAoB,gBAAoB,EAAec,SAAU,CAC/DJ,MAAOsb,IACO,gBAAoBxc,EAAUsB,SAAU,CACtDJ,MAAOhB,IACO,gBAAoB,EAAqB,CACvDzH,UAAWA,EACX8Y,cAAeA,EACflM,KAAMyU,GACNtG,SAAUuF,GACV5M,IAAK5G,GAGLxC,SAAUA,EAGV8N,OAAQyH,GAAUzH,GAAS,KAC3BC,eAAgBwH,GAAUxH,GAAiB,KAG3CxL,UAAWwV,GACXlP,SAAUA,GACVC,WAAYA,GAGZ0C,aAAcoN,GAGdvP,aAAcA,GAGd+E,iBAAkBA,EAClBC,kBAAmBA,EACnBE,mBAAoBA,EACpB5T,kBAAmBA,GACnB2T,qBAAsBA,GACtB5W,kBAAmBA,GAGnBoT,SAAUA,GACVuF,WAAYA,GAGZjF,YAAa0N,GACbnI,aAAcwI,IACA,gBAAoBra,EAAgBP,SAAU,CAC5DJ,MAAOyZ,IACN5W,IAAyB,gBAAoB,MAAO,CACrD4J,MAAO,CACL4P,QAAS,QAEX,eAAe,GACD,gBAAoB/b,EAAoBF,SAAU,CAChEJ,MAAOwZ,IACNrC,OACL,GCvdImF,GDwdJ,GCvdAA,GAAW/P,KAAO,GAClB+P,GAAWC,QAAU,GACrBD,GAAWE,UAAY,GACvBF,GAAWxI,QAAUA,GACrB,S,mJCVInb,EAAY,CAAC,YAAa,aAAc,OAAQ,aAAc,aAAc,qBAAsB,eAAgB,UAAW,YAAa,QAAS,WAAY,UAAW,QAAS,aAKnL8jB,OAAY5kB,EAChB,SAAS6kB,EAAa9lB,EAAOC,GAC3B,IAAIU,EAAYX,EAAMW,UACpBolB,EAAa/lB,EAAM+lB,WACnB3T,EAAOpS,EAAMoS,KACb4T,EAAahmB,EAAMgmB,WACnBC,EAAajmB,EAAMimB,WACnBC,EAAqBlmB,EAAMkmB,mBAC3BC,EAAenmB,EAAMmmB,aACrBC,EAAUpmB,EAAMomB,QAChBrlB,EAAYf,EAAMe,UAClB8U,EAAQ7V,EAAM6V,MACdzS,EAAWpD,EAAMoD,SACjBqiB,EAAUzlB,EAAMylB,QAChBY,EAAQrmB,EAAMqmB,MACdC,EAAmBtmB,EAAMyX,UACzB8O,OAAiC,IAArBD,EAA8B,MAAQA,EAClD5d,GAAY,OAAyB1I,EAAO+B,GAC1CykB,EAAeP,IAAeR,EAGlC,SAASgB,EAAqBC,GAC5BP,EAAaC,EAASM,EACxB,CACA,YAAgB,WACd,OAAO,WACLD,EAAqB,KACvB,CACF,EAAG,IAGH,IAGIE,EAHAC,EAAYZ,GAAc5T,IAASyT,EAAYG,EAAW5T,EAAM,CAClEiG,MAAOgO,IACJjjB,EAEA2iB,IACHY,EAAgB,CACd7f,QAAS0f,EAAe,EAAI,EAC5BK,OAAQL,EAAe,EAAIX,EAC3BiB,UAAWN,EAAe,SAAWX,EACrCQ,MAAOJ,EAAaI,EAAQR,EAC5BkB,cAAeP,EAAe,OAASX,EACvCmB,SAAUR,EAAe,WAAaX,IAG1C,IAAIoB,EAAgB,CAAC,EACjBT,IACFS,EAAc,gBAAiB,GAEjC,IAAIC,EAAwB,gBAAoBX,GAAW,OAAS,CAClExlB,UAAW,KAAYglB,GAAcplB,EAAWI,GAChD8U,OAAO,QAAc,OAAc,CAAC,EAAG8Q,GAAgB9Q,IACtDoR,EAAeve,EAAW,CAC3BzI,IAAKA,IACH2mB,GAUJ,OATIX,IACFiB,EAAwB,gBAAoB,IAAgB,CAC1DC,SAAU,SAAkBjjB,GAE1BuiB,EADkBviB,EAAKkjB,YAEzB,EACAnc,SAAUib,GACTgB,IAEEA,CACT,CACA,IAAIvR,EAAoB,aAAiBmQ,GACzCnQ,EAAKsC,YAAc,OACnB,Q,6BCpEO,SAASoP,IAEd,IAAIC,EAAgB,SAAa,MAiBjC,OAdyB,SAA4B9V,GAC9C8V,EAAcvjB,UACjBujB,EAAcvjB,QAAU,GCdf,SAAuByN,GACpC,GAA8B,oBAAnB+V,gBACT,EAAAriB,EAAA,GAAIsM,OACC,CACL,IAAIgW,EAAU,IAAID,eAClBC,EAAQC,MAAMC,UAAY,WACxB,OAAOlW,GACT,EACAgW,EAAQG,MAAMC,iBAAY3mB,EAC5B,CACF,CDKM4mB,CAAc,YACZ,IAAAC,yBAAwB,WACtBR,EAAcvjB,QAAQmF,QAAQ,SAAU6e,GACtCA,GACF,GACAT,EAAcvjB,QAAU,IAC1B,EACF,IAEFujB,EAAcvjB,QAAQ0O,KAAKjB,EAC7B,CAEF,CAKe,SAASwW,EAAeC,EAAoBC,GAEzD,IAAI3kB,EAAkB,WAAe2kB,GACnC1kB,GAAmB,OAAeD,EAAiB,GACnD4kB,EAAa3kB,EAAiB,GAC9B4kB,EAAgB5kB,EAAiB,GAQnC,MAAO,CAAC2kB,GALW,EAAAE,EAAA,GAAS,SAAUC,GACpCL,EAAmB,WACjBG,EAAcE,EAChB,EACF,GAEF,CE9CO,IAAIC,EAA+B,gBAAoB,MCC1D,EAAY,CAAC,aACfxT,EAAa,CAAC,aACdC,EAAa,CAAC,aAKZwT,EAAkB,SAAyBxoB,EAAOC,GACpD,IAAI0I,EAAU,aAAiB4f,GAG/B,IAAK5f,EAAS,CACZ,IAAI2d,EAAmBtmB,EAAMyX,UAC3B8O,OAAiC,IAArBD,EAA8B,MAAQA,EAClDmC,GAAa,OAAyBzoB,EAAO,GAC/C,OAAoB,gBAAoBumB,GAAW,OAAS,CAAC,EAAGkC,EAAY,CAC1ExoB,IAAKA,IAET,CACA,IAAIyoB,EAAmB/f,EAAQ5H,UAC7B4nB,GAAc,OAAyBhgB,EAASoM,GAC9ChU,EAAYf,EAAMe,UACpB2H,GAAY,OAAyB1I,EAAOgV,GAG9C,OAAoB,gBAAoBuT,EAAgB/e,SAAU,CAChEJ,MAAO,MACO,gBAAoB,GAAM,OAAS,CACjDnJ,IAAKA,EACLc,UAAW,IAAW2nB,EAAkB3nB,IACvC4nB,EAAajgB,IAClB,EACIkgB,EAAuB,aAAiBJ,GAC5CI,EAAQ3Q,YAAc,UACtB,QChCI,EAAY,CAAC,YAAa,OAAQ,aAAc,gBAAiB,UAAW,YAAa,MAAO,QAAS,YAAa,WAAY,aAAc,gBAAiB,SAAU,YAAa,gBAAiB,mBAUzMqN,EAAa,aACbD,EAAa,aAEjB,SAASwD,EAAkBC,GACzB,MAAO,KAAK9nB,OAAO8nB,EAAa1e,OAAQ,OAC1C,CACA,SAAS2e,EAAS/oB,EAAOC,GACvB,IAAIkC,EAAmBnC,EAAMW,UAC3BA,OAAiC,IAArBwB,EAA8B,cAAgBA,EAC1D6mB,EAAchpB,EAAM8kB,KACpBA,OAAuB,IAAhBkE,EAAyB,GAAKA,EACrChD,EAAahmB,EAAMgmB,WACnBjB,EAAgB/kB,EAAM+kB,cACtBqB,EAAUpmB,EAAMomB,QAChB6C,EAAmBjpB,EAAMkpB,UACzBA,OAAiC,IAArBD,EAA8B,GAAKA,EAC/C1D,EAAMvlB,EAAMulB,IACZ1P,EAAQ7V,EAAM6V,MACd9U,EAAYf,EAAMe,UAClBqkB,EAAWplB,EAAMolB,SACjB+D,EAAanpB,EAAMmpB,WACnBnE,EAAgBhlB,EAAMglB,cACtBoE,EAASppB,EAAMopB,OACf9C,EAAmBtmB,EAAMyX,UACzB8O,OAAiC,IAArBD,EAA8B,MAAQA,EAClD1B,EAAgB5kB,EAAM4kB,cACtBvhB,EAAkBrD,EAAMqD,gBACxBqF,GAAY,OAAyB1I,EAAO,GAC1CqpB,EAAmB,SAAR9D,EACX0C,EAAqBZ,IACrBiC,EAAkBtB,EAAeC,EAAoB,MACvDsB,GAAmB,OAAeD,EAAiB,GACnDE,EAAiBD,EAAiB,GAClCE,EAAoBF,EAAiB,GACnCG,EAAuBF,GAAkB,EACzCG,EAAmB3B,EAAeC,EAAoB,IAAIjb,KAC5D4c,GAAmB,OAAeD,EAAkB,GACpDE,EAAaD,EAAiB,GAC9BE,EAAgBF,EAAiB,GAC/BG,EAAmB/B,EAAeC,EAAoB,GACxD+B,GAAmB,OAAeD,EAAkB,GACpDE,EAAgBD,EAAiB,GACjCE,EAAmBF,EAAiB,GAClCG,GAAmBnC,EAAeC,EAAoB,GACxDmC,IAAmB,OAAeD,GAAkB,GACpDE,GAAYD,GAAiB,GAC7BE,GAAeF,GAAiB,GAC9BG,GAAmBvC,EAAeC,EAAoB,GACxDuC,IAAoB,OAAeD,GAAkB,GACrDE,GAAcD,GAAkB,GAChCE,GAAiBF,GAAkB,GACjCG,IAAY,IAAAC,UAAS,MACvBC,IAAa,OAAeF,GAAW,GACvCG,GAAmBD,GAAW,GAC9BE,GAAsBF,GAAW,GAC/BG,IAAa,IAAAJ,UAAS,MACxBK,IAAa,OAAeD,GAAY,GACxCE,GAAeD,GAAW,GAC1BE,GAAkBF,GAAW,GAC3BG,GAAqB,UAAc,WACrC,OAAqB,OAAjBF,IAAyB7B,EACpBve,OAAOugB,iBAETH,IAAgB,CACzB,EAAG,CAACA,GAAc1B,IACd8B,IAAa,IAAAV,WAAS,GACxBW,IAAa,OAAeD,GAAY,GACxCE,GAAYD,GAAW,GACvBE,GAAeF,GAAW,GACxBG,GAAgB,GAAG1qB,OAAOL,EAAW,SAGrCgrB,GAAkBvY,KAAKwY,IAAI3B,EAAeI,IAG1CwB,GAAezG,IAAaE,EAC5BwG,GAAmBhH,EAAK1a,QAAUyhB,GAClC9F,GAAaX,IAAaC,EAK1B0G,GAAWD,IAAwC,iBAAb1G,GAAyBN,EAAK1a,OAASgb,EAC7E4G,IAAa,IAAAnrB,SAAQ,WACvB,IAAIyd,EAAQwG,EAUZ,OATIgH,GAEAxN,EADqB,OAAnBkL,GAA2BH,EACrBvE,EAEAA,EAAKtR,MAAM,EAAGJ,KAAK6Y,IAAInH,EAAK1a,OAAQsf,EAAuBR,IAExC,iBAAb9D,IAChB9G,EAAQwG,EAAKtR,MAAM,EAAG4R,IAEjB9G,CACT,EAAG,CAACwG,EAAMoE,EAAWM,EAAgBpE,EAAU0G,KAC3ChD,IAAe,IAAAjoB,SAAQ,WACzB,OAAIirB,GACKhH,EAAKtR,MAAM4X,GAAqB,GAElCtG,EAAKtR,MAAMwY,GAAW5hB,OAC/B,EAAG,CAAC0a,EAAMkH,GAAYF,GAAkBV,KAGpCc,IAAS,IAAA5a,aAAY,SAAUc,EAAMiG,GACvC,IAAInU,EACJ,MAAuB,mBAAZkiB,EACFA,EAAQhU,GAE0E,QAAnFlO,EAAOkiB,IAAYhU,aAAmC,EAASA,EAAKgU,WAAgC,IAATliB,EAAkBA,EAAOmU,CAC9H,EAAG,CAAC+N,IACA+F,IAAmB,IAAA7a,aAAY0U,GAAc,SAAU5T,GACzD,OAAOA,CACT,EAAG,CAAC4T,IACJ,SAASoG,GAAmB3f,EAAO4f,EAAqBC,IAIlDpB,KAAiBze,QAAkCxL,IAAxBorB,GAAqCA,IAAwBvB,MAG5FK,GAAgB1e,GACX6f,IACHb,GAAahf,EAAQqY,EAAK1a,OAAS,GACnC/G,SAA0DA,EAAgBoJ,SAEhDxL,IAAxBorB,GACFtB,GAAoBsB,GAExB,CAMA,SAASlG,GAAahd,EAAKud,GACzBoD,EAAc,SAAUjhB,GACtB,IAAIE,EAAQ,IAAIiE,IAAInE,GAMpB,OALc,OAAV6d,EACF3d,EAAM8I,OAAO1I,GAEbJ,EAAMuE,IAAInE,EAAKud,GAEV3d,CACT,EACF,CAUA,SAASwjB,GAAalU,GACpB,OAAOwR,EAAWnb,IAAIwd,GAAOF,GAAW3T,GAAQA,GAClD,EACA,EAAAmU,EAAA,GAAgB,WACd,GAAI9C,GAAmD,iBAApBiC,IAAgCK,GAAY,CAC7E,IAAIS,EAAahC,GACbvF,EAAM8G,GAAW5hB,OACjBsiB,EAAYxH,EAAM,EAGtB,IAAKA,EAEH,YADAkH,GAAmB,EAAG,MAGxB,IAAK,IAAIO,EAAI,EAAGA,EAAIzH,EAAKyH,GAAK,EAAG,CAC/B,IAAIC,EAAmBL,GAAaI,GAQpC,GALItD,IACFuD,EAAmBA,GAAoB,QAIhB3rB,IAArB2rB,EAAgC,CAClCR,GAAmBO,EAAI,OAAG1rB,GAAW,GACrC,KACF,CAIA,GADAwrB,GAAcG,EAGA,IAAdF,GAAmBD,GAAc/C,GAEjCiD,IAAMD,EAAY,GAAKD,EAAaF,GAAaG,IAAchD,EAAsB,CAEnF0C,GAAmBM,EAAW,MAC9B,KACF,CAAO,GAAID,EAAad,GAAkBjC,EAAsB,CAE9D0C,GAAmBO,EAAI,EAAGF,EAAaG,EAAmBnC,GAAcJ,IACxE,KACF,CACF,CACIjB,GAAUmD,GAAa,GAAK9B,GAAcf,GAC5CqB,GAAoB,KAExB,CACF,EAAG,CAACrB,EAAsBG,EAAYQ,GAAWI,GAAayB,GAAQF,KAGtE,IAAIa,GAAcrB,MAAe1C,GAAa1e,OAC1C0iB,GAAc,CAAC,EACM,OAArBhC,IAA6BgB,KAC/BgB,GAAc,CACZ9F,SAAU,WACV+F,KAAMjC,GACNppB,IAAK,IAGT,IAAIsrB,GAAkB,CACpBrsB,UAAW+qB,GACXzF,WAAY6F,GACZrU,UAAWmN,EACXmB,WAAYA,IAIVkH,GAAyBlI,EAAgB,SAAU3S,EAAMiG,GAC3D,IAAIlP,EAAM+iB,GAAO9Z,EAAMiG,GACvB,OAAoB,gBAAoBkQ,EAAgB/e,SAAU,CAChEL,IAAKA,EACLC,OAAO,QAAc,OAAc,CAAC,EAAG4jB,IAAkB,CAAC,EAAG,CAC3D3G,MAAOhO,EACPjG,KAAMA,EACNgU,QAASjd,EACTgd,aAAcA,GACdV,QAASpN,GAAS+S,MAEnBrG,EAAc3S,EAAMiG,GACzB,EAAI,SAAUjG,EAAMiG,GAClB,IAAIlP,EAAM+iB,GAAO9Z,EAAMiG,GACvB,OAAoB,gBAAoB,GAAM,OAAS,CAAC,EAAG2U,GAAiB,CAC1E3G,MAAOhO,EACPlP,IAAKA,EACLiJ,KAAMA,EACN4T,WAAYmG,GACZ/F,QAASjd,EACTgd,aAAcA,GACdV,QAASpN,GAAS+S,KAEtB,EAGI8B,GAAmB,CACrB7G,MAAOwG,GAAczB,GAAqBtgB,OAAOugB,iBACjDtqB,UAAW,GAAGC,OAAO0qB,GAAe,SACpCvF,aA1GF,SAA8BgH,EAAGzG,GAC/B4D,GAAa5D,GACbwD,EAAiBG,GACnB,EAwGE5E,QAASoH,IAEPO,GAAmBjE,GAAcN,EACjCwE,GAAWrI,EAA6B,gBAAoBuD,EAAgB/e,SAAU,CACxFJ,OAAO,QAAc,OAAc,CAAC,EAAG4jB,IAAkBE,KACxDlI,EAAc8D,KAA8B,gBAAoB,GAAM,OAAS,CAAC,EAAGkE,GAAiBE,IAA+C,mBAArBE,GAAkCA,GAAiBtE,IAAgBsE,IAChME,GAA4B,gBAAoB/G,GAAW,OAAS,CACtExlB,UAAW,KAAYglB,IAAcplB,EAAWI,GAChD8U,MAAOA,EACP5V,IAAKA,GACJyI,GAAYsjB,GAAW5T,IAAI6U,IAAyBlB,GAAWsB,GAAW,KAAMjE,GAAuB,gBAAoB,GAAM,OAAS,CAAC,EAAG4D,GAAiB,CAChK/G,WAAY4F,GACZ3F,oBAAqB4F,GACrBzF,MAAO+E,GACPrqB,UAAW,GAAGC,OAAO0qB,GAAe,WACpCvF,aAtHF,SAA4BgH,EAAGzG,GAC7BgE,GAAehE,EACjB,EAqHEjB,SAAS,EACT5P,MAAOiX,KACL1D,IACJ,OAAOyC,GAA4B,gBAAoB,IAAgB,CACrE1E,SA7IF,SAA0BgG,EAAGjgB,GAC3Buc,EAAkBvc,EAAQqgB,YAC5B,EA4IEtiB,UAAW6gB,IACVwB,IAAgBA,EACrB,CACA,IAAIE,EAA+B,aAAiBzE,GACpDyE,EAAgBvV,YAAc,WAC9BuV,EAAgB7X,KAAO,EACvB6X,EAAgBlI,WAAaA,EAC7BkI,EAAgBnI,WAAaA,EAG7B,ICzSA,EDySA,C", "sources": ["webpack://autogentstudio/./node_modules/@ant-design/icons-svg/es/asn/EllipsisOutlined.js", "webpack://autogentstudio/./node_modules/@ant-design/icons/es/icons/EllipsisOutlined.js", "webpack://autogentstudio/./node_modules/rc-dropdown/es/hooks/useAccessibility.js", "webpack://autogentstudio/./node_modules/rc-dropdown/es/Overlay.js", "webpack://autogentstudio/./node_modules/rc-dropdown/es/placements.js", "webpack://autogentstudio/./node_modules/rc-dropdown/es/Dropdown.js", "webpack://autogentstudio/./node_modules/rc-dropdown/es/index.js", "webpack://autogentstudio/./node_modules/antd/es/style/motion/slide.js", "webpack://autogentstudio/./node_modules/rc-menu/es/context/IdContext.js", "webpack://autogentstudio/./node_modules/rc-menu/es/context/MenuContext.js", "webpack://autogentstudio/./node_modules/rc-menu/es/context/PathContext.js", "webpack://autogentstudio/./node_modules/rc-menu/es/context/PrivateContext.js", "webpack://autogentstudio/./node_modules/rc-util/es/Dom/focus.js", "webpack://autogentstudio/./node_modules/rc-menu/es/hooks/useAccessibility.js", "webpack://autogentstudio/./node_modules/rc-menu/es/hooks/useKeyRecords.js", "webpack://autogentstudio/./node_modules/rc-menu/es/utils/timeUtil.js", "webpack://autogentstudio/./node_modules/rc-menu/es/hooks/useMemoCallback.js", "webpack://autogentstudio/./node_modules/rc-menu/es/hooks/useUUID.js", "webpack://autogentstudio/./node_modules/rc-menu/es/hooks/useActive.js", "webpack://autogentstudio/./node_modules/rc-menu/es/hooks/useDirectionStyle.js", "webpack://autogentstudio/./node_modules/rc-menu/es/Icon.js", "webpack://autogentstudio/./node_modules/rc-menu/es/utils/warnUtil.js", "webpack://autogentstudio/./node_modules/rc-menu/es/MenuItem.js", "webpack://autogentstudio/./node_modules/rc-menu/es/SubMenu/SubMenuList.js", "webpack://autogentstudio/./node_modules/rc-menu/es/utils/commonUtil.js", "webpack://autogentstudio/./node_modules/rc-menu/es/placements.js", "webpack://autogentstudio/./node_modules/rc-menu/es/utils/motionUtil.js", "webpack://autogentstudio/./node_modules/rc-menu/es/SubMenu/PopupTrigger.js", "webpack://autogentstudio/./node_modules/rc-menu/es/SubMenu/InlineSubMenuList.js", "webpack://autogentstudio/./node_modules/rc-menu/es/SubMenu/index.js", "webpack://autogentstudio/./node_modules/rc-menu/es/Divider.js", "webpack://autogentstudio/./node_modules/rc-menu/es/MenuItemGroup.js", "webpack://autogentstudio/./node_modules/rc-menu/es/utils/nodeUtil.js", "webpack://autogentstudio/./node_modules/rc-menu/es/Menu.js", "webpack://autogentstudio/./node_modules/rc-menu/es/index.js", "webpack://autogentstudio/./node_modules/rc-overflow/es/Item.js", "webpack://autogentstudio/./node_modules/rc-overflow/es/hooks/useEffectState.js", "webpack://autogentstudio/./node_modules/rc-overflow/es/hooks/channelUpdate.js", "webpack://autogentstudio/./node_modules/rc-overflow/es/context.js", "webpack://autogentstudio/./node_modules/rc-overflow/es/RawItem.js", "webpack://autogentstudio/./node_modules/rc-overflow/es/Overflow.js", "webpack://autogentstudio/./node_modules/rc-overflow/es/index.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar EllipsisOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z\" } }] }, \"name\": \"ellipsis\", \"theme\": \"outlined\" };\nexport default EllipsisOutlined;\n", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport EllipsisOutlinedSvg from \"@ant-design/icons-svg/es/asn/EllipsisOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar EllipsisOutlined = function EllipsisOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: EllipsisOutlinedSvg\n  }));\n};\n\n/**![ellipsis](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE3NiA1MTFhNTYgNTYgMCAxMDExMiAwIDU2IDU2IDAgMTAtMTEyIDB6bTI4MCAwYTU2IDU2IDAgMTAxMTIgMCA1NiA1NiAwIDEwLTExMiAwem0yODAgMGE1NiA1NiAwIDEwMTEyIDAgNTYgNTYgMCAxMC0xMTIgMHoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(EllipsisOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'EllipsisOutlined';\n}\nexport default RefIcon;", "import KeyCode from \"rc-util/es/KeyCode\";\nimport raf from \"rc-util/es/raf\";\nimport * as React from \"react\";\nvar ESC = KeyCode.ESC,\n  TAB = KeyCode.TAB;\nexport default function useAccessibility(_ref) {\n  var visible = _ref.visible,\n    triggerRef = _ref.triggerRef,\n    onVisibleChange = _ref.onVisibleChange,\n    autoFocus = _ref.autoFocus,\n    overlayRef = _ref.overlayRef;\n  var focusMenuRef = React.useRef(false);\n  var handleCloseMenuAndReturnFocus = function handleCloseMenuAndReturnFocus() {\n    if (visible) {\n      var _triggerRef$current, _triggerRef$current$f;\n      (_triggerRef$current = triggerRef.current) === null || _triggerRef$current === void 0 || (_triggerRef$current$f = _triggerRef$current.focus) === null || _triggerRef$current$f === void 0 || _triggerRef$current$f.call(_triggerRef$current);\n      onVisibleChange === null || onVisibleChange === void 0 || onVisibleChange(false);\n    }\n  };\n  var focusMenu = function focusMenu() {\n    var _overlayRef$current;\n    if ((_overlayRef$current = overlayRef.current) !== null && _overlayRef$current !== void 0 && _overlayRef$current.focus) {\n      overlayRef.current.focus();\n      focusMenuRef.current = true;\n      return true;\n    }\n    return false;\n  };\n  var handleKeyDown = function handleKeyDown(event) {\n    switch (event.keyCode) {\n      case ESC:\n        handleCloseMenuAndReturnFocus();\n        break;\n      case TAB:\n        {\n          var focusResult = false;\n          if (!focusMenuRef.current) {\n            focusResult = focusMenu();\n          }\n          if (focusResult) {\n            event.preventDefault();\n          } else {\n            handleCloseMenuAndReturnFocus();\n          }\n          break;\n        }\n    }\n  };\n  React.useEffect(function () {\n    if (visible) {\n      window.addEventListener(\"keydown\", handleKeyDown);\n      if (autoFocus) {\n        // FIXME: hack with raf\n        raf(focusMenu, 3);\n      }\n      return function () {\n        window.removeEventListener(\"keydown\", handleKeyDown);\n        focusMenuRef.current = false;\n      };\n    }\n    return function () {\n      focusMenuRef.current = false;\n    };\n  }, [visible]); // eslint-disable-line react-hooks/exhaustive-deps\n}", "import { composeRef, getNodeRef, supportRef } from \"rc-util/es/ref\";\nimport React, { forwardRef, useMemo } from 'react';\nvar Overlay = /*#__PURE__*/forwardRef(function (props, ref) {\n  var overlay = props.overlay,\n    arrow = props.arrow,\n    prefixCls = props.prefixCls;\n  var overlayNode = useMemo(function () {\n    var overlayElement;\n    if (typeof overlay === 'function') {\n      overlayElement = overlay();\n    } else {\n      overlayElement = overlay;\n    }\n    return overlayElement;\n  }, [overlay]);\n  var composedRef = composeRef(ref, getNodeRef(overlayNode));\n  return /*#__PURE__*/React.createElement(React.Fragment, null, arrow && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-arrow\")\n  }), /*#__PURE__*/React.cloneElement(overlayNode, {\n    ref: supportRef(overlayNode) ? composedRef : undefined\n  }));\n});\nexport default Overlay;", "var autoAdjustOverflow = {\n  adjustX: 1,\n  adjustY: 1\n};\nvar targetOffset = [0, 0];\nvar placements = {\n  topLeft: {\n    points: ['bl', 'tl'],\n    overflow: autoAdjustOverflow,\n    offset: [0, -4],\n    targetOffset: targetOffset\n  },\n  top: {\n    points: ['bc', 'tc'],\n    overflow: autoAdjustOverflow,\n    offset: [0, -4],\n    targetOffset: targetOffset\n  },\n  topRight: {\n    points: ['br', 'tr'],\n    overflow: autoAdjustOverflow,\n    offset: [0, -4],\n    targetOffset: targetOffset\n  },\n  bottomLeft: {\n    points: ['tl', 'bl'],\n    overflow: autoAdjustOverflow,\n    offset: [0, 4],\n    targetOffset: targetOffset\n  },\n  bottom: {\n    points: ['tc', 'bc'],\n    overflow: autoAdjustOverflow,\n    offset: [0, 4],\n    targetOffset: targetOffset\n  },\n  bottomRight: {\n    points: ['tr', 'br'],\n    overflow: autoAdjustOverflow,\n    offset: [0, 4],\n    targetOffset: targetOffset\n  }\n};\nexport default placements;", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"arrow\", \"prefixCls\", \"transitionName\", \"animation\", \"align\", \"placement\", \"placements\", \"getPopupContainer\", \"showAction\", \"hideAction\", \"overlayClassName\", \"overlayStyle\", \"visible\", \"trigger\", \"autoFocus\", \"overlay\", \"children\", \"onVisibleChange\"];\nimport Trigger from '@rc-component/trigger';\nimport classNames from 'classnames';\nimport { composeRef, getNodeRef, supportRef } from \"rc-util/es/ref\";\nimport React from 'react';\nimport useAccessibility from \"./hooks/useAccessibility\";\nimport Overlay from \"./Overlay\";\nimport Placements from \"./placements\";\nfunction Dropdown(props, ref) {\n  var _children$props;\n  var _props$arrow = props.arrow,\n    arrow = _props$arrow === void 0 ? false : _props$arrow,\n    _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-dropdown' : _props$prefixCls,\n    transitionName = props.transitionName,\n    animation = props.animation,\n    align = props.align,\n    _props$placement = props.placement,\n    placement = _props$placement === void 0 ? 'bottomLeft' : _props$placement,\n    _props$placements = props.placements,\n    placements = _props$placements === void 0 ? Placements : _props$placements,\n    getPopupContainer = props.getPopupContainer,\n    showAction = props.showAction,\n    hideAction = props.hideAction,\n    overlayClassName = props.overlayClassName,\n    overlayStyle = props.overlayStyle,\n    visible = props.visible,\n    _props$trigger = props.trigger,\n    trigger = _props$trigger === void 0 ? ['hover'] : _props$trigger,\n    autoFocus = props.autoFocus,\n    overlay = props.overlay,\n    children = props.children,\n    onVisibleChange = props.onVisibleChange,\n    otherProps = _objectWithoutProperties(props, _excluded);\n  var _React$useState = React.useState(),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    triggerVisible = _React$useState2[0],\n    setTriggerVisible = _React$useState2[1];\n  var mergedVisible = 'visible' in props ? visible : triggerVisible;\n  var triggerRef = React.useRef(null);\n  var overlayRef = React.useRef(null);\n  var childRef = React.useRef(null);\n  React.useImperativeHandle(ref, function () {\n    return triggerRef.current;\n  });\n  var handleVisibleChange = function handleVisibleChange(newVisible) {\n    setTriggerVisible(newVisible);\n    onVisibleChange === null || onVisibleChange === void 0 || onVisibleChange(newVisible);\n  };\n  useAccessibility({\n    visible: mergedVisible,\n    triggerRef: childRef,\n    onVisibleChange: handleVisibleChange,\n    autoFocus: autoFocus,\n    overlayRef: overlayRef\n  });\n  var onClick = function onClick(e) {\n    var onOverlayClick = props.onOverlayClick;\n    setTriggerVisible(false);\n    if (onOverlayClick) {\n      onOverlayClick(e);\n    }\n  };\n  var getMenuElement = function getMenuElement() {\n    return /*#__PURE__*/React.createElement(Overlay, {\n      ref: overlayRef,\n      overlay: overlay,\n      prefixCls: prefixCls,\n      arrow: arrow\n    });\n  };\n  var getMenuElementOrLambda = function getMenuElementOrLambda() {\n    if (typeof overlay === 'function') {\n      return getMenuElement;\n    }\n    return getMenuElement();\n  };\n  var getMinOverlayWidthMatchTrigger = function getMinOverlayWidthMatchTrigger() {\n    var minOverlayWidthMatchTrigger = props.minOverlayWidthMatchTrigger,\n      alignPoint = props.alignPoint;\n    if ('minOverlayWidthMatchTrigger' in props) {\n      return minOverlayWidthMatchTrigger;\n    }\n    return !alignPoint;\n  };\n  var getOpenClassName = function getOpenClassName() {\n    var openClassName = props.openClassName;\n    if (openClassName !== undefined) {\n      return openClassName;\n    }\n    return \"\".concat(prefixCls, \"-open\");\n  };\n  var childrenNode = /*#__PURE__*/React.cloneElement(children, {\n    className: classNames((_children$props = children.props) === null || _children$props === void 0 ? void 0 : _children$props.className, mergedVisible && getOpenClassName()),\n    ref: supportRef(children) ? composeRef(childRef, getNodeRef(children)) : undefined\n  });\n  var triggerHideAction = hideAction;\n  if (!triggerHideAction && trigger.indexOf('contextMenu') !== -1) {\n    triggerHideAction = ['click'];\n  }\n  return /*#__PURE__*/React.createElement(Trigger, _extends({\n    builtinPlacements: placements\n  }, otherProps, {\n    prefixCls: prefixCls,\n    ref: triggerRef,\n    popupClassName: classNames(overlayClassName, _defineProperty({}, \"\".concat(prefixCls, \"-show-arrow\"), arrow)),\n    popupStyle: overlayStyle,\n    action: trigger,\n    showAction: showAction,\n    hideAction: triggerHideAction,\n    popupPlacement: placement,\n    popupAlign: align,\n    popupTransitionName: transitionName,\n    popupAnimation: animation,\n    popupVisible: mergedVisible,\n    stretch: getMinOverlayWidthMatchTrigger() ? 'minWidth' : '',\n    popup: getMenuElementOrLambda(),\n    onPopupVisibleChange: handleVisibleChange,\n    onPopupClick: onClick,\n    getPopupContainer: getPopupContainer\n  }), childrenNode);\n}\nexport default /*#__PURE__*/React.forwardRef(Dropdown);", "import Dropdown from \"./Dropdown\";\nexport default Dropdown;", "import { Keyframes } from '@ant-design/cssinjs';\nimport { initMotion } from './motion';\nexport const slideUpIn = new Keyframes('antSlideUpIn', {\n  '0%': {\n    transform: 'scaleY(0.8)',\n    transformOrigin: '0% 0%',\n    opacity: 0\n  },\n  '100%': {\n    transform: 'scaleY(1)',\n    transformOrigin: '0% 0%',\n    opacity: 1\n  }\n});\nexport const slideUpOut = new Keyframes('antSlideUpOut', {\n  '0%': {\n    transform: 'scaleY(1)',\n    transformOrigin: '0% 0%',\n    opacity: 1\n  },\n  '100%': {\n    transform: 'scaleY(0.8)',\n    transformOrigin: '0% 0%',\n    opacity: 0\n  }\n});\nexport const slideDownIn = new Keyframes('antSlideDownIn', {\n  '0%': {\n    transform: 'scaleY(0.8)',\n    transformOrigin: '100% 100%',\n    opacity: 0\n  },\n  '100%': {\n    transform: 'scaleY(1)',\n    transformOrigin: '100% 100%',\n    opacity: 1\n  }\n});\nexport const slideDownOut = new Keyframes('antSlideDownOut', {\n  '0%': {\n    transform: 'scaleY(1)',\n    transformOrigin: '100% 100%',\n    opacity: 1\n  },\n  '100%': {\n    transform: 'scaleY(0.8)',\n    transformOrigin: '100% 100%',\n    opacity: 0\n  }\n});\nexport const slideLeftIn = new Keyframes('antSlideLeftIn', {\n  '0%': {\n    transform: 'scaleX(0.8)',\n    transformOrigin: '0% 0%',\n    opacity: 0\n  },\n  '100%': {\n    transform: 'scaleX(1)',\n    transformOrigin: '0% 0%',\n    opacity: 1\n  }\n});\nexport const slideLeftOut = new Keyframes('antSlideLeftOut', {\n  '0%': {\n    transform: 'scaleX(1)',\n    transformOrigin: '0% 0%',\n    opacity: 1\n  },\n  '100%': {\n    transform: 'scaleX(0.8)',\n    transformOrigin: '0% 0%',\n    opacity: 0\n  }\n});\nexport const slideRightIn = new Keyframes('antSlideRightIn', {\n  '0%': {\n    transform: 'scaleX(0.8)',\n    transformOrigin: '100% 0%',\n    opacity: 0\n  },\n  '100%': {\n    transform: 'scaleX(1)',\n    transformOrigin: '100% 0%',\n    opacity: 1\n  }\n});\nexport const slideRightOut = new Keyframes('antSlideRightOut', {\n  '0%': {\n    transform: 'scaleX(1)',\n    transformOrigin: '100% 0%',\n    opacity: 1\n  },\n  '100%': {\n    transform: 'scaleX(0.8)',\n    transformOrigin: '100% 0%',\n    opacity: 0\n  }\n});\nconst slideMotion = {\n  'slide-up': {\n    inKeyframes: slideUpIn,\n    outKeyframes: slideUpOut\n  },\n  'slide-down': {\n    inKeyframes: slideDownIn,\n    outKeyframes: slideDownOut\n  },\n  'slide-left': {\n    inKeyframes: slideLeftIn,\n    outKeyframes: slideLeftOut\n  },\n  'slide-right': {\n    inKeyframes: slideRightIn,\n    outKeyframes: slideRightOut\n  }\n};\nexport const initSlideMotion = (token, motionName) => {\n  const {\n    antCls\n  } = token;\n  const motionCls = `${antCls}-${motionName}`;\n  const {\n    inKeyframes,\n    outKeyframes\n  } = slideMotion[motionName];\n  return [initMotion(motionCls, inKeyframes, outKeyframes, token.motionDurationMid), {\n    [`\n      ${motionCls}-enter,\n      ${motionCls}-appear\n    `]: {\n      transform: 'scale(0)',\n      transformOrigin: '0% 0%',\n      opacity: 0,\n      animationTimingFunction: token.motionEaseOutQuint,\n      '&-prepare': {\n        transform: 'scale(1)'\n      }\n    },\n    [`${motionCls}-leave`]: {\n      animationTimingFunction: token.motionEaseInQuint\n    }\n  }];\n};", "import * as React from 'react';\nexport var IdContext = /*#__PURE__*/React.createContext(null);\nexport function getMenuId(uuid, eventKey) {\n  if (uuid === undefined) {\n    return null;\n  }\n  return \"\".concat(uuid, \"-\").concat(eventKey);\n}\n\n/**\n * Get `data-menu-id`\n */\nexport function useMenuId(eventKey) {\n  var id = React.useContext(IdContext);\n  return getMenuId(id, eventKey);\n}", "import _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nvar _excluded = [\"children\", \"locked\"];\nimport * as React from 'react';\nimport useMemo from \"rc-util/es/hooks/useMemo\";\nimport isEqual from \"rc-util/es/isEqual\";\nexport var MenuContext = /*#__PURE__*/React.createContext(null);\nfunction mergeProps(origin, target) {\n  var clone = _objectSpread({}, origin);\n  Object.keys(target).forEach(function (key) {\n    var value = target[key];\n    if (value !== undefined) {\n      clone[key] = value;\n    }\n  });\n  return clone;\n}\nexport default function InheritableContextProvider(_ref) {\n  var children = _ref.children,\n    locked = _ref.locked,\n    restProps = _objectWithoutProperties(_ref, _excluded);\n  var context = React.useContext(MenuContext);\n  var inheritableContext = useMemo(function () {\n    return mergeProps(context, restProps);\n  }, [context, restProps], function (prev, next) {\n    return !locked && (prev[0] !== next[0] || !isEqual(prev[1], next[1], true));\n  });\n  return /*#__PURE__*/React.createElement(MenuContext.Provider, {\n    value: inheritableContext\n  }, children);\n}", "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nvar EmptyList = [];\n\n// ========================= Path Register =========================\n\nexport var PathRegisterContext = /*#__PURE__*/React.createContext(null);\nexport function useMeasure() {\n  return React.useContext(PathRegisterContext);\n}\n\n// ========================= Path Tracker ==========================\nexport var PathTrackerContext = /*#__PURE__*/React.createContext(EmptyList);\nexport function useFullPath(eventKey) {\n  var parentKeyPath = React.useContext(PathTrackerContext);\n  return React.useMemo(function () {\n    return eventKey !== undefined ? [].concat(_toConsumableArray(parentKeyPath), [eventKey]) : parentKeyPath;\n  }, [parentKeyPath, eventKey]);\n}\n\n// =========================== Path User ===========================\n\nexport var PathUserContext = /*#__PURE__*/React.createContext(null);", "import * as React from 'react';\nvar PrivateContext = /*#__PURE__*/React.createContext({});\nexport default PrivateContext;", "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport isVisible from \"./isVisible\";\nfunction focusable(node) {\n  var includePositive = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  if (isVisible(node)) {\n    var nodeName = node.nodeName.toLowerCase();\n    var isFocusableElement =\n    // Focusable element\n    ['input', 'select', 'textarea', 'button'].includes(nodeName) ||\n    // Editable element\n    node.isContentEditable ||\n    // Anchor with href element\n    nodeName === 'a' && !!node.getAttribute('href');\n\n    // Get tabIndex\n    var tabIndexAttr = node.getAttribute('tabindex');\n    var tabIndexNum = Number(tabIndexAttr);\n\n    // Parse as number if validate\n    var tabIndex = null;\n    if (tabIndexAttr && !Number.isNaN(tabIndexNum)) {\n      tabIndex = tabIndexNum;\n    } else if (isFocusableElement && tabIndex === null) {\n      tabIndex = 0;\n    }\n\n    // Block focusable if disabled\n    if (isFocusableElement && node.disabled) {\n      tabIndex = null;\n    }\n    return tabIndex !== null && (tabIndex >= 0 || includePositive && tabIndex < 0);\n  }\n  return false;\n}\nexport function getFocusNodeList(node) {\n  var includePositive = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  var res = _toConsumableArray(node.querySelectorAll('*')).filter(function (child) {\n    return focusable(child, includePositive);\n  });\n  if (focusable(node, includePositive)) {\n    res.unshift(node);\n  }\n  return res;\n}\nvar lastFocusElement = null;\n\n/** @deprecated Do not use since this may failed when used in async */\nexport function saveLastFocusNode() {\n  lastFocusElement = document.activeElement;\n}\n\n/** @deprecated Do not use since this may failed when used in async */\nexport function clearLastFocusNode() {\n  lastFocusElement = null;\n}\n\n/** @deprecated Do not use since this may failed when used in async */\nexport function backLastFocusNode() {\n  if (lastFocusElement) {\n    try {\n      // 元素可能已经被移动了\n      lastFocusElement.focus();\n\n      /* eslint-disable no-empty */\n    } catch (e) {\n      // empty\n    }\n    /* eslint-enable no-empty */\n  }\n}\nexport function limitTabRange(node, e) {\n  if (e.keyCode === 9) {\n    var tabNodeList = getFocusNodeList(node);\n    var lastTabNode = tabNodeList[e.shiftKey ? 0 : tabNodeList.length - 1];\n    var leavingTab = lastTabNode === document.activeElement || node === document.activeElement;\n    if (leavingTab) {\n      var target = tabNodeList[e.shiftKey ? tabNodeList.length - 1 : 0];\n      target.focus();\n      e.preventDefault();\n    }\n  }\n}", "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { getFocusNodeList } from \"rc-util/es/Dom/focus\";\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport raf from \"rc-util/es/raf\";\nimport * as React from 'react';\nimport { getMenuId } from \"../context/IdContext\";\n// destruct to reduce minify size\nvar LEFT = KeyCode.LEFT,\n  RIGHT = KeyCode.RIGHT,\n  UP = KeyCode.UP,\n  DOWN = KeyCode.DOWN,\n  ENTER = KeyCode.ENTER,\n  ESC = KeyCode.ESC,\n  HOME = KeyCode.HOME,\n  END = KeyCode.END;\nvar ArrowKeys = [UP, DOWN, LEFT, RIGHT];\nfunction getOffset(mode, isRootLevel, isRtl, which) {\n  var _offsets;\n  var prev = 'prev';\n  var next = 'next';\n  var children = 'children';\n  var parent = 'parent';\n\n  // Inline enter is special that we use unique operation\n  if (mode === 'inline' && which === ENTER) {\n    return {\n      inlineTrigger: true\n    };\n  }\n  var inline = _defineProperty(_defineProperty({}, UP, prev), DOWN, next);\n  var horizontal = _defineProperty(_defineProperty(_defineProperty(_defineProperty({}, LEFT, isRtl ? next : prev), RIGHT, isRtl ? prev : next), DOWN, children), ENTER, children);\n  var vertical = _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({}, UP, prev), DOWN, next), ENTER, children), ESC, parent), LEFT, isRtl ? children : parent), RIGHT, isRtl ? parent : children);\n  var offsets = {\n    inline: inline,\n    horizontal: horizontal,\n    vertical: vertical,\n    inlineSub: inline,\n    horizontalSub: vertical,\n    verticalSub: vertical\n  };\n  var type = (_offsets = offsets[\"\".concat(mode).concat(isRootLevel ? '' : 'Sub')]) === null || _offsets === void 0 ? void 0 : _offsets[which];\n  switch (type) {\n    case prev:\n      return {\n        offset: -1,\n        sibling: true\n      };\n    case next:\n      return {\n        offset: 1,\n        sibling: true\n      };\n    case parent:\n      return {\n        offset: -1,\n        sibling: false\n      };\n    case children:\n      return {\n        offset: 1,\n        sibling: false\n      };\n    default:\n      return null;\n  }\n}\nfunction findContainerUL(element) {\n  var current = element;\n  while (current) {\n    if (current.getAttribute('data-menu-list')) {\n      return current;\n    }\n    current = current.parentElement;\n  }\n\n  // Normally should not reach this line\n  /* istanbul ignore next */\n  return null;\n}\n\n/**\n * Find focused element within element set provided\n */\nfunction getFocusElement(activeElement, elements) {\n  var current = activeElement || document.activeElement;\n  while (current) {\n    if (elements.has(current)) {\n      return current;\n    }\n    current = current.parentElement;\n  }\n  return null;\n}\n\n/**\n * Get focusable elements from the element set under provided container\n */\nexport function getFocusableElements(container, elements) {\n  var list = getFocusNodeList(container, true);\n  return list.filter(function (ele) {\n    return elements.has(ele);\n  });\n}\nfunction getNextFocusElement(parentQueryContainer, elements, focusMenuElement) {\n  var offset = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 1;\n  // Key on the menu item will not get validate parent container\n  if (!parentQueryContainer) {\n    return null;\n  }\n\n  // List current level menu item elements\n  var sameLevelFocusableMenuElementList = getFocusableElements(parentQueryContainer, elements);\n\n  // Find next focus index\n  var count = sameLevelFocusableMenuElementList.length;\n  var focusIndex = sameLevelFocusableMenuElementList.findIndex(function (ele) {\n    return focusMenuElement === ele;\n  });\n  if (offset < 0) {\n    if (focusIndex === -1) {\n      focusIndex = count - 1;\n    } else {\n      focusIndex -= 1;\n    }\n  } else if (offset > 0) {\n    focusIndex += 1;\n  }\n  focusIndex = (focusIndex + count) % count;\n\n  // Focus menu item\n  return sameLevelFocusableMenuElementList[focusIndex];\n}\nexport var refreshElements = function refreshElements(keys, id) {\n  var elements = new Set();\n  var key2element = new Map();\n  var element2key = new Map();\n  keys.forEach(function (key) {\n    var element = document.querySelector(\"[data-menu-id='\".concat(getMenuId(id, key), \"']\"));\n    if (element) {\n      elements.add(element);\n      element2key.set(element, key);\n      key2element.set(key, element);\n    }\n  });\n  return {\n    elements: elements,\n    key2element: key2element,\n    element2key: element2key\n  };\n};\nexport function useAccessibility(mode, activeKey, isRtl, id, containerRef, getKeys, getKeyPath, triggerActiveKey, triggerAccessibilityOpen, originOnKeyDown) {\n  var rafRef = React.useRef();\n  var activeRef = React.useRef();\n  activeRef.current = activeKey;\n  var cleanRaf = function cleanRaf() {\n    raf.cancel(rafRef.current);\n  };\n  React.useEffect(function () {\n    return function () {\n      cleanRaf();\n    };\n  }, []);\n  return function (e) {\n    var which = e.which;\n    if ([].concat(ArrowKeys, [ENTER, ESC, HOME, END]).includes(which)) {\n      var keys = getKeys();\n      var refreshedElements = refreshElements(keys, id);\n      var _refreshedElements = refreshedElements,\n        elements = _refreshedElements.elements,\n        key2element = _refreshedElements.key2element,\n        element2key = _refreshedElements.element2key;\n\n      // First we should find current focused MenuItem/SubMenu element\n      var activeElement = key2element.get(activeKey);\n      var focusMenuElement = getFocusElement(activeElement, elements);\n      var focusMenuKey = element2key.get(focusMenuElement);\n      var offsetObj = getOffset(mode, getKeyPath(focusMenuKey, true).length === 1, isRtl, which);\n\n      // Some mode do not have fully arrow operation like inline\n      if (!offsetObj && which !== HOME && which !== END) {\n        return;\n      }\n\n      // Arrow prevent default to avoid page scroll\n      if (ArrowKeys.includes(which) || [HOME, END].includes(which)) {\n        e.preventDefault();\n      }\n      var tryFocus = function tryFocus(menuElement) {\n        if (menuElement) {\n          var focusTargetElement = menuElement;\n\n          // Focus to link instead of menu item if possible\n          var link = menuElement.querySelector('a');\n          if (link !== null && link !== void 0 && link.getAttribute('href')) {\n            focusTargetElement = link;\n          }\n          var targetKey = element2key.get(menuElement);\n          triggerActiveKey(targetKey);\n\n          /**\n           * Do not `useEffect` here since `tryFocus` may trigger async\n           * which makes React sync update the `activeKey`\n           * that force render before `useRef` set the next activeKey\n           */\n          cleanRaf();\n          rafRef.current = raf(function () {\n            if (activeRef.current === targetKey) {\n              focusTargetElement.focus();\n            }\n          });\n        }\n      };\n      if ([HOME, END].includes(which) || offsetObj.sibling || !focusMenuElement) {\n        // ========================== Sibling ==========================\n        // Find walkable focus menu element container\n        var parentQueryContainer;\n        if (!focusMenuElement || mode === 'inline') {\n          parentQueryContainer = containerRef.current;\n        } else {\n          parentQueryContainer = findContainerUL(focusMenuElement);\n        }\n\n        // Get next focus element\n        var targetElement;\n        var focusableElements = getFocusableElements(parentQueryContainer, elements);\n        if (which === HOME) {\n          targetElement = focusableElements[0];\n        } else if (which === END) {\n          targetElement = focusableElements[focusableElements.length - 1];\n        } else {\n          targetElement = getNextFocusElement(parentQueryContainer, elements, focusMenuElement, offsetObj.offset);\n        }\n        // Focus menu item\n        tryFocus(targetElement);\n\n        // ======================= InlineTrigger =======================\n      } else if (offsetObj.inlineTrigger) {\n        // Inline trigger no need switch to sub menu item\n        triggerAccessibilityOpen(focusMenuKey);\n        // =========================== Level ===========================\n      } else if (offsetObj.offset > 0) {\n        triggerAccessibilityOpen(focusMenuKey, true);\n        cleanRaf();\n        rafRef.current = raf(function () {\n          // Async should resync elements\n          refreshedElements = refreshElements(keys, id);\n          var controlId = focusMenuElement.getAttribute('aria-controls');\n          var subQueryContainer = document.getElementById(controlId);\n\n          // Get sub focusable menu item\n          var targetElement = getNextFocusElement(subQueryContainer, refreshedElements.elements);\n\n          // Focus menu item\n          tryFocus(targetElement);\n        }, 5);\n      } else if (offsetObj.offset < 0) {\n        var keyPath = getKeyPath(focusMenuKey, true);\n        var parentKey = keyPath[keyPath.length - 2];\n        var parentMenuElement = key2element.get(parentKey);\n\n        // Focus menu item\n        triggerAccessibilityOpen(parentKey, false);\n        tryFocus(parentMenuElement);\n      }\n    }\n\n    // Pass origin key down event\n    originOnKeyDown === null || originOnKeyDown === void 0 || originOnKeyDown(e);\n  };\n}", "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { useRef, useCallback } from 'react';\nimport warning from \"rc-util/es/warning\";\nimport { nextSlice } from \"../utils/timeUtil\";\nvar PATH_SPLIT = '__RC_UTIL_PATH_SPLIT__';\nvar getPathStr = function getPathStr(keyPath) {\n  return keyPath.join(PATH_SPLIT);\n};\nvar getPathKeys = function getPathKeys(keyPathStr) {\n  return keyPathStr.split(PATH_SPLIT);\n};\nexport var OVERFLOW_KEY = 'rc-menu-more';\nexport default function useKeyRecords() {\n  var _React$useState = React.useState({}),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    internalForceUpdate = _React$useState2[1];\n  var key2pathRef = useRef(new Map());\n  var path2keyRef = useRef(new Map());\n  var _React$useState3 = React.useState([]),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    overflowKeys = _React$useState4[0],\n    setOverflowKeys = _React$useState4[1];\n  var updateRef = useRef(0);\n  var destroyRef = useRef(false);\n  var forceUpdate = function forceUpdate() {\n    if (!destroyRef.current) {\n      internalForceUpdate({});\n    }\n  };\n  var registerPath = useCallback(function (key, keyPath) {\n    // Warning for invalidate or duplicated `key`\n    if (process.env.NODE_ENV !== 'production') {\n      warning(!key2pathRef.current.has(key), \"Duplicated key '\".concat(key, \"' used in Menu by path [\").concat(keyPath.join(' > '), \"]\"));\n    }\n\n    // Fill map\n    var connectedPath = getPathStr(keyPath);\n    path2keyRef.current.set(connectedPath, key);\n    key2pathRef.current.set(key, connectedPath);\n    updateRef.current += 1;\n    var id = updateRef.current;\n    nextSlice(function () {\n      if (id === updateRef.current) {\n        forceUpdate();\n      }\n    });\n  }, []);\n  var unregisterPath = useCallback(function (key, keyPath) {\n    var connectedPath = getPathStr(keyPath);\n    path2keyRef.current.delete(connectedPath);\n    key2pathRef.current.delete(key);\n  }, []);\n  var refreshOverflowKeys = useCallback(function (keys) {\n    setOverflowKeys(keys);\n  }, []);\n  var getKeyPath = useCallback(function (eventKey, includeOverflow) {\n    var fullPath = key2pathRef.current.get(eventKey) || '';\n    var keys = getPathKeys(fullPath);\n    if (includeOverflow && overflowKeys.includes(keys[0])) {\n      keys.unshift(OVERFLOW_KEY);\n    }\n    return keys;\n  }, [overflowKeys]);\n  var isSubPathKey = useCallback(function (pathKeys, eventKey) {\n    return pathKeys.filter(function (item) {\n      return item !== undefined;\n    }).some(function (pathKey) {\n      var pathKeyList = getKeyPath(pathKey, true);\n      return pathKeyList.includes(eventKey);\n    });\n  }, [getKeyPath]);\n  var getKeys = function getKeys() {\n    var keys = _toConsumableArray(key2pathRef.current.keys());\n    if (overflowKeys.length) {\n      keys.push(OVERFLOW_KEY);\n    }\n    return keys;\n  };\n\n  /**\n   * Find current key related child path keys\n   */\n  var getSubPathKeys = useCallback(function (key) {\n    var connectedPath = \"\".concat(key2pathRef.current.get(key)).concat(PATH_SPLIT);\n    var pathKeys = new Set();\n    _toConsumableArray(path2keyRef.current.keys()).forEach(function (pathKey) {\n      if (pathKey.startsWith(connectedPath)) {\n        pathKeys.add(path2keyRef.current.get(pathKey));\n      }\n    });\n    return pathKeys;\n  }, []);\n  React.useEffect(function () {\n    return function () {\n      destroyRef.current = true;\n    };\n  }, []);\n  return {\n    // Register\n    registerPath: registerPath,\n    unregisterPath: unregisterPath,\n    refreshOverflowKeys: refreshOverflowKeys,\n    // Util\n    isSubPathKey: isSubPathKey,\n    getKeyPath: getKeyPath,\n    getKeys: getKeys,\n    getSubPathKeys: getSubPathKeys\n  };\n}", "export function nextSlice(callback) {\n  /* istanbul ignore next */\n  Promise.resolve().then(callback);\n}", "import * as React from 'react';\n\n/**\n * Cache callback function that always return same ref instead.\n * This is used for context optimization.\n */\nexport default function useMemoCallback(func) {\n  var funRef = React.useRef(func);\n  funRef.current = func;\n  var callback = React.useCallback(function () {\n    var _funRef$current;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return (_funRef$current = funRef.current) === null || _funRef$current === void 0 ? void 0 : _funRef$current.call.apply(_funRef$current, [funRef].concat(args));\n  }, []);\n  return func ? callback : undefined;\n}", "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nvar uniquePrefix = Math.random().toFixed(5).toString().slice(2);\nvar internalId = 0;\nexport default function useUUID(id) {\n  var _useMergedState = useMergedState(id, {\n      value: id\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    uuid = _useMergedState2[0],\n    setUUID = _useMergedState2[1];\n  React.useEffect(function () {\n    internalId += 1;\n    var newId = process.env.NODE_ENV === 'test' ? 'test' : \"\".concat(uniquePrefix, \"-\").concat(internalId);\n    setUUID(\"rc-menu-uuid-\".concat(newId));\n  }, []);\n  return uuid;\n}", "import * as React from 'react';\nimport { MenuContext } from \"../context/MenuContext\";\nexport default function useActive(eventKey, disabled, onMouseEnter, onMouseLeave) {\n  var _React$useContext = React.useContext(MenuContext),\n    activeKey = _React$useContext.activeKey,\n    onActive = _React$useContext.onActive,\n    onInactive = _React$useContext.onInactive;\n  var ret = {\n    active: activeKey === eventKey\n  };\n\n  // Skip when disabled\n  if (!disabled) {\n    ret.onMouseEnter = function (domEvent) {\n      onMouseEnter === null || onMouseEnter === void 0 || onMouseEnter({\n        key: eventKey,\n        domEvent: domEvent\n      });\n      onActive(eventKey);\n    };\n    ret.onMouseLeave = function (domEvent) {\n      onMouseLeave === null || onMouseLeave === void 0 || onMouseLeave({\n        key: eventKey,\n        domEvent: domEvent\n      });\n      onInactive(eventKey);\n    };\n  }\n  return ret;\n}", "import * as React from 'react';\nimport { MenuContext } from \"../context/MenuContext\";\nexport default function useDirectionStyle(level) {\n  var _React$useContext = React.useContext(MenuContext),\n    mode = _React$useContext.mode,\n    rtl = _React$useContext.rtl,\n    inlineIndent = _React$useContext.inlineIndent;\n  if (mode !== 'inline') {\n    return null;\n  }\n  var len = level;\n  return rtl ? {\n    paddingRight: len * inlineIndent\n  } : {\n    paddingLeft: len * inlineIndent\n  };\n}", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nexport default function Icon(_ref) {\n  var icon = _ref.icon,\n    props = _ref.props,\n    children = _ref.children;\n  var iconNode;\n  if (icon === null || icon === false) {\n    return null;\n  }\n  if (typeof icon === 'function') {\n    iconNode = /*#__PURE__*/React.createElement(icon, _objectSpread({}, props));\n  } else if (typeof icon !== \"boolean\") {\n    // Compatible for origin definition\n    iconNode = icon;\n  }\n  return iconNode || children || null;\n}", "import _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"item\"];\nimport warning from \"rc-util/es/warning\";\n\n/**\n * `onClick` event return `info.item` which point to react node directly.\n * We should warning this since it will not work on FC.\n */\nexport function warnItemProp(_ref) {\n  var item = _ref.item,\n    restInfo = _objectWithoutProperties(_ref, _excluded);\n  Object.defineProperty(restInfo, 'item', {\n    get: function get() {\n      warning(false, '`info.item` is deprecated since we will move to function component that not provides React Node instance in future.');\n      return item;\n    }\n  });\n  return restInfo;\n}", "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nvar _excluded = [\"title\", \"attribute\", \"elementRef\"],\n  _excluded2 = [\"style\", \"className\", \"eventKey\", \"warnKey\", \"disabled\", \"itemIcon\", \"children\", \"role\", \"onMouseEnter\", \"onMouseLeave\", \"onClick\", \"onKeyDown\", \"onFocus\"],\n  _excluded3 = [\"active\"];\nimport classNames from 'classnames';\nimport Overflow from 'rc-overflow';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport omit from \"rc-util/es/omit\";\nimport { useComposeRef } from \"rc-util/es/ref\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport { useMenuId } from \"./context/IdContext\";\nimport { MenuContext } from \"./context/MenuContext\";\nimport { useFullPath, useMeasure } from \"./context/PathContext\";\nimport PrivateContext from \"./context/PrivateContext\";\nimport useActive from \"./hooks/useActive\";\nimport useDirectionStyle from \"./hooks/useDirectionStyle\";\nimport Icon from \"./Icon\";\nimport { warnItemProp } from \"./utils/warnUtil\";\n// Since Menu event provide the `info.item` which point to the MenuItem node instance.\n// We have to use class component here.\n// This should be removed from doc & api in future.\nvar LegacyMenuItem = /*#__PURE__*/function (_React$Component) {\n  _inherits(LegacyMenuItem, _React$Component);\n  var _super = _createSuper(LegacyMenuItem);\n  function LegacyMenuItem() {\n    _classCallCheck(this, LegacyMenuItem);\n    return _super.apply(this, arguments);\n  }\n  _createClass(LegacyMenuItem, [{\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n        title = _this$props.title,\n        attribute = _this$props.attribute,\n        elementRef = _this$props.elementRef,\n        restProps = _objectWithoutProperties(_this$props, _excluded);\n\n      // Here the props are eventually passed to the DOM element.\n      // React does not recognize non-standard attributes.\n      // Therefore, remove the props that is not used here.\n      // ref: https://github.com/ant-design/ant-design/issues/41395\n      var passedProps = omit(restProps, ['eventKey', 'popupClassName', 'popupOffset', 'onTitleClick']);\n      warning(!attribute, '`attribute` of Menu.Item is deprecated. Please pass attribute directly.');\n      return /*#__PURE__*/React.createElement(Overflow.Item, _extends({}, attribute, {\n        title: typeof title === 'string' ? title : undefined\n      }, passedProps, {\n        ref: elementRef\n      }));\n    }\n  }]);\n  return LegacyMenuItem;\n}(React.Component);\n/**\n * Real Menu Item component\n */\nvar InternalMenuItem = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var style = props.style,\n    className = props.className,\n    eventKey = props.eventKey,\n    warnKey = props.warnKey,\n    disabled = props.disabled,\n    itemIcon = props.itemIcon,\n    children = props.children,\n    role = props.role,\n    onMouseEnter = props.onMouseEnter,\n    onMouseLeave = props.onMouseLeave,\n    onClick = props.onClick,\n    onKeyDown = props.onKeyDown,\n    onFocus = props.onFocus,\n    restProps = _objectWithoutProperties(props, _excluded2);\n  var domDataId = useMenuId(eventKey);\n  var _React$useContext = React.useContext(MenuContext),\n    prefixCls = _React$useContext.prefixCls,\n    onItemClick = _React$useContext.onItemClick,\n    contextDisabled = _React$useContext.disabled,\n    overflowDisabled = _React$useContext.overflowDisabled,\n    contextItemIcon = _React$useContext.itemIcon,\n    selectedKeys = _React$useContext.selectedKeys,\n    onActive = _React$useContext.onActive;\n  var _React$useContext2 = React.useContext(PrivateContext),\n    _internalRenderMenuItem = _React$useContext2._internalRenderMenuItem;\n  var itemCls = \"\".concat(prefixCls, \"-item\");\n  var legacyMenuItemRef = React.useRef();\n  var elementRef = React.useRef();\n  var mergedDisabled = contextDisabled || disabled;\n  var mergedEleRef = useComposeRef(ref, elementRef);\n  var connectedKeys = useFullPath(eventKey);\n\n  // ================================ Warn ================================\n  if (process.env.NODE_ENV !== 'production' && warnKey) {\n    warning(false, 'MenuItem should not leave undefined `key`.');\n  }\n\n  // ============================= Info =============================\n  var getEventInfo = function getEventInfo(e) {\n    return {\n      key: eventKey,\n      // Note: For legacy code is reversed which not like other antd component\n      keyPath: _toConsumableArray(connectedKeys).reverse(),\n      item: legacyMenuItemRef.current,\n      domEvent: e\n    };\n  };\n\n  // ============================= Icon =============================\n  var mergedItemIcon = itemIcon || contextItemIcon;\n\n  // ============================ Active ============================\n  var _useActive = useActive(eventKey, mergedDisabled, onMouseEnter, onMouseLeave),\n    active = _useActive.active,\n    activeProps = _objectWithoutProperties(_useActive, _excluded3);\n\n  // ============================ Select ============================\n  var selected = selectedKeys.includes(eventKey);\n\n  // ======================== DirectionStyle ========================\n  var directionStyle = useDirectionStyle(connectedKeys.length);\n\n  // ============================ Events ============================\n  var onInternalClick = function onInternalClick(e) {\n    if (mergedDisabled) {\n      return;\n    }\n    var info = getEventInfo(e);\n    onClick === null || onClick === void 0 || onClick(warnItemProp(info));\n    onItemClick(info);\n  };\n  var onInternalKeyDown = function onInternalKeyDown(e) {\n    onKeyDown === null || onKeyDown === void 0 || onKeyDown(e);\n    if (e.which === KeyCode.ENTER) {\n      var info = getEventInfo(e);\n\n      // Legacy. Key will also trigger click event\n      onClick === null || onClick === void 0 || onClick(warnItemProp(info));\n      onItemClick(info);\n    }\n  };\n\n  /**\n   * Used for accessibility. Helper will focus element without key board.\n   * We should manually trigger an active\n   */\n  var onInternalFocus = function onInternalFocus(e) {\n    onActive(eventKey);\n    onFocus === null || onFocus === void 0 || onFocus(e);\n  };\n\n  // ============================ Render ============================\n  var optionRoleProps = {};\n  if (props.role === 'option') {\n    optionRoleProps['aria-selected'] = selected;\n  }\n  var renderNode = /*#__PURE__*/React.createElement(LegacyMenuItem, _extends({\n    ref: legacyMenuItemRef,\n    elementRef: mergedEleRef,\n    role: role === null ? 'none' : role || 'menuitem',\n    tabIndex: disabled ? null : -1,\n    \"data-menu-id\": overflowDisabled && domDataId ? null : domDataId\n  }, omit(restProps, ['extra']), activeProps, optionRoleProps, {\n    component: \"li\",\n    \"aria-disabled\": disabled,\n    style: _objectSpread(_objectSpread({}, directionStyle), style),\n    className: classNames(itemCls, _defineProperty(_defineProperty(_defineProperty({}, \"\".concat(itemCls, \"-active\"), active), \"\".concat(itemCls, \"-selected\"), selected), \"\".concat(itemCls, \"-disabled\"), mergedDisabled), className),\n    onClick: onInternalClick,\n    onKeyDown: onInternalKeyDown,\n    onFocus: onInternalFocus\n  }), children, /*#__PURE__*/React.createElement(Icon, {\n    props: _objectSpread(_objectSpread({}, props), {}, {\n      isSelected: selected\n    }),\n    icon: mergedItemIcon\n  }));\n  if (_internalRenderMenuItem) {\n    renderNode = _internalRenderMenuItem(renderNode, props, {\n      selected: selected\n    });\n  }\n  return renderNode;\n});\nfunction MenuItem(props, ref) {\n  var eventKey = props.eventKey;\n\n  // ==================== Record KeyPath ====================\n  var measure = useMeasure();\n  var connectedKeyPath = useFullPath(eventKey);\n\n  // eslint-disable-next-line consistent-return\n  React.useEffect(function () {\n    if (measure) {\n      measure.registerPath(eventKey, connectedKeyPath);\n      return function () {\n        measure.unregisterPath(eventKey, connectedKeyPath);\n      };\n    }\n  }, [connectedKeyPath]);\n  if (measure) {\n    return null;\n  }\n\n  // ======================== Render ========================\n  return /*#__PURE__*/React.createElement(InternalMenuItem, _extends({}, props, {\n    ref: ref\n  }));\n}\nexport default /*#__PURE__*/React.forwardRef(MenuItem);", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"className\", \"children\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { MenuContext } from \"../context/MenuContext\";\nvar InternalSubMenuList = function InternalSubMenuList(_ref, ref) {\n  var className = _ref.className,\n    children = _ref.children,\n    restProps = _objectWithoutProperties(_ref, _excluded);\n  var _React$useContext = React.useContext(MenuContext),\n    prefixCls = _React$useContext.prefixCls,\n    mode = _React$useContext.mode,\n    rtl = _React$useContext.rtl;\n  return /*#__PURE__*/React.createElement(\"ul\", _extends({\n    className: classNames(prefixCls, rtl && \"\".concat(prefixCls, \"-rtl\"), \"\".concat(prefixCls, \"-sub\"), \"\".concat(prefixCls, \"-\").concat(mode === 'inline' ? 'inline' : 'vertical'), className),\n    role: \"menu\"\n  }, restProps, {\n    \"data-menu-list\": true,\n    ref: ref\n  }), children);\n};\nvar SubMenuList = /*#__PURE__*/React.forwardRef(InternalSubMenuList);\nSubMenuList.displayName = 'SubMenuList';\nexport default SubMenuList;", "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport toArray from \"rc-util/es/Children/toArray\";\nimport * as React from 'react';\nexport function parseChildren(children, keyPath) {\n  return toArray(children).map(function (child, index) {\n    if ( /*#__PURE__*/React.isValidElement(child)) {\n      var _eventKey, _child$props;\n      var key = child.key;\n      var eventKey = (_eventKey = (_child$props = child.props) === null || _child$props === void 0 ? void 0 : _child$props.eventKey) !== null && _eventKey !== void 0 ? _eventKey : key;\n      var emptyKey = eventKey === null || eventKey === undefined;\n      if (emptyKey) {\n        eventKey = \"tmp_key-\".concat([].concat(_toConsumableArray(keyPath), [index]).join('-'));\n      }\n      var cloneProps = {\n        key: eventKey,\n        eventKey: eventKey\n      };\n      if (process.env.NODE_ENV !== 'production' && emptyKey) {\n        cloneProps.warnKey = true;\n      }\n      return /*#__PURE__*/React.cloneElement(child, cloneProps);\n    }\n    return child;\n  });\n}", "var autoAdjustOverflow = {\n  adjustX: 1,\n  adjustY: 1\n};\nexport var placements = {\n  topLeft: {\n    points: ['bl', 'tl'],\n    overflow: autoAdjustOverflow\n  },\n  topRight: {\n    points: ['br', 'tr'],\n    overflow: autoAdjustOverflow\n  },\n  bottomLeft: {\n    points: ['tl', 'bl'],\n    overflow: autoAdjustOverflow\n  },\n  bottomRight: {\n    points: ['tr', 'br'],\n    overflow: autoAdjustOverflow\n  },\n  leftTop: {\n    points: ['tr', 'tl'],\n    overflow: autoAdjustOverflow\n  },\n  leftBottom: {\n    points: ['br', 'bl'],\n    overflow: autoAdjustOverflow\n  },\n  rightTop: {\n    points: ['tl', 'tr'],\n    overflow: autoAdjustOverflow\n  },\n  rightBottom: {\n    points: ['bl', 'br'],\n    overflow: autoAdjustOverflow\n  }\n};\nexport var placementsRtl = {\n  topLeft: {\n    points: ['bl', 'tl'],\n    overflow: autoAdjustOverflow\n  },\n  topRight: {\n    points: ['br', 'tr'],\n    overflow: autoAdjustOverflow\n  },\n  bottomLeft: {\n    points: ['tl', 'bl'],\n    overflow: autoAdjustOverflow\n  },\n  bottomRight: {\n    points: ['tr', 'br'],\n    overflow: autoAdjustOverflow\n  },\n  rightTop: {\n    points: ['tr', 'tl'],\n    overflow: autoAdjustOverflow\n  },\n  rightBottom: {\n    points: ['br', 'bl'],\n    overflow: autoAdjustOverflow\n  },\n  leftTop: {\n    points: ['tl', 'tr'],\n    overflow: autoAdjustOverflow\n  },\n  leftBottom: {\n    points: ['bl', 'br'],\n    overflow: autoAdjustOverflow\n  }\n};\nexport default placements;", "export function getMotion(mode, motion, defaultMotions) {\n  if (motion) {\n    return motion;\n  }\n  if (defaultMotions) {\n    return defaultMotions[mode] || defaultMotions.other;\n  }\n  return undefined;\n}", "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport Trigger from '@rc-component/trigger';\nimport classNames from 'classnames';\nimport raf from \"rc-util/es/raf\";\nimport { MenuContext } from \"../context/MenuContext\";\nimport { placements, placementsRtl } from \"../placements\";\nimport { getMotion } from \"../utils/motionUtil\";\nvar popupPlacementMap = {\n  horizontal: 'bottomLeft',\n  vertical: 'rightTop',\n  'vertical-left': 'rightTop',\n  'vertical-right': 'leftTop'\n};\nexport default function PopupTrigger(_ref) {\n  var prefixCls = _ref.prefixCls,\n    visible = _ref.visible,\n    children = _ref.children,\n    popup = _ref.popup,\n    popupStyle = _ref.popupStyle,\n    popupClassName = _ref.popupClassName,\n    popupOffset = _ref.popupOffset,\n    disabled = _ref.disabled,\n    mode = _ref.mode,\n    onVisibleChange = _ref.onVisibleChange;\n  var _React$useContext = React.useContext(MenuContext),\n    getPopupContainer = _React$useContext.getPopupContainer,\n    rtl = _React$useContext.rtl,\n    subMenuOpenDelay = _React$useContext.subMenuOpenDelay,\n    subMenuCloseDelay = _React$useContext.subMenuCloseDelay,\n    builtinPlacements = _React$useContext.builtinPlacements,\n    triggerSubMenuAction = _React$useContext.triggerSubMenuAction,\n    forceSubMenuRender = _React$useContext.forceSubMenuRender,\n    rootClassName = _React$useContext.rootClassName,\n    motion = _React$useContext.motion,\n    defaultMotions = _React$useContext.defaultMotions;\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    innerVisible = _React$useState2[0],\n    setInnerVisible = _React$useState2[1];\n  var placement = rtl ? _objectSpread(_objectSpread({}, placementsRtl), builtinPlacements) : _objectSpread(_objectSpread({}, placements), builtinPlacements);\n  var popupPlacement = popupPlacementMap[mode];\n  var targetMotion = getMotion(mode, motion, defaultMotions);\n  var targetMotionRef = React.useRef(targetMotion);\n  if (mode !== 'inline') {\n    /**\n     * PopupTrigger is only used for vertical and horizontal types.\n     * When collapsed is unfolded, the inline animation will destroy the vertical animation.\n     */\n    targetMotionRef.current = targetMotion;\n  }\n  var mergedMotion = _objectSpread(_objectSpread({}, targetMotionRef.current), {}, {\n    leavedClassName: \"\".concat(prefixCls, \"-hidden\"),\n    removeOnLeave: false,\n    motionAppear: true\n  });\n\n  // Delay to change visible\n  var visibleRef = React.useRef();\n  React.useEffect(function () {\n    visibleRef.current = raf(function () {\n      setInnerVisible(visible);\n    });\n    return function () {\n      raf.cancel(visibleRef.current);\n    };\n  }, [visible]);\n  return /*#__PURE__*/React.createElement(Trigger, {\n    prefixCls: prefixCls,\n    popupClassName: classNames(\"\".concat(prefixCls, \"-popup\"), _defineProperty({}, \"\".concat(prefixCls, \"-rtl\"), rtl), popupClassName, rootClassName),\n    stretch: mode === 'horizontal' ? 'minWidth' : null,\n    getPopupContainer: getPopupContainer,\n    builtinPlacements: placement,\n    popupPlacement: popupPlacement,\n    popupVisible: innerVisible,\n    popup: popup,\n    popupStyle: popupStyle,\n    popupAlign: popupOffset && {\n      offset: popupOffset\n    },\n    action: disabled ? [] : [triggerSubMenuAction],\n    mouseEnterDelay: subMenuOpenDelay,\n    mouseLeaveDelay: subMenuCloseDelay,\n    onPopupVisibleChange: onVisibleChange,\n    forceRender: forceSubMenuRender,\n    popupMotion: mergedMotion,\n    fresh: true\n  }, children);\n}", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport CSSMotion from 'rc-motion';\nimport { getMotion } from \"../utils/motionUtil\";\nimport MenuContextProvider, { MenuContext } from \"../context/MenuContext\";\nimport SubMenuList from \"./SubMenuList\";\nexport default function InlineSubMenuList(_ref) {\n  var id = _ref.id,\n    open = _ref.open,\n    keyPath = _ref.keyPath,\n    children = _ref.children;\n  var fixedMode = 'inline';\n  var _React$useContext = React.useContext(MenuContext),\n    prefixCls = _React$useContext.prefixCls,\n    forceSubMenuRender = _React$useContext.forceSubMenuRender,\n    motion = _React$useContext.motion,\n    defaultMotions = _React$useContext.defaultMotions,\n    mode = _React$useContext.mode;\n\n  // Always use latest mode check\n  var sameModeRef = React.useRef(false);\n  sameModeRef.current = mode === fixedMode;\n\n  // We record `destroy` mark here since when mode change from `inline` to others.\n  // The inline list should remove when motion end.\n  var _React$useState = React.useState(!sameModeRef.current),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    destroy = _React$useState2[0],\n    setDestroy = _React$useState2[1];\n  var mergedOpen = sameModeRef.current ? open : false;\n\n  // ================================= Effect =================================\n  // Reset destroy state when mode change back\n  React.useEffect(function () {\n    if (sameModeRef.current) {\n      setDestroy(false);\n    }\n  }, [mode]);\n\n  // ================================= Render =================================\n  var mergedMotion = _objectSpread({}, getMotion(fixedMode, motion, defaultMotions));\n\n  // No need appear since nest inlineCollapse changed\n  if (keyPath.length > 1) {\n    mergedMotion.motionAppear = false;\n  }\n\n  // Hide inline list when mode changed and motion end\n  var originOnVisibleChanged = mergedMotion.onVisibleChanged;\n  mergedMotion.onVisibleChanged = function (newVisible) {\n    if (!sameModeRef.current && !newVisible) {\n      setDestroy(true);\n    }\n    return originOnVisibleChanged === null || originOnVisibleChanged === void 0 ? void 0 : originOnVisibleChanged(newVisible);\n  };\n  if (destroy) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(MenuContextProvider, {\n    mode: fixedMode,\n    locked: !sameModeRef.current\n  }, /*#__PURE__*/React.createElement(CSSMotion, _extends({\n    visible: mergedOpen\n  }, mergedMotion, {\n    forceRender: forceSubMenuRender,\n    removeOnLeave: false,\n    leavedClassName: \"\".concat(prefixCls, \"-hidden\")\n  }), function (_ref2) {\n    var motionClassName = _ref2.className,\n      motionStyle = _ref2.style;\n    return /*#__PURE__*/React.createElement(SubMenuList, {\n      id: id,\n      className: motionClassName,\n      style: motionStyle\n    }, children);\n  }));\n}", "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"style\", \"className\", \"title\", \"eventKey\", \"warnKey\", \"disabled\", \"internalPopupClose\", \"children\", \"itemIcon\", \"expandIcon\", \"popupClassName\", \"popupOffset\", \"popupStyle\", \"onClick\", \"onMouseEnter\", \"onMouseLeave\", \"onTitleClick\", \"onTitleMouseEnter\", \"onTitleMouseLeave\"],\n  _excluded2 = [\"active\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport Overflow from 'rc-overflow';\nimport warning from \"rc-util/es/warning\";\nimport SubMenuList from \"./SubMenuList\";\nimport { parseChildren } from \"../utils/commonUtil\";\nimport MenuContextProvider, { MenuContext } from \"../context/MenuContext\";\nimport useMemoCallback from \"../hooks/useMemoCallback\";\nimport PopupTrigger from \"./PopupTrigger\";\nimport Icon from \"../Icon\";\nimport useActive from \"../hooks/useActive\";\nimport { warnItemProp } from \"../utils/warnUtil\";\nimport useDirectionStyle from \"../hooks/useDirectionStyle\";\nimport InlineSubMenuList from \"./InlineSubMenuList\";\nimport { PathTrackerContext, PathUserContext, useFullPath, useMeasure } from \"../context/PathContext\";\nimport { useMenuId } from \"../context/IdContext\";\nimport PrivateContext from \"../context/PrivateContext\";\nvar InternalSubMenu = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var style = props.style,\n    className = props.className,\n    title = props.title,\n    eventKey = props.eventKey,\n    warnKey = props.warnKey,\n    disabled = props.disabled,\n    internalPopupClose = props.internalPopupClose,\n    children = props.children,\n    itemIcon = props.itemIcon,\n    expandIcon = props.expandIcon,\n    popupClassName = props.popupClassName,\n    popupOffset = props.popupOffset,\n    popupStyle = props.popupStyle,\n    onClick = props.onClick,\n    onMouseEnter = props.onMouseEnter,\n    onMouseLeave = props.onMouseLeave,\n    onTitleClick = props.onTitleClick,\n    onTitleMouseEnter = props.onTitleMouseEnter,\n    onTitleMouseLeave = props.onTitleMouseLeave,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var domDataId = useMenuId(eventKey);\n  var _React$useContext = React.useContext(MenuContext),\n    prefixCls = _React$useContext.prefixCls,\n    mode = _React$useContext.mode,\n    openKeys = _React$useContext.openKeys,\n    contextDisabled = _React$useContext.disabled,\n    overflowDisabled = _React$useContext.overflowDisabled,\n    activeKey = _React$useContext.activeKey,\n    selectedKeys = _React$useContext.selectedKeys,\n    contextItemIcon = _React$useContext.itemIcon,\n    contextExpandIcon = _React$useContext.expandIcon,\n    onItemClick = _React$useContext.onItemClick,\n    onOpenChange = _React$useContext.onOpenChange,\n    onActive = _React$useContext.onActive;\n  var _React$useContext2 = React.useContext(PrivateContext),\n    _internalRenderSubMenuItem = _React$useContext2._internalRenderSubMenuItem;\n  var _React$useContext3 = React.useContext(PathUserContext),\n    isSubPathKey = _React$useContext3.isSubPathKey;\n  var connectedPath = useFullPath();\n  var subMenuPrefixCls = \"\".concat(prefixCls, \"-submenu\");\n  var mergedDisabled = contextDisabled || disabled;\n  var elementRef = React.useRef();\n  var popupRef = React.useRef();\n\n  // ================================ Warn ================================\n  if (process.env.NODE_ENV !== 'production' && warnKey) {\n    warning(false, 'SubMenu should not leave undefined `key`.');\n  }\n\n  // ================================ Icon ================================\n  var mergedItemIcon = itemIcon !== null && itemIcon !== void 0 ? itemIcon : contextItemIcon;\n  var mergedExpandIcon = expandIcon !== null && expandIcon !== void 0 ? expandIcon : contextExpandIcon;\n\n  // ================================ Open ================================\n  var originOpen = openKeys.includes(eventKey);\n  var open = !overflowDisabled && originOpen;\n\n  // =============================== Select ===============================\n  var childrenSelected = isSubPathKey(selectedKeys, eventKey);\n\n  // =============================== Active ===============================\n  var _useActive = useActive(eventKey, mergedDisabled, onTitleMouseEnter, onTitleMouseLeave),\n    active = _useActive.active,\n    activeProps = _objectWithoutProperties(_useActive, _excluded2);\n\n  // Fallback of active check to avoid hover on menu title or disabled item\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    childrenActive = _React$useState2[0],\n    setChildrenActive = _React$useState2[1];\n  var triggerChildrenActive = function triggerChildrenActive(newActive) {\n    if (!mergedDisabled) {\n      setChildrenActive(newActive);\n    }\n  };\n  var onInternalMouseEnter = function onInternalMouseEnter(domEvent) {\n    triggerChildrenActive(true);\n    onMouseEnter === null || onMouseEnter === void 0 || onMouseEnter({\n      key: eventKey,\n      domEvent: domEvent\n    });\n  };\n  var onInternalMouseLeave = function onInternalMouseLeave(domEvent) {\n    triggerChildrenActive(false);\n    onMouseLeave === null || onMouseLeave === void 0 || onMouseLeave({\n      key: eventKey,\n      domEvent: domEvent\n    });\n  };\n  var mergedActive = React.useMemo(function () {\n    if (active) {\n      return active;\n    }\n    if (mode !== 'inline') {\n      return childrenActive || isSubPathKey([activeKey], eventKey);\n    }\n    return false;\n  }, [mode, active, activeKey, childrenActive, eventKey, isSubPathKey]);\n\n  // ========================== DirectionStyle ==========================\n  var directionStyle = useDirectionStyle(connectedPath.length);\n\n  // =============================== Events ===============================\n  // >>>> Title click\n  var onInternalTitleClick = function onInternalTitleClick(e) {\n    // Skip if disabled\n    if (mergedDisabled) {\n      return;\n    }\n    onTitleClick === null || onTitleClick === void 0 || onTitleClick({\n      key: eventKey,\n      domEvent: e\n    });\n\n    // Trigger open by click when mode is `inline`\n    if (mode === 'inline') {\n      onOpenChange(eventKey, !originOpen);\n    }\n  };\n\n  // >>>> Context for children click\n  var onMergedItemClick = useMemoCallback(function (info) {\n    onClick === null || onClick === void 0 || onClick(warnItemProp(info));\n    onItemClick(info);\n  });\n\n  // >>>>> Visible change\n  var onPopupVisibleChange = function onPopupVisibleChange(newVisible) {\n    if (mode !== 'inline') {\n      onOpenChange(eventKey, newVisible);\n    }\n  };\n\n  /**\n   * Used for accessibility. Helper will focus element without key board.\n   * We should manually trigger an active\n   */\n  var onInternalFocus = function onInternalFocus() {\n    onActive(eventKey);\n  };\n\n  // =============================== Render ===============================\n  var popupId = domDataId && \"\".concat(domDataId, \"-popup\");\n  var expandIconNode = React.useMemo(function () {\n    return /*#__PURE__*/React.createElement(Icon, {\n      icon: mode !== 'horizontal' ? mergedExpandIcon : undefined,\n      props: _objectSpread(_objectSpread({}, props), {}, {\n        isOpen: open,\n        // [Legacy] Not sure why need this mark\n        isSubMenu: true\n      })\n    }, /*#__PURE__*/React.createElement(\"i\", {\n      className: \"\".concat(subMenuPrefixCls, \"-arrow\")\n    }));\n  }, [mode, mergedExpandIcon, props, open, subMenuPrefixCls]);\n\n  // >>>>> Title\n  var titleNode = /*#__PURE__*/React.createElement(\"div\", _extends({\n    role: \"menuitem\",\n    style: directionStyle,\n    className: \"\".concat(subMenuPrefixCls, \"-title\"),\n    tabIndex: mergedDisabled ? null : -1,\n    ref: elementRef,\n    title: typeof title === 'string' ? title : null,\n    \"data-menu-id\": overflowDisabled && domDataId ? null : domDataId,\n    \"aria-expanded\": open,\n    \"aria-haspopup\": true,\n    \"aria-controls\": popupId,\n    \"aria-disabled\": mergedDisabled,\n    onClick: onInternalTitleClick,\n    onFocus: onInternalFocus\n  }, activeProps), title, expandIconNode);\n\n  // Cache mode if it change to `inline` which do not have popup motion\n  var triggerModeRef = React.useRef(mode);\n  if (mode !== 'inline' && connectedPath.length > 1) {\n    triggerModeRef.current = 'vertical';\n  } else {\n    triggerModeRef.current = mode;\n  }\n  if (!overflowDisabled) {\n    var triggerMode = triggerModeRef.current;\n\n    // Still wrap with Trigger here since we need avoid react re-mount dom node\n    // Which makes motion failed\n    titleNode = /*#__PURE__*/React.createElement(PopupTrigger, {\n      mode: triggerMode,\n      prefixCls: subMenuPrefixCls,\n      visible: !internalPopupClose && open && mode !== 'inline',\n      popupClassName: popupClassName,\n      popupOffset: popupOffset,\n      popupStyle: popupStyle,\n      popup: /*#__PURE__*/React.createElement(MenuContextProvider\n      // Special handle of horizontal mode\n      , {\n        mode: triggerMode === 'horizontal' ? 'vertical' : triggerMode\n      }, /*#__PURE__*/React.createElement(SubMenuList, {\n        id: popupId,\n        ref: popupRef\n      }, children)),\n      disabled: mergedDisabled,\n      onVisibleChange: onPopupVisibleChange\n    }, titleNode);\n  }\n\n  // >>>>> List node\n  var listNode = /*#__PURE__*/React.createElement(Overflow.Item, _extends({\n    ref: ref,\n    role: \"none\"\n  }, restProps, {\n    component: \"li\",\n    style: style,\n    className: classNames(subMenuPrefixCls, \"\".concat(subMenuPrefixCls, \"-\").concat(mode), className, _defineProperty(_defineProperty(_defineProperty(_defineProperty({}, \"\".concat(subMenuPrefixCls, \"-open\"), open), \"\".concat(subMenuPrefixCls, \"-active\"), mergedActive), \"\".concat(subMenuPrefixCls, \"-selected\"), childrenSelected), \"\".concat(subMenuPrefixCls, \"-disabled\"), mergedDisabled)),\n    onMouseEnter: onInternalMouseEnter,\n    onMouseLeave: onInternalMouseLeave\n  }), titleNode, !overflowDisabled && /*#__PURE__*/React.createElement(InlineSubMenuList, {\n    id: popupId,\n    open: open,\n    keyPath: connectedPath\n  }, children));\n  if (_internalRenderSubMenuItem) {\n    listNode = _internalRenderSubMenuItem(listNode, props, {\n      selected: childrenSelected,\n      active: mergedActive,\n      open: open,\n      disabled: mergedDisabled\n    });\n  }\n\n  // >>>>> Render\n  return /*#__PURE__*/React.createElement(MenuContextProvider, {\n    onItemClick: onMergedItemClick,\n    mode: mode === 'horizontal' ? 'vertical' : mode,\n    itemIcon: mergedItemIcon,\n    expandIcon: mergedExpandIcon\n  }, listNode);\n});\nvar SubMenu = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var eventKey = props.eventKey,\n    children = props.children;\n  var connectedKeyPath = useFullPath(eventKey);\n  var childList = parseChildren(children, connectedKeyPath);\n\n  // ==================== Record KeyPath ====================\n  var measure = useMeasure();\n\n  // eslint-disable-next-line consistent-return\n  React.useEffect(function () {\n    if (measure) {\n      measure.registerPath(eventKey, connectedKeyPath);\n      return function () {\n        measure.unregisterPath(eventKey, connectedKeyPath);\n      };\n    }\n  }, [connectedKeyPath]);\n  var renderNode;\n\n  // ======================== Render ========================\n  if (measure) {\n    renderNode = childList;\n  } else {\n    renderNode = /*#__PURE__*/React.createElement(InternalSubMenu, _extends({\n      ref: ref\n    }, props), childList);\n  }\n  return /*#__PURE__*/React.createElement(PathTrackerContext.Provider, {\n    value: connectedKeyPath\n  }, renderNode);\n});\nif (process.env.NODE_ENV !== 'production') {\n  SubMenu.displayName = 'SubMenu';\n}\nexport default SubMenu;", "import * as React from 'react';\nimport classNames from 'classnames';\nimport { MenuContext } from \"./context/MenuContext\";\nimport { useMeasure } from \"./context/PathContext\";\nexport default function Divider(_ref) {\n  var className = _ref.className,\n    style = _ref.style;\n  var _React$useContext = React.useContext(MenuContext),\n    prefixCls = _React$useContext.prefixCls;\n  var measure = useMeasure();\n  if (measure) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"li\", {\n    role: \"separator\",\n    className: classNames(\"\".concat(prefixCls, \"-item-divider\"), className),\n    style: style\n  });\n}", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"className\", \"title\", \"eventKey\", \"children\"];\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport * as React from 'react';\nimport { MenuContext } from \"./context/MenuContext\";\nimport { useFullPath, useMeasure } from \"./context/PathContext\";\nimport { parseChildren } from \"./utils/commonUtil\";\nvar InternalMenuItemGroup = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var className = props.className,\n    title = props.title,\n    eventKey = props.eventKey,\n    children = props.children,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var _React$useContext = React.useContext(MenuContext),\n    prefixCls = _React$useContext.prefixCls;\n  var groupPrefixCls = \"\".concat(prefixCls, \"-item-group\");\n  return /*#__PURE__*/React.createElement(\"li\", _extends({\n    ref: ref,\n    role: \"presentation\"\n  }, restProps, {\n    onClick: function onClick(e) {\n      return e.stopPropagation();\n    },\n    className: classNames(groupPrefixCls, className)\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    role: \"presentation\",\n    className: \"\".concat(groupPrefixCls, \"-title\"),\n    title: typeof title === 'string' ? title : undefined\n  }, title), /*#__PURE__*/React.createElement(\"ul\", {\n    role: \"group\",\n    className: \"\".concat(groupPrefixCls, \"-list\")\n  }, children));\n});\nvar MenuItemGroup = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var eventKey = props.eventKey,\n    children = props.children;\n  var connectedKeyPath = useFullPath(eventKey);\n  var childList = parseChildren(children, connectedKeyPath);\n  var measure = useMeasure();\n  if (measure) {\n    return childList;\n  }\n  return /*#__PURE__*/React.createElement(InternalMenuItemGroup, _extends({\n    ref: ref\n  }, omit(props, ['warnKey'])), childList);\n});\nif (process.env.NODE_ENV !== 'production') {\n  MenuItemGroup.displayName = 'MenuItemGroup';\n}\nexport default MenuItemGroup;", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar _excluded = [\"label\", \"children\", \"key\", \"type\", \"extra\"];\nimport * as React from 'react';\nimport Divider from \"../Divider\";\nimport MenuItem from \"../MenuItem\";\nimport MenuItemGroup from \"../MenuItemGroup\";\nimport SubMenu from \"../SubMenu\";\nimport { parseChildren } from \"./commonUtil\";\nfunction convertItemsToNodes(list, components, prefixCls) {\n  var MergedMenuItem = components.item,\n    MergedMenuItemGroup = components.group,\n    MergedSubMenu = components.submenu,\n    MergedDivider = components.divider;\n  return (list || []).map(function (opt, index) {\n    if (opt && _typeof(opt) === 'object') {\n      var _ref = opt,\n        label = _ref.label,\n        children = _ref.children,\n        key = _ref.key,\n        type = _ref.type,\n        extra = _ref.extra,\n        restProps = _objectWithoutProperties(_ref, _excluded);\n      var mergedKey = key !== null && key !== void 0 ? key : \"tmp-\".concat(index);\n\n      // MenuItemGroup & SubMenuItem\n      if (children || type === 'group') {\n        if (type === 'group') {\n          // Group\n          return /*#__PURE__*/React.createElement(MergedMenuItemGroup, _extends({\n            key: mergedKey\n          }, restProps, {\n            title: label\n          }), convertItemsToNodes(children, components, prefixCls));\n        }\n\n        // Sub Menu\n        return /*#__PURE__*/React.createElement(MergedSubMenu, _extends({\n          key: mergedKey\n        }, restProps, {\n          title: label\n        }), convertItemsToNodes(children, components, prefixCls));\n      }\n\n      // MenuItem & Divider\n      if (type === 'divider') {\n        return /*#__PURE__*/React.createElement(MergedDivider, _extends({\n          key: mergedKey\n        }, restProps));\n      }\n      return /*#__PURE__*/React.createElement(MergedMenuItem, _extends({\n        key: mergedKey\n      }, restProps, {\n        extra: extra\n      }), label, (!!extra || extra === 0) && /*#__PURE__*/React.createElement(\"span\", {\n        className: \"\".concat(prefixCls, \"-item-extra\")\n      }, extra));\n    }\n    return null;\n  }).filter(function (opt) {\n    return opt;\n  });\n}\nexport function parseItems(children, items, keyPath, components, prefixCls) {\n  var childNodes = children;\n  var mergedComponents = _objectSpread({\n    divider: Divider,\n    item: MenuItem,\n    group: MenuItemGroup,\n    submenu: SubMenu\n  }, components);\n  if (items) {\n    childNodes = convertItemsToNodes(items, mergedComponents, prefixCls);\n  }\n  return parseChildren(childNodes, keyPath);\n}", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"rootClassName\", \"style\", \"className\", \"tabIndex\", \"items\", \"children\", \"direction\", \"id\", \"mode\", \"inlineCollapsed\", \"disabled\", \"disabledOverflow\", \"subMenuOpenDelay\", \"subMenuCloseDelay\", \"forceSubMenuRender\", \"defaultOpenKeys\", \"openKeys\", \"activeKey\", \"defaultActiveFirst\", \"selectable\", \"multiple\", \"defaultSelectedKeys\", \"selectedKeys\", \"onSelect\", \"onDeselect\", \"inlineIndent\", \"motion\", \"defaultMotions\", \"triggerSubMenuAction\", \"builtinPlacements\", \"itemIcon\", \"expandIcon\", \"overflowedIndicator\", \"overflowedIndicatorPopupClassName\", \"getPopupContainer\", \"onClick\", \"onOpenChange\", \"onKeyDown\", \"openAnimation\", \"openTransitionName\", \"_internalRenderMenuItem\", \"_internalRenderSubMenuItem\", \"_internalComponents\"];\nimport classNames from 'classnames';\nimport Overflow from 'rc-overflow';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport isEqual from \"rc-util/es/isEqual\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport { useImperativeHandle } from 'react';\nimport { flushSync } from 'react-dom';\nimport { IdContext } from \"./context/IdContext\";\nimport MenuContextProvider from \"./context/MenuContext\";\nimport { PathRegisterContext, PathUserContext } from \"./context/PathContext\";\nimport PrivateContext from \"./context/PrivateContext\";\nimport { getFocusableElements, refreshElements, useAccessibility } from \"./hooks/useAccessibility\";\nimport useKeyRecords, { OVERFLOW_KEY } from \"./hooks/useKeyRecords\";\nimport useMemoCallback from \"./hooks/useMemoCallback\";\nimport useUUID from \"./hooks/useUUID\";\nimport MenuItem from \"./MenuItem\";\nimport SubMenu from \"./SubMenu\";\nimport { parseItems } from \"./utils/nodeUtil\";\nimport { warnItemProp } from \"./utils/warnUtil\";\n\n/**\n * Menu modify after refactor:\n * ## Add\n * - disabled\n *\n * ## Remove\n * - openTransitionName\n * - openAnimation\n * - onDestroy\n * - siderCollapsed: Seems antd do not use this prop (Need test in antd)\n * - collapsedWidth: Seems this logic should be handle by antd Layout.Sider\n */\n\n// optimize for render\nvar EMPTY_LIST = [];\nvar Menu = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _childList$;\n  var _ref = props,\n    _ref$prefixCls = _ref.prefixCls,\n    prefixCls = _ref$prefixCls === void 0 ? 'rc-menu' : _ref$prefixCls,\n    rootClassName = _ref.rootClassName,\n    style = _ref.style,\n    className = _ref.className,\n    _ref$tabIndex = _ref.tabIndex,\n    tabIndex = _ref$tabIndex === void 0 ? 0 : _ref$tabIndex,\n    items = _ref.items,\n    children = _ref.children,\n    direction = _ref.direction,\n    id = _ref.id,\n    _ref$mode = _ref.mode,\n    mode = _ref$mode === void 0 ? 'vertical' : _ref$mode,\n    inlineCollapsed = _ref.inlineCollapsed,\n    disabled = _ref.disabled,\n    disabledOverflow = _ref.disabledOverflow,\n    _ref$subMenuOpenDelay = _ref.subMenuOpenDelay,\n    subMenuOpenDelay = _ref$subMenuOpenDelay === void 0 ? 0.1 : _ref$subMenuOpenDelay,\n    _ref$subMenuCloseDela = _ref.subMenuCloseDelay,\n    subMenuCloseDelay = _ref$subMenuCloseDela === void 0 ? 0.1 : _ref$subMenuCloseDela,\n    forceSubMenuRender = _ref.forceSubMenuRender,\n    defaultOpenKeys = _ref.defaultOpenKeys,\n    openKeys = _ref.openKeys,\n    activeKey = _ref.activeKey,\n    defaultActiveFirst = _ref.defaultActiveFirst,\n    _ref$selectable = _ref.selectable,\n    selectable = _ref$selectable === void 0 ? true : _ref$selectable,\n    _ref$multiple = _ref.multiple,\n    multiple = _ref$multiple === void 0 ? false : _ref$multiple,\n    defaultSelectedKeys = _ref.defaultSelectedKeys,\n    selectedKeys = _ref.selectedKeys,\n    onSelect = _ref.onSelect,\n    onDeselect = _ref.onDeselect,\n    _ref$inlineIndent = _ref.inlineIndent,\n    inlineIndent = _ref$inlineIndent === void 0 ? 24 : _ref$inlineIndent,\n    motion = _ref.motion,\n    defaultMotions = _ref.defaultMotions,\n    _ref$triggerSubMenuAc = _ref.triggerSubMenuAction,\n    triggerSubMenuAction = _ref$triggerSubMenuAc === void 0 ? 'hover' : _ref$triggerSubMenuAc,\n    builtinPlacements = _ref.builtinPlacements,\n    itemIcon = _ref.itemIcon,\n    expandIcon = _ref.expandIcon,\n    _ref$overflowedIndica = _ref.overflowedIndicator,\n    overflowedIndicator = _ref$overflowedIndica === void 0 ? '...' : _ref$overflowedIndica,\n    overflowedIndicatorPopupClassName = _ref.overflowedIndicatorPopupClassName,\n    getPopupContainer = _ref.getPopupContainer,\n    onClick = _ref.onClick,\n    onOpenChange = _ref.onOpenChange,\n    onKeyDown = _ref.onKeyDown,\n    openAnimation = _ref.openAnimation,\n    openTransitionName = _ref.openTransitionName,\n    _internalRenderMenuItem = _ref._internalRenderMenuItem,\n    _internalRenderSubMenuItem = _ref._internalRenderSubMenuItem,\n    _internalComponents = _ref._internalComponents,\n    restProps = _objectWithoutProperties(_ref, _excluded);\n  var _React$useMemo = React.useMemo(function () {\n      return [parseItems(children, items, EMPTY_LIST, _internalComponents, prefixCls), parseItems(children, items, EMPTY_LIST, {}, prefixCls)];\n    }, [children, items, _internalComponents]),\n    _React$useMemo2 = _slicedToArray(_React$useMemo, 2),\n    childList = _React$useMemo2[0],\n    measureChildList = _React$useMemo2[1];\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    mounted = _React$useState2[0],\n    setMounted = _React$useState2[1];\n  var containerRef = React.useRef();\n  var uuid = useUUID(id);\n  var isRtl = direction === 'rtl';\n\n  // ========================= Warn =========================\n  if (process.env.NODE_ENV !== 'production') {\n    warning(!openAnimation && !openTransitionName, '`openAnimation` and `openTransitionName` is removed. Please use `motion` or `defaultMotion` instead.');\n  }\n\n  // ========================= Open =========================\n  var _useMergedState = useMergedState(defaultOpenKeys, {\n      value: openKeys,\n      postState: function postState(keys) {\n        return keys || EMPTY_LIST;\n      }\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    mergedOpenKeys = _useMergedState2[0],\n    setMergedOpenKeys = _useMergedState2[1];\n\n  // React 18 will merge mouse event which means we open key will not sync\n  // ref: https://github.com/ant-design/ant-design/issues/38818\n  var triggerOpenKeys = function triggerOpenKeys(keys) {\n    var forceFlush = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    function doUpdate() {\n      setMergedOpenKeys(keys);\n      onOpenChange === null || onOpenChange === void 0 || onOpenChange(keys);\n    }\n    if (forceFlush) {\n      flushSync(doUpdate);\n    } else {\n      doUpdate();\n    }\n  };\n\n  // >>>>> Cache & Reset open keys when inlineCollapsed changed\n  var _React$useState3 = React.useState(mergedOpenKeys),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    inlineCacheOpenKeys = _React$useState4[0],\n    setInlineCacheOpenKeys = _React$useState4[1];\n  var mountRef = React.useRef(false);\n\n  // ========================= Mode =========================\n  var _React$useMemo3 = React.useMemo(function () {\n      if ((mode === 'inline' || mode === 'vertical') && inlineCollapsed) {\n        return ['vertical', inlineCollapsed];\n      }\n      return [mode, false];\n    }, [mode, inlineCollapsed]),\n    _React$useMemo4 = _slicedToArray(_React$useMemo3, 2),\n    mergedMode = _React$useMemo4[0],\n    mergedInlineCollapsed = _React$useMemo4[1];\n  var isInlineMode = mergedMode === 'inline';\n  var _React$useState5 = React.useState(mergedMode),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    internalMode = _React$useState6[0],\n    setInternalMode = _React$useState6[1];\n  var _React$useState7 = React.useState(mergedInlineCollapsed),\n    _React$useState8 = _slicedToArray(_React$useState7, 2),\n    internalInlineCollapsed = _React$useState8[0],\n    setInternalInlineCollapsed = _React$useState8[1];\n  React.useEffect(function () {\n    setInternalMode(mergedMode);\n    setInternalInlineCollapsed(mergedInlineCollapsed);\n    if (!mountRef.current) {\n      return;\n    }\n    // Synchronously update MergedOpenKeys\n    if (isInlineMode) {\n      setMergedOpenKeys(inlineCacheOpenKeys);\n    } else {\n      // Trigger open event in case its in control\n      triggerOpenKeys(EMPTY_LIST);\n    }\n  }, [mergedMode, mergedInlineCollapsed]);\n\n  // ====================== Responsive ======================\n  var _React$useState9 = React.useState(0),\n    _React$useState10 = _slicedToArray(_React$useState9, 2),\n    lastVisibleIndex = _React$useState10[0],\n    setLastVisibleIndex = _React$useState10[1];\n  var allVisible = lastVisibleIndex >= childList.length - 1 || internalMode !== 'horizontal' || disabledOverflow;\n\n  // Cache\n  React.useEffect(function () {\n    if (isInlineMode) {\n      setInlineCacheOpenKeys(mergedOpenKeys);\n    }\n  }, [mergedOpenKeys]);\n  React.useEffect(function () {\n    mountRef.current = true;\n    return function () {\n      mountRef.current = false;\n    };\n  }, []);\n\n  // ========================= Path =========================\n  var _useKeyRecords = useKeyRecords(),\n    registerPath = _useKeyRecords.registerPath,\n    unregisterPath = _useKeyRecords.unregisterPath,\n    refreshOverflowKeys = _useKeyRecords.refreshOverflowKeys,\n    isSubPathKey = _useKeyRecords.isSubPathKey,\n    getKeyPath = _useKeyRecords.getKeyPath,\n    getKeys = _useKeyRecords.getKeys,\n    getSubPathKeys = _useKeyRecords.getSubPathKeys;\n  var registerPathContext = React.useMemo(function () {\n    return {\n      registerPath: registerPath,\n      unregisterPath: unregisterPath\n    };\n  }, [registerPath, unregisterPath]);\n  var pathUserContext = React.useMemo(function () {\n    return {\n      isSubPathKey: isSubPathKey\n    };\n  }, [isSubPathKey]);\n  React.useEffect(function () {\n    refreshOverflowKeys(allVisible ? EMPTY_LIST : childList.slice(lastVisibleIndex + 1).map(function (child) {\n      return child.key;\n    }));\n  }, [lastVisibleIndex, allVisible]);\n\n  // ======================== Active ========================\n  var _useMergedState3 = useMergedState(activeKey || defaultActiveFirst && ((_childList$ = childList[0]) === null || _childList$ === void 0 ? void 0 : _childList$.key), {\n      value: activeKey\n    }),\n    _useMergedState4 = _slicedToArray(_useMergedState3, 2),\n    mergedActiveKey = _useMergedState4[0],\n    setMergedActiveKey = _useMergedState4[1];\n  var onActive = useMemoCallback(function (key) {\n    setMergedActiveKey(key);\n  });\n  var onInactive = useMemoCallback(function () {\n    setMergedActiveKey(undefined);\n  });\n  useImperativeHandle(ref, function () {\n    return {\n      list: containerRef.current,\n      focus: function focus(options) {\n        var _childList$find;\n        var keys = getKeys();\n        var _refreshElements = refreshElements(keys, uuid),\n          elements = _refreshElements.elements,\n          key2element = _refreshElements.key2element,\n          element2key = _refreshElements.element2key;\n        var focusableElements = getFocusableElements(containerRef.current, elements);\n        var shouldFocusKey = mergedActiveKey !== null && mergedActiveKey !== void 0 ? mergedActiveKey : focusableElements[0] ? element2key.get(focusableElements[0]) : (_childList$find = childList.find(function (node) {\n          return !node.props.disabled;\n        })) === null || _childList$find === void 0 ? void 0 : _childList$find.key;\n        var elementToFocus = key2element.get(shouldFocusKey);\n        if (shouldFocusKey && elementToFocus) {\n          var _elementToFocus$focus;\n          elementToFocus === null || elementToFocus === void 0 || (_elementToFocus$focus = elementToFocus.focus) === null || _elementToFocus$focus === void 0 || _elementToFocus$focus.call(elementToFocus, options);\n        }\n      }\n    };\n  });\n\n  // ======================== Select ========================\n  // >>>>> Select keys\n  var _useMergedState5 = useMergedState(defaultSelectedKeys || [], {\n      value: selectedKeys,\n      // Legacy convert key to array\n      postState: function postState(keys) {\n        if (Array.isArray(keys)) {\n          return keys;\n        }\n        if (keys === null || keys === undefined) {\n          return EMPTY_LIST;\n        }\n        return [keys];\n      }\n    }),\n    _useMergedState6 = _slicedToArray(_useMergedState5, 2),\n    mergedSelectKeys = _useMergedState6[0],\n    setMergedSelectKeys = _useMergedState6[1];\n\n  // >>>>> Trigger select\n  var triggerSelection = function triggerSelection(info) {\n    if (selectable) {\n      // Insert or Remove\n      var targetKey = info.key;\n      var exist = mergedSelectKeys.includes(targetKey);\n      var newSelectKeys;\n      if (multiple) {\n        if (exist) {\n          newSelectKeys = mergedSelectKeys.filter(function (key) {\n            return key !== targetKey;\n          });\n        } else {\n          newSelectKeys = [].concat(_toConsumableArray(mergedSelectKeys), [targetKey]);\n        }\n      } else {\n        newSelectKeys = [targetKey];\n      }\n      setMergedSelectKeys(newSelectKeys);\n\n      // Trigger event\n      var selectInfo = _objectSpread(_objectSpread({}, info), {}, {\n        selectedKeys: newSelectKeys\n      });\n      if (exist) {\n        onDeselect === null || onDeselect === void 0 || onDeselect(selectInfo);\n      } else {\n        onSelect === null || onSelect === void 0 || onSelect(selectInfo);\n      }\n    }\n\n    // Whatever selectable, always close it\n    if (!multiple && mergedOpenKeys.length && internalMode !== 'inline') {\n      triggerOpenKeys(EMPTY_LIST);\n    }\n  };\n\n  // ========================= Open =========================\n  /**\n   * Click for item. SubMenu do not have selection status\n   */\n  var onInternalClick = useMemoCallback(function (info) {\n    onClick === null || onClick === void 0 || onClick(warnItemProp(info));\n    triggerSelection(info);\n  });\n  var onInternalOpenChange = useMemoCallback(function (key, open) {\n    var newOpenKeys = mergedOpenKeys.filter(function (k) {\n      return k !== key;\n    });\n    if (open) {\n      newOpenKeys.push(key);\n    } else if (internalMode !== 'inline') {\n      // We need find all related popup to close\n      var subPathKeys = getSubPathKeys(key);\n      newOpenKeys = newOpenKeys.filter(function (k) {\n        return !subPathKeys.has(k);\n      });\n    }\n    if (!isEqual(mergedOpenKeys, newOpenKeys, true)) {\n      triggerOpenKeys(newOpenKeys, true);\n    }\n  });\n\n  // ==================== Accessibility =====================\n  var triggerAccessibilityOpen = function triggerAccessibilityOpen(key, open) {\n    var nextOpen = open !== null && open !== void 0 ? open : !mergedOpenKeys.includes(key);\n    onInternalOpenChange(key, nextOpen);\n  };\n  var onInternalKeyDown = useAccessibility(internalMode, mergedActiveKey, isRtl, uuid, containerRef, getKeys, getKeyPath, setMergedActiveKey, triggerAccessibilityOpen, onKeyDown);\n\n  // ======================== Effect ========================\n  React.useEffect(function () {\n    setMounted(true);\n  }, []);\n\n  // ======================= Context ========================\n  var privateContext = React.useMemo(function () {\n    return {\n      _internalRenderMenuItem: _internalRenderMenuItem,\n      _internalRenderSubMenuItem: _internalRenderSubMenuItem\n    };\n  }, [_internalRenderMenuItem, _internalRenderSubMenuItem]);\n\n  // ======================== Render ========================\n\n  // >>>>> Children\n  var wrappedChildList = internalMode !== 'horizontal' || disabledOverflow ? childList :\n  // Need wrap for overflow dropdown that do not response for open\n  childList.map(function (child, index) {\n    return (\n      /*#__PURE__*/\n      // Always wrap provider to avoid sub node re-mount\n      React.createElement(MenuContextProvider, {\n        key: child.key,\n        overflowDisabled: index > lastVisibleIndex\n      }, child)\n    );\n  });\n\n  // >>>>> Container\n  var container = /*#__PURE__*/React.createElement(Overflow, _extends({\n    id: id,\n    ref: containerRef,\n    prefixCls: \"\".concat(prefixCls, \"-overflow\"),\n    component: \"ul\",\n    itemComponent: MenuItem,\n    className: classNames(prefixCls, \"\".concat(prefixCls, \"-root\"), \"\".concat(prefixCls, \"-\").concat(internalMode), className, _defineProperty(_defineProperty({}, \"\".concat(prefixCls, \"-inline-collapsed\"), internalInlineCollapsed), \"\".concat(prefixCls, \"-rtl\"), isRtl), rootClassName),\n    dir: direction,\n    style: style,\n    role: \"menu\",\n    tabIndex: tabIndex,\n    data: wrappedChildList,\n    renderRawItem: function renderRawItem(node) {\n      return node;\n    },\n    renderRawRest: function renderRawRest(omitItems) {\n      // We use origin list since wrapped list use context to prevent open\n      var len = omitItems.length;\n      var originOmitItems = len ? childList.slice(-len) : null;\n      return /*#__PURE__*/React.createElement(SubMenu, {\n        eventKey: OVERFLOW_KEY,\n        title: overflowedIndicator,\n        disabled: allVisible,\n        internalPopupClose: len === 0,\n        popupClassName: overflowedIndicatorPopupClassName\n      }, originOmitItems);\n    },\n    maxCount: internalMode !== 'horizontal' || disabledOverflow ? Overflow.INVALIDATE : Overflow.RESPONSIVE,\n    ssr: \"full\",\n    \"data-menu-list\": true,\n    onVisibleChange: function onVisibleChange(newLastIndex) {\n      setLastVisibleIndex(newLastIndex);\n    },\n    onKeyDown: onInternalKeyDown\n  }, restProps));\n\n  // >>>>> Render\n  return /*#__PURE__*/React.createElement(PrivateContext.Provider, {\n    value: privateContext\n  }, /*#__PURE__*/React.createElement(IdContext.Provider, {\n    value: uuid\n  }, /*#__PURE__*/React.createElement(MenuContextProvider, {\n    prefixCls: prefixCls,\n    rootClassName: rootClassName,\n    mode: internalMode,\n    openKeys: mergedOpenKeys,\n    rtl: isRtl\n    // Disabled\n    ,\n    disabled: disabled\n    // Motion\n    ,\n    motion: mounted ? motion : null,\n    defaultMotions: mounted ? defaultMotions : null\n    // Active\n    ,\n    activeKey: mergedActiveKey,\n    onActive: onActive,\n    onInactive: onInactive\n    // Selection\n    ,\n    selectedKeys: mergedSelectKeys\n    // Level\n    ,\n    inlineIndent: inlineIndent\n    // Popup\n    ,\n    subMenuOpenDelay: subMenuOpenDelay,\n    subMenuCloseDelay: subMenuCloseDelay,\n    forceSubMenuRender: forceSubMenuRender,\n    builtinPlacements: builtinPlacements,\n    triggerSubMenuAction: triggerSubMenuAction,\n    getPopupContainer: getPopupContainer\n    // Icon\n    ,\n    itemIcon: itemIcon,\n    expandIcon: expandIcon\n    // Events\n    ,\n    onItemClick: onInternalClick,\n    onOpenChange: onInternalOpenChange\n  }, /*#__PURE__*/React.createElement(PathUserContext.Provider, {\n    value: pathUserContext\n  }, container), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'none'\n    },\n    \"aria-hidden\": true\n  }, /*#__PURE__*/React.createElement(PathRegisterContext.Provider, {\n    value: registerPathContext\n  }, measureChildList)))));\n});\nexport default Menu;", "import Menu from \"./Menu\";\nimport MenuItem from \"./MenuItem\";\nimport SubMenu from \"./SubMenu\";\nimport MenuItemGroup from \"./MenuItemGroup\";\nimport { useFullPath } from \"./context/PathContext\";\nimport Divider from \"./Divider\";\nexport { SubMenu, MenuItem as Item, MenuItem, MenuItemGroup, MenuItemGroup as ItemGroup, Divider, /** @private Only used for antd internal. Do not use in your production. */\nuseFullPath };\nvar ExportMenu = Menu;\nExportMenu.Item = MenuItem;\nExportMenu.SubMenu = SubMenu;\nExportMenu.ItemGroup = MenuItemGroup;\nExportMenu.Divider = Divider;\nexport default ExportMenu;", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"invalidate\", \"item\", \"renderItem\", \"responsive\", \"responsiveDisabled\", \"registerSize\", \"itemKey\", \"className\", \"style\", \"children\", \"display\", \"order\", \"component\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\n// Use shared variable to save bundle size\nvar UNDEFINED = undefined;\nfunction InternalItem(props, ref) {\n  var prefixCls = props.prefixCls,\n    invalidate = props.invalidate,\n    item = props.item,\n    renderItem = props.renderItem,\n    responsive = props.responsive,\n    responsiveDisabled = props.responsiveDisabled,\n    registerSize = props.registerSize,\n    itemKey = props.itemKey,\n    className = props.className,\n    style = props.style,\n    children = props.children,\n    display = props.display,\n    order = props.order,\n    _props$component = props.component,\n    Component = _props$component === void 0 ? 'div' : _props$component,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var mergedHidden = responsive && !display;\n\n  // ================================ Effect ================================\n  function internalRegisterSize(width) {\n    registerSize(itemKey, width);\n  }\n  React.useEffect(function () {\n    return function () {\n      internalRegisterSize(null);\n    };\n  }, []);\n\n  // ================================ Render ================================\n  var childNode = renderItem && item !== UNDEFINED ? renderItem(item, {\n    index: order\n  }) : children;\n  var overflowStyle;\n  if (!invalidate) {\n    overflowStyle = {\n      opacity: mergedHidden ? 0 : 1,\n      height: mergedHidden ? 0 : UNDEFINED,\n      overflowY: mergedHidden ? 'hidden' : UNDEFINED,\n      order: responsive ? order : UNDEFINED,\n      pointerEvents: mergedHidden ? 'none' : UNDEFINED,\n      position: mergedHidden ? 'absolute' : UNDEFINED\n    };\n  }\n  var overflowProps = {};\n  if (mergedHidden) {\n    overflowProps['aria-hidden'] = true;\n  }\n  var itemNode = /*#__PURE__*/React.createElement(Component, _extends({\n    className: classNames(!invalidate && prefixCls, className),\n    style: _objectSpread(_objectSpread({}, overflowStyle), style)\n  }, overflowProps, restProps, {\n    ref: ref\n  }), childNode);\n  if (responsive) {\n    itemNode = /*#__PURE__*/React.createElement(ResizeObserver, {\n      onResize: function onResize(_ref) {\n        var offsetWidth = _ref.offsetWidth;\n        internalRegisterSize(offsetWidth);\n      },\n      disabled: responsiveDisabled\n    }, itemNode);\n  }\n  return itemNode;\n}\nvar Item = /*#__PURE__*/React.forwardRef(InternalItem);\nItem.displayName = 'Item';\nexport default Item;", "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport * as React from 'react';\nimport { unstable_batchedUpdates } from 'react-dom';\nimport channelUpdate from \"./channelUpdate\";\n/**\n * Batcher for record any `useEffectState` need update.\n */\nexport function useBatcher() {\n  // Updater Trigger\n  var updateFuncRef = React.useRef(null);\n\n  // Notify update\n  var notifyEffectUpdate = function notifyEffectUpdate(callback) {\n    if (!updateFuncRef.current) {\n      updateFuncRef.current = [];\n      channelUpdate(function () {\n        unstable_batchedUpdates(function () {\n          updateFuncRef.current.forEach(function (fn) {\n            fn();\n          });\n          updateFuncRef.current = null;\n        });\n      });\n    }\n    updateFuncRef.current.push(callback);\n  };\n  return notifyEffectUpdate;\n}\n\n/**\n * Trigger state update by `useLayoutEffect` to save perf.\n */\nexport default function useEffectState(notifyEffectUpdate, defaultValue) {\n  // Value\n  var _React$useState = React.useState(defaultValue),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    stateValue = _React$useState2[0],\n    setStateValue = _React$useState2[1];\n\n  // Set State\n  var setEffectVal = useEvent(function (nextValue) {\n    notifyEffectUpdate(function () {\n      setStateValue(nextValue);\n    });\n  });\n  return [stateValue, setEffectVal];\n}", "import raf from \"rc-util/es/raf\";\nexport default function channelUpdate(callback) {\n  if (typeof MessageChannel === 'undefined') {\n    raf(callback);\n  } else {\n    var channel = new MessageChannel();\n    channel.port1.onmessage = function () {\n      return callback();\n    };\n    channel.port2.postMessage(undefined);\n  }\n}", "import React from 'react';\nexport var OverflowContext = /*#__PURE__*/React.createContext(null);", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"component\"],\n  _excluded2 = [\"className\"],\n  _excluded3 = [\"className\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport Item from \"./Item\";\nimport { OverflowContext } from \"./context\";\nvar InternalRawItem = function InternalRawItem(props, ref) {\n  var context = React.useContext(OverflowContext);\n\n  // Render directly when context not provided\n  if (!context) {\n    var _props$component = props.component,\n      Component = _props$component === void 0 ? 'div' : _props$component,\n      _restProps = _objectWithoutProperties(props, _excluded);\n    return /*#__PURE__*/React.createElement(Component, _extends({}, _restProps, {\n      ref: ref\n    }));\n  }\n  var contextClassName = context.className,\n    restContext = _objectWithoutProperties(context, _excluded2);\n  var className = props.className,\n    restProps = _objectWithoutProperties(props, _excluded3);\n\n  // Do not pass context to sub item to avoid multiple measure\n  return /*#__PURE__*/React.createElement(OverflowContext.Provider, {\n    value: null\n  }, /*#__PURE__*/React.createElement(Item, _extends({\n    ref: ref,\n    className: classNames(contextClassName, className)\n  }, restContext, restProps)));\n};\nvar RawItem = /*#__PURE__*/React.forwardRef(InternalRawItem);\nRawItem.displayName = 'RawItem';\nexport default RawItem;", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"data\", \"renderItem\", \"renderRawItem\", \"itemKey\", \"itemWidth\", \"ssr\", \"style\", \"className\", \"maxCount\", \"renderRest\", \"renderRawRest\", \"suffix\", \"component\", \"itemComponent\", \"onVisibleChange\"];\nimport * as React from 'react';\nimport { useState, useMemo, useCallback } from 'react';\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport Item from \"./Item\";\nimport useEffectState, { useBatcher } from \"./hooks/useEffectState\";\nimport RawItem from \"./RawItem\";\nimport { OverflowContext } from \"./context\";\nvar RESPONSIVE = 'responsive';\nvar INVALIDATE = 'invalidate';\nexport { OverflowContext } from \"./context\";\nfunction defaultRenderRest(omittedItems) {\n  return \"+ \".concat(omittedItems.length, \" ...\");\n}\nfunction Overflow(props, ref) {\n  var _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-overflow' : _props$prefixCls,\n    _props$data = props.data,\n    data = _props$data === void 0 ? [] : _props$data,\n    renderItem = props.renderItem,\n    renderRawItem = props.renderRawItem,\n    itemKey = props.itemKey,\n    _props$itemWidth = props.itemWidth,\n    itemWidth = _props$itemWidth === void 0 ? 10 : _props$itemWidth,\n    ssr = props.ssr,\n    style = props.style,\n    className = props.className,\n    maxCount = props.maxCount,\n    renderRest = props.renderRest,\n    renderRawRest = props.renderRawRest,\n    suffix = props.suffix,\n    _props$component = props.component,\n    Component = _props$component === void 0 ? 'div' : _props$component,\n    itemComponent = props.itemComponent,\n    onVisibleChange = props.onVisibleChange,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var fullySSR = ssr === 'full';\n  var notifyEffectUpdate = useBatcher();\n  var _useEffectState = useEffectState(notifyEffectUpdate, null),\n    _useEffectState2 = _slicedToArray(_useEffectState, 2),\n    containerWidth = _useEffectState2[0],\n    setContainerWidth = _useEffectState2[1];\n  var mergedContainerWidth = containerWidth || 0;\n  var _useEffectState3 = useEffectState(notifyEffectUpdate, new Map()),\n    _useEffectState4 = _slicedToArray(_useEffectState3, 2),\n    itemWidths = _useEffectState4[0],\n    setItemWidths = _useEffectState4[1];\n  var _useEffectState5 = useEffectState(notifyEffectUpdate, 0),\n    _useEffectState6 = _slicedToArray(_useEffectState5, 2),\n    prevRestWidth = _useEffectState6[0],\n    setPrevRestWidth = _useEffectState6[1];\n  var _useEffectState7 = useEffectState(notifyEffectUpdate, 0),\n    _useEffectState8 = _slicedToArray(_useEffectState7, 2),\n    restWidth = _useEffectState8[0],\n    setRestWidth = _useEffectState8[1];\n  var _useEffectState9 = useEffectState(notifyEffectUpdate, 0),\n    _useEffectState10 = _slicedToArray(_useEffectState9, 2),\n    suffixWidth = _useEffectState10[0],\n    setSuffixWidth = _useEffectState10[1];\n  var _useState = useState(null),\n    _useState2 = _slicedToArray(_useState, 2),\n    suffixFixedStart = _useState2[0],\n    setSuffixFixedStart = _useState2[1];\n  var _useState3 = useState(null),\n    _useState4 = _slicedToArray(_useState3, 2),\n    displayCount = _useState4[0],\n    setDisplayCount = _useState4[1];\n  var mergedDisplayCount = React.useMemo(function () {\n    if (displayCount === null && fullySSR) {\n      return Number.MAX_SAFE_INTEGER;\n    }\n    return displayCount || 0;\n  }, [displayCount, containerWidth]);\n  var _useState5 = useState(false),\n    _useState6 = _slicedToArray(_useState5, 2),\n    restReady = _useState6[0],\n    setRestReady = _useState6[1];\n  var itemPrefixCls = \"\".concat(prefixCls, \"-item\");\n\n  // Always use the max width to avoid blink\n  var mergedRestWidth = Math.max(prevRestWidth, restWidth);\n\n  // ================================= Data =================================\n  var isResponsive = maxCount === RESPONSIVE;\n  var shouldResponsive = data.length && isResponsive;\n  var invalidate = maxCount === INVALIDATE;\n\n  /**\n   * When is `responsive`, we will always render rest node to get the real width of it for calculation\n   */\n  var showRest = shouldResponsive || typeof maxCount === 'number' && data.length > maxCount;\n  var mergedData = useMemo(function () {\n    var items = data;\n    if (shouldResponsive) {\n      if (containerWidth === null && fullySSR) {\n        items = data;\n      } else {\n        items = data.slice(0, Math.min(data.length, mergedContainerWidth / itemWidth));\n      }\n    } else if (typeof maxCount === 'number') {\n      items = data.slice(0, maxCount);\n    }\n    return items;\n  }, [data, itemWidth, containerWidth, maxCount, shouldResponsive]);\n  var omittedItems = useMemo(function () {\n    if (shouldResponsive) {\n      return data.slice(mergedDisplayCount + 1);\n    }\n    return data.slice(mergedData.length);\n  }, [data, mergedData, shouldResponsive, mergedDisplayCount]);\n\n  // ================================= Item =================================\n  var getKey = useCallback(function (item, index) {\n    var _ref;\n    if (typeof itemKey === 'function') {\n      return itemKey(item);\n    }\n    return (_ref = itemKey && (item === null || item === void 0 ? void 0 : item[itemKey])) !== null && _ref !== void 0 ? _ref : index;\n  }, [itemKey]);\n  var mergedRenderItem = useCallback(renderItem || function (item) {\n    return item;\n  }, [renderItem]);\n  function updateDisplayCount(count, suffixFixedStartVal, notReady) {\n    // React 18 will sync render even when the value is same in some case.\n    // We take `mergedData` as deps which may cause dead loop if it's dynamic generate.\n    // ref: https://github.com/ant-design/ant-design/issues/36559\n    if (displayCount === count && (suffixFixedStartVal === undefined || suffixFixedStartVal === suffixFixedStart)) {\n      return;\n    }\n    setDisplayCount(count);\n    if (!notReady) {\n      setRestReady(count < data.length - 1);\n      onVisibleChange === null || onVisibleChange === void 0 || onVisibleChange(count);\n    }\n    if (suffixFixedStartVal !== undefined) {\n      setSuffixFixedStart(suffixFixedStartVal);\n    }\n  }\n\n  // ================================= Size =================================\n  function onOverflowResize(_, element) {\n    setContainerWidth(element.clientWidth);\n  }\n  function registerSize(key, width) {\n    setItemWidths(function (origin) {\n      var clone = new Map(origin);\n      if (width === null) {\n        clone.delete(key);\n      } else {\n        clone.set(key, width);\n      }\n      return clone;\n    });\n  }\n  function registerOverflowSize(_, width) {\n    setRestWidth(width);\n    setPrevRestWidth(restWidth);\n  }\n  function registerSuffixSize(_, width) {\n    setSuffixWidth(width);\n  }\n\n  // ================================ Effect ================================\n  function getItemWidth(index) {\n    return itemWidths.get(getKey(mergedData[index], index));\n  }\n  useLayoutEffect(function () {\n    if (mergedContainerWidth && typeof mergedRestWidth === 'number' && mergedData) {\n      var totalWidth = suffixWidth;\n      var len = mergedData.length;\n      var lastIndex = len - 1;\n\n      // When data count change to 0, reset this since not loop will reach\n      if (!len) {\n        updateDisplayCount(0, null);\n        return;\n      }\n      for (var i = 0; i < len; i += 1) {\n        var currentItemWidth = getItemWidth(i);\n\n        // Fully will always render\n        if (fullySSR) {\n          currentItemWidth = currentItemWidth || 0;\n        }\n\n        // Break since data not ready\n        if (currentItemWidth === undefined) {\n          updateDisplayCount(i - 1, undefined, true);\n          break;\n        }\n\n        // Find best match\n        totalWidth += currentItemWidth;\n        if (\n        // Only one means `totalWidth` is the final width\n        lastIndex === 0 && totalWidth <= mergedContainerWidth ||\n        // Last two width will be the final width\n        i === lastIndex - 1 && totalWidth + getItemWidth(lastIndex) <= mergedContainerWidth) {\n          // Additional check if match the end\n          updateDisplayCount(lastIndex, null);\n          break;\n        } else if (totalWidth + mergedRestWidth > mergedContainerWidth) {\n          // Can not hold all the content to show rest\n          updateDisplayCount(i - 1, totalWidth - currentItemWidth - suffixWidth + restWidth);\n          break;\n        }\n      }\n      if (suffix && getItemWidth(0) + suffixWidth > mergedContainerWidth) {\n        setSuffixFixedStart(null);\n      }\n    }\n  }, [mergedContainerWidth, itemWidths, restWidth, suffixWidth, getKey, mergedData]);\n\n  // ================================ Render ================================\n  var displayRest = restReady && !!omittedItems.length;\n  var suffixStyle = {};\n  if (suffixFixedStart !== null && shouldResponsive) {\n    suffixStyle = {\n      position: 'absolute',\n      left: suffixFixedStart,\n      top: 0\n    };\n  }\n  var itemSharedProps = {\n    prefixCls: itemPrefixCls,\n    responsive: shouldResponsive,\n    component: itemComponent,\n    invalidate: invalidate\n  };\n\n  // >>>>> Choice render fun by `renderRawItem`\n  var internalRenderItemNode = renderRawItem ? function (item, index) {\n    var key = getKey(item, index);\n    return /*#__PURE__*/React.createElement(OverflowContext.Provider, {\n      key: key,\n      value: _objectSpread(_objectSpread({}, itemSharedProps), {}, {\n        order: index,\n        item: item,\n        itemKey: key,\n        registerSize: registerSize,\n        display: index <= mergedDisplayCount\n      })\n    }, renderRawItem(item, index));\n  } : function (item, index) {\n    var key = getKey(item, index);\n    return /*#__PURE__*/React.createElement(Item, _extends({}, itemSharedProps, {\n      order: index,\n      key: key,\n      item: item,\n      renderItem: mergedRenderItem,\n      itemKey: key,\n      registerSize: registerSize,\n      display: index <= mergedDisplayCount\n    }));\n  };\n\n  // >>>>> Rest node\n  var restContextProps = {\n    order: displayRest ? mergedDisplayCount : Number.MAX_SAFE_INTEGER,\n    className: \"\".concat(itemPrefixCls, \"-rest\"),\n    registerSize: registerOverflowSize,\n    display: displayRest\n  };\n  var mergedRenderRest = renderRest || defaultRenderRest;\n  var restNode = renderRawRest ? /*#__PURE__*/React.createElement(OverflowContext.Provider, {\n    value: _objectSpread(_objectSpread({}, itemSharedProps), restContextProps)\n  }, renderRawRest(omittedItems)) : /*#__PURE__*/React.createElement(Item, _extends({}, itemSharedProps, restContextProps), typeof mergedRenderRest === 'function' ? mergedRenderRest(omittedItems) : mergedRenderRest);\n  var overflowNode = /*#__PURE__*/React.createElement(Component, _extends({\n    className: classNames(!invalidate && prefixCls, className),\n    style: style,\n    ref: ref\n  }, restProps), mergedData.map(internalRenderItemNode), showRest ? restNode : null, suffix && /*#__PURE__*/React.createElement(Item, _extends({}, itemSharedProps, {\n    responsive: isResponsive,\n    responsiveDisabled: !shouldResponsive,\n    order: mergedDisplayCount,\n    className: \"\".concat(itemPrefixCls, \"-suffix\"),\n    registerSize: registerSuffixSize,\n    display: true,\n    style: suffixStyle\n  }), suffix));\n  return isResponsive ? /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onOverflowResize,\n    disabled: !shouldResponsive\n  }, overflowNode) : overflowNode;\n}\nvar ForwardOverflow = /*#__PURE__*/React.forwardRef(Overflow);\nForwardOverflow.displayName = 'Overflow';\nForwardOverflow.Item = RawItem;\nForwardOverflow.RESPONSIVE = RESPONSIVE;\nForwardOverflow.INVALIDATE = INVALIDATE;\n\n// Convert to generic type\nexport default ForwardOverflow;", "import Overflow from \"./Overflow\";\nexport default Overflow;"], "names": ["props", "ref", "AntdIcon", "A", "icon", "ESC", "KeyCode", "TAB", "forwardRef", "overlay", "arrow", "prefixCls", "overlayNode", "useMemo", "composedRef", "className", "concat", "undefined", "autoAdjustOverflow", "adjustX", "adjustY", "targetOffset", "topLeft", "points", "overflow", "offset", "top", "topRight", "bottomLeft", "bottom", "bottomRight", "_excluded", "Dropdown", "_children$props", "_props$arrow", "_props$prefixCls", "transitionName", "animation", "align", "_props$placement", "placement", "_props$placements", "placements", "getPopupContainer", "showAction", "hideAction", "overlayClassName", "overlayStyle", "visible", "_props$trigger", "trigger", "autoFocus", "children", "onVisibleChange", "otherProps", "_React$useState", "_React$useState2", "triggerVisible", "setTriggerVisible", "mergedVisible", "triggerRef", "overlayRef", "childRef", "current", "handleVisibleChange", "newVisible", "_ref", "focusMenuRef", "handleCloseMenuAndReturnFocus", "_triggerRef$current", "_triggerRef$current$f", "focus", "call", "focusMenu", "_overlayRef$current", "handleKeyDown", "event", "keyCode", "focusResult", "preventDefault", "window", "addEventListener", "raf", "removeEventListener", "useAccessibility", "openClassName", "minOverlayWidthMatchTrigger", "alignPoint", "getMenuElement", "childrenNode", "triggerHideAction", "indexOf", "builtinPlacements", "popupClassName", "popupStyle", "action", "popupPlacement", "popupAlign", "popupTransitionName", "popupAnimation", "popupVisible", "stretch", "popup", "onPopupVisibleChange", "onPopupClick", "e", "onOverlayClick", "slideUpIn", "transform", "transform<PERSON><PERSON>in", "opacity", "slideUpOut", "slideDownIn", "slideDownOut", "slideLeftIn", "slideLeftOut", "slideRightIn", "slideRightOut", "slideMotion", "inKeyframes", "outKeyframes", "initSlideMotion", "token", "motionName", "antCls", "motionCls", "motionDurationMid", "animationTimingFunction", "motionEaseOutQuint", "motionEaseInQuint", "IdContext", "getMenuId", "uuid", "eventKey", "useMenuId", "MenuContext", "InheritableContextProvider", "locked", "restProps", "context", "inheritable<PERSON><PERSON><PERSON><PERSON>", "origin", "target", "clone", "Object", "keys", "for<PERSON>ach", "key", "value", "prev", "next", "isEqual", "Provider", "EmptyList", "PathRegisterContext", "useMeasure", "PathTrackerContext", "useFullPath", "parent<PERSON><PERSON><PERSON><PERSON>", "PathUserContext", "focusable", "node", "includePositive", "arguments", "length", "isVisible", "nodeName", "toLowerCase", "isFocusableElement", "includes", "isContentEditable", "getAttribute", "tabIndexAttr", "tabIndexNum", "Number", "tabIndex", "isNaN", "disabled", "getFocusNodeList", "res", "querySelectorAll", "filter", "child", "unshift", "LEFT", "RIGHT", "UP", "DOWN", "ENTER", "HOME", "END", "ArrowKeys", "getFocusableElements", "container", "elements", "ele", "has", "getNextFocusElement", "parentQueryContainer", "focusMenuElement", "sameLevelFocusableMenuElementList", "count", "focusIndex", "findIndex", "refreshElements", "id", "Set", "key2element", "Map", "element2key", "element", "document", "querySelector", "add", "set", "mode", "active<PERSON><PERSON>", "isRtl", "containerRef", "get<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "triggerActiveKey", "triggerAccessibilityOpen", "originOnKeyDown", "rafRef", "activeRef", "cleanRaf", "cancel", "which", "refreshedElements", "_refreshedElements", "activeElement", "parentElement", "getFocusElement", "get", "focusMenuKey", "offsetObj", "isRootLevel", "_offsets", "parent", "inlineTrigger", "inline", "horizontal", "vertical", "inlineSub", "horizontalSub", "verticalSub", "sibling", "getOffset", "tryFocus", "menuElement", "focusTargetElement", "link", "<PERSON><PERSON><PERSON>", "targetElement", "focusableElements", "findContainerUL", "controlId", "getElementById", "keyP<PERSON>", "parent<PERSON><PERSON>", "parentMenuElement", "PATH_SPLIT", "getPathStr", "join", "OVERFLOW_KEY", "useKeyRecords", "internalForceUpdate", "key2pathRef", "useRef", "path2keyRef", "_React$useState3", "_React$useState4", "overflowKeys", "setOverflowKeys", "updateRef", "destroyRef", "registerPath", "useCallback", "connectedPath", "callback", "Promise", "resolve", "then", "unregisterPath", "delete", "refreshOverflowKeys", "includeOverflow", "fullPath", "split", "isSubPath<PERSON>ey", "pathKeys", "item", "some", "path<PERSON><PERSON>", "getSub<PERSON><PERSON><PERSON><PERSON><PERSON>", "startsWith", "push", "useMemoCallback", "func", "funRef", "_funRef$current", "_len", "args", "Array", "_key", "apply", "uniquePrefix", "Math", "random", "toFixed", "toString", "slice", "internalId", "useActive", "onMouseEnter", "onMouseLeave", "_React$useContext", "onActive", "onInactive", "ret", "active", "domEvent", "useDirectionStyle", "level", "rtl", "inlineIndent", "paddingRight", "paddingLeft", "Icon", "iconNode", "warnItemProp", "restInfo", "defineProperty", "warning", "_excluded2", "_excluded3", "LegacyMenuItem", "_React$Component", "_super", "this", "_this$props", "title", "attribute", "elementRef", "passedProps", "omit", "<PERSON><PERSON>", "InternalMenuItem", "style", "<PERSON><PERSON><PERSON>", "itemIcon", "role", "onClick", "onKeyDown", "onFocus", "domDataId", "onItemClick", "contextDisabled", "overflowDisabled", "contextItemIcon", "<PERSON><PERSON><PERSON><PERSON>", "_internalRenderMenuItem", "itemCls", "legacyMenuItemRef", "mergedDisabled", "mergedEleRef", "connectedKeys", "getEventInfo", "reverse", "mergedItemIcon", "_useActive", "activeProps", "selected", "directionStyle", "optionRoleProps", "renderNode", "component", "info", "isSelected", "MenuItem", "measure", "connectedKeyPath", "InternalSubMenuList", "SubMenuList", "displayName", "parse<PERSON><PERSON><PERSON>n", "toArray", "map", "index", "_eventKey", "_child$props", "cloneProps", "leftTop", "leftBottom", "rightTop", "rightBottom", "placementsRtl", "getMotion", "motion", "defaultMotions", "other", "popupPlacementMap", "PopupTrigger", "popupOffset", "subMenuOpenDelay", "subMenuCloseDelay", "triggerSubMenuAction", "forceSubMenuRender", "rootClassName", "innerVisible", "setInnerVisible", "targetMotion", "targetMotionRef", "mergedMotion", "leavedClassName", "removeOnLeave", "motionAppear", "visibleRef", "mouseEnterDelay", "mouseLeaveDelay", "forceRender", "popupMotion", "fresh", "InlineSubMenuList", "open", "fixedMode", "sameModeRef", "destroy", "<PERSON><PERSON><PERSON><PERSON>", "mergedOpen", "originOnVisibleChanged", "onVisibleChanged", "_ref2", "motionClassName", "motionStyle", "InternalSubMenu", "internalPopupClose", "expandIcon", "onTitleClick", "onTitleMouseEnter", "onTitleMouseLeave", "openKeys", "contextExpandIcon", "onOpenChange", "_internalRenderSubMenuItem", "subMenuPrefixCls", "popupRef", "mergedExpandIcon", "originOpen", "childrenSelected", "childrenActive", "setChildrenActive", "triggerChildrenActive", "newActive", "mergedActive", "onMergedItemClick", "popupId", "expandIconNode", "isOpen", "isSubMenu", "titleNode", "triggerModeRef", "triggerMode", "listNode", "childList", "Divider", "InternalMenuItemGroup", "groupPrefixCls", "stopPropagation", "convertItemsToNodes", "list", "components", "MergedMenuItem", "MergedMenuItemGroup", "group", "MergedSubMenu", "submenu", "MergedDivider", "divider", "opt", "label", "type", "extra", "mergedKey", "parseItems", "items", "childNodes", "mergedComponents", "EMPTY_LIST", "<PERSON><PERSON>", "_childList$", "_ref$prefixCls", "_ref$tabIndex", "direction", "_ref$mode", "inlineCollapsed", "disabledOverflow", "_ref$subMenuOpenDelay", "_ref$subMenuCloseDela", "defaultOpenKeys", "defaultActiveFirst", "_ref$selectable", "selectable", "_ref$multiple", "multiple", "defaultSelectedKeys", "onSelect", "onDeselect", "_ref$inlineIndent", "_ref$triggerSubMenuAc", "_ref$overflowedIndica", "overflowedIndicator", "overflowedIndicatorPopupClassName", "openAnimation", "openTransitionName", "_internalComponents", "_React$useMemo", "_React$useMemo2", "measureChildList", "mounted", "setMounted", "_useMergedState", "useMergedState", "_useMergedState2", "setUUID", "newId", "useUUID", "postState", "mergedOpenKeys", "setMergedOpenKeys", "triggerOpenKeys", "doUpdate", "flushSync", "inlineCacheOpenKeys", "setInlineCacheOpenKeys", "mountRef", "_React$useMemo3", "_React$useMemo4", "mergedMode", "mergedInlineCollapsed", "isInlineMode", "_React$useState5", "_React$useState6", "internalMode", "setInternalMode", "_React$useState7", "_React$useState8", "internalInlineCollapsed", "setInternalInlineCollapsed", "_React$useState9", "_React$useState10", "lastVisibleIndex", "setLastVisibleIndex", "allVisible", "_useKeyRecords", "registerPathContext", "pathUserContext", "_useMergedState3", "_useMergedState4", "mergedActiveKey", "setMergedActiveKey", "useImperativeHandle", "options", "_childList$find", "_elementToFocus$focus", "_refreshElements", "shouldFocus<PERSON>ey", "find", "elementToFocus", "_useMergedState5", "isArray", "_useMergedState6", "mergedSelectKeys", "setMergedSelectKeys", "onInternalClick", "newSelectKeys", "exist", "selectInfo", "triggerSelection", "onInternalOpenChange", "newOpenKeys", "k", "subPath<PERSON><PERSON>s", "onInternalKeyDown", "nextOpen", "privateContext", "wrappedChildList", "itemComponent", "dir", "data", "renderRawItem", "renderRawRest", "omitItems", "len", "originOmitItems", "maxCount", "INVALIDATE", "RESPONSIVE", "ssr", "newLastIndex", "display", "ExportMenu", "SubMenu", "ItemGroup", "UNDEFINED", "InternalItem", "invalidate", "renderItem", "responsive", "responsiveDisabled", "registerSize", "itemKey", "order", "_props$component", "Component", "mergedHidden", "internalRegisterSize", "width", "overflowStyle", "childNode", "height", "overflowY", "pointerEvents", "position", "overflowProps", "itemNode", "onResize", "offsetWidth", "useBatcher", "updateFuncRef", "MessageChannel", "channel", "port1", "onmessage", "port2", "postMessage", "channelUpdate", "unstable_batchedUpdates", "fn", "useEffectState", "notifyEffectUpdate", "defaultValue", "stateValue", "setStateValue", "useEvent", "nextValue", "OverflowContext", "InternalRawItem", "_restProps", "contextClassName", "restContext", "RawItem", "defaultRenderRest", "omittedItems", "Overflow", "_props$data", "_props$itemWidth", "itemWidth", "renderRest", "suffix", "fullySSR", "_useEffectState", "_useEffectState2", "containerWidth", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mergedContainerWidth", "_useEffectState3", "_useEffectState4", "itemWidths", "setItemWidths", "_useEffectState5", "_useEffectState6", "prevRestWidth", "setPrevRestWidth", "_useEffectState7", "_useEffectState8", "restWidth", "setRestWidth", "_useEffectState9", "_useEffectState10", "suffixWidth", "setSuffixWidth", "_useState", "useState", "_useState2", "suffixFixedStart", "setSuffixFixedStart", "_useState3", "_useState4", "displayCount", "setDisplayCount", "mergedDisplayCount", "MAX_SAFE_INTEGER", "_useState5", "_useState6", "restReady", "setRestReady", "itemPrefixCls", "mergedRestWidth", "max", "isResponsive", "shouldResponsive", "showRest", "mergedData", "min", "<PERSON><PERSON><PERSON>", "mergedRenderItem", "updateDisplayCount", "suffixFixedStartVal", "notReady", "getItemWidth", "useLayoutEffect", "totalWidth", "lastIndex", "i", "currentItemWidth", "displayRest", "suffixStyle", "left", "itemSharedProps", "internalRenderItemNode", "restContextProps", "_", "mergedRenderRest", "restNode", "overflowNode", "clientWidth", "ForwardOverflow"], "sourceRoot": ""}