import React, { useEffect, useState } from "react";
import { useAuth } from "../auth/context";
import { navigate } from "gatsby";
import { Card, Typography, Spin, message, Result, Button } from "antd";
import { CheckCircleOutlined, CloseCircleOutlined } from "@ant-design/icons";
import Layout from "../components/layout";
import { graphql } from "gatsby";

const { Title, Text } = Typography;

const TokenLoginPage = ({ data, location }: any) => {
  const { loginWithToken, isAuthenticated, isLoading } = useAuth();
  const [isProcessingToken, setIsProcessingToken] = useState(false);
  const [loginStatus, setLoginStatus] = useState<'processing' | 'success' | 'error' | 'no-token'>('processing');
  const [errorMessage, setErrorMessage] = useState<string>('');

  useEffect(() => {
    // If user is already authenticated, redirect to home
    if (isAuthenticated && !isLoading) {
      navigate("/");
      return;
    }

    const processTokenLogin = async () => {
      // Check if there's a token parameter in the URL
      const params = new URLSearchParams(location.search);
      const token = params.get("token");

      if (!token) {
        setLoginStatus('no-token');
        return;
      }

      try {
        setIsProcessingToken(true);
        setLoginStatus('processing');
        
        await loginWithToken(token);
        
        setLoginStatus('success');
        
        // Redirect to home after a short delay
        setTimeout(() => {
          navigate("/");
        }, 2000);
        
      } catch (error: any) {
        console.error("Token login failed:", error);
        setErrorMessage(error.message || "Token login failed. Please try again.");
        setLoginStatus('error');
      } finally {
        setIsProcessingToken(false);
      }
    };

    if (!isLoading) {
      processTokenLogin();
    }
  }, [location.search, isAuthenticated, isLoading, loginWithToken]);

  const renderContent = () => {
    switch (loginStatus) {
      case 'processing':
        return (
          <Card className="w-full max-w-md mx-auto mt-20">
            <div className="text-center">
              <Spin size="large" />
              <Title level={3} className="mt-4">正在验证登录...</Title>
              <Text type="secondary">请稍候，我们正在验证您的登录凭据</Text>
            </div>
          </Card>
        );

      case 'success':
        return (
          <Card className="w-full max-w-md mx-auto mt-20">
            <Result
              icon={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
              title="登录成功！"
              subTitle="您已成功登录，即将跳转到主页..."
              extra={
                <Button type="primary" onClick={() => navigate("/")}>
                  立即跳转
                </Button>
              }
            />
          </Card>
        );

      case 'error':
        return (
          <Card className="w-full max-w-md mx-auto mt-20">
            <Result
              icon={<CloseCircleOutlined style={{ color: '#ff4d4f' }} />}
              title="登录失败"
              subTitle={errorMessage}
              extra={[
                <Button type="primary" key="retry" onClick={() => window.location.reload()}>
                  重试
                </Button>,
                <Button key="home" onClick={() => navigate("/")}>
                  返回首页
                </Button>
              ]}
            />
          </Card>
        );

      case 'no-token':
        return (
          <Card className="w-full max-w-md mx-auto mt-20">
            <Result
              status="warning"
              title="缺少登录令牌"
              subTitle="URL中未找到有效的登录令牌"
              extra={[
                <Button type="primary" key="login" onClick={() => navigate("/login")}>
                  前往登录页面
                </Button>,
                <Button key="home" onClick={() => navigate("/")}>
                  返回首页
                </Button>
              ]}
            />
          </Card>
        );

      default:
        return null;
    }
  };

  if (isLoading) {
    return (
      <Layout meta={data.site.siteMetadata} title="Token Login" link="/token-login">
        <div className="flex items-center justify-center h-screen">
          <Spin size="large" tip="加载中..." />
        </div>
      </Layout>
    );
  }

  return (
    <Layout meta={data.site.siteMetadata} title="Token Login" link="/token-login">
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        {renderContent()}
      </div>
    </Layout>
  );
};

export const query = graphql`
  query TokenLoginPageQuery {
    site {
      siteMetadata {
        description
        title
      }
    }
  }
`;

export default TokenLoginPage;
