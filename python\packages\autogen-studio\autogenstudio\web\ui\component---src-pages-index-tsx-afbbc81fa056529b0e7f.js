"use strict";(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[245],{2783:function(e,t,a){a.r(t);var n=a(6540),i=a(1155),l=a(588),s=a(226),c=a(9036),r=a(4716);t.default=e=>{let{data:t,location:a}=e;const{loginWithToken:o,isAuthenticated:u,isLoading:h}=(0,s.A)(),{0:m,1:d}=(0,n.useState)(!1);return(0,n.useEffect)(()=>{(async()=>{const e=new URLSearchParams(a.search).get("token");if(e&&!u&&!h)try{d(!0),await o(e);const t=window.location.pathname;window.history.replaceState({},document.title,t)}catch(t){console.error("Token login failed:",t),c.Ay.error("Token login failed. Please try again.")}finally{d(!1)}})()},[a.search,u,h,o]),m?n.createElement(i.A,{meta:t.site.siteMetadata,title:"首页",link:"/"},n.createElement("div",{className:"flex items-center justify-center h-screen"},n.createElement(r.A,{size:"large",tip:"正在登录..."}))):n.createElement(i.A,{meta:t.site.siteMetadata,title:"首页",link:"/"},n.createElement("main",{style:{height:"100%"},className:" h-full "},n.createElement(l.C,null)))}}}]);
//# sourceMappingURL=component---src-pages-index-tsx-afbbc81fa056529b0e7f.js.map