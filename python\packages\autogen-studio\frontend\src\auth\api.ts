import { getServerUrl } from "../components/utils/utils";

export interface User {
  id: string;
  name: string;
  email?: string;
  avatar_url?: string;
  provider?: string;
  roles?: string[];
}

export class AuthAPI {
  private getBaseUrl(): string {
    return getServerUrl();
  }

  private getHeaders(token?: string): HeadersInit {
    const headers: HeadersInit = {
      "Content-Type": "application/json",
    };

    // Use provided token or get from localStorage
    const authToken = token || localStorage.getItem("auth_token");
    if (authToken) {
      headers["Authorization"] = `Bearer ${authToken}`;
    }

    return headers;
  }



  async getCurrentUser(token: string): Promise<User> {
    try {
      const response = await fetch(`${this.getBaseUrl()}/auth/me`, {
        headers: this.getHeaders(token),
      });

      if (response.status === 401) {
        throw new Error("Unauthorized");
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error getting current user:", error);
      throw error;
    }
  }

  async loginWithToken(token: string): Promise<{ token: string; user: User; redirect_url?: string }> {
    try {
      const response = await fetch(`${this.getBaseUrl()}/`, {
        method: "POST",
        headers: this.getHeaders(),
        body: JSON.stringify({ token }),
      });

      const data = await response.json();
      if (!response.ok || !data.status) {
        throw new Error(data.message || "Token login failed");
      }

      return {
        token: data.token || token, // 优先使用后端返回的token，如果没有则使用原始token
        user: data.user,
        redirect_url: data.redirect_url,
      };
    } catch (error) {
      console.error("Error logging in with token:", error);
      throw error;
    }
  }

  async checkAuthType(): Promise<{ type: string }> {
    try {
      const response = await fetch(`${this.getBaseUrl()}/auth/type`, {
        headers: this.getHeaders(),
      });

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error checking auth type:", error);
      return { type: "none" }; // Default to no auth
    }
  }
}

export const authAPI = new AuthAPI();
