#!/usr/bin/env python3
"""
用户显示修复测试脚本

此脚本用于测试修复后的用户显示逻辑：
1. 测试认证类型检查
2. 测试token登录
3. 验证用户状态是否正确显示
4. 测试页面刷新后用户状态保持
"""

import json
import requests
import sys
import time
from typing import Dict, Any


class UserDisplayTester:
    def __init__(self, base_url: str = "http://127.0.0.1:8081"):
        self.base_url = base_url.rstrip('/')
        self.token = None
        
    def test_auth_type(self) -> Dict[str, Any]:
        """测试认证类型检查"""
        print(f"🔍 测试认证类型: {self.base_url}/api/auth/type")
        
        try:
            response = requests.get(
                f"{self.base_url}/api/auth/type",
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            
            data = response.json()
            
            if response.ok:
                print(f"✅ 认证类型检查成功:")
                print(f"   认证类型: {data.get('type')}")
                print(f"   排除路径: {data.get('exclude_paths', [])}")
                return {"success": True, "data": data}
            else:
                print(f"❌ 认证类型检查失败: {data}")
                return {"success": False, "error": data}
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求异常: {e}")
            return {"success": False, "error": str(e)}
    
    def test_token_login(self, token: str) -> Dict[str, Any]:
        """测试token登录"""
        print(f"🔍 测试token登录: {self.base_url}/")
        
        try:
            response = requests.post(
                f"{self.base_url}/",
                json={"token": token},
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            
            data = response.json()
            
            if response.ok and data.get("status"):
                print("✅ Token登录成功!")
                user_data = data.get('user', {})
                print(f"   用户ID: {user_data.get('id')}")
                print(f"   用户名: {user_data.get('name')}")
                print(f"   邮箱: {user_data.get('email', '未设置')}")
                print(f"   提供商: {user_data.get('provider')}")
                print(f"   角色: {user_data.get('roles', [])}")
                
                # 检查是否返回了token
                if data.get("token"):
                    print(f"   返回的token: {data['token'][:50]}...")
                    self.token = data["token"]
                    return {"success": True, "data": data, "token": data["token"]}
                else:
                    print("⚠️  警告: 后端未返回token")
                    return {"success": True, "data": data, "token": None}
            else:
                print(f"❌ Token登录失败: {data.get('message', '未知错误')}")
                return {"success": False, "error": data.get('message', '未知错误')}
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求异常: {e}")
            return {"success": False, "error": str(e)}
    
    def test_current_user_with_token(self) -> Dict[str, Any]:
        """使用token测试获取当前用户"""
        if not self.token:
            print("❌ 没有可用的token，请先进行token登录")
            return {"success": False, "error": "No token available"}
        
        print(f"🔍 测试获取当前用户: {self.base_url}/api/auth/me")
        
        try:
            response = requests.get(
                f"{self.base_url}/api/auth/me",
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {self.token}"
                },
                timeout=10
            )
            
            data = response.json()
            
            if response.ok:
                print(f"✅ 获取当前用户成功:")
                print(f"   用户ID: {data.get('id')}")
                print(f"   用户名: {data.get('name')}")
                print(f"   邮箱: {data.get('email', '未设置')}")
                print(f"   提供商: {data.get('provider')}")
                print(f"   角色: {data.get('roles', [])}")
                return {"success": True, "data": data}
            else:
                print(f"❌ 获取当前用户失败: {data.get('message', '未知错误')}")
                print(f"   状态码: {response.status_code}")
                return {"success": False, "error": data.get('message', '未知错误')}
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求异常: {e}")
            return {"success": False, "error": str(e)}
    
    def test_without_token(self) -> Dict[str, Any]:
        """测试不带token的请求（应该返回默认用户）"""
        print(f"🔍 测试不带token的请求: {self.base_url}/api/auth/me")
        
        try:
            response = requests.get(
                f"{self.base_url}/api/auth/me",
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            
            if response.status_code == 401:
                print("✅ 不带token的请求正确返回401未授权")
                return {"success": True, "message": "Correctly returned 401 for no token"}
            else:
                data = response.json()
                print(f"⚠️  不带token的请求返回: {data}")
                return {"success": True, "data": data}
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求异常: {e}")
            return {"success": False, "error": str(e)}
    
    def run_comprehensive_test(self, token: str):
        """运行综合测试"""
        print("=" * 70)
        print("用户显示修复综合测试")
        print("=" * 70)
        
        # 步骤1: 检查认证类型
        print("\n📋 步骤1: 检查认证类型")
        auth_type_result = self.test_auth_type()
        
        if not auth_type_result["success"]:
            print("\n❌ 认证类型检查失败，无法继续测试")
            return False
        
        auth_type = auth_type_result["data"].get("type", "unknown")
        print(f"\n   当前认证类型: {auth_type}")
        
        # 步骤2: 测试token登录
        print("\n📋 步骤2: 测试token登录")
        login_result = self.test_token_login(token)
        
        if not login_result["success"]:
            print("\n❌ Token登录失败，无法继续测试")
            return False
        
        # 步骤3: 测试获取当前用户（带token）
        print("\n📋 步骤3: 测试获取当前用户（带token）")
        user_result = self.test_current_user_with_token()
        
        # 步骤4: 测试不带token的请求
        print("\n📋 步骤4: 测试不带token的请求")
        no_token_result = self.test_without_token()
        
        # 总结
        print("\n" + "=" * 70)
        print("测试总结")
        print("=" * 70)
        
        print(f"✅ 认证类型: {auth_type}")
        
        if login_result.get("token"):
            print("✅ 后端正确返回了token")
        else:
            print("⚠️  后端未返回token")
        
        if user_result["success"]:
            user_data = user_result["data"]
            expected_user_id = login_result["data"]["user"]["id"]
            actual_user_id = user_data.get("id")
            
            if actual_user_id == expected_user_id:
                print("✅ 用户认证成功，显示正确的用户信息")
                print(f"   登录用户: {actual_user_id}")
            else:
                print("❌ 用户认证失败，显示的用户信息不正确")
                print(f"   期望用户: {expected_user_id}")
                print(f"   实际用户: {actual_user_id}")
        else:
            print("❌ 无法获取用户信息，token认证可能未生效")
        
        # 前端测试提示
        print("\n📋 前端测试建议:")
        print("1. 打开浏览器开发者工具")
        print("2. 在控制台中执行以下命令存储token:")
        print(f"   localStorage.setItem('auth_token', '{self.token[:50]}...')")
        print("3. 刷新页面，检查用户显示是否正确")
        print("4. 检查网络请求是否携带Authorization头")
        
        return True


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python test_user_display_fix.py <JWT_TOKEN> [SERVER_URL]")
        print("示例: python test_user_display_fix.py 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...' 'http://127.0.0.1:8081'")
        sys.exit(1)
    
    token = sys.argv[1]
    server_url = sys.argv[2] if len(sys.argv) > 2 else "http://127.0.0.1:8081"
    
    tester = UserDisplayTester(server_url)
    tester.run_comprehensive_test(token)


if __name__ == "__main__":
    main()
