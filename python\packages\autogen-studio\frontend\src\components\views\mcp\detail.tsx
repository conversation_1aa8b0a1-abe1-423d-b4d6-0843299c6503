import React from "react";
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ge, Descriptions } from "antd";
import {
  Server,
  Play,
  Settings,
  CheckCircle,
  XCircle,
  Info,
} from "lucide-react";
import type { Component, McpWorkbenchConfig } from "../../types/datamodel";
import { WorkbenchFields } from "../teambuilder/builder/component-editor/fields/workbench";

interface McpDetailProps {
  workbench: Component<McpWorkbenchConfig>;
  onTestConnection: () => void;
}

const McpDetail: React.FC<McpDetailProps> = ({
  workbench,
  onTestConnection,
}) => {
  const serverParams = workbench.config.server_params;
  const serverType =
    serverParams?.type?.replace("ServerParams", "") || "Unknown";

  return (
    <div className="  ">
      <WorkbenchFields
        component={workbench}
        defaultPanelKey={["testing"]}
        readonly
        onChange={() => {
          // In the playground, we don't allow editing - this is read-only
          // The user would need to go to the Team Builder to make changes
        }}
      />
    </div>
  );
};

export default McpDetail;
