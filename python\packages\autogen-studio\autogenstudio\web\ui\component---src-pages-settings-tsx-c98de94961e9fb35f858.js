/*! For license information please see component---src-pages-settings-tsx-c98de94961e9fb35f858.js.LICENSE.txt */
"use strict";(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[512],{418:function(e,t,n){n.d(t,{A:function(){return a}});const a=(0,n(1788).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},1325:function(e,t,n){n.d(t,{C:function(){return i}});var a=n(1511),s=n(7134),l=n(1806);const r={show_llm_call_events:!1,expanded_messages_by_default:!1,show_agent_flow_by_default:!1,human_input_timeout_minutes:3},i=(0,a.v)()((0,s.Zr)((e,t)=>({serverSettings:null,isLoading:!1,error:null,uiSettings:r,initializeSettings:async n=>{if(!t().isLoading)try{e({isLoading:!0,error:null});const t=await l.Y.getSettings(n),a=(e=>e&&e.config&&e.config.ui?e.config.ui:r)(t);e({serverSettings:t,uiSettings:a,isLoading:!1})}catch(a){console.error("Failed to load settings:",a),e({error:"Failed to load settings",isLoading:!1,uiSettings:r})}},updateUISettings:n=>{const{uiSettings:a}=t(),s={...a,...n};e({uiSettings:s})},resetUISettings:async()=>(e({uiSettings:r}),Promise.resolve())}),{name:"ags-app-settings-0",partialize:e=>({uiSettings:e.uiSettings})}))},1806:function(e,t,n){n.d(t,{Y:function(){return l}});var a=n(7387);let s=function(e){function t(){return e.apply(this,arguments)||this}(0,a.A)(t,e);var n=t.prototype;return n.getSettings=async function(e){console.log(`🔍 SettingsAPI: Getting settings for user: ${e}`);const t=await fetch(`${this.getBaseUrl()}/settings/`,{headers:this.getHeaders()});console.log(`🔍 SettingsAPI: Response status: ${t.status}`);const n=await t.json();if(console.log("🔍 SettingsAPI: Response data:",n),!n.status){const e=new Error(n.detail||n.message||"Failed to fetch settings");throw console.error("❌ SettingsAPI: Failed to fetch settings:",e.message),e}return n.data},n.updateSettings=async function(e,t){const n={...e,user_id:e.user_id||t};console.log("settingsData",n);const a=await fetch(`${this.getBaseUrl()}/settings/`,{method:"PUT",headers:this.getHeaders(),body:JSON.stringify(n)}),s=await a.json();if(!s.status)throw new Error(s.message||"Failed to update settings");return s.data},t}(n(3838).y);const l=new s},2716:function(e,t,n){n.r(t),n.d(t,{default:function(){return k}});var a=n(6540),s=n(1155),l=n(6161),r=n(1788);const i=(0,r.A)("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]]),o=(0,r.A)("Variable",[["path",{d:"M8 21s-4-3-4-9 4-9 4-9",key:"uto9ud"}],["path",{d:"M16 3s4 3 4 9-4 9-4 9",key:"4w2vsq"}],["line",{x1:"15",x2:"9",y1:"9",y2:"15",key:"f7djnv"}],["line",{x1:"9",x2:"15",y1:"9",y2:"15",key:"1shsy8"}]]);var c=n(1325),d=n(367),u=n(9910),m=n(9644);const g=e=>{let{isOpen:t,sections:n,currentSection:s,onToggle:l,onSelectSection:r}=e;return t?a.createElement("div",{className:"h-full border-r border-secondary"},a.createElement("div",{className:"flex items-center justify-between pt-0 p-4 pl-2 pr-2 border-b border-secondary"},a.createElement("div",{className:"flex items-center gap-2"},a.createElement("span",{className:"text-primary font-medium"},"设置"),a.createElement("span",{className:"px-2 py-0.5 text-xs bg-accent/10 text-accent rounded"},n.length)),a.createElement(d.A,{title:"关闭侧边栏"},a.createElement("button",{onClick:l,className:"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50"},a.createElement(m.A,{strokeWidth:1.5,className:"h-6 w-6"})))),a.createElement("div",{className:"overflow-y-auto h-[calc(100%-64px)]"},n.map(e=>a.createElement("div",{key:e.id,className:"relative"},a.createElement("div",{className:"absolute top-1 left-0.5 z-50 h-[calc(100%-8px)] w-1 bg-opacity-80 rounded \n                "+(s.id===e.id?"bg-accent":"bg-tertiary")}),a.createElement("div",{className:"group ml-1 flex flex-col p-3 rounded-l cursor-pointer hover:bg-secondary \n                "+(s.id===e.id?"border-accent bg-secondary":"border-transparent"),onClick:()=>r(e)},a.createElement("div",{className:"flex items-center gap-2"},a.createElement(e.icon,{className:"w-4 h-4"}),a.createElement("span",{className:"text-sm"},e.title))))))):a.createElement("div",{className:"h-full border-r border-secondary"},a.createElement("div",{className:"p-2 -ml-2"},a.createElement(d.A,{title:`设置 (${n.length})`},a.createElement("button",{onClick:l,className:"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50"},a.createElement(u.A,{strokeWidth:1.5,className:"h-6 w-6"})))))};var f=n(2744),p=n(226),h=n(9036),y=n(2941),b=n(9492),v=n(8852),E=n(418),x=n(1806);const _=e=>{let{checked:t,onChange:n,label:s,description:l,disabled:r=!1}=e;return a.createElement("div",{className:"flex justify-between items-start p-4 hover:bg-secondary/5 rounded transition-colors"},a.createElement("div",{className:"flex flex-col gap-1"},a.createElement("label",{className:"font-medium"},s),l&&a.createElement("span",{className:"text-sm text-secondary"},l)),a.createElement("div",{className:"relative"},a.createElement("input",{type:"checkbox",checked:t,onChange:e=>n(e.target.checked),disabled:r,className:"sr-only",id:`toggle-${s.replace(/\s+/g,"-").toLowerCase()}`}),a.createElement("label",{htmlFor:`toggle-${s.replace(/\s+/g,"-").toLowerCase()}`,className:`relative inline-flex items-center h-6 rounded-full w-11 transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50 cursor-pointer ${r?"opacity-50 cursor-not-allowed":""} ${t?"bg-accent":"bg-gray-300"}`},a.createElement("span",{className:"inline-block w-4 h-4 transform bg-white rounded-full transition-transform "+(t?"translate-x-6":"translate-x-1")}))))},w=e=>{let{value:t,onChange:n,label:s,description:l,disabled:r=!1,min:i=1,max:o=30,suffix:c=""}=e;return a.createElement("div",{className:"flex justify-between items-start p-4 hover:bg-secondary rounded transition-colors"},a.createElement("div",{className:"flex flex-col gap-1"},a.createElement("label",{className:"font-medium"},s),l&&a.createElement("span",{className:"text-sm text-secondary"},l)),a.createElement("div",{className:"flex items-center gap-2"},a.createElement("input",{type:"number",value:t,onChange:e=>{const t=parseInt(e.target.value);!isNaN(t)&&t>=i&&t<=o&&n(t)},disabled:r,min:i,max:o,className:"w-16 px-2 py-1 text-sm border border-secondary rounded focus:border-accent focus:ring-1 focus:ring-accent outline-none disabled:opacity-50 bg-primary"}),c&&a.createElement("span",{className:"text-sm text-secondary"},c)))};var N=e=>{var t,n,s;let{userId:l}=e;const{serverSettings:r,uiSettings:i,initializeSettings:o}=(0,c.C)(),{0:u,1:m}=(0,a.useState)({show_llm_call_events:!1,expanded_messages_by_default:!1,show_agent_flow_by_default:!1,human_input_timeout_minutes:3}),{0:g,1:f}=(0,a.useState)(!1),{0:p,1:N}=(0,a.useState)(!1),[S,k]=h.Ay.useMessage();(0,a.useEffect)(()=>{m(i)},[i]);const $=(e,t)=>{m(n=>({...n,[e]:t})),f(!0)};return a.createElement("div",{className:" "},k,a.createElement("div",{className:"flex justify-between items-center mb-4"},a.createElement("h3",{className:"text-lg font-medium"},"界面设置"),a.createElement("div",{className:"space-x-2 inline-flex"},a.createElement(d.A,{title:"重置为默认值"},a.createElement(y.Ay,{icon:a.createElement(b.A,{className:"w-4 h-4"}),onClick:async()=>{try{if(N(!0),!r)return S.error("设置未加载"),void N(!1);const e={show_llm_call_events:!1,expanded_messages_by_default:!1,show_agent_flow_by_default:!1,human_input_timeout_minutes:3};m(e);const t={...r,config:{...r.config,ui:e},created_at:void 0,updated_at:void 0};console.log("Updated settings:",t),await x.Y.updateSettings(t,l),await o(l),f(!1),S.success("界面设置重置成功")}catch(e){console.error("Failed to reset UI settings:",e),S.error("重置界面设置失败")}finally{N(!1)}},disabled:p},"重置")),a.createElement(d.A,{title:g?"保存您的更改":"没有未保存的更改"},a.createElement(y.Ay,{type:"primary",icon:a.createElement("div",{className:"relative"},a.createElement(v.A,{className:"w-4 h-4"}),g&&a.createElement("div",{className:"absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"})),onClick:async()=>{try{if(N(!0),!r)return S.error("设置未加载"),void N(!1);const e={...r,config:{...r.config,ui:u},created_at:void 0,updated_at:void 0};await x.Y.updateSettings(e,l),await o(l),f(!1),S.success("界面设置保存成功")}catch(e){console.error("Failed to save UI settings:",e),S.error("保存界面设置失败")}finally{N(!1)}},disabled:!g||p,loading:p},"保存")))),a.createElement("div",{className:"space-y-0 rounded border border-secondary"},a.createElement(_,{checked:u.show_llm_call_events,onChange:e=>$("show_llm_call_events",e),label:"显示LLM事件",description:"在消息线程中显示详细的LLM调用日志",disabled:p}),a.createElement(_,{checked:null!==(t=u.expanded_messages_by_default)&&void 0!==t&&t,onChange:e=>$("expanded_messages_by_default",e),label:"默认展开消息",description:"加载时自动展开消息线程",disabled:p}),a.createElement(_,{checked:null!==(n=u.show_agent_flow_by_default)&&void 0!==n&&n,onChange:e=>$("show_agent_flow_by_default",e),label:"默认显示智能体流程",description:"自动显示智能体流程图",disabled:p}),a.createElement(w,{value:null!==(s=u.human_input_timeout_minutes)&&void 0!==s?s:3,onChange:e=>$("human_input_timeout_minutes",e),label:"人工输入超时时间",description:"等待用户输入的超时时间（1-30分钟）",disabled:p,min:1,max:30,suffix:"分钟"})),a.createElement("div",{className:"mt-4 hidden text-xs text-secondary"},a.createElement(E.A,{strokeWidth:1.5,className:"inline-block mr-1 h-4 w-4"})," ","这些设置会自动保存并在浏览器会话间同步"))};const S=()=>{const{0:e,1:t}=(0,a.useState)(()=>{if("undefined"!=typeof window){const e=localStorage.getItem("settingsSidebar");return null===e||JSON.parse(e)}return!0}),{user:n}=(0,a.useContext)(f.v),{isLoading:s,isAuthenticated:r}=(0,p.A)(),d=(null==n?void 0:n.id)||"";console.log("🔍 SettingsManager: user:",n),console.log("🔍 SettingsManager: userId:",d),console.log("🔍 SettingsManager: authLoading:",s),console.log("🔍 SettingsManager: isAuthenticated:",r);const{serverSettings:u,resetUISettings:m,initializeSettings:h,isLoading:y}=(0,c.C)();if((0,a.useEffect)(()=>{console.log(`🔍 SettingsManager: useEffect triggered with userId: ${d}, authLoading: ${s}`),!s&&d&&r?(console.log(`🔍 SettingsManager: Calling initializeSettings for user: ${d}`),h(d)):console.log(`⚠️ SettingsManager: Conditions not met - authLoading: ${s}, userId: ${d}, isAuthenticated: ${r}`)},[d,h,s,r]),(0,a.useEffect)(()=>{"undefined"!=typeof window&&localStorage.setItem("settingsSidebar",JSON.stringify(e))},[e]),s||y)return a.createElement("div",{className:"p-8 text-center"},"正在加载设置...");null==u||u.config.default_model_client;const b=[{key:"ui",label:a.createElement("span",{className:"flex items-center gap-2"},a.createElement(i,{className:"w-4 h-4"}),"界面设置"),children:a.createElement(N,{userId:d})}];return a.createElement("div",{className:"relative flex h-full w-full"},a.createElement("div",{className:"absolute left-0 top-0 h-full transition-all duration-200 ease-in-out "+(e?"w-64":"w-12")},a.createElement(g,{isOpen:e,sections:[{id:"settings",title:"设置",icon:o,content:()=>a.createElement(a.Fragment,null)}],currentSection:{id:"settings",title:"设置",icon:o,content:()=>a.createElement(a.Fragment,null)},onToggle:()=>t(!e),onSelectSection:()=>{}})),a.createElement("div",{className:"flex-1 transition-all max-w-5xl -mr-6 duration-200 "+(e?"ml-64":"ml-12")},a.createElement("div",{className:"p-4 pt-2"},a.createElement("div",{className:"flex items-center gap-2 mb-4 text-sm"},a.createElement("span",{className:"text-primary font-medium"},"设置")),a.createElement("div",{className:"flex items-center gap-2 mb-8 text-sm"},a.createElement("span",{className:"text-secondary"},"管理您的设置和偏好")),a.createElement("div",{className:"  rounded-lg shadow-sm"},a.createElement(l.A,{defaultActiveKey:"ui",items:b,size:"large",className:"settings-tabs"})))))};var k=e=>{let{data:t}=e;return a.createElement(s.A,{meta:t.site.siteMetadata,title:"设置",link:"/settings"},a.createElement("main",{style:{height:"100%"},className:" h-full "},a.createElement(S,null)))}},3838:function(e,t,n){n.d(t,{y:function(){return s}});var a=n(180);let s=function(){function e(){}var t=e.prototype;return t.getBaseUrl=function(){return(0,a.Tt)()},t.getHeaders=function(){const e=localStorage.getItem("auth_token"),t={"Content-Type":"application/json"};return e?(t.Authorization=`Bearer ${e}`,console.log(`🔍 BaseAPI: Using token from localStorage: ${e.substring(0,30)}...`)):console.log("⚠️ BaseAPI: No token found in localStorage"),t},e}()},5974:function(e,t,n){function a(e,t,n){const{focusElCls:a,focus:s,borderElCls:l}=n,r=l?"> *":"",i=["hover",s?"focus":null,"active"].filter(Boolean).map(e=>`&:${e} ${r}`).join(",");return{[`&-item:not(${t}-last-item)`]:{marginInlineEnd:e.calc(e.lineWidth).mul(-1).equal()},"&-item":Object.assign(Object.assign({[i]:{zIndex:2}},a?{[`&${a}`]:{zIndex:2}}:{}),{[`&[disabled] ${r}`]:{zIndex:0}})}}function s(e,t,n){const{borderElCls:a}=n,s=a?`> ${a}`:"";return{[`&-item:not(${t}-first-item):not(${t}-last-item) ${s}`]:{borderRadius:0},[`&-item:not(${t}-last-item)${t}-first-item`]:{[`& ${s}, &${e}-sm ${s}, &${e}-lg ${s}`]:{borderStartEndRadius:0,borderEndEndRadius:0}},[`&-item:not(${t}-first-item)${t}-last-item`]:{[`& ${s}, &${e}-sm ${s}, &${e}-lg ${s}`]:{borderStartStartRadius:0,borderEndStartRadius:0}}}}function l(e,t={focus:!0}){const{componentCls:n}=e,l=`${n}-compact`;return{[l]:Object.assign(Object.assign({},a(e,l,t)),s(n,l,t))}}n.d(t,{G:function(){return l}})},8852:function(e,t,n){n.d(t,{A:function(){return a}});const a=(0,n(1788).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},9492:function(e,t,n){n.d(t,{A:function(){return a}});const a=(0,n(1788).A)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])}}]);
//# sourceMappingURL=component---src-pages-settings-tsx-c98de94961e9fb35f858.js.map