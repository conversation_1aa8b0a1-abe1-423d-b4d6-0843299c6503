"use strict";(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[606],{5422:function(e,o,n){n.r(o);var t=n(6540),r=n(226),i=n(230),a=n(4716),c=n(7260),s=n(1155);const{Title:l}=i.A;o.default=e=>{let{data:o,location:n}=e;const{handleAuthCallback:i}=(0,r.A)(),{0:d,1:m}=(0,t.useState)(null),{0:p,1:u}=(0,t.useState)(!0);return(0,t.useEffect)(()=>{(async()=>{try{const e=new URLSearchParams(n.search),o=e.get("code"),t=e.get("state"),r=e.get("error");if(r)return m(`认证错误: ${r}`),void u(!1);if(!o)return m("在URL中未找到授权码"),void u(!1);await i(o,t||void 0),u(!1)}catch(e){console.error("Error during auth callback:",e),m("完成认证失败"),u(!1)}})()},[n.search,i]),t.createElement(s.A,{meta:o.site.siteMetadata,title:"认证中",link:"/callback",showHeader:!1},t.createElement("div",{className:"flex flex-col items-center justify-center h-screen"},p?t.createElement(t.Fragment,null,t.createElement(a.A,{size:"large"}),t.createElement(l,{level:4,className:"mt-4"},"正在完成认证...")):d?t.createElement(c.A,{message:"认证错误",description:d,type:"error",showIcon:!0,className:"max-w-md"}):t.createElement(c.A,{message:"认证成功",description:"您已成功认证。现在可以关闭此窗口。",type:"success",showIcon:!0,className:"max-w-md"})))}},7260:function(e,o,n){n.d(o,{A:function(){return R}});var t=n(6540),r=n(8811),i=n(6029),a=n(7852),c=n(7541),s=n(7850),l=n(6942),d=n.n(l),m=n(754),p=n(2065),u=n(8719),g=n(682),f=n(2279),b=n(2187),v=n(5905),h=n(7358);const $=(e,o,n,t,r)=>({background:e,border:`${(0,b.zA)(t.lineWidth)} ${t.lineType} ${o}`,[`${r}-icon`]:{color:n}}),y=e=>{const{componentCls:o,motionDurationSlow:n,marginXS:t,marginSM:r,fontSize:i,fontSizeLG:a,lineHeight:c,borderRadiusLG:s,motionEaseInOutCirc:l,withDescriptionIconSize:d,colorText:m,colorTextHeading:p,withDescriptionPadding:u,defaultPadding:g}=e;return{[o]:Object.assign(Object.assign({},(0,v.dF)(e)),{position:"relative",display:"flex",alignItems:"center",padding:g,wordWrap:"break-word",borderRadius:s,[`&${o}-rtl`]:{direction:"rtl"},[`${o}-content`]:{flex:1,minWidth:0},[`${o}-icon`]:{marginInlineEnd:t,lineHeight:0},"&-description":{display:"none",fontSize:i,lineHeight:c},"&-message":{color:p},[`&${o}-motion-leave`]:{overflow:"hidden",opacity:1,transition:`max-height ${n} ${l}, opacity ${n} ${l},\n        padding-top ${n} ${l}, padding-bottom ${n} ${l},\n        margin-bottom ${n} ${l}`},[`&${o}-motion-leave-active`]:{maxHeight:0,marginBottom:"0 !important",paddingTop:0,paddingBottom:0,opacity:0}}),[`${o}-with-description`]:{alignItems:"flex-start",padding:u,[`${o}-icon`]:{marginInlineEnd:r,fontSize:d,lineHeight:0},[`${o}-message`]:{display:"block",marginBottom:t,color:p,fontSize:a},[`${o}-description`]:{display:"block",color:m}},[`${o}-banner`]:{marginBottom:0,border:"0 !important",borderRadius:0}}},E=e=>{const{componentCls:o,colorSuccess:n,colorSuccessBorder:t,colorSuccessBg:r,colorWarning:i,colorWarningBorder:a,colorWarningBg:c,colorError:s,colorErrorBorder:l,colorErrorBg:d,colorInfo:m,colorInfoBorder:p,colorInfoBg:u}=e;return{[o]:{"&-success":$(r,t,n,e,o),"&-info":$(u,p,m,e,o),"&-warning":$(c,a,i,e,o),"&-error":Object.assign(Object.assign({},$(d,l,s,e,o)),{[`${o}-description > pre`]:{margin:0,padding:0}})}}},I=e=>{const{componentCls:o,iconCls:n,motionDurationMid:t,marginXS:r,fontSizeIcon:i,colorIcon:a,colorIconHover:c}=e;return{[o]:{"&-action":{marginInlineStart:r},[`${o}-close-icon`]:{marginInlineStart:r,padding:0,overflow:"hidden",fontSize:i,lineHeight:(0,b.zA)(i),backgroundColor:"transparent",border:"none",outline:"none",cursor:"pointer",[`${n}-close`]:{color:a,transition:`color ${t}`,"&:hover":{color:c}}},"&-close-text":{color:a,transition:`color ${t}`,"&:hover":{color:c}}}}};var C=(0,h.OF)("Alert",e=>[y(e),E(e),I(e)],e=>({withDescriptionIconSize:e.fontSizeHeading3,defaultPadding:`${e.paddingContentVerticalSM}px 12px`,withDescriptionPadding:`${e.paddingMD}px ${e.paddingContentHorizontalLG}px`})),S=function(e,o){var n={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&o.indexOf(t)<0&&(n[t]=e[t]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(t=Object.getOwnPropertySymbols(e);r<t.length;r++)o.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(n[t[r]]=e[t[r]])}return n};const w={success:r.A,info:s.A,error:i.A,warning:c.A},x=e=>{const{icon:o,prefixCls:n,type:r}=e,i=w[r]||null;return o?(0,g.fx)(o,t.createElement("span",{className:`${n}-icon`},o),()=>({className:d()(`${n}-icon`,o.props.className)})):t.createElement(i,{className:`${n}-icon`})},A=e=>{const{isClosable:o,prefixCls:n,closeIcon:r,handleClose:i,ariaProps:c}=e,s=!0===r||void 0===r?t.createElement(a.A,null):r;return o?t.createElement("button",Object.assign({type:"button",onClick:i,className:`${n}-close-icon`,tabIndex:0},c),s):null},k=t.forwardRef((e,o)=>{const{description:n,prefixCls:r,message:i,banner:a,className:c,rootClassName:s,style:l,onMouseEnter:g,onMouseLeave:b,onClick:v,afterClose:h,showIcon:$,closable:y,closeText:E,closeIcon:I,action:w,id:k}=e,N=S(e,["description","prefixCls","message","banner","className","rootClassName","style","onMouseEnter","onMouseLeave","onClick","afterClose","showIcon","closable","closeText","closeIcon","action","id"]),[O,j]=t.useState(!1);const z=t.useRef(null);t.useImperativeHandle(o,()=>({nativeElement:z.current}));const{getPrefixCls:M,direction:H,closable:B,closeIcon:P,className:L,style:D}=(0,f.TP)("alert"),R=M("alert",r),[T,W,F]=C(R),G=o=>{var n;j(!0),null===(n=e.onClose)||void 0===n||n.call(e,o)},X=t.useMemo(()=>void 0!==e.type?e.type:a?"warning":"info",[e.type,a]),U=t.useMemo(()=>!("object"!=typeof y||!y.closeIcon)||(!!E||("boolean"==typeof y?y:!1!==I&&null!=I||!!B)),[E,I,y,B]),K=!(!a||void 0!==$)||$,V=d()(R,`${R}-${X}`,{[`${R}-with-description`]:!!n,[`${R}-no-icon`]:!K,[`${R}-banner`]:!!a,[`${R}-rtl`]:"rtl"===H},L,c,s,F,W),q=(0,p.A)(N,{aria:!0,data:!0}),J=t.useMemo(()=>"object"==typeof y&&y.closeIcon?y.closeIcon:E||(void 0!==I?I:"object"==typeof B&&B.closeIcon?B.closeIcon:P),[I,y,E,P]),Q=t.useMemo(()=>{const e=null!=y?y:B;if("object"==typeof e){const{closeIcon:o}=e;return S(e,["closeIcon"])}return{}},[y,B]);return T(t.createElement(m.Ay,{visible:!O,motionName:`${R}-motion`,motionAppear:!1,motionEnter:!1,onLeaveStart:e=>({maxHeight:e.offsetHeight}),onLeaveEnd:h},({className:o,style:r},a)=>t.createElement("div",Object.assign({id:k,ref:(0,u.K4)(z,a),"data-show":!O,className:d()(V,o),style:Object.assign(Object.assign(Object.assign({},D),l),r),onMouseEnter:g,onMouseLeave:b,onClick:v,role:"alert"},q),K?t.createElement(x,{description:n,icon:e.icon,prefixCls:R,type:X}):null,t.createElement("div",{className:`${R}-content`},i?t.createElement("div",{className:`${R}-message`},i):null,n?t.createElement("div",{className:`${R}-description`},n):null),w?t.createElement("div",{className:`${R}-action`},w):null,t.createElement(A,{isClosable:U,prefixCls:R,closeIcon:J,handleClose:G,ariaProps:Q}))))});var N=k,O=n(3029),j=n(2901),z=n(3954),M=n(2176),H=n(6822);var B=n(5501);let P=function(e){function o(){var e,n,t,r;return(0,O.A)(this,o),n=this,t=o,r=arguments,t=(0,z.A)(t),(e=(0,H.A)(n,(0,M.A)()?Reflect.construct(t,r||[],(0,z.A)(n).constructor):t.apply(n,r))).state={error:void 0,info:{componentStack:""}},e}return(0,B.A)(o,e),(0,j.A)(o,[{key:"componentDidCatch",value:function(e,o){this.setState({error:e,info:o})}},{key:"render",value:function(){const{message:e,description:o,id:n,children:r}=this.props,{error:i,info:a}=this.state,c=(null==a?void 0:a.componentStack)||null,s=void 0===e?(i||"").toString():e,l=void 0===o?c:o;return i?t.createElement(N,{id:n,type:"error",message:s,description:t.createElement("pre",{style:{fontSize:"0.9em",overflowX:"auto"}},l)}):r}}])}(t.Component);var L=P;const D=N;D.ErrorBoundary=L;var R=D}}]);
//# sourceMappingURL=component---src-pages-callback-tsx-951cf2db59b2b7d0cb07.js.map