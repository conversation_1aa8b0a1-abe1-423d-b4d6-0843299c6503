/*! For license information please see f359c09962c4e52a9d4cb2c90fcf1a14e88c481c-398322329f3c1682e759.js.LICENSE.txt */
"use strict";(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[247],{7260:function(e,t,n){n.d(t,{A:function(){return B}});var r=n(6540),o=n(8811),i=n(6029),c=n(7852),a=n(7541),l=n(7850),u=n(6942),s=n.n(u),d=n(754),f=n(2065),p=n(8719),g=n(682),m=n(2279),h=n(2187),v=n(5905),y=n(7358);const b=(e,t,n,r,o)=>({background:e,border:`${(0,h.zA)(r.lineWidth)} ${r.lineType} ${t}`,[`${o}-icon`]:{color:n}}),w=e=>{const{componentCls:t,motionDurationSlow:n,marginXS:r,marginSM:o,fontSize:i,fontSizeLG:c,lineHeight:a,borderRadiusLG:l,motionEaseInOutCirc:u,withDescriptionIconSize:s,colorText:d,colorTextHeading:f,withDescriptionPadding:p,defaultPadding:g}=e;return{[t]:Object.assign(Object.assign({},(0,v.dF)(e)),{position:"relative",display:"flex",alignItems:"center",padding:g,wordWrap:"break-word",borderRadius:l,[`&${t}-rtl`]:{direction:"rtl"},[`${t}-content`]:{flex:1,minWidth:0},[`${t}-icon`]:{marginInlineEnd:r,lineHeight:0},"&-description":{display:"none",fontSize:i,lineHeight:a},"&-message":{color:f},[`&${t}-motion-leave`]:{overflow:"hidden",opacity:1,transition:`max-height ${n} ${u}, opacity ${n} ${u},\n        padding-top ${n} ${u}, padding-bottom ${n} ${u},\n        margin-bottom ${n} ${u}`},[`&${t}-motion-leave-active`]:{maxHeight:0,marginBottom:"0 !important",paddingTop:0,paddingBottom:0,opacity:0}}),[`${t}-with-description`]:{alignItems:"flex-start",padding:p,[`${t}-icon`]:{marginInlineEnd:o,fontSize:s,lineHeight:0},[`${t}-message`]:{display:"block",marginBottom:r,color:f,fontSize:c},[`${t}-description`]:{display:"block",color:d}},[`${t}-banner`]:{marginBottom:0,border:"0 !important",borderRadius:0}}},O=e=>{const{componentCls:t,colorSuccess:n,colorSuccessBorder:r,colorSuccessBg:o,colorWarning:i,colorWarningBorder:c,colorWarningBg:a,colorError:l,colorErrorBorder:u,colorErrorBg:s,colorInfo:d,colorInfoBorder:f,colorInfoBg:p}=e;return{[t]:{"&-success":b(o,r,n,e,t),"&-info":b(p,f,d,e,t),"&-warning":b(a,c,i,e,t),"&-error":Object.assign(Object.assign({},b(s,u,l,e,t)),{[`${t}-description > pre`]:{margin:0,padding:0}})}}},j=e=>{const{componentCls:t,iconCls:n,motionDurationMid:r,marginXS:o,fontSizeIcon:i,colorIcon:c,colorIconHover:a}=e;return{[t]:{"&-action":{marginInlineStart:o},[`${t}-close-icon`]:{marginInlineStart:o,padding:0,overflow:"hidden",fontSize:i,lineHeight:(0,h.zA)(i),backgroundColor:"transparent",border:"none",outline:"none",cursor:"pointer",[`${n}-close`]:{color:c,transition:`color ${r}`,"&:hover":{color:a}}},"&-close-text":{color:c,transition:`color ${r}`,"&:hover":{color:a}}}}};var E=(0,y.OF)("Alert",e=>[w(e),O(e),j(e)],e=>({withDescriptionIconSize:e.fontSizeHeading3,defaultPadding:`${e.paddingContentVerticalSM}px 12px`,withDescriptionPadding:`${e.paddingMD}px ${e.paddingContentHorizontalLG}px`})),M=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};const S={success:o.A,info:l.A,error:i.A,warning:a.A},C=e=>{const{icon:t,prefixCls:n,type:o}=e,i=S[o]||null;return t?(0,g.fx)(t,r.createElement("span",{className:`${n}-icon`},t),()=>({className:s()(`${n}-icon`,t.props.className)})):r.createElement(i,{className:`${n}-icon`})},I=e=>{const{isClosable:t,prefixCls:n,closeIcon:o,handleClose:i,ariaProps:a}=e,l=!0===o||void 0===o?r.createElement(c.A,null):o;return t?r.createElement("button",Object.assign({type:"button",onClick:i,className:`${n}-close-icon`,tabIndex:0},a),l):null},$=r.forwardRef((e,t)=>{const{description:n,prefixCls:o,message:i,banner:c,className:a,rootClassName:l,style:u,onMouseEnter:g,onMouseLeave:h,onClick:v,afterClose:y,showIcon:b,closable:w,closeText:O,closeIcon:j,action:S,id:$}=e,k=M(e,["description","prefixCls","message","banner","className","rootClassName","style","onMouseEnter","onMouseLeave","onClick","afterClose","showIcon","closable","closeText","closeIcon","action","id"]),[x,P]=r.useState(!1);const R=r.useRef(null);r.useImperativeHandle(t,()=>({nativeElement:R.current}));const{getPrefixCls:A,direction:N,closable:T,closeIcon:z,className:D,style:L}=(0,m.TP)("alert"),B=A("alert",o),[V,H,W]=E(B),q=t=>{var n;P(!0),null===(n=e.onClose)||void 0===n||n.call(e,t)},F=r.useMemo(()=>void 0!==e.type?e.type:c?"warning":"info",[e.type,c]),_=r.useMemo(()=>!("object"!=typeof w||!w.closeIcon)||(!!O||("boolean"==typeof w?w:!1!==j&&null!=j||!!T)),[O,j,w,T]),U=!(!c||void 0!==b)||b,G=s()(B,`${B}-${F}`,{[`${B}-with-description`]:!!n,[`${B}-no-icon`]:!U,[`${B}-banner`]:!!c,[`${B}-rtl`]:"rtl"===N},D,a,l,W,H),X=(0,f.A)(k,{aria:!0,data:!0}),K=r.useMemo(()=>"object"==typeof w&&w.closeIcon?w.closeIcon:O||(void 0!==j?j:"object"==typeof T&&T.closeIcon?T.closeIcon:z),[j,w,O,z]),Y=r.useMemo(()=>{const e=null!=w?w:T;if("object"==typeof e){const{closeIcon:t}=e;return M(e,["closeIcon"])}return{}},[w,T]);return V(r.createElement(d.Ay,{visible:!x,motionName:`${B}-motion`,motionAppear:!1,motionEnter:!1,onLeaveStart:e=>({maxHeight:e.offsetHeight}),onLeaveEnd:y},({className:t,style:o},c)=>r.createElement("div",Object.assign({id:$,ref:(0,p.K4)(R,c),"data-show":!x,className:s()(G,t),style:Object.assign(Object.assign(Object.assign({},L),u),o),onMouseEnter:g,onMouseLeave:h,onClick:v,role:"alert"},X),U?r.createElement(C,{description:n,icon:e.icon,prefixCls:B,type:F}):null,r.createElement("div",{className:`${B}-content`},i?r.createElement("div",{className:`${B}-message`},i):null,n?r.createElement("div",{className:`${B}-description`},n):null),S?r.createElement("div",{className:`${B}-action`},S):null,r.createElement(I,{isClosable:_,prefixCls:B,closeIcon:K,handleClose:q,ariaProps:Y}))))});var k=$,x=n(3029),P=n(2901),R=n(3954),A=n(2176),N=n(6822);var T=n(5501);let z=function(e){function t(){var e,n,r,o;return(0,x.A)(this,t),n=this,r=t,o=arguments,r=(0,R.A)(r),(e=(0,N.A)(n,(0,A.A)()?Reflect.construct(r,o||[],(0,R.A)(n).constructor):r.apply(n,o))).state={error:void 0,info:{componentStack:""}},e}return(0,T.A)(t,e),(0,P.A)(t,[{key:"componentDidCatch",value:function(e,t){this.setState({error:e,info:t})}},{key:"render",value:function(){const{message:e,description:t,id:n,children:o}=this.props,{error:i,info:c}=this.state,a=(null==c?void 0:c.componentStack)||null,l=void 0===e?(i||"").toString():e,u=void 0===t?a:t;return i?r.createElement(k,{id:n,type:"error",message:l,description:r.createElement("pre",{style:{fontSize:"0.9em",overflowX:"auto"}},u)}):o}}])}(r.Component);var D=z;const L=k;L.ErrorBoundary=D;var B=L},8309:function(e,t,n){n.d(t,{A:function(){return r}});const r=(0,n(1788).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},9872:function(e,t,n){n.d(t,{T:function(){return ae}});var r=n(6540);function o(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function i(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?i(Object(n),!0).forEach(function(t){o(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):i(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function a(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function u(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach(function(t){u(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function f(e){return function t(){for(var n=this,r=arguments.length,o=new Array(r),i=0;i<r;i++)o[i]=arguments[i];return o.length>=e.length?e.apply(this,o):function(){for(var e=arguments.length,r=new Array(e),i=0;i<e;i++)r[i]=arguments[i];return t.apply(n,[].concat(o,r))}}}function p(e){return{}.toString.call(e).includes("Object")}function g(e){return"function"==typeof e}var m=f(function(e,t){throw new Error(e[t]||e.default)})({initialIsRequired:"initial state is required",initialType:"initial state should be an object",initialContent:"initial state shouldn't be an empty object",handlerType:"handler should be an object or a function",handlersType:"all handlers should be a functions",selectorType:"selector should be a function",changeType:"provided value of changes should be an object",changeField:'it seams you want to change a field in the state which is not specified in the "initial" state',default:"an unknown error accured in `state-local` package"}),h={changes:function(e,t){return p(t)||m("changeType"),Object.keys(t).some(function(t){return n=e,r=t,!Object.prototype.hasOwnProperty.call(n,r);var n,r})&&m("changeField"),t},selector:function(e){g(e)||m("selectorType")},handler:function(e){g(e)||p(e)||m("handlerType"),p(e)&&Object.values(e).some(function(e){return!g(e)})&&m("handlersType")},initial:function(e){var t;e||m("initialIsRequired"),p(e)||m("initialType"),t=e,Object.keys(t).length||m("initialContent")}};function v(e,t){return g(t)?t(e.current):t}function y(e,t){return e.current=d(d({},e.current),t),t}function b(e,t,n){return g(t)?t(e.current):Object.keys(n).forEach(function(n){var r;return null===(r=t[n])||void 0===r?void 0:r.call(t,e.current[n])}),n}var w={create:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};h.initial(e),h.handler(t);var n={current:e},r=f(b)(n,t),o=f(y)(n),i=f(h.changes)(e),c=f(v)(n);return[function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:function(e){return e};return h.selector(e),e(n.current)},function(e){!function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){return t.reduceRight(function(e,t){return t(e)},e)}}(r,o,i,c)(e)}]}},O=w,j={paths:{vs:"https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs"}};var E=function(e){return function t(){for(var n=this,r=arguments.length,o=new Array(r),i=0;i<r;i++)o[i]=arguments[i];return o.length>=e.length?e.apply(this,o):function(){for(var e=arguments.length,r=new Array(e),i=0;i<e;i++)r[i]=arguments[i];return t.apply(n,[].concat(o,r))}}};var M=function(e){return{}.toString.call(e).includes("Object")};var S={configIsRequired:"the configuration object is required",configType:"the configuration object should be an object",default:"an unknown error accured in `@monaco-editor/loader` package",deprecation:"Deprecation warning!\n    You are using deprecated way of configuration.\n\n    Instead of using\n      monaco.config({ urls: { monacoBase: '...' } })\n    use\n      monaco.config({ paths: { vs: '...' } })\n\n    For more please check the link https://github.com/suren-atoyan/monaco-loader#config\n  "},C=E(function(e,t){throw new Error(e[t]||e.default)})(S),I={config:function(e){return e||C("configIsRequired"),M(e)||C("configType"),e.urls?(console.warn(S.deprecation),{paths:{vs:e.urls.monacoBase}}):e}},$=I,k=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){return t.reduceRight(function(e,t){return t(e)},e)}};var x=function e(t,n){return Object.keys(n).forEach(function(r){n[r]instanceof Object&&t[r]&&Object.assign(n[r],e(t[r],n[r]))}),c(c({},t),n)},P={type:"cancelation",msg:"operation is manually canceled"};var R,A,N=function(e){var t=!1,n=new Promise(function(n,r){e.then(function(e){return t?r(P):n(e)}),e.catch(r)});return n.cancel=function(){return t=!0},n},T=O.create({config:j,isInitialized:!1,resolve:null,reject:null,monaco:null}),z=(A=2,function(e){if(Array.isArray(e))return e}(R=T)||function(e,t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e)){var n=[],r=!0,o=!1,i=void 0;try{for(var c,a=e[Symbol.iterator]();!(r=(c=a.next()).done)&&(n.push(c.value),!t||n.length!==t);r=!0);}catch(l){o=!0,i=l}finally{try{r||null==a.return||a.return()}finally{if(o)throw i}}return n}}(R,A)||function(e,t){if(e){if("string"==typeof e)return l(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?l(e,t):void 0}}(R,A)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),D=z[0],L=z[1];function B(e){return document.body.appendChild(e)}function V(e){var t,n,r=D(function(e){return{config:e.config,reject:e.reject}}),o=(t="".concat(r.config.paths.vs,"/loader.js"),n=document.createElement("script"),t&&(n.src=t),n);return o.onload=function(){return e()},o.onerror=r.reject,o}function H(){var e=D(function(e){return{config:e.config,resolve:e.resolve,reject:e.reject}}),t=window.require;t.config(e.config),t(["vs/editor/editor.main"],function(t){W(t),e.resolve(t)},function(t){e.reject(t)})}function W(e){D().monaco||L({monaco:e})}var q=new Promise(function(e,t){return L({resolve:e,reject:t})}),F={config:function(e){var t=$.config(e),n=t.monaco,r=a(t,["monaco"]);L(function(e){return{config:x(e.config,r),monaco:n}})},init:function(){var e=D(function(e){return{monaco:e.monaco,isInitialized:e.isInitialized,resolve:e.resolve}});if(!e.isInitialized){if(L({isInitialized:!0}),e.monaco)return e.resolve(e.monaco),N(q);if(window.monaco&&window.monaco.editor)return W(window.monaco),e.resolve(window.monaco),N(q);k(B,V)(H)}return N(q)},__getMonacoInstance:function(){return D(function(e){return e.monaco})}},_=F,U={wrapper:{display:"flex",position:"relative",textAlign:"initial"},fullWidth:{width:"100%"},hide:{display:"none"}},G={container:{display:"flex",height:"100%",width:"100%",justifyContent:"center",alignItems:"center"}};var X=function({children:e}){return r.createElement("div",{style:G.container},e)};var K=function({width:e,height:t,isEditorReady:n,loading:o,_ref:i,className:c,wrapperProps:a}){return r.createElement("section",{style:{...U.wrapper,width:e,height:t},...a},!n&&r.createElement(X,null,o),r.createElement("div",{ref:i,style:{...U.fullWidth,...!n&&U.hide},className:c}))},Y=(0,r.memo)(K);var J=function(e){(0,r.useEffect)(e,[])};var Q=function(e,t,n=!0){let o=(0,r.useRef)(!0);(0,r.useEffect)(o.current||!n?()=>{o.current=!1}:e,t)};function Z(){}function ee(e,t,n,r){return function(e,t){return e.editor.getModel(te(e,t))}(e,r)||function(e,t,n,r){return e.editor.createModel(t,n,r?te(e,r):void 0)}(e,t,n,r)}function te(e,t){return e.Uri.parse(t)}var ne=function({original:e,modified:t,language:n,originalLanguage:o,modifiedLanguage:i,originalModelPath:c,modifiedModelPath:a,keepCurrentOriginalModel:l=!1,keepCurrentModifiedModel:u=!1,theme:s="light",loading:d="Loading...",options:f={},height:p="100%",width:g="100%",className:m,wrapperProps:h={},beforeMount:v=Z,onMount:y=Z}){let[b,w]=(0,r.useState)(!1),[O,j]=(0,r.useState)(!0),E=(0,r.useRef)(null),M=(0,r.useRef)(null),S=(0,r.useRef)(null),C=(0,r.useRef)(y),I=(0,r.useRef)(v),$=(0,r.useRef)(!1);J(()=>{let e=_.init();return e.then(e=>(M.current=e)&&j(!1)).catch(e=>"cancelation"!==e?.type&&console.error("Monaco initialization: error:",e)),()=>E.current?function(){let e=E.current?.getModel();l||e?.original?.dispose(),u||e?.modified?.dispose(),E.current?.dispose()}():e.cancel()}),Q(()=>{if(E.current&&M.current){let t=E.current.getOriginalEditor(),r=ee(M.current,e||"",o||n||"text",c||"");r!==t.getModel()&&t.setModel(r)}},[c],b),Q(()=>{if(E.current&&M.current){let e=E.current.getModifiedEditor(),r=ee(M.current,t||"",i||n||"text",a||"");r!==e.getModel()&&e.setModel(r)}},[a],b),Q(()=>{let e=E.current.getModifiedEditor();e.getOption(M.current.editor.EditorOption.readOnly)?e.setValue(t||""):t!==e.getValue()&&(e.executeEdits("",[{range:e.getModel().getFullModelRange(),text:t||"",forceMoveMarkers:!0}]),e.pushUndoStop())},[t],b),Q(()=>{E.current?.getModel()?.original.setValue(e||"")},[e],b),Q(()=>{let{original:e,modified:t}=E.current.getModel();M.current.editor.setModelLanguage(e,o||n||"text"),M.current.editor.setModelLanguage(t,i||n||"text")},[n,o,i],b),Q(()=>{M.current?.editor.setTheme(s)},[s],b),Q(()=>{E.current?.updateOptions(f)},[f],b);let k=(0,r.useCallback)(()=>{if(!M.current)return;I.current(M.current);let r=ee(M.current,e||"",o||n||"text",c||""),l=ee(M.current,t||"",i||n||"text",a||"");E.current?.setModel({original:r,modified:l})},[n,t,i,e,o,c,a]),x=(0,r.useCallback)(()=>{!$.current&&S.current&&(E.current=M.current.editor.createDiffEditor(S.current,{automaticLayout:!0,...f}),k(),M.current?.editor.setTheme(s),w(!0),$.current=!0)},[f,s,k]);return(0,r.useEffect)(()=>{b&&C.current(E.current,M.current)},[b]),(0,r.useEffect)(()=>{!O&&!b&&x()},[O,b,x]),r.createElement(Y,{width:g,height:p,isEditorReady:b,loading:d,_ref:S,className:m,wrapperProps:h})};(0,r.memo)(ne);var re=function(e){let t=(0,r.useRef)();return(0,r.useEffect)(()=>{t.current=e},[e]),t.current},oe=new Map;var ie=function({defaultValue:e,defaultLanguage:t,defaultPath:n,value:o,language:i,path:c,theme:a="light",line:l,loading:u="Loading...",options:s={},overrideServices:d={},saveViewState:f=!0,keepCurrentModel:p=!1,width:g="100%",height:m="100%",className:h,wrapperProps:v={},beforeMount:y=Z,onMount:b=Z,onChange:w,onValidate:O=Z}){let[j,E]=(0,r.useState)(!1),[M,S]=(0,r.useState)(!0),C=(0,r.useRef)(null),I=(0,r.useRef)(null),$=(0,r.useRef)(null),k=(0,r.useRef)(b),x=(0,r.useRef)(y),P=(0,r.useRef)(),R=(0,r.useRef)(o),A=re(c),N=(0,r.useRef)(!1),T=(0,r.useRef)(!1);J(()=>{let e=_.init();return e.then(e=>(C.current=e)&&S(!1)).catch(e=>"cancelation"!==e?.type&&console.error("Monaco initialization: error:",e)),()=>I.current?(P.current?.dispose(),p?f&&oe.set(c,I.current.saveViewState()):I.current.getModel()?.dispose(),void I.current.dispose()):e.cancel()}),Q(()=>{let r=ee(C.current,e||o||"",t||i||"",c||n||"");r!==I.current?.getModel()&&(f&&oe.set(A,I.current?.saveViewState()),I.current?.setModel(r),f&&I.current?.restoreViewState(oe.get(c)))},[c],j),Q(()=>{I.current?.updateOptions(s)},[s],j),Q(()=>{!I.current||void 0===o||(I.current.getOption(C.current.editor.EditorOption.readOnly)?I.current.setValue(o):o!==I.current.getValue()&&(T.current=!0,I.current.executeEdits("",[{range:I.current.getModel().getFullModelRange(),text:o,forceMoveMarkers:!0}]),I.current.pushUndoStop(),T.current=!1))},[o],j),Q(()=>{let e=I.current?.getModel();e&&i&&C.current?.editor.setModelLanguage(e,i)},[i],j),Q(()=>{void 0!==l&&I.current?.revealLine(l)},[l],j),Q(()=>{C.current?.editor.setTheme(a)},[a],j);let z=(0,r.useCallback)(()=>{if($.current&&C.current&&!N.current){x.current(C.current);let r=c||n,u=ee(C.current,o||e||"",t||i||"",r||"");I.current=C.current?.editor.create($.current,{model:u,automaticLayout:!0,...s},d),f&&I.current.restoreViewState(oe.get(r)),C.current.editor.setTheme(a),void 0!==l&&I.current.revealLine(l),E(!0),N.current=!0}},[e,t,n,o,i,c,s,d,f,a,l]);return(0,r.useEffect)(()=>{j&&k.current(I.current,C.current)},[j]),(0,r.useEffect)(()=>{!M&&!j&&z()},[M,j,z]),R.current=o,(0,r.useEffect)(()=>{j&&w&&(P.current?.dispose(),P.current=I.current?.onDidChangeModelContent(e=>{T.current||w(I.current.getValue(),e)}))},[j,w]),(0,r.useEffect)(()=>{if(j){let e=C.current.editor.onDidChangeMarkers(e=>{let t=I.current.getModel()?.uri;if(t&&e.find(e=>e.path===t.path)){let e=C.current.editor.getModelMarkers({resource:t});O?.(e)}});return()=>{e?.dispose()}}return()=>{}},[j,O]),r.createElement(Y,{width:g,height:m,isEditorReady:j,loading:u,_ref:$,className:h,wrapperProps:v})},ce=(0,r.memo)(ie);_.config({paths:{vs:"/monaco-editor/vs"}});const ae=e=>{let{value:t,editorRef:n,language:o,onChange:i,minimap:c=!0,className:a}=e;const{0:l,1:u}=(0,r.useState)(!1);return r.createElement("div",{id:"monaco-editor",className:`h-full rounded ${a}`},r.createElement(ce,{height:"100%",className:"h-full rounded",defaultLanguage:o,defaultValue:t,value:t,onChange:e=>{i&&e&&i(e)},onMount:(e,t)=>{n.current=e,u(!0)},theme:"vs-dark",options:{wordWrap:"on",wrappingIndent:"indent",wrappingStrategy:"advanced",minimap:{enabled:c}}}))}}}]);
//# sourceMappingURL=f359c09962c4e52a9d4cb2c90fcf1a14e88c481c-398322329f3c1682e759.js.map