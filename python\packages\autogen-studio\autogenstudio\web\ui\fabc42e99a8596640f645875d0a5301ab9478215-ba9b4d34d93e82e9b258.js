/*! For license information please see fabc42e99a8596640f645875d0a5301ab9478215-ba9b4d34d93e82e9b258.js.LICENSE.txt */
(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[207],{181:function(e,t,n){var a=/^\s+|\s+$/g,o=/^[-+]0x[0-9a-f]+$/i,l=/^0b[01]+$/i,r=/^0o[0-7]+$/i,s=parseInt,c="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g,i="object"==typeof self&&self&&self.Object===Object&&self,m=c||i||Function("return this")(),d=Object.prototype.toString,u=Math.max,p=Math.min,f=function(){return m.Date.now()};function g(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function b(e){if("number"==typeof e)return e;if(function(e){return"symbol"==typeof e||function(e){return!!e&&"object"==typeof e}(e)&&"[object Symbol]"==d.call(e)}(e))return NaN;if(g(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=g(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(a,"");var n=l.test(e);return n||r.test(e)?s(e.slice(2),n?2:8):o.test(e)?NaN:+e}e.exports=function(e,t,n){var a,o,l,r,s,c,i=0,m=!1,d=!1,v=!0;if("function"!=typeof e)throw new TypeError("Expected a function");function h(t){var n=a,l=o;return a=o=void 0,i=t,r=e.apply(l,n)}function y(e){var n=e-c;return void 0===c||n>=t||n<0||d&&e-i>=l}function E(){var e=f();if(y(e))return x(e);s=setTimeout(E,function(e){var n=t-(e-c);return d?p(n,l-(e-i)):n}(e))}function x(e){return s=void 0,v&&a?h(e):(a=o=void 0,r)}function N(){var e=f(),n=y(e);if(a=arguments,o=this,c=e,n){if(void 0===s)return function(e){return i=e,s=setTimeout(E,t),m?h(e):r}(c);if(d)return s=setTimeout(E,t),h(c)}return void 0===s&&(s=setTimeout(E,t)),r}return t=b(t)||0,g(n)&&(m=!!n.leading,l=(d="maxWait"in n)?u(b(n.maxWait)||0,t):l,v="trailing"in n?!!n.trailing:v),N.cancel=function(){void 0!==s&&clearTimeout(s),i=0,a=c=o=s=void 0},N.flush=function(){return void 0===s?r:x(f())},N}},5404:function(e,t,n){"use strict";n.d(t,{A:function(){return a}});const a=(0,n(1788).A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},5680:function(e,t,n){"use strict";n.d(t,{A:function(){return a}});const a=(0,n(1788).A)("Timer",[["line",{x1:"10",x2:"14",y1:"2",y2:"2",key:"14vaq8"}],["line",{x1:"12",x2:"15",y1:"14",y2:"11",key:"17fdiu"}],["circle",{cx:"12",cy:"14",r:"8",key:"1e1u0o"}]])},6143:function(e,t,n){"use strict";n.d(t,{A:function(){return Y}});var a=n(6540),o=n(6942),l=n.n(o),r=n(9379),s=n(5544),c=n(2595),i=n(981),m=a.createContext(null),d=a.createContext({}),u=m,p=n(4467),f=n(8168),g=n(754),b=n(6928),v=n(2065),h=n(3986),y=n(8719),E=["prefixCls","className","containerRef"];var x=function(e){var t=e.prefixCls,n=e.className,o=e.containerRef,r=(0,h.A)(e,E),s=a.useContext(d).panel,c=(0,y.xK)(s,o);return a.createElement("div",(0,f.A)({className:l()("".concat(t,"-content"),n),role:"dialog",ref:c},(0,v.A)(e,{aria:!0}),{"aria-modal":"true"},r))},N=n(8210);function k(e){return"string"==typeof e&&String(Number(e))===e?((0,N.Ay)(!1,"Invalid value type of `width` or `height` which should be number type instead."),Number(e)):e}var C={width:0,height:0,overflow:"hidden",outline:"none",position:"absolute"};function A(e,t){var n,o,c,i=e.prefixCls,m=e.open,d=e.placement,h=e.inline,y=e.push,E=e.forceRender,N=e.autoFocus,A=e.keyboard,w=e.classNames,_=e.rootClassName,O=e.rootStyle,S=e.zIndex,M=e.className,j=e.id,T=e.style,P=e.motion,$=e.width,I=e.height,D=e.children,z=e.mask,R=e.maskClosable,q=e.maskMotion,F=e.maskClassName,H=e.maskStyle,K=e.afterOpenChange,W=e.onClose,X=e.onMouseEnter,U=e.onMouseOver,L=e.onMouseLeave,B=e.onClick,J=e.onKeyDown,G=e.onKeyUp,V=e.styles,Y=e.drawerRender,Q=a.useRef(),Z=a.useRef(),ee=a.useRef();a.useImperativeHandle(t,function(){return Q.current});a.useEffect(function(){var e;m&&N&&(null===(e=Q.current)||void 0===e||e.focus({preventScroll:!0}))},[m]);var te=a.useState(!1),ne=(0,s.A)(te,2),ae=ne[0],oe=ne[1],le=a.useContext(u),re=null!==(n=null!==(o=null===(c="boolean"==typeof y?y?{}:{distance:0}:y||{})||void 0===c?void 0:c.distance)&&void 0!==o?o:null==le?void 0:le.pushDistance)&&void 0!==n?n:180,se=a.useMemo(function(){return{pushDistance:re,push:function(){oe(!0)},pull:function(){oe(!1)}}},[re]);a.useEffect(function(){var e,t;m?null==le||null===(e=le.push)||void 0===e||e.call(le):null==le||null===(t=le.pull)||void 0===t||t.call(le)},[m]),a.useEffect(function(){return function(){var e;null==le||null===(e=le.pull)||void 0===e||e.call(le)}},[]);var ce=a.createElement(g.Ay,(0,f.A)({key:"mask"},q,{visible:z&&m}),function(e,t){var n=e.className,o=e.style;return a.createElement("div",{className:l()("".concat(i,"-mask"),n,null==w?void 0:w.mask,F),style:(0,r.A)((0,r.A)((0,r.A)({},o),H),null==V?void 0:V.mask),onClick:R&&m?W:void 0,ref:t})}),ie="function"==typeof P?P(d):P,me={};if(ae&&re)switch(d){case"top":me.transform="translateY(".concat(re,"px)");break;case"bottom":me.transform="translateY(".concat(-re,"px)");break;case"left":me.transform="translateX(".concat(re,"px)");break;default:me.transform="translateX(".concat(-re,"px)")}"left"===d||"right"===d?me.width=k($):me.height=k(I);var de={onMouseEnter:X,onMouseOver:U,onMouseLeave:L,onClick:B,onKeyDown:J,onKeyUp:G},ue=a.createElement(g.Ay,(0,f.A)({key:"panel"},ie,{visible:m,forceRender:E,onVisibleChanged:function(e){null==K||K(e)},removeOnLeave:!1,leavedClassName:"".concat(i,"-content-wrapper-hidden")}),function(t,n){var o=t.className,s=t.style,c=a.createElement(x,(0,f.A)({id:j,containerRef:n,prefixCls:i,className:l()(M,null==w?void 0:w.content),style:(0,r.A)((0,r.A)({},T),null==V?void 0:V.content)},(0,v.A)(e,{aria:!0}),de),D);return a.createElement("div",(0,f.A)({className:l()("".concat(i,"-content-wrapper"),null==w?void 0:w.wrapper,o),style:(0,r.A)((0,r.A)((0,r.A)({},me),s),null==V?void 0:V.wrapper)},(0,v.A)(e,{data:!0})),Y?Y(c):c)}),pe=(0,r.A)({},O);return S&&(pe.zIndex=S),a.createElement(u.Provider,{value:se},a.createElement("div",{className:l()(i,"".concat(i,"-").concat(d),_,(0,p.A)((0,p.A)({},"".concat(i,"-open"),m),"".concat(i,"-inline"),h)),style:pe,tabIndex:-1,ref:Q,onKeyDown:function(e){var t=e.keyCode,n=e.shiftKey;switch(t){case b.A.TAB:var a;if(t===b.A.TAB)if(n||document.activeElement!==ee.current){if(n&&document.activeElement===Z.current){var o;null===(o=ee.current)||void 0===o||o.focus({preventScroll:!0})}}else null===(a=Z.current)||void 0===a||a.focus({preventScroll:!0});break;case b.A.ESC:W&&A&&(e.stopPropagation(),W(e))}}},ce,a.createElement("div",{tabIndex:0,ref:Z,style:C,"aria-hidden":"true","data-sentinel":"start"}),ue,a.createElement("div",{tabIndex:0,ref:ee,style:C,"aria-hidden":"true","data-sentinel":"end"})))}var w=a.forwardRef(A);var _=function(e){var t=e.open,n=void 0!==t&&t,o=e.prefixCls,l=void 0===o?"rc-drawer":o,m=e.placement,u=void 0===m?"right":m,p=e.autoFocus,f=void 0===p||p,g=e.keyboard,b=void 0===g||g,v=e.width,h=void 0===v?378:v,y=e.mask,E=void 0===y||y,x=e.maskClosable,N=void 0===x||x,k=e.getContainer,C=e.forceRender,A=e.afterOpenChange,_=e.destroyOnClose,O=e.onMouseEnter,S=e.onMouseOver,M=e.onMouseLeave,j=e.onClick,T=e.onKeyDown,P=e.onKeyUp,$=e.panelRef,I=a.useState(!1),D=(0,s.A)(I,2),z=D[0],R=D[1];var q=a.useState(!1),F=(0,s.A)(q,2),H=F[0],K=F[1];(0,i.A)(function(){K(!0)},[]);var W=!!H&&n,X=a.useRef(),U=a.useRef();(0,i.A)(function(){W&&(U.current=document.activeElement)},[W]);var L=a.useMemo(function(){return{panel:$}},[$]);if(!C&&!z&&!W&&_)return null;var B={onMouseEnter:O,onMouseOver:S,onMouseLeave:M,onClick:j,onKeyDown:T,onKeyUp:P},J=(0,r.A)((0,r.A)({},e),{},{open:W,prefixCls:l,placement:u,autoFocus:f,keyboard:b,width:h,mask:E,maskClosable:N,inline:!1===k,afterOpenChange:function(e){var t,n;(R(e),null==A||A(e),e||!U.current||null!==(t=X.current)&&void 0!==t&&t.contains(U.current))||(null===(n=U.current)||void 0===n||n.focus({preventScroll:!0}))},ref:X},B);return a.createElement(d.Provider,{value:L},a.createElement(c.A,{open:W||C||z,autoDestroy:!1,getContainer:k,autoLock:E&&(W||z)},a.createElement(w,J)))},O=n(2897),S=n(275),M=n(3723),j=n(235),T=n(2279),P=n(8557),$=n(8055),I=n(7072);var D=e=>{var t,n;const{prefixCls:o,title:r,footer:s,extra:c,loading:i,onClose:m,headerStyle:d,bodyStyle:u,footerStyle:p,children:f,classNames:g,styles:b}=e,v=(0,T.TP)("drawer"),h=a.useCallback(e=>a.createElement("button",{type:"button",onClick:m,className:`${o}-close`},e),[m]),[y,E]=(0,$.A)((0,$.d)(e),(0,$.d)(v),{closable:!0,closeIconRender:h}),x=a.useMemo(()=>{var e,t;return r||y?a.createElement("div",{style:Object.assign(Object.assign(Object.assign({},null===(e=v.styles)||void 0===e?void 0:e.header),d),null==b?void 0:b.header),className:l()(`${o}-header`,{[`${o}-header-close-only`]:y&&!r&&!c},null===(t=v.classNames)||void 0===t?void 0:t.header,null==g?void 0:g.header)},a.createElement("div",{className:`${o}-header-title`},E,r&&a.createElement("div",{className:`${o}-title`},r)),c&&a.createElement("div",{className:`${o}-extra`},c)):null},[y,E,c,d,o,r]),N=a.useMemo(()=>{var e,t;if(!s)return null;const n=`${o}-footer`;return a.createElement("div",{className:l()(n,null===(e=v.classNames)||void 0===e?void 0:e.footer,null==g?void 0:g.footer),style:Object.assign(Object.assign(Object.assign({},null===(t=v.styles)||void 0===t?void 0:t.footer),p),null==b?void 0:b.footer)},s)},[s,p,o]);return a.createElement(a.Fragment,null,x,a.createElement("div",{className:l()(`${o}-body`,null==g?void 0:g.body,null===(t=v.classNames)||void 0===t?void 0:t.body),style:Object.assign(Object.assign(Object.assign({},null===(n=v.styles)||void 0===n?void 0:n.body),u),null==b?void 0:b.body)},i?a.createElement(I.A,{active:!0,title:!1,paragraph:{rows:5},className:`${o}-body-skeleton`}):f),N)},z=n(2187),R=n(5905),q=n(7358),F=n(4277);const H=e=>{const t="100%";return{left:`translateX(-${t})`,right:`translateX(${t})`,top:`translateY(-${t})`,bottom:`translateY(${t})`}[e]},K=(e,t)=>({"&-enter, &-appear":Object.assign(Object.assign({},e),{"&-active":t}),"&-leave":Object.assign(Object.assign({},t),{"&-active":e})}),W=(e,t)=>Object.assign({"&-enter, &-appear, &-leave":{"&-start":{transition:"none"},"&-active":{transition:`all ${t}`}}},K({opacity:e},{opacity:1})),X=(e,t)=>[W(.7,t),K({transform:H(e)},{transform:"none"})];var U=e=>{const{componentCls:t,motionDurationSlow:n}=e;return{[t]:{[`${t}-mask-motion`]:W(0,n),[`${t}-panel-motion`]:["left","right","top","bottom"].reduce((e,t)=>Object.assign(Object.assign({},e),{[`&-${t}`]:X(t,n)}),{})}}};const L=e=>{const{borderRadiusSM:t,componentCls:n,zIndexPopup:a,colorBgMask:o,colorBgElevated:l,motionDurationSlow:r,motionDurationMid:s,paddingXS:c,padding:i,paddingLG:m,fontSizeLG:d,lineHeightLG:u,lineWidth:p,lineType:f,colorSplit:g,marginXS:b,colorIcon:v,colorIconHover:h,colorBgTextHover:y,colorBgTextActive:E,colorText:x,fontWeightStrong:N,footerPaddingBlock:k,footerPaddingInline:C,calc:A}=e,w=`${n}-content-wrapper`;return{[n]:{position:"fixed",inset:0,zIndex:a,pointerEvents:"none",color:x,"&-pure":{position:"relative",background:l,display:"flex",flexDirection:"column",[`&${n}-left`]:{boxShadow:e.boxShadowDrawerLeft},[`&${n}-right`]:{boxShadow:e.boxShadowDrawerRight},[`&${n}-top`]:{boxShadow:e.boxShadowDrawerUp},[`&${n}-bottom`]:{boxShadow:e.boxShadowDrawerDown}},"&-inline":{position:"absolute"},[`${n}-mask`]:{position:"absolute",inset:0,zIndex:a,background:o,pointerEvents:"auto"},[w]:{position:"absolute",zIndex:a,maxWidth:"100vw",transition:`all ${r}`,"&-hidden":{display:"none"}},[`&-left > ${w}`]:{top:0,bottom:0,left:{_skip_check_:!0,value:0},boxShadow:e.boxShadowDrawerLeft},[`&-right > ${w}`]:{top:0,right:{_skip_check_:!0,value:0},bottom:0,boxShadow:e.boxShadowDrawerRight},[`&-top > ${w}`]:{top:0,insetInline:0,boxShadow:e.boxShadowDrawerUp},[`&-bottom > ${w}`]:{bottom:0,insetInline:0,boxShadow:e.boxShadowDrawerDown},[`${n}-content`]:{display:"flex",flexDirection:"column",width:"100%",height:"100%",overflow:"auto",background:l,pointerEvents:"auto"},[`${n}-header`]:{display:"flex",flex:0,alignItems:"center",padding:`${(0,z.zA)(i)} ${(0,z.zA)(m)}`,fontSize:d,lineHeight:u,borderBottom:`${(0,z.zA)(p)} ${f} ${g}`,"&-title":{display:"flex",flex:1,alignItems:"center",minWidth:0,minHeight:0}},[`${n}-extra`]:{flex:"none"},[`${n}-close`]:Object.assign({display:"inline-flex",width:A(d).add(c).equal(),height:A(d).add(c).equal(),borderRadius:t,justifyContent:"center",alignItems:"center",marginInlineEnd:b,color:v,fontWeight:N,fontSize:d,fontStyle:"normal",lineHeight:1,textAlign:"center",textTransform:"none",textDecoration:"none",background:"transparent",border:0,cursor:"pointer",transition:`all ${s}`,textRendering:"auto","&:hover":{color:h,backgroundColor:y,textDecoration:"none"},"&:active":{backgroundColor:E}},(0,R.K8)(e)),[`${n}-title`]:{flex:1,margin:0,fontWeight:e.fontWeightStrong,fontSize:d,lineHeight:u},[`${n}-body`]:{flex:1,minWidth:0,minHeight:0,padding:m,overflow:"auto",[`${n}-body-skeleton`]:{width:"100%",height:"100%",display:"flex",justifyContent:"center"}},[`${n}-footer`]:{flexShrink:0,padding:`${(0,z.zA)(k)} ${(0,z.zA)(C)}`,borderTop:`${(0,z.zA)(p)} ${f} ${g}`},"&-rtl":{direction:"rtl"}}}};var B=(0,q.OF)("Drawer",e=>{const t=(0,F.oX)(e,{});return[L(t),U(t)]},e=>({zIndexPopup:e.zIndexPopupBase,footerPaddingBlock:e.paddingXS,footerPaddingInline:e.padding})),J=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(a=Object.getOwnPropertySymbols(e);o<a.length;o++)t.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]])}return n};const G={distance:180},V=e=>{const{rootClassName:t,width:n,height:o,size:r="default",mask:s=!0,push:c=G,open:i,afterOpenChange:m,onClose:d,prefixCls:u,getContainer:p,style:f,className:g,visible:b,afterVisibleChange:v,maskStyle:h,drawerStyle:y,contentWrapperStyle:E,destroyOnClose:x,destroyOnHidden:N}=e,k=J(e,["rootClassName","width","height","size","mask","push","open","afterOpenChange","onClose","prefixCls","getContainer","style","className","visible","afterVisibleChange","maskStyle","drawerStyle","contentWrapperStyle","destroyOnClose","destroyOnHidden"]),{getPopupContainer:C,getPrefixCls:A,direction:w,className:$,style:I,classNames:z,styles:R}=(0,T.TP)("drawer"),q=A("drawer",u),[F,H,K]=B(q),W=void 0===p&&C?()=>C(document.body):p,X=l()({"no-mask":!s,[`${q}-rtl`]:"rtl"===w},t,H,K);const U=a.useMemo(()=>null!=n?n:"large"===r?736:378,[n,r]),L=a.useMemo(()=>null!=o?o:"large"===r?736:378,[o,r]),V={motionName:(0,M.b)(q,"mask-motion"),motionAppear:!0,motionEnter:!0,motionLeave:!0,motionDeadline:500},Y=(0,P.f)(),[Q,Z]=(0,S.YK)("Drawer",k.zIndex),{classNames:ee={},styles:te={}}=k;return F(a.createElement(O.A,{form:!0,space:!0},a.createElement(j.A.Provider,{value:Z},a.createElement(_,Object.assign({prefixCls:q,onClose:d,maskMotion:V,motion:e=>({motionName:(0,M.b)(q,`panel-motion-${e}`),motionAppear:!0,motionEnter:!0,motionLeave:!0,motionDeadline:500})},k,{classNames:{mask:l()(ee.mask,z.mask),content:l()(ee.content,z.content),wrapper:l()(ee.wrapper,z.wrapper)},styles:{mask:Object.assign(Object.assign(Object.assign({},te.mask),h),R.mask),content:Object.assign(Object.assign(Object.assign({},te.content),y),R.content),wrapper:Object.assign(Object.assign(Object.assign({},te.wrapper),E),R.wrapper)},open:null!=i?i:b,mask:s,push:c,width:U,height:L,style:Object.assign(Object.assign({},I),f),className:l()($,g),rootClassName:X,getContainer:W,afterOpenChange:null!=m?m:v,panelRef:Y,zIndex:Q,destroyOnClose:null!=N?N:x}),a.createElement(D,Object.assign({prefixCls:q},k,{onClose:d}))))))};V._InternalPanelDoNotUseOrYouWillBeFired=e=>{const{prefixCls:t,style:n,className:o,placement:r="right"}=e,s=J(e,["prefixCls","style","className","placement"]),{getPrefixCls:c}=a.useContext(T.QO),i=c("drawer",t),[m,d,u]=B(i),p=l()(i,`${i}-pure`,`${i}-${r}`,d,u,o);return m(a.createElement("div",{className:p,style:n},a.createElement(D,Object.assign({prefixCls:i},s))))};var Y=V},6274:function(e,t,n){"use strict";n.d(t,{A:function(){return a}});const a=(0,n(1788).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},6812:function(e,t,n){"use strict";n.d(t,{L:function(){return je},A:function(){return Te}});var a=n(436),o=n(6540),l=n(9036),r=n(2941),s=n(6942),c=n.n(s),i=n(2546),m=n(2065),d=n(682),u=n(2279),p=n(4103),f=n(9106);const g=({children:e})=>{const{getPrefixCls:t}=o.useContext(u.QO),n=t("breadcrumb");return o.createElement("li",{className:`${n}-separator`,"aria-hidden":"true"},""===e?e:e||"/")};g.__ANT_BREADCRUMB_SEPARATOR=!0;var b=g,v=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(a=Object.getOwnPropertySymbols(e);o<a.length;o++)t.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]])}return n};function h(e,t,n,a){if(null==n)return null;const{className:l,onClick:r}=t,s=v(t,["className","onClick"]),i=Object.assign(Object.assign({},(0,m.A)(s,{data:!0,aria:!0})),{onClick:r});return void 0!==a?o.createElement("a",Object.assign({},i,{className:c()(`${e}-link`,l),href:a}),n):o.createElement("span",Object.assign({},i,{className:c()(`${e}-link`,l)}),n)}function y(e,t){return(n,a,o,l,r)=>{if(t)return t(n,a,o,l);const s=function(e,t){if(void 0===e.title||null===e.title)return null;const n=Object.keys(t).join("|");return"object"==typeof e.title?e.title:String(e.title).replace(new RegExp(`:(${n})`,"g"),(e,n)=>t[n]||e)}(n,a);return h(e,n,s,r)}}var E=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(a=Object.getOwnPropertySymbols(e);o<a.length;o++)t.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]])}return n};const x=e=>{const{prefixCls:t,separator:n="/",children:a,menu:l,overlay:r,dropdownProps:s,href:c}=e;const i=(e=>{if(l||r){const n=Object.assign({},s);if(l){const e=l||{},{items:t}=e,a=E(e,["items"]);n.menu=Object.assign(Object.assign({},a),{items:null==t?void 0:t.map((e,t)=>{var{key:n,title:a,label:l,path:r}=e,s=E(e,["key","title","label","path"]);let i=null!=l?l:a;return r&&(i=o.createElement("a",{href:`${c}${r}`},i)),Object.assign(Object.assign({},s),{key:null!=n?n:t,label:i})})})}else r&&(n.overlay=r);return o.createElement(f.A,Object.assign({placement:"bottom"},n),o.createElement("span",{className:`${t}-overlay-link`},e,o.createElement(p.A,null)))}return e})(a);return null!=i?o.createElement(o.Fragment,null,o.createElement("li",null,i),n&&o.createElement(b,null,n)):null},N=e=>{const{prefixCls:t,children:n,href:a}=e,l=E(e,["prefixCls","children","href"]),{getPrefixCls:r}=o.useContext(u.QO),s=r("breadcrumb",t);return o.createElement(x,Object.assign({},l,{prefixCls:s}),h(s,l,n,a))};N.__ANT_BREADCRUMB_ITEM=!0;var k=N,C=n(2187),A=n(5905),w=n(7358),_=n(4277);var O=(0,w.OF)("Breadcrumb",e=>(e=>{const{componentCls:t,iconCls:n,calc:a}=e;return{[t]:Object.assign(Object.assign({},(0,A.dF)(e)),{color:e.itemColor,fontSize:e.fontSize,[n]:{fontSize:e.iconFontSize},ol:{display:"flex",flexWrap:"wrap",margin:0,padding:0,listStyle:"none"},a:Object.assign({color:e.linkColor,transition:`color ${e.motionDurationMid}`,padding:`0 ${(0,C.zA)(e.paddingXXS)}`,borderRadius:e.borderRadiusSM,height:e.fontHeight,display:"inline-block",marginInline:a(e.marginXXS).mul(-1).equal(),"&:hover":{color:e.linkHoverColor,backgroundColor:e.colorBgTextHover}},(0,A.K8)(e)),"li:last-child":{color:e.lastItemColor},[`${t}-separator`]:{marginInline:e.separatorMargin,color:e.separatorColor},[`${t}-link`]:{[`\n          > ${n} + span,\n          > ${n} + a\n        `]:{marginInlineStart:e.marginXXS}},[`${t}-overlay-link`]:{borderRadius:e.borderRadiusSM,height:e.fontHeight,display:"inline-block",padding:`0 ${(0,C.zA)(e.paddingXXS)}`,marginInline:a(e.marginXXS).mul(-1).equal(),[`> ${n}`]:{marginInlineStart:e.marginXXS,fontSize:e.fontSizeIcon},"&:hover":{color:e.linkHoverColor,backgroundColor:e.colorBgTextHover,a:{color:e.linkHoverColor}},a:{"&:hover":{backgroundColor:"transparent"}}},[`&${e.componentCls}-rtl`]:{direction:"rtl"}})}})((0,_.oX)(e,{})),e=>({itemColor:e.colorTextDescription,lastItemColor:e.colorText,iconFontSize:e.fontSize,linkColor:e.colorTextDescription,linkHoverColor:e.colorText,separatorColor:e.colorTextDescription,separatorMargin:e.marginXS})),S=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(a=Object.getOwnPropertySymbols(e);o<a.length;o++)t.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]])}return n};function M(e){const{breadcrumbName:t,children:n}=e,a=S(e,["breadcrumbName","children"]),o=Object.assign({title:t},a);return n&&(o.menu={items:n.map(e=>{var{breadcrumbName:t}=e,n=S(e,["breadcrumbName"]);return Object.assign(Object.assign({},n),{title:t})})}),o}var j=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(a=Object.getOwnPropertySymbols(e);o<a.length;o++)t.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]])}return n};const T=e=>{const{prefixCls:t,separator:n="/",style:a,className:l,rootClassName:r,routes:s,items:p,children:f,itemRender:g,params:v={}}=e,h=j(e,["prefixCls","separator","style","className","rootClassName","routes","items","children","itemRender","params"]),{getPrefixCls:E,direction:N,breadcrumb:k}=o.useContext(u.QO);let C;const A=E("breadcrumb",t),[w,_,S]=O(A),T=function(e,t){return(0,o.useMemo)(()=>e||(t?t.map(M):null),[e,t])}(p,s);const P=y(A,g);if(T&&T.length>0){const e=[],t=p||s;C=T.map((a,l)=>{const{path:r,key:s,type:c,menu:i,overlay:d,onClick:u,className:p,separator:f,dropdownProps:g}=a,h=((e,t)=>{if(void 0===t)return t;let n=(t||"").replace(/^\//,"");return Object.keys(e).forEach(t=>{n=n.replace(`:${t}`,e[t])}),n})(v,r);void 0!==h&&e.push(h);const y=null!=s?s:l;if("separator"===c)return o.createElement(b,{key:y},f);const E={},N=l===T.length-1;i?E.menu=i:d&&(E.overlay=d);let{href:k}=a;return e.length&&void 0!==h&&(k=`#/${e.join("/")}`),o.createElement(x,Object.assign({key:y},E,(0,m.A)(a,{data:!0,aria:!0}),{className:p,dropdownProps:g,href:k,separator:N?"":n,onClick:u,prefixCls:A}),P(a,v,t,e,k))})}else if(f){const e=(0,i.A)(f).length;C=(0,i.A)(f).map((t,a)=>{if(!t)return t;const o=a===e-1;return(0,d.Ob)(t,{separator:o?"":n,key:a})})}const $=c()(A,null==k?void 0:k.className,{[`${A}-rtl`]:"rtl"===N},l,r,_,S),I=Object.assign(Object.assign({},null==k?void 0:k.style),a);return w(o.createElement("nav",Object.assign({className:$,style:I},h),o.createElement("ol",null,C)))};T.Item=k,T.Separator=b;var P=T,$=n(367),I=n(1788);const D=(0,I.A)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);var z=n(6808),R=n(7934),q=n(3164),F=n(4279),H=n(9957),K=n(696),W=n(126),X=n(2609),U=n(8603);const L=(0,I.A)("CircleHelp",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);var B=n(6305),J=n(964),G=n(8188),V=n(6816),Y=n(590),Q=n(5107),Z=n(2708);const{TextArea:ee}=H.A,te=e=>e?Array.isArray(e)?e:[e]:[],ne=e=>{let{label:t,tooltip:n,required:a,children:l}=e;return o.createElement("label",{className:"block"},o.createElement("div",{className:"flex items-center gap-2 mb-1"},o.createElement("span",{className:"text-sm font-medium text-primary"},t," ",a&&o.createElement("span",{className:"text-red-500"},"*")),o.createElement($.A,{title:n},o.createElement(L,{className:"w-4 h-4 text-secondary"}))),l)},ae=e=>{let{component:t,onChange:n,onNavigate:l,workingCopy:s,setWorkingCopy:c,editPath:i,updateComponentAtPath:m,getCurrentComponent:d}=e;if(!t)return null;const u=(0,o.useCallback)(e=>{n({...t,...e,config:{...t.config,...e.config||{}}})},[t,n]),p=(0,o.useCallback)((e,n)=>{u({config:{...t.config,[e]:n}})},[t,u]),f=((0,o.useCallback)(()=>{if(!(0,F.O6)(t))return;let e,n=te(t.config.workbench),o=n.findIndex(e=>(0,F.wb)(e));-1===o?(e={provider:"autogen_core.tools.StaticWorkbench",component_type:"workbench",config:{tools:[]},label:"Static Workbench"},n=[].concat((0,a.A)(n),[e]),o=n.length-1):e=n[o];const l=e.config,r=l.tools||[],u=[].concat((0,a.A)(r),[{provider:"autogen_core.tools.FunctionTool",component_type:"tool",version:1,component_version:1,description:"Create custom tools by wrapping standard Python functions.",label:"New Tool",config:{source_code:"def new_function():\n    pass",name:"new_function",description:"Description of the new function",global_imports:[],has_cancellation_support:!1}}]),f={...e,config:{...l,tools:u}},g=(0,a.A)(n);if(g[o]=f,p("workbench",g),s&&c&&m&&d&&i){var b;const e=m(s,i,{config:{...null===(b=d(s))||void 0===b?void 0:b.config,workbench:g}});c(e)}},[t,p,s,c,m,d,i]),(0,o.useCallback)(()=>{if(!(0,F.O6)(t))return;const e=te(t.config.workbench).filter(e=>(0,F.wb)(e)).length,n=(Date.now(),{provider:"autogen_core.tools.StaticWorkbench",component_type:"workbench",version:1,component_version:1,config:{tools:[]},label:e>0?`静态工作台 ${e+1}`:"静态工作台",description:"用于管理自定义工具的静态工作台"}),o=te(t.config.workbench);p("workbench",[].concat((0,a.A)(o),[n]))},[t,p])),g=(0,o.useCallback)(()=>{if(!(0,F.O6)(t))return;const e=te(t.config.workbench).filter(e=>(0,F.Mf)(e)).length,n={provider:"autogen_ext.tools.mcp.McpWorkbench",component_type:"workbench",version:1,component_version:1,config:{server_params:{type:"StdioServerParams",command:"server-command",args:[],env:{}}},label:e>0?`Stdio MCP 工作台 ${e+1}`:"Stdio MCP 工作台",description:"通过 stdio 连接到本地服务器的 MCP 工作台"},o=te(t.config.workbench),r=[].concat((0,a.A)(o),[n]);if(p("workbench",r),l){const e=r.length-1;l("workbench",n.label||"Stdio MCP Workbench","workbench",e)}},[t,p,l]),b=(0,o.useCallback)(()=>{if(!(0,F.O6)(t))return;const e=te(t.config.workbench).filter(e=>(0,F.Mf)(e)).length,n={provider:"autogen_ext.tools.mcp.McpWorkbench",component_type:"workbench",version:1,component_version:1,config:{server_params:{type:"StreamableHttpServerParams",url:"https://example.com/mcp",headers:{},timeout:30,sse_read_timeout:300,terminate_on_close:!0}},label:e>0?`流式 MCP 工作台 ${e+1}`:"流式 MCP 工作台",description:"通过 HTTP 流式连接到远程服务器的 MCP 工作台"},o=te(t.config.workbench),r=[].concat((0,a.A)(o),[n]);if(p("workbench",r),l){const e=r.length-1;l("workbench",n.label||"Streamable MCP Workbench","workbench",e)}},[t,p,l]),v=(0,o.useCallback)(()=>{if(!(0,F.O6)(t))return;const e=te(t.config.workbench).filter(e=>(0,F.Mf)(e)).length,n={provider:"autogen_ext.tools.mcp.McpWorkbench",component_type:"workbench",version:1,component_version:1,config:{server_params:{type:"SseServerParams",url:"https://example.com/mcp/sse",headers:{},timeout:30,sse_read_timeout:300}},label:e>0?`SSE MCP Workbench ${e+1}`:"SSE MCP Workbench",description:"An MCP workbench that connects via Server-Sent Events to remote servers"},o=te(t.config.workbench),r=[].concat((0,a.A)(o),[n]);if(p("workbench",r),l){const e=r.length-1;l("workbench",n.label||"SSE MCP Workbench","workbench",e)}},[t,p,l]),h=t.config.name||t.label||"Unnamed Agent",y=h.length>20?`${h.substring(0,30)}...`:h;return o.createElement(K.A,{defaultActiveKey:["configuration"],className:"border-0",expandIconPosition:"end",items:[{key:"details",label:o.createElement("div",{className:"flex items-center gap-2"},o.createElement(B.A,{className:"w-4 h-4 text-blue-500"}),o.createElement("span",{className:"font-medium"},"Agent Details",o.createElement("span",{className:"text-xs font-normal text-secondary ml-2"},y))),children:o.createElement(o.Fragment,null,o.createElement("div",{className:"border border-secondary rounded-lg p-3"},o.createElement("div",{className:"border-b border-secondary pb-2 mb-4"},o.createElement("h3",{className:"text-sm font-medium text-primary"},"Component Details")),o.createElement("div",{className:"space-y-4"},o.createElement("label",{className:"block"},o.createElement("span",{className:"text-sm font-medium text-primary"},"Name"),o.createElement(H.A,{value:t.label||"",onChange:e=>u({label:e.target.value}),placeholder:"Component name",className:"mt-1"})),o.createElement("label",{className:"block"},o.createElement("span",{className:"text-sm font-medium text-primary"},"Description"),o.createElement(ee,{value:t.description||"",onChange:e=>u({description:e.target.value}),placeholder:"Component description",rows:4,className:"mt-1"})),o.createElement("div",{className:"grid grid-cols-2 gap-4"},o.createElement("label",{className:"block"},o.createElement("span",{className:"text-sm font-medium text-primary"},"Version"),o.createElement(W.A,{min:1,precision:0,className:"w-full mt-1",placeholder:"e.g., 1",value:t.version||void 0,onChange:e=>u({version:e||void 0})})),o.createElement("label",{className:"block"},o.createElement("span",{className:"text-sm font-medium text-primary"},"Component Version"),o.createElement(W.A,{min:1,precision:0,className:"w-full mt-1",placeholder:"e.g., 1",value:t.component_version||void 0,onChange:e=>u({component_version:e||void 0})}))))))},{key:"configuration",label:o.createElement("div",{className:"flex items-center gap-2"},o.createElement(J.A,{className:"w-4 h-4 text-green-500"}),o.createElement("span",{className:"font-medium"},"Agent Configuration")),children:o.createElement(o.Fragment,null,o.createElement("div",{className:"border border-secondary rounded-lg p-3"},o.createElement("div",{className:"border-b border-secondary pb-2 mb-4"},o.createElement("h3",{className:"text-sm font-medium text-primary"},"Configuration")),o.createElement("div",{className:"space-y-4"},(0,F.O6)(t)&&o.createElement(o.Fragment,null,o.createElement(ne,{label:"Name",tooltip:"Name of the assistant agent",required:!0},o.createElement(H.A,{value:t.config.name,onChange:e=>p("name",e.target.value)})),o.createElement("div",{className:"space-y-2"},o.createElement("span",{className:"text-sm font-medium text-primary"},"Model Client"),t.config.model_client?o.createElement("div",{className:"bg-secondary p-1 px-2 rounded-md"},o.createElement("div",{className:"flex items-center justify-between"}," ",o.createElement("span",{className:"text-sm"},t.config.model_client.config.model),o.createElement("div",{className:"flex items-center justify-between"},t.config.model_client&&l&&o.createElement(r.Ay,{type:"text",icon:o.createElement(G.A,{className:"w-4 h-4"}),onClick:()=>{var e;return l("model",(null===(e=t.config.model_client)||void 0===e?void 0:e.label)||"","model_client")}},"Configure Model")))):o.createElement("div",{className:"text-sm text-secondary text-center bg-secondary/50 p-4 rounded-md"},"No model configured")),o.createElement(ne,{label:"System Message",tooltip:"System message for the agent"},o.createElement(ee,{rows:4,value:t.config.system_message,onChange:e=>p("system_message",e.target.value)})),o.createElement("div",{className:"flex items-center justify-between"},o.createElement("span",{className:"text-sm font-medium text-primary"},"Reflect on Tool Use"),o.createElement(X.A,{checked:t.config.reflect_on_tool_use,onChange:e=>p("reflect_on_tool_use",e)})),o.createElement("div",{className:"flex items-center justify-between"},o.createElement("span",{className:"text-sm font-medium text-primary"},"Stream Model Client"),o.createElement(X.A,{checked:t.config.model_client_stream,onChange:e=>p("model_client_stream",e)})),o.createElement(ne,{label:"Tool Call Summary Format",tooltip:"Format for tool call summaries"},o.createElement(H.A,{value:t.config.tool_call_summary_format,onChange:e=>p("tool_call_summary_format",e.target.value)}))),(0,F.fk)(t)&&o.createElement(ne,{label:"Name",tooltip:"Name of the user proxy agent",required:!0},o.createElement(H.A,{value:t.config.name,onChange:e=>p("name",e.target.value)})),(0,F.Zj)(t)&&o.createElement(o.Fragment,null,o.createElement(ne,{label:"Name",tooltip:"Name of the web surfer agent",required:!0},o.createElement(H.A,{value:t.config.name,onChange:e=>p("name",e.target.value)})),o.createElement(ne,{label:"Start Page",tooltip:"URL to start browsing from"},o.createElement(H.A,{value:t.config.start_page||"",onChange:e=>p("start_page",e.target.value)})),o.createElement(ne,{label:"Downloads Folder",tooltip:"Folder path to save downloads"},o.createElement(H.A,{value:t.config.downloads_folder||"",onChange:e=>p("downloads_folder",e.target.value)})),o.createElement(ne,{label:"Debug Directory",tooltip:"Directory for debugging logs"},o.createElement(H.A,{value:t.config.debug_dir||"",onChange:e=>p("debug_dir",e.target.value)})),o.createElement("div",{className:"space-y-2"},o.createElement("span",{className:"text-sm font-medium text-primary"},"Model Client"),t.config.model_client?o.createElement("div",{className:"bg-secondary p-1 px-2 rounded-md"},o.createElement("div",{className:"flex items-center justify-between"},o.createElement("span",{className:"text-sm"},t.config.model_client.config.model),o.createElement("div",{className:"flex items-center justify-between"},l&&o.createElement(r.Ay,{type:"text",icon:o.createElement(G.A,{className:"w-4 h-4"}),onClick:()=>{var e;return l("model",(null===(e=t.config.model_client)||void 0===e?void 0:e.label)||"","model_client")}},"Configure Model")))):o.createElement("div",{className:"text-sm text-secondary text-center bg-secondary/50 p-4 rounded-md"},"No model configured")),o.createElement("div",{className:"flex items-center justify-between"},o.createElement("span",{className:"text-sm font-medium text-primary"},"Headless"),o.createElement(X.A,{checked:t.config.headless||!1,onChange:e=>p("headless",e)})),o.createElement("div",{className:"flex items-center justify-between"},o.createElement("span",{className:"text-sm font-medium text-primary"},"Animate Actions"),o.createElement(X.A,{checked:t.config.animate_actions||!1,onChange:e=>p("animate_actions",e)})),o.createElement("div",{className:"flex items-center justify-between"},o.createElement("span",{className:"text-sm font-medium text-primary"},"Save Screenshots"),o.createElement(X.A,{checked:t.config.to_save_screenshots||!1,onChange:e=>p("to_save_screenshots",e)})),o.createElement("div",{className:"flex items-center justify-between"},o.createElement("span",{className:"text-sm font-medium text-primary"},"Use OCR"),o.createElement(X.A,{checked:t.config.use_ocr||!1,onChange:e=>p("use_ocr",e)})),o.createElement(ne,{label:"Browser Channel",tooltip:"Channel for the browser (e.g. beta, stable)"},o.createElement(H.A,{value:t.config.browser_channel||"",onChange:e=>p("browser_channel",e.target.value)})),o.createElement(ne,{label:"Browser Data Directory",tooltip:"Directory for browser profile data"},o.createElement(H.A,{value:t.config.browser_data_dir||"",onChange:e=>p("browser_data_dir",e.target.value)})),o.createElement("div",{className:"flex items-center justify-between"},o.createElement("span",{className:"text-sm font-medium text-primary"},"Resize Viewport"),o.createElement(X.A,{checked:t.config.to_resize_viewport||!1,onChange:e=>p("to_resize_viewport",e)}))))),(0,F.O6)(t)&&o.createElement("div",{className:"border border-secondary rounded-lg p-3"},o.createElement("div",{className:"border-b border-secondary pb-2 mb-4"},o.createElement("h3",{className:"text-sm font-medium text-primary"},"Workbench Management")),o.createElement("div",{className:"space-y-4"},o.createElement("div",{className:"flex items-center justify-between"},o.createElement("span",{className:"text-sm font-medium text-primary"},"Workbenches (",te(t.config.workbench).length,")"),o.createElement(U.A,{menu:{items:[{key:"static",label:o.createElement("div",null,o.createElement("div",null,"Static Workbench"),o.createElement("div",{className:"text-xs text-gray-500"},"Collection of custom tools")),icon:o.createElement(V.A,{className:"w-4 h-4"}),onClick:f},{type:"divider"},{key:"stdio-mcp",label:o.createElement("div",null,o.createElement("div",null,"Stdio MCP Workbench"),o.createElement("div",{className:"text-xs text-gray-500"},"Connect to local MCP servers")),icon:o.createElement(Y.A,{className:"w-4 h-4"}),onClick:g},{key:"sse-mcp",label:o.createElement("div",null,o.createElement("div",null,"SSE MCP Workbench"),o.createElement("div",{className:"text-xs text-gray-500"},"Connect via Server-Sent Events")),icon:o.createElement(Y.A,{className:"w-4 h-4"}),onClick:v},{key:"streamable-mcp",label:o.createElement("div",null,o.createElement("div",null,"Streamable MCP Workbench"),o.createElement("div",{className:"text-xs text-gray-500"},"Connect via HTTP streaming")),icon:o.createElement(Y.A,{className:"w-4 h-4"}),onClick:b}]},trigger:["click"]},o.createElement(r.Ay,{type:"dashed",size:"small",icon:o.createElement(Y.A,{className:"w-4 h-4"})},"Add Workbench ",o.createElement(Q.A,{className:"w-3 h-3 ml-1"})))),o.createElement("div",{className:"space-y-3"},te(t.config.workbench).map((e,n)=>{const s=(0,F.wb)(e)?(null===(c=e.config.tools)||void 0===c?void 0:c.length)||0:null;var c;return o.createElement("div",{key:n,className:"bg-secondary/30 p-4 rounded-lg border border-gray-200"},o.createElement("div",{className:"flex items-center justify-between"},o.createElement("div",{className:"flex items-center gap-2"},o.createElement("div",null,o.createElement("div",{className:"flex items-center gap-2"},o.createElement("span",{className:"text-sm font-medium"},e.label||e.provider.split(".").pop()),null!==s&&o.createElement("span",{className:"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded"},s," ",1===s?"tool":"tools"),(0,F.Mf)(e)&&o.createElement("span",{className:"text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded"},"MCP Server")),o.createElement("div",{className:"text-xs text-gray-500 mt-1"},"Provider: ",e.provider))),o.createElement("div",{className:"flex items-center gap-2"},l&&o.createElement(r.Ay,{type:"text",icon:o.createElement(G.A,{className:"w-4 h-4"}),onClick:()=>{l("workbench",e.label||`workbench-${n}`,"workbench",n)}}),o.createElement(r.Ay,{type:"text",danger:!0,icon:o.createElement(Z.A,{className:"w-4 h-4"}),onClick:()=>{const e=te(t.config.workbench),o=(0,a.A)(e);o.splice(n,1),p("workbench",o)}}))),(0,F.wb)(e)&&e.config.tools&&e.config.tools.length>0&&o.createElement("div",{className:"mt-3"},o.createElement("div",{className:"flex flex-wrap gap-1"},e.config.tools.map((e,t)=>o.createElement("span",{key:t,className:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200"},o.createElement(V.A,{className:"w-3 h-3 mr-1"}),e.config.name||e.label||`Tool ${t+1}`)))))}),0===te(t.config.workbench).length&&o.createElement("div",{className:"text-sm text-secondary text-center bg-secondary/50 p-4 rounded-md"},"No workbenches configured. Add a workbench to provide tools to this agent.")))))}]})};var oe=n(9314),le=n(5144);const re=e=>{let{label:t,tooltip:n,children:a}=e;return o.createElement("label",{className:"block"},o.createElement("div",{className:"flex items-center gap-2 mb-1"},o.createElement("span",{className:"text-sm font-medium text-primary"},t),o.createElement($.A,{title:n},o.createElement(L,{className:"w-4 h-4 text-secondary"}))),a)},se={temperature:{label:"Temperature",tooltip:"Controls randomness in the model's output. Higher values make output more random, lower values make it more focused.",component:W.A,props:{min:0,max:2,step:.1,className:"w-full"}},max_tokens:{label:"Max Tokens",tooltip:"Maximum length of the model's output in tokens",component:W.A,props:{min:1,className:"w-full"}},top_p:{label:"Top P",tooltip:"Controls diversity via nucleus sampling. Lower values make output more focused, higher values make it more diverse.",component:W.A,props:{min:0,max:1,step:.1,className:"w-full"}},top_k:{label:"Top K",tooltip:"Limits the next token selection to the K most likely tokens. Only used by some models.",component:W.A,props:{min:0,className:"w-full"}},frequency_penalty:{label:"Frequency Penalty",tooltip:"Decreases the model's likelihood to repeat the same information. Values range from -2.0 to 2.0.",component:W.A,props:{min:-2,max:2,step:.1,className:"w-full"}},presence_penalty:{label:"Presence Penalty",tooltip:"Increases the model's likelihood to talk about new topics. Values range from -2.0 to 2.0.",component:W.A,props:{min:-2,max:2,step:.1,className:"w-full"}},stop:{label:"Stop Sequences",tooltip:"Sequences where the model will stop generating further tokens",component:oe.A,props:{mode:"tags",placeholder:"Enter stop sequences",className:"w-full"}},stop_sequences:{label:"Stop Sequences",tooltip:"Sequences where the model will stop generating further tokens",component:oe.A,props:{mode:"tags",placeholder:"Enter stop sequences",className:"w-full"}},model:{label:"Model",tooltip:"The name of the model to use",component:H.A,props:{required:!0}},api_key:{label:"API Key",tooltip:"Your API key",component:H.A.Password,props:{}},organization:{label:"Organization",tooltip:"Optional: Your OpenAI organization ID",component:H.A,props:{}},base_url:{label:"Base URL",tooltip:"Optional: Custom base URL for API requests",component:H.A,props:{}},timeout:{label:"Timeout",tooltip:"Request timeout in seconds",component:W.A,props:{min:1,className:"w-full"}},max_retries:{label:"Max Retries",tooltip:"Maximum number of retry attempts for failed requests",component:W.A,props:{min:0,className:"w-full"}},azure_endpoint:{label:"Azure Endpoint",tooltip:"Your Azure OpenAI service endpoint URL",component:H.A,props:{required:!0}},azure_deployment:{label:"Azure Deployment",tooltip:"The name of your Azure OpenAI model deployment",component:H.A,props:{}},api_version:{label:"API Version",tooltip:"Azure OpenAI API version (e.g., 2023-05-15)",component:H.A,props:{required:!0}},azure_ad_token:{label:"Azure AD Token",tooltip:"Optional: Azure Active Directory token for authentication",component:H.A.Password,props:{}},tools:{label:"Tools",tooltip:"JSON definition of tools the model can use",component:le.A,props:{rows:4,placeholder:"Enter tools JSON definition"},transform:{fromConfig:e=>e?JSON.stringify(e,null,2):"",toConfig:e=>{try{return e?JSON.parse(e):null}catch(t){return e}}}},tool_choice:{label:"Tool Choice",tooltip:"Controls whether the model uses tools ('auto', 'any', 'none', or JSON object)",component:oe.A,props:{options:[{label:"Auto",value:"auto"},{label:"Any",value:"any"},{label:"None",value:"none"},{label:"Custom",value:"custom"}],className:"w-full"},transform:{fromConfig:e=>"object"==typeof e?"custom":e||"auto",toConfig:(e,t)=>"custom"!==e?e:"object"==typeof t?t:{type:"function"}}},metadata:{label:"Metadata",tooltip:"Optional: Custom metadata to include with the request",component:le.A,props:{rows:2,placeholder:"Enter metadata as JSON"},transform:{fromConfig:e=>e?JSON.stringify(e,null,2):"",toConfig:e=>{try{return e?JSON.parse(e):null}catch(t){return e}}}}},ce={openai:{modelConfig:["model","api_key","organization","base_url","timeout","max_retries"],modelParams:["temperature","max_tokens","top_p","frequency_penalty","presence_penalty","stop"]},azure:{modelConfig:["model","api_key","azure_endpoint","azure_deployment","api_version","azure_ad_token","timeout","max_retries"],modelParams:["temperature","max_tokens","top_p","frequency_penalty","presence_penalty","stop"]},anthropic:{modelConfig:["model","api_key","base_url","timeout","max_retries"],modelParams:["temperature","max_tokens","top_p","top_k","stop_sequences","tools","tool_choice","metadata"]}},ie=e=>{let{component:t,onChange:n}=e,l=null;if((0,F.Ed)(t)?l="openai":(0,F.Jz)(t)?l="azure":(0,F.wx)(t)&&(l="anthropic"),!l)return null;const r=(0,o.useCallback)(e=>{n({...t,...e,config:{...t.config,...e.config||{}}})},[t,n]),s=(0,o.useCallback)((e,n)=>{var a;const o=se[e],l=null!==(a=o.transform)&&void 0!==a&&a.toConfig?o.transform.toConfig(n,t.config[e]):n;r({config:{...t.config,[e]:l}})},[t,r]),c=e=>o.createElement("div",{className:"space-y-4"},e.map(e=>(e=>{var n;const a=se[e];if(!a)return null;const l=null!==(n=a.transform)&&void 0!==n&&n.fromConfig?a.transform.fromConfig(t.config[e]):t.config[e];return o.createElement(re,{key:e,label:a.label,tooltip:a.tooltip},o.createElement(a.component,Object.assign({},a.props,{value:l,onChange:t=>{const n=t&&t.target?t.target.value:t;s(e,n)}})))})(e)));return o.createElement(K.A,{defaultActiveKey:["details","configuration","parameters"],className:"border-0",expandIconPosition:"end",items:[{key:"details",label:o.createElement("div",{className:"flex items-center gap-2"},o.createElement(B.A,{className:"w-4 h-4 text-blue-500"}),o.createElement("span",{className:"font-medium"},"Component Details")),children:o.createElement("div",{className:"space-y-4"},o.createElement("label",{className:"block"},o.createElement("span",{className:"text-sm font-medium text-primary"},"Name"),o.createElement(H.A,{value:t.label||"",onChange:e=>r({label:e.target.value}),placeholder:"Model name",className:"mt-1"})),o.createElement("label",{className:"block"},o.createElement("span",{className:"text-sm font-medium text-primary"},"Description"),o.createElement(le.A,{value:t.description||"",onChange:e=>r({description:e.target.value}),placeholder:"Model description",rows:4,className:"mt-1"})))},{key:"configuration",label:o.createElement("div",{className:"flex items-center gap-2"},o.createElement(J.A,{className:"w-4 h-4 text-green-500"}),o.createElement("span",{className:"font-medium"},"azure"===l?"Azure Configuration":"Model Configuration")),children:c(ce[l].modelConfig)},{key:"parameters",label:o.createElement("div",{className:"flex items-center gap-2"},o.createElement(V.A,{className:"w-4 h-4 text-orange-500"}),o.createElement("span",{className:"font-medium"},"Model Parameters")),children:c(ce[l].modelParams)}].concat((0,a.A)("anthropic"===l&&"custom"===t.config.tool_choice?[{key:"tools",label:o.createElement("div",{className:"flex items-center gap-2"},o.createElement(V.A,{className:"w-4 h-4 text-purple-500"}),o.createElement("span",{className:"font-medium"},"Custom Tool Choice")),children:o.createElement("div",{className:"space-y-4"},o.createElement(le.A,{value:JSON.stringify(t.config.tool_choice,null,2),onChange:e=>{try{const t=JSON.parse(e.target.value);s("tool_choice",t)}catch(t){console.error("Invalid JSON for tool_choice")}},placeholder:"Enter tool choice configuration as JSON",rows:4}))}]:[]))})};var me=n(5680);const{TextArea:de}=H.A,ue=e=>{let{component:t,onChange:n,onNavigate:a}=e;if(!(0,F.HX)(t)&&!(0,F.Il)(t)&&!(0,F.gU)(t))return null;const l=(0,o.useCallback)(e=>{n({...t,...e,config:{...t.config,...e.config||{}}})},[t,n]),s=(0,o.useCallback)((e,n)=>{(0,F.HX)(t)?l({config:{...t.config,[e]:n}}):(0,F.Il)(t)?l({config:{...t.config,[e]:n}}):(0,F.gU)(t)&&l({config:{...t.config,[e]:n}})},[t,l]);return o.createElement(K.A,{defaultActiveKey:["details","configuration"],className:"border-0",expandIconPosition:"end",items:[{key:"details",label:o.createElement("div",{className:"flex items-center gap-2"},o.createElement(B.A,{className:"w-4 h-4 text-blue-500"}),o.createElement("span",{className:"font-medium"},"Component Details")),children:o.createElement("div",{className:"space-y-4"},o.createElement("label",{className:"block"},o.createElement("span",{className:"text-sm font-medium text-primary"},"Name"),o.createElement(H.A,{value:t.label||"",onChange:e=>l({label:e.target.value}),placeholder:"Team name",className:"mt-1"})),o.createElement("label",{className:"block"},o.createElement("span",{className:"text-sm font-medium text-primary"},"Description"),o.createElement(de,{value:t.description||"",onChange:e=>l({description:e.target.value}),placeholder:"Team description",rows:4,className:"mt-1"})))},{key:"configuration",label:o.createElement("div",{className:"flex items-center gap-2"},o.createElement(J.A,{className:"w-4 h-4 text-green-500"}),o.createElement("span",{className:"font-medium"},"Team Configuration")),children:o.createElement("div",{className:"space-y-4"},(0,F.HX)(t)&&o.createElement("div",{className:"space-y-4"},o.createElement("label",{className:"block"},o.createElement("span",{className:"text-sm font-medium text-primary"},"Selector Prompt"),o.createElement(de,{value:t.config.selector_prompt||"",onChange:e=>s("selector_prompt",e.target.value),placeholder:"Prompt for the selector",rows:4,className:"mt-1"})),o.createElement("div",{className:"space-y-2"},o.createElement("h3",{className:"text-sm font-medium text-primary"},"Model"),o.createElement("div",{className:"bg-secondary p-4 rounded-md"},t.config.model_client?o.createElement("div",{className:"flex items-center justify-between"},o.createElement("span",{className:"text-sm"},t.config.model_client.config.model),a&&o.createElement(r.Ay,{type:"text",icon:o.createElement(G.A,{className:"w-4 h-4"}),onClick:()=>{var e;return a("model",(null===(e=t.config.model_client)||void 0===e?void 0:e.label)||"","model_client")}})):o.createElement("div",{className:"text-sm text-secondary text-center"},"No model configured")))),(0,F.gU)(t)&&o.createElement("div",{className:"space-y-4"},o.createElement("div",{className:"space-y-2"},o.createElement("h3",{className:"text-sm font-medium text-primary"},"Team Type"),o.createElement("div",{className:"bg-secondary p-4 rounded-md"},o.createElement("div",{className:"text-sm text-secondary"},"Swarm team uses handoff messages to transfer control between agents. Each agent specifies which agents they can hand off to."))),o.createElement("label",{className:"block"},o.createElement("span",{className:"text-sm font-medium text-primary"},"Emit Team Events"),o.createElement("div",{className:"mt-1"},o.createElement("input",{type:"checkbox",checked:t.config.emit_team_events||!1,onChange:e=>s("emit_team_events",e.target.checked),className:"mr-2"}),o.createElement("span",{className:"text-sm text-secondary"},"Enable team event emission for debugging and monitoring")))),o.createElement("div",{className:"space-y-2 mt-4"},o.createElement("h3",{className:"text-sm font-medium text-primary"},"Termination Condition"),o.createElement("div",{className:"bg-secondary p-4 rounded-md"},t.config.termination_condition?o.createElement("div",{className:"flex items-center justify-between"},o.createElement("div",{className:"flex items-center gap-2"},o.createElement(me.A,{className:"w-4 h-4 text-secondary"}),o.createElement("span",{className:"text-sm"},t.config.termination_condition.label||t.config.termination_condition.component_type)),a&&o.createElement(r.Ay,{type:"text",icon:o.createElement(G.A,{className:"w-4 h-4"}),onClick:()=>{var e;return a("termination",(null===(e=t.config.termination_condition)||void 0===e?void 0:e.label)||"","termination_condition")}})):o.createElement("div",{className:"text-sm text-secondary text-center"},"No termination condition configured"))))}]})};var pe=n(8073),fe=n(8107),ge=n(2697),be=n(3140);const ve={MAX_MESSAGE:{label:"Max Messages",provider:F.xq.MAX_MESSAGE,defaultConfig:{max_messages:10,include_agent_event:!1}},TEXT_MENTION:{label:"Text Mention",provider:F.xq.TEXT_MENTION,defaultConfig:{text:"TERMINATE"}},STOP_MESSAGE:{label:"Stop Message",provider:F.xq.STOP_MESSAGE,defaultConfig:{}},TOKEN_USAGE:{label:"Token Usage",provider:F.xq.TOKEN_USAGE,defaultConfig:{max_total_token:1e3}},TIMEOUT:{label:"Timeout",provider:F.xq.TIMEOUT,defaultConfig:{timeout_seconds:300}},HANDOFF:{label:"Handoff",provider:F.xq.HANDOFF,defaultConfig:{target:""}},SOURCE_MATCH:{label:"Source Match",provider:F.xq.SOURCE_MATCH,defaultConfig:{sources:[]}},TEXT_MESSAGE:{label:"Text Message",provider:F.xq.TEXT_MESSAGE,defaultConfig:{source:""}},EXTERNAL:{label:"External",provider:F.xq.EXTERNAL,defaultConfig:{}}},he=e=>{let{label:t,tooltip:n,children:a}=e;return o.createElement("label",{className:"block"},o.createElement("div",{className:"flex items-center gap-2 mb-1"},o.createElement("span",{className:"text-sm font-medium text-gray-700"},t),o.createElement($.A,{title:n},o.createElement(L,{className:"w-4 h-4 text-gray-400"}))),a)},ye=e=>{let{component:t,onChange:n,onNavigate:l}=e;const{0:s,1:c}=(0,o.useState)(!1),{0:i,1:m}=(0,o.useState)("");if(!t)return null;const d=(0,o.useCallback)(e=>{n({...t,...e,config:{...t.config,...e.config||{}}})},[t,n]),u=()=>{if(!i||!(0,F.qt)(t))return;const e=(e=>{const t=ve[e];return{provider:t.provider,component_type:"termination",version:1,component_version:1,description:`${t.label} termination condition`,label:t.label,config:t.defaultConfig}})(i),n=t.config.conditions||[];d({config:{conditions:[].concat((0,a.A)(n),[e])}}),c(!1),m("")};var p;if((0,F.qt)(t))return o.createElement(K.A,{defaultActiveKey:["conditions"],className:"border-0",expandIconPosition:"end",items:[{key:"conditions",label:o.createElement("div",{className:"flex items-center gap-2"},o.createElement(me.A,{className:"w-4 h-4 text-blue-500"}),o.createElement("span",{className:"font-medium"},"Termination Conditions")),children:o.createElement("div",{className:"space-y-4"},o.createElement("div",{className:"flex justify-between items-center"},o.createElement(r.Ay,{type:"dashed",onClick:()=>c(!0),icon:o.createElement(Y.A,{className:"w-4 h-4"}),className:"w-full"},"Add Condition")),s&&o.createElement("div",{className:"border rounded p-4 space-y-4"},o.createElement(he,{label:"Condition Type",tooltip:"Select the type of termination condition to add"},o.createElement(oe.A,{value:i,onChange:m,className:"w-full"},Object.entries(ve).map(e=>{let[t,n]=e;return o.createElement(oe.A.Option,{key:t,value:t},n.label)}))),o.createElement(r.Ay,{onClick:u,disabled:!i,className:"w-full"},"Add")),o.createElement("div",{className:"space-y-2"},null===(p=t.config.conditions)||void 0===p?void 0:p.map((e,n)=>o.createElement("div",{key:n,className:"flex items-center gap-2"},o.createElement(r.Ay,{onClick:()=>null==l?void 0:l(e.component_type,e.label||"","conditions"),className:"w-full flex justify-between items-center"},o.createElement("span",null,e.label||`Condition ${n+1}`),o.createElement(G.A,{className:"w-4 h-4"})),o.createElement(r.Ay,{type:"text",danger:!0,icon:o.createElement(be.A,{className:"w-4 h-4"}),onClick:()=>(e=>{if(!(0,F.qt)(t))return;const n=(0,a.A)(t.config.conditions);n.splice(e,1),d({config:{conditions:n}})})(n)})))))}]});if((0,F.WR)(t))return o.createElement(K.A,{defaultActiveKey:["configuration"],className:"border-0",expandIconPosition:"end",items:[{key:"configuration",label:o.createElement("div",{className:"flex items-center gap-2"},o.createElement(J.A,{className:"w-4 h-4 text-green-500"}),o.createElement("span",{className:"font-medium"},"Max Messages Configuration")),children:o.createElement("div",{className:"space-y-4"},o.createElement(he,{label:"Max Messages",tooltip:"Maximum number of messages before termination"},o.createElement(W.A,{min:1,value:t.config.max_messages,onChange:e=>d({config:{max_messages:e}}),className:"w-full"})),o.createElement(he,{label:"Include Agent Events",tooltip:"Include agent events in the message count, not just chat messages"},o.createElement(ge.A,{checked:t.config.include_agent_event||!1,onChange:e=>d({config:{include_agent_event:e.target.checked}})},"Include agent events in message count")))}]});if((0,F.K1)(t))return o.createElement(K.A,{defaultActiveKey:["configuration"],className:"border-0",expandIconPosition:"end",items:[{key:"configuration",label:o.createElement("div",{className:"flex items-center gap-2"},o.createElement(J.A,{className:"w-4 h-4 text-purple-500"}),o.createElement("span",{className:"font-medium"},"Text Mention Configuration")),children:o.createElement(he,{label:"Termination Text",tooltip:"Text that triggers termination when mentioned"},o.createElement(H.A,{value:t.config.text,onChange:e=>d({config:{text:e.target.value}})}))}]});if((0,F.Uy)(t))return o.createElement(K.A,{defaultActiveKey:["configuration"],className:"border-0",expandIconPosition:"end",items:[{key:"configuration",label:o.createElement("div",{className:"flex items-center gap-2"},o.createElement(J.A,{className:"w-4 h-4 text-red-500"}),o.createElement("span",{className:"font-medium"},"Stop Message Configuration")),children:o.createElement("div",{className:"text-sm text-gray-600"},"This termination condition triggers when a StopMessage is received. No additional configuration is required.")}]});if((0,F.Tj)(t)){const e=t;return o.createElement(K.A,{defaultActiveKey:["configuration"],className:"border-0",expandIconPosition:"end",items:[{key:"configuration",label:o.createElement("div",{className:"flex items-center gap-2"},o.createElement(J.A,{className:"w-4 h-4 text-yellow-500"}),o.createElement("span",{className:"font-medium"},"Token Usage Configuration")),children:o.createElement("div",{className:"space-y-4"},o.createElement(he,{label:"Max Total Tokens",tooltip:"Maximum total number of tokens allowed"},o.createElement(W.A,{min:1,value:e.config.max_total_token,onChange:e=>d({config:{max_total_token:e}}),className:"w-full",placeholder:"e.g., 1000"})),o.createElement(he,{label:"Max Prompt Tokens",tooltip:"Maximum number of prompt tokens allowed"},o.createElement(W.A,{min:1,value:e.config.max_prompt_token,onChange:e=>d({config:{max_prompt_token:e}}),className:"w-full",placeholder:"e.g., 800"})),o.createElement(he,{label:"Max Completion Tokens",tooltip:"Maximum number of completion tokens allowed"},o.createElement(W.A,{min:1,value:e.config.max_completion_token,onChange:e=>d({config:{max_completion_token:e}}),className:"w-full",placeholder:"e.g., 200"})))}]})}if((0,F.KX)(t)){const e=t;return o.createElement(K.A,{defaultActiveKey:["configuration"],className:"border-0",expandIconPosition:"end",items:[{key:"configuration",label:o.createElement("div",{className:"flex items-center gap-2"},o.createElement(me.A,{className:"w-4 h-4 text-orange-500"}),o.createElement("span",{className:"font-medium"},"Timeout Configuration")),children:o.createElement(he,{label:"Timeout (seconds)",tooltip:"Maximum duration in seconds before termination"},o.createElement(W.A,{min:1,value:e.config.timeout_seconds,onChange:e=>d({config:{timeout_seconds:e}}),className:"w-full",placeholder:"e.g., 300"}))}]})}if((0,F.RG)(t)){const e=t;return o.createElement(K.A,{defaultActiveKey:["configuration"],className:"border-0",expandIconPosition:"end",items:[{key:"configuration",label:o.createElement("div",{className:"flex items-center gap-2"},o.createElement(B.A,{className:"w-4 h-4 text-blue-500"}),o.createElement("span",{className:"font-medium"},"Handoff Configuration")),children:o.createElement(he,{label:"Target Agent",tooltip:"Agent to handoff to before termination"},o.createElement(H.A,{value:e.config.target,onChange:e=>d({config:{target:e.target.value}}),placeholder:"e.g., agent_name"}))}]})}if((0,F.D$)(t)){var f;const e=t;return o.createElement(K.A,{defaultActiveKey:["configuration"],className:"border-0",expandIconPosition:"end",items:[{key:"configuration",label:o.createElement("div",{className:"flex items-center gap-2"},o.createElement(J.A,{className:"w-4 h-4 text-indigo-500"}),o.createElement("span",{className:"font-medium"},"Source Match Configuration")),children:o.createElement(he,{label:"Source Names",tooltip:"List of source names to match (comma-separated)"},o.createElement(H.A,{value:(null===(f=e.config.sources)||void 0===f?void 0:f.join(", "))||"",onChange:e=>d({config:{sources:e.target.value.split(",").map(e=>e.trim()).filter(e=>e.length>0)}}),placeholder:"e.g., agent1, agent2"}))}]})}if((0,F.nb)(t)){const e=t;return o.createElement(K.A,{defaultActiveKey:["configuration"],className:"border-0",expandIconPosition:"end",items:[{key:"configuration",label:o.createElement("div",{className:"flex items-center gap-2"},o.createElement(J.A,{className:"w-4 h-4 text-pink-500"}),o.createElement("span",{className:"font-medium"},"Text Message Configuration")),children:o.createElement(he,{label:"Source Filter (optional)",tooltip:"Filter to only terminate on text messages from specific source"},o.createElement(H.A,{value:e.config.source||"",onChange:e=>d({config:{source:e.target.value||void 0}}),placeholder:"e.g., agent_name (leave empty for any source)"}))}]})}return(0,F.oD)(t)?o.createElement(K.A,{defaultActiveKey:["configuration"],className:"border-0",expandIconPosition:"end",items:[{key:"configuration",label:o.createElement("div",{className:"flex items-center gap-2"},o.createElement(J.A,{className:"w-4 h-4 text-gray-500"}),o.createElement("span",{className:"font-medium"},"External Termination Configuration")),children:o.createElement("div",{className:"text-sm text-gray-600"},"This termination condition is controlled externally by calling the set() method. No additional configuration is required.")}]}):null};var Ee=n(181),xe=n.n(Ee),Ne=n(9872),ke=n(2197),Ce=n(4471);const Ae=(0,I.A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);var we=n(2102),_e=n(7799),Oe=n(6813);var Se=e=>{let{result:t,onClose:n}=e;const[a,l]=o.useState(!1),r=t.status?" border-green-200":"  border-red-200",s=t.status?"text-green-500":"text-red-500";return o.createElement("div",{className:`mb-6 rounded-lg border text-primary ${r} overflow-hidden`},o.createElement("div",{className:"p-4"},o.createElement("div",{className:"flex items-start justify-between"},o.createElement("div",{className:"flex items-center gap-2"},t.status?o.createElement(Ce.A,{className:`w-5 h-5 ${s}`}):o.createElement(Ae,{className:`w-5 h-5 ${s}`}),o.createElement("span",{className:"font-medium text-primary"},t.message)),o.createElement("div",{className:"flex items-center gap-2"},o.createElement("button",{onClick:()=>l(!a),className:"p-1 hover:bg-black/5 rounded-md"},a?o.createElement(we.A,{className:"w-4 h-4"}):o.createElement(Q.A,{className:"w-4 h-4"})),o.createElement("button",{onClick:n,className:"p-1 hover:bg-black/5 rounded-md"},o.createElement(_e.A,{className:"w-4 h-4"})))),a&&t.logs&&t.logs.length>0&&o.createElement("div",{className:"mt-4"},o.createElement("div",{className:"flex items-center gap-2 mb-2"},o.createElement(Oe.A,{className:"w-4 h-4"}),o.createElement("span",{className:"text-sm font-medium"},"Execution Logs")),o.createElement("pre",{className:"bg-secondary text-primary p-4 rounded-md text-sm font-mono overflow-x-auto"},t.logs.join("\n"))),a&&t.data&&o.createElement("div",{className:"mt-4"},o.createElement("div",{className:"flex items-center gap-2 mb-2"},o.createElement(Oe.A,{className:"w-4 h-4"}),o.createElement("span",{className:"text-sm font-medium"},"Additional Data")),o.createElement("pre",{className:"bg-secondary text-primary p-4 rounded-md text-sm font-mono overflow-x-auto"},JSON.stringify(t.data,null,2)))))};const Me=e=>e?Array.isArray(e)?e:[e]:[],je=e=>{let{component:t,onChange:n,onClose:s,navigationDepth:c=!1}=e;const{0:i,1:m}=(0,o.useState)([]),{0:d,1:u}=(0,o.useState)(Object.assign({},t)),{0:p,1:f}=(0,o.useState)(!1),{0:g,1:b}=(0,o.useState)(!1),{0:v,1:h}=(0,o.useState)(null),[y,E]=l.Ay.useMessage(),x=(0,o.useRef)(null);o.useEffect(()=>{u(t),m([]),h(null)},[t]);const N=(0,o.useCallback)(e=>i.reduce((e,t)=>{if(!e)return null;let n=e.config[t.parentField];if("workbench"===t.parentField&&n&&(n=Me(n)),"tools"===t.parentField&&!n&&(0,F.fF)(e)&&(0,F.O6)(e)){const t=e.config,o=Me(t.workbench).find(e=>(0,F.wb)(e));var a;if(o)n=null===(a=o.config)||void 0===a?void 0:a.tools}return Array.isArray(n)?"number"==typeof t.index&&t.index>=0&&t.index<n.length?n[t.index]:n.find(e=>e.label===t.id||e.config&&"name"in e.config&&e.config.name===t.id)||null:n||null},e),[i]),k=(0,o.useCallback)((e,t,n)=>{if(0===t.length)return{...e,...n,config:{...e.config,...n.config||{}}};const[o,...l]=t;let r=e.config[o.parentField];"workbench"===o.parentField&&r&&(r=Me(r));let s=!1;if("tools"===o.parentField&&!r&&(0,F.fF)(e)&&(0,F.O6)(e)){const t=e.config,n=Me(t.workbench).find(e=>(0,F.wb)(e));var c;if(n)r=null===(c=n.config)||void 0===c?void 0:c.tools,s=!0}const i=e=>Array.isArray(e)?"number"==typeof o.index&&o.index>=0&&o.index<e.length?e.map((e,t)=>t===o.index?k(e,l,n):e):e.map(e=>"component_type"in e&&(e.label===o.id||"name"in e.config&&e.config.name===o.id)?k(e,l,n):e):e&&"component_type"in e?k(e,l,n):e;return{...e,config:{...e.config,...s&&(0,F.fF)(e)&&(0,F.O6)(e)?(()=>{const t=e.config,n=Me(t.workbench),o=n.findIndex(e=>(0,F.wb)(e));if(-1!==o){const e=(0,a.A)(n);return e[o]={...n[o],config:{...n[o].config,tools:i(r)}},{workbench:e}}return{}})():{[o.parentField]:i(r)}}}},[]),C=(0,o.useCallback)(e=>{const t=k(d,i,e);u(t)},[d,i,k]),A=(0,o.useCallback)((e,t,n,o)=>{c&&m(l=>[].concat((0,a.A)(l),[{componentType:e,id:t,parentField:n,index:o}]))},[c]),w=(0,o.useCallback)(()=>{m(e=>e.slice(0,-1))},[]),_=(0,o.useCallback)(xe()(e=>{try{const t=JSON.parse(e);u(t)}catch(t){console.error("Invalid JSON",t)}},500),[]),O=N(d)||d,S=(0,o.useCallback)(()=>{const e={component:O,onChange:C};return(0,F.VZ)(O)?o.createElement(ue,{component:O,onChange:C,onNavigate:A}):(0,F.fF)(O)?o.createElement(ae,{component:O,onChange:C,onNavigate:A}):(0,F.nL)(O)?o.createElement(ie,{component:O,onChange:C}):(0,F.gG)(O)?o.createElement(pe.e,e):(0,F.ls)(O)?o.createElement(fe.dv,e):(0,F.U9)(O)?o.createElement(ye,{component:O,onChange:C,onNavigate:A}):null},[O,C,A]),M=o.useMemo(()=>[{title:d.label||"Root"}].concat((0,a.A)(i.map(e=>({title:e.id})))),[d.label,i]),j=(0,o.useCallback)(()=>{console.log("working copy",d.config),n(d),null==s||s()},[d,n,s]),T=(0,F.nL)(O);return o.createElement("div",{className:"flex flex-col h-full"},E,o.createElement("div",{className:"flex items-center gap-4 mb-6"},c&&i.length>0&&o.createElement(r.Ay,{onClick:w,icon:o.createElement(D,{className:"w-4 h-4"}),type:"text"}),o.createElement("div",{className:"flex-1"},o.createElement(P,{items:M})),T&&o.createElement($.A,{title:"Test Component"},o.createElement(r.Ay,{onClick:async()=>{b(!0),h(null);try{const e=await ke.gw.testComponent(O);h(e),e.status?y.success("Component test passed!"):y.error("Component test failed!")}catch(e){console.error("Test component error:",e),h({status:!1,message:e instanceof Error?e.message:"Test failed",logs:[]}),y.error("Failed to test component")}finally{b(!1)}},loading:g,type:"default",className:"flex items-center gap-2 text-xs mr-0",icon:o.createElement("div",{className:"relative"},o.createElement(z.A,{className:"w-4 h-4 text-accent"}),v&&o.createElement("div",{className:`absolute top-0 right-0 w-2 h-2 ${v.status?"bg-green-500":"bg-red-500"} rounded-full`}))},"Test")),o.createElement(r.Ay,{onClick:()=>f(e=>!e),type:"default",className:"flex text-accent items-center gap-2 text-xs"},p?o.createElement(o.Fragment,null,o.createElement(R.A,{className:"w-4 text-accent h-4 mr-1 inline-block"}),"Form Editor"):o.createElement(o.Fragment,null,o.createElement(q.A,{className:"w-4 text-accent h-4 mr-1 inline-block"}),"JSON Editor"))),v&&o.createElement(Se,{result:v,onClose:()=>h(null)}),p?o.createElement("div",{className:"flex-1 overflow-y-auto"},o.createElement(Ne.T,{editorRef:x,value:JSON.stringify(d,null,2),onChange:_,language:"json",minimap:!0})):o.createElement("div",{className:"flex-1 overflow-y-auto"},S()),s&&o.createElement("div",{className:"flex justify-end gap-2 mt-6 pt-4 border-t border-secondary"},o.createElement(r.Ay,{onClick:s},"Cancel"),o.createElement(r.Ay,{type:"primary",onClick:j},"Save Changes")))};var Te=je},7073:function(e,t,n){"use strict";n.d(t,{A:function(){return a}});const a=(0,n(1788).A)("Brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},7934:function(e,t,n){"use strict";n.d(t,{A:function(){return a}});const a=(0,n(1788).A)("RectangleEllipsis",[["rect",{width:"20",height:"12",x:"2",y:"6",rx:"2",key:"9lu3g6"}],["path",{d:"M12 12h.01",key:"1mp3jc"}],["path",{d:"M17 12h.01",key:"1m0b6t"}],["path",{d:"M7 12h.01",key:"eqddd0"}]])}}]);
//# sourceMappingURL=fabc42e99a8596640f645875d0a5301ab9478215-ba9b4d34d93e82e9b258.js.map