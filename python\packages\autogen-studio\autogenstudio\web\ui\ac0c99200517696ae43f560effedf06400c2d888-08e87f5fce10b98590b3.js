/*! For license information please see ac0c99200517696ae43f560effedf06400c2d888-08e87f5fce10b98590b3.js.LICENSE.txt */
"use strict";(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[557],{126:function(e,t,n){n.d(t,{A:function(){return ye}});var a=n(6540),r=n(4103),o=n(8168),i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"}}]},name:"up",theme:"outlined"},l=n(7064),s=function(e,t){return a.createElement(l.A,(0,o.A)({},e,{ref:t,icon:i}))};var c=a.forwardRef(s),d=n(6942),u=n.n(d),m=n(4467),p=n(2284),g=n(5544),f=n(3986),h=n(3029),b=n(2901);function v(){return"function"==typeof BigInt}function y(e){return!e&&0!==e&&!Number.isNaN(e)||!String(e).trim()}function E(e){var t=e.trim(),n=t.startsWith("-");n&&(t=t.slice(1)),(t=t.replace(/(\.\d*[^0])0*$/,"$1").replace(/\.0*$/,"").replace(/^0+/,"")).startsWith(".")&&(t="0".concat(t));var a=t||"0",r=a.split("."),o=r[0]||"0",i=r[1]||"0";"0"===o&&"0"===i&&(n=!1);var l=n?"-":"";return{negative:n,negativeStr:l,trimStr:a,integerStr:o,decimalStr:i,fullStr:"".concat(l).concat(a)}}function x(e){var t=String(e);return!Number.isNaN(Number(t))&&t.includes("e")}function N(e){var t=String(e);if(x(e)){var n=Number(t.slice(t.indexOf("e-")+2)),a=t.match(/\.(\d+)/);return null!=a&&a[1]&&(n+=a[1].length),n}return t.includes(".")&&A(t)?t.length-t.indexOf(".")-1:0}function w(e){var t=String(e);if(x(e)){if(e>Number.MAX_SAFE_INTEGER)return String(v()?BigInt(e).toString():Number.MAX_SAFE_INTEGER);if(e<Number.MIN_SAFE_INTEGER)return String(v()?BigInt(e).toString():Number.MIN_SAFE_INTEGER);t=e.toFixed(N(t))}return E(t).fullStr}function A(e){return"number"==typeof e?!Number.isNaN(e):!!e&&(/^\s*-?\d+(\.\d+)?\s*$/.test(e)||/^\s*-?\d+\.\s*$/.test(e)||/^\s*-?\.\d+\s*$/.test(e))}var C=function(){function e(t){if((0,h.A)(this,e),(0,m.A)(this,"origin",""),(0,m.A)(this,"negative",void 0),(0,m.A)(this,"integer",void 0),(0,m.A)(this,"decimal",void 0),(0,m.A)(this,"decimalLen",void 0),(0,m.A)(this,"empty",void 0),(0,m.A)(this,"nan",void 0),y(t))this.empty=!0;else if(this.origin=String(t),"-"===t||Number.isNaN(t))this.nan=!0;else{var n=t;if(x(n)&&(n=Number(n)),A(n="string"==typeof n?n:w(n))){var a=E(n);this.negative=a.negative;var r=a.trimStr.split(".");this.integer=BigInt(r[0]);var o=r[1]||"0";this.decimal=BigInt(o),this.decimalLen=o.length}else this.nan=!0}}return(0,b.A)(e,[{key:"getMark",value:function(){return this.negative?"-":""}},{key:"getIntegerStr",value:function(){return this.integer.toString()}},{key:"getDecimalStr",value:function(){return this.decimal.toString().padStart(this.decimalLen,"0")}},{key:"alignDecimal",value:function(e){var t="".concat(this.getMark()).concat(this.getIntegerStr()).concat(this.getDecimalStr().padEnd(e,"0"));return BigInt(t)}},{key:"negate",value:function(){var t=new e(this.toString());return t.negative=!t.negative,t}},{key:"cal",value:function(t,n,a){var r=Math.max(this.getDecimalStr().length,t.getDecimalStr().length),o=n(this.alignDecimal(r),t.alignDecimal(r)).toString(),i=a(r),l=E(o),s=l.negativeStr,c=l.trimStr,d="".concat(s).concat(c.padStart(i+1,"0"));return new e("".concat(d.slice(0,-i),".").concat(d.slice(-i)))}},{key:"add",value:function(t){if(this.isInvalidate())return new e(t);var n=new e(t);return n.isInvalidate()?this:this.cal(n,function(e,t){return e+t},function(e){return e})}},{key:"multi",value:function(t){var n=new e(t);return this.isInvalidate()||n.isInvalidate()?new e(NaN):this.cal(n,function(e,t){return e*t},function(e){return 2*e})}},{key:"isEmpty",value:function(){return this.empty}},{key:"isNaN",value:function(){return this.nan}},{key:"isInvalidate",value:function(){return this.isEmpty()||this.isNaN()}},{key:"equals",value:function(e){return this.toString()===(null==e?void 0:e.toString())}},{key:"lessEquals",value:function(e){return this.add(e.negate().toString()).toNumber()<=0}},{key:"toNumber",value:function(){return this.isNaN()?NaN:Number(this.toString())}},{key:"toString",value:function(){return!(arguments.length>0&&void 0!==arguments[0])||arguments[0]?this.isInvalidate()?"":E("".concat(this.getMark()).concat(this.getIntegerStr(),".").concat(this.getDecimalStr())).fullStr:this.origin}}]),e}(),S=function(){function e(t){(0,h.A)(this,e),(0,m.A)(this,"origin",""),(0,m.A)(this,"number",void 0),(0,m.A)(this,"empty",void 0),y(t)?this.empty=!0:(this.origin=String(t),this.number=Number(t))}return(0,b.A)(e,[{key:"negate",value:function(){return new e(-this.toNumber())}},{key:"add",value:function(t){if(this.isInvalidate())return new e(t);var n=Number(t);if(Number.isNaN(n))return this;var a=this.number+n;if(a>Number.MAX_SAFE_INTEGER)return new e(Number.MAX_SAFE_INTEGER);if(a<Number.MIN_SAFE_INTEGER)return new e(Number.MIN_SAFE_INTEGER);var r=Math.max(N(this.number),N(n));return new e(a.toFixed(r))}},{key:"multi",value:function(t){var n=Number(t);if(this.isInvalidate()||Number.isNaN(n))return new e(NaN);var a=this.number*n;if(a>Number.MAX_SAFE_INTEGER)return new e(Number.MAX_SAFE_INTEGER);if(a<Number.MIN_SAFE_INTEGER)return new e(Number.MIN_SAFE_INTEGER);var r=Math.max(N(this.number),N(n));return new e(a.toFixed(r))}},{key:"isEmpty",value:function(){return this.empty}},{key:"isNaN",value:function(){return Number.isNaN(this.number)}},{key:"isInvalidate",value:function(){return this.isEmpty()||this.isNaN()}},{key:"equals",value:function(e){return this.toNumber()===(null==e?void 0:e.toNumber())}},{key:"lessEquals",value:function(e){return this.add(e.negate().toString()).toNumber()<=0}},{key:"toNumber",value:function(){return this.number}},{key:"toString",value:function(){return!(arguments.length>0&&void 0!==arguments[0])||arguments[0]?this.isInvalidate()?"":w(this.number):this.origin}}]),e}();function k(e){return v()?new C(e):new S(e)}function $(e,t,n){var a=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(""===e)return"";var r=E(e),o=r.negativeStr,i=r.integerStr,l=r.decimalStr,s="".concat(t).concat(l),c="".concat(o).concat(i);if(n>=0){var d=Number(l[n]);return d>=5&&!a?$(k(e).add("".concat(o,"0.").concat("0".repeat(n)).concat(10-d)).toString(),t,n,a):0===n?c:"".concat(c).concat(t).concat(l.padEnd(n,"0").slice(0,n))}return".0"===s?c:"".concat(c).concat(s)}var O=k,I=n(8491),z=n(981);var M=n(8719),T=n(8210);var R=n(8430),P=function(){var e=(0,a.useState)(!1),t=(0,g.A)(e,2),n=t[0],r=t[1];return(0,z.A)(function(){r((0,R.A)())},[]),n},j=n(5371);function _(e){var t=e.prefixCls,n=e.upNode,r=e.downNode,i=e.upDisabled,l=e.downDisabled,s=e.onStep,c=a.useRef(),d=a.useRef([]),p=a.useRef();p.current=s;var g=function(){clearTimeout(c.current)},f=function(e,t){e.preventDefault(),g(),p.current(t),c.current=setTimeout(function e(){p.current(t),c.current=setTimeout(e,200)},600)};if(a.useEffect(function(){return function(){g(),d.current.forEach(function(e){return j.A.cancel(e)})}},[]),P())return null;var h="".concat(t,"-handler"),b=u()(h,"".concat(h,"-up"),(0,m.A)({},"".concat(h,"-up-disabled"),i)),v=u()(h,"".concat(h,"-down"),(0,m.A)({},"".concat(h,"-down-disabled"),l)),y=function(){return d.current.push((0,j.A)(g))},E={unselectable:"on",role:"button",onMouseUp:y,onMouseLeave:y};return a.createElement("div",{className:"".concat(h,"-wrap")},a.createElement("span",(0,o.A)({},E,{onMouseDown:function(e){f(e,!0)},"aria-label":"Increase Value","aria-disabled":i,className:b}),n||a.createElement("span",{unselectable:"on",className:"".concat(t,"-handler-up-inner")})),a.createElement("span",(0,o.A)({},E,{onMouseDown:function(e){f(e,!1)},"aria-label":"Decrease Value","aria-disabled":l,className:v}),r||a.createElement("span",{unselectable:"on",className:"".concat(t,"-handler-down-inner")})))}function B(e){var t="number"==typeof e?w(e):E(e).fullStr;return t.includes(".")?E(t.replace(/(\d)\.(\d)/g,"$1$2.")).fullStr:e+"0"}var H=n(1980),D=["prefixCls","className","style","min","max","step","defaultValue","value","disabled","readOnly","upHandler","downHandler","keyboard","changeOnWheel","controls","classNames","stringMode","parser","formatter","precision","decimalSeparator","onChange","onInput","onPressEnter","onStep","changeOnBlur","domRef"],L=["disabled","style","prefixCls","value","prefix","suffix","addonBefore","addonAfter","className","classNames"],F=function(e,t){return e||t.isEmpty()?t.toString():t.toNumber()},W=function(e){var t=O(e);return t.isInvalidate()?null:t},q=a.forwardRef(function(e,t){var n=e.prefixCls,r=e.className,i=e.style,l=e.min,s=e.max,c=e.step,d=void 0===c?1:c,h=e.defaultValue,b=e.value,v=e.disabled,y=e.readOnly,E=e.upHandler,x=e.downHandler,C=e.keyboard,S=e.changeOnWheel,k=void 0!==S&&S,I=e.controls,R=void 0===I||I,P=(e.classNames,e.stringMode),H=e.parser,L=e.formatter,q=e.precision,X=e.decimalSeparator,G=e.onChange,U=e.onInput,V=e.onPressEnter,Y=e.onStep,K=e.changeOnBlur,Z=void 0===K||K,J=e.domRef,Q=(0,f.A)(e,D),ee="".concat(n,"-input"),te=a.useRef(null),ne=a.useState(!1),ae=(0,g.A)(ne,2),re=ae[0],oe=ae[1],ie=a.useRef(!1),le=a.useRef(!1),se=a.useRef(!1),ce=a.useState(function(){return O(null!=b?b:h)}),de=(0,g.A)(ce,2),ue=de[0],me=de[1];var pe=a.useCallback(function(e,t){if(!t)return q>=0?q:Math.max(N(e),N(d))},[q,d]),ge=a.useCallback(function(e){var t=String(e);if(H)return H(t);var n=t;return X&&(n=n.replace(X,".")),n.replace(/[^\w.-]+/g,"")},[H,X]),fe=a.useRef(""),he=a.useCallback(function(e,t){if(L)return L(e,{userTyping:t,input:String(fe.current)});var n="number"==typeof e?w(e):e;if(!t){var a=pe(n,t);if(A(n)&&(X||a>=0))n=$(n,X||".",a)}return n},[L,pe,X]),be=a.useState(function(){var e=null!=h?h:b;return ue.isInvalidate()&&["string","number"].includes((0,p.A)(e))?Number.isNaN(e)?"":e:he(ue.toString(),!1)}),ve=(0,g.A)(be,2),ye=ve[0],Ee=ve[1];function xe(e,t){Ee(he(e.isInvalidate()?e.toString(!1):e.toString(!t),t))}fe.current=ye;var Ne,we,Ae,Ce,Se,ke=a.useMemo(function(){return W(s)},[s,q]),$e=a.useMemo(function(){return W(l)},[l,q]),Oe=a.useMemo(function(){return!(!ke||!ue||ue.isInvalidate())&&ke.lessEquals(ue)},[ke,ue]),Ie=a.useMemo(function(){return!(!$e||!ue||ue.isInvalidate())&&ue.lessEquals($e)},[$e,ue]),ze=(Ne=te.current,we=re,Ae=(0,a.useRef)(null),[function(){try{var e=Ne.selectionStart,t=Ne.selectionEnd,n=Ne.value,a=n.substring(0,e),r=n.substring(t);Ae.current={start:e,end:t,value:n,beforeTxt:a,afterTxt:r}}catch(o){}},function(){if(Ne&&Ae.current&&we)try{var e=Ne.value,t=Ae.current,n=t.beforeTxt,a=t.afterTxt,r=t.start,o=e.length;if(e.startsWith(n))o=n.length;else if(e.endsWith(a))o=e.length-Ae.current.afterTxt.length;else{var i=n[r-1],l=e.indexOf(i,r-1);-1!==l&&(o=l+1)}Ne.setSelectionRange(o,o)}catch(s){(0,T.Ay)(!1,"Something warning of cursor restore. Please fire issue about this: ".concat(s.message))}}]),Me=(0,g.A)(ze,2),Te=Me[0],Re=Me[1],Pe=function(e){return ke&&!e.lessEquals(ke)?ke:$e&&!$e.lessEquals(e)?$e:null},je=function(e){return!Pe(e)},_e=function(e,t){var n,a=e,r=je(a)||a.isEmpty();if(a.isEmpty()||t||(a=Pe(a)||a,r=!0),!y&&!v&&r){var o=a.toString(),i=pe(o,t);return i>=0&&(a=O($(o,".",i)),je(a)||(a=O($(o,".",i,!0)))),a.equals(ue)||(n=a,void 0===b&&me(n),null==G||G(a.isEmpty()?null:F(P,a)),void 0===b&&xe(a,t)),a}return ue},Be=(Ce=(0,a.useRef)(0),Se=function(){j.A.cancel(Ce.current)},(0,a.useEffect)(function(){return Se},[]),function(e){Se(),Ce.current=(0,j.A)(function(){e()})}),He=function e(t){if(Te(),fe.current=t,Ee(t),!le.current){var n=ge(t),a=O(n);a.isNaN()||_e(a,!0)}null==U||U(t),Be(function(){var n=t;H||(n=t.replace(/。/g,".")),n!==t&&e(n)})},De=function(e){var t;if(!(e&&Oe||!e&&Ie)){ie.current=!1;var n=O(se.current?B(d):d);e||(n=n.negate());var a=(ue||O(0)).add(n.toString()),r=_e(a,!1);null==Y||Y(F(P,r),{offset:se.current?B(d):d,type:e?"up":"down"}),null===(t=te.current)||void 0===t||t.focus()}},Le=function(e){var t,n=O(ge(ye));t=n.isNaN()?_e(ue,e):_e(n,e),void 0!==b?xe(ue,!1):t.isNaN()||xe(t,!1)};a.useEffect(function(){if(k&&re){var e=function(e){De(e.deltaY<0),e.preventDefault()},t=te.current;if(t)return t.addEventListener("wheel",e,{passive:!1}),function(){return t.removeEventListener("wheel",e)}}});return(0,z.o)(function(){ue.isInvalidate()||xe(ue,!1)},[q,L]),(0,z.o)(function(){var e=O(b);me(e);var t=O(ge(ye));e.equals(t)&&ie.current&&!L||xe(e,ie.current)},[b]),(0,z.o)(function(){L&&Re()},[ye]),a.createElement("div",{ref:J,className:u()(n,r,(0,m.A)((0,m.A)((0,m.A)((0,m.A)((0,m.A)({},"".concat(n,"-focused"),re),"".concat(n,"-disabled"),v),"".concat(n,"-readonly"),y),"".concat(n,"-not-a-number"),ue.isNaN()),"".concat(n,"-out-of-range"),!ue.isInvalidate()&&!je(ue))),style:i,onFocus:function(){oe(!0)},onBlur:function(){Z&&Le(!1),oe(!1),ie.current=!1},onKeyDown:function(e){var t=e.key,n=e.shiftKey;ie.current=!0,se.current=n,"Enter"===t&&(le.current||(ie.current=!1),Le(!1),null==V||V(e)),!1!==C&&!le.current&&["Up","ArrowUp","Down","ArrowDown"].includes(t)&&(De("Up"===t||"ArrowUp"===t),e.preventDefault())},onKeyUp:function(){ie.current=!1,se.current=!1},onCompositionStart:function(){le.current=!0},onCompositionEnd:function(){le.current=!1,He(te.current.value)},onBeforeInput:function(){ie.current=!0}},R&&a.createElement(_,{prefixCls:n,upNode:E,downNode:x,upDisabled:Oe,downDisabled:Ie,onStep:De}),a.createElement("div",{className:"".concat(ee,"-wrap")},a.createElement("input",(0,o.A)({autoComplete:"off",role:"spinbutton","aria-valuemin":l,"aria-valuemax":s,"aria-valuenow":ue.isInvalidate()?null:ue.toString(),step:d},Q,{ref:(0,M.K4)(te,t),className:ee,value:ye,onChange:function(e){He(e.target.value)},disabled:v,readOnly:y}))))}),X=a.forwardRef(function(e,t){var n=e.disabled,r=e.style,i=e.prefixCls,l=void 0===i?"rc-input-number":i,s=e.value,c=e.prefix,d=e.suffix,u=e.addonBefore,m=e.addonAfter,p=e.className,g=e.classNames,h=(0,f.A)(e,L),b=a.useRef(null),v=a.useRef(null),y=a.useRef(null),E=function(e){y.current&&(0,H.F4)(y.current,e)};return a.useImperativeHandle(t,function(){return e=y.current,t={focus:E,nativeElement:b.current.nativeElement||v.current},"undefined"!=typeof Proxy&&e?new Proxy(e,{get:function(e,n){if(t[n])return t[n];var a=e[n];return"function"==typeof a?a.bind(e):a}}):e;var e,t}),a.createElement(I.a,{className:p,triggerFocus:E,prefixCls:l,value:s,disabled:n,style:r,prefix:c,suffix:d,addonAfter:m,addonBefore:u,classNames:g,components:{affixWrapper:"div",groupWrapper:"div",wrapper:"div",groupAddon:"div"},ref:b},a.createElement(q,(0,o.A)({prefixCls:l,disabled:n,ref:y,domRef:v,className:null==g?void 0:g.input},h)))});var G=X,U=n(2897),V=n(8182),Y=n(2279),K=n(867),Z=n(8119),J=n(934),Q=n(829),ee=n(4241),te=n(124),ne=n(6327),ae=n(2187),re=n(1594),oe=n(6716),ie=n(9222),le=n(5905),se=n(5974),ce=n(7358),de=n(4277),ue=n(2616);const me=({componentCls:e,borderRadiusSM:t,borderRadiusLG:n},a)=>{const r="lg"===a?n:t;return{[`&-${a}`]:{[`${e}-handler-wrap`]:{borderStartEndRadius:r,borderEndEndRadius:r},[`${e}-handler-up`]:{borderStartEndRadius:r},[`${e}-handler-down`]:{borderEndEndRadius:r}}}},pe=e=>{const{componentCls:t,lineWidth:n,lineType:a,borderRadius:r,inputFontSizeSM:o,inputFontSizeLG:i,controlHeightLG:l,controlHeightSM:s,colorError:c,paddingInlineSM:d,paddingBlockSM:u,paddingBlockLG:m,paddingInlineLG:p,colorIcon:g,motionDurationMid:f,handleHoverColor:h,handleOpacity:b,paddingInline:v,paddingBlock:y,handleBg:E,handleActiveBg:x,colorTextDisabled:N,borderRadiusSM:w,borderRadiusLG:A,controlWidth:C,handleBorderColor:S,filledHandleBg:k,lineHeightLG:$,calc:O}=e;return[{[t]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},(0,le.dF)(e)),(0,re.wj)(e)),{display:"inline-block",width:C,margin:0,padding:0,borderRadius:r}),(0,ie.Eb)(e,{[`${t}-handler-wrap`]:{background:E,[`${t}-handler-down`]:{borderBlockStart:`${(0,ae.zA)(n)} ${a} ${S}`}}})),(0,ie.sA)(e,{[`${t}-handler-wrap`]:{background:k,[`${t}-handler-down`]:{borderBlockStart:`${(0,ae.zA)(n)} ${a} ${S}`}},"&:focus-within":{[`${t}-handler-wrap`]:{background:E}}})),(0,ie.aP)(e,{[`${t}-handler-wrap`]:{background:E,[`${t}-handler-down`]:{borderBlockStart:`${(0,ae.zA)(n)} ${a} ${S}`}}})),(0,ie.lB)(e)),{"&-rtl":{direction:"rtl",[`${t}-input`]:{direction:"rtl"}},"&-lg":{padding:0,fontSize:i,lineHeight:$,borderRadius:A,[`input${t}-input`]:{height:O(l).sub(O(n).mul(2)).equal(),padding:`${(0,ae.zA)(m)} ${(0,ae.zA)(p)}`}},"&-sm":{padding:0,fontSize:o,borderRadius:w,[`input${t}-input`]:{height:O(s).sub(O(n).mul(2)).equal(),padding:`${(0,ae.zA)(u)} ${(0,ae.zA)(d)}`}},"&-out-of-range":{[`${t}-input-wrap`]:{input:{color:c}}},"&-group":Object.assign(Object.assign(Object.assign({},(0,le.dF)(e)),(0,re.XM)(e)),{"&-wrapper":Object.assign(Object.assign(Object.assign({display:"inline-block",textAlign:"start",verticalAlign:"top",[`${t}-affix-wrapper`]:{width:"100%"},"&-lg":{[`${t}-group-addon`]:{borderRadius:A,fontSize:e.fontSizeLG}},"&-sm":{[`${t}-group-addon`]:{borderRadius:w}}},(0,ie.nm)(e)),(0,ie.Vy)(e)),{[`&:not(${t}-compact-first-item):not(${t}-compact-last-item)${t}-compact-item`]:{[`${t}, ${t}-group-addon`]:{borderRadius:0}},[`&:not(${t}-compact-last-item)${t}-compact-first-item`]:{[`${t}, ${t}-group-addon`]:{borderStartEndRadius:0,borderEndEndRadius:0}},[`&:not(${t}-compact-first-item)${t}-compact-last-item`]:{[`${t}, ${t}-group-addon`]:{borderStartStartRadius:0,borderEndStartRadius:0}}})}),[`&-disabled ${t}-input`]:{cursor:"not-allowed"},[t]:{"&-input":Object.assign(Object.assign(Object.assign(Object.assign({},(0,le.dF)(e)),{width:"100%",padding:`${(0,ae.zA)(y)} ${(0,ae.zA)(v)}`,textAlign:"start",backgroundColor:"transparent",border:0,borderRadius:r,outline:0,transition:`all ${f} linear`,appearance:"textfield",fontSize:"inherit"}),(0,re.j_)(e.colorTextPlaceholder)),{'&[type="number"]::-webkit-inner-spin-button, &[type="number"]::-webkit-outer-spin-button':{margin:0,appearance:"none"}})},[`&:hover ${t}-handler-wrap, &-focused ${t}-handler-wrap`]:{width:e.handleWidth,opacity:1}})},{[t]:Object.assign(Object.assign(Object.assign({[`${t}-handler-wrap`]:{position:"absolute",insetBlockStart:0,insetInlineEnd:0,width:e.handleVisibleWidth,opacity:b,height:"100%",borderStartStartRadius:0,borderStartEndRadius:r,borderEndEndRadius:r,borderEndStartRadius:0,display:"flex",flexDirection:"column",alignItems:"stretch",transition:`all ${f}`,overflow:"hidden",[`${t}-handler`]:{display:"flex",alignItems:"center",justifyContent:"center",flex:"auto",height:"40%",[`\n              ${t}-handler-up-inner,\n              ${t}-handler-down-inner\n            `]:{marginInlineEnd:0,fontSize:e.handleFontSize}}},[`${t}-handler`]:{height:"50%",overflow:"hidden",color:g,fontWeight:"bold",lineHeight:0,textAlign:"center",cursor:"pointer",borderInlineStart:`${(0,ae.zA)(n)} ${a} ${S}`,transition:`all ${f} linear`,"&:active":{background:x},"&:hover":{height:"60%",[`\n              ${t}-handler-up-inner,\n              ${t}-handler-down-inner\n            `]:{color:h}},"&-up-inner, &-down-inner":Object.assign(Object.assign({},(0,le.Nk)()),{color:g,transition:`all ${f} linear`,userSelect:"none"})},[`${t}-handler-up`]:{borderStartEndRadius:r},[`${t}-handler-down`]:{borderEndEndRadius:r}},me(e,"lg")),me(e,"sm")),{"&-disabled, &-readonly":{[`${t}-handler-wrap`]:{display:"none"},[`${t}-input`]:{color:"inherit"}},[`\n          ${t}-handler-up-disabled,\n          ${t}-handler-down-disabled\n        `]:{cursor:"not-allowed"},[`\n          ${t}-handler-up-disabled:hover &-handler-up-inner,\n          ${t}-handler-down-disabled:hover &-handler-down-inner\n        `]:{color:N}})}]},ge=e=>{const{componentCls:t,paddingBlock:n,paddingInline:a,inputAffixPadding:r,controlWidth:o,borderRadiusLG:i,borderRadiusSM:l,paddingInlineLG:s,paddingInlineSM:c,paddingBlockLG:d,paddingBlockSM:u,motionDurationMid:m}=e;return{[`${t}-affix-wrapper`]:Object.assign(Object.assign({[`input${t}-input`]:{padding:`${(0,ae.zA)(n)} 0`}},(0,re.wj)(e)),{position:"relative",display:"inline-flex",alignItems:"center",width:o,padding:0,paddingInlineStart:a,"&-lg":{borderRadius:i,paddingInlineStart:s,[`input${t}-input`]:{padding:`${(0,ae.zA)(d)} 0`}},"&-sm":{borderRadius:l,paddingInlineStart:c,[`input${t}-input`]:{padding:`${(0,ae.zA)(u)} 0`}},[`&:not(${t}-disabled):hover`]:{zIndex:1},"&-focused, &:focus":{zIndex:1},[`&-disabled > ${t}-disabled`]:{background:"transparent"},[`> div${t}`]:{width:"100%",border:"none",outline:"none",[`&${t}-focused`]:{boxShadow:"none !important"}},"&::before":{display:"inline-block",width:0,visibility:"hidden",content:'"\\a0"'},[`${t}-handler-wrap`]:{zIndex:2},[t]:{position:"static",color:"inherit","&-prefix, &-suffix":{display:"flex",flex:"none",alignItems:"center",pointerEvents:"none"},"&-prefix":{marginInlineEnd:r},"&-suffix":{insetBlockStart:0,insetInlineEnd:0,height:"100%",marginInlineEnd:a,marginInlineStart:r,transition:`margin ${m}`}},[`&:hover ${t}-handler-wrap, &-focused ${t}-handler-wrap`]:{width:e.handleWidth,opacity:1},[`&:not(${t}-affix-wrapper-without-controls):hover ${t}-suffix`]:{marginInlineEnd:e.calc(e.handleWidth).add(a).equal()}}),[`${t}-underlined`]:{borderRadius:0}}};var fe=(0,ce.OF)("InputNumber",e=>{const t=(0,de.oX)(e,(0,oe.C)(e));return[pe(t),ge(t),(0,se.G)(t)]},e=>{var t;const n=null!==(t=e.handleVisible)&&void 0!==t?t:"auto",a=e.controlHeightSM-2*e.lineWidth;return Object.assign(Object.assign({},(0,oe.b)(e)),{controlWidth:90,handleWidth:a,handleFontSize:e.fontSize/2,handleVisible:n,handleActiveBg:e.colorFillAlter,handleBg:e.colorBgContainer,filledHandleBg:new ue.Y(e.colorFillSecondary).onBackground(e.colorBgContainer).toHexString(),handleHoverColor:e.colorPrimary,handleBorderColor:e.colorBorder,handleOpacity:!0===n?1:0,handleVisibleWidth:!0===n?a:0})},{unitless:{handleOpacity:!0},resetFont:!1}),he=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]])}return n};const be=a.forwardRef((e,t)=>{const{getPrefixCls:n,direction:o}=a.useContext(Y.QO),i=a.useRef(null);a.useImperativeHandle(t,()=>i.current);const{className:l,rootClassName:s,size:d,disabled:m,prefixCls:p,addonBefore:g,addonAfter:f,prefix:h,suffix:b,bordered:v,readOnly:y,status:E,controls:x,variant:N}=e,w=he(e,["className","rootClassName","size","disabled","prefixCls","addonBefore","addonAfter","prefix","suffix","bordered","readOnly","status","controls","variant"]),A=n("input-number",p),C=(0,J.A)(A),[S,k,$]=fe(A,C),{compactSize:O,compactItemClassnames:I}=(0,ne.RQ)(A,o);let z=a.createElement(c,{className:`${A}-handler-up-inner`}),M=a.createElement(r.A,{className:`${A}-handler-down-inner`});const T="boolean"==typeof x?x:void 0;"object"==typeof x&&(z=void 0===x.upIcon?z:a.createElement("span",{className:`${A}-handler-up-inner`},x.upIcon),M=void 0===x.downIcon?M:a.createElement("span",{className:`${A}-handler-down-inner`},x.downIcon));const{hasFeedback:R,status:P,isFormItemInput:j,feedbackIcon:_}=a.useContext(ee.$W),B=(0,V.v)(P,E),H=(0,Q.A)(e=>{var t;return null!==(t=null!=d?d:O)&&void 0!==t?t:e}),D=a.useContext(Z.A),L=null!=m?m:D,[F,W]=(0,te.A)("inputNumber",N,v),q=R&&a.createElement(a.Fragment,null,_),X=u()({[`${A}-lg`]:"large"===H,[`${A}-sm`]:"small"===H,[`${A}-rtl`]:"rtl"===o,[`${A}-in-form-item`]:j},k),K=`${A}-group`;return S(a.createElement(G,Object.assign({ref:i,disabled:L,className:u()($,C,l,s,I),upHandler:z,downHandler:M,prefixCls:A,readOnly:y,controls:T,prefix:h,suffix:q||b,addonBefore:g&&a.createElement(U.A,{form:!0,space:!0},g),addonAfter:f&&a.createElement(U.A,{form:!0,space:!0},f),classNames:{input:X,variant:u()({[`${A}-${F}`]:W},(0,V.L)(A,B,R)),affixWrapper:u()({[`${A}-affix-wrapper-sm`]:"small"===H,[`${A}-affix-wrapper-lg`]:"large"===H,[`${A}-affix-wrapper-rtl`]:"rtl"===o,[`${A}-affix-wrapper-without-controls`]:!1===x||L},k),wrapper:u()({[`${K}-rtl`]:"rtl"===o},k),groupWrapper:u()({[`${A}-group-wrapper-sm`]:"small"===H,[`${A}-group-wrapper-lg`]:"large"===H,[`${A}-group-wrapper-rtl`]:"rtl"===o,[`${A}-group-wrapper-${F}`]:W},(0,V.L)(`${A}-group-wrapper`,B,R),k)}},w)))}),ve=be;ve._InternalPanelDoNotUseOrYouWillBeFired=e=>a.createElement(K.Ay,{theme:{components:{InputNumber:{handleVisible:!0}}}},a.createElement(be,Object.assign({},e)));var ye=ve},590:function(e,t,n){n.d(t,{A:function(){return a}});const a=(0,n(1788).A)("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},696:function(e,t,n){n.d(t,{A:function(){return U}});var a=n(6540),r=n(8e3),o=n(6942),i=n.n(o),l=n(8168),s=n(436),c=n(5544),d=n(2284),u=n(2533),m=n(8210),p=n(3986),g=n(2546),f=n(9379),h=n(4467),b=n(754),v=n(6928),y=a.forwardRef(function(e,t){var n=e.prefixCls,r=e.forceRender,o=e.className,l=e.style,s=e.children,d=e.isActive,u=e.role,m=e.classNames,p=e.styles,g=a.useState(d||r),f=(0,c.A)(g,2),b=f[0],v=f[1];return a.useEffect(function(){(r||d)&&v(!0)},[r,d]),b?a.createElement("div",{ref:t,className:i()("".concat(n,"-content"),(0,h.A)((0,h.A)({},"".concat(n,"-content-active"),d),"".concat(n,"-content-inactive"),!d),o),style:l,role:u},a.createElement("div",{className:i()("".concat(n,"-content-box"),null==m?void 0:m.body),style:null==p?void 0:p.body},s)):null});y.displayName="PanelContent";var E=y,x=["showArrow","headerClass","isActive","onItemClick","forceRender","className","classNames","styles","prefixCls","collapsible","accordion","panelKey","extra","header","expandIcon","openMotion","destroyInactivePanel","children"],N=a.forwardRef(function(e,t){var n=e.showArrow,r=void 0===n||n,o=e.headerClass,s=e.isActive,c=e.onItemClick,d=e.forceRender,u=e.className,m=e.classNames,g=void 0===m?{}:m,y=e.styles,N=void 0===y?{}:y,w=e.prefixCls,A=e.collapsible,C=e.accordion,S=e.panelKey,k=e.extra,$=e.header,O=e.expandIcon,I=e.openMotion,z=e.destroyInactivePanel,M=e.children,T=(0,p.A)(e,x),R="disabled"===A,P=null!=k&&"boolean"!=typeof k,j=(0,h.A)((0,h.A)((0,h.A)({onClick:function(){null==c||c(S)},onKeyDown:function(e){"Enter"!==e.key&&e.keyCode!==v.A.ENTER&&e.which!==v.A.ENTER||null==c||c(S)},role:C?"tab":"button"},"aria-expanded",s),"aria-disabled",R),"tabIndex",R?-1:0),_="function"==typeof O?O(e):a.createElement("i",{className:"arrow"}),B=_&&a.createElement("div",(0,l.A)({className:"".concat(w,"-expand-icon")},["header","icon"].includes(A)?j:{}),_),H=i()("".concat(w,"-item"),(0,h.A)((0,h.A)({},"".concat(w,"-item-active"),s),"".concat(w,"-item-disabled"),R),u),D=i()(o,"".concat(w,"-header"),(0,h.A)({},"".concat(w,"-collapsible-").concat(A),!!A),g.header),L=(0,f.A)({className:D,style:N.header},["header","icon"].includes(A)?{}:j);return a.createElement("div",(0,l.A)({},T,{ref:t,className:H}),a.createElement("div",L,r&&B,a.createElement("span",(0,l.A)({className:"".concat(w,"-header-text")},"header"===A?j:{}),$),P&&a.createElement("div",{className:"".concat(w,"-extra")},k)),a.createElement(b.Ay,(0,l.A)({visible:s,leavedClassName:"".concat(w,"-content-hidden")},I,{forceRender:d,removeOnLeave:z}),function(e,t){var n=e.className,r=e.style;return a.createElement(E,{ref:t,prefixCls:w,className:n,classNames:g,style:r,styles:N,isActive:s,forceRender:d,role:C?"tabpanel":void 0},M)}))}),w=["children","label","key","collapsible","onItemClick","destroyInactivePanel"];var A=function(e,t,n){return Array.isArray(e)?function(e,t){var n=t.prefixCls,r=t.accordion,o=t.collapsible,i=t.destroyInactivePanel,s=t.onItemClick,c=t.activeKey,d=t.openMotion,u=t.expandIcon;return e.map(function(e,t){var m=e.children,g=e.label,f=e.key,h=e.collapsible,b=e.onItemClick,v=e.destroyInactivePanel,y=(0,p.A)(e,w),E=String(null!=f?f:t),x=null!=h?h:o,A=null!=v?v:i,C=!1;return C=r?c[0]===E:c.indexOf(E)>-1,a.createElement(N,(0,l.A)({},y,{prefixCls:n,key:E,panelKey:E,isActive:C,accordion:r,openMotion:d,expandIcon:u,header:g,collapsible:x,onItemClick:function(e){"disabled"!==x&&(s(e),null==b||b(e))},destroyInactivePanel:A}),m)})}(e,n):(0,g.A)(t).map(function(e,t){return function(e,t,n){if(!e)return null;var r=n.prefixCls,o=n.accordion,i=n.collapsible,l=n.destroyInactivePanel,s=n.onItemClick,c=n.activeKey,d=n.openMotion,u=n.expandIcon,m=e.key||String(t),p=e.props,g=p.header,f=p.headerClass,h=p.destroyInactivePanel,b=p.collapsible,v=p.onItemClick,y=!1;y=o?c[0]===m:c.indexOf(m)>-1;var E=null!=b?b:i,x={key:m,panelKey:m,header:g,headerClass:f,isActive:y,prefixCls:r,destroyInactivePanel:null!=h?h:l,openMotion:d,accordion:o,children:e.props.children,onItemClick:function(e){"disabled"!==E&&(s(e),null==v||v(e))},expandIcon:u,collapsible:E};return"string"==typeof e.type?e:(Object.keys(x).forEach(function(e){void 0===x[e]&&delete x[e]}),a.cloneElement(e,x))}(e,t,n)})},C=n(2065);function S(e){var t=e;if(!Array.isArray(t)){var n=(0,d.A)(t);t="number"===n||"string"===n?[t]:[]}return t.map(function(e){return String(e)})}var k=a.forwardRef(function(e,t){var n=e.prefixCls,r=void 0===n?"rc-collapse":n,o=e.destroyInactivePanel,d=void 0!==o&&o,p=e.style,g=e.accordion,f=e.className,h=e.children,b=e.collapsible,v=e.openMotion,y=e.expandIcon,E=e.activeKey,x=e.defaultActiveKey,N=e.onChange,w=e.items,k=i()(r,f),$=(0,u.A)([],{value:E,onChange:function(e){return null==N?void 0:N(e)},defaultValue:x,postState:S}),O=(0,c.A)($,2),I=O[0],z=O[1];(0,m.Ay)(!h,"[rc-collapse] `children` will be removed in next major version. Please use `items` instead.");var M=A(w,h,{prefixCls:r,accordion:g,openMotion:v,expandIcon:y,collapsible:b,destroyInactivePanel:d,onItemClick:function(e){return z(function(){return g?I[0]===e?[]:[e]:I.indexOf(e)>-1?I.filter(function(t){return t!==e}):[].concat((0,s.A)(I),[e])})},activeKey:I});return a.createElement("div",(0,l.A)({ref:t,className:k,style:p,role:g?"tablist":void 0},(0,C.A)(e,{aria:!0,data:!0})),M)}),$=Object.assign(k,{Panel:N}),O=$,I=($.Panel,n(9853)),z=n(3723),M=n(682),T=n(2279),R=n(829);var P=a.forwardRef((e,t)=>{const{getPrefixCls:n}=a.useContext(T.QO),{prefixCls:r,className:o,showArrow:l=!0}=e,s=n("collapse",r),c=i()({[`${s}-no-arrow`]:!l},o);return a.createElement(O.Panel,Object.assign({ref:t},e,{prefixCls:s,className:c}))}),j=n(2187),_=n(5905),B=n(977),H=n(7358),D=n(4277);const L=e=>{const{componentCls:t,contentBg:n,padding:a,headerBg:r,headerPadding:o,collapseHeaderPaddingSM:i,collapseHeaderPaddingLG:l,collapsePanelBorderRadius:s,lineWidth:c,lineType:d,colorBorder:u,colorText:m,colorTextHeading:p,colorTextDisabled:g,fontSizeLG:f,lineHeight:h,lineHeightLG:b,marginSM:v,paddingSM:y,paddingLG:E,paddingXS:x,motionDurationSlow:N,fontSizeIcon:w,contentPadding:A,fontHeight:C,fontHeightLG:S}=e,k=`${(0,j.zA)(c)} ${d} ${u}`;return{[t]:Object.assign(Object.assign({},(0,_.dF)(e)),{backgroundColor:r,border:k,borderRadius:s,"&-rtl":{direction:"rtl"},[`& > ${t}-item`]:{borderBottom:k,"&:first-child":{[`\n            &,\n            & > ${t}-header`]:{borderRadius:`${(0,j.zA)(s)} ${(0,j.zA)(s)} 0 0`}},"&:last-child":{[`\n            &,\n            & > ${t}-header`]:{borderRadius:`0 0 ${(0,j.zA)(s)} ${(0,j.zA)(s)}`}},[`> ${t}-header`]:Object.assign(Object.assign({position:"relative",display:"flex",flexWrap:"nowrap",alignItems:"flex-start",padding:o,color:p,lineHeight:h,cursor:"pointer",transition:`all ${N}, visibility 0s`},(0,_.K8)(e)),{[`> ${t}-header-text`]:{flex:"auto"},[`${t}-expand-icon`]:{height:C,display:"flex",alignItems:"center",paddingInlineEnd:v},[`${t}-arrow`]:Object.assign(Object.assign({},(0,_.Nk)()),{fontSize:w,transition:`transform ${N}`,svg:{transition:`transform ${N}`}}),[`${t}-header-text`]:{marginInlineEnd:"auto"}}),[`${t}-collapsible-header`]:{cursor:"default",[`${t}-header-text`]:{flex:"none",cursor:"pointer"}},[`${t}-collapsible-icon`]:{cursor:"unset",[`${t}-expand-icon`]:{cursor:"pointer"}}},[`${t}-content`]:{color:m,backgroundColor:n,borderTop:k,[`& > ${t}-content-box`]:{padding:A},"&-hidden":{display:"none"}},"&-small":{[`> ${t}-item`]:{[`> ${t}-header`]:{padding:i,paddingInlineStart:x,[`> ${t}-expand-icon`]:{marginInlineStart:e.calc(y).sub(x).equal()}},[`> ${t}-content > ${t}-content-box`]:{padding:y}}},"&-large":{[`> ${t}-item`]:{fontSize:f,lineHeight:b,[`> ${t}-header`]:{padding:l,paddingInlineStart:a,[`> ${t}-expand-icon`]:{height:S,marginInlineStart:e.calc(E).sub(a).equal()}},[`> ${t}-content > ${t}-content-box`]:{padding:E}}},[`${t}-item:last-child`]:{borderBottom:0,[`> ${t}-content`]:{borderRadius:`0 0 ${(0,j.zA)(s)} ${(0,j.zA)(s)}`}},[`& ${t}-item-disabled > ${t}-header`]:{"\n          &,\n          & > .arrow\n        ":{color:g,cursor:"not-allowed"}},[`&${t}-icon-position-end`]:{[`& > ${t}-item`]:{[`> ${t}-header`]:{[`${t}-expand-icon`]:{order:1,paddingInlineEnd:0,paddingInlineStart:v}}}}})}},F=e=>{const{componentCls:t}=e,n=`> ${t}-item > ${t}-header ${t}-arrow`;return{[`${t}-rtl`]:{[n]:{transform:"rotate(180deg)"}}}},W=e=>{const{componentCls:t,headerBg:n,borderlessContentPadding:a,borderlessContentBg:r,colorBorder:o}=e;return{[`${t}-borderless`]:{backgroundColor:n,border:0,[`> ${t}-item`]:{borderBottom:`1px solid ${o}`},[`\n        > ${t}-item:last-child,\n        > ${t}-item:last-child ${t}-header\n      `]:{borderRadius:0},[`> ${t}-item:last-child`]:{borderBottom:0},[`> ${t}-item > ${t}-content`]:{backgroundColor:r,borderTop:0},[`> ${t}-item > ${t}-content > ${t}-content-box`]:{padding:a}}}},q=e=>{const{componentCls:t,paddingSM:n}=e;return{[`${t}-ghost`]:{backgroundColor:"transparent",border:0,[`> ${t}-item`]:{borderBottom:0,[`> ${t}-content`]:{backgroundColor:"transparent",border:0,[`> ${t}-content-box`]:{paddingBlock:n}}}}}};var X=(0,H.OF)("Collapse",e=>{const t=(0,D.oX)(e,{collapseHeaderPaddingSM:`${(0,j.zA)(e.paddingXS)} ${(0,j.zA)(e.paddingSM)}`,collapseHeaderPaddingLG:`${(0,j.zA)(e.padding)} ${(0,j.zA)(e.paddingLG)}`,collapsePanelBorderRadius:e.borderRadiusLG});return[L(t),W(t),q(t),F(t),(0,B.A)(t)]},e=>({headerPadding:`${e.paddingSM}px ${e.padding}px`,headerBg:e.colorFillAlter,contentPadding:`${e.padding}px 16px`,contentBg:e.colorBgContainer,borderlessContentPadding:`${e.paddingXXS}px 16px ${e.padding}px`,borderlessContentBg:"transparent"}));const G=a.forwardRef((e,t)=>{const{getPrefixCls:n,direction:o,expandIcon:l,className:s,style:c}=(0,T.TP)("collapse"),{prefixCls:d,className:u,rootClassName:m,style:p,bordered:f=!0,ghost:h,size:b,expandIconPosition:v="start",children:y,destroyInactivePanel:E,destroyOnHidden:x,expandIcon:N}=e,w=(0,R.A)(e=>{var t;return null!==(t=null!=b?b:e)&&void 0!==t?t:"middle"}),A=n("collapse",d),C=n(),[S,k,$]=X(A);const P=a.useMemo(()=>"left"===v?"start":"right"===v?"end":v,[v]),j=null!=N?N:l,_=a.useCallback((e={})=>{const t="function"==typeof j?j(e):a.createElement(r.A,{rotate:e.isActive?"rtl"===o?-90:90:void 0,"aria-label":e.isActive?"expanded":"collapsed"});return(0,M.Ob)(t,()=>{var e;return{className:i()(null===(e=t.props)||void 0===e?void 0:e.className,`${A}-arrow`)}})},[j,A,o]),B=i()(`${A}-icon-position-${P}`,{[`${A}-borderless`]:!f,[`${A}-rtl`]:"rtl"===o,[`${A}-ghost`]:!!h,[`${A}-${w}`]:"middle"!==w},s,u,m,k,$),H=a.useMemo(()=>Object.assign(Object.assign({},(0,z.A)(C)),{motionAppear:!1,leavedClassName:`${A}-content-hidden`}),[C,A]),D=a.useMemo(()=>y?(0,g.A)(y).map((e,t)=>{var n,a;const r=e.props;if(null==r?void 0:r.disabled){const o=null!==(n=e.key)&&void 0!==n?n:String(t),i=Object.assign(Object.assign({},(0,I.A)(e.props,["disabled"])),{key:o,collapsible:null!==(a=r.collapsible)&&void 0!==a?a:"disabled"});return(0,M.Ob)(e,i)}return e}):null,[y]);return S(a.createElement(O,Object.assign({ref:t,openMotion:H},(0,I.A)(e,["rootClassName"]),{expandIcon:_,prefixCls:A,className:B,style:Object.assign(Object.assign({},c),p),destroyInactivePanel:null!=x?x:E}),D))});var U=Object.assign(G,{Panel:P})},2609:function(e,t,n){n.d(t,{A:function(){return R}});var a=n(6540),r=n(3567),o=n(6942),i=n.n(o),l=n(8168),s=n(4467),c=n(5544),d=n(3986),u=n(2533),m=n(6928),p=["prefixCls","className","checked","defaultChecked","disabled","loadingIcon","checkedChildren","unCheckedChildren","onClick","onChange","onKeyDown"],g=a.forwardRef(function(e,t){var n,r=e.prefixCls,o=void 0===r?"rc-switch":r,g=e.className,f=e.checked,h=e.defaultChecked,b=e.disabled,v=e.loadingIcon,y=e.checkedChildren,E=e.unCheckedChildren,x=e.onClick,N=e.onChange,w=e.onKeyDown,A=(0,d.A)(e,p),C=(0,u.A)(!1,{value:f,defaultValue:h}),S=(0,c.A)(C,2),k=S[0],$=S[1];function O(e,t){var n=k;return b||($(n=e),null==N||N(n,t)),n}var I=i()(o,g,(n={},(0,s.A)(n,"".concat(o,"-checked"),k),(0,s.A)(n,"".concat(o,"-disabled"),b),n));return a.createElement("button",(0,l.A)({},A,{type:"button",role:"switch","aria-checked":k,disabled:b,className:I,ref:t,onKeyDown:function(e){e.which===m.A.LEFT?O(!1,e):e.which===m.A.RIGHT&&O(!0,e),null==w||w(e)},onClick:function(e){var t=O(!k,e);null==x||x(t,e)}}),v,a.createElement("span",{className:"".concat(o,"-inner")},a.createElement("span",{className:"".concat(o,"-inner-checked")},y),a.createElement("span",{className:"".concat(o,"-inner-unchecked")},E)))});g.displayName="Switch";var f=g,h=n(57),b=n(2279),v=n(8119),y=n(829),E=n(2187),x=n(2616),N=n(5905),w=n(7358),A=n(4277);const C=e=>{const{componentCls:t,trackHeightSM:n,trackPadding:a,trackMinWidthSM:r,innerMinMarginSM:o,innerMaxMarginSM:i,handleSizeSM:l,calc:s}=e,c=`${t}-inner`,d=(0,E.zA)(s(l).add(s(a).mul(2)).equal()),u=(0,E.zA)(s(i).mul(2).equal());return{[t]:{[`&${t}-small`]:{minWidth:r,height:n,lineHeight:(0,E.zA)(n),[`${t}-inner`]:{paddingInlineStart:i,paddingInlineEnd:o,[`${c}-checked, ${c}-unchecked`]:{minHeight:n},[`${c}-checked`]:{marginInlineStart:`calc(-100% + ${d} - ${u})`,marginInlineEnd:`calc(100% - ${d} + ${u})`},[`${c}-unchecked`]:{marginTop:s(n).mul(-1).equal(),marginInlineStart:0,marginInlineEnd:0}},[`${t}-handle`]:{width:l,height:l},[`${t}-loading-icon`]:{top:s(s(l).sub(e.switchLoadingIconSize)).div(2).equal(),fontSize:e.switchLoadingIconSize},[`&${t}-checked`]:{[`${t}-inner`]:{paddingInlineStart:o,paddingInlineEnd:i,[`${c}-checked`]:{marginInlineStart:0,marginInlineEnd:0},[`${c}-unchecked`]:{marginInlineStart:`calc(100% - ${d} + ${u})`,marginInlineEnd:`calc(-100% + ${d} - ${u})`}},[`${t}-handle`]:{insetInlineStart:`calc(100% - ${(0,E.zA)(s(l).add(a).equal())})`}},[`&:not(${t}-disabled):active`]:{[`&:not(${t}-checked) ${c}`]:{[`${c}-unchecked`]:{marginInlineStart:s(e.marginXXS).div(2).equal(),marginInlineEnd:s(e.marginXXS).mul(-1).div(2).equal()}},[`&${t}-checked ${c}`]:{[`${c}-checked`]:{marginInlineStart:s(e.marginXXS).mul(-1).div(2).equal(),marginInlineEnd:s(e.marginXXS).div(2).equal()}}}}}}},S=e=>{const{componentCls:t,handleSize:n,calc:a}=e;return{[t]:{[`${t}-loading-icon${e.iconCls}`]:{position:"relative",top:a(a(n).sub(e.fontSize)).div(2).equal(),color:e.switchLoadingIconColor,verticalAlign:"top"},[`&${t}-checked ${t}-loading-icon`]:{color:e.switchColor}}}},k=e=>{const{componentCls:t,trackPadding:n,handleBg:a,handleShadow:r,handleSize:o,calc:i}=e,l=`${t}-handle`;return{[t]:{[l]:{position:"absolute",top:n,insetInlineStart:n,width:o,height:o,transition:`all ${e.switchDuration} ease-in-out`,"&::before":{position:"absolute",top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,backgroundColor:a,borderRadius:i(o).div(2).equal(),boxShadow:r,transition:`all ${e.switchDuration} ease-in-out`,content:'""'}},[`&${t}-checked ${l}`]:{insetInlineStart:`calc(100% - ${(0,E.zA)(i(o).add(n).equal())})`},[`&:not(${t}-disabled):active`]:{[`${l}::before`]:{insetInlineEnd:e.switchHandleActiveInset,insetInlineStart:0},[`&${t}-checked ${l}::before`]:{insetInlineEnd:0,insetInlineStart:e.switchHandleActiveInset}}}}},$=e=>{const{componentCls:t,trackHeight:n,trackPadding:a,innerMinMargin:r,innerMaxMargin:o,handleSize:i,calc:l}=e,s=`${t}-inner`,c=(0,E.zA)(l(i).add(l(a).mul(2)).equal()),d=(0,E.zA)(l(o).mul(2).equal());return{[t]:{[s]:{display:"block",overflow:"hidden",borderRadius:100,height:"100%",paddingInlineStart:o,paddingInlineEnd:r,transition:`padding-inline-start ${e.switchDuration} ease-in-out, padding-inline-end ${e.switchDuration} ease-in-out`,[`${s}-checked, ${s}-unchecked`]:{display:"block",color:e.colorTextLightSolid,fontSize:e.fontSizeSM,transition:`margin-inline-start ${e.switchDuration} ease-in-out, margin-inline-end ${e.switchDuration} ease-in-out`,pointerEvents:"none",minHeight:n},[`${s}-checked`]:{marginInlineStart:`calc(-100% + ${c} - ${d})`,marginInlineEnd:`calc(100% - ${c} + ${d})`},[`${s}-unchecked`]:{marginTop:l(n).mul(-1).equal(),marginInlineStart:0,marginInlineEnd:0}},[`&${t}-checked ${s}`]:{paddingInlineStart:r,paddingInlineEnd:o,[`${s}-checked`]:{marginInlineStart:0,marginInlineEnd:0},[`${s}-unchecked`]:{marginInlineStart:`calc(100% - ${c} + ${d})`,marginInlineEnd:`calc(-100% + ${c} - ${d})`}},[`&:not(${t}-disabled):active`]:{[`&:not(${t}-checked) ${s}`]:{[`${s}-unchecked`]:{marginInlineStart:l(a).mul(2).equal(),marginInlineEnd:l(a).mul(-1).mul(2).equal()}},[`&${t}-checked ${s}`]:{[`${s}-checked`]:{marginInlineStart:l(a).mul(-1).mul(2).equal(),marginInlineEnd:l(a).mul(2).equal()}}}}}},O=e=>{const{componentCls:t,trackHeight:n,trackMinWidth:a}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,N.dF)(e)),{position:"relative",display:"inline-block",boxSizing:"border-box",minWidth:a,height:n,lineHeight:(0,E.zA)(n),verticalAlign:"middle",background:e.colorTextQuaternary,border:"0",borderRadius:100,cursor:"pointer",transition:`all ${e.motionDurationMid}`,userSelect:"none",[`&:hover:not(${t}-disabled)`]:{background:e.colorTextTertiary}}),(0,N.K8)(e)),{[`&${t}-checked`]:{background:e.switchColor,[`&:hover:not(${t}-disabled)`]:{background:e.colorPrimaryHover}},[`&${t}-loading, &${t}-disabled`]:{cursor:"not-allowed",opacity:e.switchDisabledOpacity,"*":{boxShadow:"none",cursor:"not-allowed"}},[`&${t}-rtl`]:{direction:"rtl"}})}};var I=(0,w.OF)("Switch",e=>{const t=(0,A.oX)(e,{switchDuration:e.motionDurationMid,switchColor:e.colorPrimary,switchDisabledOpacity:e.opacityLoading,switchLoadingIconSize:e.calc(e.fontSizeIcon).mul(.75).equal(),switchLoadingIconColor:`rgba(0, 0, 0, ${e.opacityLoading})`,switchHandleActiveInset:"-30%"});return[O(t),$(t),k(t),S(t),C(t)]},e=>{const{fontSize:t,lineHeight:n,controlHeight:a,colorWhite:r}=e,o=t*n,i=a/2,l=o-4,s=i-4;return{trackHeight:o,trackHeightSM:i,trackMinWidth:2*l+8,trackMinWidthSM:2*s+4,trackPadding:2,handleBg:r,handleSize:l,handleSizeSM:s,handleShadow:`0 2px 4px 0 ${new x.Y("#00230b").setA(.2).toRgbString()}`,innerMinMargin:l/2,innerMaxMargin:l+2+4,innerMinMarginSM:s/2,innerMaxMarginSM:s+2+4}}),z=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]])}return n};const M=a.forwardRef((e,t)=>{const{prefixCls:n,size:o,disabled:l,loading:s,className:c,rootClassName:d,style:m,checked:p,value:g,defaultChecked:E,defaultValue:x,onChange:N}=e,w=z(e,["prefixCls","size","disabled","loading","className","rootClassName","style","checked","value","defaultChecked","defaultValue","onChange"]),[A,C]=(0,u.A)(!1,{value:null!=p?p:g,defaultValue:null!=E?E:x}),{getPrefixCls:S,direction:k,switch:$}=a.useContext(b.QO),O=a.useContext(v.A),M=(null!=l?l:O)||s,T=S("switch",n),R=a.createElement("div",{className:`${T}-handle`},s&&a.createElement(r.A,{className:`${T}-loading-icon`})),[P,j,_]=I(T),B=(0,y.A)(o),H=i()(null==$?void 0:$.className,{[`${T}-small`]:"small"===B,[`${T}-loading`]:s,[`${T}-rtl`]:"rtl"===k},c,d,j,_),D=Object.assign(Object.assign({},null==$?void 0:$.style),m);return P(a.createElement(h.A,{component:"Switch"},a.createElement(f,Object.assign({},w,{checked:A,onChange:(...e)=>{C(e[0]),null==N||N.apply(void 0,e)},prefixCls:T,className:H,style:D,disabled:M,ref:t,loadingIcon:R}))))}),T=M;T.__ANT_SWITCH=!0;var R=T},2697:function(e,t,n){n.d(t,{A:function(){return D}});var a=n(6540),r=n(6942),o=n.n(r),i=n(8168),l=n(9379),s=n(4467),c=n(5544),d=n(3986),u=n(2533),m=["prefixCls","className","style","checked","disabled","defaultChecked","type","title","onChange"],p=(0,a.forwardRef)(function(e,t){var n=e.prefixCls,r=void 0===n?"rc-checkbox":n,p=e.className,g=e.style,f=e.checked,h=e.disabled,b=e.defaultChecked,v=void 0!==b&&b,y=e.type,E=void 0===y?"checkbox":y,x=e.title,N=e.onChange,w=(0,d.A)(e,m),A=(0,a.useRef)(null),C=(0,a.useRef)(null),S=(0,u.A)(v,{value:f}),k=(0,c.A)(S,2),$=k[0],O=k[1];(0,a.useImperativeHandle)(t,function(){return{focus:function(e){var t;null===(t=A.current)||void 0===t||t.focus(e)},blur:function(){var e;null===(e=A.current)||void 0===e||e.blur()},input:A.current,nativeElement:C.current}});var I=o()(r,p,(0,s.A)((0,s.A)({},"".concat(r,"-checked"),$),"".concat(r,"-disabled"),h));return a.createElement("span",{className:I,title:x,style:g,ref:C},a.createElement("input",(0,i.A)({},w,{className:"".concat(r,"-input"),ref:A,onChange:function(t){h||("checked"in e||O(t.target.checked),null==N||N({target:(0,l.A)((0,l.A)({},e),{},{type:E,checked:t.target.checked}),stopPropagation:function(){t.stopPropagation()},preventDefault:function(){t.preventDefault()},nativeEvent:t.nativeEvent}))},disabled:h,checked:!!$,type:E})),a.createElement("span",{className:"".concat(r,"-inner")}))}),g=p,f=n(8719),h=n(57),b=n(4424),v=n(2279),y=n(8119),E=n(934),x=n(4241);var N=a.createContext(null),w=n(2187),A=n(5905),C=n(4277),S=n(7358);const k=e=>{const{checkboxCls:t}=e,n=`${t}-wrapper`;return[{[`${t}-group`]:Object.assign(Object.assign({},(0,A.dF)(e)),{display:"inline-flex",flexWrap:"wrap",columnGap:e.marginXS,[`> ${e.antCls}-row`]:{flex:1}}),[n]:Object.assign(Object.assign({},(0,A.dF)(e)),{display:"inline-flex",alignItems:"baseline",cursor:"pointer","&:after":{display:"inline-block",width:0,overflow:"hidden",content:"'\\a0'"},[`& + ${n}`]:{marginInlineStart:0},[`&${n}-in-form-item`]:{'input[type="checkbox"]':{width:14,height:14}}}),[t]:Object.assign(Object.assign({},(0,A.dF)(e)),{position:"relative",whiteSpace:"nowrap",lineHeight:1,cursor:"pointer",borderRadius:e.borderRadiusSM,alignSelf:"center",[`${t}-input`]:{position:"absolute",inset:0,zIndex:1,cursor:"pointer",opacity:0,margin:0,[`&:focus-visible + ${t}-inner`]:Object.assign({},(0,A.jk)(e))},[`${t}-inner`]:{boxSizing:"border-box",display:"block",width:e.checkboxSize,height:e.checkboxSize,direction:"ltr",backgroundColor:e.colorBgContainer,border:`${(0,w.zA)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,borderCollapse:"separate",transition:`all ${e.motionDurationSlow}`,"&:after":{boxSizing:"border-box",position:"absolute",top:"50%",insetInlineStart:"25%",display:"table",width:e.calc(e.checkboxSize).div(14).mul(5).equal(),height:e.calc(e.checkboxSize).div(14).mul(8).equal(),border:`${(0,w.zA)(e.lineWidthBold)} solid ${e.colorWhite}`,borderTop:0,borderInlineStart:0,transform:"rotate(45deg) scale(0) translate(-50%,-50%)",opacity:0,content:'""',transition:`all ${e.motionDurationFast} ${e.motionEaseInBack}, opacity ${e.motionDurationFast}`}},"& + span":{paddingInlineStart:e.paddingXS,paddingInlineEnd:e.paddingXS}})},{[`\n        ${n}:not(${n}-disabled),\n        ${t}:not(${t}-disabled)\n      `]:{[`&:hover ${t}-inner`]:{borderColor:e.colorPrimary}},[`${n}:not(${n}-disabled)`]:{[`&:hover ${t}-checked:not(${t}-disabled) ${t}-inner`]:{backgroundColor:e.colorPrimaryHover,borderColor:"transparent"},[`&:hover ${t}-checked:not(${t}-disabled):after`]:{borderColor:e.colorPrimaryHover}}},{[`${t}-checked`]:{[`${t}-inner`]:{backgroundColor:e.colorPrimary,borderColor:e.colorPrimary,"&:after":{opacity:1,transform:"rotate(45deg) scale(1) translate(-50%,-50%)",transition:`all ${e.motionDurationMid} ${e.motionEaseOutBack} ${e.motionDurationFast}`}}},[`\n        ${n}-checked:not(${n}-disabled),\n        ${t}-checked:not(${t}-disabled)\n      `]:{[`&:hover ${t}-inner`]:{backgroundColor:e.colorPrimaryHover,borderColor:"transparent"}}},{[t]:{"&-indeterminate":{"&":{[`${t}-inner`]:{backgroundColor:`${e.colorBgContainer}`,borderColor:`${e.colorBorder}`,"&:after":{top:"50%",insetInlineStart:"50%",width:e.calc(e.fontSizeLG).div(2).equal(),height:e.calc(e.fontSizeLG).div(2).equal(),backgroundColor:e.colorPrimary,border:0,transform:"translate(-50%, -50%) scale(1)",opacity:1,content:'""'}},[`&:hover ${t}-inner`]:{backgroundColor:`${e.colorBgContainer}`,borderColor:`${e.colorPrimary}`}}}}},{[`${n}-disabled`]:{cursor:"not-allowed"},[`${t}-disabled`]:{[`&, ${t}-input`]:{cursor:"not-allowed",pointerEvents:"none"},[`${t}-inner`]:{background:e.colorBgContainerDisabled,borderColor:e.colorBorder,"&:after":{borderColor:e.colorTextDisabled}},"&:after":{display:"none"},"& + span":{color:e.colorTextDisabled},[`&${t}-indeterminate ${t}-inner::after`]:{background:e.colorTextDisabled}}}]};function $(e,t){const n=(0,C.oX)(t,{checkboxCls:`.${e}`,checkboxSize:t.controlInteractiveSize});return[k(n)]}var O=(0,S.OF)("Checkbox",(e,{prefixCls:t})=>[$(t,e)]),I=n(5371);var z=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]])}return n};const M=(e,t)=>{var n;const{prefixCls:r,className:i,rootClassName:l,children:s,indeterminate:c=!1,style:d,onMouseEnter:u,onMouseLeave:m,skipGroup:p=!1,disabled:w}=e,A=z(e,["prefixCls","className","rootClassName","children","indeterminate","style","onMouseEnter","onMouseLeave","skipGroup","disabled"]),{getPrefixCls:C,direction:S,checkbox:k}=a.useContext(v.QO),$=a.useContext(N),{isFormItemInput:M}=a.useContext(x.$W),T=a.useContext(y.A),R=null!==(n=(null==$?void 0:$.disabled)||w)&&void 0!==n?n:T,P=a.useRef(A.value),j=a.useRef(null),_=(0,f.K4)(t,j);a.useEffect(()=>{null==$||$.registerValue(A.value)},[]),a.useEffect(()=>{if(!p)return A.value!==P.current&&(null==$||$.cancelValue(P.current),null==$||$.registerValue(A.value),P.current=A.value),()=>null==$?void 0:$.cancelValue(A.value)},[A.value]),a.useEffect(()=>{var e;(null===(e=j.current)||void 0===e?void 0:e.input)&&(j.current.input.indeterminate=c)},[c]);const B=C("checkbox",r),H=(0,E.A)(B),[D,L,F]=O(B,H),W=Object.assign({},A);$&&!p&&(W.onChange=(...e)=>{A.onChange&&A.onChange.apply(A,e),$.toggleOption&&$.toggleOption({label:s,value:A.value})},W.name=$.name,W.checked=$.value.includes(A.value));const q=o()(`${B}-wrapper`,{[`${B}-rtl`]:"rtl"===S,[`${B}-wrapper-checked`]:W.checked,[`${B}-wrapper-disabled`]:R,[`${B}-wrapper-in-form-item`]:M},null==k?void 0:k.className,i,l,F,H,L),X=o()({[`${B}-indeterminate`]:c},b.D,L),[G,U]=function(e){const t=a.useRef(null),n=()=>{I.A.cancel(t.current),t.current=null};return[()=>{n(),t.current=(0,I.A)(()=>{t.current=null})},a=>{t.current&&(a.stopPropagation(),n()),null==e||e(a)}]}(W.onClick);return D(a.createElement(h.A,{component:"Checkbox",disabled:R},a.createElement("label",{className:q,style:Object.assign(Object.assign({},null==k?void 0:k.style),d),onMouseEnter:u,onMouseLeave:m,onClick:G},a.createElement(g,Object.assign({},W,{onClick:U,prefixCls:B,className:X,disabled:R,ref:_})),null!=s&&a.createElement("span",{className:`${B}-label`},s))))};var T=a.forwardRef(M),R=n(436),P=n(9853),j=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]])}return n};const _=a.forwardRef((e,t)=>{const{defaultValue:n,children:r,options:i=[],prefixCls:l,className:s,rootClassName:c,style:d,onChange:u}=e,m=j(e,["defaultValue","children","options","prefixCls","className","rootClassName","style","onChange"]),{getPrefixCls:p,direction:g}=a.useContext(v.QO),[f,h]=a.useState(m.value||n||[]),[b,y]=a.useState([]);a.useEffect(()=>{"value"in m&&h(m.value||[])},[m.value]);const x=a.useMemo(()=>i.map(e=>"string"==typeof e||"number"==typeof e?{label:e,value:e}:e),[i]),w=e=>{y(t=>t.filter(t=>t!==e))},A=e=>{y(t=>[].concat((0,R.A)(t),[e]))},C=e=>{const t=f.indexOf(e.value),n=(0,R.A)(f);-1===t?n.push(e.value):n.splice(t,1),"value"in m||h(n),null==u||u(n.filter(e=>b.includes(e)).sort((e,t)=>x.findIndex(t=>t.value===e)-x.findIndex(e=>e.value===t)))},S=p("checkbox",l),k=`${S}-group`,$=(0,E.A)(S),[I,z,M]=O(S,$),_=(0,P.A)(m,["value","disabled"]),B=i.length?x.map(e=>a.createElement(T,{prefixCls:S,key:e.value.toString(),disabled:"disabled"in e?e.disabled:m.disabled,value:e.value,checked:f.includes(e.value),onChange:e.onChange,className:o()(`${k}-item`,e.className),style:e.style,title:e.title,id:e.id,required:e.required},e.label)):r,H=a.useMemo(()=>({toggleOption:C,value:f,disabled:m.disabled,name:m.name,registerValue:A,cancelValue:w}),[C,f,m.disabled,m.name,A,w]),D=o()(k,{[`${k}-rtl`]:"rtl"===g},s,c,M,$,z);return I(a.createElement("div",Object.assign({className:D,style:d},_,{ref:t}),a.createElement(N.Provider,{value:H},B)))});var B=_;const H=T;H.Group=B,H.__ANT_CHECKBOX=!0;var D=H},3140:function(e,t,n){n.d(t,{A:function(){return a}});const a=(0,n(1788).A)("CircleMinus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}]])},3164:function(e,t,n){n.d(t,{A:function(){return a}});const a=(0,n(1788).A)("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]])},4279:function(e,t,n){n.d(t,{D$:function(){return z},Ed:function(){return h},HX:function(){return u},Il:function(){return d},Jz:function(){return b},K1:function(){return C},KX:function(){return O},Mf:function(){return N},O6:function(){return p},RG:function(){return $},Tj:function(){return k},U9:function(){return c},Uy:function(){return S},VZ:function(){return o},WR:function(){return A},Zj:function(){return f},d9:function(){return y},fF:function(){return i},fk:function(){return g},gG:function(){return s},gU:function(){return m},gn:function(){return x},ls:function(){return T},nL:function(){return l},nb:function(){return M},oD:function(){return I},qt:function(){return w},wb:function(){return E},wx:function(){return v},xq:function(){return a}});const a={ROUND_ROBIN_TEAM:"autogen_agentchat.teams.RoundRobinGroupChat",SELECTOR_TEAM:"autogen_agentchat.teams.SelectorGroupChat",SWARM_TEAM:"autogen_agentchat.teams.Swarm",ASSISTANT_AGENT:"autogen_agentchat.agents.AssistantAgent",USER_PROXY:"autogen_agentchat.agents.UserProxyAgent",WEB_SURFER:"autogen_ext.agents.web_surfer.MultimodalWebSurfer",OPENAI:"autogen_ext.models.openai.OpenAIChatCompletionClient",AZURE_OPENAI:"autogen_ext.models.openai.AzureOpenAIChatCompletionClient",ANTHROPIC:"autogen_ext.models.anthropic.AnthropicChatCompletionClient",FUNCTION_TOOL:"autogen_core.tools.FunctionTool",PYTHON_CODE_EXECUTION_TOOL:"autogen_ext.tools.code_execution.PythonCodeExecutionTool",LOCAL_COMMAND_LINE_CODE_EXECUTOR:"autogen_ext.code_executors.local.LocalCommandLineCodeExecutor",STATIC_WORKBENCH:"autogen_core.tools.StaticWorkbench",STATIC_STREAM_WORKBENCH:"autogen_core.tools.StaticStreamWorkbench",MCP_WORKBENCH:"autogen_ext.tools.mcp.McpWorkbench",OR_TERMINATION:"autogen_agentchat.base.OrTerminationCondition",AND_TERMINATION:"autogen_agentchat.base.AndTerminationCondition",MAX_MESSAGE:"autogen_agentchat.conditions.MaxMessageTermination",TEXT_MENTION:"autogen_agentchat.conditions.TextMentionTermination",STOP_MESSAGE:"autogen_agentchat.conditions.StopMessageTermination",TOKEN_USAGE:"autogen_agentchat.conditions.TokenUsageTermination",HANDOFF:"autogen_agentchat.conditions.HandoffTermination",TIMEOUT:"autogen_agentchat.conditions.TimeoutTermination",EXTERNAL:"autogen_agentchat.conditions.ExternalTermination",SOURCE_MATCH:"autogen_agentchat.conditions.SourceMatchTermination",TEXT_MESSAGE:"autogen_agentchat.conditions.TextMessageTermination",UNBOUNDED_CONTEXT:"autogen_core.model_context.UnboundedChatCompletionContext"};function r(e,t){return e.provider===t}function o(e){return"team"===e.component_type}function i(e){return"agent"===e.component_type}function l(e){return"model"===e.component_type}function s(e){return"tool"===e.component_type}function c(e){return"termination"===e.component_type}function d(e){return r(e,a.ROUND_ROBIN_TEAM)}function u(e){return r(e,a.SELECTOR_TEAM)}function m(e){return r(e,a.SWARM_TEAM)}function p(e){return r(e,a.ASSISTANT_AGENT)}function g(e){return r(e,a.USER_PROXY)}function f(e){return r(e,a.WEB_SURFER)}function h(e){return r(e,a.OPENAI)}function b(e){return r(e,a.AZURE_OPENAI)}function v(e){return e.provider===a.ANTHROPIC}function y(e){return r(e,a.FUNCTION_TOOL)}function E(e){return!!e&&r(e,a.STATIC_WORKBENCH)}function x(e){return E(e)||function(e){return!!e&&r(e,a.STATIC_STREAM_WORKBENCH)}(e)}function N(e){return!!e&&r(e,a.MCP_WORKBENCH)}function w(e){return r(e,a.OR_TERMINATION)||r(e,a.AND_TERMINATION)}function A(e){return r(e,a.MAX_MESSAGE)}function C(e){return r(e,a.TEXT_MENTION)}function S(e){return r(e,a.STOP_MESSAGE)}function k(e){return r(e,a.TOKEN_USAGE)}function $(e){return r(e,a.HANDOFF)}function O(e){return r(e,a.TIMEOUT)}function I(e){return r(e,a.EXTERNAL)}function z(e){return r(e,a.SOURCE_MATCH)}function M(e){return r(e,a.TEXT_MESSAGE)}function T(e){return"workbench"===e.component_type}},6808:function(e,t,n){n.d(t,{A:function(){return a}});const a=(0,n(1788).A)("CirclePlay",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polygon",{points:"10 8 16 12 10 16 10 8",key:"1cimsy"}]])},6816:function(e,t,n){n.d(t,{A:function(){return a}});const a=(0,n(1788).A)("Wrench",[["path",{d:"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z",key:"cbrjhi"}]])},6914:function(e,t,n){n.d(t,{A:function(){return I}});var a=n(6540),r=n(6942),o=n.n(r),i=n(9853),l=n(4121),s=n(8055),c=n(682),d=n(57),u=n(2279),m=n(2187),p=n(2616),g=n(5905),f=n(4277),h=n(7358);const b=e=>{const{lineWidth:t,fontSizeIcon:n,calc:a}=e,r=e.fontSizeSM;return(0,f.oX)(e,{tagFontSize:r,tagLineHeight:(0,m.zA)(a(e.lineHeightSM).mul(r).equal()),tagIconSize:a(n).sub(a(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},v=e=>({defaultBg:new p.Y(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var y=(0,h.OF)("Tag",e=>(e=>{const{paddingXXS:t,lineWidth:n,tagPaddingHorizontal:a,componentCls:r,calc:o}=e,i=o(a).sub(n).equal(),l=o(t).sub(n).equal();return{[r]:Object.assign(Object.assign({},(0,g.dF)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:i,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,m.zA)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${r}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${r}-close-icon`]:{marginInlineStart:l,fontSize:e.tagIconSize,color:e.colorIcon,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${r}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${r}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:i}}),[`${r}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}})(b(e)),v),E=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]])}return n};const x=a.forwardRef((e,t)=>{const{prefixCls:n,style:r,className:i,checked:l,onChange:s,onClick:c}=e,d=E(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:m,tag:p}=a.useContext(u.QO),g=m("tag",n),[f,h,b]=y(g),v=o()(g,`${g}-checkable`,{[`${g}-checkable-checked`]:l},null==p?void 0:p.className,i,h,b);return f(a.createElement("span",Object.assign({},d,{ref:t,style:Object.assign(Object.assign({},r),null==p?void 0:p.style),className:v,onClick:e=>{null==s||s(!l),null==c||c(e)}})))});var N=x,w=n(1108);var A=(0,h.bf)(["Tag","preset"],e=>(e=>(0,w.A)(e,(t,{textColor:n,lightBorderColor:a,lightColor:r,darkColor:o})=>({[`${e.componentCls}${e.componentCls}-${t}`]:{color:n,background:r,borderColor:a,"&-inverse":{color:e.colorTextLightSolid,background:o,borderColor:o},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}})))(b(e)),v);const C=(e,t,n)=>{const a="string"!=typeof(r=n)?r:r.charAt(0).toUpperCase()+r.slice(1);var r;return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:e[`color${n}`],background:e[`color${a}Bg`],borderColor:e[`color${a}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}};var S=(0,h.bf)(["Tag","status"],e=>{const t=b(e);return[C(t,"success","Success"),C(t,"processing","Info"),C(t,"error","Error"),C(t,"warning","Warning")]},v),k=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]])}return n};const $=a.forwardRef((e,t)=>{const{prefixCls:n,className:r,rootClassName:m,style:p,children:g,icon:f,color:h,onClose:b,bordered:v=!0,visible:E}=e,x=k(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:N,direction:w,tag:C}=a.useContext(u.QO),[$,O]=a.useState(!0),I=(0,i.A)(x,["closeIcon","closable"]);a.useEffect(()=>{void 0!==E&&O(E)},[E]);const z=(0,l.nP)(h),M=(0,l.ZZ)(h),T=z||M,R=Object.assign(Object.assign({backgroundColor:h&&!T?h:void 0},null==C?void 0:C.style),p),P=N("tag",n),[j,_,B]=y(P),H=o()(P,null==C?void 0:C.className,{[`${P}-${h}`]:T,[`${P}-has-color`]:h&&!T,[`${P}-hidden`]:!$,[`${P}-rtl`]:"rtl"===w,[`${P}-borderless`]:!v},r,m,_,B),D=e=>{e.stopPropagation(),null==b||b(e),e.defaultPrevented||O(!1)},[,L]=(0,s.A)((0,s.d)(e),(0,s.d)(C),{closable:!1,closeIconRender:e=>{const t=a.createElement("span",{className:`${P}-close-icon`,onClick:D},e);return(0,c.fx)(e,t,e=>({onClick:t=>{var n;null===(n=null==e?void 0:e.onClick)||void 0===n||n.call(e,t),D(t)},className:o()(null==e?void 0:e.className,`${P}-close-icon`)}))}}),F="function"==typeof x.onClick||g&&"a"===g.type,W=f||null,q=W?a.createElement(a.Fragment,null,W,g&&a.createElement("span",null,g)):g,X=a.createElement("span",Object.assign({},I,{ref:t,className:H,style:R}),q,L,z&&a.createElement(A,{key:"preset",prefixCls:P}),M&&a.createElement(S,{key:"status",prefixCls:P}));return j(F?a.createElement(d.A,{component:"Tag"},X):X)}),O=$;O.CheckableTag=N;var I=O},7133:function(e,t,n){n.d(t,{A:function(){return a}});const a=(0,n(1788).A)("Package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["path",{d:"m3.3 7 7.703 4.734a2 2 0 0 0 1.994 0L20.7 7",key:"yx3hmr"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},7799:function(e,t,n){n.d(t,{A:function(){return a}});const a=(0,n(1788).A)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},8073:function(e,t,n){n.d(t,{e:function(){return y}});var a=n(436),r=n(6540),o=n(9957),i=n(9314),l=n(696),s=n(2941),c=n(2702),d=n(2609),u=n(6305),m=n(964),p=n(3140),g=n(590),f=n(4279),h=n(9872);const{TextArea:b}=o.A,{Option:v}=i.A,y=e=>{let{component:t,onChange:n}=e;if(!(0,f.d9)(t))return null;const y=(0,r.useRef)(null),{0:E,1:x}=(0,r.useState)(!1),{0:N,1:w}=(0,r.useState)("direct"),{0:A,1:C}=(0,r.useState)(""),{0:S,1:k}=(0,r.useState)({module:"",imports:""}),$=(0,r.useCallback)(e=>{n({...t,...e,config:{...t.config,...e.config||{}}})},[t,n]),O=()=>{const e=(0,a.A)(t.config.global_imports||[]);"direct"===N&&A?(e.push(A),C("")):"fromModule"===N&&S.module&&S.imports&&(e.push({module:S.module,imports:S.imports.split(",").map(e=>e.trim()).filter(e=>e)}),k({module:"",imports:""})),$({config:{...t.config,global_imports:e}}),x(!1)};return r.createElement(l.A,{defaultActiveKey:["details","configuration"],className:"border-0",expandIconPosition:"end",items:[{key:"details",label:r.createElement("div",{className:"flex items-center gap-2"},r.createElement(u.A,{className:"w-4 h-4 text-blue-500"}),r.createElement("span",{className:"font-medium"},"Component Details")),children:r.createElement("div",{className:"space-y-4"},r.createElement("label",{className:"block"},r.createElement("span",{className:"text-sm font-medium text-gray-700"},"Name"),r.createElement(o.A,{value:t.label||"",onChange:e=>$({label:e.target.value}),placeholder:"Tool name",className:"mt-1"})),r.createElement("label",{className:"block"},r.createElement("span",{className:"text-sm font-medium text-gray-700"},"Description"),r.createElement(b,{value:t.description||"",onChange:e=>$({description:e.target.value}),placeholder:"Tool description",rows:4,className:"mt-1"})))},{key:"configuration",label:r.createElement("div",{className:"flex items-center gap-2"},r.createElement(m.A,{className:"w-4 h-4 text-green-500"}),r.createElement("span",{className:"font-medium"},"Tool Configuration")),children:r.createElement("div",{className:"space-y-4"},r.createElement("label",{className:"block"},r.createElement("span",{className:"text-sm font-medium text-gray-700"},"Function Name"),r.createElement(o.A,{value:t.config.name||"",onChange:e=>$({config:{...t.config,name:e.target.value}}),placeholder:"Function name",className:"mt-1"})),r.createElement("div",{className:"space-y-2"},r.createElement("span",{className:"text-sm font-medium text-gray-700"},"Global Imports"),r.createElement("div",{className:"flex flex-wrap gap-2 mt-2"},(t.config.global_imports||[]).map((e,n)=>r.createElement("div",{key:n,className:"flex items-center gap-2 bg-tertiary rounded px-2 py-1"},r.createElement("span",{className:"text-sm"},(e=>e?"string"==typeof e?e:`from ${e.module} import ${e.imports.join(", ")}`:"")(e)),r.createElement(s.Ay,{type:"text",size:"small",className:"flex items-center justify-center h-6 w-6 p-0",onClick:()=>(e=>{const n=(0,a.A)(t.config.global_imports||[]);n.splice(e,1),$({config:{...t.config,global_imports:n}})})(n),icon:r.createElement(p.A,{className:"h-4 w-4"})})))),E?r.createElement("div",{className:"border rounded p-3 space-y-3"},r.createElement(i.A,{value:N,onChange:w,style:{width:200}},r.createElement(v,{value:"direct"},"Direct Import"),r.createElement(v,{value:"fromModule"},"From Module Import")),"direct"===N?r.createElement(c.A,null,r.createElement(o.A,{placeholder:"Package name (e.g., os)",className:"w-64",value:A,onChange:e=>C(e.target.value),onKeyDown:e=>{"Enter"===e.key&&A&&O()}}),r.createElement(s.Ay,{onClick:O,disabled:!A},"Add")):r.createElement(c.A,{direction:"vertical",className:"w-full"},r.createElement(o.A,{placeholder:"Module name (e.g., typing)",className:"w-64",value:S.module,onChange:e=>k(t=>({...t,module:e.target.value}))}),r.createElement(c.A,{className:"w-full"},r.createElement(o.A,{placeholder:"Import names (comma-separated)",className:"w-64",value:S.imports,onChange:e=>k(t=>({...t,imports:e.target.value}))}),r.createElement(s.Ay,{onClick:O,disabled:!S.module||!S.imports},"Add")))):r.createElement(s.Ay,{type:"dashed",onClick:()=>x(!0),className:"w-full"},r.createElement(g.A,{className:"h-4 w-4 mr-2"}),"Add Import")),r.createElement("label",{className:"block"},r.createElement("span",{className:"text-sm font-medium text-gray-700"},"Source Code"),r.createElement("div",{className:"mt-1 h-96"},r.createElement(h.T,{value:t.config.source_code||"",editorRef:y,language:"python",onChange:e=>$({config:{...t.config,source_code:e}})}))),r.createElement("div",{className:"flex items-center gap-2"},r.createElement("span",{className:"text-sm font-medium text-gray-700"},"Has Cancellation Support"),r.createElement(d.A,{checked:t.config.has_cancellation_support||!1,onChange:e=>$({config:{...t.config,has_cancellation_support:e}})})))}]})}},8107:function(e,t,n){n.d(t,{dv:function(){return ra}});var a=n(436),r=n(6540),o=n(9957),i=n(9314),l=n(696),s=n(677),c=n(126),d=n(2941),u=n(2702),m=n(7213),p=n(964),g=n(7133),f=n(590),h=n(6816),b=n(2708),v=n(827),y=n(3140),E=n(4279),x=n(8073),N=n(230),w=n(4716),A=n(7260),C=n(6914),S=n(7072),k=n(7308),$=n(6942),O=n.n($),I=n(754),z=n(4121),M=n(682),T=n(2279),R=n(2187),P=n(5905),j=n(1108),_=n(4277),B=n(7358);const H=new R.Mo("antStatusProcessing",{"0%":{transform:"scale(0.8)",opacity:.5},"100%":{transform:"scale(2.4)",opacity:0}}),D=new R.Mo("antZoomBadgeIn",{"0%":{transform:"scale(0) translate(50%, -50%)",opacity:0},"100%":{transform:"scale(1) translate(50%, -50%)"}}),L=new R.Mo("antZoomBadgeOut",{"0%":{transform:"scale(1) translate(50%, -50%)"},"100%":{transform:"scale(0) translate(50%, -50%)",opacity:0}}),F=new R.Mo("antNoWrapperZoomBadgeIn",{"0%":{transform:"scale(0)",opacity:0},"100%":{transform:"scale(1)"}}),W=new R.Mo("antNoWrapperZoomBadgeOut",{"0%":{transform:"scale(1)"},"100%":{transform:"scale(0)",opacity:0}}),q=new R.Mo("antBadgeLoadingCircle",{"0%":{transformOrigin:"50%"},"100%":{transform:"translate(50%, -50%) rotate(360deg)",transformOrigin:"50%"}}),X=e=>{const{fontHeight:t,lineWidth:n,marginXS:a,colorBorderBg:r}=e,o=t,i=n,l=e.colorTextLightSolid,s=e.colorError,c=e.colorErrorHover;return(0,_.oX)(e,{badgeFontHeight:o,badgeShadowSize:i,badgeTextColor:l,badgeColor:s,badgeColorHover:c,badgeShadowColor:r,badgeProcessingDuration:"1.2s",badgeRibbonOffset:a,badgeRibbonCornerTransform:"scaleY(0.75)",badgeRibbonCornerFilter:"brightness(75%)"})},G=e=>{const{fontSize:t,lineHeight:n,fontSizeSM:a,lineWidth:r}=e;return{indicatorZIndex:"auto",indicatorHeight:Math.round(t*n)-2*r,indicatorHeightSM:t,dotSize:a/2,textFontSize:a,textFontSizeSM:a,textFontWeight:"normal",statusSize:a/2}};var U=(0,B.OF)("Badge",e=>(e=>{const{componentCls:t,iconCls:n,antCls:a,badgeShadowSize:r,textFontSize:o,textFontSizeSM:i,statusSize:l,dotSize:s,textFontWeight:c,indicatorHeight:d,indicatorHeightSM:u,marginXS:m,calc:p}=e,g=`${a}-scroll-number`,f=(0,j.A)(e,(e,{darkColor:n})=>({[`&${t} ${t}-color-${e}`]:{background:n,[`&:not(${t}-count)`]:{color:n},"a:hover &":{background:n}}}));return{[t]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,P.dF)(e)),{position:"relative",display:"inline-block",width:"fit-content",lineHeight:1,[`${t}-count`]:{display:"inline-flex",justifyContent:"center",zIndex:e.indicatorZIndex,minWidth:d,height:d,color:e.badgeTextColor,fontWeight:c,fontSize:o,lineHeight:(0,R.zA)(d),whiteSpace:"nowrap",textAlign:"center",background:e.badgeColor,borderRadius:p(d).div(2).equal(),boxShadow:`0 0 0 ${(0,R.zA)(r)} ${e.badgeShadowColor}`,transition:`background ${e.motionDurationMid}`,a:{color:e.badgeTextColor},"a:hover":{color:e.badgeTextColor},"a:hover &":{background:e.badgeColorHover}},[`${t}-count-sm`]:{minWidth:u,height:u,fontSize:i,lineHeight:(0,R.zA)(u),borderRadius:p(u).div(2).equal()},[`${t}-multiple-words`]:{padding:`0 ${(0,R.zA)(e.paddingXS)}`,bdi:{unicodeBidi:"plaintext"}},[`${t}-dot`]:{zIndex:e.indicatorZIndex,width:s,minWidth:s,height:s,background:e.badgeColor,borderRadius:"100%",boxShadow:`0 0 0 ${(0,R.zA)(r)} ${e.badgeShadowColor}`},[`${t}-count, ${t}-dot, ${g}-custom-component`]:{position:"absolute",top:0,insetInlineEnd:0,transform:"translate(50%, -50%)",transformOrigin:"100% 0%",[`&${n}-spin`]:{animationName:q,animationDuration:"1s",animationIterationCount:"infinite",animationTimingFunction:"linear"}},[`&${t}-status`]:{lineHeight:"inherit",verticalAlign:"baseline",[`${t}-status-dot`]:{position:"relative",top:-1,display:"inline-block",width:l,height:l,verticalAlign:"middle",borderRadius:"50%"},[`${t}-status-success`]:{backgroundColor:e.colorSuccess},[`${t}-status-processing`]:{overflow:"visible",color:e.colorInfo,backgroundColor:e.colorInfo,borderColor:"currentcolor","&::after":{position:"absolute",top:0,insetInlineStart:0,width:"100%",height:"100%",borderWidth:r,borderStyle:"solid",borderColor:"inherit",borderRadius:"50%",animationName:H,animationDuration:e.badgeProcessingDuration,animationIterationCount:"infinite",animationTimingFunction:"ease-in-out",content:'""'}},[`${t}-status-default`]:{backgroundColor:e.colorTextPlaceholder},[`${t}-status-error`]:{backgroundColor:e.colorError},[`${t}-status-warning`]:{backgroundColor:e.colorWarning},[`${t}-status-text`]:{marginInlineStart:m,color:e.colorText,fontSize:e.fontSize}}}),f),{[`${t}-zoom-appear, ${t}-zoom-enter`]:{animationName:D,animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseOutBack,animationFillMode:"both"},[`${t}-zoom-leave`]:{animationName:L,animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseOutBack,animationFillMode:"both"},[`&${t}-not-a-wrapper`]:{[`${t}-zoom-appear, ${t}-zoom-enter`]:{animationName:F,animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseOutBack},[`${t}-zoom-leave`]:{animationName:W,animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseOutBack},[`&:not(${t}-status)`]:{verticalAlign:"middle"},[`${g}-custom-component, ${t}-count`]:{transform:"none"},[`${g}-custom-component, ${g}`]:{position:"relative",top:"auto",display:"block",transformOrigin:"50% 50%"}},[g]:{overflow:"hidden",transition:`all ${e.motionDurationMid} ${e.motionEaseOutBack}`,[`${g}-only`]:{position:"relative",display:"inline-block",height:d,transition:`all ${e.motionDurationSlow} ${e.motionEaseOutBack}`,WebkitTransformStyle:"preserve-3d",WebkitBackfaceVisibility:"hidden",[`> p${g}-only-unit`]:{height:d,margin:0,WebkitTransformStyle:"preserve-3d",WebkitBackfaceVisibility:"hidden"}},[`${g}-symbol`]:{verticalAlign:"top"}},"&-rtl":{direction:"rtl",[`${t}-count, ${t}-dot, ${g}-custom-component`]:{transform:"translate(-50%, -50%)"}}})}})(X(e)),G);var V=(0,B.OF)(["Badge","Ribbon"],e=>(e=>{const{antCls:t,badgeFontHeight:n,marginXS:a,badgeRibbonOffset:r,calc:o}=e,i=`${t}-ribbon`,l=`${t}-ribbon-wrapper`,s=(0,j.A)(e,(e,{darkColor:t})=>({[`&${i}-color-${e}`]:{background:t,color:t}}));return{[l]:{position:"relative"},[i]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,P.dF)(e)),{position:"absolute",top:a,padding:`0 ${(0,R.zA)(e.paddingXS)}`,color:e.colorPrimary,lineHeight:(0,R.zA)(n),whiteSpace:"nowrap",backgroundColor:e.colorPrimary,borderRadius:e.borderRadiusSM,[`${i}-text`]:{color:e.badgeTextColor},[`${i}-corner`]:{position:"absolute",top:"100%",width:r,height:r,color:"currentcolor",border:`${(0,R.zA)(o(r).div(2).equal())} solid`,transform:e.badgeRibbonCornerTransform,transformOrigin:"top",filter:e.badgeRibbonCornerFilter}}),s),{[`&${i}-placement-end`]:{insetInlineEnd:o(r).mul(-1).equal(),borderEndEndRadius:0,[`${i}-corner`]:{insetInlineEnd:0,borderInlineEndColor:"transparent",borderBlockEndColor:"transparent"}},[`&${i}-placement-start`]:{insetInlineStart:o(r).mul(-1).equal(),borderEndStartRadius:0,[`${i}-corner`]:{insetInlineStart:0,borderBlockEndColor:"transparent",borderInlineStartColor:"transparent"}},"&-rtl":{direction:"rtl"}})}})(X(e)),G);var Y=e=>{const{className:t,prefixCls:n,style:a,color:o,children:i,text:l,placement:s="end",rootClassName:c}=e,{getPrefixCls:d,direction:u}=r.useContext(T.QO),m=d("ribbon",n),p=`${m}-wrapper`,[g,f,h]=V(m,p),b=(0,z.nP)(o,!1),v=O()(m,`${m}-placement-${s}`,{[`${m}-rtl`]:"rtl"===u,[`${m}-color-${o}`]:b},t),y={},E={};return o&&!b&&(y.background=o,E.color=o),g(r.createElement("div",{className:O()(p,c,f,h)},i,r.createElement("div",{className:O()(v,f),style:Object.assign(Object.assign({},y),a)},r.createElement("span",{className:`${m}-text`},l),r.createElement("div",{className:`${m}-corner`,style:E}))))};const K=e=>{const{prefixCls:t,value:n,current:a,offset:o=0}=e;let i;return o&&(i={position:"absolute",top:`${o}00%`,left:0}),r.createElement("span",{style:i,className:O()(`${t}-only-unit`,{current:a})},n)};function Z(e,t,n){let a=e,r=0;for(;(a+10)%10!==t;)a+=n,r+=n;return r}var J=e=>{const{prefixCls:t,count:n,value:a}=e,o=Number(a),i=Math.abs(n),[l,s]=r.useState(o),[c,d]=r.useState(i),u=()=>{s(o),d(i)};let m,p;if(r.useEffect(()=>{const e=setTimeout(u,1e3);return()=>clearTimeout(e)},[o]),l===o||Number.isNaN(o)||Number.isNaN(l))m=[r.createElement(K,Object.assign({},e,{key:o,current:!0}))],p={transition:"none"};else{m=[];const t=o+10,n=[];for(let e=o;e<=t;e+=1)n.push(e);const a=c<i?1:-1,s=n.findIndex(e=>e%10===l);m=(a<0?n.slice(0,s+1):n.slice(s)).map((t,n)=>{const o=t%10;return r.createElement(K,Object.assign({},e,{key:t,value:o,offset:a<0?n-s:n,current:n===s}))}),p={transform:`translateY(${-Z(l,o,a)}00%)`}}return r.createElement("span",{className:`${t}-only`,style:p,onTransitionEnd:u},m)},Q=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]])}return n};const ee=r.forwardRef((e,t)=>{const{prefixCls:n,count:a,className:o,motionClassName:i,style:l,title:s,show:c,component:d="sup",children:u}=e,m=Q(e,["prefixCls","count","className","motionClassName","style","title","show","component","children"]),{getPrefixCls:p}=r.useContext(T.QO),g=p("scroll-number",n),f=Object.assign(Object.assign({},m),{"data-show":c,style:l,className:O()(g,o,i),title:s});let h=a;if(a&&Number(a)%1==0){const e=String(a).split("");h=r.createElement("bdi",null,e.map((t,n)=>r.createElement(J,{prefixCls:g,count:Number(a),value:t,key:e.length-n})))}return(null==l?void 0:l.borderColor)&&(f.style=Object.assign(Object.assign({},l),{boxShadow:`0 0 0 1px ${l.borderColor} inset`})),u?(0,M.Ob)(u,e=>({className:O()(`${g}-custom-component`,null==e?void 0:e.className,i)})):r.createElement(d,Object.assign({},f,{ref:t}),h)});var te=ee,ne=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]])}return n};const ae=r.forwardRef((e,t)=>{var n,a,o,i,l;const{prefixCls:s,scrollNumberPrefixCls:c,children:d,status:u,text:m,color:p,count:g=null,overflowCount:f=99,dot:h=!1,size:b="default",title:v,offset:y,style:E,className:x,rootClassName:N,classNames:w,styles:A,showZero:C=!1}=e,S=ne(e,["prefixCls","scrollNumberPrefixCls","children","status","text","color","count","overflowCount","dot","size","title","offset","style","className","rootClassName","classNames","styles","showZero"]),{getPrefixCls:k,direction:$,badge:R}=r.useContext(T.QO),P=k("badge",s),[j,_,B]=U(P),H=g>f?`${f}+`:g,D="0"===H||0===H,L=null===g||D&&!C,F=(null!=u||null!=p)&&L,W=null!=u||!D,q=h&&!D,X=q?"":H,G=(0,r.useMemo)(()=>(null==X||""===X||D&&!C)&&!q,[X,D,C,q]),V=(0,r.useRef)(g);G||(V.current=g);const Y=V.current,K=(0,r.useRef)(X);G||(K.current=X);const Z=K.current,J=(0,r.useRef)(q);G||(J.current=q);const Q=(0,r.useMemo)(()=>{if(!y)return Object.assign(Object.assign({},null==R?void 0:R.style),E);const e={marginTop:y[1]};return"rtl"===$?e.left=parseInt(y[0],10):e.right=-parseInt(y[0],10),Object.assign(Object.assign(Object.assign({},e),null==R?void 0:R.style),E)},[$,y,E,null==R?void 0:R.style]),ee=null!=v?v:"string"==typeof Y||"number"==typeof Y?Y:void 0,ae=G||!m?null:r.createElement("span",{className:`${P}-status-text`},m),re=Y&&"object"==typeof Y?(0,M.Ob)(Y,e=>({style:Object.assign(Object.assign({},Q),e.style)})):void 0,oe=(0,z.nP)(p,!1),ie=O()(null==w?void 0:w.indicator,null===(n=null==R?void 0:R.classNames)||void 0===n?void 0:n.indicator,{[`${P}-status-dot`]:F,[`${P}-status-${u}`]:!!u,[`${P}-color-${p}`]:oe}),le={};p&&!oe&&(le.color=p,le.background=p);const se=O()(P,{[`${P}-status`]:F,[`${P}-not-a-wrapper`]:!d,[`${P}-rtl`]:"rtl"===$},x,N,null==R?void 0:R.className,null===(a=null==R?void 0:R.classNames)||void 0===a?void 0:a.root,null==w?void 0:w.root,_,B);if(!d&&F&&(m||W||!L)){const e=Q.color;return j(r.createElement("span",Object.assign({},S,{className:se,style:Object.assign(Object.assign(Object.assign({},null==A?void 0:A.root),null===(o=null==R?void 0:R.styles)||void 0===o?void 0:o.root),Q)}),r.createElement("span",{className:ie,style:Object.assign(Object.assign(Object.assign({},null==A?void 0:A.indicator),null===(i=null==R?void 0:R.styles)||void 0===i?void 0:i.indicator),le)}),m&&r.createElement("span",{style:{color:e},className:`${P}-status-text`},m)))}return j(r.createElement("span",Object.assign({ref:t},S,{className:se,style:Object.assign(Object.assign({},null===(l=null==R?void 0:R.styles)||void 0===l?void 0:l.root),null==A?void 0:A.root)}),d,r.createElement(I.Ay,{visible:!G,motionName:`${P}-zoom`,motionAppear:!1,motionDeadline:1e3},({className:e})=>{var t,n;const a=k("scroll-number",c),o=J.current,i=O()(null==w?void 0:w.indicator,null===(t=null==R?void 0:R.classNames)||void 0===t?void 0:t.indicator,{[`${P}-dot`]:o,[`${P}-count`]:!o,[`${P}-count-sm`]:"small"===b,[`${P}-multiple-words`]:!o&&Z&&Z.toString().length>1,[`${P}-status-${u}`]:!!u,[`${P}-color-${p}`]:oe});let l=Object.assign(Object.assign(Object.assign({},null==A?void 0:A.indicator),null===(n=null==R?void 0:R.styles)||void 0===n?void 0:n.indicator),Q);return p&&!oe&&(l=l||{},l.background=p),r.createElement(te,{prefixCls:a,show:!G,motionClassName:e,className:i,count:Z,title:ee,style:l,key:"scrollNumber"},re)}),ae))}),re=ae;re.Ribbon=Y;var oe=re,ie=n(8168),le=n(5544),se=n(3986),ce=n(4467),de=n(9379),ue=n(2284),me=n(2533),pe=n(9853),ge=n(8719),fe=n(981),he=function(e,t){if(!e)return null;var n={left:e.offsetLeft,right:e.parentElement.clientWidth-e.clientWidth-e.offsetLeft,width:e.clientWidth,top:e.offsetTop,bottom:e.parentElement.clientHeight-e.clientHeight-e.offsetTop,height:e.clientHeight};return t?{left:0,right:0,width:0,top:n.top,bottom:n.bottom,height:n.height}:{left:n.left,right:n.right,width:n.width,top:0,bottom:0,height:0}},be=function(e){return void 0!==e?"".concat(e,"px"):void 0};function ve(e){var t=e.prefixCls,n=e.containerRef,a=e.value,o=e.getValueIndex,i=e.motionName,l=e.onMotionStart,s=e.onMotionEnd,c=e.direction,d=e.vertical,u=void 0!==d&&d,m=r.useRef(null),p=r.useState(a),g=(0,le.A)(p,2),f=g[0],h=g[1],b=function(e){var a,r=o(e),i=null===(a=n.current)||void 0===a?void 0:a.querySelectorAll(".".concat(t,"-item"))[r];return(null==i?void 0:i.offsetParent)&&i},v=r.useState(null),y=(0,le.A)(v,2),E=y[0],x=y[1],N=r.useState(null),w=(0,le.A)(N,2),A=w[0],C=w[1];(0,fe.A)(function(){if(f!==a){var e=b(f),t=b(a),n=he(e,u),r=he(t,u);h(a),x(n),C(r),e&&t?l():s()}},[a]);var S=r.useMemo(function(){var e;return be(u?null!==(e=null==E?void 0:E.top)&&void 0!==e?e:0:"rtl"===c?-(null==E?void 0:E.right):null==E?void 0:E.left)},[u,c,E]),k=r.useMemo(function(){var e;return be(u?null!==(e=null==A?void 0:A.top)&&void 0!==e?e:0:"rtl"===c?-(null==A?void 0:A.right):null==A?void 0:A.left)},[u,c,A]);return E&&A?r.createElement(I.Ay,{visible:!0,motionName:i,motionAppear:!0,onAppearStart:function(){return u?{transform:"translateY(var(--thumb-start-top))",height:"var(--thumb-start-height)"}:{transform:"translateX(var(--thumb-start-left))",width:"var(--thumb-start-width)"}},onAppearActive:function(){return u?{transform:"translateY(var(--thumb-active-top))",height:"var(--thumb-active-height)"}:{transform:"translateX(var(--thumb-active-left))",width:"var(--thumb-active-width)"}},onVisibleChanged:function(){x(null),C(null),s()}},function(e,n){var a=e.className,o=e.style,i=(0,de.A)((0,de.A)({},o),{},{"--thumb-start-left":S,"--thumb-start-width":be(null==E?void 0:E.width),"--thumb-active-left":k,"--thumb-active-width":be(null==A?void 0:A.width),"--thumb-start-top":S,"--thumb-start-height":be(null==E?void 0:E.height),"--thumb-active-top":k,"--thumb-active-height":be(null==A?void 0:A.height)}),l={ref:(0,ge.K4)(m,n),style:i,className:O()("".concat(t,"-thumb"),a)};return r.createElement("div",l)}):null}var ye=["prefixCls","direction","vertical","options","disabled","defaultValue","value","name","onChange","className","motionName"];function Ee(e){return e.map(function(e){if("object"===(0,ue.A)(e)&&null!==e){var t=function(e){return void 0!==e.title?e.title:"object"!==(0,ue.A)(e.label)?null===(t=e.label)||void 0===t?void 0:t.toString():void 0;var t}(e);return(0,de.A)((0,de.A)({},e),{},{title:t})}return{label:null==e?void 0:e.toString(),title:null==e?void 0:e.toString(),value:e}})}var xe=function(e){var t=e.prefixCls,n=e.className,a=e.disabled,o=e.checked,i=e.label,l=e.title,s=e.value,c=e.name,d=e.onChange,u=e.onFocus,m=e.onBlur,p=e.onKeyDown,g=e.onKeyUp,f=e.onMouseDown;return r.createElement("label",{className:O()(n,(0,ce.A)({},"".concat(t,"-item-disabled"),a)),onMouseDown:f},r.createElement("input",{name:c,className:"".concat(t,"-item-input"),type:"radio",disabled:a,checked:o,onChange:function(e){a||d(e,s)},onFocus:u,onBlur:m,onKeyDown:p,onKeyUp:g}),r.createElement("div",{className:"".concat(t,"-item-label"),title:l,"aria-selected":o},i))};var Ne=r.forwardRef(function(e,t){var n,a,o=e.prefixCls,i=void 0===o?"rc-segmented":o,l=e.direction,s=e.vertical,c=e.options,d=void 0===c?[]:c,u=e.disabled,m=e.defaultValue,p=e.value,g=e.name,f=e.onChange,h=e.className,b=void 0===h?"":h,v=e.motionName,y=void 0===v?"thumb-motion":v,E=(0,se.A)(e,ye),x=r.useRef(null),N=r.useMemo(function(){return(0,ge.K4)(x,t)},[x,t]),w=r.useMemo(function(){return Ee(d)},[d]),A=(0,me.A)(null===(n=w[0])||void 0===n?void 0:n.value,{value:p,defaultValue:m}),C=(0,le.A)(A,2),S=C[0],k=C[1],$=r.useState(!1),I=(0,le.A)($,2),z=I[0],M=I[1],T=function(e,t){k(t),null==f||f(t)},R=(0,pe.A)(E,["children"]),P=r.useState(!1),j=(0,le.A)(P,2),_=j[0],B=j[1],H=r.useState(!1),D=(0,le.A)(H,2),L=D[0],F=D[1],W=function(){F(!0)},q=function(){F(!1)},X=function(){B(!1)},G=function(e){"Tab"===e.key&&B(!0)},U=function(e){var t=w.findIndex(function(e){return e.value===S}),n=w.length,a=w[(t+e+n)%n];a&&(k(a.value),null==f||f(a.value))},V=function(e){switch(e.key){case"ArrowLeft":case"ArrowUp":U(-1);break;case"ArrowRight":case"ArrowDown":U(1)}};return r.createElement("div",(0,ie.A)({role:"radiogroup","aria-label":"segmented control",tabIndex:u?void 0:0},R,{className:O()(i,(a={},(0,ce.A)(a,"".concat(i,"-rtl"),"rtl"===l),(0,ce.A)(a,"".concat(i,"-disabled"),u),(0,ce.A)(a,"".concat(i,"-vertical"),s),a),b),ref:N}),r.createElement("div",{className:"".concat(i,"-group")},r.createElement(ve,{vertical:s,prefixCls:i,value:S,containerRef:x,motionName:"".concat(i,"-").concat(y),direction:l,getValueIndex:function(e){return w.findIndex(function(t){return t.value===e})},onMotionStart:function(){M(!0)},onMotionEnd:function(){M(!1)}}),w.map(function(e){var t;return r.createElement(xe,(0,ie.A)({},e,{name:g,key:e.value,prefixCls:i,className:O()(e.className,"".concat(i,"-item"),(t={},(0,ce.A)(t,"".concat(i,"-item-selected"),e.value===S&&!z),(0,ce.A)(t,"".concat(i,"-item-focused"),L&&_&&e.value===S),t)),checked:e.value===S,onChange:T,onFocus:W,onBlur:q,onKeyDown:V,onKeyUp:G,onMouseDown:X,disabled:!!u||!!e.disabled}))})))}),we=n(6855),Ae=n(829);function Ce(e,t){return{[`${e}, ${e}:hover, ${e}:focus`]:{color:t.colorTextDisabled,cursor:"not-allowed"}}}function Se(e){return{backgroundColor:e.itemSelectedBg,boxShadow:e.boxShadowTertiary}}const ke=Object.assign({overflow:"hidden"},P.L9),$e=e=>{const{componentCls:t}=e,n=e.calc(e.controlHeight).sub(e.calc(e.trackPadding).mul(2)).equal(),a=e.calc(e.controlHeightLG).sub(e.calc(e.trackPadding).mul(2)).equal(),r=e.calc(e.controlHeightSM).sub(e.calc(e.trackPadding).mul(2)).equal();return{[t]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},(0,P.dF)(e)),{display:"inline-block",padding:e.trackPadding,color:e.itemColor,background:e.trackBg,borderRadius:e.borderRadius,transition:`all ${e.motionDurationMid} ${e.motionEaseInOut}`}),(0,P.K8)(e)),{[`${t}-group`]:{position:"relative",display:"flex",alignItems:"stretch",justifyItems:"flex-start",flexDirection:"row",width:"100%"},[`&${t}-rtl`]:{direction:"rtl"},[`&${t}-vertical`]:{[`${t}-group`]:{flexDirection:"column"},[`${t}-thumb`]:{width:"100%",height:0,padding:`0 ${(0,R.zA)(e.paddingXXS)}`}},[`&${t}-block`]:{display:"flex"},[`&${t}-block ${t}-item`]:{flex:1,minWidth:0},[`${t}-item`]:{position:"relative",textAlign:"center",cursor:"pointer",transition:`color ${e.motionDurationMid} ${e.motionEaseInOut}`,borderRadius:e.borderRadiusSM,transform:"translateZ(0)","&-selected":Object.assign(Object.assign({},Se(e)),{color:e.itemSelectedColor}),"&-focused":Object.assign({},(0,P.jk)(e)),"&::after":{content:'""',position:"absolute",zIndex:-1,width:"100%",height:"100%",top:0,insetInlineStart:0,borderRadius:"inherit",opacity:0,transition:`opacity ${e.motionDurationMid}`,pointerEvents:"none"},[`&:hover:not(${t}-item-selected):not(${t}-item-disabled)`]:{color:e.itemHoverColor,"&::after":{opacity:1,backgroundColor:e.itemHoverBg}},[`&:active:not(${t}-item-selected):not(${t}-item-disabled)`]:{color:e.itemHoverColor,"&::after":{opacity:1,backgroundColor:e.itemActiveBg}},"&-label":Object.assign({minHeight:n,lineHeight:(0,R.zA)(n),padding:`0 ${(0,R.zA)(e.segmentedPaddingHorizontal)}`},ke),"&-icon + *":{marginInlineStart:e.calc(e.marginSM).div(2).equal()},"&-input":{position:"absolute",insetBlockStart:0,insetInlineStart:0,width:0,height:0,opacity:0,pointerEvents:"none"}},[`${t}-thumb`]:Object.assign(Object.assign({},Se(e)),{position:"absolute",insetBlockStart:0,insetInlineStart:0,width:0,height:"100%",padding:`${(0,R.zA)(e.paddingXXS)} 0`,borderRadius:e.borderRadiusSM,transition:`transform ${e.motionDurationSlow} ${e.motionEaseInOut}, height ${e.motionDurationSlow} ${e.motionEaseInOut}`,[`& ~ ${t}-item:not(${t}-item-selected):not(${t}-item-disabled)::after`]:{backgroundColor:"transparent"}}),[`&${t}-lg`]:{borderRadius:e.borderRadiusLG,[`${t}-item-label`]:{minHeight:a,lineHeight:(0,R.zA)(a),padding:`0 ${(0,R.zA)(e.segmentedPaddingHorizontal)}`,fontSize:e.fontSizeLG},[`${t}-item, ${t}-thumb`]:{borderRadius:e.borderRadius}},[`&${t}-sm`]:{borderRadius:e.borderRadiusSM,[`${t}-item-label`]:{minHeight:r,lineHeight:(0,R.zA)(r),padding:`0 ${(0,R.zA)(e.segmentedPaddingHorizontalSM)}`},[`${t}-item, ${t}-thumb`]:{borderRadius:e.borderRadiusXS}}}),Ce(`&-disabled ${t}-item`,e)),Ce(`${t}-item-disabled`,e)),{[`${t}-thumb-motion-appear-active`]:{transition:`transform ${e.motionDurationSlow} ${e.motionEaseInOut}, width ${e.motionDurationSlow} ${e.motionEaseInOut}`,willChange:"transform, width"},[`&${t}-shape-round`]:{borderRadius:9999,[`${t}-item, ${t}-thumb`]:{borderRadius:9999}}})}};var Oe=(0,B.OF)("Segmented",e=>{const{lineWidth:t,calc:n}=e,a=(0,_.oX)(e,{segmentedPaddingHorizontal:n(e.controlPaddingHorizontal).sub(t).equal(),segmentedPaddingHorizontalSM:n(e.controlPaddingHorizontalSM).sub(t).equal()});return[$e(a)]},e=>{const{colorTextLabel:t,colorText:n,colorFillSecondary:a,colorBgElevated:r,colorFill:o,lineWidthBold:i,colorBgLayout:l}=e;return{trackPadding:i,trackBg:l,itemColor:t,itemHoverColor:n,itemHoverBg:a,itemSelectedBg:r,itemActiveBg:o,itemSelectedColor:n}}),Ie=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]])}return n};const ze=r.forwardRef((e,t)=>{const n=(0,we.A)(),{prefixCls:a,className:o,rootClassName:i,block:l,options:s=[],size:c="middle",style:d,vertical:u,shape:m="default",name:p=n}=e,g=Ie(e,["prefixCls","className","rootClassName","block","options","size","style","vertical","shape","name"]),{getPrefixCls:f,direction:h,className:b,style:v}=(0,T.TP)("segmented"),y=f("segmented",a),[E,x,N]=Oe(y),w=(0,Ae.A)(c),A=r.useMemo(()=>s.map(e=>{if(function(e){return"object"==typeof e&&!!(null==e?void 0:e.icon)}(e)){const{icon:t,label:n}=e,a=Ie(e,["icon","label"]);return Object.assign(Object.assign({},a),{label:r.createElement(r.Fragment,null,r.createElement("span",{className:`${y}-item-icon`},t),n&&r.createElement("span",null,n))})}return e}),[s,y]),C=O()(o,i,b,{[`${y}-block`]:l,[`${y}-sm`]:"small"===w,[`${y}-lg`]:"large"===w,[`${y}-vertical`]:u,[`${y}-shape-${m}`]:"round"===m},x,N),S=Object.assign(Object.assign({},v),d);return E(r.createElement(Ne,Object.assign({},g,{name:p,className:C,style:S,options:A,ref:t,prefixCls:y,direction:h,vertical:u})))});var Me=ze,Te=n(1788);const Re=(0,Te.A)("Unplug",[["path",{d:"m19 5 3-3",key:"yk6iyv"}],["path",{d:"m2 22 3-3",key:"19mgm9"}],["path",{d:"M6.3 20.3a2.4 2.4 0 0 0 3.4 0L12 18l-6-6-2.3 2.3a2.4 2.4 0 0 0 0 3.4Z",key:"goz73y"}],["path",{d:"M7.5 13.5 10 11",key:"7xgeeb"}],["path",{d:"M10.5 16.5 13 14",key:"10btkg"}],["path",{d:"m12 6 6 6 2.3-2.3a2.4 2.4 0 0 0 0-3.4l-2.6-2.6a2.4 2.4 0 0 0-3.4 0Z",key:"1snsnr"}]]),Pe=(0,Te.A)("Wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]]);var je=n(5731);const _e=(0,Te.A)("WifiOff",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]),Be=(0,Te.A)("Activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]);var He=n(7799),De=n(4471),Le=n(7504),Fe=n(9709),We=n(234);function qe(){return{width:document.documentElement.clientWidth,height:window.innerHeight||document.documentElement.clientHeight}}var Xe=n(3766),Ge=n(961);function Ue(e,t,n,a){var r=Ge.unstable_batchedUpdates?function(e){Ge.unstable_batchedUpdates(n,e)}:n;return null!=e&&e.addEventListener&&e.addEventListener(t,r,a),{remove:function(){null!=e&&e.removeEventListener&&e.removeEventListener(t,r,a)}}}var Ve=n(6928),Ye=n(2595),Ke=r.createContext(null),Ze=function(e){var t=e.visible,n=e.maskTransitionName,a=e.getContainer,o=e.prefixCls,i=e.rootClassName,l=e.icons,s=e.countRender,c=e.showSwitch,d=e.showProgress,u=e.current,m=e.transform,p=e.count,g=e.scale,f=e.minScale,h=e.maxScale,b=e.closeIcon,v=e.onActive,y=e.onClose,E=e.onZoomIn,x=e.onZoomOut,N=e.onRotateRight,w=e.onRotateLeft,A=e.onFlipX,C=e.onFlipY,S=e.onReset,k=e.toolbarRender,$=e.zIndex,z=e.image,M=(0,r.useContext)(Ke),T=l.rotateLeft,R=l.rotateRight,P=l.zoomIn,j=l.zoomOut,_=l.close,B=l.left,H=l.right,D=l.flipX,L=l.flipY,F="".concat(o,"-operations-operation");r.useEffect(function(){var e=function(e){e.keyCode===Ve.A.ESC&&y()};return t&&window.addEventListener("keydown",e),function(){window.removeEventListener("keydown",e)}},[t]);var W=function(e,t){e.preventDefault(),e.stopPropagation(),v(t)},q=r.useCallback(function(e){var t=e.type,n=e.disabled,a=e.onClick,i=e.icon;return r.createElement("div",{key:t,className:O()(F,"".concat(o,"-operations-operation-").concat(t),(0,ce.A)({},"".concat(o,"-operations-operation-disabled"),!!n)),onClick:a},i)},[F,o]),X=c?q({icon:B,onClick:function(e){return W(e,-1)},type:"prev",disabled:0===u}):void 0,G=c?q({icon:H,onClick:function(e){return W(e,1)},type:"next",disabled:u===p-1}):void 0,U=q({icon:L,onClick:C,type:"flipY"}),V=q({icon:D,onClick:A,type:"flipX"}),Y=q({icon:T,onClick:w,type:"rotateLeft"}),K=q({icon:R,onClick:N,type:"rotateRight"}),Z=q({icon:j,onClick:x,type:"zoomOut",disabled:g<=f}),J=q({icon:P,onClick:E,type:"zoomIn",disabled:g===h}),Q=r.createElement("div",{className:"".concat(o,"-operations")},U,V,Y,K,Z,J);return r.createElement(I.Ay,{visible:t,motionName:n},function(e){var t=e.className,n=e.style;return r.createElement(Ye.A,{open:!0,getContainer:null!=a?a:document.body},r.createElement("div",{className:O()("".concat(o,"-operations-wrapper"),t,i),style:(0,de.A)((0,de.A)({},n),{},{zIndex:$})},null===b?null:r.createElement("button",{className:"".concat(o,"-close"),onClick:y},b||_),c&&r.createElement(r.Fragment,null,r.createElement("div",{className:O()("".concat(o,"-switch-left"),(0,ce.A)({},"".concat(o,"-switch-left-disabled"),0===u)),onClick:function(e){return W(e,-1)}},B),r.createElement("div",{className:O()("".concat(o,"-switch-right"),(0,ce.A)({},"".concat(o,"-switch-right-disabled"),u===p-1)),onClick:function(e){return W(e,1)}},H)),r.createElement("div",{className:"".concat(o,"-footer")},d&&r.createElement("div",{className:"".concat(o,"-progress")},s?s(u+1,p):r.createElement("bdi",null,"".concat(u+1," / ").concat(p))),k?k(Q,(0,de.A)((0,de.A)({icons:{prevIcon:X,nextIcon:G,flipYIcon:U,flipXIcon:V,rotateLeftIcon:Y,rotateRightIcon:K,zoomOutIcon:Z,zoomInIcon:J},actions:{onActive:v,onFlipY:C,onFlipX:A,onRotateLeft:w,onRotateRight:N,onZoomOut:x,onZoomIn:E,onReset:S,onClose:y},transform:m},M?{current:u,total:p}:{}),{},{image:z})):Q)))})},Je=n(3210),Qe=n(5371),et={x:0,y:0,rotate:0,scale:1,flipX:!1,flipY:!1};var tt=n(8210);function nt(e,t,n,a){var r=t+n,o=(n-a)/2;if(n>a){if(t>0)return(0,ce.A)({},e,o);if(t<0&&r<a)return(0,ce.A)({},e,-o)}else if(t<0||r>a)return(0,ce.A)({},e,t<0?o:-o);return{}}function at(e,t,n,a){var r=qe(),o=r.width,i=r.height,l=null;return e<=o&&t<=i?l={x:0,y:0}:(e>o||t>i)&&(l=(0,de.A)((0,de.A)({},nt("x",n,e,o)),nt("y",a,t,i))),l}function rt(e){var t=e.src,n=e.isCustomPlaceholder,a=e.fallback,o=(0,r.useState)(n?"loading":"normal"),i=(0,le.A)(o,2),l=i[0],s=i[1],c=(0,r.useRef)(!1),d="error"===l;(0,r.useEffect)(function(){var e=!0;return function(e){return new Promise(function(t){if(e){var n=document.createElement("img");n.onerror=function(){return t(!1)},n.onload=function(){return t(!0)},n.src=e}else t(!1)})}(t).then(function(t){!t&&e&&s("error")}),function(){e=!1}},[t]),(0,r.useEffect)(function(){n&&!c.current?s("loading"):d&&s("normal")},[t]);var u=function(){s("normal")};return[function(e){c.current=!1,"loading"===l&&null!=e&&e.complete&&(e.naturalWidth||e.naturalHeight)&&(c.current=!0,u())},d&&a?{src:a}:{onLoad:u,src:t},l]}function ot(e,t){var n=e.x-t.x,a=e.y-t.y;return Math.hypot(n,a)}function it(e,t,n,a,o,i,l){var s=o.rotate,c=o.scale,d=o.x,u=o.y,m=(0,r.useState)(!1),p=(0,le.A)(m,2),g=p[0],f=p[1],h=(0,r.useRef)({point1:{x:0,y:0},point2:{x:0,y:0},eventType:"none"}),b=function(e){h.current=(0,de.A)((0,de.A)({},h.current),e)};return(0,r.useEffect)(function(){var e;return n&&t&&(e=Ue(window,"touchmove",function(e){return e.preventDefault()},{passive:!1})),function(){var t;null===(t=e)||void 0===t||t.remove()}},[n,t]),{isTouching:g,onTouchStart:function(e){if(t){e.stopPropagation(),f(!0);var n=e.touches,a=void 0===n?[]:n;a.length>1?b({point1:{x:a[0].clientX,y:a[0].clientY},point2:{x:a[1].clientX,y:a[1].clientY},eventType:"touchZoom"}):b({point1:{x:a[0].clientX-d,y:a[0].clientY-u},eventType:"move"})}},onTouchMove:function(e){var t=e.touches,n=void 0===t?[]:t,a=h.current,r=a.point1,o=a.point2,s=a.eventType;if(n.length>1&&"touchZoom"===s){var c={x:n[0].clientX,y:n[0].clientY},d={x:n[1].clientX,y:n[1].clientY},u=function(e,t,n,a){var r=ot(e,n),o=ot(t,a);if(0===r&&0===o)return[e.x,e.y];var i=r/(r+o);return[e.x+i*(t.x-e.x),e.y+i*(t.y-e.y)]}(r,o,c,d),m=(0,le.A)(u,2),p=m[0],g=m[1],f=ot(c,d)/ot(r,o);l(f,"touchZoom",p,g,!0),b({point1:c,point2:d,eventType:"touchZoom"})}else"move"===s&&(i({x:n[0].clientX-r.x,y:n[0].clientY-r.y},"move"),b({eventType:"move"}))},onTouchEnd:function(){if(n){if(g&&f(!1),b({eventType:"none"}),a>c)return i({x:0,y:0,scale:a},"touchZoom");var t=e.current.offsetWidth*c,r=e.current.offsetHeight*c,o=e.current.getBoundingClientRect(),l=o.left,d=o.top,u=s%180!=0,m=at(u?r:t,u?t:r,l,d);m&&i((0,de.A)({},m),"dragRebound")}}}}var lt=["fallback","src","imgRef"],st=["prefixCls","src","alt","imageInfo","fallback","movable","onClose","visible","icons","rootClassName","closeIcon","getContainer","current","count","countRender","scaleStep","minScale","maxScale","transitionName","maskTransitionName","imageRender","imgCommonProps","toolbarRender","onTransform","onChange"],ct=function(e){var t=e.fallback,n=e.src,a=e.imgRef,o=(0,se.A)(e,lt),i=rt({src:n,fallback:t}),l=(0,le.A)(i,2),s=l[0],c=l[1];return r.createElement("img",(0,ie.A)({ref:function(e){a.current=e,s(e)}},o,c))},dt=function(e){var t=e.prefixCls,n=e.src,a=e.alt,o=e.imageInfo,i=e.fallback,l=e.movable,s=void 0===l||l,c=e.onClose,d=e.visible,u=e.icons,m=void 0===u?{}:u,p=e.rootClassName,g=e.closeIcon,f=e.getContainer,h=e.current,b=void 0===h?0:h,v=e.count,y=void 0===v?1:v,E=e.countRender,x=e.scaleStep,N=void 0===x?.5:x,w=e.minScale,A=void 0===w?1:w,C=e.maxScale,S=void 0===C?50:C,k=e.transitionName,$=void 0===k?"zoom":k,I=e.maskTransitionName,z=void 0===I?"fade":I,M=e.imageRender,T=e.imgCommonProps,R=e.toolbarRender,P=e.onTransform,j=e.onChange,_=(0,se.A)(e,st),B=(0,r.useRef)(),H=(0,r.useContext)(Ke),D=H&&y>1,L=H&&y>=1,F=(0,r.useState)(!0),W=(0,le.A)(F,2),q=W[0],X=W[1],G=function(e,t,n,a){var o=(0,r.useRef)(null),i=(0,r.useRef)([]),l=(0,r.useState)(et),s=(0,le.A)(l,2),c=s[0],d=s[1],u=function(e,t){null===o.current&&(i.current=[],o.current=(0,Qe.A)(function(){d(function(e){var n=e;return i.current.forEach(function(e){n=(0,de.A)((0,de.A)({},n),e)}),o.current=null,null==a||a({transform:n,action:t}),n})})),i.current.push((0,de.A)((0,de.A)({},c),e))};return{transform:c,resetTransform:function(e){d(et),(0,Je.A)(et,c)||null==a||a({transform:et,action:e})},updateTransform:u,dispatchZoomChange:function(a,r,o,i,l){var s=e.current,d=s.width,m=s.height,p=s.offsetWidth,g=s.offsetHeight,f=s.offsetLeft,h=s.offsetTop,b=a,v=c.scale*a;v>n?(v=n,b=n/c.scale):v<t&&(b=(v=l?v:t)/c.scale);var y=null!=o?o:innerWidth/2,E=null!=i?i:innerHeight/2,x=b-1,N=x*d*.5,w=x*m*.5,A=x*(y-c.x-f),C=x*(E-c.y-h),S=c.x-(A-N),k=c.y-(C-w);if(a<1&&1===v){var $=p*v,O=g*v,I=qe(),z=I.width,M=I.height;$<=z&&O<=M&&(S=0,k=0)}u({x:S,y:k,scale:v},r)}}}(B,A,S,P),U=G.transform,V=G.resetTransform,Y=G.updateTransform,K=G.dispatchZoomChange,Z=function(e,t,n,a,o,i,l){var s=o.rotate,c=o.scale,d=o.x,u=o.y,m=(0,r.useState)(!1),p=(0,le.A)(m,2),g=p[0],f=p[1],h=(0,r.useRef)({diffX:0,diffY:0,transformX:0,transformY:0}),b=function(e){n&&g&&i({x:e.pageX-h.current.diffX,y:e.pageY-h.current.diffY},"move")},v=function(){if(n&&g){f(!1);var t=h.current,a=t.transformX,r=t.transformY;if(d===a||u===r)return;var o=e.current.offsetWidth*c,l=e.current.offsetHeight*c,m=e.current.getBoundingClientRect(),p=m.left,b=m.top,v=s%180!=0,y=at(v?l:o,v?o:l,p,b);y&&i((0,de.A)({},y),"dragRebound")}};return(0,r.useEffect)(function(){var e,n,a,r;if(t){a=Ue(window,"mouseup",v,!1),r=Ue(window,"mousemove",b,!1);try{window.top!==window.self&&(e=Ue(window.top,"mouseup",v,!1),n=Ue(window.top,"mousemove",b,!1))}catch(o){(0,tt.$e)(!1,"[rc-image] ".concat(o))}}return function(){var t,o,i,l;null===(t=a)||void 0===t||t.remove(),null===(o=r)||void 0===o||o.remove(),null===(i=e)||void 0===i||i.remove(),null===(l=n)||void 0===l||l.remove()}},[n,g,d,u,s,t]),{isMoving:g,onMouseDown:function(e){t&&0===e.button&&(e.preventDefault(),e.stopPropagation(),h.current={diffX:e.pageX-d,diffY:e.pageY-u,transformX:d,transformY:u},f(!0))},onMouseMove:b,onMouseUp:v,onWheel:function(e){if(n&&0!=e.deltaY){var t=Math.abs(e.deltaY/100),r=1+Math.min(t,1)*a;e.deltaY>0&&(r=1/r),l(r,"wheel",e.clientX,e.clientY)}}}}(B,s,d,N,U,Y,K),J=Z.isMoving,Q=Z.onMouseDown,ee=Z.onWheel,te=it(B,s,d,A,U,Y,K),ne=te.isTouching,ae=te.onTouchStart,re=te.onTouchMove,oe=te.onTouchEnd,ue=U.rotate,me=U.scale,pe=O()((0,ce.A)({},"".concat(t,"-moving"),J));(0,r.useEffect)(function(){q||X(!0)},[q]);var ge=function(e){var t=b+e;!Number.isInteger(t)||t<0||t>y-1||(X(!1),V(e<0?"prev":"next"),null==j||j(t,b))},fe=function(e){d&&D&&(e.keyCode===Ve.A.LEFT?ge(-1):e.keyCode===Ve.A.RIGHT&&ge(1))};(0,r.useEffect)(function(){var e=Ue(window,"keydown",fe,!1);return function(){e.remove()}},[d,D,b]);var he=r.createElement(ct,(0,ie.A)({},T,{width:e.width,height:e.height,imgRef:B,className:"".concat(t,"-img"),alt:a,style:{transform:"translate3d(".concat(U.x,"px, ").concat(U.y,"px, 0) scale3d(").concat(U.flipX?"-":"").concat(me,", ").concat(U.flipY?"-":"").concat(me,", 1) rotate(").concat(ue,"deg)"),transitionDuration:(!q||ne)&&"0s"},fallback:i,src:n,onWheel:ee,onMouseDown:Q,onDoubleClick:function(e){d&&(1!==me?Y({x:0,y:0,scale:1},"doubleClick"):K(1+N,"doubleClick",e.clientX,e.clientY))},onTouchStart:ae,onTouchMove:re,onTouchEnd:oe,onTouchCancel:oe})),be=(0,de.A)({url:n,alt:a},o);return r.createElement(r.Fragment,null,r.createElement(Xe.A,(0,ie.A)({transitionName:$,maskTransitionName:z,closable:!1,keyboard:!0,prefixCls:t,onClose:c,visible:d,classNames:{wrapper:pe},rootClassName:p,getContainer:f},_,{afterClose:function(){V("close")}}),r.createElement("div",{className:"".concat(t,"-img-wrapper")},M?M(he,(0,de.A)({transform:U,image:be},H?{current:b}:{})):he)),r.createElement(Ze,{visible:d,transform:U,maskTransitionName:z,closeIcon:g,getContainer:f,prefixCls:t,rootClassName:p,icons:m,countRender:E,showSwitch:D,showProgress:L,current:b,count:y,scale:me,minScale:A,maxScale:S,toolbarRender:R,onActive:ge,onZoomIn:function(){K(1+N,"zoomIn")},onZoomOut:function(){K(1/(1+N),"zoomOut")},onRotateRight:function(){Y({rotate:ue+90},"rotateRight")},onRotateLeft:function(){Y({rotate:ue-90},"rotateLeft")},onFlipX:function(){Y({flipX:!U.flipX},"flipX")},onFlipY:function(){Y({flipY:!U.flipY},"flipY")},onClose:c,onReset:function(){V("reset")},zIndex:void 0!==_.zIndex?_.zIndex+1:void 0,image:be}))},ut=["crossOrigin","decoding","draggable","loading","referrerPolicy","sizes","srcSet","useMap","alt"];var mt=["visible","onVisibleChange","getContainer","current","movable","minScale","maxScale","countRender","closeIcon","onChange","onTransform","toolbarRender","imageRender"],pt=["src"],gt=function(e){var t,n=e.previewPrefixCls,o=void 0===n?"rc-image-preview":n,i=e.children,l=e.icons,s=void 0===l?{}:l,c=e.items,d=e.preview,u=e.fallback,m="object"===(0,ue.A)(d)?d:{},p=m.visible,g=m.onVisibleChange,f=m.getContainer,h=m.current,b=m.movable,v=m.minScale,y=m.maxScale,E=m.countRender,x=m.closeIcon,N=m.onChange,w=m.onTransform,A=m.toolbarRender,C=m.imageRender,S=(0,se.A)(m,mt),k=function(e){var t=r.useState({}),n=(0,le.A)(t,2),o=n[0],i=n[1],l=r.useCallback(function(e,t){return i(function(n){return(0,de.A)((0,de.A)({},n),{},(0,ce.A)({},e,t))}),function(){i(function(t){var n=(0,de.A)({},t);return delete n[e],n})}},[]);return[r.useMemo(function(){return e?e.map(function(e){if("string"==typeof e)return{data:{src:e}};var t={};return Object.keys(e).forEach(function(n){["src"].concat((0,a.A)(ut)).includes(n)&&(t[n]=e[n])}),{data:t}}):Object.keys(o).reduce(function(e,t){var n=o[t],a=n.canPreview,r=n.data;return a&&e.push({data:r,id:t}),e},[])},[e,o]),l,!!e]}(c),$=(0,le.A)(k,3),O=$[0],I=$[1],z=$[2],M=(0,me.A)(0,{value:h}),T=(0,le.A)(M,2),R=T[0],P=T[1],j=(0,r.useState)(!1),_=(0,le.A)(j,2),B=_[0],H=_[1],D=(null===(t=O[R])||void 0===t?void 0:t.data)||{},L=D.src,F=(0,se.A)(D,pt),W=(0,me.A)(!!p,{value:p,onChange:function(e,t){null==g||g(e,t,R)}}),q=(0,le.A)(W,2),X=q[0],G=q[1],U=(0,r.useState)(null),V=(0,le.A)(U,2),Y=V[0],K=V[1],Z=r.useCallback(function(e,t,n,a){var r=z?O.findIndex(function(e){return e.data.src===t}):O.findIndex(function(t){return t.id===e});P(r<0?0:r),G(!0),K({x:n,y:a}),H(!0)},[O,z]);r.useEffect(function(){X?B||P(0):H(!1)},[X]);var J=r.useMemo(function(){return{register:I,onPreview:Z}},[I,Z]);return r.createElement(Ke.Provider,{value:J},i,r.createElement(dt,(0,ie.A)({"aria-hidden":!X,movable:b,visible:X,prefixCls:o,closeIcon:x,onClose:function(){G(!1),K(null)},mousePosition:Y,imgCommonProps:F,src:L,fallback:u,icons:s,minScale:v,maxScale:y,getContainer:f,current:R,count:O.length,countRender:E,onTransform:w,toolbarRender:A,imageRender:C,onChange:function(e,t){P(e),null==N||N(e,t)}},S)))},ft=0;var ht=["src","alt","onPreviewClose","prefixCls","previewPrefixCls","placeholder","fallback","width","height","style","preview","className","onClick","onError","wrapperClassName","wrapperStyle","rootClassName"],bt=["src","visible","onVisibleChange","getContainer","mask","maskClassName","movable","icons","scaleStep","minScale","maxScale","imageRender","toolbarRender"],vt=function(e){var t=e.src,n=e.alt,a=e.onPreviewClose,o=e.prefixCls,i=void 0===o?"rc-image":o,l=e.previewPrefixCls,s=void 0===l?"".concat(i,"-preview"):l,c=e.placeholder,d=e.fallback,u=e.width,m=e.height,p=e.style,g=e.preview,f=void 0===g||g,h=e.className,b=e.onClick,v=e.onError,y=e.wrapperClassName,E=e.wrapperStyle,x=e.rootClassName,N=(0,se.A)(e,ht),w=c&&!0!==c,A="object"===(0,ue.A)(f)?f:{},C=A.src,S=A.visible,k=void 0===S?void 0:S,$=A.onVisibleChange,I=void 0===$?a:$,z=A.getContainer,M=void 0===z?void 0:z,T=A.mask,R=A.maskClassName,P=A.movable,j=A.icons,_=A.scaleStep,B=A.minScale,H=A.maxScale,D=A.imageRender,L=A.toolbarRender,F=(0,se.A)(A,bt),W=null!=C?C:t,q=(0,me.A)(!!k,{value:k,onChange:I}),X=(0,le.A)(q,2),G=X[0],U=X[1],V=rt({src:t,isCustomPlaceholder:w,fallback:d}),Y=(0,le.A)(V,3),K=Y[0],Z=Y[1],J=Y[2],Q=(0,r.useState)(null),ee=(0,le.A)(Q,2),te=ee[0],ne=ee[1],ae=(0,r.useContext)(Ke),re=!!f,oe=O()(i,y,x,(0,ce.A)({},"".concat(i,"-error"),"error"===J)),pe=(0,r.useMemo)(function(){var t={};return ut.forEach(function(n){void 0!==e[n]&&(t[n]=e[n])}),t},ut.map(function(t){return e[t]})),ge=function(e,t){var n=r.useState(function(){return String(ft+=1)}),a=(0,le.A)(n,1)[0],o=r.useContext(Ke),i={data:t,canPreview:e};return r.useEffect(function(){if(o)return o.register(a,i)},[]),r.useEffect(function(){o&&o.register(a,i)},[e,t]),a}(re,(0,r.useMemo)(function(){return(0,de.A)((0,de.A)({},pe),{},{src:W})},[W,pe]));return r.createElement(r.Fragment,null,r.createElement("div",(0,ie.A)({},N,{className:oe,onClick:re?function(e){var t,n,a,r=(t=e.target,n=t.getBoundingClientRect(),a=document.documentElement,{left:n.left+(window.pageXOffset||a.scrollLeft)-(a.clientLeft||document.body.clientLeft||0),top:n.top+(window.pageYOffset||a.scrollTop)-(a.clientTop||document.body.clientTop||0)}),o=r.left,i=r.top;ae?ae.onPreview(ge,W,o,i):(ne({x:o,y:i}),U(!0)),null==b||b(e)}:b,style:(0,de.A)({width:u,height:m},E)}),r.createElement("img",(0,ie.A)({},pe,{className:O()("".concat(i,"-img"),(0,ce.A)({},"".concat(i,"-img-placeholder"),!0===c),h),style:(0,de.A)({height:m},p),ref:K},Z,{width:u,height:m,onError:v})),"loading"===J&&r.createElement("div",{"aria-hidden":"true",className:"".concat(i,"-placeholder")},c),T&&re&&r.createElement("div",{className:O()("".concat(i,"-mask"),R),style:{display:"none"===(null==p?void 0:p.display)?"none":void 0}},T)),!ae&&re&&r.createElement(dt,(0,ie.A)({"aria-hidden":!G,visible:G,prefixCls:s,onClose:function(){U(!1),ne(null)},mousePosition:te,src:W,alt:n,imageInfo:{width:u,height:m},fallback:d,getContainer:M,icons:j,movable:P,scaleStep:_,minScale:B,maxScale:H,rootClassName:x,imageRender:D,imgCommonProps:pe,toolbarRender:L},F)))};vt.PreviewGroup=gt;var yt=vt,Et=n(275),xt=n(3723),Nt=n(934),wt=n(9155),At=n(7852),Ct=n(329),St=n(8e3),kt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M672 418H144c-17.7 0-32 14.3-32 32v414c0 17.7 14.3 32 32 32h528c17.7 0 32-14.3 32-32V450c0-17.7-14.3-32-32-32zm-44 402H188V494h440v326z"}},{tag:"path",attrs:{d:"M819.3 328.5c-78.8-100.7-196-153.6-314.6-154.2l-.2-64c0-6.5-7.6-10.1-12.6-6.1l-128 101c-4 3.1-3.9 9.1 0 12.3L492 318.6c5.1 4 12.7.4 12.6-6.1v-63.9c12.9.1 25.9.9 38.8 2.5 42.1 5.2 82.1 18.2 119 38.7 38.1 21.2 71.2 49.7 98.4 84.3 27.1 34.7 46.7 73.7 58.1 115.8a325.95 325.95 0 016.5 140.9h74.9c14.8-103.6-11.3-213-81-302.3z"}}]},name:"rotate-left",theme:"outlined"},$t=n(7064),Ot=function(e,t){return r.createElement($t.A,(0,ie.A)({},e,{ref:t,icon:kt}))};var It=r.forwardRef(Ot),zt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M480.5 251.2c13-1.6 25.9-2.4 38.8-2.5v63.9c0 6.5 7.5 10.1 12.6 6.1L660 217.6c4-3.2 4-9.2 0-12.3l-128-101c-5.1-4-12.6-.4-12.6 6.1l-.2 64c-118.6.5-235.8 53.4-314.6 154.2A399.75 399.75 0 00123.5 631h74.9c-.9-5.3-1.7-10.7-2.4-16.1-5.1-42.1-2.1-84.1 8.9-124.8 11.4-42.2 31-81.1 58.1-115.8 27.2-34.7 60.3-63.2 98.4-84.3 37-20.6 76.9-33.6 119.1-38.8z"}},{tag:"path",attrs:{d:"M880 418H352c-17.7 0-32 14.3-32 32v414c0 17.7 14.3 32 32 32h528c17.7 0 32-14.3 32-32V450c0-17.7-14.3-32-32-32zm-44 402H396V494h440v326z"}}]},name:"rotate-right",theme:"outlined"},Mt=function(e,t){return r.createElement($t.A,(0,ie.A)({},e,{ref:t,icon:zt}))};var Tt=r.forwardRef(Mt),Rt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M847.9 592H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h605.2L612.9 851c-4.1 5.2-.4 13 6.3 13h72.5c4.9 0 9.5-2.2 12.6-6.1l168.8-214.1c16.5-21 1.6-51.8-25.2-51.8zM872 356H266.8l144.3-183c4.1-5.2.4-13-6.3-13h-72.5c-4.9 0-9.5 2.2-12.6 6.1L150.9 380.2c-16.5 21-1.6 51.8 25.1 51.8h696c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"swap",theme:"outlined"},Pt=function(e,t){return r.createElement($t.A,(0,ie.A)({},e,{ref:t,icon:Rt}))};var jt=r.forwardRef(Pt),_t={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M637 443H519V309c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v134H325c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h118v134c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V519h118c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8zm284 424L775 721c122.1-148.9 113.6-369.5-26-509-148-148.1-388.4-148.1-537 0-148.1 148.6-148.1 389 0 537 139.5 139.6 360.1 148.1 509 26l146 146c3.2 2.8 8.3 2.8 11 0l43-43c2.8-2.7 2.8-7.8 0-11zM696 696c-118.8 118.7-311.2 118.7-430 0-118.7-118.8-118.7-311.2 0-430 118.8-118.7 311.2-118.7 430 0 118.7 118.8 118.7 311.2 0 430z"}}]},name:"zoom-in",theme:"outlined"},Bt=function(e,t){return r.createElement($t.A,(0,ie.A)({},e,{ref:t,icon:_t}))};var Ht=r.forwardRef(Bt),Dt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M637 443H325c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h312c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8zm284 424L775 721c122.1-148.9 113.6-369.5-26-509-148-148.1-388.4-148.1-537 0-148.1 148.6-148.1 389 0 537 139.5 139.6 360.1 148.1 509 26l146 146c3.2 2.8 8.3 2.8 11 0l43-43c2.8-2.7 2.8-7.8 0-11zM696 696c-118.8 118.7-311.2 118.7-430 0-118.7-118.8-118.7-311.2 0-430 118.8-118.7 311.2-118.7 430 0 118.7 118.8 118.7 311.2 0 430z"}}]},name:"zoom-out",theme:"outlined"},Lt=function(e,t){return r.createElement($t.A,(0,ie.A)({},e,{ref:t,icon:Dt}))};var Ft=r.forwardRef(Lt),Wt=n(2616),qt=n(8071),Xt=n(9077),Gt=n(8680);const Ut=e=>({position:e||"absolute",inset:0}),Vt=e=>{const{iconCls:t,motionDurationSlow:n,paddingXXS:a,marginXXS:r,prefixCls:o,colorTextLightSolid:i}=e;return{position:"absolute",inset:0,display:"flex",alignItems:"center",justifyContent:"center",color:i,background:new Wt.Y("#000").setA(.5).toRgbString(),cursor:"pointer",opacity:0,transition:`opacity ${n}`,[`.${o}-mask-info`]:Object.assign(Object.assign({},P.L9),{padding:`0 ${(0,R.zA)(a)}`,[t]:{marginInlineEnd:r,svg:{verticalAlign:"baseline"}}})}},Yt=e=>{const{previewCls:t,modalMaskBg:n,paddingSM:a,marginXL:r,margin:o,paddingLG:i,previewOperationColorDisabled:l,previewOperationHoverColor:s,motionDurationSlow:c,iconCls:d,colorTextLightSolid:u}=e,m=new Wt.Y(n).setA(.1),p=m.clone().setA(.2);return{[`${t}-footer`]:{position:"fixed",bottom:r,left:{_skip_check_:!0,value:"50%"},display:"flex",flexDirection:"column",alignItems:"center",color:e.previewOperationColor,transform:"translateX(-50%)"},[`${t}-progress`]:{marginBottom:o},[`${t}-close`]:{position:"fixed",top:r,right:{_skip_check_:!0,value:r},display:"flex",color:u,backgroundColor:m.toRgbString(),borderRadius:"50%",padding:a,outline:0,border:0,cursor:"pointer",transition:`all ${c}`,"&:hover":{backgroundColor:p.toRgbString()},[`& > ${d}`]:{fontSize:e.previewOperationSize}},[`${t}-operations`]:{display:"flex",alignItems:"center",padding:`0 ${(0,R.zA)(i)}`,backgroundColor:m.toRgbString(),borderRadius:100,"&-operation":{marginInlineStart:a,padding:a,cursor:"pointer",transition:`all ${c}`,userSelect:"none",[`&:not(${t}-operations-operation-disabled):hover > ${d}`]:{color:s},"&-disabled":{color:l,cursor:"not-allowed"},"&:first-of-type":{marginInlineStart:0},[`& > ${d}`]:{fontSize:e.previewOperationSize}}}}},Kt=e=>{const{modalMaskBg:t,iconCls:n,previewOperationColorDisabled:a,previewCls:r,zIndexPopup:o,motionDurationSlow:i}=e,l=new Wt.Y(t).setA(.1),s=l.clone().setA(.2);return{[`${r}-switch-left, ${r}-switch-right`]:{position:"fixed",insetBlockStart:"50%",zIndex:e.calc(o).add(1).equal(),display:"flex",alignItems:"center",justifyContent:"center",width:e.imagePreviewSwitchSize,height:e.imagePreviewSwitchSize,marginTop:e.calc(e.imagePreviewSwitchSize).mul(-1).div(2).equal(),color:e.previewOperationColor,background:l.toRgbString(),borderRadius:"50%",transform:"translateY(-50%)",cursor:"pointer",transition:`all ${i}`,userSelect:"none","&:hover":{background:s.toRgbString()},"&-disabled":{"&, &:hover":{color:a,background:"transparent",cursor:"not-allowed",[`> ${n}`]:{cursor:"not-allowed"}}},[`> ${n}`]:{fontSize:e.previewOperationSize}},[`${r}-switch-left`]:{insetInlineStart:e.marginSM},[`${r}-switch-right`]:{insetInlineEnd:e.marginSM}}},Zt=e=>{const{motionEaseOut:t,previewCls:n,motionDurationSlow:a,componentCls:r}=e;return[{[`${r}-preview-root`]:{[n]:{height:"100%",textAlign:"center",pointerEvents:"none"},[`${n}-body`]:Object.assign(Object.assign({},Ut()),{overflow:"hidden"}),[`${n}-img`]:{maxWidth:"100%",maxHeight:"70%",verticalAlign:"middle",transform:"scale3d(1, 1, 1)",cursor:"grab",transition:`transform ${a} ${t} 0s`,userSelect:"none","&-wrapper":Object.assign(Object.assign({},Ut()),{transition:`transform ${a} ${t} 0s`,display:"flex",justifyContent:"center",alignItems:"center","& > *":{pointerEvents:"auto"},"&::before":{display:"inline-block",width:1,height:"50%",marginInlineEnd:-1,content:'""'}})},[`${n}-moving`]:{[`${n}-preview-img`]:{cursor:"grabbing","&-wrapper":{transitionDuration:"0s"}}}}},{[`${r}-preview-root`]:{[`${n}-wrap`]:{zIndex:e.zIndexPopup}}},{[`${r}-preview-operations-wrapper`]:{position:"fixed",zIndex:e.calc(e.zIndexPopup).add(1).equal()},"&":[Yt(e),Kt(e)]}]},Jt=e=>{const{componentCls:t}=e;return{[t]:{position:"relative",display:"inline-block",[`${t}-img`]:{width:"100%",height:"auto",verticalAlign:"middle"},[`${t}-img-placeholder`]:{backgroundColor:e.colorBgContainerDisabled,backgroundImage:"url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMTQuNSAyLjVoLTEzQS41LjUgMCAwIDAgMSAzdjEwYS41LjUgMCAwIDAgLjUuNWgxM2EuNS41IDAgMCAwIC41LS41VjNhLjUuNSAwIDAgMC0uNS0uNXpNNS4yODEgNC43NWExIDEgMCAwIDEgMCAyIDEgMSAwIDAgMSAwLTJ6bTguMDMgNi44M2EuMTI3LjEyNyAwIDAgMS0uMDgxLjAzSDIuNzY5YS4xMjUuMTI1IDAgMCAxLS4wOTYtLjIwN2wyLjY2MS0zLjE1NmEuMTI2LjEyNiAwIDAgMSAuMTc3LS4wMTZsLjAxNi4wMTZMNy4wOCAxMC4wOWwyLjQ3LTIuOTNhLjEyNi4xMjYgMCAwIDEgLjE3Ny0uMDE2bC4wMTUuMDE2IDMuNTg4IDQuMjQ0YS4xMjcuMTI3IDAgMCAxLS4wMi4xNzV6IiBmaWxsPSIjOEM4QzhDIiBmaWxsLXJ1bGU9Im5vbnplcm8iLz48L3N2Zz4=')",backgroundRepeat:"no-repeat",backgroundPosition:"center center",backgroundSize:"30%"},[`${t}-mask`]:Object.assign({},Vt(e)),[`${t}-mask:hover`]:{opacity:1},[`${t}-placeholder`]:Object.assign({},Ut())}}},Qt=e=>{const{previewCls:t}=e;return{[`${t}-root`]:(0,Xt.aB)(e,"zoom"),"&":(0,Gt.p9)(e,!0)}};var en=(0,B.OF)("Image",e=>{const t=`${e.componentCls}-preview`,n=(0,_.oX)(e,{previewCls:t,modalMaskBg:new Wt.Y("#000").setA(.45).toRgbString(),imagePreviewSwitchSize:e.controlHeightLG});return[Jt(n),Zt(n),(0,qt.Dk)((0,_.oX)(n,{componentCls:t})),Qt(n)]},e=>({zIndexPopup:e.zIndexPopupBase+80,previewOperationColor:new Wt.Y(e.colorTextLightSolid).setA(.65).toRgbString(),previewOperationHoverColor:new Wt.Y(e.colorTextLightSolid).setA(.85).toRgbString(),previewOperationColorDisabled:new Wt.Y(e.colorTextLightSolid).setA(.25).toRgbString(),previewOperationSize:1.5*e.fontSizeIcon})),tn=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]])}return n};const nn={rotateLeft:r.createElement(It,null),rotateRight:r.createElement(Tt,null),zoomIn:r.createElement(Ht,null),zoomOut:r.createElement(Ft,null),close:r.createElement(At.A,null),left:r.createElement(Ct.A,null),right:r.createElement(St.A,null),flipX:r.createElement(jt,null),flipY:r.createElement(jt,{rotate:90})};var an=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]])}return n};const rn=e=>{const{prefixCls:t,preview:n,className:a,rootClassName:o,style:i}=e,l=an(e,["prefixCls","preview","className","rootClassName","style"]);const{getPrefixCls:s,getPopupContainer:c,className:d,style:u,preview:m}=(0,T.TP)("image"),[p]=(0,wt.A)("Image"),g=s("image",t),f=s(),h=(0,Nt.A)(g),[b,v,y]=en(g,h),E=O()(o,v,y,h),x=O()(a,v,d),[N]=(0,Et.YK)("ImagePreview","object"==typeof n?n.zIndex:void 0),w=r.useMemo(()=>{if(!1===n)return n;const e="object"==typeof n?n:{},{getContainer:t,closeIcon:a,rootClassName:o,destroyOnClose:i,destroyOnHidden:l}=e,s=an(e,["getContainer","closeIcon","rootClassName","destroyOnClose","destroyOnHidden"]);return Object.assign(Object.assign({mask:r.createElement("div",{className:`${g}-mask-info`},r.createElement(We.A,null),null==p?void 0:p.preview),icons:nn},s),{destroyOnClose:null!=l?l:i,rootClassName:O()(E,o),getContainer:null!=t?t:c,transitionName:(0,xt.b)(f,"zoom",e.transitionName),maskTransitionName:(0,xt.b)(f,"fade",e.maskTransitionName),zIndex:N,closeIcon:null!=a?a:null==m?void 0:m.closeIcon})},[n,p,null==m?void 0:m.closeIcon]),A=Object.assign(Object.assign({},u),i);return b(r.createElement(yt,Object.assign({prefixCls:g,preview:w,rootClassName:E,className:x,style:A},l)))};rn.PreviewGroup=e=>{var{previewPrefixCls:t,preview:n}=e,a=tn(e,["previewPrefixCls","preview"]);const{getPrefixCls:o,direction:i}=r.useContext(T.QO),l=o("image",t),s=`${l}-preview`,c=o(),d=(0,Nt.A)(l),[u,m,p]=en(l,d),[g]=(0,Et.YK)("ImagePreview","object"==typeof n?n.zIndex:void 0),f=r.useMemo(()=>Object.assign(Object.assign({},nn),{left:"rtl"===i?r.createElement(St.A,null):r.createElement(Ct.A,null),right:"rtl"===i?r.createElement(Ct.A,null):r.createElement(St.A,null)}),[i]),h=r.useMemo(()=>{var e;if(!1===n)return n;const t="object"==typeof n?n:{},a=O()(m,p,d,null!==(e=t.rootClassName)&&void 0!==e?e:"");return Object.assign(Object.assign({},t),{transitionName:(0,xt.b)(c,"zoom",t.transitionName),maskTransitionName:(0,xt.b)(c,"fade",t.maskTransitionName),rootClassName:a,zIndex:g})},[n]);return u(r.createElement(yt.PreviewGroup,Object.assign({preview:h,previewPrefixCls:s,icons:f},a)))};var on=rn,ln=n(8603),sn=n(3618);const cn=e=>{const{componentCls:t}=e;return{[t]:{"&-horizontal":{[`&${t}`]:{"&-sm":{marginBlock:e.marginXS},"&-md":{marginBlock:e.margin}}}}}},dn=e=>{const{componentCls:t,sizePaddingEdgeHorizontal:n,colorSplit:a,lineWidth:r,textPaddingInline:o,orientationMargin:i,verticalMarginInline:l}=e;return{[t]:Object.assign(Object.assign({},(0,P.dF)(e)),{borderBlockStart:`${(0,R.zA)(r)} solid ${a}`,"&-vertical":{position:"relative",top:"-0.06em",display:"inline-block",height:"0.9em",marginInline:l,marginBlock:0,verticalAlign:"middle",borderTop:0,borderInlineStart:`${(0,R.zA)(r)} solid ${a}`},"&-horizontal":{display:"flex",clear:"both",width:"100%",minWidth:"100%",margin:`${(0,R.zA)(e.marginLG)} 0`},[`&-horizontal${t}-with-text`]:{display:"flex",alignItems:"center",margin:`${(0,R.zA)(e.dividerHorizontalWithTextGutterMargin)} 0`,color:e.colorTextHeading,fontWeight:500,fontSize:e.fontSizeLG,whiteSpace:"nowrap",textAlign:"center",borderBlockStart:`0 ${a}`,"&::before, &::after":{position:"relative",width:"50%",borderBlockStart:`${(0,R.zA)(r)} solid transparent`,borderBlockStartColor:"inherit",borderBlockEnd:0,transform:"translateY(50%)",content:"''"}},[`&-horizontal${t}-with-text-start`]:{"&::before":{width:`calc(${i} * 100%)`},"&::after":{width:`calc(100% - ${i} * 100%)`}},[`&-horizontal${t}-with-text-end`]:{"&::before":{width:`calc(100% - ${i} * 100%)`},"&::after":{width:`calc(${i} * 100%)`}},[`${t}-inner-text`]:{display:"inline-block",paddingBlock:0,paddingInline:o},"&-dashed":{background:"none",borderColor:a,borderStyle:"dashed",borderWidth:`${(0,R.zA)(r)} 0 0`},[`&-horizontal${t}-with-text${t}-dashed`]:{"&::before, &::after":{borderStyle:"dashed none none"}},[`&-vertical${t}-dashed`]:{borderInlineStartWidth:r,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},"&-dotted":{background:"none",borderColor:a,borderStyle:"dotted",borderWidth:`${(0,R.zA)(r)} 0 0`},[`&-horizontal${t}-with-text${t}-dotted`]:{"&::before, &::after":{borderStyle:"dotted none none"}},[`&-vertical${t}-dotted`]:{borderInlineStartWidth:r,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},[`&-plain${t}-with-text`]:{color:e.colorText,fontWeight:"normal",fontSize:e.fontSize},[`&-horizontal${t}-with-text-start${t}-no-default-orientation-margin-start`]:{"&::before":{width:0},"&::after":{width:"100%"},[`${t}-inner-text`]:{paddingInlineStart:n}},[`&-horizontal${t}-with-text-end${t}-no-default-orientation-margin-end`]:{"&::before":{width:"100%"},"&::after":{width:0},[`${t}-inner-text`]:{paddingInlineEnd:n}}})}};var un=(0,B.OF)("Divider",e=>{const t=(0,_.oX)(e,{dividerHorizontalWithTextGutterMargin:e.margin,sizePaddingEdgeHorizontal:0});return[dn(t),cn(t)]},e=>({textPaddingInline:"1em",orientationMargin:.05,verticalMarginInline:e.marginXS}),{unitless:{orientationMargin:!0}}),mn=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]])}return n};const pn={small:"sm",middle:"md"};var gn=e=>{const{getPrefixCls:t,direction:n,className:a,style:o}=(0,T.TP)("divider"),{prefixCls:i,type:l="horizontal",orientation:s="center",orientationMargin:c,className:d,rootClassName:u,children:m,dashed:p,variant:g="solid",plain:f,style:h,size:b}=e,v=mn(e,["prefixCls","type","orientation","orientationMargin","className","rootClassName","children","dashed","variant","plain","style","size"]),y=t("divider",i),[E,x,N]=un(y),w=(0,Ae.A)(b),A=pn[w],C=!!m,S=r.useMemo(()=>"left"===s?"rtl"===n?"end":"start":"right"===s?"rtl"===n?"start":"end":s,[n,s]),k="start"===S&&null!=c,$="end"===S&&null!=c,I=O()(y,a,x,N,`${y}-${l}`,{[`${y}-with-text`]:C,[`${y}-with-text-${S}`]:C,[`${y}-dashed`]:!!p,[`${y}-${g}`]:"solid"!==g,[`${y}-plain`]:!!f,[`${y}-rtl`]:"rtl"===n,[`${y}-no-default-orientation-margin-start`]:k,[`${y}-no-default-orientation-margin-end`]:$,[`${y}-${A}`]:!!A},d,u),z=r.useMemo(()=>"number"==typeof c?c:/^\d+$/.test(c)?Number(c):c,[c]),M={marginInlineStart:k?z:void 0,marginInlineEnd:$?z:void 0};return E(r.createElement("div",Object.assign({className:I,style:Object.assign(Object.assign({},o),h)},v,{role:"separator"}),m&&"vertical"!==l&&r.createElement("span",{className:`${y}-inner-text`,style:M},m)))},fn=n(9612),hn=n(3164);const bn=(0,Te.A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var vn=n(418);const yn=(0,Te.A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);var En=n(73),xn=n(5107),Nn=n(6808);const{TextArea:wn}=o.A,{Text:An}=N.A,{Option:Cn}=i.A,Sn=e=>{let{serverParams:t,wsClient:n,connected:c,capabilities:m}=e;const{0:p,1:g}=(0,r.useState)([]),{0:f,1:b}=(0,r.useState)(null),{0:y,1:E}=(0,r.useState)({}),{0:x,1:N}=(0,r.useState)(!1),{0:S,1:k}=(0,r.useState)(!1),{0:$,1:O}=(0,r.useState)(null),{0:I,1:z}=(0,r.useState)(null),{0:M,1:T}=(0,r.useState)(null),{0:R,1:P}=(0,r.useState)(""),{0:j,1:_}=(0,r.useState)("parsed"),B=(0,r.useRef)(null),H=(0,r.useRef)(null);(0,r.useEffect)(()=>{if(p.length>0&&!f){const e=p[0];b(e),O(null);const t={},n=e.inputSchema,a=(null==n?void 0:n.properties)||{};Object.entries(a).forEach(e=>{let[n,a]=e;void 0!==a.default&&(t[n]=a.default)}),E(t)}},[p,f]),(0,r.useEffect)(()=>{f&&B.current&&setTimeout(()=>{var e;null===(e=B.current)||void 0===e||e.scrollIntoView({behavior:"smooth",block:"nearest"})},100)},[f]),(0,r.useEffect)(()=>{$&&H.current&&setTimeout(()=>{var e;null===(e=H.current)||void 0===e||e.scrollIntoView({behavior:"smooth",block:"nearest"})},100)},[$]);const D=(0,r.useCallback)(async function(e){if(void 0===e&&(e=!1),c)if(n){N(!0),T(null),e&&(b(null),O(null),E({}));try{const e=await n.executeOperation({operation:"list_tools"});null!=e&&e.tools?g(e.tools):T("No tools received from server")}catch(t){T(`Failed to fetch tools: ${t.message}`)}finally{N(!1)}}else T("WebSocket client not initialized");else T("WebSocket not connected")},[c]),L=(0,r.useCallback)(()=>{D(!0)},[D]),F=(0,r.useRef)(!1);(0,r.useEffect)(()=>{console.log("Tools useEffect triggered - connected:",c,"capabilities?.tools:",!(null==m||!m.tools),"wsClient:",!!n,"hasLoaded:",F.current),c&&null!=m&&m.tools&&n&&!F.current?(console.log("Loading tools for the first time"),F.current=!0,D()):c&&null!=m&&m.tools||(console.log("Resetting hasLoadedToolsRef due to disconnection or no tools capability"),F.current=!1)},[c,null==m?void 0:m.tools,D]);const W=(0,r.useCallback)(async()=>{if(f&&c&&n){n.clearActivityMessages&&n.clearActivityMessages(),k(!0),z(null),O(null);try{const e=await n.executeOperation({operation:"call_tool",tool_name:f.name,arguments:y});null!=e&&e.result?O(e.result):z("No result received from tool execution")}catch(e){z(`Failed to execute tool: ${e.message}`)}finally{k(!1)}}},[c,f,y,n]),q=p.filter(e=>{var t;if(!R)return!0;const n=R.toLowerCase(),a=((null===(t=e.annotations)||void 0===t?void 0:t.title)||e.name).toLowerCase(),r=(e.description||"").toLowerCase();return a.includes(n)||r.includes(n)}),X=e=>{var t;return(null===(t=e.annotations)||void 0===t?void 0:t.title)||e.name},G=e=>{var t;return Object.keys((null===(t=e.inputSchema)||void 0===t?void 0:t.properties)||{}).length},U=e=>{const t=e.key,n=p.find(e=>e.name===t);n&&V(n)},V=e=>{b(e),O(null);const t={},n=e.inputSchema,a=(null==n?void 0:n.properties)||{};Object.entries(a).forEach(e=>{let[n,a]=e;void 0!==a.default&&(t[n]=a.default)}),E(t)},Y=(e,t)=>{const{type:n,text:a,data:o,mimeType:i}=e;return r.createElement(s.A,{key:t,size:"small",className:"bg-secondary",style:{border:"1px solid var(--border-color, #e8e8e8)"}},(()=>{if(null!=i&&i.startsWith("image/")&&o&&(e=>{try{return btoa(atob(e))===e}catch(t){return!1}})(o))return r.createElement("div",null,r.createElement("div",{className:"flex items-center gap-2 mb-2"},r.createElement(fn.A,{size:16}),r.createElement(An,{strong:!0},"Image"),r.createElement(C.A,{color:"green"},i)),r.createElement(on,{src:`data:${i};base64,${o}`,alt:"MCP Result Image",style:{maxWidth:"300px",maxHeight:"300px"},placeholder:r.createElement("div",null,"Loading image...")}));if("application/json"===i||"text"===n&&a&&(a.trim().startsWith("{")||a.trim().startsWith("["))){const e=(e=>{try{const t=JSON.parse(e);return JSON.stringify(t,null,2)}catch{return e}})(a||o);return r.createElement("div",null,r.createElement("div",{className:"flex items-center gap-2 mb-2"},r.createElement(hn.A,{size:16}),r.createElement(An,{strong:!0},"JSON Data"),r.createElement(C.A,{color:"purple"},"application/json")),r.createElement(l.A,{size:"small",ghost:!0,defaultActiveKey:["json"]},r.createElement(l.A.Panel,{header:r.createElement("div",{className:"flex items-center gap-2"},r.createElement(An,{type:"secondary"},"View formatted JSON")),key:"json"},r.createElement("pre",{className:"bg-secondary text-primary",style:{padding:"12px",borderRadius:"6px",fontSize:"12px",lineHeight:"1.4",overflow:"auto",maxHeight:"300px",whiteSpace:"pre-wrap",wordBreak:"break-word",overflowWrap:"break-word",margin:0,border:"1px solid var(--border-color, #e1e4e8)"}},e))))}if("text"===n){const e=a||o||"";return r.createElement("div",null,r.createElement("div",{className:"flex items-center gap-2 mb-2"},r.createElement(v.A,{size:16}),r.createElement(An,{strong:!0},"Text Content"),i&&r.createElement(C.A,{color:"blue"},i)),e?r.createElement("pre",{className:"bg-secondary text-primary",style:{whiteSpace:"pre-wrap",wordBreak:"break-word",padding:"12px",borderRadius:"6px",fontSize:"13px",lineHeight:"1.5",maxHeight:"400px",overflow:"auto",margin:0}},e):r.createElement(An,{type:"secondary",className:"italic"},"Empty text content"))}if(o){const e=o.length>100?`${o.substring(0,100)}...`:o;return r.createElement("div",null,r.createElement("div",{className:"flex items-center gap-2 mb-2"},r.createElement(hn.A,{size:16}),r.createElement(An,{strong:!0},"Binary Data"),i&&r.createElement(C.A,{color:"orange"},i),r.createElement(C.A,{color:"default"},o.length," bytes")),r.createElement(l.A,{size:"small",ghost:!0},r.createElement(l.A.Panel,{header:r.createElement("div",{className:"flex items-center gap-2"},r.createElement(bn,{size:14}),r.createElement(An,{type:"secondary"},"View raw data (preview)")),key:"data"},r.createElement("pre",{className:"bg-secondary text-primary",style:{padding:"12px",borderRadius:"6px",fontSize:"11px",lineHeight:"1.4",overflow:"auto",maxHeight:"200px",border:"1px solid var(--border-color, #e1e4e8)",fontFamily:"monospace",wordBreak:"break-all"}},e))))}return r.createElement("div",null,r.createElement("div",{className:"flex items-center gap-2 mb-2"},r.createElement(vn.A,{size:16}),r.createElement(An,{strong:!0},"Unknown Content"),r.createElement(C.A,{color:"default"},n),i&&r.createElement(C.A,{color:"gray"},i)),r.createElement(An,{type:"secondary"},"No suitable renderer found for this content type."))})())};return r.createElement("div",{style:{width:"100%",maxWidth:"100%",overflow:"hidden"}},r.createElement(u.A,{direction:"vertical",style:{width:"100%"}},(()=>{var e,t,n,i;if(x)return r.createElement(s.A,{size:"small",title:"Available Tools"},r.createElement("div",{style:{textAlign:"center",padding:"24px"}},r.createElement(w.A,{size:"large"}),r.createElement("div",{style:{marginTop:"16px"}},r.createElement(An,null,"Loading tools..."))));if(0===p.length)return r.createElement(s.A,{size:"small",title:"Available Tools"},r.createElement(u.A,{direction:"vertical",style:{width:"100%"}},r.createElement(d.Ay,{type:"primary",onClick:L,icon:r.createElement(h.A,{size:16})},"Load Tools"),M&&r.createElement(A.A,{type:"error",message:"Failed to Load Tools",description:M,action:r.createElement(u.A,null,r.createElement(d.Ay,{size:"small",onClick:L,loading:x},"Retry"),r.createElement(d.Ay,{size:"small",onClick:()=>T(null)},"Clear")),showIcon:!0})));const l={items:[{type:"group",label:r.createElement("div",null,r.createElement("div",{className:"text-xs text-secondary mb-1"},"Select a tool"),r.createElement(o.A,{prefix:r.createElement(yn,{className:"w-4 h-4"}),placeholder:"Search tools...",value:R,onChange:e=>P(e.target.value),onClick:e=>e.stopPropagation()})),key:"search-tools"},{type:"divider"}].concat((0,a.A)(q.map(e=>{const t=X(e),n=G(e),a=e.description&&e.description.length>50?`${e.description.substring(0,50)}...`:e.description||"No description available";return{label:r.createElement("div",null,r.createElement("div",{className:"flex items-center justify-between"},r.createElement("span",{className:"font-medium"},t),r.createElement("div",{className:"flex items-center gap-1 text-xs text-secondary"},r.createElement(En.A,{size:12}),r.createElement("span",null,n))),r.createElement("div",{className:"text-xs text-secondary mt-1"},a)),key:e.name,icon:r.createElement(h.A,{className:"w-4 h-4"})}}))),onClick:U};return r.createElement(s.A,{size:"small",title:r.createElement(u.A,{style:{width:"100%",justifyContent:"space-between"}},r.createElement("span",null,"Available Tools (",p.length,")"),r.createElement(d.Ay,{type:"link",size:"small",onClick:L,loading:x,icon:r.createElement(h.A,{size:12})},"Refresh"))},r.createElement(u.A,{direction:"vertical",style:{width:"100%"},size:"middle"},r.createElement(ln.A.Button,{menu:l,type:"default",className:"w-full",placement:"bottomLeft",icon:r.createElement(xn.A,{className:"w-4 h-4"}),disabled:x},r.createElement("div",{className:"flex items-center gap-2"},r.createElement(h.A,{className:"w-4 h-4"}),r.createElement("span",null,f?X(f):"Select a tool"))),f&&r.createElement("div",{className:"p-3 bg-secondary rounded border"},r.createElement("div",{className:"flex items-center gap-2 mb-2"},r.createElement(h.A,{size:16}),r.createElement(An,{strong:!0},X(f)),r.createElement(C.A,{color:"blue"},G(f)," params")),r.createElement("div",{className:"flex flex-wrap gap-1 mb-2"},(null===(e=f.annotations)||void 0===e?void 0:e.readOnlyHint)&&r.createElement("div",{className:"inline-flex items-center px-2 py-0.5 rounded-full text-xs bg-green-50 text-green-700 border border-green-200"},"Read-only"),(null===(t=f.annotations)||void 0===t?void 0:t.destructiveHint)&&r.createElement("div",{className:"inline-flex items-center px-2 py-0.5 rounded-full text-xs bg-red-50 text-red-700 border border-red-200"},"Destructive"),(null===(n=f.annotations)||void 0===n?void 0:n.idempotentHint)&&r.createElement("div",{className:"inline-flex items-center px-2 py-0.5 rounded-full text-xs bg-purple-50 text-purple-700 border border-purple-200"},"Idempotent"),(null===(i=f.annotations)||void 0===i?void 0:i.openWorldHint)&&r.createElement("div",{className:"inline-flex items-center px-2 py-0.5 rounded-full text-xs bg-orange-50 text-orange-700 border border-orange-200"},"Open-world")),f.description&&r.createElement(An,{type:"secondary",className:"text-sm"},f.description))))})(),f&&!x&&!M&&r.createElement(r.Fragment,null,r.createElement(gn,null),(()=>{if(!f)return null;const e=f.inputSchema,t=(null==e?void 0:e.properties)||{},n=(null==e?void 0:e.required)||[];return r.createElement(s.A,{size:"small",title:`Run ${f.name} Tool`},r.createElement(u.A,{direction:"vertical",style:{width:"100%"}},f.description&&r.createElement(An,{type:"secondary"},f.description),r.createElement(sn.A,{layout:"vertical"},Object.entries(t).map(e=>{let[t,a]=e;return r.createElement(sn.A.Item,{key:t,label:r.createElement(u.A,null,r.createElement(An,null,t),n.includes(t)&&r.createElement(C.A,{color:"red"},"Required"))},"string"===a.type&&a.enum?r.createElement(i.A,{style:{width:"100%"},placeholder:`Select ${t}`,value:y[t],onChange:e=>E({...y,[t]:e})},a.enum.map(e=>r.createElement(Cn,{key:e,value:e},e))):"object"===a.type||"array"===a.type?r.createElement(wn,{placeholder:`Enter ${t} as JSON`,rows:3,value:y[t]?JSON.stringify(y[t],null,2):"",onChange:e=>{try{const n=JSON.parse(e.target.value);E({...y,[t]:n})}catch{E({...y,[t]:e.target.value})}}}):"boolean"===a.type?r.createElement(i.A,{style:{width:"100%"},placeholder:`Select ${t}`,value:y[t],onChange:e=>E({...y,[t]:e})},r.createElement(Cn,{value:!0},"True"),r.createElement(Cn,{value:!1},"False")):r.createElement(o.A,{type:"number"===a.type||"integer"===a.type?"number":"text",placeholder:a.description||`Enter ${t}`,value:y[t]||"",onChange:e=>{let n=e.target.value;if("integer"===a.type)if(""===n)n=void 0;else{const e=parseInt(n,10);n=isNaN(e)?n:e}else if("number"===a.type)if(""===n)n=void 0;else{const e=parseFloat(n);n=isNaN(e)?n:e}E({...y,[t]:n})}}),a.description&&r.createElement(An,{type:"secondary",style:{fontSize:"12px"}},a.description),("integer"===a.type||"number"===a.type)&&r.createElement(An,{type:"secondary",style:{fontSize:"11px",fontStyle:"italic"}},"Expected: ",a.type," | Current:"," ",typeof y[t]," | Value:"," ",JSON.stringify(y[t])))})),r.createElement("div",{ref:B},r.createElement(d.Ay,{type:"primary",icon:r.createElement(Nn.A,{size:16}),onClick:W,loading:S,style:{width:"100%"}},S?"Executing...":"Run Tool"))))})()),I&&r.createElement(A.A,{type:"error",message:"Tool Operation Error",description:I,action:r.createElement(u.A,null,r.createElement(d.Ay,{size:"small",onClick:()=>z(null)},"Clear Error"),f&&r.createElement(d.Ay,{size:"small",type:"primary",onClick:W,loading:S},"Retry")),showIcon:!0}),$&&!x&&!M&&r.createElement(r.Fragment,null,r.createElement(gn,null),$?r.createElement(s.A,{size:"small",className:"bg-secondary",title:r.createElement(u.A,null,r.createElement(De.A,{size:16,color:"green"}),"Tool Result",r.createElement(C.A,{color:"green"},$.content.length," item",1!==$.content.length?"s":""))},r.createElement("div",{ref:H},r.createElement(u.A,{direction:"vertical",style:{width:"100%"}},$.content.map((e,t)=>Y(e,t))))):null)))},kn=(e,t)=>e.connected===t.connected&&e.capabilities===t.capabilities&&JSON.stringify(e.serverParams)===JSON.stringify(t.serverParams)&&!!e.wsClient==!!t.wsClient,$n=r.memo(Sn,kn);var On=n(8309);const{Text:In}=N.A,zn=e=>{let{serverParams:t,wsClient:n,connected:a,capabilities:o}=e;const{0:i,1:l}=(0,r.useState)([]),{0:s,1:c}=(0,r.useState)(null),{0:m,1:p}=(0,r.useState)(null),{0:f,1:h}=(0,r.useState)(!1),{0:b,1:y}=(0,r.useState)(!1),{0:E,1:x}=(0,r.useState)(null),{0:N,1:w}=(0,r.useState)(null),C=(0,r.useRef)(null),S=(0,r.useCallback)(async()=>{if(a&&n){n.clearActivityMessages&&n.clearActivityMessages(),h(!0),w(null);try{const e=await n.executeOperation({operation:"list_resources"});null!=e&&e.resources?l(e.resources):w("No resources received from server")}catch(e){w(`Failed to fetch resources: ${e.message||"Unknown error"}`)}finally{h(!1)}}else w("WebSocket not connected")},[a]),k=(0,r.useCallback)(async e=>{if(a&&n){n.clearActivityMessages&&n.clearActivityMessages(),y(!0),x(null),c(e);try{const t=await n.executeOperation({operation:"read_resource",uri:e.uri});null!=t&&t.contents?p(t.contents):x("No content received from resource")}catch(t){x(`Failed to get resource: ${t.message||"Unknown error"}`)}finally{y(!1)}}},[a,n]);(0,r.useEffect)(()=>{a&&null!=o&&o.resources&&S()},[a,null==o?void 0:o.resources,S]),(0,r.useEffect)(()=>{m&&C.current&&setTimeout(()=>{var e;null===(e=C.current)||void 0===e||e.scrollIntoView({behavior:"smooth",block:"nearest"})},100)},[m]);return r.createElement("div",{className:"mt-4 space-y-6 h-full overflow-auto"},r.createElement("div",{className:"bg-secondary rounded-lg border border-tertiary p-4"},r.createElement("div",{className:"flex items-center gap-2 mb-4"},r.createElement(g.A,{size:18,className:"text-primary"}),r.createElement("h3",{className:"text-lg font-semibold text-primary m-0"},"Available Resources")),r.createElement("div",{className:"space-y-4"},r.createElement(d.Ay,{type:"primary",onClick:S,loading:f,icon:r.createElement(g.A,{size:16}),className:"flex items-center gap-2"},i.length>0?"Refresh Resources":"Load Resources"),N&&r.createElement(A.A,{type:"error",message:"Failed to Load Resources",description:N,action:r.createElement(u.A,null,r.createElement(d.Ay,{size:"small",onClick:S,loading:f},"Retry"),r.createElement(d.Ay,{size:"small",onClick:()=>w(null)},"Clear")),showIcon:!0}),i.length>0?r.createElement("div",{className:"space-y-3"},i.map((e,t)=>r.createElement("div",{key:e.uri,className:"bg-primary border border-tertiary rounded-md p-3 hover:border-accent transition-colors"},r.createElement("div",{className:"flex justify-between items-start gap-3"},r.createElement("div",{className:"flex-1 min-w-0"},r.createElement("div",{className:"flex items-center gap-2 mb-2"},r.createElement(In,{className:"font-medium text-primary truncate"},e.name||e.uri),e.mimeType&&r.createElement("span",{className:"inline-flex items-center px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full"},e.mimeType)),e.description&&r.createElement(In,{className:"text-secondary text-sm mb-2 block"},e.description),r.createElement("div",{className:"bg-tertiary rounded px-2 py-1"},r.createElement(In,{className:"text-xs font-mono text-secondary break-all"},e.uri))),r.createElement(d.Ay,{type:"text",size:"small",icon:r.createElement(bn,{size:14}),onClick:()=>k(e),loading:b&&(null==s?void 0:s.uri)===e.uri,className:"flex items-center gap-1 text-accent hover:text-accent-dark hover:bg-accent/10"},"View"))))):!f&&r.createElement(In,{className:"text-secondary text-center block py-4"},"No resources found"),i.length>0&&r.createElement(In,{className:"text-secondary text-sm"},"Found ",i.length," resource(s)"))),s&&m&&!N&&r.createElement(r.Fragment,null,r.createElement("div",{className:"border-t border-tertiary"}),s&&m?r.createElement("div",{ref:C,className:"bg-secondary rounded-lg border border-tertiary p-4"},r.createElement("div",{className:"flex items-center gap-2 mb-4"},r.createElement(bn,{size:18,className:"text-primary"}),r.createElement("h3",{className:"text-lg font-semibold text-primary m-0"},"Resource Content: ",s.name||s.uri)),r.createElement("div",{className:"space-y-4"},m.map((e,t)=>{var n;return r.createElement("div",{key:t,className:"bg-primary border border-tertiary rounded-lg p-4"},r.createElement("div",{className:"flex items-center gap-2 mb-3"},r.createElement("span",{className:"inline-flex items-center px-2.5 py-1 text-sm font-medium bg-green-100 text-green-800 rounded-full"},e.type),e.mimeType&&r.createElement("span",{className:"inline-flex items-center px-2.5 py-1 text-sm font-medium bg-blue-100 text-blue-800 rounded-full"},e.mimeType)),e.text&&r.createElement("div",null,r.createElement("div",{className:"flex items-center gap-2 mb-2"},r.createElement(v.A,{size:16,className:"text-primary"}),r.createElement(In,{className:"font-medium text-primary"},"Content:")),r.createElement("pre",{className:"whitespace-pre-wrap bg-tertiary border border-tertiary rounded-md p-3 text-sm text-primary overflow-auto max-h-96 font-mono"},e.text)),e.blob&&r.createElement("div",null,r.createElement("div",{className:"flex items-center gap-2 mb-2"},r.createElement(On.A,{size:16,className:"text-primary"}),r.createElement(In,{className:"font-medium text-primary"},"Binary Data:")),r.createElement("div",{className:"bg-tertiary border border-tertiary rounded-md p-3"},r.createElement("div",{className:"flex items-center gap-2 text-secondary"},r.createElement(On.A,{size:16}),r.createElement(In,{className:"text-secondary"},"Binary content (",e.blob.length," characters)"),(null===(n=e.mimeType)||void 0===n?void 0:n.startsWith("image/"))&&r.createElement("span",{className:"inline-flex items-center px-2 py-1 text-xs font-medium bg-orange-100 text-orange-800 rounded-full ml-2"},"Image")))))}))):null),E&&r.createElement(A.A,{type:"error",message:"Resource Operation Error",description:E,action:r.createElement(u.A,null,r.createElement(d.Ay,{size:"small",onClick:()=>x(null)},"Clear Error"),r.createElement(d.Ay,{size:"small",onClick:S,loading:f},"Retry")),showIcon:!0}))},Mn=(e,t)=>e.connected===t.connected&&e.capabilities===t.capabilities&&JSON.stringify(e.serverParams)===JSON.stringify(t.serverParams)&&!!e.wsClient==!!t.wsClient,Tn=r.memo(zn,Mn),Rn=(0,Te.A)("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]]);var Pn=n(6305),jn=n(2640),_n=n(9492);const{Text:Bn}=N.A,{Option:Hn}=i.A,Dn=e=>{let{serverParams:t,wsClient:n,connected:a,capabilities:l}=e;const{0:s,1:c}=(0,r.useState)([]),{0:m,1:p}=(0,r.useState)(null),{0:g,1:f}=(0,r.useState)({}),{0:h,1:b}=(0,r.useState)(null),{0:y,1:E}=(0,r.useState)(!1),{0:x,1:N}=(0,r.useState)(!1),{0:w,1:C}=(0,r.useState)(null),{0:S,1:k}=(0,r.useState)(null),{0:$,1:O}=(0,r.useState)({}),I=(0,r.useRef)(null),z=(0,r.useCallback)(async()=>{if(a&&n){n.clearActivityMessages&&n.clearActivityMessages(),E(!0),k(null);try{const e=await n.executeOperation({operation:"list_prompts"});null!=e&&e.prompts?c(e.prompts):k("No prompts received from server")}catch(e){k(`Failed to fetch prompts: ${e.message||"Unknown error"}`)}finally{E(!1)}}else k("WebSocket not connected")},[a]),M=(0,r.useCallback)((e,t)=>{var n;const a=[];return((null===(n=e.arguments)||void 0===n?void 0:n.filter(e=>e.required))||[]).forEach(e=>{const n=t[e.name];(null==n||"string"==typeof n&&""===n.trim())&&a.push(`Required argument '${e.name}' is missing or empty`)}),a},[]),T=(0,r.useCallback)((e,t,n)=>!n||t&&""!==t.trim()?null:`${e} is required`,[]),R=(0,r.useCallback)((e,t,n)=>{f(n=>({...n,[e]:t}));const a=T(e,t,n);O(t=>({...t,[e]:a||""}))},[T]),P=(0,r.useCallback)(async e=>{if(!a||!n)return;const t=M(e,g);if(t.length>0)C(t.join(", "));else{n.clearActivityMessages&&n.clearActivityMessages(),N(!0),C(null),p(e);try{const t=await n.executeOperation({operation:"get_prompt",name:e.name,arguments:g});t?b({name:t.name||e.name,description:t.description,messages:t.messages||[]}):C("No prompt result received")}catch(r){C(`Failed to get prompt: ${r.message||"Unknown error"}`)}finally{N(!1)}}},[a,n,g,M]);(0,r.useEffect)(()=>{h&&I.current&&setTimeout(()=>{var e;null===(e=I.current)||void 0===e||e.scrollIntoView({behavior:"smooth",block:"nearest"})},100)},[h]),(0,r.useEffect)(()=>{a&&null!=l&&l.prompts&&z()},[a,null==l?void 0:l.prompts,z]),(0,r.useEffect)(()=>{s.length>0&&!m&&(p(s[0]),f({}),b(null),O({}))},[s,m]);return w?r.createElement(A.A,{type:"error",message:"Prompts Error",description:w,action:r.createElement(d.Ay,{size:"small",onClick:z,loading:y,icon:r.createElement(_n.A,{size:14}),className:"flex items-center gap-1"},"Retry"),className:"m-4"}):r.createElement("div",{className:"mt-4 space-y-6 h-full overflow-auto"},r.createElement("div",{className:"bg-secondary rounded-lg border border-tertiary p-4"},r.createElement("div",{className:"flex items-center gap-2 mb-4"},r.createElement(v.A,{size:18,className:"text-primary"}),r.createElement("h3",{className:"text-lg font-semibold text-primary m-0"},"Available Prompts")),r.createElement("div",{className:"space-y-4"},r.createElement(d.Ay,{type:"primary",onClick:z,loading:y,icon:r.createElement(v.A,{size:16}),className:"flex items-center gap-2"},s.length>0?"Refresh Prompts":"Load Prompts"),S&&r.createElement(A.A,{type:"error",message:"Failed to Load Prompts",description:S,action:r.createElement(u.A,null,r.createElement(d.Ay,{size:"small",onClick:z,loading:y},"Retry"),r.createElement(d.Ay,{size:"small",onClick:()=>k(null)},"Clear")),showIcon:!0}),s.length>0&&r.createElement("div",{className:"space-y-2"},r.createElement(i.A,{className:"w-full",placeholder:"Select a prompt to view",value:null==m?void 0:m.name,onChange:e=>{const t=s.find(t=>t.name===e);p(t||null),f({}),b(null),O({}),C(null)}},s.map(e=>r.createElement(Hn,{key:e.name,value:e.name,title:e.description},r.createElement(Bn,{className:"font-medium"},e.name)))),r.createElement(Bn,{className:"text-secondary text-sm"},"Found ",s.length," prompt(s)")),0===s.length&&!y&&r.createElement(Bn,{className:"text-secondary text-center block py-4"},"No prompts found")))," ",m&&!S&&r.createElement(r.Fragment,null,r.createElement("div",{className:"border-t border-tertiary"}),(()=>{if(!m)return null;const e=m.arguments||[];return r.createElement("div",{className:"bg-secondary rounded-lg border border-tertiary p-4"},r.createElement("div",{className:"flex items-center gap-2 mb-4"},r.createElement(Rn,{size:18,className:"text-primary"}),r.createElement("h3",{className:"text-lg font-semibold text-primary m-0"},"Configure ",m.name)),r.createElement("div",{className:"space-y-4"},m.description&&r.createElement(Bn,{className:"text-secondary block"},m.description),e.length>0?r.createElement(sn.A,{layout:"vertical",className:"space-y-4"},e.map(e=>r.createElement(sn.A.Item,{key:e.name,label:r.createElement("div",{className:"flex items-center gap-2"},r.createElement(Bn,{className:"font-medium text-primary"},e.name),e.required&&r.createElement("span",{className:"inline-flex items-center px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full"},"Required")),className:"mb-4"},r.createElement(o.A,{placeholder:e.description||`Enter ${e.name}`,value:g[e.name]||"",onChange:t=>R(e.name,t.target.value,e.required||!1),className:"w-full",status:$[e.name]?"error":void 0}),$[e.name]&&r.createElement(Bn,{className:"text-red-500 text-xs mt-1 block"},$[e.name]),e.description&&r.createElement(Bn,{className:"text-secondary text-xs mt-1 block"},e.description)))):r.createElement(Bn,{className:"text-secondary"},"This prompt has no arguments"),r.createElement(d.Ay,{type:"primary",icon:r.createElement(je.A,{size:16}),onClick:()=>P(m),loading:x,className:"w-full flex items-center justify-center gap-2"},x?"Loading...":"Get Prompt")))})()),w&&r.createElement(A.A,{type:"error",message:"Prompt Operation Error",description:w,action:r.createElement(u.A,null,r.createElement(d.Ay,{size:"small",onClick:()=>C(null)},"Clear Error"),m&&r.createElement(d.Ay,{size:"small",type:"primary",onClick:()=>P(m),loading:x},"Retry")),showIcon:!0}),h&&!S&&r.createElement(r.Fragment,null,r.createElement("div",{className:"border-t border-tertiary"}),h?r.createElement("div",{ref:I,className:"bg-secondary rounded-lg border border-tertiary p-4"},r.createElement("div",{className:"flex items-center gap-2 mb-4"},r.createElement(bn,{size:18,className:"text-primary"}),r.createElement("h3",{className:"text-lg font-semibold text-primary m-0"},"Prompt: ",h.name)),r.createElement("div",{className:"space-y-4"},h.description&&r.createElement(Bn,{className:"text-secondary block"},h.description),h.messages.length>0?r.createElement("div",{className:"space-y-3"},h.messages.map((e,t)=>r.createElement("div",{key:t,className:"rounded-lg border-l-4 p-4  bg-tertiary"},r.createElement("div",{className:"flex items-center gap-2 mb-2"},"user"===e.role?r.createElement(Pn.A,{size:16,className:"text-blue-600"}):r.createElement(jn.A,{size:16,className:"text-green-600"}),r.createElement("span",{className:"inline-flex items-center px-2.5 py-1 text-sm font-medium rounded-full "+("user"===e.role?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800")},e.role),r.createElement("span",{className:"inline-flex items-center px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full"},e.content.type)),r.createElement("pre",{className:"whitespace-pre-wrap font-mono text-sm text-primary leading-relaxed"},e.content.text)))):r.createElement(Bn,{className:"text-secondary text-center block py-4"},"No messages in this prompt"))):null))},Ln=(e,t)=>e.connected===t.connected&&e.capabilities===t.capabilities&&JSON.stringify(e.serverParams)===JSON.stringify(t.serverParams)&&!!e.wsClient==!!t.wsClient,Fn=r.memo(Dn,Ln);var Wn=n(2697),qn=n(6108);const{Title:Xn,Text:Gn,Paragraph:Un}=N.A,{Option:Vn}=i.A,Yn=e=>{let{name:t,schema:n,value:a,onChange:l,required:s=!1}=e;const{type:d,description:u,minimum:m,maximum:p,enum:g}=n||{},f=e=>{l(t,e)};return r.createElement(sn.A.Item,{label:u||t,required:s,style:{marginBottom:16}},(()=>{switch(d){case"boolean":return r.createElement(Wn.A,{checked:a||!1,onChange:e=>f(e.target.checked)},u||t);case"number":case"integer":return r.createElement(c.A,{value:a,onChange:f,min:m,max:p,placeholder:u||t,style:{width:"100%"},step:"integer"===d?1:.1});case"string":return g&&Array.isArray(g)?r.createElement(i.A,{value:a,onChange:f,placeholder:u||t,style:{width:"100%"}},g.map(e=>r.createElement(Vn,{key:e,value:e},e))):r.createElement(o.A,{value:a||"",onChange:e=>f(e.target.value),placeholder:u||t});default:return r.createElement(o.A,{value:a||"",onChange:e=>f(e.target.value),placeholder:u||t})}})())},Kn=e=>{let{request:t,onResponse:n,visible:a,onCancel:o}=e;const{0:i,1:l}=(0,r.useState)({}),{0:c,1:m}=(0,r.useState)(!1),{0:p,1:g}=(0,r.useState)(!1);(0,r.useEffect)(()=>{var e;if(null!=t&&null!==(e=t.requestedSchema)&&void 0!==e&&e.properties){const e={};Object.keys(t.requestedSchema.properties).forEach(n=>{const a=t.requestedSchema.properties[n];void 0!==a.default?e[n]=a.default:"boolean"===a.type&&(e[n]=!1)}),l(e)}else l({});g(!1)},[t]),(0,r.useEffect)(()=>{var e;if(null==t||null===(e=t.requestedSchema)||void 0===e||!e.properties)return void m(!0);const{properties:n,required:a=[]}=t.requestedSchema,r=a.every(e=>{const t=i[e];return null!=t&&""!==t});m(r)},[i,t]),(0,r.useEffect)(()=>{if(!a)return;const e=setTimeout(()=>{g(!0)},25e3);return()=>clearTimeout(e)},[a]);const f=(e,t)=>{l(n=>({...n,[e]:t}))},h=()=>{if(!t)return;const e={type:"elicitation_response",request_id:t.request_id,action:"cancel",session_id:t.session_id};n(e),o()};if(!t)return null;const{requestedSchema:b}=t,v=(null==b?void 0:b.properties)||{},y=(null==b?void 0:b.required)||[];return r.createElement(qn.A,{title:r.createElement(u.A,null,r.createElement(Le.A,{size:20,className:"text-blue-600"}),r.createElement("span",null,"Tool Request")),open:a,onCancel:h,footer:null,width:600,maskClosable:!1,destroyOnHidden:!0},r.createElement(s.A,null,r.createElement(u.A,{direction:"vertical",style:{width:"100%"},size:"large"},r.createElement("div",null,r.createElement(Xn,{level:4,style:{marginBottom:8}},"Request Details"),r.createElement(Un,null,t.message)),p&&r.createElement(A.A,{message:"Time Limit Warning",description:"This request will timeout in 5 seconds. Please respond quickly.",type:"warning",icon:r.createElement(vn.A,{size:16}),showIcon:!0}),Object.keys(v).length>0&&r.createElement("div",null,r.createElement(Xn,{level:5,style:{marginBottom:16}},"Required Information"),r.createElement(sn.A,{layout:"vertical"},Object.entries(v).map(e=>{let[t,n]=e;return r.createElement(Yn,{key:t,name:t,schema:n,value:i[t],onChange:f,required:y.includes(t)})}))),r.createElement("div",{style:{display:"flex",justifyContent:"flex-end",gap:8}},r.createElement(d.Ay,{onClick:h,size:"large"},"Cancel"),r.createElement(d.Ay,{onClick:()=>{if(!t)return;const e={type:"elicitation_response",request_id:t.request_id,action:"decline",session_id:t.session_id};n(e)},size:"large",danger:!0,icon:r.createElement(He.A,{size:16})},"Decline"),r.createElement(d.Ay,{onClick:()=>{if(!t)return;const e={type:"elicitation_response",request_id:t.request_id,action:"accept",data:i,session_id:t.session_id};console.log("ElicitationDialog: Sending accept response:",e),n(e)},type:"primary",size:"large",disabled:!c,icon:r.createElement(De.A,{size:16})},"Accept")))))},Zn=e=>{let{count:t,onClick:n}=e;return 0===t?null:r.createElement(d.Ay,{type:"primary",size:"small",onClick:n,style:{backgroundColor:"#f59e0b",borderColor:"#f59e0b",fontSize:"12px",height:"24px",padding:"0 8px"}},t," pending")},{Text:Jn,Title:Qn}=N.A,ea=e=>{let{serverParams:t}=e;const{0:n,1:a}=(0,r.useState)({connected:!1,connecting:!1,capabilities:null,sessionId:null,error:null,lastActivity:null,activityMessages:[],pendingElicitations:[]}),{0:o,1:i}=(0,r.useState)(null),{0:s,1:c}=(0,r.useState)("tools"),{0:m,1:p}=(0,r.useState)(!1),f=(0,r.useRef)(null),{0:b,1:y}=(0,r.useState)([]),E=(0,r.useRef)(null),{0:x,1:N}=(0,r.useState)(null),{0:$,1:O}=(0,r.useState)(!1),{connected:I,connecting:z,capabilities:M,error:T,pendingElicitations:R}=n;r.useEffect(()=>{E.current&&n.activityMessages.length>0&&E.current.scrollTo({top:E.current.scrollHeight,behavior:"smooth"})},[n.activityMessages]),r.useEffect(()=>{f.current=o},[o]);const P=(0,r.useCallback)(async()=>{o&&o.disconnect();const e=new Fe.dm(t,e=>{a(t=>({...t,...e}))});i(e),await e.connect()},[t]),j=(0,r.useCallback)(()=>{o&&(o.disconnect(),i(null))},[o]),_=(0,r.useCallback)(e=>{p(!0),c(e),setTimeout(()=>{p(!1)},150)},[]);r.useEffect(()=>{const e=f.current;e&&(e.disconnect(),i(null)),a({connected:!1,connecting:!1,capabilities:null,sessionId:null,error:null,lastActivity:null,activityMessages:[],pendingElicitations:[]}),c("tools"),p(!1),N(null),O(!1),y([])},[t]),r.useEffect(()=>{if(R.length>0&&!x){const e=R[0];N(e),O(!0)}else 0===R.length&&x&&(N(null),O(!1))},[R,x]);const B=(0,r.useCallback)(e=>{o&&x?(o.sendElicitationResponse(e),N(null),O(!1),R.length):console.error("McpCapabilitiesPanel: Cannot send response - missing wsClient or currentElicitation")},[o,x,R]),H=(0,r.useCallback)(()=>{if(x){const e={type:"elicitation_response",request_id:x.request_id,action:"cancel",session_id:x.session_id};B(e)}},[x,B]);r.useEffect(()=>()=>{console.log("Component unmounting, disconnecting WebSocket"),o&&o.disconnect()},[o]);const D=z?r.createElement("div",{className:"text-center py-8 px-4"},r.createElement(w.A,{size:"large",className:"mb-4"}),r.createElement("div",null,r.createElement(Jn,{strong:!0,className:"block mb-2"},"正在连接到 MCP 服务器"),r.createElement(Jn,{type:"secondary",className:"text-sm"},"正在建立 WebSocket 连接并发现功能...")),r.createElement("div",{className:"mt-4"},r.createElement(d.Ay,{onClick:j,size:"small",icon:r.createElement(Re,{size:14})},"取消"))):T?r.createElement(A.A,{message:"Connection Error",description:r.createElement("div",null,r.createElement(Jn,null,T),T.includes("JSON")&&r.createElement("div",{className:"mt-2"},r.createElement(Jn,{type:"secondary",className:"text-xs"},"This might indicate the 多智能体工作室 server is not running or the MCP routes are not properly configured.")),r.createElement("div",{className:"mt-3"},r.createElement(u.A,null,r.createElement(d.Ay,{type:"primary",size:"small",onClick:P,icon:r.createElement(Pe,{size:14})},"Retry Connection"),r.createElement(d.Ay,{size:"small",onClick:j,icon:r.createElement(Re,{size:14})},"Reset")))),type:"error",showIcon:!0,className:"m-4"}):I||M?null:r.createElement("div",{className:"text-center py-8 px-4"},r.createElement("div",{className:"mb-6"},r.createElement(Qn,{level:4,className:"mb-2 text-secondary"},"MCP 服务器测试面板"),r.createElement(Jn,{type:"secondary",className:"block text-sm leading-relaxed max-w-md mx-auto"},"点击下方按钮建立 WebSocket 连接，并发现此 MCP 服务器提供的工具、资源和提示。")),r.createElement(d.Ay,{type:"primary",onClick:P,icon:r.createElement(je.A,{size:16}),size:"large",className:"rounded-md h-10 px-6 font-medium"},"连接到服务器"),r.createElement("div",{className:"mt-4"},r.createElement(Jn,{type:"secondary",className:"text-xs"},"Server: ",t.type," •"," ",t.command||t.url||"Unknown")));if(D)return D;const L=[];null!=M&&M.tools&&L.push({label:r.createElement("span",{className:"flex items-center gap-1.5"},r.createElement(h.A,{size:16}),r.createElement("span",null,"Tools")),value:"tools"}),null!=M&&M.resources&&L.push({label:r.createElement("span",{className:"flex items-center gap-1.5"},r.createElement(g.A,{size:16}),r.createElement("span",null,"Resources")),value:"resources"}),null!=M&&M.prompts&&L.push({label:r.createElement("span",{className:"flex items-center gap-1.5"},r.createElement(v.A,{size:16}),r.createElement("span",null,"Prompts")),value:"prompts"});return r.createElement("div",{className:"h-full flex flex-col"},r.createElement("div",null,r.createElement("div",{className:"flex justify-between items-center mb-3"},r.createElement("div",{className:"flex items-center gap-2"},r.createElement(Qn,{level:5,className:"m-0"},"Server Capabilities"),R.length>0&&r.createElement(Zn,{count:R.length})),r.createElement(u.A,null,I||z?z?r.createElement(C.A,{color:"orange",icon:r.createElement(w.A,{size:"small"})},"Connecting"):r.createElement(C.A,{color:"green",icon:r.createElement(Pe,{className:"inline-block mr-1",size:12})},"Connected"):r.createElement(C.A,{color:"red",icon:r.createElement(_e,{className:"inline-block mr-1",size:12})},"Disconnected"),I&&r.createElement(d.Ay,{size:"small",onClick:j,icon:r.createElement(Re,{size:12}),type:"text"},"Disconnect"))),L.length>0&&r.createElement(Me,{options:L,value:s,onChange:_,className:"w-full"})),r.createElement("div",{className:"flex-1 overflow-hidden mt-3"},r.createElement("div",{className:"h-full grid gap-4",style:{gridTemplateColumns:I&&n.activityMessages.length>0?"1fr 400px":"1fr"}},r.createElement("div",{className:"h-full overflow-auto"},!I||M||T?0===L.length?r.createElement(k.A,{description:r.createElement("div",{className:"text-center"},r.createElement(Jn,{type:"secondary"},"此 MCP 服务器未公开任何功能"),r.createElement("br",null),r.createElement(Jn,{type:"secondary",className:"text-xs"},"没有可用的工具、资源或提示")),className:"py-12 px-6"}):(()=>{if(m)return r.createElement("div",{className:"p-6"},r.createElement(S.A,{active:!0,paragraph:{rows:4}}));switch(s){case"tools":return r.createElement($n,{key:"tools-tab",serverParams:t,wsClient:o,connected:I,capabilities:M});case"resources":return r.createElement(Tn,{key:"resources-tab",serverParams:t,wsClient:o,connected:I,capabilities:M});case"prompts":return r.createElement(Fn,{key:"prompts-tab",serverParams:t,wsClient:o,connected:I,capabilities:M});default:return r.createElement(k.A,{description:"选择一个功能进行探索",className:"py-12 px-6"})}})():r.createElement("div",{className:"text-center py-12 px-6"},r.createElement(w.A,{size:"large",className:"mb-4"}),r.createElement("div",null,r.createElement(Jn,{strong:!0,className:"block mb-2"},"正在发现服务器功能"),r.createElement(Jn,{type:"secondary",className:"text-sm"},"正在获取可用的工具、资源和提示...")))),I&&n.activityMessages.length>0&&(()=>{const{activityMessages:e}=n;return I&&0!==e.length?r.createElement("div",{className:"h-full flex flex-col border-l border-gray-200"},r.createElement("div",{className:"px-4 py-3 border-b border-gray-200  "},r.createElement("div",{className:"flex items-center justify-between"},r.createElement("div",{className:"flex items-center gap-2"},r.createElement(Be,{size:16,className:"text-gray-600"}),r.createElement("span",{className:"font-medium text-sm"},"Notification Stream"),r.createElement(oe,{count:e.length,size:"small"})),r.createElement(d.Ay,{size:"small",type:"text",onClick:()=>a(e=>({...e,activityMessages:[]})),className:"text-xs"},"Clear"))),r.createElement("div",{ref:E,className:"flex-1 overflow-y-auto p-2 space-y-1"},e.map(e=>r.createElement("div",{key:e.id,className:"border bg-secondary rounded p-2 hover:bg-primary transition-colors"},r.createElement("div",{className:"flex items-center justify-between"},r.createElement("div",{className:"flex items-center gap-2 flex-1 min-w-0"},r.createElement("span",{className:"text-gray-500 flex-shrink-0"},"protocol"===e.activity_type&&r.createElement(Be,{size:14}),"error"===e.activity_type&&r.createElement(He.A,{size:14}),"sampling"===e.activity_type&&r.createElement(De.A,{size:14}),"elicitation"===e.activity_type&&r.createElement(Le.A,{size:14})),r.createElement(Jn,{className:"text-sm truncate flex-1"},e.message)),r.createElement(Jn,{type:"secondary",className:"text-xs whitespace-nowrap ml-2 flex-shrink-0"},e.timestamp.toLocaleTimeString())),e.details&&r.createElement(l.A,{ghost:!0,size:"small",className:"mt-1",items:[{key:"1",label:r.createElement(Jn,{type:"secondary",className:"text-xs"},"View Details"),children:r.createElement("pre",{className:"bg-gray-50 p-2 rounded text-xs overflow-x-auto mt-1"},JSON.stringify(e.details,null,2))}]}))))):null})())),x&&r.createElement(Kn,{visible:$,onCancel:H,request:x,onResponse:B}))},{TextArea:ta}=o.A,{Option:na}=i.A,{Panel:aa}=l.A,ra=e=>{let{component:t,onChange:n,defaultPanelKey:i=["configuration","testing"],readonly:N=!1}=e;const{0:w,1:A}=(0,r.useState)(!1),{0:C,1:S}=(0,r.useState)({key:"",value:""}),k=(0,r.useCallback)(e=>{n({...t,...e,config:{...t.config,...e.config||{}}})},[t,n]),$=()=>{if((0,E.wb)(t)){var e;const n=t.config;return r.createElement(s.A,{className:"mb-4",size:"small"},r.createElement("div",{className:"space-y-2"},r.createElement("div",{className:"flex items-center justify-between"},r.createElement("h4",{className:"font-medium text-lg"},t.label||"Static Workbench"),r.createElement("span",{className:"text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded"},"Static")),t.description&&r.createElement("p",{className:"text-sm text-secondary"},t.description),r.createElement("div",{className:"flex items-center gap-4 text-sm text-gray-500"},r.createElement("span",null,"Version: ",t.version||1),r.createElement("span",null,"•"),r.createElement("span",null,"Tools: ",(null===(e=n.tools)||void 0===e?void 0:e.length)||0))))}if((0,E.Mf)(t)){const e=t.config.server_params;let n="";return"StdioServerParams"===e.type?n=`${e.command||"N/A"}`:("SseServerParams"===e.type||"StreamableHttpServerParams"===e.type)&&(n=e.url||"N/A"),r.createElement(s.A,{className:"mb-4",size:"small"},r.createElement("div",{className:"space-y-2"},r.createElement("div",{className:"flex items-center justify-between"},r.createElement("h4",{className:"font-medium text-lg"},t.label||"MCP 工作台"),r.createElement("span",{className:"text-xs bg-green-100 text-green-700 px-2 py-1 rounded"},"MCP")),t.description&&r.createElement("p",{className:"text-sm text-secondary"},t.description),r.createElement("div",{className:"flex items-center gap-4 text-sm text-secondary"},r.createElement("span",null,"Version: ",t.version||1),r.createElement("span",null,"•"),r.createElement("span",null,"Type: ",e.type.replace("ServerParams","")),r.createElement("span",null,"•"),r.createElement("span",{className:"truncate max-w-40"},n))))}return null};if((0,E.wb)(t)){var O;const e=t.config,n=()=>{const t=[].concat((0,a.A)(e.tools||[]),[{provider:"autogen_core.tools.FunctionTool",component_type:"tool",version:1,component_version:1,label:"New Tool",description:"A new tool",config:{source_code:'def new_tool():\n    """A new tool function"""\n    return "Hello from new tool"',name:"new_tool",description:"A new tool",global_imports:[],has_cancellation_support:!1}}]);k({config:{...e,tools:t}})},s=(t,n)=>{const r=(0,a.A)(e.tools||[]);r[t]={...r[t],...n},k({config:{...e,tools:r}})},u=t=>{const n=(0,a.A)(e.tools||[]);n.splice(t,1),k({config:{...e,tools:n}})},v=()=>{const e=t.label||"",n=e.length>20?`${e.substring(0,30)}...`:e;return r.createElement("div",{className:"flex items-center gap-2"},r.createElement(m.A,{className:"w-4 h-4 text-blue-500"}),r.createElement("span",{className:"font-medium"},"Component",n&&r.createElement("span",{className:"text-gray-500 font-normal ml-2"},"(",n,")")))};return r.createElement("div",{className:"space-y-4"},N?r.createElement(r.Fragment,null,r.createElement($,null),r.createElement("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4"},r.createElement("p",{className:"text-sm text-yellow-800"},r.createElement("strong",null,"Note:")," Static workbenches don't have runtime testing capabilities. Tools are predefined and tested through the component's execution environment."))):r.createElement(l.A,{defaultActiveKey:i,className:"border-0",expandIconPosition:"end",items:[{key:"details",label:v(),children:r.createElement("div",{className:"space-y-4"},r.createElement("label",{className:"block"},r.createElement("span",{className:"text-sm font-medium text-secondary"},"Label"),r.createElement(o.A,{value:t.label||"",onChange:e=>k({label:e.target.value}),placeholder:"Workbench label",className:"mt-1"})),r.createElement("label",{className:"block"},r.createElement("span",{className:"text-sm font-medium text-secondary"},"Description"),r.createElement(ta,{value:t.description||"",onChange:e=>k({description:e.target.value}),placeholder:"Workbench description",rows:3,className:"mt-1"})),r.createElement("div",{className:"grid grid-cols-2 gap-4"},r.createElement("label",{className:"block"},r.createElement("span",{className:"text-sm font-medium text-secondary"},"Version"),r.createElement(c.A,{value:t.version||1,onChange:e=>k({version:e||1}),min:1,precision:0,className:"w-full mt-1",placeholder:"e.g., 1"})),r.createElement("label",{className:"block"},r.createElement("span",{className:"text-sm font-medium text-secondary"},"Component Version"),r.createElement(c.A,{value:t.component_version||1,onChange:e=>k({component_version:e||1}),min:1,precision:0,className:"w-full mt-1",placeholder:"e.g., 1"}))))},{key:"configuration",label:r.createElement("div",{className:"flex items-center gap-2"},r.createElement(p.A,{className:"w-4 h-4 text-green-500"}),r.createElement("span",{className:"font-medium"},"Static Workbench Configuration")),children:r.createElement("div",{className:"space-y-4"},r.createElement("div",{className:"flex items-center justify-between"},r.createElement("div",{className:"flex items-center gap-2 text-sm text-secondary"},r.createElement(g.A,{className:"w-4 h-4"}),r.createElement("span",null,"Tools: ",(null===(O=e.tools)||void 0===O?void 0:O.length)||0," configured")),r.createElement(d.Ay,{type:"primary",size:"small",onClick:n,icon:r.createElement(f.A,{className:"h-4 w-4"})},"Add Tool")),e.tools&&e.tools.length>0?r.createElement(l.A,{className:""},e.tools.map((t,n)=>{var a;return(0,E.d9)(t)?r.createElement(aa,{key:n,header:r.createElement("div",{className:"flex items-center justify-between w-full"},r.createElement("div",{className:"flex items-center gap-2"},r.createElement(h.A,{className:"w-4 h-4 text-blue-500"}),r.createElement("span",{className:"font-medium"},(null===(a=t.config)||void 0===a?void 0:a.name)||t.label||"Unnamed Tool"),r.createElement("span",{className:"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded"},t.provider)),e.tools.length>1&&r.createElement(d.Ay,{type:"text",size:"small",danger:!0,onClick:e=>{e.stopPropagation(),u(n)},icon:r.createElement(b.A,{className:"h-4 w-4"})}))},r.createElement(x.e,{component:t,onChange:e=>s(n,e)})):null})):r.createElement("div",{className:"text-center text-gray-500 py-8 border-2 border-dashed border-gray-200 rounded-lg"},r.createElement(g.A,{className:"w-12 h-12 mx-auto mb-4 text-gray-400"}),r.createElement("p",{className:"mb-4"},"No tools configured"),r.createElement(d.Ay,{type:"dashed",onClick:n},"Add Your First Tool")),r.createElement("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4"},r.createElement("p",{className:"text-sm text-blue-800"},'Static workbenches contain a predefined set of tools. Click on a tool above to edit its configuration, or add new tools using the "Add Tool" button.')))}]}))}if((0,E.Mf)(t)){var I;const e=t.config.server_params,n=n=>{k({config:{...t.config,server_params:{...e,...n}}})},s=()=>{if(!C.key||!C.value)return;const t={..."StdioServerParams"===e.type&&e.env||{},[C.key]:C.value};"StdioServerParams"===e.type&&n({env:t}),S({key:"",value:""}),A(!1)},g=t=>{if("StdioServerParams"===e.type&&e.env){const a={...e.env};delete a[t],n({env:a})}},h=()=>{const e=t.label||"",n=e.length>30?`${e.substring(0,30)}...`:e;return r.createElement("div",{className:"flex items-center gap-2"},r.createElement(m.A,{className:"w-4 h-4 text-blue-500"}),r.createElement("span",{className:"font-medium"},"Details",n&&r.createElement("span",{className:"text-gray-500 font-normal ml-2"},"(",n,")")))};return r.createElement("div",{className:"space-y-4 scroll  h-full"},N?r.createElement("div",{className:" h-full"},r.createElement($,null),r.createElement(l.A,{defaultActiveKey:["testing"],className:"border-0",expandIconPosition:"end",items:[{key:"testing",label:r.createElement("div",{className:"flex items-center gap-2"},r.createElement(v.A,{className:"w-4 h-4 text-purple-500"}),r.createElement("span",{className:"font-medium"},"MCP Testing Panel")),children:r.createElement(r.Fragment,null,r.createElement(ea,{serverParams:e}))}]})):r.createElement(l.A,{defaultActiveKey:i,className:"border-0",expandIconPosition:"end",items:[{key:"details",label:h(),children:r.createElement("div",{className:"space-y-4"},r.createElement("label",{className:"block"},r.createElement("span",{className:"text-sm font-medium text-secondary"},"Label"),r.createElement(o.A,{value:t.label||"",onChange:e=>k({label:e.target.value}),placeholder:"Workbench label",className:"mt-1"})),r.createElement("label",{className:"block"},r.createElement("span",{className:"text-sm font-medium text-secondary"},"Description"),r.createElement(ta,{value:t.description||"",onChange:e=>k({description:e.target.value}),placeholder:"Workbench description",rows:3,className:"mt-1"})),r.createElement("div",{className:"grid grid-cols-2 gap-4"},r.createElement("label",{className:"block"},r.createElement("span",{className:"text-sm font-medium text-secondary"},"Version"),r.createElement(c.A,{value:t.version||1,onChange:e=>k({version:e||1}),min:1,precision:0,className:"w-full mt-1",placeholder:"e.g., 1"})),r.createElement("label",{className:"block"},r.createElement("span",{className:"text-sm font-medium text-secondary"},"Component Version"),r.createElement(c.A,{value:t.component_version||1,onChange:e=>k({component_version:e||1}),min:1,precision:0,className:"w-full mt-1",placeholder:"e.g., 1"}))))},{key:"configuration",label:r.createElement("div",{className:"flex items-center gap-2"},r.createElement(p.A,{className:"w-4 h-4 text-green-500"}),r.createElement("span",{className:"font-medium"},"Configuration")),children:r.createElement("div",{className:"space-y-4"},r.createElement("div",{className:"flex items-center gap-2 text-sm text-secondary"},r.createElement(p.A,{className:"w-4 h-4"}),r.createElement("span",null,"Server Type: ",e.type)),"StdioServerParams"===e.type&&r.createElement(r.Fragment,null,r.createElement("label",{className:"block"},r.createElement("span",{className:"text-sm font-medium text-primary"},"Command"),r.createElement(o.A,{value:e.command||"",onChange:e=>n({command:e.target.value}),placeholder:"e.g., uvx",className:"mt-1"})),r.createElement("div",{className:"space-y-2"},r.createElement("div",{className:"flex items-center justify-between"},r.createElement("span",{className:"text-sm font-medium text-primary"},"Arguments"),r.createElement(d.Ay,{type:"dashed",size:"small",icon:r.createElement(f.A,{className:"w-4 h-4"}),onClick:()=>{const t=e.args||[];n({args:[].concat((0,a.A)(t),[""])})}},"Add Argument")),r.createElement("div",{className:"space-y-2"},0===(e.args||[]).length?r.createElement("div",{className:"text-sm text-secondary italic p-3 border border-dashed border-secondary rounded"},'No arguments. Click "Add Argument" to add command line arguments.'):(e.args||[]).map((t,i)=>r.createElement("div",{key:i,className:"flex items-center gap-2"},r.createElement("span",{className:"text-xs text-secondary w-8"},i),r.createElement(o.A,{value:t,onChange:t=>{const r=(0,a.A)(e.args||[]);r[i]=t.target.value,n({args:r})},placeholder:`Argument ${i}`,className:"flex-1"}),r.createElement(d.Ay,{type:"text",size:"small",icon:r.createElement(b.A,{className:"w-4 h-4"}),onClick:()=>{const t=(0,a.A)(e.args||[]);t.splice(i,1),n({args:t})},className:"text-red-500 hover:text-red-700"})))),(e.args||[]).length>0&&r.createElement("div",{className:"text-xs text-secondary bg-secondary/30 p-2 rounded"},r.createElement("strong",null,"Command preview:")," ",e.command," ",(e.args||[]).join(" "))),r.createElement("label",{className:"block"},r.createElement("span",{className:"text-sm font-medium text-primary"},"Read Timeout (seconds)"),r.createElement(o.A,{type:"number",value:e.read_timeout_seconds||5,onChange:e=>n({read_timeout_seconds:parseFloat(e.target.value)||5}),className:"mt-1"})),r.createElement("div",{className:"space-y-2"},r.createElement("span",{className:"text-sm font-medium text-primary"},"Environment Variables"),e.env&&Object.keys(e.env).length>0&&r.createElement("div",{className:"space-y-2"},Object.entries(e.env).map(e=>{let[t,n]=e;return r.createElement("div",{key:t,className:"flex items-center gap-2 bg-gray-50 rounded px-3 py-2"},r.createElement("span",{className:"font-mono text-sm flex-1"},t,"=",n),r.createElement(d.Ay,{type:"text",size:"small",onClick:()=>g(t),icon:r.createElement(y.A,{className:"h-4 w-4"})}))})),w?r.createElement("div",{className:"border rounded p-3 space-y-3"},r.createElement(u.A,null,r.createElement(o.A,{placeholder:"Variable name",value:C.key,onChange:e=>S(t=>({...t,key:e.target.value}))}),r.createElement(o.A,{placeholder:"Variable value",value:C.value,onChange:e=>S(t=>({...t,value:e.target.value})),onKeyDown:e=>{"Enter"===e.key&&C.key&&C.value&&s()}}),r.createElement(d.Ay,{onClick:s,disabled:!C.key||!C.value},"Add"))):r.createElement(d.Ay,{type:"dashed",onClick:()=>A(!0),className:"w-full"},r.createElement(f.A,{className:"h-4 w-4 mr-2"}),"Add Environment Variable"))),"SseServerParams"===e.type&&r.createElement(r.Fragment,null,r.createElement("label",{className:"block"},r.createElement("span",{className:"text-sm font-medium text-primary"},"Server URL"),r.createElement(o.A,{value:e.url||"",onChange:e=>n({url:e.target.value}),placeholder:"https://your-mcp-server.com",className:"mt-1"})),r.createElement("label",{className:"block"},r.createElement("span",{className:"text-sm font-medium text-primary"},"Timeout (seconds)"),r.createElement(o.A,{type:"number",value:e.timeout||5,onChange:e=>n({timeout:parseFloat(e.target.value)||5}),className:"mt-1"})),r.createElement("label",{className:"block"},r.createElement("span",{className:"text-sm font-medium text-primary"},"SSE Read Timeout (seconds)"),r.createElement(o.A,{type:"number",value:e.sse_read_timeout||300,onChange:e=>n({sse_read_timeout:parseFloat(e.target.value)||300}),className:"mt-1"}))),"StreamableHttpServerParams"===e.type&&r.createElement(r.Fragment,null,r.createElement("label",{className:"block"},r.createElement("span",{className:"text-sm font-medium text-secondary"},"Server URL"),r.createElement(o.A,{type:"url",value:e.url||"",onChange:e=>n({url:e.target.value}),placeholder:"https://your-streamable-http-server.com",className:"mt-1"})),r.createElement("label",{className:"block"},r.createElement("span",{className:"text-sm font-medium text-primary"},"Timeout (seconds)"),r.createElement(o.A,{type:"number",value:e.timeout||30,onChange:e=>n({timeout:parseFloat(e.target.value)||30}),className:"mt-1"})),r.createElement("label",{className:"block"},r.createElement("span",{className:"text-sm font-medium text-primary"},"SSE Read Timeout (seconds)"),r.createElement(o.A,{type:"number",value:e.sse_read_timeout||300,onChange:e=>n({sse_read_timeout:parseFloat(e.target.value)||300}),className:"mt-1"})),r.createElement("label",{className:"flex items-center gap-2"},r.createElement("input",{type:"checkbox",checked:null===(I=e.terminate_on_close)||void 0===I||I,onChange:e=>n({terminate_on_close:e.target.checked}),className:"rounded"}),r.createElement("span",{className:"text-sm font-medium text-primary"},"Terminate on Close"))),r.createElement("div",{className:"bg-secondary/30 border border-secondary rounded-lg p-4"},r.createElement("p",{className:"text-sm text-secondary"},"StreamableHttpServerParams"===e.type?"Streamable HTTP workbenches connect to MCP servers over HTTP with streaming capabilities, ideal for cloud-based services and web APIs.":"MCP (Model Context Protocol) workbenches connect to external tool servers that provide dynamic tool capabilities. The tools available depend on what the MCP server provides at runtime.")))},{key:"testing",label:r.createElement("div",{className:"flex items-center gap-2"},r.createElement(v.A,{className:"w-4 h-4 text-purple-500"}),r.createElement("span",{className:"font-medium"},"MCP Testing Panel")),children:r.createElement(r.Fragment,null,r.createElement(ea,{serverParams:e}))}]}))}return r.createElement("div",{className:"space-y-4"},r.createElement("div",{className:"text-center text-gray-500 py-8"},r.createElement(g.A,{className:"w-12 h-12 mx-auto mb-4 text-gray-400"}),r.createElement("p",null,"Unknown workbench type")))}},9709:function(e,t,n){n.d(t,{B0:function(){return c},dm:function(){return s}});var a=n(436),r=n(7387),o=n(3838),i=n(180);let l=function(e){function t(){return e.apply(this,arguments)||this}(0,r.A)(t,e);var n=t.prototype;return n.listGalleries=async function(e){const t=await fetch(`${this.getBaseUrl()}/gallery/?user_id=${e}`,{headers:this.getHeaders()}),n=await t.json();if(!n.status)throw new Error(n.message||"Failed to fetch galleries");return n.data},n.getGallery=async function(e,t){const n=await fetch(`${this.getBaseUrl()}/gallery/${e}?user_id=${t}`,{headers:this.getHeaders()}),a=await n.json();if(!a.status)throw new Error(a.message||"Failed to fetch gallery");return a.data},n.extractMcpWorkbenches=function(e){var t;return(null===(t=e.config.components.workbenches)||void 0===t?void 0:t.filter(e=>{var t;return e.provider.includes("McpWorkbench")||void 0!==(null===(t=e.config)||void 0===t?void 0:t.server_params)}))||[]},n.listResources=async function(e){const t=await fetch(`${this.getBaseUrl()}/mcp/resources/list`,{method:"POST",headers:this.getHeaders(),body:JSON.stringify({server_params:e})}),n=await t.json();if(!t.ok)throw new Error(n.message||"Failed to list MCP resources");return n},n.getResource=async function(e,t){const n=await fetch(`${this.getBaseUrl()}/mcp/resources/get`,{method:"POST",headers:this.getHeaders(),body:JSON.stringify({server_params:e,uri:t})}),a=await n.json();if(!n.ok)throw new Error(a.message||"Failed to get MCP resource");return a},n.listPrompts=async function(e){const t=await fetch(`${this.getBaseUrl()}/mcp/prompts/list`,{method:"POST",headers:this.getHeaders(),body:JSON.stringify({server_params:e})}),n=await t.json();if(!t.ok)throw new Error(n.message||"Failed to list MCP prompts");return n},n.getPrompt=async function(e,t,n){const a=await fetch(`${this.getBaseUrl()}/mcp/prompts/get`,{method:"POST",headers:this.getHeaders(),body:JSON.stringify({server_params:e,name:t,arguments:n||{}})}),r=await a.json();if(!a.ok)throw new Error(r.message||"Failed to get MCP prompt");return r},n.getCapabilities=async function(e){const t=await fetch(`${this.getBaseUrl()}/mcp/capabilities/get`,{method:"POST",headers:this.getHeaders(),body:JSON.stringify({server_params:e})}),n=await t.json();if(!t.ok)throw new Error(n.message||"Failed to get MCP capabilities");return n},n.listTools=async function(e){const t=await fetch(`${this.getBaseUrl()}/mcp/tools/list`,{method:"POST",headers:this.getHeaders(),body:JSON.stringify({server_params:e})}),n=await t.json();if(!t.ok)throw new Error(n.message||"Failed to list MCP tools");return n},n.callTool=async function(e,t,n){const a=await fetch(`${this.getBaseUrl()}/mcp/tools/call`,{method:"POST",headers:this.getHeaders(),body:JSON.stringify({server_params:e,tool_name:t,arguments:n})}),r=await a.json();if(!a.ok)throw new Error(r.message||"Failed to call MCP tool");return r},n.healthCheck=async function(){const e=await fetch(`${this.getBaseUrl()}/mcp/health`,{method:"GET",headers:this.getHeaders()}),t=await e.json();if(!e.ok)throw new Error(t.message||"MCP health check failed");return t},n.testMcpConnection=async function(e){try{if(e.config.server_params){return(await this.listTools(e.config.server_params)).status}return!1}catch(t){return!1}},n.createWebSocketConnection=async function(e){const t=await fetch(`${this.getBaseUrl()}/mcp/ws/connect`,{method:"POST",headers:this.getHeaders(),body:JSON.stringify({server_params:e})});if(!t.ok){const e=await t.text();throw new Error(`HTTP ${t.status}: ${e||"Failed to create WebSocket connection"}`)}try{const e=await t.text();return JSON.parse(e)}catch(n){throw new Error("Invalid JSON response from server. Check if the MCP route is properly configured.")}},t}(o.y),s=function(){function e(e,t){this.wsRef=null,this.operationPromises=new Map,this.operationTimers=new Map,this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.baseReconnectDelay=1e3,this.reconnectTimeout=null,this.currentState={connected:!1,connecting:!1,capabilities:null,sessionId:null,error:null,lastActivity:null,activityMessages:[],pendingElicitations:[]},this.serverParams=e,this.onStateChange=t}var t=e.prototype;return t.updateState=function(e){this.currentState={...this.currentState,...e},this.onStateChange(e)},t.clearTimersForOperation=function(e){const t=this.operationTimers.get(e);t&&(t.forEach(e=>clearTimeout(e)),t.clear())},t.getWebSocketBaseUrl=function(e){try{let t=e.replace(/(^\w+:|^)\/\//,"");return t=t.startsWith("localhost")?t.replace("/api",""):"/api"===t?window.location.host:t.replace("/api","").replace(/\/$/,""),t}catch(t){throw new Error("Invalid server URL configuration")}},t.connect=async function(){this.updateState({connecting:!0,error:null});try{const e=c,t=await e.createWebSocketConnection(this.serverParams);if(!t.status)throw new Error(t.message||"Failed to create WebSocket connection");const{session_id:n,websocket_url:r}=t,o=(0,i.Tt)(),l=this.getWebSocketBaseUrl(o),s=`${"https:"===window.location.protocol?"wss:":"ws:"}//${l}${r}`,d=new WebSocket(s);this.wsRef=d,d.onopen=()=>{this.updateState({connected:!0,connecting:!1,sessionId:n,error:null,lastActivity:new Date}),this.reconnectAttempts=0},d.onmessage=e=>{try{const t=JSON.parse(e.data);switch(console.log("Received MCP WebSocket message:",t),this.updateState({lastActivity:new Date}),t.type){case"connected":case"initializing":case"pong":default:break;case"initialized":t.capabilities&&this.updateState({capabilities:t.capabilities});break;case"operation_result":if(t.operation){const e=t.operation,n=this.operationPromises.get(e);n&&(this.clearTimersForOperation(e),n.resolve(t.data),this.operationPromises.delete(e))}break;case"operation_error":if(t.operation){const e=this.operationPromises.get(t.operation);e&&(this.clearTimersForOperation(t.operation),e.reject(new Error(t.error||"Operation failed")),this.operationPromises.delete(t.operation))}break;case"error":this.updateState({error:t.error||"Connection error"});break;case"mcp_activity":if(t.message&&t.activity_type){const e={id:`${Date.now()}-${Math.random().toString(36).substr(2,9)}`,activity_type:t.activity_type,message:t.message,details:t.details,session_id:t.session_id,timestamp:t.timestamp?new Date(t.timestamp):new Date};this.updateState({activityMessages:[].concat((0,a.A)(this.currentState.activityMessages),[e])})}break;case"elicitation_request":if(t.request_id&&t.message){const e={request_id:t.request_id,message:t.message,requestedSchema:t.requestedSchema,session_id:t.session_id||this.currentState.sessionId||"",timestamp:t.timestamp||(new Date).toISOString()};this.updateState({pendingElicitations:[].concat((0,a.A)(this.currentState.pendingElicitations),[e])})}else console.error("Invalid elicitation request - missing request_id or message:",t)}}catch(t){}},d.onerror=e=>{this.updateState({error:"WebSocket connection error",connected:!1,connecting:!1})},d.onclose=e=>{this.updateState({connected:!1,connecting:!1})}}catch(e){this.updateState({error:e instanceof Error?e.message:"Connection failed",connected:!1,connecting:!1})}},t.executeOperation=async function(e){return new Promise((t,n)=>{if(!this.wsRef||this.wsRef.readyState!==WebSocket.OPEN)return void n(new Error("WebSocket not connected"));const a=e.operation;this.operationPromises.set(a,{resolve:t,reject:n});const r={type:"operation",...e};try{this.wsRef.send(JSON.stringify(r))}catch(i){this.operationPromises.delete(a),n(i)}const o=setTimeout(()=>{this.operationPromises.has(a)&&(this.operationPromises.delete(a),n(new Error("Operation timeout")))},12e4);this.operationTimers.has(a)||this.operationTimers.set(a,new Set),this.operationTimers.get(a).add(o)})},t.ping=function(){this.wsRef&&this.wsRef.readyState===WebSocket.OPEN&&this.wsRef.send(JSON.stringify({type:"ping"}))},t.disconnect=function(){this.reconnectTimeout&&(clearTimeout(this.reconnectTimeout),this.reconnectTimeout=null),this.wsRef&&(this.wsRef.close(),this.wsRef=null),this.operationPromises.forEach(e=>{let{reject:t}=e;t(new Error("WebSocket connection closed"))}),this.operationPromises.clear(),this.operationTimers.forEach(e=>{e.forEach(e=>clearTimeout(e)),e.clear()}),this.operationTimers.clear(),this.updateState({connected:!1,connecting:!1,sessionId:null,capabilities:null,error:null})},t.sendElicitationResponse=function(e){if(this.wsRef&&this.wsRef.readyState===WebSocket.OPEN)try{this.wsRef.send(JSON.stringify(e)),this.updateState({pendingElicitations:this.currentState.pendingElicitations.filter(t=>t.request_id!==e.request_id)})}catch(t){console.error("Failed to send elicitation response:",t)}else console.error("WebSocket not connected - cannot send elicitation response")},t.clearActivityMessages=function(){this.updateState({activityMessages:[]})},e}();const c=new l}}]);
//# sourceMappingURL=ac0c99200517696ae43f560effedf06400c2d888-08e87f5fce10b98590b3.js.map