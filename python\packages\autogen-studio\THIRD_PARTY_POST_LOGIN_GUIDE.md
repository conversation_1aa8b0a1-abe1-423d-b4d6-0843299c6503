# 第三方POST请求登录功能使用指南

## 功能概述

此功能允许第三方网站通过向AutoGen Studio根目录发送包含JWT token的POST请求，直接获取登录后的主页面，而无需经过重定向流程。

## 实现原理

1. **请求检测**: 系统检测POST请求的来源，判断是否为第三方直接请求
2. **Token验证**: 验证提交的JWT token的有效性
3. **页面注入**: 在主应用HTML中注入用户认证状态
4. **直接返回**: 返回包含认证状态的完整主应用页面

## 使用方法

### 1. HTML表单提交方式

```html
<form method="POST" action="http://your-autogen-studio.com/" target="_blank">
    <input type="hidden" name="token" value="your-jwt-token-here">
    <button type="submit">登录到AutoGen Studio</button>
</form>
```

### 2. JavaScript Fetch方式

```javascript
// 获取HTML页面
fetch('http://your-autogen-studio.com/', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
    },
    body: 'token=' + encodeURIComponent(yourToken)
})
.then(response => response.text())
.then(html => {
    // 在新窗口中显示HTML
    const newWindow = window.open('', '_blank');
    newWindow.document.write(html);
    newWindow.document.close();
});

// 获取JSON响应
fetch('http://your-autogen-studio.com/', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    },
    body: JSON.stringify({ token: yourToken })
})
.then(response => response.json())
.then(data => {
    if (data.status) {
        console.log('登录成功:', data.user);
        // 重定向到主应用
        window.location.href = data.redirect_url || '/';
    }
});
```

### 3. cURL命令行方式

```bash
# HTML响应
curl -X POST \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "Accept: text/html" \
  -d "token=your-jwt-token-here" \
  http://your-autogen-studio.com/

# JSON响应
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{"token":"your-jwt-token-here"}' \
  http://your-autogen-studio.com/
```

## 响应格式

### HTML响应

当请求头包含 `Accept: text/html` 时，系统返回完整的主应用HTML页面，其中包含：

- 完整的前端应用代码
- 注入的用户认证状态
- 自动的token存储逻辑

### JSON响应

当请求头包含 `Accept: application/json` 时，系统返回JSON格式的响应：

```json
{
    "status": true,
    "message": "Token login successful",
    "token": "your-jwt-token",
    "user": {
        "id": "<EMAIL>",
        "name": "User Name",
        "email": "<EMAIL>",
        "provider": "jwt",
        "roles": ["user"]
    },
    "redirect_url": "/"
}
```

## 认证状态注入

在HTML响应中，系统会注入以下JavaScript代码：

```javascript
// 存储token到localStorage
localStorage.setItem('auth_token', 'your-token');

// 注入认证状态
window.__AUTOGEN_AUTH_STATE__ = {
    token: 'your-token',
    user: {
        id: '<EMAIL>',
        name: 'User Name',
        email: '<EMAIL>',
        provider: 'jwt',
        roles: ['user']
    },
    isAuthenticated: true,
    loginMethod: 'token_post',
    injectedAt: '2024-01-01T00:00:00.000Z'
};

// 触发自定义事件
window.dispatchEvent(new CustomEvent('autogen-auth-injected', {
    detail: window.__AUTOGEN_AUTH_STATE__
}));
```

## 错误处理

### 无效Token

```json
{
    "status": false,
    "message": "Invalid token provided",
    "error": "Token authentication failed"
}
```

### Token过期

```json
{
    "status": false,
    "message": "Token has expired",
    "error": "Token authentication failed"
}
```

### 服务器错误

```json
{
    "status": false,
    "message": "Internal server error during token authentication",
    "error": "详细错误信息"
}
```

## 测试工具

### 1. HTML测试页面

打开 `test_third_party_post_login.html` 文件，可以进行交互式测试：

- 配置服务器地址和token
- 测试不同的请求场景
- 查看详细的测试结果

### 2. Python测试脚本

运行测试脚本进行自动化测试：

```bash
python test_post_login_functionality.py --server http://127.0.0.1:8081
```

可选参数：
- `--server`: 指定服务器地址
- `--token`: 指定自定义测试token

## 安全注意事项

1. **Token安全**: 确保JWT token的安全传输，建议使用HTTPS
2. **来源验证**: 系统会检查请求来源，防止恶意重定向
3. **Token验证**: 严格验证JWT token的签名和有效期
4. **XSS防护**: 用户数据在注入前会进行适当的转义处理

## 开发模式

在开发模式下（未配置JWT密钥时），系统会：

- 接受任何token值
- 使用默认的测试用户信息
- 记录警告日志

## 故障排除

### 问题1: 返回重定向页面而不是主应用

**原因**: 请求被识别为来自同源的弹窗或iframe

**解决**: 确保请求来自外部域名，或者没有referer头

### 问题2: 认证状态未注入

**原因**: 主应用HTML文件未找到或读取失败

**解决**: 检查UI文件路径配置，确保index.html存在

### 问题3: 前端未识别认证状态

**原因**: 前端认证上下文未正确处理注入的状态

**解决**: 检查前端代码是否包含最新的认证状态检测逻辑

## 更新日志

- **v1.0**: 初始实现，支持基本的POST请求登录
- **v1.1**: 添加认证状态注入和前端集成
- **v1.2**: 改进错误处理和安全性检查
