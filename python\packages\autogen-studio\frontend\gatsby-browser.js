import "antd/dist/reset.css";
import "./src/styles/global.css";

import AuthProvider from "./src/hooks/provider";

// Configure Monaco Editor environment for client-side
if (typeof window !== 'undefined') {
  window.MonacoEnvironment = {
    getWorkerUrl: function (moduleId, label) {
      if (label === 'json') {
        return '/monaco-editor/vs/language/json/jsonWorker.js';
      }
      if (label === 'css' || label === 'scss' || label === 'less') {
        return '/monaco-editor/vs/language/css/cssWorker.js';
      }
      if (label === 'html' || label === 'handlebars' || label === 'razor') {
        return '/monaco-editor/vs/language/html/htmlWorker.js';
      }
      if (label === 'typescript' || label === 'javascript') {
        return '/monaco-editor/vs/language/typescript/tsWorker.js';
      }
      return '/monaco-editor/vs/base/worker/workerMain.js';
    }
  };
}

export const wrapRootElement = AuthProvider;
