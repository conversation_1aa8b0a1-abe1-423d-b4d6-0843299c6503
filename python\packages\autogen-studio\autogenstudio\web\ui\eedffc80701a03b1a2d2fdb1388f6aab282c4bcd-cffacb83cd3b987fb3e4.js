"use strict";(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[658],{7072:function(e,t,a){a.d(t,{A:function(){return z}});var s=a(6540),n=a(6942),i=a.n(n),l=a(2279),r=a(9853);var o=e=>{const{prefixCls:t,className:a,style:n,size:l,shape:r}=e,o=i()({[`${t}-lg`]:"large"===l,[`${t}-sm`]:"small"===l}),c=i()({[`${t}-circle`]:"circle"===r,[`${t}-square`]:"square"===r,[`${t}-round`]:"round"===r}),g=s.useMemo(()=>"number"==typeof l?{width:l,height:l,lineHeight:`${l}px`}:{},[l]);return s.createElement("span",{className:i()(t,o,c,a),style:Object.assign(Object.assign({},g),n)})},c=a(2187),g=a(7358),d=a(4277);const u=new c.Mo("ant-skeleton-loading",{"0%":{backgroundPosition:"100% 50%"},"100%":{backgroundPosition:"0 50%"}}),m=e=>({height:e,lineHeight:(0,c.zA)(e)}),b=e=>Object.assign({width:e},m(e)),h=e=>({background:e.skeletonLoadingBackground,backgroundSize:"400% 100%",animationName:u,animationDuration:e.skeletonLoadingMotionDuration,animationTimingFunction:"ease",animationIterationCount:"infinite"}),$=(e,t)=>Object.assign({width:t(e).mul(5).equal(),minWidth:t(e).mul(5).equal()},m(e)),p=e=>{const{skeletonAvatarCls:t,gradientFromColor:a,controlHeight:s,controlHeightLG:n,controlHeightSM:i}=e;return{[t]:Object.assign({display:"inline-block",verticalAlign:"top",background:a},b(s)),[`${t}${t}-circle`]:{borderRadius:"50%"},[`${t}${t}-lg`]:Object.assign({},b(n)),[`${t}${t}-sm`]:Object.assign({},b(i))}},C=e=>{const{controlHeight:t,borderRadiusSM:a,skeletonInputCls:s,controlHeightLG:n,controlHeightSM:i,gradientFromColor:l,calc:r}=e;return{[s]:Object.assign({display:"inline-block",verticalAlign:"top",background:l,borderRadius:a},$(t,r)),[`${s}-lg`]:Object.assign({},$(n,r)),[`${s}-sm`]:Object.assign({},$(i,r))}},k=e=>Object.assign({width:e},m(e)),v=e=>{const{skeletonImageCls:t,imageSizeBase:a,gradientFromColor:s,borderRadiusSM:n,calc:i}=e;return{[t]:Object.assign(Object.assign({display:"inline-flex",alignItems:"center",justifyContent:"center",verticalAlign:"middle",background:s,borderRadius:n},k(i(a).mul(2).equal())),{[`${t}-path`]:{fill:"#bfbfbf"},[`${t}-svg`]:Object.assign(Object.assign({},k(a)),{maxWidth:i(a).mul(4).equal(),maxHeight:i(a).mul(4).equal()}),[`${t}-svg${t}-svg-circle`]:{borderRadius:"50%"}}),[`${t}${t}-circle`]:{borderRadius:"50%"}}},O=(e,t,a)=>{const{skeletonButtonCls:s}=e;return{[`${a}${s}-circle`]:{width:t,minWidth:t,borderRadius:"50%"},[`${a}${s}-round`]:{borderRadius:t}}},f=(e,t)=>Object.assign({width:t(e).mul(2).equal(),minWidth:t(e).mul(2).equal()},m(e)),j=e=>{const{borderRadiusSM:t,skeletonButtonCls:a,controlHeight:s,controlHeightLG:n,controlHeightSM:i,gradientFromColor:l,calc:r}=e;return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({[a]:Object.assign({display:"inline-block",verticalAlign:"top",background:l,borderRadius:t,width:r(s).mul(2).equal(),minWidth:r(s).mul(2).equal()},f(s,r))},O(e,s,a)),{[`${a}-lg`]:Object.assign({},f(n,r))}),O(e,n,`${a}-lg`)),{[`${a}-sm`]:Object.assign({},f(i,r))}),O(e,i,`${a}-sm`))},w=e=>{const{componentCls:t,skeletonAvatarCls:a,skeletonTitleCls:s,skeletonParagraphCls:n,skeletonButtonCls:i,skeletonInputCls:l,skeletonImageCls:r,controlHeight:o,controlHeightLG:c,controlHeightSM:g,gradientFromColor:d,padding:u,marginSM:m,borderRadius:$,titleHeight:k,blockRadius:O,paragraphLiHeight:f,controlHeightXS:w,paragraphMarginTop:x}=e;return{[t]:{display:"table",width:"100%",[`${t}-header`]:{display:"table-cell",paddingInlineEnd:u,verticalAlign:"top",[a]:Object.assign({display:"inline-block",verticalAlign:"top",background:d},b(o)),[`${a}-circle`]:{borderRadius:"50%"},[`${a}-lg`]:Object.assign({},b(c)),[`${a}-sm`]:Object.assign({},b(g))},[`${t}-content`]:{display:"table-cell",width:"100%",verticalAlign:"top",[s]:{width:"100%",height:k,background:d,borderRadius:O,[`+ ${n}`]:{marginBlockStart:g}},[n]:{padding:0,"> li":{width:"100%",height:f,listStyle:"none",background:d,borderRadius:O,"+ li":{marginBlockStart:w}}},[`${n}> li:last-child:not(:first-child):not(:nth-child(2))`]:{width:"61%"}},[`&-round ${t}-content`]:{[`${s}, ${n} > li`]:{borderRadius:$}}},[`${t}-with-avatar ${t}-content`]:{[s]:{marginBlockStart:m,[`+ ${n}`]:{marginBlockStart:x}}},[`${t}${t}-element`]:Object.assign(Object.assign(Object.assign(Object.assign({display:"inline-block",width:"auto"},j(e)),p(e)),C(e)),v(e)),[`${t}${t}-block`]:{width:"100%",[i]:{width:"100%"},[l]:{width:"100%"}},[`${t}${t}-active`]:{[`\n        ${s},\n        ${n} > li,\n        ${a},\n        ${i},\n        ${l},\n        ${r}\n      `]:Object.assign({},h(e))}}};var x=(0,g.OF)("Skeleton",e=>{const{componentCls:t,calc:a}=e,s=(0,d.oX)(e,{skeletonAvatarCls:`${t}-avatar`,skeletonTitleCls:`${t}-title`,skeletonParagraphCls:`${t}-paragraph`,skeletonButtonCls:`${t}-button`,skeletonInputCls:`${t}-input`,skeletonImageCls:`${t}-image`,imageSizeBase:a(e.controlHeight).mul(1.5).equal(),borderRadius:100,skeletonLoadingBackground:`linear-gradient(90deg, ${e.gradientFromColor} 25%, ${e.gradientToColor} 37%, ${e.gradientFromColor} 63%)`,skeletonLoadingMotionDuration:"1.4s"});return[w(s)]},e=>{const{colorFillContent:t,colorFill:a}=e;return{color:t,colorGradientEnd:a,gradientFromColor:t,gradientToColor:a,titleHeight:e.controlHeight/2,blockRadius:e.borderRadiusSM,paragraphMarginTop:e.marginLG+e.marginXXS,paragraphLiHeight:e.controlHeight/2}},{deprecatedTokens:[["color","gradientFromColor"],["colorGradientEnd","gradientToColor"]]});var N=e=>{const{prefixCls:t,className:a,rootClassName:n,active:c,shape:g="circle",size:d="default"}=e,{getPrefixCls:u}=s.useContext(l.QO),m=u("skeleton",t),[b,h,$]=x(m),p=(0,r.A)(e,["prefixCls","className"]),C=i()(m,`${m}-element`,{[`${m}-active`]:c},a,n,h,$);return b(s.createElement("div",{className:C},s.createElement(o,Object.assign({prefixCls:`${m}-avatar`,shape:g,size:d},p))))};var y=e=>{const{prefixCls:t,className:a,rootClassName:n,style:r,active:o}=e,{getPrefixCls:c}=s.useContext(l.QO),g=c("skeleton",t),[d,u,m]=x(g),b=i()(g,`${g}-element`,{[`${g}-active`]:o},a,n,u,m);return d(s.createElement("div",{className:b},s.createElement("div",{className:i()(`${g}-image`,a),style:r},s.createElement("svg",{viewBox:"0 0 1098 1024",xmlns:"http://www.w3.org/2000/svg",className:`${g}-image-svg`},s.createElement("title",null,"Image placeholder"),s.createElement("path",{d:"M365.714286 329.142857q0 45.714286-32.036571 77.677714t-77.677714 32.036571-77.677714-32.036571-32.036571-77.677714 32.036571-77.677714 77.677714-32.036571 77.677714 32.036571 32.036571 77.677714zM950.857143 548.571429l0 256-804.571429 0 0-109.714286 182.857143-182.857143 91.428571 91.428571 292.571429-292.571429zM1005.714286 146.285714l-914.285714 0q-7.460571 0-12.873143 5.412571t-5.412571 12.873143l0 694.857143q0 7.460571 5.412571 12.873143t12.873143 5.412571l914.285714 0q7.460571 0 12.873143-5.412571t5.412571-12.873143l0-694.857143q0-7.460571-5.412571-12.873143t-12.873143-5.412571zM1097.142857 164.571429l0 694.857143q0 37.741714-26.843429 64.585143t-64.585143 26.843429l-914.285714 0q-37.741714 0-64.585143-26.843429t-26.843429-64.585143l0-694.857143q0-37.741714 26.843429-64.585143t64.585143-26.843429l914.285714 0q37.741714 0 64.585143 26.843429t26.843429 64.585143z",className:`${g}-image-path`})))))};var E=e=>{const{prefixCls:t,className:a,rootClassName:n,active:c,block:g,size:d="default"}=e,{getPrefixCls:u}=s.useContext(l.QO),m=u("skeleton",t),[b,h,$]=x(m),p=(0,r.A)(e,["prefixCls"]),C=i()(m,`${m}-element`,{[`${m}-active`]:c,[`${m}-block`]:g},a,n,h,$);return b(s.createElement("div",{className:C},s.createElement(o,Object.assign({prefixCls:`${m}-input`,size:d},p))))};var H=e=>{const{prefixCls:t,className:a,rootClassName:n,style:r,active:o,children:c}=e,{getPrefixCls:g}=s.useContext(l.QO),d=g("skeleton",t),[u,m,b]=x(d),h=i()(d,`${d}-element`,{[`${d}-active`]:o},m,a,n,b);return u(s.createElement("div",{className:h},s.createElement("div",{className:i()(`${d}-image`,a),style:r},c)))};const q=(e,t)=>{const{width:a,rows:s=2}=t;return Array.isArray(a)?a[e]:s-1===e?a:void 0};var R=e=>{const{prefixCls:t,className:a,style:n,rows:l=0}=e,r=Array.from({length:l}).map((t,a)=>s.createElement("li",{key:a,style:{width:q(a,e)}}));return s.createElement("ul",{className:i()(t,a),style:n},r)};var S=({prefixCls:e,className:t,width:a,style:n})=>s.createElement("h3",{className:i()(e,t),style:Object.assign({width:a},n)});function A(e){return e&&"object"==typeof e?e:{}}const M=e=>{const{prefixCls:t,loading:a,className:n,rootClassName:r,style:c,children:g,avatar:d=!1,title:u=!0,paragraph:m=!0,active:b,round:h}=e,{getPrefixCls:$,direction:p,className:C,style:k}=(0,l.TP)("skeleton"),v=$("skeleton",t),[O,f,j]=x(v);if(a||!("loading"in e)){const e=!!d,t=!!u,a=!!m;let l,g;if(e){const e=Object.assign(Object.assign({prefixCls:`${v}-avatar`},function(e,t){return e&&!t?{size:"large",shape:"square"}:{size:"large",shape:"circle"}}(t,a)),A(d));l=s.createElement("div",{className:`${v}-header`},s.createElement(o,Object.assign({},e)))}if(t||a){let n,i;if(t){const t=Object.assign(Object.assign({prefixCls:`${v}-title`},function(e,t){return!e&&t?{width:"38%"}:e&&t?{width:"50%"}:{}}(e,a)),A(u));n=s.createElement(S,Object.assign({},t))}if(a){const a=Object.assign(Object.assign({prefixCls:`${v}-paragraph`},function(e,t){const a={};return e&&t||(a.width="61%"),a.rows=!e&&t?3:2,a}(e,t)),A(m));i=s.createElement(R,Object.assign({},a))}g=s.createElement("div",{className:`${v}-content`},n,i)}const $=i()(v,{[`${v}-with-avatar`]:e,[`${v}-active`]:b,[`${v}-rtl`]:"rtl"===p,[`${v}-round`]:h},C,n,r,f,j);return O(s.createElement("div",{className:$,style:Object.assign(Object.assign({},k),c)},l,g))}return null!=g?g:null};M.Button=e=>{const{prefixCls:t,className:a,rootClassName:n,active:c,block:g=!1,size:d="default"}=e,{getPrefixCls:u}=s.useContext(l.QO),m=u("skeleton",t),[b,h,$]=x(m),p=(0,r.A)(e,["prefixCls"]),C=i()(m,`${m}-element`,{[`${m}-active`]:c,[`${m}-block`]:g},a,n,h,$);return b(s.createElement("div",{className:C},s.createElement(o,Object.assign({prefixCls:`${m}-button`,size:d},p))))},M.Avatar=N,M.Input=E,M.Image=y,M.Node=H;var z=M}}]);
//# sourceMappingURL=eedffc80701a03b1a2d2fdb1388f6aab282c4bcd-cffacb83cd3b987fb3e4.js.map