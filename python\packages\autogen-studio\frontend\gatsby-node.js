const path = require('path');

exports.onCreateWebpackConfig = ({ actions, stage, getConfig }) => {
  // Configure Monaco Editor to use local files
  if (stage === 'build-javascript' || stage === 'develop') {
    try {
      const MonacoWebpackPlugin = require('monaco-editor-webpack-plugin');

      actions.setWebpackConfig({
        plugins: [
          new MonacoWebpackPlugin({
            // Available languages
            languages: ['json', 'javascript', 'typescript', 'python', 'yaml', 'markdown'],
            // Available features
            features: [
              'accessibilityHelp',
              'bracketMatching',
              'caretOperations',
              'clipboard',
              'codeAction',
              'codelens',
              'colorPicker',
              'comment',
              'contextmenu',
              'coreCommands',
              'cursorUndo',
              'dnd',
              'find',
              'folding',
              'fontZoom',
              'format',
              'gotoError',
              'gotoLine',
              'gotoSymbol',
              'hover',
              'iPadShowKeyboard',
              'inPlaceReplace',
              'indentation',
              'inlineHints',
              'inspectTokens',
              'linesOperations',
              'linkedEditing',
              'links',
              'multicursor',
              'parameterHints',
              'quickCommand',
              'quickHelp',
              'quickOutline',
              'referenceSearch',
              'rename',
              'smartSelect',
              'snippets',
              'suggest',
              'toggleHighContrast',
              'toggleTabFocusMode',
              'transpose',
              'unusualLineTerminators',
              'viewportSemanticTokens',
              'wordHighlighter',
              'wordOperations',
              'wordPartOperations'
            ]
          })
        ],
        resolve: {
          alias: {
            // Ensure Monaco Editor uses local files
            'monaco-editor': path.resolve(__dirname, 'node_modules/monaco-editor')
          }
        }
      });
    } catch (error) {
      console.warn('Monaco Editor webpack plugin not available:', error.message);
    }
  }
};
