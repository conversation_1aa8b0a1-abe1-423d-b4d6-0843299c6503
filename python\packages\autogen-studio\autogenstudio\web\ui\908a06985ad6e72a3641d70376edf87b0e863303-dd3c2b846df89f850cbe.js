"use strict";(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[698],{588:function(e,t,a){a.d(t,{C:function(){return z}});var n=a(436),s=a(6540),l=a(9036),o=a(5625),r=a(2744),i=a(9850),c=a(3618),m=a(6108),d=a(9957),u=a(9314),f=a(4716),p=a(2941),E=a(418),g=a(4810);const y=e=>{let{session:t,onSave:a,onCancel:n,isOpen:o,teams:r}=e;const[i]=c.A.useForm(),{0:y,1:v}=(0,s.useState)(!1),[h,w]=l.Ay.useMessage();(0,s.useEffect)(()=>{o?i.setFieldsValue({name:(null==t?void 0:t.name)||"",team_id:(null==t?void 0:t.team_id)||void 0}):i.resetFields()},[i,t,o]);const x=!y&&0===r.length;return s.createElement(m.A,{title:t?"Edit Session":"Create Session",open:o,onCancel:n,footer:null,className:"text-primary",forceRender:!0},w,s.createElement(c.A,{form:i,name:"session-form",layout:"vertical",onFinish:async e=>{try{await a({...e,id:null==t?void 0:t.id}),h.success(`Session ${t?"updated":"created"} successfully`)}catch(n){n instanceof Error&&h.error(n.message)}},onFinishFailed:e=>{h.error("Please check the form for errors"),console.error("Form validation failed:",e)},autoComplete:"off"},s.createElement(c.A.Item,{label:"Session Name",name:"name",rules:[{required:!0,message:"Please enter a session name"},{max:100,message:"Session name cannot exceed 100 characters"}]},s.createElement(d.A,null)),s.createElement("div",{className:"space-y-2   w-full"},s.createElement(c.A.Item,{className:"w-full",label:"Team",name:"team_id",rules:[{required:!0,message:"Please select a team"}]},s.createElement(u.A,{placeholder:"Select a team",loading:y,disabled:y||x,showSearch:!0,optionFilterProp:"children",filterOption:(e,t)=>{var a;return(null!==(a=null==t?void 0:t.label)&&void 0!==a?a:"").toLowerCase().includes(e.toLowerCase())},options:r.map(e=>({value:e.id,label:`${e.component.label} (${e.component.component_type})`})),notFoundContent:y?s.createElement(f.A,{size:"small"}):null}))),s.createElement("div",{className:"text-sm text-accent "},s.createElement(g.Link,{to:"/build"},"view all teams")),x&&s.createElement("div",{className:"flex border p-1 rounded -mt-2 mb-4 items-center gap-1.5 text-sm text-yellow-600"},s.createElement(E.A,{className:"h-4 w-4"}),s.createElement("span",null,"No teams found. Please create a team first.")),s.createElement(c.A.Item,{className:"flex justify-end mb-0"},s.createElement("div",{className:"flex gap-2"},s.createElement(p.Ay,{onClick:n},"Cancel"),s.createElement(p.Ay,{type:"primary",htmlType:"submit",disabled:x},t?"Update":"Create")))))};var v=a(6427),h=a(367),w=a(9910),x=a(697),S=a(9644),N=a(85),b=a(4060),A=a(7213),C=a(8188),k=a(2708),L=a(7163),I=a(8603),G=a(7015),j=a(2640),P=a(5107),F=a(180),T=a(8017);var O=e=>{let{teams:t,isLoading:a,onStartSession:l}=e;const{0:o,1:r}=(0,s.useState)(),{0:i,1:c}=(0,s.useState)(()=>{if("undefined"!=typeof window){const e=localStorage.getItem("lastUsedTeamId");return e?parseInt(e):void 0}}),{0:m,1:d}=(0,s.useState)(""),u=t.filter(e=>{var t,a;return(null===(t=e.component.label)||void 0===t?void 0:t.toLowerCase().includes(m.toLowerCase()))||(null===(a=e.component.description)||void 0===a?void 0:a.toLowerCase().includes(m.toLowerCase()))});(0,s.useEffect)(()=>{i&&t.some(e=>e.id===i)?r(i):t.length>0&&r(t[0].id)},[t,i]);const f=!a&&0===t.length,p={items:[{type:"group",label:s.createElement("div",null,s.createElement("div",{className:"text-xs text-secondary mb-1"},"选择团队"),s.createElement(T.A,{prefix:s.createElement(G.A,{className:"w-4 h-4"}),placeholder:"搜索团队",onChange:e=>d(e.target.value)})),key:"from-team"},{type:"divider"}].concat((0,n.A)(u.map(e=>{var t;return{label:s.createElement("div",null,s.createElement("div",null,(0,F.EJ)(e.component.label||"",20)),s.createElement("div",{className:"text-xs text-secondary"},e.component.component_type)),key:(null==e||null===(t=e.id)||void 0===t?void 0:t.toString())||"",icon:s.createElement(j.A,{className:"w-4 h-4"})}}))),onClick:async e=>{const a=parseInt(e.key);t.find(e=>e.id===a)?r(a):console.error("Selected team not found:",a)}},E=t.find(e=>e.id===o);return s.createElement("div",{className:"space-y-2 w-full"},s.createElement(I.A.Button,{menu:p,type:"primary",className:"w-full",placement:"bottomRight",icon:s.createElement(P.A,{className:"w-4 h-4"}),onClick:async()=>{if(!o)return;"undefined"!=typeof window&&localStorage.setItem("lastUsedTeamId",o.toString());const e=t.find(e=>e.id===o);e&&(await new Promise(e=>setTimeout(e,100)),l(o,e.component.label||""))},disabled:!o||a},s.createElement("div",{className:"",style:{width:"183px"}},s.createElement(x.A,{className:"w-4 h-4 inline-block -mt-1"})," 新建会话")),s.createElement("div",{className:"text-xs text-secondary",title:null==E?void 0:E.component.label},(0,F.EJ)((null==E?void 0:E.component.label)||"",30)),f&&s.createElement("div",{className:"flex items-center gap-1.5 text-xs text-yellow-600 mt-1"},s.createElement(A.A,{className:"h-3 w-3"}),s.createElement("span",null,"创建团队以开始使用")))};const _=e=>{let{isOpen:t,sessions:a,currentSession:n,onToggle:l,onSelectSession:o,onEditSession:r,onDeleteSession:i,isLoading:c=!1,onStartSession:m,teams:d}=e;return t?s.createElement("div",{className:"h-full border-r border-secondary "},s.createElement("div",{className:"flex items-center justify-between pt-0 p-4 pl-2 pr-2 border-b border-secondary"},s.createElement("div",{className:"flex items-center gap-2"},s.createElement("span",{className:"text-primary font-medium"},"会话"),s.createElement("span",{className:"px-2 py-0.5 text-xs bg-accent/10 text-accent rounded"},a.length)),s.createElement(h.A,{title:"Close Sidebar"},s.createElement("button",{onClick:l,className:"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50"},s.createElement(S.A,{strokeWidth:1.5,className:"h-6 w-6"})))),s.createElement("div",{className:"my-4 flex text-sm  "},s.createElement("div",{className:" mr-2 w-full pr-2"},t&&s.createElement(O,{teams:d,isLoading:c,onStartSession:m}))),s.createElement("div",{className:"py-2 flex text-sm text-secondary"},s.createElement(N.A,{className:"w-4 h-4 inline-block mr-1.5"}),s.createElement("div",{className:"inline-block -mt-0.5"},"最近"," ",s.createElement("span",{className:"text-accent text-xs mx-1 mt-0.5"}," ","(",a.length,")"," ")," "),c&&s.createElement(b.A,{className:"w-4 h-4 inline-block ml-2 animate-spin"})),!c&&0===a.length&&s.createElement("div",{className:"p-2 mr-2 text-center text-secondary text-sm border border-dashed rounded "},s.createElement(A.A,{className:"w-4 h-4 inline-block mr-1.5 -mt-0.5"}),"未找到最近的会话"),s.createElement("div",{className:"overflow-y-auto   scroll   h-[calc(100%-181px)]"},a.map(e=>s.createElement("div",{key:e.id,className:"relative"},s.createElement("div",{className:"bg-accent absolute top-1 left-0.5 z-50 h-[calc(100%-8px)]\n               w-1 bg-opacity-80  rounded "+((null==n?void 0:n.id)===e.id?"bg-accent":"bg-tertiary")}," "),s.createElement("div",{className:"group ml-1 flex items-center justify-between rounded-l p-2 py-1 text-sm cursor-pointer hover:bg-tertiary "+((null==n?void 0:n.id)===e.id?"border-accent bg-secondary":""),onClick:()=>o(e)},s.createElement("div",{className:"flex flex-col min-w-0 flex-1 mr-2"},s.createElement("div",{className:"truncate text-sm"},e.name),s.createElement("span",{className:"truncate text-xs text-secondary"},(0,L.vq)(e.updated_at||""))),s.createElement("div",{className:"py-3 flex gap-1 shrink-0 opacity-0 group-hover:opacity-100 transition-opacity"},s.createElement(h.A,{title:"Edit session"},s.createElement(p.Ay,{type:"text",size:"small",className:"p-1 min-w-[24px] h-6",icon:s.createElement(C.A,{className:"w-4 h-4"}),onClick:t=>{t.stopPropagation(),r(e)}})),s.createElement(h.A,{title:"Delete session"},s.createElement(p.Ay,{type:"text",size:"small",className:"p-1 min-w-[24px] h-6",danger:!0,icon:s.createElement(k.A,{className:"w-4 h-4 text-red-500"}),onClick:t=>{t.stopPropagation(),e.id&&i(e.id)}})))))))):s.createElement("div",{className:"h-full  border-r border-secondary"},s.createElement("div",{className:"p-2 -ml-2 "},s.createElement(h.A,{title:s.createElement("span",null,"会话"," ",s.createElement("span",{className:"text-accent mx-1"}," ",a.length," ")," ")},s.createElement("button",{onClick:l,className:"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50"},s.createElement(w.A,{strokeWidth:1.5,className:"h-6 w-6"})))),s.createElement("div",{className:"mt-4 px-2 -ml-1"},s.createElement(h.A,{title:"创建新会话"},s.createElement(p.Ay,{type:"text",className:"w-full p-2 flex justify-center",onClick:()=>r(),icon:s.createElement(x.A,{className:"w-4 h-4"})}))))};var D=a(2197),R=a(1511),U=a(2571);const $=(0,R.v)((e,t)=>({galleries:[],selectedGallery:null,isLoading:!1,error:null,fetchGalleries:async a=>{try{e({isLoading:!0,error:null});const n=await U.f.listGalleries(a);e({galleries:n,selectedGallery:t().selectedGallery||n[0]||null,isLoading:!1})}catch(n){e({error:n instanceof Error?n.message:"Failed to fetch galleries",isLoading:!1})}},selectGallery:t=>{e({selectedGallery:t})},getSelectedGallery:()=>t().selectedGallery})),z=()=>{const{0:e,1:t}=(0,s.useState)([]),{0:a,1:c}=(0,s.useState)(!1),{0:m,1:d}=(0,s.useState)(!1),{0:u,1:f}=(0,s.useState)(),{0:p,1:E}=(0,s.useState)(()=>{if("undefined"!=typeof window){const e=localStorage.getItem("sessionSidebar");return null===e||JSON.parse(e)}return!0}),[g,h]=l.Ay.useMessage(),{user:w}=(0,s.useContext)(r.v),{session:x,setSession:S,sessions:N,setSessions:b}=(0,o.J)(),{0:A,1:C}=(0,s.useState)(!1),{0:k,1:L}=(0,s.useState)(null),I=$();(0,s.useEffect)(()=>{"undefined"!=typeof window&&localStorage.setItem("sessionSidebar",JSON.stringify(p))},[p]);const G=(0,s.useCallback)(async()=>{if(null!=w&&w.id)try{c(!0);const e=await i.j.listSessions(w.id);b(e);const t=new URLSearchParams(window.location.search).get("sessionId");!x&&e.length>0&&!t&&S(e[0])}catch(e){console.error("Error fetching sessions:",e),g.error("Error loading sessions")}finally{c(!1)}},[null==w?void 0:w.id,b,x,S]);(0,s.useEffect)(()=>{const e=new URLSearchParams(window.location.search).get("sessionId");e&&!x&&j({id:parseInt(e)})},[]),(0,s.useEffect)(()=>{const e=()=>{!new URLSearchParams(window.location.search).get("sessionId")&&x&&S(null)};return window.addEventListener("popstate",e),()=>window.removeEventListener("popstate",e)},[x]);const j=async e=>{if(null!=w&&w.id&&e.id)try{c(!0);const t=await i.j.getSession(e.id,w.id);if(!t)return g.error("Session not found"),window.history.pushState({},"",window.location.pathname),void(N.length>0?S(N[0]):S(null));S(t),window.history.pushState({},"",`?sessionId=${e.id}`)}catch(t){console.error("Error loading session:",t),g.error("Error loading session")}finally{c(!1)}};(0,s.useEffect)(()=>{G()},[G]);const P=(0,s.useCallback)(async()=>{if(null!=w&&w.id)try{c(!0);const e=await D.CG.listTeams(w.id);if(e.length>0)t(e);else{console.log("No teams found, creating default team"),await I.fetchGalleries(w.id);const e=I.getSelectedGallery(),a=null==e?void 0:e.config.components.teams[0];if(console.log("Default Gallery .. manager fetching ",a),a){const e={component:a},n=await D.CG.createTeam(e,w.id);console.log("Default team created:",e),t([n])}}}catch(e){console.error("Error fetching teams:",e),g.error("Error loading teams")}finally{c(!1)}},[null==w?void 0:w.id,g]);return(0,s.useEffect)(()=>{P()},[P]),s.createElement("div",{className:"relative flex h-full w-full"},h,s.createElement("div",{className:"absolute left-0 top-0 h-full transition-all duration-200 ease-in-out "+(p?"w-64":"w-12")},s.createElement(_,{isOpen:p,teams:e,onStartSession:async(e,t)=>{if(null!=w&&w.id)try{const a=`${t.substring(0,20)} - ${(new Date).toLocaleString()} Session`,s=await i.j.createSession({name:a,team_id:e},w.id);b([s].concat((0,n.A)(N))),S(s),g.success("Session created!")}catch(a){g.error("Error creating session")}},sessions:N,currentSession:x,onToggle:()=>E(!p),onSelectSession:j,onEditSession:e=>{f(e),d(!0)},onDeleteSession:async e=>{if(null!=w&&w.id)try{await i.j.deleteSession(e,w.id);b(N.filter(t=>t.id!==e)),(null==x?void 0:x.id)!==e&&0!==N.length||(S(N[0]||null),window.history.pushState({},"",window.location.pathname)),g.success("Session deleted")}catch(t){console.error("Error deleting session:",t),g.error("Error deleting session")}},isLoading:a})),s.createElement("div",{className:"flex-1 transition-all duration-200 "+(p?"ml-64":"ml-12")},x&&N.length>0?s.createElement("div",{className:"flex gap-4 pl-4"},s.createElement("div",{className:"flex-1 "+(A?"w-1/2":"w-full")},s.createElement(v.A,{session:x,isCompareMode:A,onCompareClick:()=>{if(N.length>1){const e=N.find(e=>e.id!==(null==x?void 0:x.id));L(e||x)}else L(x);C(!0)},onSessionChange:j,availableSessions:N})),A&&s.createElement("div",{className:"flex-1 w-1/2 border-l border-secondary/20 pl-4"},s.createElement(v.A,{session:k,isCompareMode:!0,isSecondaryView:!0,onExitCompare:()=>{C(!1),L(null)},onSessionChange:L,availableSessions:N}))):s.createElement("div",{className:"flex items-center justify-center h-full text-secondary"},'"未选择会话。请从侧边栏创建或选择一个会话。"')),s.createElement(y,{teams:e,session:u,isOpen:m,onSave:async e=>{if(null!=w&&w.id)try{if(e.id){const t=await i.j.updateSession(e.id,e,w.id);b(N.map(e=>e.id===t.id?t:e)),(null==x?void 0:x.id)===t.id&&S(t)}else{const t=await i.j.createSession(e,w.id);b([t].concat((0,n.A)(N))),S(t)}d(!1),f(void 0)}catch(t){g.error("Error saving session"),console.error(t)}},onCancel:()=>{d(!1),f(void 0)}}))}},6067:function(e,t,a){a.d(t,{A:function(){return i}});var n=a(8168),s=a(6540),l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"}}]},name:"check",theme:"outlined"},o=a(7064),r=function(e,t){return s.createElement(o.A,(0,n.A)({},e,{ref:t,icon:l}))};var i=s.forwardRef(r)}}]);
//# sourceMappingURL=908a06985ad6e72a3641d70376edf87b0e863303-dd3c2b846df89f850cbe.js.map