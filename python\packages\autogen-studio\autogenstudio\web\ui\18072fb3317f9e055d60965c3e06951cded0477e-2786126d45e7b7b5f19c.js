/*! For license information please see 18072fb3317f9e055d60965c3e06951cded0477e-2786126d45e7b7b5f19c.js.LICENSE.txt */
"use strict";(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[396],{367:function(e,t,n){n.d(t,{A:function(){return W}});var r=n(6540),o=n(6942),i=n.n(o);function a(e){var t=e.children,n=e.prefixCls,o=e.id,a=e.overlayInnerStyle,l=e.bodyClassName,s=e.className,u=e.style;return r.createElement("div",{className:i()("".concat(n,"-content"),s),style:u},r.createElement("div",{className:i()("".concat(n,"-inner"),l),id:o,role:"tooltip",style:a},"function"==typeof t?t():t))}var l=n(8168),s=n(9379),u=n(3986),c=n(2427),d={shiftX:64,adjustY:1},f={adjustX:1,shiftY:!0},m=[0,0],p={left:{points:["cr","cl"],overflow:f,offset:[-4,0],targetOffset:m},right:{points:["cl","cr"],overflow:f,offset:[4,0],targetOffset:m},top:{points:["bc","tc"],overflow:d,offset:[0,-4],targetOffset:m},bottom:{points:["tc","bc"],overflow:d,offset:[0,4],targetOffset:m},topLeft:{points:["bl","tl"],overflow:d,offset:[0,-4],targetOffset:m},leftTop:{points:["tr","tl"],overflow:f,offset:[-4,0],targetOffset:m},topRight:{points:["br","tr"],overflow:d,offset:[0,-4],targetOffset:m},rightTop:{points:["tl","tr"],overflow:f,offset:[4,0],targetOffset:m},bottomRight:{points:["tr","br"],overflow:d,offset:[0,4],targetOffset:m},rightBottom:{points:["bl","br"],overflow:f,offset:[4,0],targetOffset:m},bottomLeft:{points:["tl","bl"],overflow:d,offset:[0,4],targetOffset:m},leftBottom:{points:["br","bl"],overflow:f,offset:[-4,0],targetOffset:m}},h=n(6855),v=["overlayClassName","trigger","mouseEnterDelay","mouseLeaveDelay","overlayStyle","prefixCls","children","onVisibleChange","afterVisibleChange","transitionName","animation","motion","placement","align","destroyTooltipOnHide","defaultVisible","getTooltipContainer","overlayInnerStyle","arrowContent","overlay","id","showArrow","classNames","styles"],g=function(e,t){var n=e.overlayClassName,o=e.trigger,d=void 0===o?["hover"]:o,f=e.mouseEnterDelay,m=void 0===f?0:f,g=e.mouseLeaveDelay,y=void 0===g?.1:g,b=e.overlayStyle,w=e.prefixCls,C=void 0===w?"rc-tooltip":w,E=e.children,A=e.onVisibleChange,x=e.afterVisibleChange,k=e.transitionName,F=e.animation,S=e.motion,P=e.placement,O=void 0===P?"right":P,M=e.align,T=void 0===M?{}:M,N=e.destroyTooltipOnHide,R=void 0!==N&&N,L=e.defaultVisible,I=e.getTooltipContainer,$=e.overlayInnerStyle,V=(e.arrowContent,e.overlay),D=e.id,j=e.showArrow,_=void 0===j||j,H=e.classNames,z=e.styles,B=(0,u.A)(e,v),q=(0,h.A)(D),W=(0,r.useRef)(null);(0,r.useImperativeHandle)(t,function(){return W.current});var U=(0,s.A)({},B);"visible"in e&&(U.popupVisible=e.visible);var X,Z,G;return r.createElement(c.A,(0,l.A)({popupClassName:i()(n,null==H?void 0:H.root),prefixCls:C,popup:function(){return r.createElement(a,{key:"content",prefixCls:C,id:q,bodyClassName:null==H?void 0:H.body,overlayInnerStyle:(0,s.A)((0,s.A)({},$),null==z?void 0:z.body)},V)},action:d,builtinPlacements:p,popupPlacement:O,ref:W,popupAlign:T,getPopupContainer:I,onPopupVisibleChange:A,afterPopupVisibleChange:x,popupTransitionName:k,popupAnimation:F,popupMotion:S,defaultPopupVisible:L,autoDestroy:R,mouseLeaveDelay:y,popupStyle:(0,s.A)((0,s.A)({},b),null==z?void 0:z.root),mouseEnterDelay:m,arrow:_},U),(X=r.Children.only(E),Z=(null==X?void 0:X.props)||{},G=(0,s.A)((0,s.A)({},Z),{},{"aria-describedby":V?q:null}),r.cloneElement(E,G)))},y=(0,r.forwardRef)(g),b=n(2533),w=n(2897),C=n(275),E=n(3723),A=n(3257),x=n(682),k=n(8877),F=n(235),S=n(2279),P=n(1320),O=n(2187),M=n(5905),T=n(9077),N=n(5201),R=n(791),L=n(1108),I=n(4277),$=n(7358);const V=e=>{const{calc:t,componentCls:n,tooltipMaxWidth:r,tooltipColor:o,tooltipBg:i,tooltipBorderRadius:a,zIndexPopup:l,controlHeight:s,boxShadowSecondary:u,paddingSM:c,paddingXS:d,arrowOffsetHorizontal:f,sizePopupArrow:m}=e,p=t(a).add(m).add(f).equal(),h=t(a).mul(2).add(m).equal();return[{[n]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,M.dF)(e)),{position:"absolute",zIndex:l,display:"block",width:"max-content",maxWidth:r,visibility:"visible","--valid-offset-x":"var(--arrow-offset-horizontal, var(--arrow-x))",transformOrigin:["var(--valid-offset-x, 50%)","var(--arrow-y, 50%)"].join(" "),"&-hidden":{display:"none"},"--antd-arrow-background-color":i,[`${n}-inner`]:{minWidth:h,minHeight:s,padding:`${(0,O.zA)(e.calc(c).div(2).equal())} ${(0,O.zA)(d)}`,color:o,textAlign:"start",textDecoration:"none",wordWrap:"break-word",backgroundColor:i,borderRadius:a,boxShadow:u,boxSizing:"border-box"},[["&-placement-topLeft","&-placement-topRight","&-placement-bottomLeft","&-placement-bottomRight"].join(",")]:{minWidth:p},[["&-placement-left","&-placement-leftTop","&-placement-leftBottom","&-placement-right","&-placement-rightTop","&-placement-rightBottom"].join(",")]:{[`${n}-inner`]:{borderRadius:e.min(a,N.Zs)}},[`${n}-content`]:{position:"relative"}}),(0,L.A)(e,(e,{darkColor:t})=>({[`&${n}-${e}`]:{[`${n}-inner`]:{backgroundColor:t},[`${n}-arrow`]:{"--antd-arrow-background-color":t}}}))),{"&-rtl":{direction:"rtl"}})},(0,N.Ay)(e,"var(--antd-arrow-background-color)"),{[`${n}-pure`]:{position:"relative",maxWidth:"none",margin:e.sizePopupArrow}}]},D=e=>Object.assign(Object.assign({zIndexPopup:e.zIndexPopupBase+70},(0,N.Ke)({contentRadius:e.borderRadius,limitVerticalRadius:!0})),(0,R.n)((0,I.oX)(e,{borderRadiusOuter:Math.min(e.borderRadiusOuter,4)})));var j=(e,t=!0)=>(0,$.OF)("Tooltip",e=>{const{borderRadius:t,colorTextLightSolid:n,colorBgSpotlight:r}=e,o=(0,I.oX)(e,{tooltipMaxWidth:250,tooltipColor:n,tooltipBorderRadius:t,tooltipBg:r});return[V(o),(0,T.aB)(e,"zoom-big-fast")]},D,{resetStyle:!1,injectStyle:t})(e),_=n(4121);function H(e,t){const n=(0,_.nP)(t),r=i()({[`${e}-${t}`]:t&&n}),o={},a={};return t&&!n&&(o.background=t,a["--antd-arrow-background-color"]=t),{className:r,overlayStyle:o,arrowStyle:a}}var z=e=>{const{prefixCls:t,className:n,placement:o="top",title:l,color:s,overlayInnerStyle:u}=e,{getPrefixCls:c}=r.useContext(S.QO),d=c("tooltip",t),[f,m,p]=j(d),h=H(d,s),v=h.arrowStyle,g=Object.assign(Object.assign({},u),h.overlayStyle),y=i()(m,p,d,`${d}-pure`,`${d}-placement-${o}`,n,h.className);return f(r.createElement("div",{className:y,style:v},r.createElement("div",{className:`${d}-arrow`}),r.createElement(a,Object.assign({},e,{className:m,prefixCls:d,overlayInnerStyle:g}),l)))},B=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};const q=r.forwardRef((e,t)=>{var n,o;const{prefixCls:a,openClassName:l,getTooltipContainer:s,color:u,overlayInnerStyle:c,children:d,afterOpenChange:f,afterVisibleChange:m,destroyTooltipOnHide:p,destroyOnHidden:h,arrow:v=!0,title:g,overlay:O,builtinPlacements:M,arrowPointAtCenter:T=!1,autoAdjustOverflow:N=!0,motion:R,getPopupContainer:L,placement:I="top",mouseEnterDelay:$=.1,mouseLeaveDelay:V=.1,overlayStyle:D,rootClassName:_,overlayClassName:z,styles:q,classNames:W}=e,U=B(e,["prefixCls","openClassName","getTooltipContainer","color","overlayInnerStyle","children","afterOpenChange","afterVisibleChange","destroyTooltipOnHide","destroyOnHidden","arrow","title","overlay","builtinPlacements","arrowPointAtCenter","autoAdjustOverflow","motion","getPopupContainer","placement","mouseEnterDelay","mouseLeaveDelay","overlayStyle","rootClassName","overlayClassName","styles","classNames"]),X=!!v,[,Z]=(0,P.Ay)(),{getPopupContainer:G,getPrefixCls:Y,direction:K,className:J,style:Q,classNames:ee,styles:te}=(0,S.TP)("tooltip"),ne=(0,k.rJ)("Tooltip"),re=r.useRef(null),oe=()=>{var e;null===(e=re.current)||void 0===e||e.forceAlign()};r.useImperativeHandle(t,()=>{var e,t;return{forceAlign:oe,forcePopupAlign:()=>{ne.deprecated(!1,"forcePopupAlign","forceAlign"),oe()},nativeElement:null===(e=re.current)||void 0===e?void 0:e.nativeElement,popupElement:null===(t=re.current)||void 0===t?void 0:t.popupElement}});const[ie,ae]=(0,b.A)(!1,{value:null!==(n=e.open)&&void 0!==n?n:e.visible,defaultValue:null!==(o=e.defaultOpen)&&void 0!==o?o:e.defaultVisible}),le=!g&&!O&&0!==g,se=r.useMemo(()=>{var e,t;let n=T;return"object"==typeof v&&(n=null!==(t=null!==(e=v.pointAtCenter)&&void 0!==e?e:v.arrowPointAtCenter)&&void 0!==t?t:T),M||(0,A.A)({arrowPointAtCenter:n,autoAdjustOverflow:N,arrowWidth:X?Z.sizePopupArrow:0,borderRadius:Z.borderRadius,offset:Z.marginXXS,visibleFirst:!0})},[T,v,M,Z]),ue=r.useMemo(()=>0===g?g:O||g||"",[O,g]),ce=r.createElement(w.A,{space:!0},"function"==typeof ue?ue():ue),de=Y("tooltip",a),fe=Y(),me=e["data-popover-inject"];let pe=ie;"open"in e||"visible"in e||!le||(pe=!1);const he=r.isValidElement(d)&&!(0,x.zv)(d)?d:r.createElement("span",null,d),ve=he.props,ge=ve.className&&"string"!=typeof ve.className?ve.className:i()(ve.className,l||`${de}-open`),[ye,be,we]=j(de,!me),Ce=H(de,u),Ee=Ce.arrowStyle,Ae=i()(z,{[`${de}-rtl`]:"rtl"===K},Ce.className,_,be,we,J,ee.root,null==W?void 0:W.root),xe=i()(ee.body,null==W?void 0:W.body),[ke,Fe]=(0,C.YK)("Tooltip",U.zIndex),Se=r.createElement(y,Object.assign({},U,{zIndex:ke,showArrow:X,placement:I,mouseEnterDelay:$,mouseLeaveDelay:V,prefixCls:de,classNames:{root:Ae,body:xe},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},Ee),te.root),Q),D),null==q?void 0:q.root),body:Object.assign(Object.assign(Object.assign(Object.assign({},te.body),c),null==q?void 0:q.body),Ce.overlayStyle)},getTooltipContainer:L||s||G,ref:re,builtinPlacements:se,overlay:ce,visible:pe,onVisibleChange:t=>{var n,r;ae(!le&&t),le||(null===(n=e.onOpenChange)||void 0===n||n.call(e,t),null===(r=e.onVisibleChange)||void 0===r||r.call(e,t))},afterVisibleChange:null!=f?f:m,arrowContent:r.createElement("span",{className:`${de}-arrow-content`}),motion:{motionName:(0,E.b)(fe,"zoom-big-fast",e.transitionName),motionDeadline:1e3},destroyTooltipOnHide:null!=h?h:!!p}),pe?(0,x.Ob)(he,{className:ge}):he);return ye(r.createElement(F.A.Provider,{value:Fe},Se))});q._InternalPanelDoNotUseOrYouWillBeFired=z;var W=q},682:function(e,t,n){n.d(t,{Ob:function(){return a},fx:function(){return i},zv:function(){return o}});var r=n(6540);function o(e){return e&&r.isValidElement(e)&&e.type===r.Fragment}const i=(e,t,n)=>r.isValidElement(e)?r.cloneElement(e,"function"==typeof n?n(e.props||{}):n):t;function a(e,t){return i(e,e,t)}},791:function(e,t,n){n.d(t,{j:function(){return i},n:function(){return o}});var r=n(2187);function o(e){const{sizePopupArrow:t,borderRadiusXS:n,borderRadiusOuter:r}=e,o=t/2,i=o,a=1*r/Math.sqrt(2),l=o-r*(1-1/Math.sqrt(2)),s=o-n*(1/Math.sqrt(2)),u=r*(Math.sqrt(2)-1)+n*(1/Math.sqrt(2)),c=2*o-s,d=u,f=2*o-a,m=l,p=2*o-0,h=i,v=o*Math.sqrt(2)+r*(Math.sqrt(2)-2),g=r*(Math.sqrt(2)-1);return{arrowShadowWidth:v,arrowPath:`path('M 0 ${i} A ${r} ${r} 0 0 0 ${a} ${l} L ${s} ${u} A ${n} ${n} 0 0 1 ${c} ${d} L ${f} ${m} A ${r} ${r} 0 0 0 ${p} ${h} Z')`,arrowPolygon:`polygon(${g}px 100%, 50% ${g}px, ${2*o-g}px 100%, ${g}px 100%)`}}const i=(e,t,n)=>{const{sizePopupArrow:o,arrowPolygon:i,arrowPath:a,arrowShadowWidth:l,borderRadiusXS:s,calc:u}=e;return{pointerEvents:"none",width:o,height:o,overflow:"hidden","&::before":{position:"absolute",bottom:0,insetInlineStart:0,width:o,height:u(o).div(2).equal(),background:t,clipPath:{_multi_value_:!0,value:[i,a]},content:'""'},"&::after":{content:'""',position:"absolute",width:l,height:l,bottom:0,insetInline:0,margin:"auto",borderRadius:{_skip_check_:!0,value:`0 0 ${(0,r.zA)(s)} 0`},transform:"translateY(50%) rotate(-135deg)",boxShadow:n,zIndex:0,background:"transparent"}}}},829:function(e,t,n){var r=n(6540),o=n(8224);t.A=e=>{const t=r.useContext(o.A);return r.useMemo(()=>e?"string"==typeof e?null!=e?e:t:"function"==typeof e?e(t):t:t,[e,t])}},964:function(e,t,n){n.d(t,{A:function(){return r}});const r=(0,n(1788).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1108:function(e,t,n){n.d(t,{A:function(){return o}});var r=n(3950);function o(e,t){return r.s.reduce((n,r)=>{const o=e[`${r}1`],i=e[`${r}3`],a=e[`${r}6`],l=e[`${r}7`];return Object.assign(Object.assign({},n),t(r,{lightColor:o,lightBorderColor:i,darkColor:a,textColor:l}))},{})}},1155:function(e,t,n){n.d(t,{j:function(){return ri},A:function(){return ni}});var r=n(6540),o=n.t(r,2),i=(e=>(e.Space=" ",e.Enter="Enter",e.Escape="Escape",e.Backspace="Backspace",e.Delete="Delete",e.ArrowLeft="ArrowLeft",e.ArrowUp="ArrowUp",e.ArrowRight="ArrowRight",e.ArrowDown="ArrowDown",e.Home="Home",e.End="End",e.PageUp="PageUp",e.PageDown="PageDown",e.Tab="Tab",e))(i||{}),a=Object.defineProperty,l=(e,t,n)=>(((e,t,n)=>{t in e?a(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n})(e,"symbol"!=typeof t?t+"":t,n),n);let s=new class{constructor(){l(this,"current",this.detect()),l(this,"handoffState","pending"),l(this,"currentId",0)}set(e){this.current!==e&&(this.handoffState="pending",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return"server"===this.current}get isClient(){return"client"===this.current}detect(){return"undefined"==typeof window||"undefined"==typeof document?"server":"client"}handoff(){"pending"===this.handoffState&&(this.handoffState="complete")}get isHandoffComplete(){return"complete"===this.handoffState}},u=(e,t)=>{s.isServer?(0,r.useEffect)(e,t):(0,r.useLayoutEffect)(e,t)};function c(e){let t=(0,r.useRef)(e);return u(()=>{t.current=e},[e]),t}function d(e,t,n,o){let i=c(n);(0,r.useEffect)(()=>{function n(e){i.current(e)}return(e=null!=e?e:window).addEventListener(t,n,o),()=>e.removeEventListener(t,n,o)},[e,t,o])}class f extends Map{constructor(e){super(),this.factory=e}get(e){let t=super.get(e);return void 0===t&&(t=this.factory(e),this.set(e,t)),t}}function m(e){"function"==typeof queueMicrotask?queueMicrotask(e):Promise.resolve().then(e).catch(e=>setTimeout(()=>{throw e}))}function p(){let e=[],t={addEventListener(e,n,r,o){return e.addEventListener(n,r,o),t.add(()=>e.removeEventListener(n,r,o))},requestAnimationFrame(...e){let n=requestAnimationFrame(...e);return t.add(()=>cancelAnimationFrame(n))},nextFrame(...e){return t.requestAnimationFrame(()=>t.requestAnimationFrame(...e))},setTimeout(...e){let n=setTimeout(...e);return t.add(()=>clearTimeout(n))},microTask(...e){let n={current:!0};return m(()=>{n.current&&e[0]()}),t.add(()=>{n.current=!1})},style(e,t,n){let r=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:n}),this.add(()=>{Object.assign(e.style,{[t]:r})})},group(e){let t=p();return e(t),this.add(()=>t.dispose())},add(t){return e.includes(t)||e.push(t),()=>{let n=e.indexOf(t);if(n>=0)for(let t of e.splice(n,1))t()}},dispose(){for(let t of e.splice(0))t()}};return t}var h,v,g,y=Object.defineProperty,b=(e,t,n)=>(((e,t,n)=>{t in e?y(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n})(e,"symbol"!=typeof t?t+"":t,n),n),w=(e,t,n)=>{if(!t.has(e))throw TypeError("Cannot "+n)},C=(e,t,n)=>(w(e,t,"read from private field"),n?n.call(e):t.get(e)),E=(e,t,n)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,n)},A=(e,t,n,r)=>(w(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n);class x{constructor(e){E(this,h,{}),E(this,v,new f(()=>new Set)),E(this,g,new Set),b(this,"disposables",p()),A(this,h,e)}dispose(){this.disposables.dispose()}get state(){return C(this,h)}subscribe(e,t){let n={selector:e,callback:t,current:e(C(this,h))};return C(this,g).add(n),this.disposables.add(()=>{C(this,g).delete(n)})}on(e,t){return C(this,v).get(e).add(t),this.disposables.add(()=>{C(this,v).get(e).delete(t)})}send(e){let t=this.reduce(C(this,h),e);if(t!==C(this,h)){A(this,h,t);for(let e of C(this,g)){let t=e.selector(C(this,h));k(e.current,t)||(e.current=t,e.callback(t))}for(let t of C(this,v).get(e.type))t(C(this,h),e)}}}function k(e,t){return!!Object.is(e,t)||"object"==typeof e&&null!==e&&"object"==typeof t&&null!==t&&(Array.isArray(e)&&Array.isArray(t)?e.length===t.length&&F(e[Symbol.iterator](),t[Symbol.iterator]()):e instanceof Map&&t instanceof Map||e instanceof Set&&t instanceof Set?e.size===t.size&&F(e.entries(),t.entries()):!(!S(e)||!S(t))&&F(Object.entries(e)[Symbol.iterator](),Object.entries(t)[Symbol.iterator]()))}function F(e,t){for(;;){let n=e.next(),r=t.next();if(n.done&&r.done)return!0;if(n.done||r.done||!Object.is(n.value,r.value))return!1}}function S(e){if("[object Object]"!==Object.prototype.toString.call(e))return!1;let t=Object.getPrototypeOf(e);return null===t||null===Object.getPrototypeOf(t)}function P(e){let[t,n]=e(),r=p();return(...e)=>{t(...e),r.dispose(),r.microTask(n)}}function O(e,t,...n){if(e in t){let r=t[e];return"function"==typeof r?r(...n):r}let r=new Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(t).map(e=>`"${e}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(r,O),r}h=new WeakMap,v=new WeakMap,g=new WeakMap;var M=Object.defineProperty,T=(e,t,n)=>(((e,t,n)=>{t in e?M(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n})(e,"symbol"!=typeof t?t+"":t,n),n),N=(e=>(e[e.Push=0]="Push",e[e.Pop=1]="Pop",e))(N||{});let R={0(e,t){let n=t.id,r=e.stack,o=e.stack.indexOf(n);if(-1!==o){let t=e.stack.slice();return t.splice(o,1),t.push(n),r=t,{...e,stack:r}}return{...e,stack:[...e.stack,n]}},1(e,t){let n=t.id,r=e.stack.indexOf(n);if(-1===r)return e;let o=e.stack.slice();return o.splice(r,1),{...e,stack:o}}};class L extends x{constructor(){super(...arguments),T(this,"actions",{push:e=>this.send({type:0,id:e}),pop:e=>this.send({type:1,id:e})}),T(this,"selectors",{isTop:(e,t)=>e.stack[e.stack.length-1]===t,inStack:(e,t)=>e.stack.includes(t)})}static new(){return new L({stack:[]})}reduce(e,t){return O(t.type,R,e,t)}}const I=new f(()=>L.new());var $=n(8418);let V=function(e){let t=c(e);return r.useCallback((...e)=>t.current(...e),[t])};function D(e,t,n=k){return(0,$.useSyncExternalStoreWithSelector)(V(t=>e.subscribe(j,t)),V(()=>e.state),V(()=>e.state),V(t),n)}function j(e){return e}function _(e,t){let n=(0,r.useId)(),o=I.get(t),[i,a]=D(o,(0,r.useCallback)(e=>[o.selectors.isTop(e,n),o.selectors.inStack(e,n)],[o,n]));return u(()=>{if(e)return o.actions.push(n),()=>o.actions.pop(n)},[o,e,n]),!!e&&(!a||i)}function H(e,t=("undefined"!=typeof document?document.defaultView:null),n){let r=_(e,"escape");d(t,"keydown",e=>{r&&(e.defaultPrevented||e.key===i.Escape&&n(e))})}function z(e){var t,n;return s.isServer?null:e?"ownerDocument"in e?e.ownerDocument:"current"in e?null!=(n=null==(t=e.current)?void 0:t.ownerDocument)?n:document:null:document}let B=new Map,q=new Map;function W(e){var t;let n=null!=(t=q.get(e))?t:0;return q.set(e,n+1),0!==n||(B.set(e,{"aria-hidden":e.getAttribute("aria-hidden"),inert:e.inert}),e.setAttribute("aria-hidden","true"),e.inert=!0),()=>U(e)}function U(e){var t;let n=null!=(t=q.get(e))?t:1;if(1===n?q.delete(e):q.set(e,n-1),1!==n)return;let r=B.get(e);r&&(null===r["aria-hidden"]?e.removeAttribute("aria-hidden"):e.setAttribute("aria-hidden",r["aria-hidden"]),e.inert=r.inert,B.delete(e))}function X(e,{allowed:t,disallowed:n}={}){let r=_(e,"inert-others");u(()=>{var e,o;if(!r)return;let i=p();for(let t of null!=(e=null==n?void 0:n())?e:[])t&&i.add(W(t));let a=null!=(o=null==t?void 0:t())?o:[];for(let t of a){if(!t)continue;let e=z(t);if(!e)continue;let n=t.parentElement;for(;n&&n!==e.body;){for(let e of n.children)a.some(t=>e.contains(t))||i.add(W(e));n=n.parentElement}}return i.dispose},[r,t,n])}function Z(e){return"object"==typeof e&&null!==e&&"nodeType"in e}function G(e){return Z(e)&&"tagName"in e}function Y(e){return G(e)&&"accessKey"in e}function K(e){return G(e)&&"tabIndex"in e}function J(e){return Y(e)&&"INPUT"===e.nodeName}function Q(e){return Y(e)&&"LABEL"===e.nodeName}function ee(e){return Y(e)&&"FIELDSET"===e.nodeName}function te(e){return Y(e)&&"LEGEND"===e.nodeName}function ne(e,t,n){let o=c(e=>{let t=e.getBoundingClientRect();0===t.x&&0===t.y&&0===t.width&&0===t.height&&n()});(0,r.useEffect)(()=>{if(!e)return;let n=null===t?null:Y(t)?t:t.current;if(!n)return;let r=p();if("undefined"!=typeof ResizeObserver){let e=new ResizeObserver(()=>o.current(n));e.observe(n),r.add(()=>e.disconnect())}if("undefined"!=typeof IntersectionObserver){let e=new IntersectionObserver(()=>o.current(n));e.observe(n),r.add(()=>e.disconnect())}return()=>r.dispose()},[t,o,e])}let re=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>`${e}:not([tabindex='-1'])`).join(","),oe=["[data-autofocus]"].map(e=>`${e}:not([tabindex='-1'])`).join(",");var ie=(e=>(e[e.First=1]="First",e[e.Previous=2]="Previous",e[e.Next=4]="Next",e[e.Last=8]="Last",e[e.WrapAround=16]="WrapAround",e[e.NoScroll=32]="NoScroll",e[e.AutoFocus=64]="AutoFocus",e))(ie||{}),ae=(e=>(e[e.Error=0]="Error",e[e.Overflow=1]="Overflow",e[e.Success=2]="Success",e[e.Underflow=3]="Underflow",e))(ae||{}),le=(e=>(e[e.Previous=-1]="Previous",e[e.Next=1]="Next",e))(le||{});function se(e=document.body){return null==e?[]:Array.from(e.querySelectorAll(re)).sort((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}var ue=(e=>(e[e.Strict=0]="Strict",e[e.Loose=1]="Loose",e))(ue||{});function ce(e,t=0){var n;return e!==(null==(n=z(e))?void 0:n.body)&&O(t,{0(){return e.matches(re)},1(){let t=e;for(;null!==t;){if(t.matches(re))return!0;t=t.parentElement}return!1}})}function de(e){let t=z(e);p().nextFrame(()=>{t&&K(t.activeElement)&&!ce(t.activeElement,0)&&me(e)})}var fe=(e=>(e[e.Keyboard=0]="Keyboard",e[e.Mouse=1]="Mouse",e))(fe||{});function me(e){null==e||e.focus({preventScroll:!0})}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("keydown",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")},!0),document.addEventListener("click",e=>{1===e.detail?delete document.documentElement.dataset.headlessuiFocusVisible:0===e.detail&&(document.documentElement.dataset.headlessuiFocusVisible="")},!0));let pe=["textarea","input"].join(",");function he(e,t=e=>e){return e.slice().sort((e,n)=>{let r=t(e),o=t(n);if(null===r||null===o)return 0;let i=r.compareDocumentPosition(o);return i&Node.DOCUMENT_POSITION_FOLLOWING?-1:i&Node.DOCUMENT_POSITION_PRECEDING?1:0})}function ve(e,t,{sorted:n=!0,relativeTo:r=null,skipElements:o=[]}={}){let i=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,a=Array.isArray(e)?n?he(e):e:64&t?function(e=document.body){return null==e?[]:Array.from(e.querySelectorAll(oe)).sort((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}(e):se(e);o.length>0&&a.length>1&&(a=a.filter(e=>!o.some(t=>null!=t&&"current"in t?(null==t?void 0:t.current)===e:t===e))),r=null!=r?r:i.activeElement;let l,s=(()=>{if(5&t)return 1;if(10&t)return-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),u=(()=>{if(1&t)return 0;if(2&t)return Math.max(0,a.indexOf(r))-1;if(4&t)return Math.max(0,a.indexOf(r))+1;if(8&t)return a.length-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),c=32&t?{preventScroll:!0}:{},d=0,f=a.length;do{if(d>=f||d+f<=0)return 0;let e=u+d;if(16&t)e=(e+f)%f;else{if(e<0)return 3;if(e>=f)return 1}l=a[e],null==l||l.focus(c),d+=s}while(l!==i.activeElement);return 6&t&&function(e){var t,n;return null!=(n=null==(t=null==e?void 0:e.matches)?void 0:t.call(e,pe))&&n}(l)&&l.select(),2}function ge(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function ye(){return ge()||/Android/gi.test(window.navigator.userAgent)}function be(e,t,n,o){let i=c(n);(0,r.useEffect)(()=>{if(e)return document.addEventListener(t,n,o),()=>document.removeEventListener(t,n,o);function n(e){i.current(e)}},[e,t,o])}function we(e,t,n,o){let i=c(n);(0,r.useEffect)(()=>{if(e)return window.addEventListener(t,n,o),()=>window.removeEventListener(t,n,o);function n(e){i.current(e)}},[e,t,o])}function Ce(e,t,n){let o=c(n),i=(0,r.useCallback)(function(e,n){if(e.defaultPrevented)return;let r=n(e);if(null===r||!r.getRootNode().contains(r)||!r.isConnected)return;let i=function e(t){return"function"==typeof t?e(t()):Array.isArray(t)||t instanceof Set?t:[t]}(t);for(let t of i)if(null!==t&&(t.contains(r)||e.composed&&e.composedPath().includes(t)))return;return!ce(r,ue.Loose)&&-1!==r.tabIndex&&e.preventDefault(),o.current(e,r)},[o,t]),a=(0,r.useRef)(null);be(e,"pointerdown",e=>{var t,n;ye()||(a.current=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target)},!0),be(e,"pointerup",e=>{if(ye()||!a.current)return;let t=a.current;return a.current=null,i(e,()=>t)},!0);let l=(0,r.useRef)({x:0,y:0});be(e,"touchstart",e=>{l.current.x=e.touches[0].clientX,l.current.y=e.touches[0].clientY},!0),be(e,"touchend",e=>{let t=e.changedTouches[0].clientX,n=e.changedTouches[0].clientY;if(!(Math.abs(t-l.current.x)>=30||Math.abs(n-l.current.y)>=30))return i(e,()=>K(e.target)?e.target:null)},!0),we(e,"blur",e=>i(e,()=>function(e){return Y(e)&&"IFRAME"===e.nodeName}(window.document.activeElement)?window.document.activeElement:null),!0)}function Ee(...e){return(0,r.useMemo)(()=>z(...e),[...e])}function Ae(...e){return Array.from(new Set(e.flatMap(e=>"string"==typeof e?e.split(" "):[]))).filter(Boolean).join(" ")}var xe=(e=>(e[e.None=0]="None",e[e.RenderStrategy=1]="RenderStrategy",e[e.Static=2]="Static",e))(xe||{}),ke=(e=>(e[e.Unmount=0]="Unmount",e[e.Hidden=1]="Hidden",e))(ke||{});function Fe(){let e=function(){let e=(0,r.useRef)([]),t=(0,r.useCallback)(t=>{for(let n of e.current)null!=n&&("function"==typeof n?n(t):n.current=t)},[]);return(...n)=>{if(!n.every(e=>null==e))return e.current=n,t}}();return(0,r.useCallback)(t=>function({ourProps:e,theirProps:t,slot:n,defaultTag:r,features:o,visible:i=!0,name:a,mergeRefs:l}){l=null!=l?l:Pe;let s=Oe(t,e);if(i)return Se(s,n,r,a,l);let u=null!=o?o:0;if(2&u){let{static:e=!1,...t}=s;if(e)return Se(t,n,r,a,l)}if(1&u){let{unmount:e=!0,...t}=s;return O(e?0:1,{0(){return null},1(){return Se({...t,hidden:!0,style:{display:"none"}},n,r,a,l)}})}return Se(s,n,r,a,l)}({mergeRefs:e,...t}),[e])}function Se(e,t={},n,o,i){let{as:a=n,children:l,refName:s="ref",...u}=Re(e,["unmount","static"]),c=void 0!==e.ref?{[s]:e.ref}:{},d="function"==typeof l?l(t):l;"className"in u&&u.className&&"function"==typeof u.className&&(u.className=u.className(t)),u["aria-labelledby"]&&u["aria-labelledby"]===u.id&&(u["aria-labelledby"]=void 0);let f={};if(t){let e=!1,n=[];for(let[r,o]of Object.entries(t))"boolean"==typeof o&&(e=!0),!0===o&&n.push(r.replace(/([A-Z])/g,e=>`-${e.toLowerCase()}`));if(e){f["data-headlessui-state"]=n.join(" ");for(let e of n)f[`data-${e}`]=""}}if(a===r.Fragment&&(Object.keys(Ne(u)).length>0||Object.keys(Ne(f)).length>0)){if((0,r.isValidElement)(d)&&!(Array.isArray(d)&&d.length>1)){let e=d.props,t=null==e?void 0:e.className,n="function"==typeof t?(...e)=>Ae(t(...e),u.className):Ae(t,u.className),o=n?{className:n}:{},a=Oe(d.props,Ne(Re(u,["ref"])));for(let r in f)r in a&&delete f[r];return(0,r.cloneElement)(d,Object.assign({},a,f,c,{ref:i(Le(d),c.ref)},o))}if(Object.keys(Ne(u)).length>0)throw new Error(['Passing props on "Fragment"!',"",`The current component <${o} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(Ne(u)).concat(Object.keys(Ne(f))).map(e=>`  - ${e}`).join("\n"),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(e=>`  - ${e}`).join("\n")].join("\n"))}return(0,r.createElement)(a,Object.assign({},Re(u,["ref"]),a!==r.Fragment&&c,a!==r.Fragment&&f),d)}function Pe(...e){return e.every(e=>null==e)?void 0:t=>{for(let n of e)null!=n&&("function"==typeof n?n(t):n.current=t)}}function Oe(...e){if(0===e.length)return{};if(1===e.length)return e[0];let t={},n={};for(let r of e)for(let e in r)e.startsWith("on")&&"function"==typeof r[e]?(null!=n[e]||(n[e]=[]),n[e].push(r[e])):t[e]=r[e];if(t.disabled||t["aria-disabled"])for(let r in n)/^(on(?:Click|Pointer|Mouse|Key)(?:Down|Up|Press)?)$/.test(r)&&(n[r]=[e=>{var t;return null==(t=null==e?void 0:e.preventDefault)?void 0:t.call(e)}]);for(let r in n)Object.assign(t,{[r](e,...t){let o=n[r];for(let n of o){if((e instanceof Event||(null==e?void 0:e.nativeEvent)instanceof Event)&&e.defaultPrevented)return;n(e,...t)}}});return t}function Me(...e){if(0===e.length)return{};if(1===e.length)return e[0];let t={},n={};for(let r of e)for(let e in r)e.startsWith("on")&&"function"==typeof r[e]?(null!=n[e]||(n[e]=[]),n[e].push(r[e])):t[e]=r[e];for(let r in n)Object.assign(t,{[r](...e){let t=n[r];for(let n of t)null==n||n(...e)}});return t}function Te(e){var t;return Object.assign((0,r.forwardRef)(e),{displayName:null!=(t=e.displayName)?t:e.name})}function Ne(e){let t=Object.assign({},e);for(let n in t)void 0===t[n]&&delete t[n];return t}function Re(e,t=[]){let n=Object.assign({},e);for(let r of t)r in n&&delete n[r];return n}function Le(e){return r.version.split(".")[0]>="19"?e.props.ref:e.ref}var Ie=(e=>(e[e.None=1]="None",e[e.Focusable=2]="Focusable",e[e.Hidden=4]="Hidden",e))(Ie||{});let $e=Te(function(e,t){var n;let{features:r=1,...o}=e,i={ref:t,"aria-hidden":!(2&~r)||(null!=(n=o["aria-hidden"])?n:void 0),hidden:!(4&~r)||void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...!(4&~r)&&!!(2&~r)&&{display:"none"}}};return Fe()({ourProps:i,theirProps:o,slot:{},defaultTag:"span",name:"Hidden"})});let Ve=(0,r.createContext)(null);function De({children:e,node:t}){let[n,o]=(0,r.useState)(null),i=je(null!=t?t:n);return r.createElement(Ve.Provider,{value:i},e,null===i&&r.createElement($e,{features:Ie.Hidden,ref:e=>{var t,n;if(e)for(let r of null!=(n=null==(t=z(e))?void 0:t.querySelectorAll("html > *, body > *"))?n:[])if(r!==document.body&&r!==document.head&&G(r)&&null!=r&&r.contains(e)){o(r);break}}}))}function je(e=null){var t;return null!=(t=(0,r.useContext)(Ve))?t:e}function _e(){let e;return{before({doc:t}){var n;let r=t.documentElement,o=null!=(n=t.defaultView)?n:window;e=Math.max(0,o.innerWidth-r.clientWidth)},after({doc:t,d:n}){let r=t.documentElement,o=Math.max(0,r.clientWidth-r.offsetWidth),i=Math.max(0,e-o);n.style(r,"paddingRight",`${i}px`)}}}function He(){return ge()?{before({doc:e,d:t,meta:n}){function r(e){return n.containers.flatMap(e=>e()).some(t=>t.contains(e))}t.microTask(()=>{var n;if("auto"!==window.getComputedStyle(e.documentElement).scrollBehavior){let n=p();n.style(e.documentElement,"scrollBehavior","auto"),t.add(()=>t.microTask(()=>n.dispose()))}let o=null!=(n=window.scrollY)?n:window.pageYOffset,i=null;t.addEventListener(e,"click",t=>{if(K(t.target))try{let n=t.target.closest("a");if(!n)return;let{hash:o}=new URL(n.href),a=e.querySelector(o);K(a)&&!r(a)&&(i=a)}catch{}},!0),t.addEventListener(e,"touchstart",e=>{if(K(e.target)&&function(e){return G(e)&&"style"in e}(e.target))if(r(e.target)){let n=e.target;for(;n.parentElement&&r(n.parentElement);)n=n.parentElement;t.style(n,"overscrollBehavior","contain")}else t.style(e.target,"touchAction","none")}),t.addEventListener(e,"touchmove",e=>{if(K(e.target)){if(J(e.target))return;if(r(e.target)){let t=e.target;for(;t.parentElement&&""!==t.dataset.headlessuiPortal&&!(t.scrollHeight>t.clientHeight||t.scrollWidth>t.clientWidth);)t=t.parentElement;""===t.dataset.headlessuiPortal&&e.preventDefault()}else e.preventDefault()}},{passive:!1}),t.add(()=>{var e;let t=null!=(e=window.scrollY)?e:window.pageYOffset;o!==t&&window.scrollTo(0,o),i&&i.isConnected&&(i.scrollIntoView({block:"nearest"}),i=null)})})}}:{}}function ze(e){let t={};for(let n of e)Object.assign(t,n(t));return t}let Be=function(e,t){let n=e(),r=new Set;return{getSnapshot(){return n},subscribe(e){return r.add(e),()=>r.delete(e)},dispatch(e,...o){let i=t[e].call(n,...o);i&&(n=i,r.forEach(e=>e()))}}}(()=>new Map,{PUSH(e,t){var n;let r=null!=(n=this.get(e))?n:{doc:e,count:0,d:p(),meta:new Set};return r.count++,r.meta.add(t),this.set(e,r),this},POP(e,t){let n=this.get(e);return n&&(n.count--,n.meta.delete(t)),this},SCROLL_PREVENT({doc:e,d:t,meta:n}){let r={doc:e,d:t,meta:ze(n)},o=[He(),_e(),{before({doc:e,d:t}){t.style(e.documentElement,"overflow","hidden")}}];o.forEach(({before:e})=>null==e?void 0:e(r)),o.forEach(({after:e})=>null==e?void 0:e(r))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});function qe(e,t,n=()=>({containers:[]})){let o=function(e){return(0,r.useSyncExternalStore)(e.subscribe,e.getSnapshot,e.getSnapshot)}(Be),i=t?o.get(t):void 0,a=!!i&&i.count>0;return u(()=>{if(t&&e)return Be.dispatch("PUSH",t,n),()=>Be.dispatch("POP",t,n)},[e,t]),a}function We(e,t,n=()=>[document.body]){qe(_(e,"scroll-lock"),t,e=>{var t;return{containers:[...null!=(t=e.containers)?t:[],n]}})}function Ue(){let e=function(){let e="undefined"==typeof document;return(e=>e.useSyncExternalStore)(o)(()=>()=>{},()=>!1,()=>!e)}(),[t,n]=r.useState(s.isHandoffComplete);return t&&!1===s.isHandoffComplete&&n(!1),r.useEffect(()=>{!0!==t&&n(!0)},[t]),r.useEffect(()=>s.handoff(),[]),!e&&t}Be.subscribe(()=>{let e=Be.getSnapshot(),t=new Map;for(let[n]of e)t.set(n,n.documentElement.style.overflow);for(let n of e.values()){let e="hidden"===t.get(n.doc),r=0!==n.count;(r&&!e||!r&&e)&&Be.dispatch(n.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",n),0===n.count&&Be.dispatch("TEARDOWN",n)}});let Xe=Symbol();function Ze(...e){let t=(0,r.useRef)(e);(0,r.useEffect)(()=>{t.current=e},[e]);let n=V(e=>{for(let n of t.current)null!=n&&("function"==typeof n?n(e):n.current=e)});return e.every(e=>null==e||(null==e?void 0:e[Xe]))?void 0:n}let Ge=(0,r.createContext)(()=>{});function Ye({value:e,children:t}){return r.createElement(Ge.Provider,{value:e},t)}let Ke=(0,r.createContext)(null);Ke.displayName="OpenClosedContext";var Je=(e=>(e[e.Open=1]="Open",e[e.Closed=2]="Closed",e[e.Closing=4]="Closing",e[e.Opening=8]="Opening",e))(Je||{});function Qe(){return(0,r.useContext)(Ke)}function et({value:e,children:t}){return r.createElement(Ke.Provider,{value:e},t)}function tt({children:e}){return r.createElement(Ke.Provider,{value:null},e)}let nt=(0,r.createContext)(!1);function rt(e){return r.createElement(nt.Provider,{value:e.force},e.children)}let ot=(0,r.createContext)(void 0);function it(){return(0,r.useContext)(ot)}let at=(0,r.createContext)(null);function lt(){let e=(0,r.useContext)(at);if(null===e){let e=new Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(e,lt),e}return e}function st(){let[e,t]=(0,r.useState)([]);return[e.length>0?e.join(" "):void 0,(0,r.useMemo)(()=>function(e){let n=V(e=>(t(t=>[...t,e]),()=>t(t=>{let n=t.slice(),r=n.indexOf(e);return-1!==r&&n.splice(r,1),n}))),o=(0,r.useMemo)(()=>({register:n,slot:e.slot,name:e.name,props:e.props,value:e.value}),[n,e.slot,e.name,e.props,e.value]);return r.createElement(at.Provider,{value:o},e.children)},[t])]}at.displayName="DescriptionContext";let ut=Te(function(e,t){let n=(0,r.useId)(),o=it(),{id:i=`headlessui-description-${n}`,...a}=e,l=lt(),s=Ze(t);u(()=>l.register(i),[i,l.register]);let c=o||!1,d=(0,r.useMemo)(()=>({...l.slot,disabled:c}),[l.slot,c]),f={ref:s,...l.props,id:i};return Fe()({ourProps:f,theirProps:a,slot:d,defaultTag:"p",name:l.name||"Description"})}),ct=Object.assign(ut,{});function dt(){let[e]=(0,r.useState)(p);return(0,r.useEffect)(()=>()=>e.dispose(),[e]),e}function ft(){let e=(0,r.useRef)(!1);return u(()=>(e.current=!0,()=>{e.current=!1}),[]),e}function mt(e){let t=V(e),n=(0,r.useRef)(!1);(0,r.useEffect)(()=>(n.current=!1,()=>{n.current=!0,m(()=>{n.current&&t()})}),[t])}var pt=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))(pt||{});function ht(e,t){let n=(0,r.useRef)([]),o=V(e);(0,r.useEffect)(()=>{let e=[...n.current];for(let[r,i]of t.entries())if(n.current[r]!==i){let r=o(t,e);return n.current=t,r}},[o,...t])}let vt=[];function gt(e){if(!e)return new Set;if("function"==typeof e)return new Set(e());let t=new Set;for(let n of e.current)G(n.current)&&t.add(n.current);return t}!function(e){function t(){"loading"!==document.readyState&&(e(),document.removeEventListener("DOMContentLoaded",t))}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("DOMContentLoaded",t),t())}(()=>{function e(e){if(!K(e.target)||e.target===document.body||vt[0]===e.target)return;let t=e.target;t=t.closest(re),vt.unshift(null!=t?t:e.target),vt=vt.filter(e=>null!=e&&e.isConnected),vt.splice(10)}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})});var yt=(e=>(e[e.None=0]="None",e[e.InitialFocus=1]="InitialFocus",e[e.TabLock=2]="TabLock",e[e.FocusLock=4]="FocusLock",e[e.RestoreFocus=8]="RestoreFocus",e[e.AutoFocus=16]="AutoFocus",e))(yt||{});let bt=Te(function(e,t){let n=(0,r.useRef)(null),o=Ze(n,t),{initialFocus:i,initialFocusFallback:a,containers:l,features:s=15,...u}=e;Ue()||(s=0);let c=Ee(n);!function(e,{ownerDocument:t}){let n=!!(8&e),o=function(e=!0){let t=(0,r.useRef)(vt.slice());return ht(([e],[n])=>{!0===n&&!1===e&&m(()=>{t.current.splice(0)}),!1===n&&!0===e&&(t.current=vt.slice())},[e,vt,t]),V(()=>{var e;return null!=(e=t.current.find(e=>null!=e&&e.isConnected))?e:null})}(n);ht(()=>{n||(null==t?void 0:t.activeElement)===(null==t?void 0:t.body)&&me(o())},[n]),mt(()=>{n&&me(o())})}(s,{ownerDocument:c});let f=function(e,{ownerDocument:t,container:n,initialFocus:o,initialFocusFallback:i}){let a=(0,r.useRef)(null),l=_(!!(1&e),"focus-trap#initial-focus"),s=ft();return ht(()=>{if(0===e)return;if(!l)return void(null!=i&&i.current&&me(i.current));let r=n.current;r&&m(()=>{if(!s.current)return;let n=null==t?void 0:t.activeElement;if(null!=o&&o.current){if((null==o?void 0:o.current)===n)return void(a.current=n)}else if(r.contains(n))return void(a.current=n);if(null!=o&&o.current)me(o.current);else{if(16&e){if(ve(r,ie.First|ie.AutoFocus)!==ae.Error)return}else if(ve(r,ie.First)!==ae.Error)return;if(null!=i&&i.current&&(me(i.current),(null==t?void 0:t.activeElement)===i.current))return;console.warn("There are no focusable elements inside the <FocusTrap />")}a.current=null==t?void 0:t.activeElement})},[i,l,e]),a}(s,{ownerDocument:c,container:n,initialFocus:i,initialFocusFallback:a});!function(e,{ownerDocument:t,container:n,containers:r,previousActiveElement:o}){let i=ft(),a=!!(4&e);d(null==t?void 0:t.defaultView,"focus",e=>{if(!a||!i.current)return;let t=gt(r);Y(n.current)&&t.add(n.current);let l=o.current;if(!l)return;let s=e.target;Y(s)?Ct(t,s)?(o.current=s,me(s)):(e.preventDefault(),e.stopPropagation(),me(l)):me(o.current)},!0)}(s,{ownerDocument:c,container:n,containers:l,previousActiveElement:f});let p=function(){let e=(0,r.useRef)(0);return we(!0,"keydown",t=>{"Tab"===t.key&&(e.current=t.shiftKey?1:0)},!0),e}(),h=V(e=>{if(!Y(n.current))return;let t=n.current;O(p.current,{[pt.Forwards]:()=>{ve(t,ie.First,{skipElements:[e.relatedTarget,a]})},[pt.Backwards]:()=>{ve(t,ie.Last,{skipElements:[e.relatedTarget,a]})}})}),v=_(!!(2&s),"focus-trap#tab-lock"),g=dt(),y=(0,r.useRef)(!1),b={ref:o,onKeyDown(e){"Tab"==e.key&&(y.current=!0,g.requestAnimationFrame(()=>{y.current=!1}))},onBlur(e){if(!(4&s))return;let t=gt(l);Y(n.current)&&t.add(n.current);let r=e.relatedTarget;K(r)&&"true"!==r.dataset.headlessuiFocusGuard&&(Ct(t,r)||(y.current?ve(n.current,O(p.current,{[pt.Forwards]:()=>ie.Next,[pt.Backwards]:()=>ie.Previous})|ie.WrapAround,{relativeTo:e.target}):K(e.target)&&me(e.target)))}},w=Fe();return r.createElement(r.Fragment,null,v&&r.createElement($e,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:h,features:Ie.Focusable}),w({ourProps:b,theirProps:u,defaultTag:"div",name:"FocusTrap"}),v&&r.createElement($e,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:h,features:Ie.Focusable}))}),wt=Object.assign(bt,{features:yt});function Ct(e,t){for(let n of e)if(n.contains(t))return!0;return!1}var Et=n(961);function At(e){let t=(0,r.useContext)(nt),n=(0,r.useContext)(St),[o,i]=(0,r.useState)(()=>{var r;if(!t&&null!==n)return null!=(r=n.current)?r:null;if(s.isServer)return null;let o=null==e?void 0:e.getElementById("headlessui-portal-root");if(o)return o;if(null===e)return null;let i=e.createElement("div");return i.setAttribute("id","headlessui-portal-root"),e.body.appendChild(i)});return(0,r.useEffect)(()=>{null!==o&&(null!=e&&e.body.contains(o)||null==e||e.body.appendChild(o))},[o,e]),(0,r.useEffect)(()=>{t||null!==n&&i(n.current)},[n,i,t]),o}let xt=r.Fragment,kt=Te(function(e,t){let{ownerDocument:n=null,...o}=e,i=(0,r.useRef)(null),a=Ze(function(e,t=!0){return Object.assign(e,{[Xe]:t})}(e=>{i.current=e}),t),l=Ee(i),c=null!=n?n:l,d=At(c),[f]=(0,r.useState)(()=>{var e;return s.isServer?null:null!=(e=null==c?void 0:c.createElement("div"))?e:null}),m=(0,r.useContext)(Pt),p=Ue();u(()=>{!d||!f||d.contains(f)||(f.setAttribute("data-headlessui-portal",""),d.appendChild(f))},[d,f]),u(()=>{if(f&&m)return m.register(f)},[m,f]),mt(()=>{var e;!d||!f||(Z(f)&&d.contains(f)&&d.removeChild(f),d.childNodes.length<=0&&(null==(e=d.parentElement)||e.removeChild(d)))});let h=Fe();return p&&d&&f?(0,Et.createPortal)(h({ourProps:{ref:a},theirProps:o,slot:{},defaultTag:xt,name:"Portal"}),f):null});let Ft=r.Fragment,St=(0,r.createContext)(null);let Pt=(0,r.createContext)(null);function Ot(){let e=(0,r.useContext)(Pt),t=(0,r.useRef)([]),n=V(n=>(t.current.push(n),e&&e.register(n),()=>o(n))),o=V(n=>{let r=t.current.indexOf(n);-1!==r&&t.current.splice(r,1),e&&e.unregister(n)}),i=(0,r.useMemo)(()=>({register:n,unregister:o,portals:t}),[n,o,t]);return[t,(0,r.useMemo)(()=>function({children:e}){return r.createElement(Pt.Provider,{value:i},e)},[i])]}let Mt=Te(function(e,t){let n=Ze(t),{enabled:o=!0,ownerDocument:i,...a}=e,l=Fe();return o?r.createElement(kt,{...a,ownerDocument:i,ref:n}):l({ourProps:{ref:n},theirProps:a,slot:{},defaultTag:xt,name:"Portal"})}),Tt=Te(function(e,t){let{target:n,...o}=e,i={ref:Ze(t)},a=Fe();return r.createElement(St.Provider,{value:n},a({ourProps:i,theirProps:o,defaultTag:Ft,name:"Popover.Group"}))}),Nt=Object.assign(Mt,{Group:Tt});var Rt,Lt;"undefined"!=typeof process&&"undefined"!=typeof globalThis&&"undefined"!=typeof Element&&"test"===(null==(Rt=null==process?void 0:{})?void 0:Rt.NODE_ENV)&&void 0===(null==(Lt=null==Element?void 0:Element.prototype)?void 0:Lt.getAnimations)&&(Element.prototype.getAnimations=function(){return console.warn(["Headless UI has polyfilled `Element.prototype.getAnimations` for your tests.","Please install a proper polyfill e.g. `jsdom-testing-mocks`, to silence these warnings.","","Example usage:","```js","import { mockAnimationsApi } from 'jsdom-testing-mocks'","mockAnimationsApi()","```"].join("\n")),[]});var It=(e=>(e[e.None=0]="None",e[e.Closed=1]="Closed",e[e.Enter=2]="Enter",e[e.Leave=4]="Leave",e))(It||{});function $t(e){let t={};for(let n in e)!0===e[n]&&(t[`data-${n}`]="");return t}function Vt(e,t,n,o){let[i,a]=(0,r.useState)(n),{hasFlag:l,addFlag:s,removeFlag:c}=function(e=0){let[t,n]=(0,r.useState)(e),o=(0,r.useCallback)(e=>n(e),[t]),i=(0,r.useCallback)(e=>n(t=>t|e),[t]),a=(0,r.useCallback)(e=>(t&e)===e,[t]),l=(0,r.useCallback)(e=>n(t=>t&~e),[n]),s=(0,r.useCallback)(e=>n(t=>t^e),[n]);return{flags:t,setFlag:o,addFlag:i,hasFlag:a,removeFlag:l,toggleFlag:s}}(e&&i?3:0),d=(0,r.useRef)(!1),f=(0,r.useRef)(!1),m=dt();return u(()=>{var r;if(e)return n&&a(!0),t?(null==(r=null==o?void 0:o.start)||r.call(o,n),function(e,{prepare:t,run:n,done:r,inFlight:o}){let i=p();return function(e,{inFlight:t,prepare:n}){if(null!=t&&t.current)return void n();let r=e.style.transition;e.style.transition="none",n(),e.offsetHeight,e.style.transition=r}(e,{prepare:t,inFlight:o}),i.nextFrame(()=>{n(),i.requestAnimationFrame(()=>{i.add(function(e,t){var n,r;let o=p();if(!e)return o.dispose;let i=!1;o.add(()=>{i=!0});let a=null!=(r=null==(n=e.getAnimations)?void 0:n.call(e).filter(e=>e instanceof CSSTransition))?r:[];return 0===a.length?(t(),o.dispose):(Promise.allSettled(a.map(e=>e.finished)).then(()=>{i||t()}),o.dispose)}(e,r))})}),i.dispose}(t,{inFlight:d,prepare(){f.current?f.current=!1:f.current=d.current,d.current=!0,!f.current&&(n?(s(3),c(4)):(s(4),c(2)))},run(){f.current?n?(c(3),s(4)):(c(4),s(3)):n?c(1):s(1)},done(){var e;f.current&&"function"==typeof t.getAnimations&&t.getAnimations().length>0||(d.current=!1,c(7),n||a(!1),null==(e=null==o?void 0:o.end)||e.call(o,n))}})):void(n&&s(3))},[e,n,t,m]),e?[i,{closed:l(1),enter:l(2),leave:l(4),transition:l(2)||l(4)}]:[n,{closed:void 0,enter:void 0,leave:void 0,transition:void 0}]}function Dt(e){var t;return!!(e.enter||e.enterFrom||e.enterTo||e.leave||e.leaveFrom||e.leaveTo)||(null!=(t=e.as)?t:qt)!==r.Fragment||1===r.Children.count(e.children)}let jt=(0,r.createContext)(null);jt.displayName="TransitionContext";var _t=(e=>(e.Visible="visible",e.Hidden="hidden",e))(_t||{});let Ht=(0,r.createContext)(null);function zt(e){return"children"in e?zt(e.children):e.current.filter(({el:e})=>null!==e.current).filter(({state:e})=>"visible"===e).length>0}function Bt(e,t){let n=c(e),o=(0,r.useRef)([]),i=ft(),a=dt(),l=V((e,t=ke.Hidden)=>{let r=o.current.findIndex(({el:t})=>t===e);-1!==r&&(O(t,{[ke.Unmount](){o.current.splice(r,1)},[ke.Hidden](){o.current[r].state="hidden"}}),a.microTask(()=>{var e;!zt(o)&&i.current&&(null==(e=n.current)||e.call(n))}))}),s=V(e=>{let t=o.current.find(({el:t})=>t===e);return t?"visible"!==t.state&&(t.state="visible"):o.current.push({el:e,state:"visible"}),()=>l(e,ke.Unmount)}),u=(0,r.useRef)([]),d=(0,r.useRef)(Promise.resolve()),f=(0,r.useRef)({enter:[],leave:[]}),m=V((e,n,r)=>{u.current.splice(0),t&&(t.chains.current[n]=t.chains.current[n].filter(([t])=>t!==e)),null==t||t.chains.current[n].push([e,new Promise(e=>{u.current.push(e)})]),null==t||t.chains.current[n].push([e,new Promise(e=>{Promise.all(f.current[n].map(([e,t])=>t)).then(()=>e())})]),"enter"===n?d.current=d.current.then(()=>null==t?void 0:t.wait.current).then(()=>r(n)):r(n)}),p=V((e,t,n)=>{Promise.all(f.current[t].splice(0).map(([e,t])=>t)).then(()=>{var e;null==(e=u.current.shift())||e()}).then(()=>n(t))});return(0,r.useMemo)(()=>({children:o,register:s,unregister:l,onStart:m,onStop:p,wait:d,chains:f}),[s,l,o,m,p,f,d])}Ht.displayName="NestingContext";let qt=r.Fragment,Wt=xe.RenderStrategy;let Ut=Te(function(e,t){let{show:n,appear:o=!1,unmount:i=!0,...a}=e,l=(0,r.useRef)(null),s=Ze(...Dt(e)?[l,t]:null===t?[]:[t]);Ue();let c=Qe();if(void 0===n&&null!==c&&(n=(c&Je.Open)===Je.Open),void 0===n)throw new Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[d,f]=(0,r.useState)(n?"visible":"hidden"),m=Bt(()=>{n||f("hidden")}),[p,h]=(0,r.useState)(!0),v=(0,r.useRef)([n]);u(()=>{!1!==p&&v.current[v.current.length-1]!==n&&(v.current.push(n),h(!1))},[v,n]);let g=(0,r.useMemo)(()=>({show:n,appear:o,initial:p}),[n,o,p]);u(()=>{n?f("visible"):!zt(m)&&null!==l.current&&f("hidden")},[n,m]);let y={unmount:i},b=V(()=>{var t;p&&h(!1),null==(t=e.beforeEnter)||t.call(e)}),w=V(()=>{var t;p&&h(!1),null==(t=e.beforeLeave)||t.call(e)}),C=Fe();return r.createElement(Ht.Provider,{value:m},r.createElement(jt.Provider,{value:g},C({ourProps:{...y,as:r.Fragment,children:r.createElement(Xt,{ref:s,...y,...a,beforeEnter:b,beforeLeave:w})},theirProps:{},defaultTag:r.Fragment,features:Wt,visible:"visible"===d,name:"Transition"})))}),Xt=Te(function(e,t){var n,o;let{transition:i=!0,beforeEnter:a,afterEnter:l,beforeLeave:s,afterLeave:c,enter:d,enterFrom:f,enterTo:m,entered:p,leave:h,leaveFrom:v,leaveTo:g,...y}=e,[b,w]=(0,r.useState)(null),C=(0,r.useRef)(null),E=Dt(e),A=Ze(...E?[C,t,w]:null===t?[]:[t]),x=null==(n=y.unmount)||n?ke.Unmount:ke.Hidden,{show:k,appear:F,initial:S}=function(){let e=(0,r.useContext)(jt);if(null===e)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),[P,M]=(0,r.useState)(k?"visible":"hidden"),T=function(){let e=(0,r.useContext)(Ht);if(null===e)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),{register:N,unregister:R}=T;u(()=>N(C),[N,C]),u(()=>{if(x===ke.Hidden&&C.current)return k&&"visible"!==P?void M("visible"):O(P,{hidden:()=>R(C),visible:()=>N(C)})},[P,C,N,R,k,x]);let L=Ue();u(()=>{if(E&&L&&"visible"===P&&null===C.current)throw new Error("Did you forget to passthrough the `ref` to the actual DOM node?")},[C,P,L,E]);let I=S&&!F,$=F&&k&&S,D=(0,r.useRef)(!1),j=Bt(()=>{D.current||(M("hidden"),R(C))},T),_=V(e=>{D.current=!0;let t=e?"enter":"leave";j.onStart(C,t,e=>{"enter"===e?null==a||a():"leave"===e&&(null==s||s())})}),H=V(e=>{let t=e?"enter":"leave";D.current=!1,j.onStop(C,t,e=>{"enter"===e?null==l||l():"leave"===e&&(null==c||c())}),"leave"===t&&!zt(j)&&(M("hidden"),R(C))});(0,r.useEffect)(()=>{E&&i||(_(k),H(k))},[k,E,i]);let z=!(!i||!E||!L||I),[,B]=Vt(z,b,k,{start:_,end:H}),q=Ne({ref:A,className:(null==(o=Ae(y.className,$&&d,$&&f,B.enter&&d,B.enter&&B.closed&&f,B.enter&&!B.closed&&m,B.leave&&h,B.leave&&!B.closed&&v,B.leave&&B.closed&&g,!B.transition&&k&&p))?void 0:o.trim())||void 0,...$t(B)}),W=0;"visible"===P&&(W|=Je.Open),"hidden"===P&&(W|=Je.Closed),k&&"hidden"===P&&(W|=Je.Opening),!k&&"visible"===P&&(W|=Je.Closing);let U=Fe();return r.createElement(Ht.Provider,{value:j},r.createElement(et,{value:W},U({ourProps:q,theirProps:y,defaultTag:qt,features:Wt,visible:"visible"===P,name:"Transition.Child"})))}),Zt=Te(function(e,t){let n=null!==(0,r.useContext)(jt),o=null!==Qe();return r.createElement(r.Fragment,null,!n&&o?r.createElement(Ut,{ref:t,...e}):r.createElement(Xt,{ref:t,...e}))}),Gt=Object.assign(Ut,{Child:Zt,Root:Ut});var Yt=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(Yt||{}),Kt=(e=>(e[e.SetTitleId=0]="SetTitleId",e))(Kt||{});let Jt={0(e,t){return e.titleId===t.id?e:{...e,titleId:t.id}}},Qt=(0,r.createContext)(null);function en(e){let t=(0,r.useContext)(Qt);if(null===t){let t=new Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,en),t}return t}function tn(e,t){return O(t.type,Jt,e,t)}Qt.displayName="DialogContext";let nn=Te(function(e,t){let n=(0,r.useId)(),{id:o=`headlessui-dialog-${n}`,open:i,onClose:a,initialFocus:l,role:s="dialog",autoFocus:c=!0,__demoMode:d=!1,unmount:f=!1,...m}=e,p=(0,r.useRef)(!1);s="dialog"===s||"alertdialog"===s?s:(p.current||(p.current=!0,console.warn(`Invalid role [${s}] passed to <Dialog />. Only \`dialog\` and and \`alertdialog\` are supported. Using \`dialog\` instead.`)),"dialog");let h=Qe();void 0===i&&null!==h&&(i=(h&Je.Open)===Je.Open);let v=(0,r.useRef)(null),g=Ze(v,t),y=Ee(v),b=i?0:1,[w,C]=(0,r.useReducer)(tn,{titleId:null,descriptionId:null,panelRef:(0,r.createRef)()}),E=V(()=>a(!1)),A=V(e=>C({type:0,id:e})),x=!!Ue()&&0===b,[k,F]=Ot(),S={get current(){var e;return null!=(e=w.panelRef.current)?e:v.current}},P=je(),{resolveContainers:O}=function({defaultContainers:e=[],portals:t,mainTreeNode:n}={}){let r=Ee(n),o=V(()=>{var o,i;let a=[];for(let t of e)null!==t&&(G(t)?a.push(t):"current"in t&&G(t.current)&&a.push(t.current));if(null!=t&&t.current)for(let e of t.current)a.push(e);for(let e of null!=(o=null==r?void 0:r.querySelectorAll("html > *, body > *"))?o:[])e!==document.body&&e!==document.head&&G(e)&&"headlessui-portal-root"!==e.id&&(n&&(e.contains(n)||e.contains(null==(i=null==n?void 0:n.getRootNode())?void 0:i.host))||a.some(t=>e.contains(t))||a.push(e));return a});return{resolveContainers:o,contains:V(e=>o().some(t=>t.contains(e)))}}({mainTreeNode:P,portals:k,defaultContainers:[S]}),M=null!==h&&(h&Je.Closing)===Je.Closing;X(!d&&!M&&x,{allowed:V(()=>{var e,t;return[null!=(t=null==(e=v.current)?void 0:e.closest("[data-headlessui-portal]"))?t:null]}),disallowed:V(()=>{var e;return[null!=(e=null==P?void 0:P.closest("body > *:not(#headlessui-portal-root)"))?e:null]})});let T=I.get(null);u(()=>{if(x)return T.actions.push(o),()=>T.actions.pop(o)},[T,o,x]);let N=D(T,(0,r.useCallback)(e=>T.selectors.isTop(e,o),[T,o]));Ce(N,O,e=>{e.preventDefault(),E()}),H(N,null==y?void 0:y.defaultView,e=>{e.preventDefault(),e.stopPropagation(),document.activeElement&&"blur"in document.activeElement&&"function"==typeof document.activeElement.blur&&document.activeElement.blur(),E()}),We(!d&&!M&&x,y,O),ne(x,v,E);let[R,L]=st(),$=(0,r.useMemo)(()=>[{dialogState:b,close:E,setTitleId:A,unmount:f},w],[b,w,E,A,f]),j=(0,r.useMemo)(()=>({open:0===b}),[b]),_={ref:g,id:o,role:s,tabIndex:-1,"aria-modal":d?void 0:0===b||void 0,"aria-labelledby":w.titleId,"aria-describedby":R,unmount:f},z=!function(){var e;let[t]=(0,r.useState)(()=>"undefined"!=typeof window&&"function"==typeof window.matchMedia?window.matchMedia("(pointer: coarse)"):null),[n,o]=(0,r.useState)(null!=(e=null==t?void 0:t.matches)&&e);return u(()=>{if(t)return t.addEventListener("change",e),()=>t.removeEventListener("change",e);function e(e){o(e.matches)}},[t]),n}(),B=yt.None;x&&!d&&(B|=yt.RestoreFocus,B|=yt.TabLock,c&&(B|=yt.AutoFocus),z&&(B|=yt.InitialFocus));let q=Fe();return r.createElement(tt,null,r.createElement(rt,{force:!0},r.createElement(Nt,null,r.createElement(Qt.Provider,{value:$},r.createElement(Tt,{target:v},r.createElement(rt,{force:!1},r.createElement(L,{slot:j},r.createElement(F,null,r.createElement(wt,{initialFocus:l,initialFocusFallback:v,containers:O,features:B},r.createElement(Ye,{value:E},q({ourProps:_,theirProps:m,slot:j,defaultTag:rn,features:on,visible:0===b,name:"Dialog"})))))))))))}),rn="div",on=xe.RenderStrategy|xe.Static;let an=Te(function(e,t){let{transition:n=!1,open:o,...i}=e,a=Qe(),l=e.hasOwnProperty("open")||null!==a,s=e.hasOwnProperty("onClose");if(!l&&!s)throw new Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!l)throw new Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!s)throw new Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if(!a&&"boolean"!=typeof e.open)throw new Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${e.open}`);if("function"!=typeof e.onClose)throw new Error(`You provided an \`onClose\` prop to the \`Dialog\`, but the value is not a function. Received: ${e.onClose}`);return void 0===o&&!n||i.static?r.createElement(De,null,r.createElement(nn,{ref:t,open:o,...i})):r.createElement(De,null,r.createElement(Gt,{show:o,transition:n,unmount:i.unmount},r.createElement(nn,{ref:t,...i})))}),ln=Te(function(e,t){let n=(0,r.useId)(),{id:o=`headlessui-dialog-panel-${n}`,transition:i=!1,...a}=e,[{dialogState:l,unmount:s},u]=en("Dialog.Panel"),c=Ze(t,u.panelRef),d=(0,r.useMemo)(()=>({open:0===l}),[l]),f=V(e=>{e.stopPropagation()}),m={ref:c,id:o,onClick:f},p=i?Zt:r.Fragment,h=i?{unmount:s}:{},v=Fe();return r.createElement(p,{...h},v({ourProps:m,theirProps:a,slot:d,defaultTag:"div",name:"Dialog.Panel"}))}),sn=(Te(function(e,t){let{transition:n=!1,...o}=e,[{dialogState:i,unmount:a}]=en("Dialog.Backdrop"),l=(0,r.useMemo)(()=>({open:0===i}),[i]),s={ref:t,"aria-hidden":!0},u=n?Zt:r.Fragment,c=n?{unmount:a}:{},d=Fe();return r.createElement(u,{...c},d({ourProps:s,theirProps:o,slot:l,defaultTag:"div",name:"Dialog.Backdrop"}))}),Te(function(e,t){let n=(0,r.useId)(),{id:o=`headlessui-dialog-title-${n}`,...i}=e,[{dialogState:a,setTitleId:l}]=en("Dialog.Title"),s=Ze(t);(0,r.useEffect)(()=>(l(o),()=>l(null)),[o,l]);let u=(0,r.useMemo)(()=>({open:0===a}),[a]),c={ref:s,id:o};return Fe()({ourProps:c,theirProps:i,slot:u,defaultTag:"h2",name:"Dialog.Title"})})),un=Object.assign(an,{Panel:ln,Title:sn,Description:ct});var cn=n(8697),dn=n(2744),fn=n(5625),mn=(n(8686),n(4810)),pn=n(367),hn=n(2640),vn=n(5061),gn=n(6148),yn=n(1788);const bn=(0,yn.A)("Rocket",[["path",{d:"M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z",key:"m3kijz"}],["path",{d:"m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z",key:"1fmvmk"}],["path",{d:"M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0",key:"1f8sc4"}],["path",{d:"M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5",key:"qeys4"}]]);var wn=n(964),Cn=n(9644),En=n(9910),An=n(6647);const xn=[{name:"团队构建器",href:"/build",icon:hn.A,breadcrumbs:[{name:"团队构建器",href:"/build",current:!0}]},{name:"游乐场",href:"/",icon:vn.A,breadcrumbs:[{name:"游乐场",href:"/",current:!0}]},{name:"MCP (实验性)",href:"/mcp",icon:e=>{let{className:t}=e;return r.createElement(An.A,{size:6,icon:"mcp",className:t})},breadcrumbs:[{name:"MCP (实验性)",href:"/mcp",current:!0}]},{name:"画廊",href:"/gallery",icon:gn.A,breadcrumbs:[{name:"画廊",href:"/gallery",current:!0}]},{name:"部署",href:"/deploy",icon:bn,breadcrumbs:[{name:"部署",href:"/deploy",current:!0}]}],kn=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter(Boolean).join(" ")};var Fn=e=>{let{link:t,meta:n,isMobile:o}=e;const{sidebar:i,setHeader:a,setSidebarState:l}=(0,fn.J)(),{isExpanded:s}=i;r.useEffect(()=>{c(t)},[t]);const u=o||s,c=e=>{const t=xn.find(t=>t.href===e);t?a({title:t.name,breadcrumbs:t.breadcrumbs}):"/settings"===e&&a({title:"设置",breadcrumbs:[{name:"设置",href:"/settings",current:!0}]})};return r.createElement("div",{className:kn("flex grow   z-50  flex-col gap-y-5 overflow-y-auto border-r border-secondary bg-primary","transition-all duration-300 ease-in-out",u?"w-72 px-6":"w-16 px-2")},r.createElement("div",{className:"flex h-16 items-center "+(u?"gap-x-3":"ml-2")},r.createElement(mn.Link,{to:"/",onClick:()=>c("/"),className:"w-8 text-right text-accent hover:opacity-80 transition-opacity"},r.createElement(An.A,{icon:"app",size:8})),u&&r.createElement("div",{className:"flex flex-col",style:{minWidth:"200px"}},r.createElement("span",{className:"text-base font-semibold text-primary"},null==n?void 0:n.title),r.createElement("span",{className:"text-xs text-secondary"},null==n?void 0:n.description))),r.createElement("nav",{className:"flex flex-1 flex-col"},r.createElement("ul",{role:"list",className:"flex flex-1 flex-col gap-y-7"},r.createElement("li",null,r.createElement("ul",{role:"list",className:kn("-mx-2 space-y-1",!u&&"items-center")},xn.map(e=>{const n=e.href===t,i=e.icon,l=r.createElement("div",{className:"relative"},n&&r.createElement("div",{className:"bg-accent absolute top-1 left-0.5 z-50 h-8 w-1 bg-opacity-80  rounded"}," "),r.createElement(mn.Link,{to:e.href,onClick:()=>(e=>{a({title:e.name,breadcrumbs:e.breadcrumbs})})(e),className:kn("group  ml-1 flex gap-x-3 rounded-md mr-2  p-2 text-sm font-medium",!u&&"justify-center",n?"bg-secondary text-primary ":"text-secondary hover:bg-tertiary hover:text-accent")}," ",r.createElement(i,{className:kn("h-6 w-6 shrink-0",n?"text-accent":"text-secondary group-hover:text-accent")}),u&&e.name));return r.createElement("li",{key:e.name},u||o?l:r.createElement(pn.A,{title:e.name,placement:"right"},l))}))),r.createElement("li",{className:kn("mt-auto -mx-2 mb-4",!u&&"flex flex-col items-center gap-1")},u||o?r.createElement("div",{className:"flex items-center gap-2"},r.createElement("div",{className:"w-full  "},r.createElement("div",{className:""}," ",r.createElement(mn.Link,{to:"/settings",onClick:()=>a({title:"设置",breadcrumbs:[{name:"设置",href:"/settings",current:!0}]}),className:"group flex flex-1 gap-x-3 rounded-md p-2 text-sm font-medium text-primary hover:text-accent hover:bg-secondary"},r.createElement(wn.A,{className:"h-6 w-6 shrink-0 text-secondary group-hover:text-accent"}),u&&"设置"))),r.createElement("div",{className:"hidden md:block"},r.createElement(pn.A,{title:""+(s?"关闭侧边栏":"打开侧边栏"),placement:"right"},r.createElement("button",{onClick:()=>l({isExpanded:!s}),className:"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50"},s?r.createElement(Cn.A,{strokeWidth:1.5,className:"h-6 w-6"}):r.createElement(En.A,{strokeWidth:1.5,className:"h-6 w-6"}))))):r.createElement(r.Fragment,null,r.createElement(pn.A,{title:"设置",placement:"right"},r.createElement(mn.Link,{to:"/settings",onClick:()=>a({title:"设置",breadcrumbs:[{name:"设置",href:"/settings",current:!0}]}),className:"group   flex gap-x-3 rounded-md p-2 text-sm font-medium text-primary hover:text-accent hover:bg-secondary justify-center"},r.createElement(wn.A,{className:"h-6 w-6 shrink-0 text-secondary group-hover:text-accent"}))),r.createElement("div",{className:"hidden md:block"},r.createElement(pn.A,{title:s?"Close Sidebar":"打开侧边栏",placement:"right"},r.createElement("button",{onClick:()=>l({isExpanded:!s}),className:"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50"},s?r.createElement(Cn.A,{strokeWidth:1.5,className:"h-6 w-6"}):r.createElement(En.A,{strokeWidth:1.5,className:"h-6 w-6"})))))))))};const Sn="undefined"!=typeof document?r.useLayoutEffect:()=>{};var Pn;const On=null!==(Pn=r.useInsertionEffect)&&void 0!==Pn?Pn:Sn;function Mn(e){const t=(0,r.useRef)(null);return On(()=>{t.current=e},[e]),(0,r.useCallback)((...e)=>{const n=t.current;return null==n?void 0:n(...e)},[])}function Tn(e){let t=e;return t.nativeEvent=e,t.isDefaultPrevented=()=>t.defaultPrevented,t.isPropagationStopped=()=>t.cancelBubble,t.persist=()=>{},t}function Nn(e,t){Object.defineProperty(e,"target",{value:t}),Object.defineProperty(e,"currentTarget",{value:t})}function Rn(e){let t=(0,r.useRef)({isFocused:!1,observer:null});Sn(()=>{const e=t.current;return()=>{e.observer&&(e.observer.disconnect(),e.observer=null)}},[]);let n=Mn(t=>{null==e||e(t)});return(0,r.useCallback)(e=>{if(e.target instanceof HTMLButtonElement||e.target instanceof HTMLInputElement||e.target instanceof HTMLTextAreaElement||e.target instanceof HTMLSelectElement){t.current.isFocused=!0;let r=e.target,o=e=>{if(t.current.isFocused=!1,r.disabled){let t=Tn(e);n(t)}t.current.observer&&(t.current.observer.disconnect(),t.current.observer=null)};r.addEventListener("focusout",o,{once:!0}),t.current.observer=new MutationObserver(()=>{if(t.current.isFocused&&r.disabled){var e;null===(e=t.current.observer)||void 0===e||e.disconnect();let n=r===document.activeElement?null:document.activeElement;r.dispatchEvent(new FocusEvent("blur",{relatedTarget:n})),r.dispatchEvent(new FocusEvent("focusout",{bubbles:!0,relatedTarget:n}))}}),t.current.observer.observe(r,{attributes:!0,attributeFilter:["disabled"]})}},[n])}let Ln=!1;function In(e){var t;if("undefined"==typeof window||null==window.navigator)return!1;let n=null===(t=window.navigator.userAgentData)||void 0===t?void 0:t.brands;return Array.isArray(n)&&n.some(t=>e.test(t.brand))||e.test(window.navigator.userAgent)}function $n(e){var t;return"undefined"!=typeof window&&null!=window.navigator&&e.test((null===(t=window.navigator.userAgentData)||void 0===t?void 0:t.platform)||window.navigator.platform)}function Vn(e){let t=null;return()=>(null==t&&(t=e()),t)}const Dn=Vn(function(){return $n(/^Mac/i)}),jn=Vn(function(){return $n(/^iPhone/i)}),_n=Vn(function(){return $n(/^iPad/i)||Dn()&&navigator.maxTouchPoints>1}),Hn=Vn(function(){return jn()||_n()}),zn=(Vn(function(){return Dn()||Hn()}),Vn(function(){return In(/AppleWebKit/i)&&!zn()}),Vn(function(){return In(/Chrome/i)})),Bn=Vn(function(){return In(/Android/i)});Vn(function(){return In(/Firefox/i)});const qn=e=>{var t;return null!==(t=null==e?void 0:e.ownerDocument)&&void 0!==t?t:document},Wn=e=>{if(e&&"window"in e&&e.window===e)return e;return qn(e).defaultView||window};function Un(e){return null!==(t=e)&&"object"==typeof t&&"nodeType"in t&&"number"==typeof t.nodeType&&e.nodeType===Node.DOCUMENT_FRAGMENT_NODE&&"host"in e;var t}let Xn=null,Zn=new Set,Gn=new Map,Yn=!1,Kn=!1;const Jn={Tab:!0,Escape:!0};function Qn(e,t){for(let n of Zn)n(e,t)}function er(e){Yn=!0,function(e){return!(e.metaKey||!Dn()&&e.altKey||e.ctrlKey||"Control"===e.key||"Shift"===e.key||"Meta"===e.key)}(e)&&(Xn="keyboard",Qn("keyboard",e))}function tr(e){Xn="pointer","mousedown"!==e.type&&"pointerdown"!==e.type||(Yn=!0,Qn("pointer",e))}function nr(e){var t;(0===(t=e).mozInputSource&&t.isTrusted||(Bn()&&t.pointerType?"click"===t.type&&1===t.buttons:0===t.detail&&!t.pointerType))&&(Yn=!0,Xn="virtual")}function rr(e){e.target!==window&&e.target!==document&&!Ln&&e.isTrusted&&(Yn||Kn||(Xn="virtual",Qn("virtual",e)),Yn=!1,Kn=!1)}function or(){Ln||(Yn=!1,Kn=!0)}function ir(e){if("undefined"==typeof window||"undefined"==typeof document||Gn.get(Wn(e)))return;const t=Wn(e),n=qn(e);let r=t.HTMLElement.prototype.focus;t.HTMLElement.prototype.focus=function(){Yn=!0,r.apply(this,arguments)},n.addEventListener("keydown",er,!0),n.addEventListener("keyup",er,!0),n.addEventListener("click",nr,!0),t.addEventListener("focus",rr,!0),t.addEventListener("blur",or,!1),"undefined"!=typeof PointerEvent&&(n.addEventListener("pointerdown",tr,!0),n.addEventListener("pointermove",tr,!0),n.addEventListener("pointerup",tr,!0)),t.addEventListener("beforeunload",()=>{ar(e)},{once:!0}),Gn.set(t,{focus:r})}const ar=(e,t)=>{const n=Wn(e),r=qn(e);t&&r.removeEventListener("DOMContentLoaded",t),Gn.has(n)&&(n.HTMLElement.prototype.focus=Gn.get(n).focus,r.removeEventListener("keydown",er,!0),r.removeEventListener("keyup",er,!0),r.removeEventListener("click",nr,!0),n.removeEventListener("focus",rr,!0),n.removeEventListener("blur",or,!1),"undefined"!=typeof PointerEvent&&(r.removeEventListener("pointerdown",tr,!0),r.removeEventListener("pointermove",tr,!0),r.removeEventListener("pointerup",tr,!0)),Gn.delete(n))};function lr(){return"pointer"!==Xn}"undefined"!=typeof document&&function(e){const t=qn(e);let n;"loading"!==t.readyState?ir(e):(n=()=>{ir(e)},t.addEventListener("DOMContentLoaded",n))}();const sr=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]);function ur(e,t,n){ir(),(0,r.useEffect)(()=>{let t=(t,r)=>{(function(e,t,n){let r=qn(null==n?void 0:n.target);const o="undefined"!=typeof window?Wn(null==n?void 0:n.target).HTMLInputElement:HTMLInputElement,i="undefined"!=typeof window?Wn(null==n?void 0:n.target).HTMLTextAreaElement:HTMLTextAreaElement,a="undefined"!=typeof window?Wn(null==n?void 0:n.target).HTMLElement:HTMLElement,l="undefined"!=typeof window?Wn(null==n?void 0:n.target).KeyboardEvent:KeyboardEvent;return!((e=e||r.activeElement instanceof o&&!sr.has(r.activeElement.type)||r.activeElement instanceof i||r.activeElement instanceof a&&r.activeElement.isContentEditable)&&"keyboard"===t&&n instanceof l&&!Jn[n.key])})(!!(null==n?void 0:n.isTextInput),t,r)&&e(lr())};return Zn.add(t),()=>{Zn.delete(t)}},t)}let cr=!1;function dr(){return cr}function fr(e,t){if(!dr())return!(!t||!e)&&e.contains(t);if(!e||!t)return!1;let n=t;for(;null!==n;){if(n===e)return!0;n="SLOT"===n.tagName&&n.assignedSlot?n.assignedSlot.parentNode:Un(n)?n.host:n.parentNode}return!1}const mr=(e=document)=>{var t;if(!dr())return e.activeElement;let n=e.activeElement;for(;n&&"shadowRoot"in n&&(null===(t=n.shadowRoot)||void 0===t?void 0:t.activeElement);)n=n.shadowRoot.activeElement;return n};function pr(e){return dr()&&e.target.shadowRoot&&e.composedPath?e.composedPath()[0]:e.target}function hr(e){let{isDisabled:t,onFocus:n,onBlur:o,onFocusChange:i}=e;const a=(0,r.useCallback)(e=>{if(e.target===e.currentTarget)return o&&o(e),i&&i(!1),!0},[o,i]),l=Rn(a),s=(0,r.useCallback)(e=>{const t=qn(e.target),r=t?mr(t):mr();e.target===e.currentTarget&&r===pr(e.nativeEvent)&&(n&&n(e),i&&i(!0),l(e))},[i,n,l]);return{focusProps:{onFocus:!t&&(n||i||o)?s:void 0,onBlur:t||!o&&!i?void 0:a}}}function vr(){let e=(0,r.useRef)(new Map),t=(0,r.useCallback)((t,n,r,o)=>{let i=(null==o?void 0:o.once)?(...t)=>{e.current.delete(r),r(...t)}:r;e.current.set(r,{type:n,eventTarget:t,fn:i,options:o}),t.addEventListener(n,i,o)},[]),n=(0,r.useCallback)((t,n,r,o)=>{var i;let a=(null===(i=e.current.get(r))||void 0===i?void 0:i.fn)||r;t.removeEventListener(n,a,o),e.current.delete(r)},[]),o=(0,r.useCallback)(()=>{e.current.forEach((e,t)=>{n(e.eventTarget,e.type,t,e.options)})},[n]);return(0,r.useEffect)(()=>o,[o]),{addGlobalListener:t,removeGlobalListener:n,removeAllGlobalListeners:o}}function gr(e){let{isDisabled:t,onBlurWithin:n,onFocusWithin:o,onFocusWithinChange:i}=e,a=(0,r.useRef)({isFocusWithin:!1}),{addGlobalListener:l,removeAllGlobalListeners:s}=vr(),u=(0,r.useCallback)(e=>{e.currentTarget.contains(e.target)&&a.current.isFocusWithin&&!e.currentTarget.contains(e.relatedTarget)&&(a.current.isFocusWithin=!1,s(),n&&n(e),i&&i(!1))},[n,i,a,s]),c=Rn(u),d=(0,r.useCallback)(e=>{if(!e.currentTarget.contains(e.target))return;const t=qn(e.target),n=mr(t);if(!a.current.isFocusWithin&&n===pr(e.nativeEvent)){o&&o(e),i&&i(!0),a.current.isFocusWithin=!0,c(e);let n=e.currentTarget;l(t,"focus",e=>{if(a.current.isFocusWithin&&!fr(n,e.target)){let r=new t.defaultView.FocusEvent("blur",{relatedTarget:e.target});Nn(r,n);let o=Tn(r);u(o)}},{capture:!0})}},[o,i,c,l,u]);return t?{focusWithinProps:{onFocus:void 0,onBlur:void 0}}:{focusWithinProps:{onFocus:d,onBlur:u}}}function yr(e={}){let{autoFocus:t=!1,isTextInput:n,within:o}=e,i=(0,r.useRef)({isFocused:!1,isFocusVisible:t||lr()}),[a,l]=(0,r.useState)(!1),[s,u]=(0,r.useState)(()=>i.current.isFocused&&i.current.isFocusVisible),c=(0,r.useCallback)(()=>u(i.current.isFocused&&i.current.isFocusVisible),[]),d=(0,r.useCallback)(e=>{i.current.isFocused=e,l(e),c()},[c]);ur(e=>{i.current.isFocusVisible=e,c()},[],{isTextInput:n});let{focusProps:f}=hr({isDisabled:o,onFocusChange:d}),{focusWithinProps:m}=gr({isDisabled:!o,onFocusWithinChange:d});return{isFocused:a,isFocusVisible:s,focusProps:o?m:f}}let br=!1,wr=0;function Cr(e){"touch"===e.pointerType&&(br=!0,setTimeout(()=>{br=!1},50))}function Er(){if("undefined"!=typeof document)return 0===wr&&"undefined"!=typeof PointerEvent&&document.addEventListener("pointerup",Cr),wr++,()=>{wr--,wr>0||"undefined"!=typeof PointerEvent&&document.removeEventListener("pointerup",Cr)}}function Ar(e){let{onHoverStart:t,onHoverChange:n,onHoverEnd:o,isDisabled:i}=e,[a,l]=(0,r.useState)(!1),s=(0,r.useRef)({isHovered:!1,ignoreEmulatedMouseEvents:!1,pointerType:"",target:null}).current;(0,r.useEffect)(Er,[]);let{addGlobalListener:u,removeAllGlobalListeners:c}=vr(),{hoverProps:d,triggerHoverEnd:f}=(0,r.useMemo)(()=>{let e=(e,t)=>{let r=s.target;s.pointerType="",s.target=null,"touch"!==t&&s.isHovered&&r&&(s.isHovered=!1,c(),o&&o({type:"hoverend",target:r,pointerType:t}),n&&n(!1),l(!1))},r={};return"undefined"!=typeof PointerEvent&&(r.onPointerEnter=r=>{br&&"mouse"===r.pointerType||((r,o)=>{if(s.pointerType=o,i||"touch"===o||s.isHovered||!r.currentTarget.contains(r.target))return;s.isHovered=!0;let a=r.currentTarget;s.target=a,u(qn(r.target),"pointerover",t=>{s.isHovered&&s.target&&!fr(s.target,t.target)&&e(t,t.pointerType)},{capture:!0}),t&&t({type:"hoverstart",target:a,pointerType:o}),n&&n(!0),l(!0)})(r,r.pointerType)},r.onPointerLeave=t=>{!i&&t.currentTarget.contains(t.target)&&e(t,t.pointerType)}),{hoverProps:r,triggerHoverEnd:e}},[t,n,o,i,s,u,c]);return(0,r.useEffect)(()=>{i&&f({currentTarget:s.target},s.pointerType)},[i]),{hoverProps:d,isHovered:a}}function xr(e,t=!1){let[n,o]=(0,r.useReducer)(()=>({}),{}),i=(0,r.useMemo)(()=>function(e){if(null===e)return{width:0,height:0};let{width:t,height:n}=e.getBoundingClientRect();return{width:t,height:n}}(e),[e,n]);return u(()=>{if(!e)return;let t=new ResizeObserver(o);return t.observe(e),()=>{t.disconnect()}},[e]),t?{width:`${i.width}px`,height:`${i.height}px`}:i}var kr=(e=>(e[e.Ignore=0]="Ignore",e[e.Select=1]="Select",e[e.Close=2]="Close",e))(kr||{});const Fr={Ignore:{kind:0},Select:e=>({kind:1,target:e}),Close:{kind:2}};function Sr(e,t){return(0,r.useMemo)(()=>{var n;if(e.type)return e.type;let r=null!=(n=e.as)?n:"button";return"string"==typeof r&&"button"===r.toLowerCase()||"BUTTON"===(null==t?void 0:t.tagName)&&!t.hasAttribute("type")?"button":void 0},[e.type,e.as,t])}let Pr=/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g;function Or(e){var t,n;let r=null!=(t=e.innerText)?t:"",o=e.cloneNode(!0);if(!Y(o))return r;let i=!1;for(let l of o.querySelectorAll('[hidden],[aria-hidden],[role="img"]'))l.remove(),i=!0;let a=i?null!=(n=o.innerText)?n:"":r;return Pr.test(a)&&(a=a.replace(Pr,"")),a}function Mr(e){let t=(0,r.useRef)(""),n=(0,r.useRef)("");return V(()=>{let r=e.current;if(!r)return"";let o=r.innerText;if(t.current===o)return n.current;let i=function(e){let t=e.getAttribute("aria-label");if("string"==typeof t)return t.trim();let n=e.getAttribute("aria-labelledby");if(n){let e=n.split(" ").map(e=>{let t=document.getElementById(e);if(t){let e=t.getAttribute("aria-label");return"string"==typeof e?e.trim():Or(t).trim()}return null}).filter(Boolean);if(e.length>0)return e.join(", ")}return Or(e).trim()}(r).trim().toLowerCase();return t.current=o,n.current=i,i})}function Tr(e){return[e.screenX,e.screenY]}var Nr=n(8689),Rr=n(4743),Lr=n(6885);let Ir=(0,r.createContext)({styles:void 0,setReference:()=>{},setFloating:()=>{},getReferenceProps:()=>({}),getFloatingProps:()=>({}),slot:{}});Ir.displayName="FloatingContext";let $r=(0,r.createContext)(null);$r.displayName="PlacementContext";function Vr({children:e,enabled:t=!0}){let[n,o]=(0,r.useState)(null),[i,a]=(0,r.useState)(0),l=(0,r.useRef)(null),[s,c]=(0,r.useState)(null);!function(e){u(()=>{if(!e)return;let t=new MutationObserver(()=>{let t=window.getComputedStyle(e).maxHeight,n=parseFloat(t);if(isNaN(n))return;let r=parseInt(t);isNaN(r)||n!==r&&(e.style.maxHeight=`${Math.ceil(n)}px`)});return t.observe(e,{attributes:!0,attributeFilter:["style"]}),()=>{t.disconnect()}},[e])}(s);let d=t&&null!==n&&null!==s,{to:f="bottom",gap:m=0,offset:p=0,padding:h=0,inner:v}=function(e,t){var n,r,o;let i=Dr(null!=(n=null==e?void 0:e.gap)?n:"var(--anchor-gap, 0)",t),a=Dr(null!=(r=null==e?void 0:e.offset)?r:"var(--anchor-offset, 0)",t),l=Dr(null!=(o=null==e?void 0:e.padding)?o:"var(--anchor-padding, 0)",t);return{...e,gap:i,offset:a,padding:l}}(n,s),[g,y="center"]=f.split(" ");u(()=>{d&&a(0)},[d]);let{refs:b,floatingStyles:w,context:C}=(0,Nr.we)({open:d,placement:"selection"===g?"center"===y?"bottom":`bottom-${y}`:"center"===y?`${g}`:`${g}-${y}`,strategy:"absolute",transform:!1,middleware:[(0,Rr.cY)({mainAxis:"selection"===g?0:m,crossAxis:p}),(0,Rr.BN)({padding:h}),"selection"!==g&&(0,Rr.UU)({padding:h}),"selection"===g&&v?(0,Nr.vW)({...v,padding:h,overflowRef:l,offset:i,minItemsVisible:4,referenceOverflowThreshold:h,onFallbackChange(e){var t,n;if(!e)return;let r=C.elements.floating;if(!r)return;let o=parseFloat(getComputedStyle(r).scrollPaddingBottom)||0,i=Math.min(4,r.childElementCount),l=0,s=0;for(let a of null!=(n=null==(t=C.elements.floating)?void 0:t.childNodes)?n:[])if(Y(a)){let e=a.offsetTop,t=e+a.clientHeight+o,n=r.scrollTop,u=n+r.clientHeight;if(!(e>=n&&t<=u)){s=Math.max(0,Math.min(t,u)-Math.max(e,n)),l=a.clientHeight;break}i--}i>=1&&a(e=>{let t=l*i-s+o;return e>=t?e:t})}}):null,(0,Rr.Ej)({padding:h,apply({availableWidth:e,availableHeight:t,elements:n}){Object.assign(n.floating.style,{overflow:"auto",maxWidth:`${e}px`,maxHeight:`min(var(--anchor-max-height, 100vh), ${t}px)`})}})].filter(Boolean),whileElementsMounted:Lr.ll}),[E=g,A=y]=C.placement.split("-");"selection"===g&&(E="selection");let x=(0,r.useMemo)(()=>({anchor:[E,A].filter(Boolean).join(" ")}),[E,A]),k=(0,Nr.Zx)(C,{overflowRef:l,onChange:a}),{getReferenceProps:F,getFloatingProps:S}=(0,Nr.bv)([k]),P=V(e=>{c(e),b.setFloating(e)});return r.createElement($r.Provider,{value:o},r.createElement(Ir.Provider,{value:{setFloating:P,setReference:b.setReference,styles:w,getReferenceProps:F,getFloatingProps:S,slot:x}},e))}function Dr(e,t,n=void 0){let o=dt(),i=V((e,t)=>{if(null==e)return[n,null];if("number"==typeof e)return[e,null];if("string"==typeof e){if(!t)return[n,null];let r=_r(e,t);return[r,n=>{let i=jr(e);{let a=i.map(e=>window.getComputedStyle(t).getPropertyValue(e));o.requestAnimationFrame(function l(){o.nextFrame(l);let s=!1;for(let[e,n]of i.entries()){let r=window.getComputedStyle(t).getPropertyValue(n);if(a[e]!==r){a[e]=r,s=!0;break}}if(!s)return;let u=_r(e,t);r!==u&&(n(u),r=u)})}return o.dispose}]}return[n,null]}),a=(0,r.useMemo)(()=>i(e,t)[0],[e,t]),[l=a,s]=(0,r.useState)();return u(()=>{let[n,r]=i(e,t);if(s(n),r)return r(s)},[e,t]),l}function jr(e){let t=/var\((.*)\)/.exec(e);if(t){let e=t[1].indexOf(",");if(-1===e)return[t[1]];let n=t[1].slice(0,e).trim(),r=t[1].slice(e+1).trim();return r?[n,...jr(r)]:[n]}return[]}function _r(e,t){let n=document.createElement("div");t.appendChild(n),n.style.setProperty("margin-top","0px","important"),n.style.setProperty("margin-top",e,"important");let r=parseFloat(window.getComputedStyle(n).marginTop)||0;return t.removeChild(n),r}function Hr(e){let t=e.parentElement,n=null;for(;t&&!ee(t);)te(t)&&(n=t),t=t.parentElement;let r=""===(null==t?void 0:t.getAttribute("disabled"));return(!r||!function(e){if(!e)return!1;let t=e.previousElementSibling;for(;null!==t;){if(te(t))return!1;t=t.previousElementSibling}return!0}(n))&&r}var zr=(e=>(e[e.First=0]="First",e[e.Previous=1]="Previous",e[e.Next=2]="Next",e[e.Last=3]="Last",e[e.Specific=4]="Specific",e[e.Nothing=5]="Nothing",e))(zr||{});function Br(e,t){let n=t.resolveItems();if(n.length<=0)return null;let r=t.resolveActiveIndex(),o=null!=r?r:-1;switch(e.focus){case 0:for(let e=0;e<n.length;++e)if(!t.resolveDisabled(n[e],e,n))return e;return r;case 1:-1===o&&(o=n.length);for(let e=o-1;e>=0;--e)if(!t.resolveDisabled(n[e],e,n))return e;return r;case 2:for(let e=o+1;e<n.length;++e)if(!t.resolveDisabled(n[e],e,n))return e;return r;case 3:for(let e=n.length-1;e>=0;--e)if(!t.resolveDisabled(n[e],e,n))return e;return r;case 4:for(let r=0;r<n.length;++r)if(t.resolveId(n[r],r,n)===e.id)return r;return r;case 5:return null;default:!function(e){throw new Error("Unexpected object: "+e)}(e)}}let qr=(0,r.createContext)(void 0);let Wr=(0,r.createContext)(null);function Ur(){let e=(0,r.useContext)(Wr);if(null===e){let e=new Error("You used a <Label /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(e,Ur),e}return e}function Xr({inherit:e=!1}={}){let t=function(e){var t,n,o;let i=null!=(n=null==(t=(0,r.useContext)(Wr))?void 0:t.value)?n:void 0;return(null!=(o=null==e?void 0:e.length)?o:0)>0?[i,...e].filter(Boolean).join(" "):i}(),[n,o]=(0,r.useState)([]),i=e?[t,...n].filter(Boolean):n;return[i.length>0?i.join(" "):void 0,(0,r.useMemo)(()=>function(e){let t=V(e=>(o(t=>[...t,e]),()=>o(t=>{let n=t.slice(),r=n.indexOf(e);return-1!==r&&n.splice(r,1),n}))),n=(0,r.useMemo)(()=>({register:t,slot:e.slot,name:e.name,props:e.props,value:e.value}),[t,e.slot,e.name,e.props,e.value]);return r.createElement(Wr.Provider,{value:n},e.children)},[o])]}Wr.displayName="LabelContext";let Zr=Te(function(e,t){var n;let o=(0,r.useId)(),i=Ur(),a=(0,r.useContext)(qr),l=it(),{id:s=`headlessui-label-${o}`,htmlFor:c=(null!=a?a:null==(n=i.props)?void 0:n.htmlFor),passive:d=!1,...f}=e,m=Ze(t);u(()=>i.register(s),[s,i.register]);let p=V(e=>{let t=e.currentTarget;if((e.target===e.currentTarget||!function(e){return!!G(e)&&e.matches('a[href],audio[controls],button,details,embed,iframe,img[usemap],input:not([type="hidden"]),label,select,textarea,video[controls]')}(e.target))&&(Q(t)&&e.preventDefault(),i.props&&"onClick"in i.props&&"function"==typeof i.props.onClick&&i.props.onClick(e),Q(t))){let e=document.getElementById(t.htmlFor);if(e){let t=e.getAttribute("disabled");if("true"===t||""===t)return;let n=e.getAttribute("aria-disabled");if("true"===n||""===n)return;(J(e)&&("file"===e.type||"radio"===e.type||"checkbox"===e.type)||"radio"===e.role||"checkbox"===e.role||"switch"===e.role)&&e.click(),e.focus({preventScroll:!0})}}}),h=l||!1,v=(0,r.useMemo)(()=>({...i.slot,disabled:h}),[i.slot,h]),g={ref:m,...i.props,id:s,htmlFor:c,onClick:p};return d&&("onClick"in g&&(delete g.htmlFor,delete g.onClick),"onClick"in f&&delete f.onClick),Fe()({ourProps:g,theirProps:f,slot:v,defaultTag:c?"label":"div",name:i.name||"Label"})});Object.assign(Zr,{});var Gr=Object.defineProperty,Yr=(e,t,n)=>(((e,t,n)=>{t in e?Gr(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n})(e,"symbol"!=typeof t?t+"":t,n),n),Kr=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(Kr||{}),Jr=(e=>(e[e.Pointer=0]="Pointer",e[e.Other=1]="Other",e))(Jr||{}),Qr=(e=>(e[e.OpenMenu=0]="OpenMenu",e[e.CloseMenu=1]="CloseMenu",e[e.GoToItem=2]="GoToItem",e[e.Search=3]="Search",e[e.ClearSearch=4]="ClearSearch",e[e.RegisterItems=5]="RegisterItems",e[e.UnregisterItems=6]="UnregisterItems",e[e.SetButtonElement=7]="SetButtonElement",e[e.SetItemsElement=8]="SetItemsElement",e[e.SortItems=9]="SortItems",e))(Qr||{});function eo(e,t=e=>e){let n=null!==e.activeItemIndex?e.items[e.activeItemIndex]:null,r=he(t(e.items.slice()),e=>e.dataRef.current.domRef.current),o=n?r.indexOf(n):null;return-1===o&&(o=null),{items:r,activeItemIndex:o}}let to={1(e){return 1===e.menuState?e:{...e,activeItemIndex:null,pendingFocus:{focus:zr.Nothing},menuState:1}},0(e,t){return 0===e.menuState?e:{...e,__demoMode:!1,pendingFocus:t.focus,menuState:0}},2:(e,t)=>{var n,r,o,i,a;if(1===e.menuState)return e;let l={...e,searchQuery:"",activationTrigger:null!=(n=t.trigger)?n:1,__demoMode:!1};if(t.focus===zr.Nothing)return{...l,activeItemIndex:null};if(t.focus===zr.Specific)return{...l,activeItemIndex:e.items.findIndex(e=>e.id===t.id)};if(t.focus===zr.Previous){let n=e.activeItemIndex;if(null!==n){let i=e.items[n].dataRef.current.domRef,a=Br(t,{resolveItems:()=>e.items,resolveActiveIndex:()=>e.activeItemIndex,resolveId:e=>e.id,resolveDisabled:e=>e.dataRef.current.disabled});if(null!==a){let t=e.items[a].dataRef.current.domRef;if((null==(r=i.current)?void 0:r.previousElementSibling)===t.current||null===(null==(o=t.current)?void 0:o.previousElementSibling))return{...l,activeItemIndex:a}}}}else if(t.focus===zr.Next){let n=e.activeItemIndex;if(null!==n){let r=e.items[n].dataRef.current.domRef,o=Br(t,{resolveItems:()=>e.items,resolveActiveIndex:()=>e.activeItemIndex,resolveId:e=>e.id,resolveDisabled:e=>e.dataRef.current.disabled});if(null!==o){let t=e.items[o].dataRef.current.domRef;if((null==(i=r.current)?void 0:i.nextElementSibling)===t.current||null===(null==(a=t.current)?void 0:a.nextElementSibling))return{...l,activeItemIndex:o}}}}let s=eo(e),u=Br(t,{resolveItems:()=>s.items,resolveActiveIndex:()=>s.activeItemIndex,resolveId:e=>e.id,resolveDisabled:e=>e.dataRef.current.disabled});return{...l,...s,activeItemIndex:u}},3:(e,t)=>{let n=""!==e.searchQuery?0:1,r=e.searchQuery+t.value.toLowerCase(),o=(null!==e.activeItemIndex?e.items.slice(e.activeItemIndex+n).concat(e.items.slice(0,e.activeItemIndex+n)):e.items).find(e=>{var t;return(null==(t=e.dataRef.current.textValue)?void 0:t.startsWith(r))&&!e.dataRef.current.disabled}),i=o?e.items.indexOf(o):-1;return-1===i||i===e.activeItemIndex?{...e,searchQuery:r}:{...e,searchQuery:r,activeItemIndex:i,activationTrigger:1}},4(e){return""===e.searchQuery?e:{...e,searchQuery:"",searchActiveItemIndex:null}},5:(e,t)=>{let n=e.items.concat(t.items.map(e=>e)),r=e.activeItemIndex;return e.pendingFocus.focus!==zr.Nothing&&(r=Br(e.pendingFocus,{resolveItems:()=>n,resolveActiveIndex:()=>e.activeItemIndex,resolveId:e=>e.id,resolveDisabled:e=>e.dataRef.current.disabled})),{...e,items:n,activeItemIndex:r,pendingFocus:{focus:zr.Nothing},pendingShouldSort:!0}},6:(e,t)=>{let n=e.items,r=[],o=new Set(t.items);for(let[i,a]of n.entries())if(o.has(a.id)&&(r.push(i),o.delete(a.id),0===o.size))break;if(r.length>0){n=n.slice();for(let e of r.reverse())n.splice(e,1)}return{...e,items:n,activationTrigger:1}},7:(e,t)=>e.buttonElement===t.element?e:{...e,buttonElement:t.element},8:(e,t)=>e.itemsElement===t.element?e:{...e,itemsElement:t.element},9:e=>e.pendingShouldSort?{...e,...eo(e),pendingShouldSort:!1}:e};class no extends x{constructor(e){super(e),Yr(this,"actions",{registerItem:P(()=>{let e=[],t=new Set;return[(n,r)=>{t.has(r)||(t.add(r),e.push({id:n,dataRef:r}))},()=>(t.clear(),this.send({type:5,items:e.splice(0)}))]}),unregisterItem:P(()=>{let e=[];return[t=>e.push(t),()=>this.send({type:6,items:e.splice(0)})]})}),Yr(this,"selectors",{activeDescendantId(e){var t;let n=e.activeItemIndex,r=e.items;return null===n||null==(t=r[n])?void 0:t.id},isActive(e,t){var n;let r=e.activeItemIndex,o=e.items;return null!==r&&(null==(n=o[r])?void 0:n.id)===t},shouldScrollIntoView(e,t){return!e.__demoMode&&0===e.menuState&&0!==e.activationTrigger&&this.isActive(e,t)}}),this.on(5,()=>{this.disposables.requestAnimationFrame(()=>{this.send({type:9})})});{let e=this.state.id,t=I.get(null);this.disposables.add(t.on(N.Push,n=>{!t.selectors.isTop(n,e)&&0===this.state.menuState&&this.send({type:1})})),this.on(0,()=>t.actions.push(e)),this.on(1,()=>t.actions.pop(e))}}static new({id:e,__demoMode:t=!1}){return new no({id:e,__demoMode:t,menuState:t?0:1,buttonElement:null,itemsElement:null,items:[],searchQuery:"",activeItemIndex:null,activationTrigger:1,pendingShouldSort:!1,pendingFocus:{focus:zr.Nothing}})}reduce(e,t){return O(t.type,to,e,t)}}const ro=(0,r.createContext)(null);function oo(e){let t=(0,r.useContext)(ro);if(null===t){let t=new Error(`<${e} /> is missing a parent <Menu /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,io),t}return t}function io({id:e,__demoMode:t=!1}){let n=(0,r.useMemo)(()=>no.new({id:e,__demoMode:t}),[]);return mt(()=>n.dispose()),n}let ao=r.Fragment;let lo=xe.RenderStrategy|xe.Static;let so=r.Fragment;let uo=Te(function(e,t){let n=(0,r.useId)(),{__demoMode:o=!1,...i}=e,a=io({id:n,__demoMode:o}),[l,s,u]=D(a,e=>[e.menuState,e.itemsElement,e.buttonElement]),c=Ze(t),d=I.get(null),f=D(d,(0,r.useCallback)(e=>d.selectors.isTop(e,n),[d,n]));Ce(f,[u,s],(e,t)=>{var n;a.send({type:Qr.CloseMenu}),ce(t,ue.Loose)||(e.preventDefault(),null==(n=a.state.buttonElement)||n.focus())});let m=V(()=>{a.send({type:Qr.CloseMenu})}),p=(0,r.useMemo)(()=>({open:l===Kr.Open,close:m}),[l,m]),h={ref:c},v=Fe();return r.createElement(Vr,null,r.createElement(ro.Provider,{value:a},r.createElement(et,{value:O(l,{[Kr.Open]:Je.Open,[Kr.Closed]:Je.Closed})},v({ourProps:h,theirProps:i,slot:p,defaultTag:ao,name:"Menu"}))))}),co=Te(function(e,t){let n=oo("Menu.Button"),o=(0,r.useId)(),{id:a=`headlessui-menu-button-${o}`,disabled:l=!1,autoFocus:s=!1,...u}=e,c=(0,r.useRef)(null),d=(0,r.useContext)(Ir).getReferenceProps,f=Ze(t,c,(0,r.useContext)(Ir).setReference,V(e=>n.send({type:Qr.SetButtonElement,element:e}))),m=V(e=>{switch(e.key){case i.Space:case i.Enter:case i.ArrowDown:e.preventDefault(),e.stopPropagation(),n.send({type:Qr.OpenMenu,focus:{focus:zr.First}});break;case i.ArrowUp:e.preventDefault(),e.stopPropagation(),n.send({type:Qr.OpenMenu,focus:{focus:zr.Last}})}}),p=V(e=>{if(e.key===i.Space)e.preventDefault()}),[h,v,g]=D(n,e=>[e.menuState,e.buttonElement,e.itemsElement]);!function(e,{trigger:t,action:n,close:o,select:i}){let a=(0,r.useRef)(null),l=(0,r.useRef)(null),s=(0,r.useRef)(null);be(e&&null!==t,"pointerdown",e=>{Z(null==e?void 0:e.target)&&null!=t&&t.contains(e.target)&&(l.current=e.x,s.current=e.y,a.current=new Date)}),be(e&&null!==t,"pointerup",e=>{var t,r;if(null===a.current||!K(e.target)||Math.abs(e.x-(null!=(t=l.current)?t:e.x))<5&&Math.abs(e.y-(null!=(r=s.current)?r:e.y))<5)return;let u=n(e),c=(new Date).getTime()-a.current.getTime();switch(a.current=null,u.kind){case 0:return;case 1:c>200&&(i(u.target),o());break;case 2:o()}},{capture:!0})}(h===Kr.Open,{trigger:v,action:(0,r.useCallback)(e=>{if(null!=v&&v.contains(e.target))return Fr.Ignore;let t=e.target.closest('[role="menuitem"]:not([data-disabled])');return Y(t)?Fr.Select(t):null!=g&&g.contains(e.target)?Fr.Ignore:Fr.Close},[v,g]),close:(0,r.useCallback)(()=>n.send({type:Qr.CloseMenu}),[]),select:(0,r.useCallback)(e=>e.click(),[])});let y=V(e=>{var t;if(0===e.button){if(Hr(e.currentTarget))return e.preventDefault();l||(h===Kr.Open?((0,Et.flushSync)(()=>n.send({type:Qr.CloseMenu})),null==(t=c.current)||t.focus({preventScroll:!0})):(e.preventDefault(),n.send({type:Qr.OpenMenu,focus:{focus:zr.Nothing},trigger:Jr.Pointer})))}}),{isFocusVisible:b,focusProps:w}=yr({autoFocus:s}),{isHovered:C,hoverProps:E}=Ar({isDisabled:l}),{pressed:A,pressProps:x}=function({disabled:e=!1}={}){let t=(0,r.useRef)(null),[n,o]=(0,r.useState)(!1),i=dt(),a=V(()=>{t.current=null,o(!1),i.dispose()}),l=V(e=>{if(i.dispose(),null===t.current){t.current=e.currentTarget,o(!0);{let n=z(e.currentTarget);i.addEventListener(n,"pointerup",a,!1),i.addEventListener(n,"pointermove",e=>{if(t.current){let n=function(e){let t=e.width/2,n=e.height/2;return{top:e.clientY-n,right:e.clientX+t,bottom:e.clientY+n,left:e.clientX-t}}(e);o(function(e,t){return!(!e||!t||e.right<t.left||e.left>t.right||e.bottom<t.top||e.top>t.bottom)}(n,t.current.getBoundingClientRect()))}},!1),i.addEventListener(n,"pointercancel",a,!1)}}});return{pressed:n,pressProps:e?{}:{onPointerDown:l,onPointerUp:a,onClick:a}}}({disabled:l}),k=(0,r.useMemo)(()=>({open:h===Kr.Open,active:A||h===Kr.Open,disabled:l,hover:C,focus:b,autofocus:s}),[h,C,b,A,l,s]),F=Me(d(),{ref:f,id:a,type:Sr(e,c.current),"aria-haspopup":"menu","aria-controls":null==g?void 0:g.id,"aria-expanded":h===Kr.Open,disabled:l||void 0,autoFocus:s,onKeyDown:m,onKeyUp:p,onPointerDown:y},w,E,x);return Fe()({ourProps:F,theirProps:u,slot:k,defaultTag:"button",name:"Menu.Button"})}),fo=Te(function(e,t){let n=(0,r.useId)(),{id:o=`headlessui-menu-items-${n}`,anchor:a,portal:l=!1,modal:s=!0,transition:c=!1,...d}=e,f=function(e){return(0,r.useMemo)(()=>e?"string"==typeof e?{to:e}:e:null,[e])}(a),m=oo("Menu.Items"),[p,h]=function(e=null){!1===e&&(e=null),"string"==typeof e&&(e={to:e});let t=(0,r.useContext)($r),n=(0,r.useMemo)(()=>e,[JSON.stringify(e,(e,t)=>{var n;return null!=(n=null==t?void 0:t.outerHTML)?n:t})]);u(()=>{null==t||t(null!=n?n:null)},[t,n]);let o=(0,r.useContext)(Ir);return(0,r.useMemo)(()=>[o.setFloating,e?o.styles:{}],[o.setFloating,e,o.styles])}(f),v=function(){let{getFloatingProps:e,slot:t}=(0,r.useContext)(Ir);return(0,r.useCallback)((...n)=>Object.assign({},e(...n),{"data-anchor":t.anchor}),[e,t])}(),[g,y]=(0,r.useState)(null),b=Ze(t,f?p:null,V(e=>m.send({type:Qr.SetItemsElement,element:e})),y),[w,C]=D(m,e=>[e.menuState,e.buttonElement]),E=Ee(C),A=Ee(g);f&&(l=!0);let x=Qe(),[k,F]=Vt(c,g,null!==x?(x&Je.Open)===Je.Open:w===Kr.Open);ne(k,C,()=>{m.send({type:Qr.CloseMenu})});let S=D(m,e=>e.__demoMode);We(!S&&(s&&w===Kr.Open),A),X(!S&&(s&&w===Kr.Open),{allowed:(0,r.useCallback)(()=>[C,g],[C,g])});let P=!function(e,t){let n=(0,r.useRef)({left:0,top:0});if(u(()=>{if(!t)return;let e=t.getBoundingClientRect();e&&(n.current=e)},[e,t]),null==t||!e||t===document.activeElement)return!1;let o=t.getBoundingClientRect();return o.top!==n.current.top||o.left!==n.current.left}(w!==Kr.Open,C)&&k;(0,r.useEffect)(()=>{let e=g;e&&w===Kr.Open&&e!==(null==A?void 0:A.activeElement)&&e.focus({preventScroll:!0})},[w,g,A]),function(e,{container:t,accept:n,walk:o}){let i=(0,r.useRef)(n),a=(0,r.useRef)(o);(0,r.useEffect)(()=>{i.current=n,a.current=o},[n,o]),u(()=>{if(!t||!e)return;let n=z(t);if(!n)return;let r=i.current,o=a.current,l=Object.assign(e=>r(e),{acceptNode:r}),s=n.createTreeWalker(t,NodeFilter.SHOW_ELEMENT,l,!1);for(;s.nextNode();)o(s.currentNode)},[t,e,i,a])}(w===Kr.Open,{container:g,accept(e){return"menuitem"===e.getAttribute("role")?NodeFilter.FILTER_REJECT:e.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(e){e.setAttribute("role","none")}});let O=dt(),M=V(e=>{var t,n,r;switch(O.dispose(),e.key){case i.Space:if(""!==m.state.searchQuery)return e.preventDefault(),e.stopPropagation(),m.send({type:Qr.Search,value:e.key});case i.Enter:if(e.preventDefault(),e.stopPropagation(),null!==m.state.activeItemIndex){let{dataRef:e}=m.state.items[m.state.activeItemIndex];null==(n=null==(t=e.current)?void 0:t.domRef.current)||n.click()}m.send({type:Qr.CloseMenu}),de(m.state.buttonElement);break;case i.ArrowDown:return e.preventDefault(),e.stopPropagation(),m.send({type:Qr.GoToItem,focus:zr.Next});case i.ArrowUp:return e.preventDefault(),e.stopPropagation(),m.send({type:Qr.GoToItem,focus:zr.Previous});case i.Home:case i.PageUp:return e.preventDefault(),e.stopPropagation(),m.send({type:Qr.GoToItem,focus:zr.First});case i.End:case i.PageDown:return e.preventDefault(),e.stopPropagation(),m.send({type:Qr.GoToItem,focus:zr.Last});case i.Escape:e.preventDefault(),e.stopPropagation(),(0,Et.flushSync)(()=>m.send({type:Qr.CloseMenu})),null==(r=m.state.buttonElement)||r.focus({preventScroll:!0});break;case i.Tab:e.preventDefault(),e.stopPropagation(),(0,Et.flushSync)(()=>m.send({type:Qr.CloseMenu})),function(e,t){ve(se(),t,{relativeTo:e})}(m.state.buttonElement,e.shiftKey?ie.Previous:ie.Next);break;default:1===e.key.length&&(m.send({type:Qr.Search,value:e.key}),O.setTimeout(()=>m.send({type:Qr.ClearSearch}),350))}}),T=V(e=>{if(e.key===i.Space)e.preventDefault()}),N=(0,r.useMemo)(()=>({open:w===Kr.Open}),[w]),R=Me(f?v():{},{"aria-activedescendant":D(m,m.selectors.activeDescendantId),"aria-labelledby":D(m,e=>{var t;return null==(t=e.buttonElement)?void 0:t.id}),id:o,onKeyDown:M,onKeyUp:T,role:"menu",tabIndex:w===Kr.Open?0:void 0,ref:b,style:{...d.style,...h,"--button-width":xr(C,!0).width},...$t(F)}),L=Fe();return r.createElement(Nt,{enabled:!!l&&(e.static||k),ownerDocument:E},L({ourProps:R,theirProps:d,slot:N,defaultTag:"div",features:lo,visible:P,name:"Menu.Items"}))}),mo=Te(function(e,t){let n=(0,r.useId)(),{id:o=`headlessui-menu-item-${n}`,disabled:i=!1,...a}=e,l=oo("Menu.Item"),s=D(l,e=>l.selectors.isActive(e,o)),c=(0,r.useRef)(null),d=Ze(t,c),f=D(l,e=>l.selectors.shouldScrollIntoView(e,o));u(()=>{if(f)return p().requestAnimationFrame(()=>{var e,t;null==(t=null==(e=c.current)?void 0:e.scrollIntoView)||t.call(e,{block:"nearest"})})},[f,c]);let m=Mr(c),h=(0,r.useRef)({disabled:i,domRef:c,get textValue(){return m()}});u(()=>{h.current.disabled=i},[h,i]),u(()=>(l.actions.registerItem(o,h),()=>l.actions.unregisterItem(o)),[h,o]);let v=V(()=>{l.send({type:Qr.CloseMenu})}),g=V(e=>{if(i)return e.preventDefault();l.send({type:Qr.CloseMenu}),de(l.state.buttonElement)}),y=V(()=>{if(i)return l.send({type:Qr.GoToItem,focus:zr.Nothing});l.send({type:Qr.GoToItem,focus:zr.Specific,id:o})}),b=function(){let e=(0,r.useRef)([-1,-1]);return{wasMoved(t){let n=Tr(t);return(e.current[0]!==n[0]||e.current[1]!==n[1])&&(e.current=n,!0)},update(t){e.current=Tr(t)}}}(),w=V(e=>{b.update(e),!i&&(s||l.send({type:Qr.GoToItem,focus:zr.Specific,id:o,trigger:Jr.Pointer}))}),C=V(e=>{b.wasMoved(e)&&(i||s||l.send({type:Qr.GoToItem,focus:zr.Specific,id:o,trigger:Jr.Pointer}))}),E=V(e=>{b.wasMoved(e)&&(i||s&&l.send({type:Qr.GoToItem,focus:zr.Nothing}))}),[A,x]=Xr(),[k,F]=st(),S=(0,r.useMemo)(()=>({active:s,focus:s,disabled:i,close:v}),[s,i,v]),P={id:o,ref:d,role:"menuitem",tabIndex:!0===i?void 0:-1,"aria-disabled":!0===i||void 0,"aria-labelledby":A,"aria-describedby":k,disabled:void 0,onClick:g,onFocus:y,onPointerEnter:w,onMouseEnter:w,onPointerMove:C,onMouseMove:C,onPointerLeave:E,onMouseLeave:E},O=Fe();return r.createElement(x,null,r.createElement(F,null,O({ourProps:P,theirProps:a,slot:S,defaultTag:so,name:"Menu.Item"})))}),po=Te(function(e,t){let[n,o]=Xr(),i=e,a={ref:t,"aria-labelledby":n,role:"group"},l=Fe();return r.createElement(o,null,l({ourProps:a,theirProps:i,slot:{},defaultTag:"div",name:"Menu.Section"}))}),ho=Te(function(e,t){let n=(0,r.useId)(),{id:o=`headlessui-menu-heading-${n}`,...i}=e,a=Ur();u(()=>a.register(o),[o,a.register]);let l={id:o,ref:t,role:"presentation",...a.props};return Fe()({ourProps:l,theirProps:i,slot:{},defaultTag:"header",name:"Menu.Heading"})}),vo=Te(function(e,t){let n=e,r={ref:t,role:"separator"};return Fe()({ourProps:r,theirProps:n,slot:{},defaultTag:"div",name:"Menu.Separator"})}),go=Object.assign(uo,{Button:co,Items:fo,Item:mo,Section:po,Heading:ho,Separator:vo});function yo({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))}var bo=r.forwardRef(yo);function wo({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.752 15.002A9.72 9.72 0 0 1 18 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 0 0 3 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 0 0 9.002-5.998Z"}))}var Co=r.forwardRef(wo);function Eo({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 3v2.25m6.364.386-1.591 1.591M21 12h-2.25m-.386 6.364-1.591-1.591M12 18.75V21m-4.773-4.227-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z"}))}var Ao=r.forwardRef(Eo);function xo({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"}))}var ko=r.forwardRef(xo);const Fo=(0,yn.A)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);var So=n(5107),Po=n(2531);const Oo=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter(Boolean).join(" ")};var Mo=e=>{var t;let{onMobileMenuToggle:n,isMobileMenuOpen:o}=e;const{darkMode:i,setDarkMode:a,user:l,logout:s}=r.useContext(dn.v),{sidebar:u,setSidebarState:c,header:d}=(0,fn.J)(),{isExpanded:f}=u,{title:m,breadcrumbs:p}=d;return r.createElement("div",{className:"sticky top-0 z-40 bg-primary border-b border-secondary"},r.createElement("div",{className:"flex h-16 items-center gap-x-4 px-4"},r.createElement("button",{onClick:n,className:"md:hidden p-2 rounded-md hover:bg-secondary text-secondary hover:text-accent transition-colors","aria-label":"Toggle mobile menu"},r.createElement(Fo,{className:"h-6 w-6"})),r.createElement("div",{className:"flex flex-1 gap-x-4 self-stretch lg:gap-x-6"},r.createElement("div",{className:"flex flex-1 items-center min-w-0"},p&&p.length>0?r.createElement("nav",{"aria-label":"Breadcrumb",className:"flex"},r.createElement("ol",{role:"list",className:"flex items-center space-x-4"},p.map((e,t)=>r.createElement("li",{key:t},r.createElement("div",{className:"flex items-center"},t>0&&r.createElement("svg",{fill:"currentColor",viewBox:"0 0 20 20","aria-hidden":"true",className:"size-5 shrink-0 text-secondary"},r.createElement("path",{d:"M5.555 17.776l8-16 .894.448-8 16-.894-.448z"})),r.createElement(mn.Link,{to:e.href,"aria-current":e.current?"page":void 0,className:Oo("text-sm font-medium",t>0?"ml-4":"",e.current?"text-primary":"text-secondary hover:text-accent")},e.name)))))):r.createElement("h1",{className:"text-lg font-medium text-primary"},m)),r.createElement("div",{className:"flex items-center gap-x-4 lg:gap-x-6 ml-auto"},r.createElement("form",{className:"relative flex hidden h-8"},r.createElement("label",{htmlFor:"search-field",className:"sr-only"},"搜索"),r.createElement(bo,{className:"pointer-events-none absolute inset-y-0 left-2 h-full w-5 text-secondary"}),r.createElement("input",{id:"search-field",type:"search",placeholder:"搜索...",className:"block h-full w-full border-0 bg-primary py-0 pl-10 pr-0 text-primary placeholder:text-secondary focus:ring-0 sm:text-sm"})),r.createElement("button",{onClick:()=>a("dark"===i?"light":"dark"),className:"text-secondary hover:text-primary"},"dark"===i?r.createElement(Co,{className:"h-6 w-6"}):r.createElement(Ao,{className:"h-6 w-6"})),r.createElement("button",{className:"text-secondary hidden hover:text-primary"},r.createElement(ko,{className:"h-6 w-6"})),r.createElement("div",{className:"hidden lg:block lg:h-6 lg:w-px lg:bg-secondary"}),l&&r.createElement(go,{as:"div",className:"relative"},r.createElement(co,{className:"flex items-center"},l.avatar_url?r.createElement("img",{className:"h-8 w-8 rounded-full",src:(0,Po.Jf)(l.avatar_url),alt:l.name}):r.createElement("div",{className:"border-2 bg-accent h-8 w-8 rounded-full flex items-center justify-center text-white"},null===(t=l.name)||void 0===t?void 0:t[0]),r.createElement("span",{className:"hidden lg:flex lg:items-center"},r.createElement("span",{className:"ml-4 text-sm text-primary"},l.name),r.createElement(So.A,{className:"ml-2 h-5 w-5 text-secondary"}))),r.createElement(fo,{className:"absolute right-0 mt-2 w-48 origin-top-right rounded-md bg-primary py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"},r.createElement(mo,null,e=>{let{active:t}=e;return r.createElement("a",{href:"#",onClick:()=>s(),className:(t?"bg-secondary":"")+" block px-4 py-2 text-sm text-primary"},"Sign out")})))))))},To=n(867),No=n(2187),Ro=n(7595),Lo=n(723),Io=n(8734);var $o=e=>{const t=(null==e?void 0:e.algorithm)?(0,No.an)(e.algorithm):Ro.A,n=Object.assign(Object.assign({},Lo.A),null==e?void 0:e.token);return(0,No.lO)(n,{override:null==e?void 0:e.token},t,Io.A)},Vo=n(1320),Do=n(9806),jo=n(4184),_o=n(8690),Ho=n(1892);var zo=(e,t)=>{const n=null!=t?t:(0,jo.A)(e),r=n.fontSizeSM,o=n.controlHeight-4;return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},n),function(e){const{sizeUnit:t,sizeStep:n}=e,r=n-2;return{sizeXXL:t*(r+10),sizeXL:t*(r+6),sizeLG:t*(r+2),sizeMD:t*(r+2),sizeMS:t*(r+1),size:t*r,sizeSM:t*r,sizeXS:t*(r-1),sizeXXS:t*(r-1)}}(null!=t?t:e)),(0,Ho.A)(r)),{controlHeight:o}),(0,_o.A)(Object.assign(Object.assign({},n),{controlHeight:o})))},Bo=n(5748),qo=n(7484),Wo=n(2616);const Uo=(e,t)=>new Wo.Y(e).setA(t).toRgbString(),Xo=(e,t)=>new Wo.Y(e).lighten(t).toHexString(),Zo=e=>{const t=(0,Bo.cM)(e,{theme:"dark"});return{1:t[0],2:t[1],3:t[2],4:t[3],5:t[6],6:t[5],7:t[4],8:t[6],9:t[5],10:t[4]}},Go=(e,t)=>{const n=e||"#000",r=t||"#fff";return{colorBgBase:n,colorTextBase:r,colorText:Uo(r,.85),colorTextSecondary:Uo(r,.65),colorTextTertiary:Uo(r,.45),colorTextQuaternary:Uo(r,.25),colorFill:Uo(r,.18),colorFillSecondary:Uo(r,.12),colorFillTertiary:Uo(r,.08),colorFillQuaternary:Uo(r,.04),colorBgSolid:Uo(r,.95),colorBgSolidHover:Uo(r,1),colorBgSolidActive:Uo(r,.9),colorBgElevated:Xo(n,12),colorBgContainer:Xo(n,8),colorBgLayout:Xo(n,0),colorBgSpotlight:Xo(n,26),colorBgBlur:Uo(r,.04),colorBorder:Xo(n,26),colorBorderSecondary:Xo(n,19)}};var Yo=(e,t)=>{const n=Object.keys(Lo.r).map(t=>{const n=(0,Bo.cM)(e[t],{theme:"dark"});return Array.from({length:10},()=>1).reduce((e,r,o)=>(e[`${t}-${o+1}`]=n[o],e[`${t}${o+1}`]=n[o],e),{})}).reduce((e,t)=>e=Object.assign(Object.assign({},e),t),{}),r=null!=t?t:(0,jo.A)(e),o=(0,qo.A)(e,{generateColorPalettes:Zo,generateNeutralColorPalettes:Go});return Object.assign(Object.assign(Object.assign(Object.assign({},r),n),o),{colorPrimaryBg:o.colorPrimaryBorder,colorPrimaryBgHover:o.colorPrimaryBorderHover})};var Ko={defaultSeed:Do.sb.token,useToken:function(){const[e,t,n]=(0,Vo.Ay)();return{theme:e,token:t,hashId:n}},defaultAlgorithm:jo.A,darkAlgorithm:Yo,compactAlgorithm:zo,getDesignToken:$o,defaultConfig:Do.sb,_internalContext:Do.vG},Jo=n(226),Qo=n(4716);var ei=e=>{let{children:t,redirectTo:n="/login"}=e;const{isAuthenticated:o,isLoading:i,authType:a}=(0,Jo.A)();return(0,r.useEffect)(()=>{i||"none"===a||o||(0,mn.navigate)(n)},[o,i,a,n]),i?r.createElement("div",{className:"flex items-center justify-center h-screen"},r.createElement(Qo.A,{size:"large",tip:"Loading..."})):"none"===a||o?r.createElement(r.Fragment,null,t):null};const ti=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter(Boolean).join(" ")};var ni=e=>{let{meta:t,title:n,link:o,children:i,showHeader:a=!0,restricted:l=!0}=e;const{darkMode:s}=r.useContext(dn.v),{sidebar:u}=(0,fn.J)(),{isExpanded:c}=u,[d,f]=r.useState(!1),{authType:m}=(0,Jo.A)();r.useEffect(()=>{f(!1)},[o]),r.useEffect(()=>{document.getElementsByTagName("html")[0].className=""+("dark"===s?"dark bg-primary":"light bg-primary")},[s]);const p=r.createElement("div",{className:"min-h-screen flex"},r.createElement(un,{as:"div",open:d,onClose:()=>f(!1),className:"relative z-50 md:hidden"},r.createElement("div",{className:"fixed inset-0 bg-black/30","aria-hidden":"true"}),r.createElement("div",{className:"fixed inset-0 flex"},r.createElement(un.Panel,{className:"relative mr-16 flex w-full max-w-xs flex-1"},r.createElement("div",{className:"absolute right-0 top-0 flex w-16 justify-center pt-5"},r.createElement("button",{type:"button",className:"text-secondary",onClick:()=>f(!1)},r.createElement("span",{className:"sr-only"},"关闭侧边栏"),r.createElement(cn.A,{className:"h-6 w-6","aria-hidden":"true"}))),r.createElement(Fn,{link:o,meta:t,isMobile:!0})))),r.createElement("div",{className:"hidden md:flex md:flex-col md:fixed md:inset-y-0"},r.createElement(Fn,{link:o,meta:t,isMobile:!1})),r.createElement("div",{className:ti("flex-1 flex flex-col min-h-screen","transition-all duration-300 ease-in-out","md:pl-16",c?"md:pl-72":"md:pl-16")},a&&r.createElement(Mo,{isMobileMenuOpen:d,onMobileMenuToggle:()=>f(!d)}),r.createElement(To.Ay,{theme:{token:{borderRadius:4,colorBgBase:"dark"===s?"#05080C":"#ffffff"},algorithm:"dark"===s?Ko.darkAlgorithm:Ko.defaultAlgorithm}},r.createElement("main",{className:"flex-1 p-2 text-primary"},i))));return l&&"none"!==m?r.createElement(ei,null,p):p};const ri=e=>{let{children:t,showHeader:n=!0,restricted:o=!0}=e;const{darkMode:i}=r.useContext(dn.v),{authType:a}=(0,Jo.A)(),{setHeader:l}=(0,fn.J)();r.useEffect(()=>{document.getElementsByTagName("html")[0].className=""+("dark"===i?"dark bg-primary":"light bg-primary")},[i]),r.useEffect(()=>{l({title:"多智能体工作室 轻量版",breadcrumbs:[{name:"首页",href:"/",current:!1},{name:"轻量模式",href:"/lite",current:!0}]})},[l]);const s=r.createElement("div",{className:"min-h-screen flex flex-col"},n&&r.createElement(Mo,{isMobileMenuOpen:!1,onMobileMenuToggle:()=>{}}),r.createElement(To.Ay,{theme:{token:{borderRadius:4,colorBgBase:"dark"===i?"#05080C":"#ffffff"},algorithm:"dark"===i?Ko.darkAlgorithm:Ko.defaultAlgorithm}},r.createElement("main",{className:"flex-1 p-2 text-primary"},t)));return o&&"none"!==a?r.createElement(ei,null,s):s}},1511:function(e,t,n){n.d(t,{v:function(){return l}});var r=n(6540);const o=e=>{let t;const n=new Set,r=(e,r)=>{const o="function"==typeof e?e(t):e;if(!Object.is(o,t)){const e=t;t=(null!=r?r:"object"!=typeof o||null===o)?o:Object.assign({},t,o),n.forEach(n=>n(t,e))}},o=()=>t,i={setState:r,getState:o,getInitialState:()=>a,subscribe:e=>(n.add(e),()=>n.delete(e))},a=t=e(r,o,i);return i},i=e=>e;const a=e=>{const t=(e=>e?o(e):o)(e),n=e=>function(e,t=i){const n=r.useSyncExternalStore(e.subscribe,()=>t(e.getState()),()=>t(e.getInitialState()));return r.useDebugValue(n),n}(t,e);return Object.assign(n,t),n},l=e=>e?a(e):a},1788:function(e,t,n){n.d(t,{A:function(){return l}});var r=n(6540);const o=(...e)=>e.filter((e,t,n)=>Boolean(e)&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim();var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const a=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:a,className:l="",children:s,iconNode:u,...c},d)=>(0,r.createElement)("svg",{ref:d,...i,width:t,height:t,stroke:e,strokeWidth:a?24*Number(n)/Number(t):n,className:o("lucide",l),...c},[...u.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(s)?s:[s]])),l=(e,t)=>{const n=(0,r.forwardRef)(({className:n,...i},l)=>{return(0,r.createElement)(a,{ref:l,iconNode:t,className:o(`lucide-${s=e,s.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,n),...i});var s});return n.displayName=`${e}`,n}},2427:function(e,t,n){n.d(t,{A:function(){return z}});var r=n(9379),o=n(5544),i=n(3986),a=n(2595),l=n(6942),s=n.n(l),u=n(8462),c=n(6588),d=n(2633),f=n(6956),m=n(6855),p=n(981),h=n(8430),v=n(6540),g=n(8168),y=n(754),b=n(8719);function w(e){var t=e.prefixCls,n=e.align,r=e.arrow,o=e.arrowPos,i=r||{},a=i.className,l=i.content,u=o.x,c=void 0===u?0:u,d=o.y,f=void 0===d?0:d,m=v.useRef();if(!n||!n.points)return null;var p={position:"absolute"};if(!1!==n.autoArrow){var h=n.points[0],g=n.points[1],y=h[0],b=h[1],w=g[0],C=g[1];y!==w&&["t","b"].includes(y)?"t"===y?p.top=0:p.bottom=0:p.top=f,b!==C&&["l","r"].includes(b)?"l"===b?p.left=0:p.right=0:p.left=c}return v.createElement("div",{ref:m,className:s()("".concat(t,"-arrow"),a),style:p},l)}function C(e){var t=e.prefixCls,n=e.open,r=e.zIndex,o=e.mask,i=e.motion;return o?v.createElement(y.Ay,(0,g.A)({},i,{motionAppear:!0,visible:n,removeOnLeave:!0}),function(e){var n=e.className;return v.createElement("div",{style:{zIndex:r},className:s()("".concat(t,"-mask"),n)})}):null}var E=v.memo(function(e){return e.children},function(e,t){return t.cache});var A=v.forwardRef(function(e,t){var n=e.popup,i=e.className,a=e.prefixCls,l=e.style,c=e.target,d=e.onVisibleChanged,f=e.open,m=e.keepDom,h=e.fresh,A=e.onClick,x=e.mask,k=e.arrow,F=e.arrowPos,S=e.align,P=e.motion,O=e.maskMotion,M=e.forceRender,T=e.getPopupContainer,N=e.autoDestroy,R=e.portal,L=e.zIndex,I=e.onMouseEnter,$=e.onMouseLeave,V=e.onPointerEnter,D=e.onPointerDownCapture,j=e.ready,_=e.offsetX,H=e.offsetY,z=e.offsetR,B=e.offsetB,q=e.onAlign,W=e.onPrepare,U=e.stretch,X=e.targetWidth,Z=e.targetHeight,G="function"==typeof n?n():n,Y=f||m,K=(null==T?void 0:T.length)>0,J=v.useState(!T||!K),Q=(0,o.A)(J,2),ee=Q[0],te=Q[1];if((0,p.A)(function(){!ee&&K&&c&&te(!0)},[ee,K,c]),!ee)return null;var ne="auto",re={left:"-1000vw",top:"-1000vh",right:ne,bottom:ne};if(j||!f){var oe,ie=S.points,ae=S.dynamicInset||(null===(oe=S._experimental)||void 0===oe?void 0:oe.dynamicInset),le=ae&&"r"===ie[0][1],se=ae&&"b"===ie[0][0];le?(re.right=z,re.left=ne):(re.left=_,re.right=ne),se?(re.bottom=B,re.top=ne):(re.top=H,re.bottom=ne)}var ue={};return U&&(U.includes("height")&&Z?ue.height=Z:U.includes("minHeight")&&Z&&(ue.minHeight=Z),U.includes("width")&&X?ue.width=X:U.includes("minWidth")&&X&&(ue.minWidth=X)),f||(ue.pointerEvents="none"),v.createElement(R,{open:M||Y,getContainer:T&&function(){return T(c)},autoDestroy:N},v.createElement(C,{prefixCls:a,open:f,zIndex:L,mask:x,motion:O}),v.createElement(u.A,{onResize:q,disabled:!f},function(e){return v.createElement(y.Ay,(0,g.A)({motionAppear:!0,motionEnter:!0,motionLeave:!0,removeOnLeave:!1,forceRender:M,leavedClassName:"".concat(a,"-hidden")},P,{onAppearPrepare:W,onEnterPrepare:W,visible:f,onVisibleChanged:function(e){var t;null==P||null===(t=P.onVisibleChanged)||void 0===t||t.call(P,e),d(e)}}),function(n,o){var u=n.className,c=n.style,d=s()(a,u,i);return v.createElement("div",{ref:(0,b.K4)(e,t,o),className:d,style:(0,r.A)((0,r.A)((0,r.A)((0,r.A)({"--arrow-x":"".concat(F.x||0,"px"),"--arrow-y":"".concat(F.y||0,"px")},re),ue),c),{},{boxSizing:"border-box",zIndex:L},l),onMouseEnter:I,onMouseLeave:$,onPointerEnter:V,onClick:A,onPointerDownCapture:D},k&&v.createElement(w,{prefixCls:a,arrow:k,arrowPos:F,align:S}),v.createElement(E,{cache:!f&&!h},G))})}))});var x=v.forwardRef(function(e,t){var n=e.children,r=e.getTriggerDOMNode,o=(0,b.f3)(n),i=v.useCallback(function(e){(0,b.Xf)(t,r?r(e):e)},[r]),a=(0,b.xK)(i,(0,b.A9)(n));return o?v.cloneElement(n,{ref:a}):n}),k=v.createContext(null);function F(e){return e?Array.isArray(e)?e:[e]:[]}var S=n(2467);function P(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return(arguments.length>2?arguments[2]:void 0)?e[0]===t[0]:e[0]===t[0]&&e[1]===t[1]}function O(e,t,n,r){return t||(n?{motionName:"".concat(e,"-").concat(n)}:r?{motionName:r}:null)}function M(e){return e.ownerDocument.defaultView}function T(e){for(var t=[],n=null==e?void 0:e.parentElement,r=["hidden","scroll","clip","auto"];n;){var o=M(n).getComputedStyle(n);[o.overflowX,o.overflowY,o.overflow].some(function(e){return r.includes(e)})&&t.push(n),n=n.parentElement}return t}function N(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;return Number.isNaN(e)?t:e}function R(e){return N(parseFloat(e),0)}function L(e,t){var n=(0,r.A)({},e);return(t||[]).forEach(function(e){if(!(e instanceof HTMLBodyElement||e instanceof HTMLHtmlElement)){var t=M(e).getComputedStyle(e),r=t.overflow,o=t.overflowClipMargin,i=t.borderTopWidth,a=t.borderBottomWidth,l=t.borderLeftWidth,s=t.borderRightWidth,u=e.getBoundingClientRect(),c=e.offsetHeight,d=e.clientHeight,f=e.offsetWidth,m=e.clientWidth,p=R(i),h=R(a),v=R(l),g=R(s),y=N(Math.round(u.width/f*1e3)/1e3),b=N(Math.round(u.height/c*1e3)/1e3),w=(f-m-v-g)*y,C=(c-d-p-h)*b,E=p*b,A=h*b,x=v*y,k=g*y,F=0,S=0;if("clip"===r){var P=R(o);F=P*y,S=P*b}var O=u.x+x-F,T=u.y+E-S,L=O+u.width+2*F-x-k-w,I=T+u.height+2*S-E-A-C;n.left=Math.max(n.left,O),n.top=Math.max(n.top,T),n.right=Math.min(n.right,L),n.bottom=Math.min(n.bottom,I)}}),n}function I(e){var t="".concat(arguments.length>1&&void 0!==arguments[1]?arguments[1]:0),n=t.match(/^(.*)\%$/);return n?e*(parseFloat(n[1])/100):parseFloat(t)}function $(e,t){var n=t||[],r=(0,o.A)(n,2),i=r[0],a=r[1];return[I(e.width,i),I(e.height,a)]}function V(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return[e[0],e[1]]}function D(e,t){var n,r=t[0],o=t[1];return n="t"===r?e.y:"b"===r?e.y+e.height:e.y+e.height/2,{x:"l"===o?e.x:"r"===o?e.x+e.width:e.x+e.width/2,y:n}}function j(e,t){var n={t:"b",b:"t",l:"r",r:"l"};return e.map(function(e,r){return r===t?n[e]||"c":e}).join("")}var _=n(436);n(8210);var H=["prefixCls","children","action","showAction","hideAction","popupVisible","defaultPopupVisible","onPopupVisibleChange","afterPopupVisibleChange","mouseEnterDelay","mouseLeaveDelay","focusDelay","blurDelay","mask","maskClosable","getPopupContainer","forceRender","autoDestroy","destroyPopupOnHide","popup","popupClassName","popupStyle","popupPlacement","builtinPlacements","popupAlign","zIndex","stretch","getPopupClassNameFromAlign","fresh","alignPoint","onPopupClick","onPopupAlign","arrow","popupMotion","maskMotion","popupTransitionName","popupAnimation","maskTransitionName","maskAnimation","className","getTriggerDOMNode"];var z=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:a.A,t=v.forwardRef(function(t,n){var a=t.prefixCls,l=void 0===a?"rc-trigger-popup":a,g=t.children,y=t.action,b=void 0===y?"hover":y,w=t.showAction,C=t.hideAction,E=t.popupVisible,R=t.defaultPopupVisible,I=t.onPopupVisibleChange,z=t.afterPopupVisibleChange,B=t.mouseEnterDelay,q=t.mouseLeaveDelay,W=void 0===q?.1:q,U=t.focusDelay,X=t.blurDelay,Z=t.mask,G=t.maskClosable,Y=void 0===G||G,K=t.getPopupContainer,J=t.forceRender,Q=t.autoDestroy,ee=t.destroyPopupOnHide,te=t.popup,ne=t.popupClassName,re=t.popupStyle,oe=t.popupPlacement,ie=t.builtinPlacements,ae=void 0===ie?{}:ie,le=t.popupAlign,se=t.zIndex,ue=t.stretch,ce=t.getPopupClassNameFromAlign,de=t.fresh,fe=t.alignPoint,me=t.onPopupClick,pe=t.onPopupAlign,he=t.arrow,ve=t.popupMotion,ge=t.maskMotion,ye=t.popupTransitionName,be=t.popupAnimation,we=t.maskTransitionName,Ce=t.maskAnimation,Ee=t.className,Ae=t.getTriggerDOMNode,xe=(0,i.A)(t,H),ke=Q||ee||!1,Fe=v.useState(!1),Se=(0,o.A)(Fe,2),Pe=Se[0],Oe=Se[1];(0,p.A)(function(){Oe((0,h.A)())},[]);var Me=v.useRef({}),Te=v.useContext(k),Ne=v.useMemo(function(){return{registerSubPopup:function(e,t){Me.current[e]=t,null==Te||Te.registerSubPopup(e,t)}}},[Te]),Re=(0,m.A)(),Le=v.useState(null),Ie=(0,o.A)(Le,2),$e=Ie[0],Ve=Ie[1],De=v.useRef(null),je=(0,f.A)(function(e){De.current=e,(0,c.fk)(e)&&$e!==e&&Ve(e),null==Te||Te.registerSubPopup(Re,e)}),_e=v.useState(null),He=(0,o.A)(_e,2),ze=He[0],Be=He[1],qe=v.useRef(null),We=(0,f.A)(function(e){(0,c.fk)(e)&&ze!==e&&(Be(e),qe.current=e)}),Ue=v.Children.only(g),Xe=(null==Ue?void 0:Ue.props)||{},Ze={},Ge=(0,f.A)(function(e){var t,n,r=ze;return(null==r?void 0:r.contains(e))||(null===(t=(0,d.j)(r))||void 0===t?void 0:t.host)===e||e===r||(null==$e?void 0:$e.contains(e))||(null===(n=(0,d.j)($e))||void 0===n?void 0:n.host)===e||e===$e||Object.values(Me.current).some(function(t){return(null==t?void 0:t.contains(e))||e===t})}),Ye=O(l,ve,be,ye),Ke=O(l,ge,Ce,we),Je=v.useState(R||!1),Qe=(0,o.A)(Je,2),et=Qe[0],tt=Qe[1],nt=null!=E?E:et,rt=(0,f.A)(function(e){void 0===E&&tt(e)});(0,p.A)(function(){tt(E||!1)},[E]);var ot=v.useRef(nt);ot.current=nt;var it=v.useRef([]);it.current=[];var at=(0,f.A)(function(e){var t;rt(e),(null!==(t=it.current[it.current.length-1])&&void 0!==t?t:nt)!==e&&(it.current.push(e),null==I||I(e))}),lt=v.useRef(),st=function(){clearTimeout(lt.current)},ut=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;st(),0===t?at(e):lt.current=setTimeout(function(){at(e)},1e3*t)};v.useEffect(function(){return st},[]);var ct=v.useState(!1),dt=(0,o.A)(ct,2),ft=dt[0],mt=dt[1];(0,p.A)(function(e){e&&!nt||mt(!0)},[nt]);var pt=v.useState(null),ht=(0,o.A)(pt,2),vt=ht[0],gt=ht[1],yt=v.useState(null),bt=(0,o.A)(yt,2),wt=bt[0],Ct=bt[1],Et=function(e){Ct([e.clientX,e.clientY])},At=function(e,t,n,i,a,l,s){var u=v.useState({ready:!1,offsetX:0,offsetY:0,offsetR:0,offsetB:0,arrowX:0,arrowY:0,scaleX:1,scaleY:1,align:a[i]||{}}),d=(0,o.A)(u,2),m=d[0],h=d[1],g=v.useRef(0),y=v.useMemo(function(){return t?T(t):[]},[t]),b=v.useRef({});e||(b.current={});var w=(0,f.A)(function(){if(t&&n&&e){var u,d,f,m,p,v=t,g=v.ownerDocument,w=M(v),C=w.getComputedStyle(v).position,E=v.style.left,A=v.style.top,x=v.style.right,k=v.style.bottom,F=v.style.overflow,P=(0,r.A)((0,r.A)({},a[i]),l),O=g.createElement("div");if(null===(u=v.parentElement)||void 0===u||u.appendChild(O),O.style.left="".concat(v.offsetLeft,"px"),O.style.top="".concat(v.offsetTop,"px"),O.style.position=C,O.style.height="".concat(v.offsetHeight,"px"),O.style.width="".concat(v.offsetWidth,"px"),v.style.left="0",v.style.top="0",v.style.right="auto",v.style.bottom="auto",v.style.overflow="hidden",Array.isArray(n))p={x:n[0],y:n[1],width:0,height:0};else{var T,R,I=n.getBoundingClientRect();I.x=null!==(T=I.x)&&void 0!==T?T:I.left,I.y=null!==(R=I.y)&&void 0!==R?R:I.top,p={x:I.x,y:I.y,width:I.width,height:I.height}}var _=v.getBoundingClientRect(),H=w.getComputedStyle(v),z=H.height,B=H.width;_.x=null!==(d=_.x)&&void 0!==d?d:_.left,_.y=null!==(f=_.y)&&void 0!==f?f:_.top;var q=g.documentElement,W=q.clientWidth,U=q.clientHeight,X=q.scrollWidth,Z=q.scrollHeight,G=q.scrollTop,Y=q.scrollLeft,K=_.height,J=_.width,Q=p.height,ee=p.width,te={left:0,top:0,right:W,bottom:U},ne={left:-Y,top:-G,right:X-Y,bottom:Z-G},re=P.htmlRegion,oe="visible",ie="visibleFirst";"scroll"!==re&&re!==ie&&(re=oe);var ae=re===ie,le=L(ne,y),se=L(te,y),ue=re===oe?se:le,ce=ae?se:ue;v.style.left="auto",v.style.top="auto",v.style.right="0",v.style.bottom="0";var de=v.getBoundingClientRect();v.style.left=E,v.style.top=A,v.style.right=x,v.style.bottom=k,v.style.overflow=F,null===(m=v.parentElement)||void 0===m||m.removeChild(O);var fe=N(Math.round(J/parseFloat(B)*1e3)/1e3),me=N(Math.round(K/parseFloat(z)*1e3)/1e3);if(0===fe||0===me||(0,c.fk)(n)&&!(0,S.A)(n))return;var pe=P.offset,he=P.targetOffset,ve=$(_,pe),ge=(0,o.A)(ve,2),ye=ge[0],be=ge[1],we=$(p,he),Ce=(0,o.A)(we,2),Ee=Ce[0],Ae=Ce[1];p.x-=Ee,p.y-=Ae;var xe=P.points||[],ke=(0,o.A)(xe,2),Fe=ke[0],Se=V(ke[1]),Pe=V(Fe),Oe=D(p,Se),Me=D(_,Pe),Te=(0,r.A)({},P),Ne=Oe.x-Me.x+ye,Re=Oe.y-Me.y+be;function Ft(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:ue,r=_.x+e,o=_.y+t,i=r+J,a=o+K,l=Math.max(r,n.left),s=Math.max(o,n.top),u=Math.min(i,n.right),c=Math.min(a,n.bottom);return Math.max(0,(u-l)*(c-s))}var Le,Ie,$e,Ve,De=Ft(Ne,Re),je=Ft(Ne,Re,se),_e=D(p,["t","l"]),He=D(_,["t","l"]),ze=D(p,["b","r"]),Be=D(_,["b","r"]),qe=P.overflow||{},We=qe.adjustX,Ue=qe.adjustY,Xe=qe.shiftX,Ze=qe.shiftY,Ge=function(e){return"boolean"==typeof e?e:e>=0};function St(){Le=_.y+Re,Ie=Le+K,$e=_.x+Ne,Ve=$e+J}St();var Ye=Ge(Ue),Ke=Pe[0]===Se[0];if(Ye&&"t"===Pe[0]&&(Ie>ce.bottom||b.current.bt)){var Je=Re;Ke?Je-=K-Q:Je=_e.y-Be.y-be;var Qe=Ft(Ne,Je),et=Ft(Ne,Je,se);Qe>De||Qe===De&&(!ae||et>=je)?(b.current.bt=!0,Re=Je,be=-be,Te.points=[j(Pe,0),j(Se,0)]):b.current.bt=!1}if(Ye&&"b"===Pe[0]&&(Le<ce.top||b.current.tb)){var tt=Re;Ke?tt+=K-Q:tt=ze.y-He.y-be;var nt=Ft(Ne,tt),rt=Ft(Ne,tt,se);nt>De||nt===De&&(!ae||rt>=je)?(b.current.tb=!0,Re=tt,be=-be,Te.points=[j(Pe,0),j(Se,0)]):b.current.tb=!1}var ot=Ge(We),it=Pe[1]===Se[1];if(ot&&"l"===Pe[1]&&(Ve>ce.right||b.current.rl)){var at=Ne;it?at-=J-ee:at=_e.x-Be.x-ye;var lt=Ft(at,Re),st=Ft(at,Re,se);lt>De||lt===De&&(!ae||st>=je)?(b.current.rl=!0,Ne=at,ye=-ye,Te.points=[j(Pe,1),j(Se,1)]):b.current.rl=!1}if(ot&&"r"===Pe[1]&&($e<ce.left||b.current.lr)){var ut=Ne;it?ut+=J-ee:ut=ze.x-He.x-ye;var ct=Ft(ut,Re),dt=Ft(ut,Re,se);ct>De||ct===De&&(!ae||dt>=je)?(b.current.lr=!0,Ne=ut,ye=-ye,Te.points=[j(Pe,1),j(Se,1)]):b.current.lr=!1}St();var ft=!0===Xe?0:Xe;"number"==typeof ft&&($e<se.left&&(Ne-=$e-se.left-ye,p.x+ee<se.left+ft&&(Ne+=p.x-se.left+ee-ft)),Ve>se.right&&(Ne-=Ve-se.right-ye,p.x>se.right-ft&&(Ne+=p.x-se.right+ft)));var mt=!0===Ze?0:Ze;"number"==typeof mt&&(Le<se.top&&(Re-=Le-se.top-be,p.y+Q<se.top+mt&&(Re+=p.y-se.top+Q-mt)),Ie>se.bottom&&(Re-=Ie-se.bottom-be,p.y>se.bottom-mt&&(Re+=p.y-se.bottom+mt)));var pt=_.x+Ne,ht=pt+J,vt=_.y+Re,gt=vt+K,yt=p.x,bt=yt+ee,wt=p.y,Ct=wt+Q,Et=(Math.max(pt,yt)+Math.min(ht,bt))/2-pt,At=(Math.max(vt,wt)+Math.min(gt,Ct))/2-vt;null==s||s(t,Te);var xt=de.right-_.x-(Ne+_.width),kt=de.bottom-_.y-(Re+_.height);1===fe&&(Ne=Math.round(Ne),xt=Math.round(xt)),1===me&&(Re=Math.round(Re),kt=Math.round(kt)),h({ready:!0,offsetX:Ne/fe,offsetY:Re/me,offsetR:xt/fe,offsetB:kt/me,arrowX:Et/fe,arrowY:At/me,scaleX:fe,scaleY:me,align:Te})}}),C=function(){h(function(e){return(0,r.A)((0,r.A)({},e),{},{ready:!1})})};return(0,p.A)(C,[i]),(0,p.A)(function(){e||C()},[e]),[m.ready,m.offsetX,m.offsetY,m.offsetR,m.offsetB,m.arrowX,m.arrowY,m.scaleX,m.scaleY,m.align,function(){g.current+=1;var e=g.current;Promise.resolve().then(function(){g.current===e&&w()})}]}(nt,$e,fe&&null!==wt?wt:ze,oe,ae,le,pe),xt=(0,o.A)(At,11),kt=xt[0],Ft=xt[1],St=xt[2],Pt=xt[3],Ot=xt[4],Mt=xt[5],Tt=xt[6],Nt=xt[7],Rt=xt[8],Lt=xt[9],It=xt[10],$t=function(e,t,n,r){return v.useMemo(function(){var o=F(null!=n?n:t),i=F(null!=r?r:t),a=new Set(o),l=new Set(i);return e&&(a.has("hover")&&(a.delete("hover"),a.add("click")),l.has("hover")&&(l.delete("hover"),l.add("click"))),[a,l]},[e,t,n,r])}(Pe,b,w,C),Vt=(0,o.A)($t,2),Dt=Vt[0],jt=Vt[1],_t=Dt.has("click"),Ht=jt.has("click")||jt.has("contextMenu"),zt=(0,f.A)(function(){ft||It()});!function(e,t,n,r,o){(0,p.A)(function(){if(e&&t&&n){var i=n,a=T(t),l=T(i),s=M(i),u=new Set([s].concat((0,_.A)(a),(0,_.A)(l)));function c(){r(),o()}return u.forEach(function(e){e.addEventListener("scroll",c,{passive:!0})}),s.addEventListener("resize",c,{passive:!0}),r(),function(){u.forEach(function(e){e.removeEventListener("scroll",c),s.removeEventListener("resize",c)})}}},[e,t,n])}(nt,ze,$e,zt,function(){ot.current&&fe&&Ht&&ut(!1)}),(0,p.A)(function(){zt()},[wt,oe]),(0,p.A)(function(){!nt||null!=ae&&ae[oe]||zt()},[JSON.stringify(le)]);var Bt=v.useMemo(function(){var e=function(e,t,n,r){for(var o=n.points,i=Object.keys(e),a=0;a<i.length;a+=1){var l,s=i[a];if(P(null===(l=e[s])||void 0===l?void 0:l.points,o,r))return"".concat(t,"-placement-").concat(s)}return""}(ae,l,Lt,fe);return s()(e,null==ce?void 0:ce(Lt))},[Lt,ce,ae,l,fe]);v.useImperativeHandle(n,function(){return{nativeElement:qe.current,popupElement:De.current,forceAlign:zt}});var qt=v.useState(0),Wt=(0,o.A)(qt,2),Ut=Wt[0],Xt=Wt[1],Zt=v.useState(0),Gt=(0,o.A)(Zt,2),Yt=Gt[0],Kt=Gt[1],Jt=function(){if(ue&&ze){var e=ze.getBoundingClientRect();Xt(e.width),Kt(e.height)}};function Qt(e,t,n,r){Ze[e]=function(o){var i;null==r||r(o),ut(t,n);for(var a=arguments.length,l=new Array(a>1?a-1:0),s=1;s<a;s++)l[s-1]=arguments[s];null===(i=Xe[e])||void 0===i||i.call.apply(i,[Xe,o].concat(l))}}(0,p.A)(function(){vt&&(It(),vt(),gt(null))},[vt]),(_t||Ht)&&(Ze.onClick=function(e){var t;ot.current&&Ht?ut(!1):!ot.current&&_t&&(Et(e),ut(!0));for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];null===(t=Xe.onClick)||void 0===t||t.call.apply(t,[Xe,e].concat(r))});var en,tn,nn=function(e,t,n,r,o,i,a,l){var s=v.useRef(e);s.current=e;var u=v.useRef(!1);return v.useEffect(function(){if(t&&r&&(!o||i)){var e=function(){u.current=!1},c=function(e){var t;!s.current||a((null===(t=e.composedPath)||void 0===t||null===(t=t.call(e))||void 0===t?void 0:t[0])||e.target)||u.current||l(!1)},f=M(r);f.addEventListener("pointerdown",e,!0),f.addEventListener("mousedown",c,!0),f.addEventListener("contextmenu",c,!0);var m=(0,d.j)(n);return m&&(m.addEventListener("mousedown",c,!0),m.addEventListener("contextmenu",c,!0)),function(){f.removeEventListener("pointerdown",e,!0),f.removeEventListener("mousedown",c,!0),f.removeEventListener("contextmenu",c,!0),m&&(m.removeEventListener("mousedown",c,!0),m.removeEventListener("contextmenu",c,!0))}}},[t,n,r,o,i]),function(){u.current=!0}}(nt,Ht,ze,$e,Z,Y,Ge,ut),rn=Dt.has("hover"),on=jt.has("hover");rn&&(Qt("onMouseEnter",!0,B,function(e){Et(e)}),Qt("onPointerEnter",!0,B,function(e){Et(e)}),en=function(e){(nt||ft)&&null!=$e&&$e.contains(e.target)&&ut(!0,B)},fe&&(Ze.onMouseMove=function(e){var t;null===(t=Xe.onMouseMove)||void 0===t||t.call(Xe,e)})),on&&(Qt("onMouseLeave",!1,W),Qt("onPointerLeave",!1,W),tn=function(){ut(!1,W)}),Dt.has("focus")&&Qt("onFocus",!0,U),jt.has("focus")&&Qt("onBlur",!1,X),Dt.has("contextMenu")&&(Ze.onContextMenu=function(e){var t;ot.current&&jt.has("contextMenu")?ut(!1):(Et(e),ut(!0)),e.preventDefault();for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];null===(t=Xe.onContextMenu)||void 0===t||t.call.apply(t,[Xe,e].concat(r))}),Ee&&(Ze.className=s()(Xe.className,Ee));var an=v.useRef(!1);an.current||(an.current=J||nt||ft);var ln=(0,r.A)((0,r.A)({},Xe),Ze),sn={};["onContextMenu","onClick","onMouseDown","onTouchStart","onMouseEnter","onMouseLeave","onFocus","onBlur"].forEach(function(e){xe[e]&&(sn[e]=function(){for(var t,n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];null===(t=ln[e])||void 0===t||t.call.apply(t,[ln].concat(r)),xe[e].apply(xe,r)})});var un=v.cloneElement(Ue,(0,r.A)((0,r.A)({},ln),sn)),cn={x:Mt,y:Tt},dn=he?(0,r.A)({},!0!==he?he:{}):null;return v.createElement(v.Fragment,null,v.createElement(u.A,{disabled:!nt,ref:We,onResize:function(){Jt(),zt()}},v.createElement(x,{getTriggerDOMNode:Ae},un)),an.current&&v.createElement(k.Provider,{value:Ne},v.createElement(A,{portal:e,ref:je,prefixCls:l,popup:te,className:s()(ne,Bt),style:re,target:ze,onMouseEnter:en,onMouseLeave:tn,onPointerEnter:en,zIndex:se,open:nt,keepDom:ft,fresh:de,onClick:me,onPointerDownCapture:nn,mask:Z,motion:Ye,maskMotion:Ke,onVisibleChanged:function(e){mt(!1),It(),null==z||z(e)},onPrepare:function(){return new Promise(function(e){Jt(),gt(function(){return e})})},forceRender:J,autoDestroy:ke,getPopupContainer:K,align:Lt,arrow:dn,arrowPos:cn,ready:kt,offsetX:Ft,offsetY:St,offsetR:Pt,offsetB:Ot,onAlign:zt,stretch:ue,targetWidth:Ut/Nt,targetHeight:Yt/Rt})))});return t}(a.A)},2467:function(e,t){t.A=function(e){if(!e)return!1;if(e instanceof Element){if(e.offsetParent)return!0;if(e.getBBox){var t=e.getBBox(),n=t.width,r=t.height;if(n||r)return!0}if(e.getBoundingClientRect){var o=e.getBoundingClientRect(),i=o.width,a=o.height;if(i||a)return!0}}return!1}},2546:function(e,t,n){n.d(t,{A:function(){return i}});var r=n(6288),o=n(6540);function i(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=[];return o.Children.forEach(e,function(e){(null!=e||t.keepEmpty)&&(Array.isArray(e)?n=n.concat(i(e)):(0,r.A)(e)&&e.props?n=n.concat(i(e.props.children,t)):n.push(e))}),n}},2595:function(e,t,n){n.d(t,{A:function(){return b}});var r=n(5544),o=n(6540),i=n(961),a=n(998),l=(n(8210),n(8719)),s=o.createContext(null),u=n(436),c=n(981),d=[];var f=n(5089);function m(e){var t="rc-scrollbar-measure-".concat(Math.random().toString(36).substring(7)),n=document.createElement("div");n.id=t;var r,o,i=n.style;if(i.position="absolute",i.left="0",i.top="0",i.width="100px",i.height="100px",i.overflow="scroll",e){var a=getComputedStyle(e);i.scrollbarColor=a.scrollbarColor,i.scrollbarWidth=a.scrollbarWidth;var l=getComputedStyle(e,"::-webkit-scrollbar"),s=parseInt(l.width,10),u=parseInt(l.height,10);try{var c=s?"width: ".concat(l.width,";"):"",d=u?"height: ".concat(l.height,";"):"";(0,f.BD)("\n#".concat(t,"::-webkit-scrollbar {\n").concat(c,"\n").concat(d,"\n}"),t)}catch(h){console.error(h),r=s,o=u}}document.body.appendChild(n);var m=e&&r&&!isNaN(r)?r:n.offsetWidth-n.clientWidth,p=e&&o&&!isNaN(o)?o:n.offsetHeight-n.clientHeight;return document.body.removeChild(n),(0,f.m6)(t),{width:m,height:p}}var p="rc-util-locker-".concat(Date.now()),h=0;function v(e){var t=!!e,n=o.useState(function(){return h+=1,"".concat(p,"_").concat(h)}),i=(0,r.A)(n,1)[0];(0,c.A)(function(){if(t){var e=(r=document.body,"undefined"!=typeof document&&r&&r instanceof Element?m(r):{width:0,height:0}).width,n=document.body.scrollHeight>(window.innerHeight||document.documentElement.clientHeight)&&window.innerWidth>document.body.offsetWidth;(0,f.BD)("\nhtml body {\n  overflow-y: hidden;\n  ".concat(n?"width: calc(100% - ".concat(e,"px);"):"","\n}"),i)}else(0,f.m6)(i);var r;return function(){(0,f.m6)(i)}},[t,i])}var g=!1;var y=function(e){return!1!==e&&((0,a.A)()&&e?"string"==typeof e?document.querySelector(e):"function"==typeof e?e():e:null)};var b=o.forwardRef(function(e,t){var n=e.open,f=e.autoLock,m=e.getContainer,p=(e.debug,e.autoDestroy),h=void 0===p||p,b=e.children,w=o.useState(n),C=(0,r.A)(w,2),E=C[0],A=C[1],x=E||n;o.useEffect(function(){(h||n)&&A(n)},[n,h]);var k=o.useState(function(){return y(m)}),F=(0,r.A)(k,2),S=F[0],P=F[1];o.useEffect(function(){var e=y(m);P(null!=e?e:null)});var O=function(e){var t=o.useState(function(){return(0,a.A)()?document.createElement("div"):null}),n=(0,r.A)(t,1)[0],i=o.useRef(!1),l=o.useContext(s),f=o.useState(d),m=(0,r.A)(f,2),p=m[0],h=m[1],v=l||(i.current?void 0:function(e){h(function(t){return[e].concat((0,u.A)(t))})});function g(){n.parentElement||document.body.appendChild(n),i.current=!0}function y(){var e;null===(e=n.parentElement)||void 0===e||e.removeChild(n),i.current=!1}return(0,c.A)(function(){return e?l?l(g):g():y(),y},[e]),(0,c.A)(function(){p.length&&(p.forEach(function(e){return e()}),h(d))},[p]),[n,v]}(x&&!S),M=(0,r.A)(O,2),T=M[0],N=M[1],R=null!=S?S:T;v(f&&n&&(0,a.A)()&&(R===T||R===document.body));var L=null;b&&(0,l.f3)(b)&&t&&(L=b.ref);var I=(0,l.xK)(L,t);if(!x||!(0,a.A)()||void 0===S)return null;var $,V=!1===R||("boolean"==typeof $&&(g=$),g),D=b;return t&&(D=o.cloneElement(b,{ref:I})),o.createElement(s.Provider,{value:N},V?D:(0,i.createPortal)(D,R))})},2640:function(e,t,n){n.d(t,{A:function(){return r}});const r=(0,n(1788).A)("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]])},2897:function(e,t,n){var r=n(6540),o=n(4241),i=n(6327);t.A=e=>{const{space:t,form:n,children:a}=e;if(null==a)return null;let l=a;return n&&(l=r.createElement(o.XB,{override:!0,status:!0},l)),t&&(l=r.createElement(i.K6,null,l)),l}},3257:function(e,t,n){n.d(t,{A:function(){return l}});var r=n(5201);const o={left:{points:["cr","cl"]},right:{points:["cl","cr"]},top:{points:["bc","tc"]},bottom:{points:["tc","bc"]},topLeft:{points:["bl","tl"]},leftTop:{points:["tr","tl"]},topRight:{points:["br","tr"]},rightTop:{points:["tl","tr"]},bottomRight:{points:["tr","br"]},rightBottom:{points:["bl","br"]},bottomLeft:{points:["tl","bl"]},leftBottom:{points:["br","bl"]}},i={topLeft:{points:["bl","tc"]},leftTop:{points:["tr","cl"]},topRight:{points:["br","tc"]},rightTop:{points:["tl","cr"]},bottomRight:{points:["tr","bc"]},rightBottom:{points:["bl","cr"]},bottomLeft:{points:["tl","bc"]},leftBottom:{points:["br","cl"]}},a=new Set(["topLeft","topRight","bottomLeft","bottomRight","leftTop","leftBottom","rightTop","rightBottom"]);function l(e){const{arrowWidth:t,autoAdjustOverflow:n,arrowPointAtCenter:l,offset:s,borderRadius:u,visibleFirst:c}=e,d=t/2,f={};return Object.keys(o).forEach(e=>{const m=l&&i[e]||o[e],p=Object.assign(Object.assign({},m),{offset:[0,0],dynamicInset:!0});switch(f[e]=p,a.has(e)&&(p.autoArrow=!1),e){case"top":case"topLeft":case"topRight":p.offset[1]=-d-s;break;case"bottom":case"bottomLeft":case"bottomRight":p.offset[1]=d+s;break;case"left":case"leftTop":case"leftBottom":p.offset[0]=-d-s;break;case"right":case"rightTop":case"rightBottom":p.offset[0]=d+s}const h=(0,r.Ke)({contentRadius:u,limitVerticalRadius:!0});if(l)switch(e){case"topLeft":case"bottomLeft":p.offset[0]=-h.arrowOffsetHorizontal-d;break;case"topRight":case"bottomRight":p.offset[0]=h.arrowOffsetHorizontal+d;break;case"leftTop":case"rightTop":p.offset[1]=2*-h.arrowOffsetHorizontal+d;break;case"leftBottom":case"rightBottom":p.offset[1]=2*h.arrowOffsetHorizontal-d}p.overflow=function(e,t,n,r){if(!1===r)return{adjustX:!1,adjustY:!1};const o=r&&"object"==typeof r?r:{},i={};switch(e){case"top":case"bottom":i.shiftX=2*t.arrowOffsetHorizontal+n,i.shiftY=!0,i.adjustY=!0;break;case"left":case"right":i.shiftY=2*t.arrowOffsetVertical+n,i.shiftX=!0,i.adjustX=!0}const a=Object.assign(Object.assign({},i),o);return a.shiftX||(a.adjustX=!0),a.shiftY||(a.adjustY=!0),a}(e,h,t,n),c&&(p.htmlRegion="visibleFirst")}),f}},3592:function(e,t,n){n.d(t,{D0:function(){return ye},_z:function(){return C},Op:function(){return Oe},B8:function(){return be},EF:function(){return E},Ay:function(){return Ve},mN:function(){return Se},FH:function(){return Ie}});var r=n(6540),o=n(8168),i=n(3986),a=n(5041),l=n(467),s=n(9379),u=n(436),c=n(3029),d=n(2901),f=n(9417),m=n(5501),p=n(9426),h=n(4467),v=n(2546),g=n(3210),y=n(8210),b="RC_FORM_INTERNAL_HOOKS",w=function(){(0,y.Ay)(!1,"Can not find FormContext. Please make sure you wrap Field under Form.")},C=r.createContext({getFieldValue:w,getFieldsValue:w,getFieldError:w,getFieldWarning:w,getFieldsError:w,isFieldsTouched:w,isFieldTouched:w,isFieldValidating:w,isFieldsValidating:w,resetFields:w,setFields:w,setFieldValue:w,setFieldsValue:w,validateFields:w,submit:w,getInternalHooks:function(){return w(),{dispatch:w,initEntityValue:w,registerField:w,useSubscribe:w,setInitialValues:w,destroyForm:w,setCallbacks:w,registerWatch:w,getFields:w,setValidateMessages:w,setPreserve:w,getInitialValue:w}}}),E=r.createContext(null);function A(e){return null==e?[]:Array.isArray(e)?e:[e]}var x=n(2284);function k(){return{default:"Validation error on field %s",required:"%s is required",enum:"%s must be one of %s",whitespace:"%s cannot be empty",date:{format:"%s date %s is invalid for format %s",parse:"%s date could not be parsed, %s is invalid ",invalid:"%s date %s is invalid"},types:{string:"%s is not a %s",method:"%s is not a %s (function)",array:"%s is not an %s",object:"%s is not an %s",number:"%s is not a %s",date:"%s is not a %s",boolean:"%s is not a %s",integer:"%s is not an %s",float:"%s is not a %s",regexp:"%s is not a valid %s",email:"%s is not a valid %s",url:"%s is not a valid %s",hex:"%s is not a valid %s"},string:{len:"%s must be exactly %s characters",min:"%s must be at least %s characters",max:"%s cannot be longer than %s characters",range:"%s must be between %s and %s characters"},number:{len:"%s must equal %s",min:"%s cannot be less than %s",max:"%s cannot be greater than %s",range:"%s must be between %s and %s"},array:{len:"%s must be exactly %s in length",min:"%s cannot be less than %s in length",max:"%s cannot be greater than %s in length",range:"%s must be between %s and %s in length"},pattern:{mismatch:"%s value %s does not match pattern %s"},clone:function(){var e=JSON.parse(JSON.stringify(this));return e.clone=this.clone,e}}}var F=k(),S=n(3437),P=/%[sdj%]/g;function O(e){if(!e||!e.length)return null;var t={};return e.forEach(function(e){var n=e.field;t[n]=t[n]||[],t[n].push(e)}),t}function M(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];var o=0,i=n.length;return"function"==typeof e?e.apply(null,n):"string"==typeof e?e.replace(P,function(e){if("%%"===e)return"%";if(o>=i)return e;switch(e){case"%s":return String(n[o++]);case"%d":return Number(n[o++]);case"%j":try{return JSON.stringify(n[o++])}catch(t){return"[Circular]"}break;default:return e}}):e}function T(e,t){return null==e||(!("array"!==t||!Array.isArray(e)||e.length)||!(!function(e){return"string"===e||"url"===e||"hex"===e||"email"===e||"date"===e||"pattern"===e}(t)||"string"!=typeof e||e))}function N(e,t,n){var r=0,o=e.length;!function i(a){if(a&&a.length)n(a);else{var l=r;r+=1,l<o?t(e[l],i):n([])}}([])}var R=function(e){(0,m.A)(n,e);var t=(0,p.A)(n);function n(e,r){var o;return(0,c.A)(this,n),o=t.call(this,"Async Validation Error"),(0,h.A)((0,f.A)(o),"errors",void 0),(0,h.A)((0,f.A)(o),"fields",void 0),o.errors=e,o.fields=r,o}return(0,d.A)(n)}((0,S.A)(Error));function L(e,t,n,r,o){if(t.first){var i=new Promise(function(t,i){var a=function(e){var t=[];return Object.keys(e).forEach(function(n){t.push.apply(t,(0,u.A)(e[n]||[]))}),t}(e);N(a,n,function(e){return r(e),e.length?i(new R(e,O(e))):t(o)})});return i.catch(function(e){return e}),i}var a=!0===t.firstFields?Object.keys(e):t.firstFields||[],l=Object.keys(e),s=l.length,c=0,d=[],f=new Promise(function(t,i){var f=function(e){if(d.push.apply(d,e),++c===s)return r(d),d.length?i(new R(d,O(d))):t(o)};l.length||(r(d),t(o)),l.forEach(function(t){var r=e[t];-1!==a.indexOf(t)?N(r,n,f):function(e,t,n){var r=[],o=0,i=e.length;function a(e){r.push.apply(r,(0,u.A)(e||[])),++o===i&&n(r)}e.forEach(function(e){t(e,a)})}(r,n,f)})});return f.catch(function(e){return e}),f}function I(e,t){return function(n){var r,o;return r=e.fullFields?function(e,t){for(var n=e,r=0;r<t.length;r++){if(null==n)return n;n=n[t[r]]}return n}(t,e.fullFields):t[n.field||e.fullField],(o=n)&&void 0!==o.message?(n.field=n.field||e.fullField,n.fieldValue=r,n):{message:"function"==typeof n?n():n,fieldValue:r,field:n.field||e.fullField}}}function $(e,t){if(t)for(var n in t)if(t.hasOwnProperty(n)){var r=t[n];"object"===(0,x.A)(r)&&"object"===(0,x.A)(e[n])?e[n]=(0,s.A)((0,s.A)({},e[n]),r):e[n]=r}return e}var V,D="enum",j=function(e,t,n,r,o,i){!e.required||n.hasOwnProperty(e.field)&&!T(t,i||e.type)||r.push(M(o.messages.required,e.fullField))},_=/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+\.)+[a-zA-Z\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]{2,}))$/,H=/^#?([a-f0-9]{6}|[a-f0-9]{3})$/i,z={integer:function(e){return z.number(e)&&parseInt(e,10)===e},float:function(e){return z.number(e)&&!z.integer(e)},array:function(e){return Array.isArray(e)},regexp:function(e){if(e instanceof RegExp)return!0;try{return!!new RegExp(e)}catch(t){return!1}},date:function(e){return"function"==typeof e.getTime&&"function"==typeof e.getMonth&&"function"==typeof e.getYear&&!isNaN(e.getTime())},number:function(e){return!isNaN(e)&&"number"==typeof e},object:function(e){return"object"===(0,x.A)(e)&&!z.array(e)},method:function(e){return"function"==typeof e},email:function(e){return"string"==typeof e&&e.length<=320&&!!e.match(_)},url:function(e){return"string"==typeof e&&e.length<=2048&&!!e.match(function(){if(V)return V;var e="[a-fA-F\\d:]",t=function(t){return t&&t.includeBoundaries?"(?:(?<=\\s|^)(?=".concat(e,")|(?<=").concat(e,")(?=\\s|$))"):""},n="(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}",r="[a-fA-F\\d]{1,4}",o=["(?:".concat(r,":){7}(?:").concat(r,"|:)"),"(?:".concat(r,":){6}(?:").concat(n,"|:").concat(r,"|:)"),"(?:".concat(r,":){5}(?::").concat(n,"|(?::").concat(r,"){1,2}|:)"),"(?:".concat(r,":){4}(?:(?::").concat(r,"){0,1}:").concat(n,"|(?::").concat(r,"){1,3}|:)"),"(?:".concat(r,":){3}(?:(?::").concat(r,"){0,2}:").concat(n,"|(?::").concat(r,"){1,4}|:)"),"(?:".concat(r,":){2}(?:(?::").concat(r,"){0,3}:").concat(n,"|(?::").concat(r,"){1,5}|:)"),"(?:".concat(r,":){1}(?:(?::").concat(r,"){0,4}:").concat(n,"|(?::").concat(r,"){1,6}|:)"),"(?::(?:(?::".concat(r,"){0,5}:").concat(n,"|(?::").concat(r,"){1,7}|:))")],i="(?:".concat(o.join("|"),")").concat("(?:%[0-9a-zA-Z]{1,})?"),a=new RegExp("(?:^".concat(n,"$)|(?:^").concat(i,"$)")),l=new RegExp("^".concat(n,"$")),s=new RegExp("^".concat(i,"$")),u=function(e){return e&&e.exact?a:new RegExp("(?:".concat(t(e)).concat(n).concat(t(e),")|(?:").concat(t(e)).concat(i).concat(t(e),")"),"g")};u.v4=function(e){return e&&e.exact?l:new RegExp("".concat(t(e)).concat(n).concat(t(e)),"g")},u.v6=function(e){return e&&e.exact?s:new RegExp("".concat(t(e)).concat(i).concat(t(e)),"g")};var c=u.v4().source,d=u.v6().source,f="(?:".concat("(?:(?:[a-z]+:)?//)","|www\\.)").concat("(?:\\S+(?::\\S*)?@)?","(?:localhost|").concat(c,"|").concat(d,"|").concat("(?:(?:[a-z\\u00a1-\\uffff0-9][-_]*)*[a-z\\u00a1-\\uffff0-9]+)").concat("(?:\\.(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)*").concat("(?:\\.(?:[a-z\\u00a1-\\uffff]{2,}))",")").concat("(?::\\d{2,5})?").concat('(?:[/?#][^\\s"]*)?');return V=new RegExp("(?:^".concat(f,"$)"),"i")}())},hex:function(e){return"string"==typeof e&&!!e.match(H)}},B=function(e,t,n,r,o){if(e.required&&void 0===t)j(e,t,n,r,o);else{var i=e.type;["integer","float","array","regexp","object","method","email","number","date","url","hex"].indexOf(i)>-1?z[i](t)||r.push(M(o.messages.types[i],e.fullField,e.type)):i&&(0,x.A)(t)!==e.type&&r.push(M(o.messages.types[i],e.fullField,e.type))}},q={required:j,whitespace:function(e,t,n,r,o){(/^\s+$/.test(t)||""===t)&&r.push(M(o.messages.whitespace,e.fullField))},type:B,range:function(e,t,n,r,o){var i="number"==typeof e.len,a="number"==typeof e.min,l="number"==typeof e.max,s=t,u=null,c="number"==typeof t,d="string"==typeof t,f=Array.isArray(t);if(c?u="number":d?u="string":f&&(u="array"),!u)return!1;f&&(s=t.length),d&&(s=t.replace(/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,"_").length),i?s!==e.len&&r.push(M(o.messages[u].len,e.fullField,e.len)):a&&!l&&s<e.min?r.push(M(o.messages[u].min,e.fullField,e.min)):l&&!a&&s>e.max?r.push(M(o.messages[u].max,e.fullField,e.max)):a&&l&&(s<e.min||s>e.max)&&r.push(M(o.messages[u].range,e.fullField,e.min,e.max))},enum:function(e,t,n,r,o){e[D]=Array.isArray(e[D])?e[D]:[],-1===e[D].indexOf(t)&&r.push(M(o.messages[D],e.fullField,e[D].join(", ")))},pattern:function(e,t,n,r,o){if(e.pattern)if(e.pattern instanceof RegExp)e.pattern.lastIndex=0,e.pattern.test(t)||r.push(M(o.messages.pattern.mismatch,e.fullField,t,e.pattern));else if("string"==typeof e.pattern){new RegExp(e.pattern).test(t)||r.push(M(o.messages.pattern.mismatch,e.fullField,t,e.pattern))}}},W=function(e,t,n,r,o){var i=[],a=Array.isArray(t)?"array":(0,x.A)(t);q.required(e,t,r,i,o,a),n(i)},U=function(e,t,n,r,o){var i=e.type,a=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(T(t,i)&&!e.required)return n();q.required(e,t,r,a,o,i),T(t,i)||q.type(e,t,r,a,o)}n(a)},X={string:function(e,t,n,r,o){var i=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(T(t,"string")&&!e.required)return n();q.required(e,t,r,i,o,"string"),T(t,"string")||(q.type(e,t,r,i,o),q.range(e,t,r,i,o),q.pattern(e,t,r,i,o),!0===e.whitespace&&q.whitespace(e,t,r,i,o))}n(i)},method:function(e,t,n,r,o){var i=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(T(t)&&!e.required)return n();q.required(e,t,r,i,o),void 0!==t&&q.type(e,t,r,i,o)}n(i)},number:function(e,t,n,r,o){var i=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(""===t&&(t=void 0),T(t)&&!e.required)return n();q.required(e,t,r,i,o),void 0!==t&&(q.type(e,t,r,i,o),q.range(e,t,r,i,o))}n(i)},boolean:function(e,t,n,r,o){var i=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(T(t)&&!e.required)return n();q.required(e,t,r,i,o),void 0!==t&&q.type(e,t,r,i,o)}n(i)},regexp:function(e,t,n,r,o){var i=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(T(t)&&!e.required)return n();q.required(e,t,r,i,o),T(t)||q.type(e,t,r,i,o)}n(i)},integer:function(e,t,n,r,o){var i=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(T(t)&&!e.required)return n();q.required(e,t,r,i,o),void 0!==t&&(q.type(e,t,r,i,o),q.range(e,t,r,i,o))}n(i)},float:function(e,t,n,r,o){var i=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(T(t)&&!e.required)return n();q.required(e,t,r,i,o),void 0!==t&&(q.type(e,t,r,i,o),q.range(e,t,r,i,o))}n(i)},array:function(e,t,n,r,o){var i=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(null==t&&!e.required)return n();q.required(e,t,r,i,o,"array"),null!=t&&(q.type(e,t,r,i,o),q.range(e,t,r,i,o))}n(i)},object:function(e,t,n,r,o){var i=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(T(t)&&!e.required)return n();q.required(e,t,r,i,o),void 0!==t&&q.type(e,t,r,i,o)}n(i)},enum:function(e,t,n,r,o){var i=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(T(t)&&!e.required)return n();q.required(e,t,r,i,o),void 0!==t&&q.enum(e,t,r,i,o)}n(i)},pattern:function(e,t,n,r,o){var i=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(T(t,"string")&&!e.required)return n();q.required(e,t,r,i,o),T(t,"string")||q.pattern(e,t,r,i,o)}n(i)},date:function(e,t,n,r,o){var i=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(T(t,"date")&&!e.required)return n();var a;if(q.required(e,t,r,i,o),!T(t,"date"))a=t instanceof Date?t:new Date(t),q.type(e,a,r,i,o),a&&q.range(e,a.getTime(),r,i,o)}n(i)},url:U,hex:U,email:U,required:W,any:function(e,t,n,r,o){var i=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(T(t)&&!e.required)return n();q.required(e,t,r,i,o)}n(i)}},Z=function(){function e(t){(0,c.A)(this,e),(0,h.A)(this,"rules",null),(0,h.A)(this,"_messages",F),this.define(t)}return(0,d.A)(e,[{key:"define",value:function(e){var t=this;if(!e)throw new Error("Cannot configure a schema with no rules");if("object"!==(0,x.A)(e)||Array.isArray(e))throw new Error("Rules must be an object");this.rules={},Object.keys(e).forEach(function(n){var r=e[n];t.rules[n]=Array.isArray(r)?r:[r]})}},{key:"messages",value:function(e){return e&&(this._messages=$(k(),e)),this._messages}},{key:"validate",value:function(t){var n=this,r=t,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(){};if("function"==typeof o&&(i=o,o={}),!this.rules||0===Object.keys(this.rules).length)return i&&i(null,r),Promise.resolve(r);if(o.messages){var a=this.messages();a===F&&(a=k()),$(a,o.messages),o.messages=a}else o.messages=this.messages();var l={};(o.keys||Object.keys(this.rules)).forEach(function(e){var o=n.rules[e],i=r[e];o.forEach(function(o){var a=o;"function"==typeof a.transform&&(r===t&&(r=(0,s.A)({},r)),null!=(i=r[e]=a.transform(i))&&(a.type=a.type||(Array.isArray(i)?"array":(0,x.A)(i)))),(a="function"==typeof a?{validator:a}:(0,s.A)({},a)).validator=n.getValidationMethod(a),a.validator&&(a.field=e,a.fullField=a.fullField||e,a.type=n.getType(a),l[e]=l[e]||[],l[e].push({rule:a,value:i,source:r,field:e}))})});var c={};return L(l,o,function(t,n){var i,a=t.rule,l=!("object"!==a.type&&"array"!==a.type||"object"!==(0,x.A)(a.fields)&&"object"!==(0,x.A)(a.defaultField));function d(e,t){return(0,s.A)((0,s.A)({},t),{},{fullField:"".concat(a.fullField,".").concat(e),fullFields:a.fullFields?[].concat((0,u.A)(a.fullFields),[e]):[e]})}function f(){var i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],f=Array.isArray(i)?i:[i];!o.suppressWarning&&f.length&&e.warning("async-validator:",f),f.length&&void 0!==a.message&&(f=[].concat(a.message));var m=f.map(I(a,r));if(o.first&&m.length)return c[a.field]=1,n(m);if(l){if(a.required&&!t.value)return void 0!==a.message?m=[].concat(a.message).map(I(a,r)):o.error&&(m=[o.error(a,M(o.messages.required,a.field))]),n(m);var p={};a.defaultField&&Object.keys(t.value).map(function(e){p[e]=a.defaultField}),p=(0,s.A)((0,s.A)({},p),t.rule.fields);var h={};Object.keys(p).forEach(function(e){var t=p[e],n=Array.isArray(t)?t:[t];h[e]=n.map(d.bind(null,e))});var v=new e(h);v.messages(o.messages),t.rule.options&&(t.rule.options.messages=o.messages,t.rule.options.error=o.error),v.validate(t.value,t.rule.options||o,function(e){var t=[];m&&m.length&&t.push.apply(t,(0,u.A)(m)),e&&e.length&&t.push.apply(t,(0,u.A)(e)),n(t.length?t:null)})}else n(m)}if(l=l&&(a.required||!a.required&&t.value),a.field=t.field,a.asyncValidator)i=a.asyncValidator(a,t.value,f,t.source,o);else if(a.validator){try{i=a.validator(a,t.value,f,t.source,o)}catch(h){var m,p;null===(m=(p=console).error)||void 0===m||m.call(p,h),o.suppressValidatorError||setTimeout(function(){throw h},0),f(h.message)}!0===i?f():!1===i?f("function"==typeof a.message?a.message(a.fullField||a.field):a.message||"".concat(a.fullField||a.field," fails")):i instanceof Array?f(i):i instanceof Error&&f(i.message)}i&&i.then&&i.then(function(){return f()},function(e){return f(e)})},function(e){!function(e){var t=[],n={};function o(e){var n;Array.isArray(e)?t=(n=t).concat.apply(n,(0,u.A)(e)):t.push(e)}for(var a=0;a<e.length;a++)o(e[a]);t.length?(n=O(t),i(t,n)):i(null,r)}(e)},r)}},{key:"getType",value:function(e){if(void 0===e.type&&e.pattern instanceof RegExp&&(e.type="pattern"),"function"!=typeof e.validator&&e.type&&!X.hasOwnProperty(e.type))throw new Error(M("Unknown rule type %s",e.type));return e.type||"string"}},{key:"getValidationMethod",value:function(e){if("function"==typeof e.validator)return e.validator;var t=Object.keys(e),n=t.indexOf("message");return-1!==n&&t.splice(n,1),1===t.length&&"required"===t[0]?X.required:X[this.getType(e)]||void 0}}]),e}();(0,h.A)(Z,"register",function(e,t){if("function"!=typeof t)throw new Error("Cannot register a validator by type, validator is not a function");X[e]=t}),(0,h.A)(Z,"warning",function(){}),(0,h.A)(Z,"messages",F),(0,h.A)(Z,"validators",X);var G=Z,Y="'${name}' is not a valid ${type}",K={default:"Validation error on field '${name}'",required:"'${name}' is required",enum:"'${name}' must be one of [${enum}]",whitespace:"'${name}' cannot be empty",date:{format:"'${name}' is invalid for format date",parse:"'${name}' could not be parsed as date",invalid:"'${name}' is invalid date"},types:{string:Y,method:Y,array:Y,object:Y,number:Y,date:Y,boolean:Y,integer:Y,float:Y,regexp:Y,email:Y,url:Y,hex:Y},string:{len:"'${name}' must be exactly ${len} characters",min:"'${name}' must be at least ${min} characters",max:"'${name}' cannot be longer than ${max} characters",range:"'${name}' must be between ${min} and ${max} characters"},number:{len:"'${name}' must equal ${len}",min:"'${name}' cannot be less than ${min}",max:"'${name}' cannot be greater than ${max}",range:"'${name}' must be between ${min} and ${max}"},array:{len:"'${name}' must be exactly ${len} in length",min:"'${name}' cannot be less than ${min} in length",max:"'${name}' cannot be greater than ${max} in length",range:"'${name}' must be between ${min} and ${max} in length"},pattern:{mismatch:"'${name}' does not match pattern ${pattern}"}},J=n(488),Q=G;function ee(e,t){return e.replace(/\\?\$\{\w+\}/g,function(e){if(e.startsWith("\\"))return e.slice(1);var n=e.slice(2,-1);return t[n]})}var te="CODE_LOGIC_ERROR";function ne(e,t,n,r,o){return re.apply(this,arguments)}function re(){return re=(0,l.A)((0,a.A)().mark(function e(t,n,o,i,l){var c,d,f,m,p,v,g,y,b;return(0,a.A)().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return delete(c=(0,s.A)({},o)).ruleIndex,Q.warning=function(){},c.validator&&(d=c.validator,c.validator=function(){try{return d.apply(void 0,arguments)}catch(e){return console.error(e),Promise.reject(te)}}),f=null,c&&"array"===c.type&&c.defaultField&&(f=c.defaultField,delete c.defaultField),m=new Q((0,h.A)({},t,[c])),p=(0,J.h)(K,i.validateMessages),m.messages(p),v=[],e.prev=10,e.next=13,Promise.resolve(m.validate((0,h.A)({},t,n),(0,s.A)({},i)));case 13:e.next=18;break;case 15:e.prev=15,e.t0=e.catch(10),e.t0.errors&&(v=e.t0.errors.map(function(e,t){var n=e.message,o=n===te?p.default:n;return r.isValidElement(o)?r.cloneElement(o,{key:"error_".concat(t)}):o}));case 18:if(v.length||!f){e.next=23;break}return e.next=21,Promise.all(n.map(function(e,n){return ne("".concat(t,".").concat(n),e,f,i,l)}));case 21:return g=e.sent,e.abrupt("return",g.reduce(function(e,t){return[].concat((0,u.A)(e),(0,u.A)(t))},[]));case 23:return y=(0,s.A)((0,s.A)({},o),{},{name:t,enum:(o.enum||[]).join(", ")},l),b=v.map(function(e){return"string"==typeof e?ee(e,y):e}),e.abrupt("return",b);case 26:case"end":return e.stop()}},e,null,[[10,15]])})),re.apply(this,arguments)}function oe(e,t,n,r,o,i){var u,c=e.join("."),d=n.map(function(e,t){var n=e.validator,r=(0,s.A)((0,s.A)({},e),{},{ruleIndex:t});return n&&(r.validator=function(e,t,r){var o=!1,i=n(e,t,function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];Promise.resolve().then(function(){(0,y.Ay)(!o,"Your validator function has already return a promise. `callback` will be ignored."),o||r.apply(void 0,t)})});o=i&&"function"==typeof i.then&&"function"==typeof i.catch,(0,y.Ay)(o,"`callback` is deprecated. Please return a promise instead."),o&&i.then(function(){r()}).catch(function(e){r(e||" ")})}),r}).sort(function(e,t){var n=e.warningOnly,r=e.ruleIndex,o=t.warningOnly,i=t.ruleIndex;return!!n==!!o?r-i:n?1:-1});if(!0===o)u=new Promise(function(){var e=(0,l.A)((0,a.A)().mark(function e(n,o){var l,s,u;return(0,a.A)().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:l=0;case 1:if(!(l<d.length)){e.next=12;break}return s=d[l],e.next=5,ne(c,t,s,r,i);case 5:if(!(u=e.sent).length){e.next=9;break}return o([{errors:u,rule:s}]),e.abrupt("return");case 9:l+=1,e.next=1;break;case 12:n([]);case 13:case"end":return e.stop()}},e)}));return function(t,n){return e.apply(this,arguments)}}());else{var f=d.map(function(e){return ne(c,t,e,r,i).then(function(t){return{errors:t,rule:e}})});u=(o?function(e){return ae.apply(this,arguments)}(f):function(e){return ie.apply(this,arguments)}(f)).then(function(e){return Promise.reject(e)})}return u.catch(function(e){return e}),u}function ie(){return(ie=(0,l.A)((0,a.A)().mark(function e(t){return(0,a.A)().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",Promise.all(t).then(function(e){var t;return(t=[]).concat.apply(t,(0,u.A)(e))}));case 1:case"end":return e.stop()}},e)}))).apply(this,arguments)}function ae(){return(ae=(0,l.A)((0,a.A)().mark(function e(t){var n;return(0,a.A)().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return n=0,e.abrupt("return",new Promise(function(e){t.forEach(function(r){r.then(function(r){r.errors.length&&e([r]),(n+=1)===t.length&&e([])})})}));case 2:case"end":return e.stop()}},e)}))).apply(this,arguments)}var le=n(6300);function se(e){return A(e)}function ue(e,t){var n={};return t.forEach(function(t){var r=(0,le.A)(e,t);n=(0,J.A)(n,t,r)}),n}function ce(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return e&&e.some(function(e){return de(t,e,n)})}function de(e,t){return!(!e||!t)&&(!(!(arguments.length>2&&void 0!==arguments[2]&&arguments[2])&&e.length!==t.length)&&t.every(function(t,n){return e[n]===t}))}function fe(e){var t=arguments.length<=1?void 0:arguments[1];return t&&t.target&&"object"===(0,x.A)(t.target)&&e in t.target?t.target[e]:t}function me(e,t,n){var r=e.length;if(t<0||t>=r||n<0||n>=r)return e;var o=e[t],i=t-n;return i>0?[].concat((0,u.A)(e.slice(0,n)),[o],(0,u.A)(e.slice(n,t)),(0,u.A)(e.slice(t+1,r))):i<0?[].concat((0,u.A)(e.slice(0,t)),(0,u.A)(e.slice(t+1,n+1)),[o],(0,u.A)(e.slice(n+1,r))):e}var pe=["name"],he=[];function ve(e,t,n,r,o,i){return"function"==typeof e?e(t,n,"source"in i?{source:i.source}:{}):r!==o}var ge=function(e){(0,m.A)(n,e);var t=(0,p.A)(n);function n(e){var o;((0,c.A)(this,n),o=t.call(this,e),(0,h.A)((0,f.A)(o),"state",{resetCount:0}),(0,h.A)((0,f.A)(o),"cancelRegisterFunc",null),(0,h.A)((0,f.A)(o),"mounted",!1),(0,h.A)((0,f.A)(o),"touched",!1),(0,h.A)((0,f.A)(o),"dirty",!1),(0,h.A)((0,f.A)(o),"validatePromise",void 0),(0,h.A)((0,f.A)(o),"prevValidating",void 0),(0,h.A)((0,f.A)(o),"errors",he),(0,h.A)((0,f.A)(o),"warnings",he),(0,h.A)((0,f.A)(o),"cancelRegister",function(){var e=o.props,t=e.preserve,n=e.isListField,r=e.name;o.cancelRegisterFunc&&o.cancelRegisterFunc(n,t,se(r)),o.cancelRegisterFunc=null}),(0,h.A)((0,f.A)(o),"getNamePath",function(){var e=o.props,t=e.name,n=e.fieldContext.prefixName,r=void 0===n?[]:n;return void 0!==t?[].concat((0,u.A)(r),(0,u.A)(t)):[]}),(0,h.A)((0,f.A)(o),"getRules",function(){var e=o.props,t=e.rules,n=void 0===t?[]:t,r=e.fieldContext;return n.map(function(e){return"function"==typeof e?e(r):e})}),(0,h.A)((0,f.A)(o),"refresh",function(){o.mounted&&o.setState(function(e){return{resetCount:e.resetCount+1}})}),(0,h.A)((0,f.A)(o),"metaCache",null),(0,h.A)((0,f.A)(o),"triggerMetaEvent",function(e){var t=o.props.onMetaChange;if(t){var n=(0,s.A)((0,s.A)({},o.getMeta()),{},{destroy:e});(0,g.A)(o.metaCache,n)||t(n),o.metaCache=n}else o.metaCache=null}),(0,h.A)((0,f.A)(o),"onStoreChange",function(e,t,n){var r=o.props,i=r.shouldUpdate,a=r.dependencies,l=void 0===a?[]:a,s=r.onReset,u=n.store,c=o.getNamePath(),d=o.getValue(e),f=o.getValue(u),m=t&&ce(t,c);switch("valueUpdate"!==n.type||"external"!==n.source||(0,g.A)(d,f)||(o.touched=!0,o.dirty=!0,o.validatePromise=null,o.errors=he,o.warnings=he,o.triggerMetaEvent()),n.type){case"reset":if(!t||m)return o.touched=!1,o.dirty=!1,o.validatePromise=void 0,o.errors=he,o.warnings=he,o.triggerMetaEvent(),null==s||s(),void o.refresh();break;case"remove":if(i&&ve(i,e,u,d,f,n))return void o.reRender();break;case"setField":var p=n.data;if(m)return"touched"in p&&(o.touched=p.touched),"validating"in p&&!("originRCField"in p)&&(o.validatePromise=p.validating?Promise.resolve([]):null),"errors"in p&&(o.errors=p.errors||he),"warnings"in p&&(o.warnings=p.warnings||he),o.dirty=!0,o.triggerMetaEvent(),void o.reRender();if("value"in p&&ce(t,c,!0))return void o.reRender();if(i&&!c.length&&ve(i,e,u,d,f,n))return void o.reRender();break;case"dependenciesUpdate":if(l.map(se).some(function(e){return ce(n.relatedFields,e)}))return void o.reRender();break;default:if(m||(!l.length||c.length||i)&&ve(i,e,u,d,f,n))return void o.reRender()}!0===i&&o.reRender()}),(0,h.A)((0,f.A)(o),"validateRules",function(e){var t=o.getNamePath(),n=o.getValue(),r=e||{},i=r.triggerName,s=r.validateOnly,c=void 0!==s&&s,d=Promise.resolve().then((0,l.A)((0,a.A)().mark(function r(){var l,s,c,f,m,p,h;return(0,a.A)().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:if(o.mounted){r.next=2;break}return r.abrupt("return",[]);case 2:if(l=o.props,s=l.validateFirst,c=void 0!==s&&s,f=l.messageVariables,m=l.validateDebounce,p=o.getRules(),i&&(p=p.filter(function(e){return e}).filter(function(e){var t=e.validateTrigger;return!t||A(t).includes(i)})),!m||!i){r.next=10;break}return r.next=8,new Promise(function(e){setTimeout(e,m)});case 8:if(o.validatePromise===d){r.next=10;break}return r.abrupt("return",[]);case 10:return(h=oe(t,n,p,e,c,f)).catch(function(e){return e}).then(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:he;if(o.validatePromise===d){var t;o.validatePromise=null;var n=[],r=[];null===(t=e.forEach)||void 0===t||t.call(e,function(e){var t=e.rule.warningOnly,o=e.errors,i=void 0===o?he:o;t?r.push.apply(r,(0,u.A)(i)):n.push.apply(n,(0,u.A)(i))}),o.errors=n,o.warnings=r,o.triggerMetaEvent(),o.reRender()}}),r.abrupt("return",h);case 13:case"end":return r.stop()}},r)})));return c||(o.validatePromise=d,o.dirty=!0,o.errors=he,o.warnings=he,o.triggerMetaEvent(),o.reRender()),d}),(0,h.A)((0,f.A)(o),"isFieldValidating",function(){return!!o.validatePromise}),(0,h.A)((0,f.A)(o),"isFieldTouched",function(){return o.touched}),(0,h.A)((0,f.A)(o),"isFieldDirty",function(){return!(!o.dirty&&void 0===o.props.initialValue)||void 0!==(0,o.props.fieldContext.getInternalHooks(b).getInitialValue)(o.getNamePath())}),(0,h.A)((0,f.A)(o),"getErrors",function(){return o.errors}),(0,h.A)((0,f.A)(o),"getWarnings",function(){return o.warnings}),(0,h.A)((0,f.A)(o),"isListField",function(){return o.props.isListField}),(0,h.A)((0,f.A)(o),"isList",function(){return o.props.isList}),(0,h.A)((0,f.A)(o),"isPreserve",function(){return o.props.preserve}),(0,h.A)((0,f.A)(o),"getMeta",function(){return o.prevValidating=o.isFieldValidating(),{touched:o.isFieldTouched(),validating:o.prevValidating,errors:o.errors,warnings:o.warnings,name:o.getNamePath(),validated:null===o.validatePromise}}),(0,h.A)((0,f.A)(o),"getOnlyChild",function(e){if("function"==typeof e){var t=o.getMeta();return(0,s.A)((0,s.A)({},o.getOnlyChild(e(o.getControlled(),t,o.props.fieldContext))),{},{isFunction:!0})}var n=(0,v.A)(e);return 1===n.length&&r.isValidElement(n[0])?{child:n[0],isFunction:!1}:{child:n,isFunction:!1}}),(0,h.A)((0,f.A)(o),"getValue",function(e){var t=o.props.fieldContext.getFieldsValue,n=o.getNamePath();return(0,le.A)(e||t(!0),n)}),(0,h.A)((0,f.A)(o),"getControlled",function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=o.props,n=t.name,r=t.trigger,i=t.validateTrigger,a=t.getValueFromEvent,l=t.normalize,u=t.valuePropName,c=t.getValueProps,d=t.fieldContext,f=void 0!==i?i:d.validateTrigger,m=o.getNamePath(),p=d.getInternalHooks,v=d.getFieldsValue,g=p(b).dispatch,y=o.getValue(),w=c||function(e){return(0,h.A)({},u,e)},C=e[r],E=void 0!==n?w(y):{};var x=(0,s.A)((0,s.A)({},e),E);return x[r]=function(){var e;o.touched=!0,o.dirty=!0,o.triggerMetaEvent();for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];e=a?a.apply(void 0,n):fe.apply(void 0,[u].concat(n)),l&&(e=l(e,y,v(!0))),e!==y&&g({type:"updateValue",namePath:m,value:e}),C&&C.apply(void 0,n)},A(f||[]).forEach(function(e){var t=x[e];x[e]=function(){t&&t.apply(void 0,arguments);var n=o.props.rules;n&&n.length&&g({type:"validateField",namePath:m,triggerName:e})}}),x}),e.fieldContext)&&(0,(0,e.fieldContext.getInternalHooks)(b).initEntityValue)((0,f.A)(o));return o}return(0,d.A)(n,[{key:"componentDidMount",value:function(){var e=this.props,t=e.shouldUpdate,n=e.fieldContext;if(this.mounted=!0,n){var r=(0,n.getInternalHooks)(b).registerField;this.cancelRegisterFunc=r(this)}!0===t&&this.reRender()}},{key:"componentWillUnmount",value:function(){this.cancelRegister(),this.triggerMetaEvent(!0),this.mounted=!1}},{key:"reRender",value:function(){this.mounted&&this.forceUpdate()}},{key:"render",value:function(){var e,t=this.state.resetCount,n=this.props.children,o=this.getOnlyChild(n),i=o.child;return o.isFunction?e=i:r.isValidElement(i)?e=r.cloneElement(i,this.getControlled(i.props)):((0,y.Ay)(!i,"`children` of Field is not validate ReactElement."),e=i),r.createElement(r.Fragment,{key:t},e)}}]),n}(r.Component);(0,h.A)(ge,"contextType",C),(0,h.A)(ge,"defaultProps",{trigger:"onChange",valuePropName:"value"});var ye=function(e){var t,n=e.name,a=(0,i.A)(e,pe),l=r.useContext(C),s=r.useContext(E),u=void 0!==n?se(n):void 0,c=null!==(t=a.isListField)&&void 0!==t?t:!!s,d="keep";return c||(d="_".concat((u||[]).join("_"))),r.createElement(ge,(0,o.A)({key:d,name:u,isListField:c},a,{fieldContext:l}))};var be=function(e){var t=e.name,n=e.initialValue,o=e.children,i=e.rules,a=e.validateTrigger,l=e.isListField,c=r.useContext(C),d=r.useContext(E),f=r.useRef({keys:[],id:0}).current,m=r.useMemo(function(){var e=se(c.prefixName)||[];return[].concat((0,u.A)(e),(0,u.A)(se(t)))},[c.prefixName,t]),p=r.useMemo(function(){return(0,s.A)((0,s.A)({},c),{},{prefixName:m})},[c,m]),h=r.useMemo(function(){return{getKey:function(e){var t=m.length,n=e[t];return[f.keys[n],e.slice(t+1)]}}},[m]);return"function"!=typeof o?((0,y.Ay)(!1,"Form.List only accepts function as children."),null):r.createElement(E.Provider,{value:h},r.createElement(C.Provider,{value:p},r.createElement(ye,{name:[],shouldUpdate:function(e,t,n){return"internal"!==n.source&&e!==t},rules:i,validateTrigger:a,initialValue:n,isList:!0,isListField:null!=l?l:!!d},function(e,t){var n=e.value,r=void 0===n?[]:n,i=e.onChange,a=c.getFieldValue,l=function(){return a(m||[])||[]},s={add:function(e,t){var n=l();t>=0&&t<=n.length?(f.keys=[].concat((0,u.A)(f.keys.slice(0,t)),[f.id],(0,u.A)(f.keys.slice(t))),i([].concat((0,u.A)(n.slice(0,t)),[e],(0,u.A)(n.slice(t))))):(f.keys=[].concat((0,u.A)(f.keys),[f.id]),i([].concat((0,u.A)(n),[e]))),f.id+=1},remove:function(e){var t=l(),n=new Set(Array.isArray(e)?e:[e]);n.size<=0||(f.keys=f.keys.filter(function(e,t){return!n.has(t)}),i(t.filter(function(e,t){return!n.has(t)})))},move:function(e,t){if(e!==t){var n=l();e<0||e>=n.length||t<0||t>=n.length||(f.keys=me(f.keys,e,t),i(me(n,e,t)))}}},d=r||[];return Array.isArray(d)||(d=[]),o(d.map(function(e,t){var n=f.keys[t];return void 0===n&&(f.keys[t]=f.id,n=f.keys[t],f.id+=1),{name:t,key:n,isListField:!0}}),s,t)})))},we=n(5544);var Ce="__@field_split__";function Ee(e){return e.map(function(e){return"".concat((0,x.A)(e),":").concat(e)}).join(Ce)}var Ae=function(){function e(){(0,c.A)(this,e),(0,h.A)(this,"kvs",new Map)}return(0,d.A)(e,[{key:"set",value:function(e,t){this.kvs.set(Ee(e),t)}},{key:"get",value:function(e){return this.kvs.get(Ee(e))}},{key:"update",value:function(e,t){var n=t(this.get(e));n?this.set(e,n):this.delete(e)}},{key:"delete",value:function(e){this.kvs.delete(Ee(e))}},{key:"map",value:function(e){return(0,u.A)(this.kvs.entries()).map(function(t){var n=(0,we.A)(t,2),r=n[0],o=n[1],i=r.split(Ce);return e({key:i.map(function(e){var t=e.match(/^([^:]*):(.*)$/),n=(0,we.A)(t,3),r=n[1],o=n[2];return"number"===r?Number(o):o}),value:o})})}},{key:"toJSON",value:function(){var e={};return this.map(function(t){var n=t.key,r=t.value;return e[n.join(".")]=r,null}),e}}]),e}(),xe=Ae,ke=["name"],Fe=(0,d.A)(function e(t){var n=this;(0,c.A)(this,e),(0,h.A)(this,"formHooked",!1),(0,h.A)(this,"forceRootUpdate",void 0),(0,h.A)(this,"subscribable",!0),(0,h.A)(this,"store",{}),(0,h.A)(this,"fieldEntities",[]),(0,h.A)(this,"initialValues",{}),(0,h.A)(this,"callbacks",{}),(0,h.A)(this,"validateMessages",null),(0,h.A)(this,"preserve",null),(0,h.A)(this,"lastValidatePromise",null),(0,h.A)(this,"getForm",function(){return{getFieldValue:n.getFieldValue,getFieldsValue:n.getFieldsValue,getFieldError:n.getFieldError,getFieldWarning:n.getFieldWarning,getFieldsError:n.getFieldsError,isFieldsTouched:n.isFieldsTouched,isFieldTouched:n.isFieldTouched,isFieldValidating:n.isFieldValidating,isFieldsValidating:n.isFieldsValidating,resetFields:n.resetFields,setFields:n.setFields,setFieldValue:n.setFieldValue,setFieldsValue:n.setFieldsValue,validateFields:n.validateFields,submit:n.submit,_init:!0,getInternalHooks:n.getInternalHooks}}),(0,h.A)(this,"getInternalHooks",function(e){return e===b?(n.formHooked=!0,{dispatch:n.dispatch,initEntityValue:n.initEntityValue,registerField:n.registerField,useSubscribe:n.useSubscribe,setInitialValues:n.setInitialValues,destroyForm:n.destroyForm,setCallbacks:n.setCallbacks,setValidateMessages:n.setValidateMessages,getFields:n.getFields,setPreserve:n.setPreserve,getInitialValue:n.getInitialValue,registerWatch:n.registerWatch}):((0,y.Ay)(!1,"`getInternalHooks` is internal usage. Should not call directly."),null)}),(0,h.A)(this,"useSubscribe",function(e){n.subscribable=e}),(0,h.A)(this,"prevWithoutPreserves",null),(0,h.A)(this,"setInitialValues",function(e,t){if(n.initialValues=e||{},t){var r,o=(0,J.h)(e,n.store);null===(r=n.prevWithoutPreserves)||void 0===r||r.map(function(t){var n=t.key;o=(0,J.A)(o,n,(0,le.A)(e,n))}),n.prevWithoutPreserves=null,n.updateStore(o)}}),(0,h.A)(this,"destroyForm",function(e){if(e)n.updateStore({});else{var t=new xe;n.getFieldEntities(!0).forEach(function(e){n.isMergedPreserve(e.isPreserve())||t.set(e.getNamePath(),!0)}),n.prevWithoutPreserves=t}}),(0,h.A)(this,"getInitialValue",function(e){var t=(0,le.A)(n.initialValues,e);return e.length?(0,J.h)(t):t}),(0,h.A)(this,"setCallbacks",function(e){n.callbacks=e}),(0,h.A)(this,"setValidateMessages",function(e){n.validateMessages=e}),(0,h.A)(this,"setPreserve",function(e){n.preserve=e}),(0,h.A)(this,"watchList",[]),(0,h.A)(this,"registerWatch",function(e){return n.watchList.push(e),function(){n.watchList=n.watchList.filter(function(t){return t!==e})}}),(0,h.A)(this,"notifyWatch",function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];if(n.watchList.length){var t=n.getFieldsValue(),r=n.getFieldsValue(!0);n.watchList.forEach(function(n){n(t,r,e)})}}),(0,h.A)(this,"timeoutId",null),(0,h.A)(this,"warningUnhooked",function(){0}),(0,h.A)(this,"updateStore",function(e){n.store=e}),(0,h.A)(this,"getFieldEntities",function(){return arguments.length>0&&void 0!==arguments[0]&&arguments[0]?n.fieldEntities.filter(function(e){return e.getNamePath().length}):n.fieldEntities}),(0,h.A)(this,"getFieldsMap",function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=new xe;return n.getFieldEntities(e).forEach(function(e){var n=e.getNamePath();t.set(n,e)}),t}),(0,h.A)(this,"getFieldEntitiesForNamePathList",function(e){if(!e)return n.getFieldEntities(!0);var t=n.getFieldsMap(!0);return e.map(function(e){var n=se(e);return t.get(n)||{INVALIDATE_NAME_PATH:se(e)}})}),(0,h.A)(this,"getFieldsValue",function(e,t){var r,o,i;if(n.warningUnhooked(),!0===e||Array.isArray(e)?(r=e,o=t):e&&"object"===(0,x.A)(e)&&(i=e.strict,o=e.filter),!0===r&&!o)return n.store;var a=n.getFieldEntitiesForNamePathList(Array.isArray(r)?r:null),l=[];return a.forEach(function(e){var t,n,a,s,u="INVALIDATE_NAME_PATH"in e?e.INVALIDATE_NAME_PATH:e.getNamePath();if(i){if(null!==(a=(s=e).isList)&&void 0!==a&&a.call(s))return}else if(!r&&null!==(t=(n=e).isListField)&&void 0!==t&&t.call(n))return;if(o){var c="getMeta"in e?e.getMeta():null;o(c)&&l.push(u)}else l.push(u)}),ue(n.store,l.map(se))}),(0,h.A)(this,"getFieldValue",function(e){n.warningUnhooked();var t=se(e);return(0,le.A)(n.store,t)}),(0,h.A)(this,"getFieldsError",function(e){return n.warningUnhooked(),n.getFieldEntitiesForNamePathList(e).map(function(t,n){return t&&!("INVALIDATE_NAME_PATH"in t)?{name:t.getNamePath(),errors:t.getErrors(),warnings:t.getWarnings()}:{name:se(e[n]),errors:[],warnings:[]}})}),(0,h.A)(this,"getFieldError",function(e){n.warningUnhooked();var t=se(e);return n.getFieldsError([t])[0].errors}),(0,h.A)(this,"getFieldWarning",function(e){n.warningUnhooked();var t=se(e);return n.getFieldsError([t])[0].warnings}),(0,h.A)(this,"isFieldsTouched",function(){n.warningUnhooked();for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var o,i=t[0],a=t[1],l=!1;0===t.length?o=null:1===t.length?Array.isArray(i)?(o=i.map(se),l=!1):(o=null,l=i):(o=i.map(se),l=a);var s=n.getFieldEntities(!0),c=function(e){return e.isFieldTouched()};if(!o)return l?s.every(function(e){return c(e)||e.isList()}):s.some(c);var d=new xe;o.forEach(function(e){d.set(e,[])}),s.forEach(function(e){var t=e.getNamePath();o.forEach(function(n){n.every(function(e,n){return t[n]===e})&&d.update(n,function(t){return[].concat((0,u.A)(t),[e])})})});var f=function(e){return e.some(c)},m=d.map(function(e){return e.value});return l?m.every(f):m.some(f)}),(0,h.A)(this,"isFieldTouched",function(e){return n.warningUnhooked(),n.isFieldsTouched([e])}),(0,h.A)(this,"isFieldsValidating",function(e){n.warningUnhooked();var t=n.getFieldEntities();if(!e)return t.some(function(e){return e.isFieldValidating()});var r=e.map(se);return t.some(function(e){var t=e.getNamePath();return ce(r,t)&&e.isFieldValidating()})}),(0,h.A)(this,"isFieldValidating",function(e){return n.warningUnhooked(),n.isFieldsValidating([e])}),(0,h.A)(this,"resetWithFieldInitialValue",function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new xe,r=n.getFieldEntities(!0);r.forEach(function(e){var n=e.props.initialValue,r=e.getNamePath();if(void 0!==n){var o=t.get(r)||new Set;o.add({entity:e,value:n}),t.set(r,o)}});var o;e.entities?o=e.entities:e.namePathList?(o=[],e.namePathList.forEach(function(e){var n,r=t.get(e);r&&(n=o).push.apply(n,(0,u.A)((0,u.A)(r).map(function(e){return e.entity})))})):o=r,o.forEach(function(r){if(void 0!==r.props.initialValue){var o=r.getNamePath();if(void 0!==n.getInitialValue(o))(0,y.Ay)(!1,"Form already set 'initialValues' with path '".concat(o.join("."),"'. Field can not overwrite it."));else{var i=t.get(o);if(i&&i.size>1)(0,y.Ay)(!1,"Multiple Field with path '".concat(o.join("."),"' set 'initialValue'. Can not decide which one to pick."));else if(i){var a=n.getFieldValue(o);r.isListField()||e.skipExist&&void 0!==a||n.updateStore((0,J.A)(n.store,o,(0,u.A)(i)[0].value))}}}})}),(0,h.A)(this,"resetFields",function(e){n.warningUnhooked();var t=n.store;if(!e)return n.updateStore((0,J.h)(n.initialValues)),n.resetWithFieldInitialValue(),n.notifyObservers(t,null,{type:"reset"}),void n.notifyWatch();var r=e.map(se);r.forEach(function(e){var t=n.getInitialValue(e);n.updateStore((0,J.A)(n.store,e,t))}),n.resetWithFieldInitialValue({namePathList:r}),n.notifyObservers(t,r,{type:"reset"}),n.notifyWatch(r)}),(0,h.A)(this,"setFields",function(e){n.warningUnhooked();var t=n.store,r=[];e.forEach(function(e){var o=e.name,a=(0,i.A)(e,ke),l=se(o);r.push(l),"value"in a&&n.updateStore((0,J.A)(n.store,l,a.value)),n.notifyObservers(t,[l],{type:"setField",data:e})}),n.notifyWatch(r)}),(0,h.A)(this,"getFields",function(){return n.getFieldEntities(!0).map(function(e){var t=e.getNamePath(),r=e.getMeta(),o=(0,s.A)((0,s.A)({},r),{},{name:t,value:n.getFieldValue(t)});return Object.defineProperty(o,"originRCField",{value:!0}),o})}),(0,h.A)(this,"initEntityValue",function(e){var t=e.props.initialValue;if(void 0!==t){var r=e.getNamePath();void 0===(0,le.A)(n.store,r)&&n.updateStore((0,J.A)(n.store,r,t))}}),(0,h.A)(this,"isMergedPreserve",function(e){var t=void 0!==e?e:n.preserve;return null==t||t}),(0,h.A)(this,"registerField",function(e){n.fieldEntities.push(e);var t=e.getNamePath();if(n.notifyWatch([t]),void 0!==e.props.initialValue){var r=n.store;n.resetWithFieldInitialValue({entities:[e],skipExist:!0}),n.notifyObservers(r,[e.getNamePath()],{type:"valueUpdate",source:"internal"})}return function(r,o){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];if(n.fieldEntities=n.fieldEntities.filter(function(t){return t!==e}),!n.isMergedPreserve(o)&&(!r||i.length>1)){var a=r?void 0:n.getInitialValue(t);if(t.length&&n.getFieldValue(t)!==a&&n.fieldEntities.every(function(e){return!de(e.getNamePath(),t)})){var l=n.store;n.updateStore((0,J.A)(l,t,a,!0)),n.notifyObservers(l,[t],{type:"remove"}),n.triggerDependenciesUpdate(l,t)}}n.notifyWatch([t])}}),(0,h.A)(this,"dispatch",function(e){switch(e.type){case"updateValue":var t=e.namePath,r=e.value;n.updateValue(t,r);break;case"validateField":var o=e.namePath,i=e.triggerName;n.validateFields([o],{triggerName:i})}}),(0,h.A)(this,"notifyObservers",function(e,t,r){if(n.subscribable){var o=(0,s.A)((0,s.A)({},r),{},{store:n.getFieldsValue(!0)});n.getFieldEntities().forEach(function(n){(0,n.onStoreChange)(e,t,o)})}else n.forceRootUpdate()}),(0,h.A)(this,"triggerDependenciesUpdate",function(e,t){var r=n.getDependencyChildrenFields(t);return r.length&&n.validateFields(r),n.notifyObservers(e,r,{type:"dependenciesUpdate",relatedFields:[t].concat((0,u.A)(r))}),r}),(0,h.A)(this,"updateValue",function(e,t){var r=se(e),o=n.store;n.updateStore((0,J.A)(n.store,r,t)),n.notifyObservers(o,[r],{type:"valueUpdate",source:"internal"}),n.notifyWatch([r]);var i=n.triggerDependenciesUpdate(o,r),a=n.callbacks.onValuesChange;a&&a(ue(n.store,[r]),n.getFieldsValue());n.triggerOnFieldsChange([r].concat((0,u.A)(i)))}),(0,h.A)(this,"setFieldsValue",function(e){n.warningUnhooked();var t=n.store;if(e){var r=(0,J.h)(n.store,e);n.updateStore(r)}n.notifyObservers(t,null,{type:"valueUpdate",source:"external"}),n.notifyWatch()}),(0,h.A)(this,"setFieldValue",function(e,t){n.setFields([{name:e,value:t,errors:[],warnings:[]}])}),(0,h.A)(this,"getDependencyChildrenFields",function(e){var t=new Set,r=[],o=new xe;n.getFieldEntities().forEach(function(e){(e.props.dependencies||[]).forEach(function(t){var n=se(t);o.update(n,function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new Set;return t.add(e),t})})});return function e(n){(o.get(n)||new Set).forEach(function(n){if(!t.has(n)){t.add(n);var o=n.getNamePath();n.isFieldDirty()&&o.length&&(r.push(o),e(o))}})}(e),r}),(0,h.A)(this,"triggerOnFieldsChange",function(e,t){var r=n.callbacks.onFieldsChange;if(r){var o=n.getFields();if(t){var i=new xe;t.forEach(function(e){var t=e.name,n=e.errors;i.set(t,n)}),o.forEach(function(e){e.errors=i.get(e.name)||e.errors})}var a=o.filter(function(t){var n=t.name;return ce(e,n)});a.length&&r(a,o)}}),(0,h.A)(this,"validateFields",function(e,t){var r,o;n.warningUnhooked(),Array.isArray(e)||"string"==typeof e||"string"==typeof t?(r=e,o=t):o=e;var i=!!r,a=i?r.map(se):[],l=[],c=String(Date.now()),d=new Set,f=o||{},m=f.recursive,p=f.dirty;n.getFieldEntities(!0).forEach(function(e){if(i||a.push(e.getNamePath()),e.props.rules&&e.props.rules.length&&(!p||e.isFieldDirty())){var t=e.getNamePath();if(d.add(t.join(c)),!i||ce(a,t,m)){var r=e.validateRules((0,s.A)({validateMessages:(0,s.A)((0,s.A)({},K),n.validateMessages)},o));l.push(r.then(function(){return{name:t,errors:[],warnings:[]}}).catch(function(e){var n,r=[],o=[];return null===(n=e.forEach)||void 0===n||n.call(e,function(e){var t=e.rule.warningOnly,n=e.errors;t?o.push.apply(o,(0,u.A)(n)):r.push.apply(r,(0,u.A)(n))}),r.length?Promise.reject({name:t,errors:r,warnings:o}):{name:t,errors:r,warnings:o}}))}}});var h=function(e){var t=!1,n=e.length,r=[];return e.length?new Promise(function(o,i){e.forEach(function(e,a){e.catch(function(e){return t=!0,e}).then(function(e){n-=1,r[a]=e,n>0||(t&&i(r),o(r))})})}):Promise.resolve([])}(l);n.lastValidatePromise=h,h.catch(function(e){return e}).then(function(e){var t=e.map(function(e){return e.name});n.notifyObservers(n.store,t,{type:"validateFinish"}),n.triggerOnFieldsChange(t,e)});var v=h.then(function(){return n.lastValidatePromise===h?Promise.resolve(n.getFieldsValue(a)):Promise.reject([])}).catch(function(e){var t=e.filter(function(e){return e&&e.errors.length});return Promise.reject({values:n.getFieldsValue(a),errorFields:t,outOfDate:n.lastValidatePromise!==h})});v.catch(function(e){return e});var g=a.filter(function(e){return d.has(e.join(c))});return n.triggerOnFieldsChange(g),v}),(0,h.A)(this,"submit",function(){n.warningUnhooked(),n.validateFields().then(function(e){var t=n.callbacks.onFinish;if(t)try{t(e)}catch(r){console.error(r)}}).catch(function(e){var t=n.callbacks.onFinishFailed;t&&t(e)})}),this.forceRootUpdate=t});var Se=function(e){var t=r.useRef(),n=r.useState({}),o=(0,we.A)(n,2)[1];if(!t.current)if(e)t.current=e;else{var i=new Fe(function(){o({})});t.current=i.getForm()}return[t.current]},Pe=r.createContext({triggerFormChange:function(){},triggerFormFinish:function(){},registerForm:function(){},unregisterForm:function(){}}),Oe=function(e){var t=e.validateMessages,n=e.onFormChange,o=e.onFormFinish,i=e.children,a=r.useContext(Pe),l=r.useRef({});return r.createElement(Pe.Provider,{value:(0,s.A)((0,s.A)({},a),{},{validateMessages:(0,s.A)((0,s.A)({},a.validateMessages),t),triggerFormChange:function(e,t){n&&n(e,{changedFields:t,forms:l.current}),a.triggerFormChange(e,t)},triggerFormFinish:function(e,t){o&&o(e,{values:t,forms:l.current}),a.triggerFormFinish(e,t)},registerForm:function(e,t){e&&(l.current=(0,s.A)((0,s.A)({},l.current),{},(0,h.A)({},e,t))),a.registerForm(e,t)},unregisterForm:function(e){var t=(0,s.A)({},l.current);delete t[e],l.current=t,a.unregisterForm(e)}})},i)},Me=Pe,Te=["name","initialValues","fields","form","preserve","children","component","validateMessages","validateTrigger","onValuesChange","onFieldsChange","onFinish","onFinishFailed","clearOnDestroy"],Ne=function(e,t){var n=e.name,a=e.initialValues,l=e.fields,c=e.form,d=e.preserve,f=e.children,m=e.component,p=void 0===m?"form":m,h=e.validateMessages,v=e.validateTrigger,g=void 0===v?"onChange":v,y=e.onValuesChange,w=e.onFieldsChange,A=e.onFinish,k=e.onFinishFailed,F=e.clearOnDestroy,S=(0,i.A)(e,Te),P=r.useRef(null),O=r.useContext(Me),M=Se(c),T=(0,we.A)(M,1)[0],N=T.getInternalHooks(b),R=N.useSubscribe,L=N.setInitialValues,I=N.setCallbacks,$=N.setValidateMessages,V=N.setPreserve,D=N.destroyForm;r.useImperativeHandle(t,function(){return(0,s.A)((0,s.A)({},T),{},{nativeElement:P.current})}),r.useEffect(function(){return O.registerForm(n,T),function(){O.unregisterForm(n)}},[O,T,n]),$((0,s.A)((0,s.A)({},O.validateMessages),h)),I({onValuesChange:y,onFieldsChange:function(e){if(O.triggerFormChange(n,e),w){for(var t=arguments.length,r=new Array(t>1?t-1:0),o=1;o<t;o++)r[o-1]=arguments[o];w.apply(void 0,[e].concat(r))}},onFinish:function(e){O.triggerFormFinish(n,e),A&&A(e)},onFinishFailed:k}),V(d);var j,_=r.useRef(null);L(a,!_.current),_.current||(_.current=!0),r.useEffect(function(){return function(){return D(F)}},[]);var H="function"==typeof f;H?j=f(T.getFieldsValue(!0),T):j=f;R(!H);var z=r.useRef();r.useEffect(function(){(function(e,t){if(e===t)return!0;if(!e&&t||e&&!t)return!1;if(!e||!t||"object"!==(0,x.A)(e)||"object"!==(0,x.A)(t))return!1;var n=Object.keys(e),r=Object.keys(t),o=new Set([].concat(n,r));return(0,u.A)(o).every(function(n){var r=e[n],o=t[n];return"function"==typeof r&&"function"==typeof o||r===o})})(z.current||[],l||[])||T.setFields(l||[]),z.current=l},[l,T]);var B=r.useMemo(function(){return(0,s.A)((0,s.A)({},T),{},{validateTrigger:g})},[T,g]),q=r.createElement(E.Provider,{value:null},r.createElement(C.Provider,{value:B},j));return!1===p?q:r.createElement(p,(0,o.A)({},S,{ref:P,onSubmit:function(e){e.preventDefault(),e.stopPropagation(),T.submit()},onReset:function(e){var t;e.preventDefault(),T.resetFields(),null===(t=S.onReset)||void 0===t||t.call(S,e)}}),q)};function Re(e){try{return JSON.stringify(e)}catch(t){return Math.random()}}var Le=function(){};var Ie=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var o=t[0],i=t[1],a=void 0===i?{}:i,l=function(e){return e&&!!e._init}(a)?{form:a}:a,s=l.form,u=(0,r.useState)(),c=(0,we.A)(u,2),d=c[0],f=c[1],m=(0,r.useMemo)(function(){return Re(d)},[d]),p=(0,r.useRef)(m);p.current=m;var h=(0,r.useContext)(C),v=s||h,g=v&&v._init,y=se(o),w=(0,r.useRef)(y);return w.current=y,Le(y),(0,r.useEffect)(function(){if(g){var e=v.getFieldsValue,t=(0,v.getInternalHooks)(b).registerWatch,n=function(e,t){var n=l.preserve?t:e;return"function"==typeof o?o(n):(0,le.A)(n,w.current)},r=t(function(e,t){var r=n(e,t),o=Re(r);p.current!==o&&(p.current=o,f(r))}),i=n(e(),e(!0));return d!==i&&f(i),r}},[g]),d},$e=r.forwardRef(Ne);$e.FormProvider=Oe,$e.Field=ye,$e.List=be,$e.useForm=Se,$e.useWatch=Ie;var Ve=$e},3723:function(e,t,n){n.d(t,{b:function(){return s}});var r=n(2279);const o=()=>({height:0,opacity:0}),i=e=>{const{scrollHeight:t}=e;return{height:t,opacity:1}},a=e=>({height:e?e.offsetHeight:0}),l=(e,t)=>!0===(null==t?void 0:t.deadline)||"height"===t.propertyName,s=(e,t,n)=>void 0!==n?n:`${e}-${t}`;t.A=(e=r.yH)=>({motionName:`${e}-motion-collapse`,onAppearStart:o,onEnterStart:o,onAppearActive:i,onEnterActive:i,onLeaveStart:a,onLeaveActive:o,onAppearEnd:l,onEnterEnd:l,onLeaveEnd:l,motionDeadline:500})},3950:function(e,t,n){n.d(t,{s:function(){return r}});const r=["blue","purple","cyan","green","magenta","pink","red","orange","yellow","volcano","geekblue","lime","gold"]},4121:function(e,t,n){n.d(t,{ZZ:function(){return s},nP:function(){return l}});var r=n(436),o=n(3950);const i=o.s.map(e=>`${e}-inverse`),a=["success","processing","error","default","warning"];function l(e,t=!0){return t?[].concat((0,r.A)(i),(0,r.A)(o.s)).includes(e):o.s.includes(e)}function s(e){return a.includes(e)}},4241:function(e,t,n){n.d(t,{$W:function(){return c},Op:function(){return s},Pp:function(){return f},XB:function(){return d},cK:function(){return a},hb:function(){return u},jC:function(){return l}});var r=n(6540),o=n(3592),i=n(9853);const a=r.createContext({labelAlign:"right",vertical:!1,itemRef:()=>{}}),l=r.createContext(null),s=e=>{const t=(0,i.A)(e,["prefixCls"]);return r.createElement(o.Op,Object.assign({},t))},u=r.createContext({prefixCls:""}),c=r.createContext({});const d=({children:e,status:t,override:n})=>{const o=r.useContext(c),i=r.useMemo(()=>{const e=Object.assign({},o);return n&&delete e.isFormItemInput,t&&(delete e.status,delete e.hasFeedback,delete e.feedbackIcon),e},[t,n,o]);return r.createElement(c.Provider,{value:i},e)},f=r.createContext(void 0)},4716:function(e,t,n){n.d(t,{A:function(){return S}});var r=n(6540),o=n(6942),i=n.n(o);function a(e,t,n){var r=(n||{}).atBegin;return function(e,t,n){var r,o=n||{},i=o.noTrailing,a=void 0!==i&&i,l=o.noLeading,s=void 0!==l&&l,u=o.debounceMode,c=void 0===u?void 0:u,d=!1,f=0;function m(){r&&clearTimeout(r)}function p(){for(var n=arguments.length,o=new Array(n),i=0;i<n;i++)o[i]=arguments[i];var l=this,u=Date.now()-f;function p(){f=Date.now(),t.apply(l,o)}function h(){r=void 0}d||(s||!c||r||p(),m(),void 0===c&&u>e?s?(f=Date.now(),a||(r=setTimeout(c?h:p,e))):p():!0!==a&&(r=setTimeout(c?h:p,void 0===c?e-u:e)))}return p.cancel=function(e){var t=(e||{}).upcomingOnly,n=void 0!==t&&t;m(),d=!n},p}(e,t,{debounceMode:!1!==(void 0!==r&&r)})}var l=n(2279),s=n(682),u=n(981);const c=80*Math.PI,d=e=>{const{dotClassName:t,style:n,hasCircleCls:o}=e;return r.createElement("circle",{className:i()(`${t}-circle`,{[`${t}-circle-bg`]:o}),r:40,cx:50,cy:50,strokeWidth:20,style:n})};var f=({percent:e,prefixCls:t})=>{const n=`${t}-dot`,o=`${n}-holder`,a=`${o}-hidden`,[l,s]=r.useState(!1);(0,u.A)(()=>{0!==e&&s(!0)},[0!==e]);const f=Math.max(Math.min(e,100),0);if(!l)return null;const m={strokeDashoffset:""+c/4,strokeDasharray:`${c*f/100} ${c*(100-f)/100}`};return r.createElement("span",{className:i()(o,`${n}-progress`,f<=0&&a)},r.createElement("svg",{viewBox:"0 0 100 100",role:"progressbar","aria-valuemin":0,"aria-valuemax":100,"aria-valuenow":f},r.createElement(d,{dotClassName:n,hasCircleCls:!0}),r.createElement(d,{dotClassName:n,style:m})))};function m(e){const{prefixCls:t,percent:n=0}=e,o=`${t}-dot`,a=`${o}-holder`,l=`${a}-hidden`;return r.createElement(r.Fragment,null,r.createElement("span",{className:i()(a,n>0&&l)},r.createElement("span",{className:i()(o,`${t}-dot-spin`)},[1,2,3,4].map(e=>r.createElement("i",{className:`${t}-dot-item`,key:e})))),r.createElement(f,{prefixCls:t,percent:n}))}function p(e){var t;const{prefixCls:n,indicator:o,percent:a}=e,l=`${n}-dot`;return o&&r.isValidElement(o)?(0,s.Ob)(o,{className:i()(null===(t=o.props)||void 0===t?void 0:t.className,l),percent:a}):r.createElement(m,{prefixCls:n,percent:a})}var h=n(2187),v=n(5905),g=n(7358),y=n(4277);const b=new h.Mo("antSpinMove",{to:{opacity:1}}),w=new h.Mo("antRotate",{to:{transform:"rotate(405deg)"}}),C=e=>{const{componentCls:t,calc:n}=e;return{[t]:Object.assign(Object.assign({},(0,v.dF)(e)),{position:"absolute",display:"none",color:e.colorPrimary,fontSize:0,textAlign:"center",verticalAlign:"middle",opacity:0,transition:`transform ${e.motionDurationSlow} ${e.motionEaseInOutCirc}`,"&-spinning":{position:"relative",display:"inline-block",opacity:1},[`${t}-text`]:{fontSize:e.fontSize,paddingTop:n(n(e.dotSize).sub(e.fontSize)).div(2).add(2).equal()},"&-fullscreen":{position:"fixed",width:"100vw",height:"100vh",backgroundColor:e.colorBgMask,zIndex:e.zIndexPopupBase,inset:0,display:"flex",alignItems:"center",flexDirection:"column",justifyContent:"center",opacity:0,visibility:"hidden",transition:`all ${e.motionDurationMid}`,"&-show":{opacity:1,visibility:"visible"},[t]:{[`${t}-dot-holder`]:{color:e.colorWhite},[`${t}-text`]:{color:e.colorTextLightSolid}}},"&-nested-loading":{position:"relative",[`> div > ${t}`]:{position:"absolute",top:0,insetInlineStart:0,zIndex:4,display:"block",width:"100%",height:"100%",maxHeight:e.contentHeight,[`${t}-dot`]:{position:"absolute",top:"50%",insetInlineStart:"50%",margin:n(e.dotSize).mul(-1).div(2).equal()},[`${t}-text`]:{position:"absolute",top:"50%",width:"100%",textShadow:`0 1px 2px ${e.colorBgContainer}`},[`&${t}-show-text ${t}-dot`]:{marginTop:n(e.dotSize).div(2).mul(-1).sub(10).equal()},"&-sm":{[`${t}-dot`]:{margin:n(e.dotSizeSM).mul(-1).div(2).equal()},[`${t}-text`]:{paddingTop:n(n(e.dotSizeSM).sub(e.fontSize)).div(2).add(2).equal()},[`&${t}-show-text ${t}-dot`]:{marginTop:n(e.dotSizeSM).div(2).mul(-1).sub(10).equal()}},"&-lg":{[`${t}-dot`]:{margin:n(e.dotSizeLG).mul(-1).div(2).equal()},[`${t}-text`]:{paddingTop:n(n(e.dotSizeLG).sub(e.fontSize)).div(2).add(2).equal()},[`&${t}-show-text ${t}-dot`]:{marginTop:n(e.dotSizeLG).div(2).mul(-1).sub(10).equal()}}},[`${t}-container`]:{position:"relative",transition:`opacity ${e.motionDurationSlow}`,"&::after":{position:"absolute",top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,zIndex:10,width:"100%",height:"100%",background:e.colorBgContainer,opacity:0,transition:`all ${e.motionDurationSlow}`,content:'""',pointerEvents:"none"}},[`${t}-blur`]:{clear:"both",opacity:.5,userSelect:"none",pointerEvents:"none","&::after":{opacity:.4,pointerEvents:"auto"}}},"&-tip":{color:e.spinDotDefault},[`${t}-dot-holder`]:{width:"1em",height:"1em",fontSize:e.dotSize,display:"inline-block",transition:`transform ${e.motionDurationSlow} ease, opacity ${e.motionDurationSlow} ease`,transformOrigin:"50% 50%",lineHeight:1,color:e.colorPrimary,"&-hidden":{transform:"scale(0.3)",opacity:0}},[`${t}-dot-progress`]:{position:"absolute",inset:0},[`${t}-dot`]:{position:"relative",display:"inline-block",fontSize:e.dotSize,width:"1em",height:"1em","&-item":{position:"absolute",display:"block",width:n(e.dotSize).sub(n(e.marginXXS).div(2)).div(2).equal(),height:n(e.dotSize).sub(n(e.marginXXS).div(2)).div(2).equal(),background:"currentColor",borderRadius:"100%",transform:"scale(0.75)",transformOrigin:"50% 50%",opacity:.3,animationName:b,animationDuration:"1s",animationIterationCount:"infinite",animationTimingFunction:"linear",animationDirection:"alternate","&:nth-child(1)":{top:0,insetInlineStart:0,animationDelay:"0s"},"&:nth-child(2)":{top:0,insetInlineEnd:0,animationDelay:"0.4s"},"&:nth-child(3)":{insetInlineEnd:0,bottom:0,animationDelay:"0.8s"},"&:nth-child(4)":{bottom:0,insetInlineStart:0,animationDelay:"1.2s"}},"&-spin":{transform:"rotate(45deg)",animationName:w,animationDuration:"1.2s",animationIterationCount:"infinite",animationTimingFunction:"linear"},"&-circle":{strokeLinecap:"round",transition:["stroke-dashoffset","stroke-dasharray","stroke","stroke-width","opacity"].map(t=>`${t} ${e.motionDurationSlow} ease`).join(","),fillOpacity:0,stroke:"currentcolor"},"&-circle-bg":{stroke:e.colorFillSecondary}},[`&-sm ${t}-dot`]:{"&, &-holder":{fontSize:e.dotSizeSM}},[`&-sm ${t}-dot-holder`]:{i:{width:n(n(e.dotSizeSM).sub(n(e.marginXXS).div(2))).div(2).equal(),height:n(n(e.dotSizeSM).sub(n(e.marginXXS).div(2))).div(2).equal()}},[`&-lg ${t}-dot`]:{"&, &-holder":{fontSize:e.dotSizeLG}},[`&-lg ${t}-dot-holder`]:{i:{width:n(n(e.dotSizeLG).sub(e.marginXXS)).div(2).equal(),height:n(n(e.dotSizeLG).sub(e.marginXXS)).div(2).equal()}},[`&${t}-show-text ${t}-text`]:{display:"block"}})}};var E=(0,g.OF)("Spin",e=>{const t=(0,y.oX)(e,{spinDotDefault:e.colorTextDescription});return[C(t)]},e=>{const{controlHeightLG:t,controlHeight:n}=e;return{contentHeight:400,dotSize:t/2,dotSizeSM:.35*t,dotSizeLG:n}});const A=[[30,.05],[70,.03],[96,.01]];var x=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};let k;const F=e=>{var t;const{prefixCls:n,spinning:o=!0,delay:s=0,className:u,rootClassName:c,size:d="default",tip:f,wrapperClassName:m,style:h,children:v,fullscreen:g=!1,indicator:y,percent:b}=e,w=x(e,["prefixCls","spinning","delay","className","rootClassName","size","tip","wrapperClassName","style","children","fullscreen","indicator","percent"]),{getPrefixCls:C,direction:F,className:S,style:P,indicator:O}=(0,l.TP)("spin"),M=C("spin",n),[T,N,R]=E(M),[L,I]=r.useState(()=>o&&!function(e,t){return!!e&&!!t&&!Number.isNaN(Number(t))}(o,s)),$=function(e,t){const[n,o]=r.useState(0),i=r.useRef(null),a="auto"===t;return r.useEffect(()=>(a&&e&&(o(0),i.current=setInterval(()=>{o(e=>{const t=100-e;for(let n=0;n<A.length;n+=1){const[r,o]=A[n];if(e<=r)return e+t*o}return e})},200)),()=>{clearInterval(i.current)}),[a,e]),a?n:t}(L,b);r.useEffect(()=>{if(o){const e=a(s,()=>{I(!0)});return e(),()=>{var t;null===(t=null==e?void 0:e.cancel)||void 0===t||t.call(e)}}I(!1)},[s,o]);const V=r.useMemo(()=>void 0!==v&&!g,[v,g]);const D=i()(M,S,{[`${M}-sm`]:"small"===d,[`${M}-lg`]:"large"===d,[`${M}-spinning`]:L,[`${M}-show-text`]:!!f,[`${M}-rtl`]:"rtl"===F},u,!g&&c,N,R),j=i()(`${M}-container`,{[`${M}-blur`]:L}),_=null!==(t=null!=y?y:O)&&void 0!==t?t:k,H=Object.assign(Object.assign({},P),h),z=r.createElement("div",Object.assign({},w,{style:H,className:D,"aria-live":"polite","aria-busy":L}),r.createElement(p,{prefixCls:M,indicator:_,percent:$}),f&&(V||g)?r.createElement("div",{className:`${M}-text`},f):null);return T(V?r.createElement("div",Object.assign({},w,{className:i()(`${M}-nested-loading`,m,N,R)}),L&&r.createElement("div",{key:"loading"},z),r.createElement("div",{className:j,key:"container"},v)):g?r.createElement("div",{className:i()(`${M}-fullscreen`,{[`${M}-fullscreen-show`]:L},c,N,R)},z):z)};F.setDefaultIndicator=e=>{k=e};var S=F},4743:function(e,t,n){n.d(t,{BN:function(){return m},Ej:function(){return h},UU:function(){return p},cY:function(){return f},we:function(){return d}});var r=n(6885),o=n(6540),i=n(961),a="undefined"!=typeof document?o.useLayoutEffect:function(){};function l(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;0!==r--;)if(!l(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;0!==r--;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!==r--;){const n=o[r];if(("_owner"!==n||!e.$$typeof)&&!l(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function s(e){if("undefined"==typeof window)return 1;return(e.ownerDocument.defaultView||window).devicePixelRatio||1}function u(e,t){const n=s(e);return Math.round(t*n)/n}function c(e){const t=o.useRef(e);return a(()=>{t.current=e}),t}function d(e){void 0===e&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:d=[],platform:f,elements:{reference:m,floating:p}={},transform:h=!0,whileElementsMounted:v,open:g}=e,[y,b]=o.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[w,C]=o.useState(d);l(w,d)||C(d);const[E,A]=o.useState(null),[x,k]=o.useState(null),F=o.useCallback(e=>{e!==M.current&&(M.current=e,A(e))},[]),S=o.useCallback(e=>{e!==T.current&&(T.current=e,k(e))},[]),P=m||E,O=p||x,M=o.useRef(null),T=o.useRef(null),N=o.useRef(y),R=null!=v,L=c(v),I=c(f),$=c(g),V=o.useCallback(()=>{if(!M.current||!T.current)return;const e={placement:t,strategy:n,middleware:w};I.current&&(e.platform=I.current),(0,r.rD)(M.current,T.current,e).then(e=>{const t={...e,isPositioned:!1!==$.current};D.current&&!l(N.current,t)&&(N.current=t,i.flushSync(()=>{b(t)}))})},[w,t,n,I,$]);a(()=>{!1===g&&N.current.isPositioned&&(N.current.isPositioned=!1,b(e=>({...e,isPositioned:!1})))},[g]);const D=o.useRef(!1);a(()=>(D.current=!0,()=>{D.current=!1}),[]),a(()=>{if(P&&(M.current=P),O&&(T.current=O),P&&O){if(L.current)return L.current(P,O,V);V()}},[P,O,V,L,R]);const j=o.useMemo(()=>({reference:M,floating:T,setReference:F,setFloating:S}),[F,S]),_=o.useMemo(()=>({reference:P,floating:O}),[P,O]),H=o.useMemo(()=>{const e={position:n,left:0,top:0};if(!_.floating)return e;const t=u(_.floating,y.x),r=u(_.floating,y.y);return h?{...e,transform:"translate("+t+"px, "+r+"px)",...s(_.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,h,_.floating,y.x,y.y]);return o.useMemo(()=>({...y,update:V,refs:j,elements:_,floatingStyles:H}),[y,V,j,_,H])}const f=(e,t)=>({...(0,r.cY)(e),options:[e,t]}),m=(e,t)=>({...(0,r.BN)(e),options:[e,t]}),p=(e,t)=>({...(0,r.UU)(e),options:[e,t]}),h=(e,t)=>({...(0,r.Ej)(e),options:[e,t]})},4980:function(e,t,n){n.d(t,{b:function(){return i}});const r=e=>({animationDuration:e,animationFillMode:"both"}),o=e=>({animationDuration:e,animationFillMode:"both"}),i=(e,t,n,i,a=!1)=>{const l=a?"&":"";return{[`\n      ${l}${e}-enter,\n      ${l}${e}-appear\n    `]:Object.assign(Object.assign({},r(i)),{animationPlayState:"paused"}),[`${l}${e}-leave`]:Object.assign(Object.assign({},o(i)),{animationPlayState:"paused"}),[`\n      ${l}${e}-enter${e}-enter-active,\n      ${l}${e}-appear${e}-appear-active\n    `]:{animationName:t,animationPlayState:"running"},[`${l}${e}-leave${e}-leave-active`]:{animationName:n,animationPlayState:"running",pointerEvents:"none"}}}},5061:function(e,t,n){n.d(t,{A:function(){return r}});const r=(0,n(1788).A)("MessagesSquare",[["path",{d:"M14 9a2 2 0 0 1-2 2H6l-4 4V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2z",key:"p1xzt8"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v11l-4-4h-6a2 2 0 0 1-2-2v-1",key:"1cx29u"}]])},5107:function(e,t,n){n.d(t,{A:function(){return r}});const r=(0,n(1788).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},5160:function(e,t,n){var r=n(6540);var o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=r.useSyncExternalStore,a=r.useRef,l=r.useEffect,s=r.useMemo,u=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,c){var d=a(null);if(null===d.current){var f={hasValue:!1,value:null};d.current=f}else f=d.current;d=s(function(){function e(e){if(!l){if(l=!0,i=e,e=r(e),void 0!==c&&f.hasValue){var t=f.value;if(c(t,e))return a=t}return a=e}if(t=a,o(i,e))return t;var n=r(e);return void 0!==c&&c(t,n)?(i=e,t):(i=e,a=n)}var i,a,l=!1,s=void 0===n?null:n;return[function(){return e(t())},null===s?void 0:function(){return e(s())}]},[t,n,r,c]);var m=i(e,d[0],d[1]);return l(function(){f.hasValue=!0,f.value=m},[m]),u(m),m}},5201:function(e,t,n){n.d(t,{Ay:function(){return s},Ke:function(){return a},Zs:function(){return i}});var r=n(2187),o=n(791);const i=8;function a(e){const{contentRadius:t,limitVerticalRadius:n}=e,r=t>12?t+2:12;return{arrowOffsetHorizontal:r,arrowOffsetVertical:n?i:r}}function l(e,t){return e?t:{}}function s(e,t,n){const{componentCls:i,boxShadowPopoverArrow:a,arrowOffsetVertical:s,arrowOffsetHorizontal:u}=e,{arrowDistance:c=0,arrowPlacement:d={left:!0,right:!0,top:!0,bottom:!0}}=n||{};return{[i]:Object.assign(Object.assign(Object.assign(Object.assign({[`${i}-arrow`]:[Object.assign(Object.assign({position:"absolute",zIndex:1,display:"block"},(0,o.j)(e,t,a)),{"&:before":{background:t}})]},l(!!d.top,{[[`&-placement-top > ${i}-arrow`,`&-placement-topLeft > ${i}-arrow`,`&-placement-topRight > ${i}-arrow`].join(",")]:{bottom:c,transform:"translateY(100%) rotate(180deg)"},[`&-placement-top > ${i}-arrow`]:{left:{_skip_check_:!0,value:"50%"},transform:"translateX(-50%) translateY(100%) rotate(180deg)"},"&-placement-topLeft":{"--arrow-offset-horizontal":u,[`> ${i}-arrow`]:{left:{_skip_check_:!0,value:u}}},"&-placement-topRight":{"--arrow-offset-horizontal":`calc(100% - ${(0,r.zA)(u)})`,[`> ${i}-arrow`]:{right:{_skip_check_:!0,value:u}}}})),l(!!d.bottom,{[[`&-placement-bottom > ${i}-arrow`,`&-placement-bottomLeft > ${i}-arrow`,`&-placement-bottomRight > ${i}-arrow`].join(",")]:{top:c,transform:"translateY(-100%)"},[`&-placement-bottom > ${i}-arrow`]:{left:{_skip_check_:!0,value:"50%"},transform:"translateX(-50%) translateY(-100%)"},"&-placement-bottomLeft":{"--arrow-offset-horizontal":u,[`> ${i}-arrow`]:{left:{_skip_check_:!0,value:u}}},"&-placement-bottomRight":{"--arrow-offset-horizontal":`calc(100% - ${(0,r.zA)(u)})`,[`> ${i}-arrow`]:{right:{_skip_check_:!0,value:u}}}})),l(!!d.left,{[[`&-placement-left > ${i}-arrow`,`&-placement-leftTop > ${i}-arrow`,`&-placement-leftBottom > ${i}-arrow`].join(",")]:{right:{_skip_check_:!0,value:c},transform:"translateX(100%) rotate(90deg)"},[`&-placement-left > ${i}-arrow`]:{top:{_skip_check_:!0,value:"50%"},transform:"translateY(-50%) translateX(100%) rotate(90deg)"},[`&-placement-leftTop > ${i}-arrow`]:{top:s},[`&-placement-leftBottom > ${i}-arrow`]:{bottom:s}})),l(!!d.right,{[[`&-placement-right > ${i}-arrow`,`&-placement-rightTop > ${i}-arrow`,`&-placement-rightBottom > ${i}-arrow`].join(",")]:{left:{_skip_check_:!0,value:c},transform:"translateX(-100%) rotate(-90deg)"},[`&-placement-right > ${i}-arrow`]:{top:{_skip_check_:!0,value:"50%"},transform:"translateY(-50%) translateX(-100%) rotate(-90deg)"},[`&-placement-rightTop > ${i}-arrow`]:{top:s},[`&-placement-rightBottom > ${i}-arrow`]:{bottom:s}}))}}},5447:function(e,t,n){n.d(t,{A:function(){return s}});var r=n(7358),o=n(4277);var i=e=>{const{componentCls:t}=e;return{[t]:{"&-block":{display:"flex",width:"100%"},"&-vertical":{flexDirection:"column"}}}};const a=e=>{const{componentCls:t,antCls:n}=e;return{[t]:{display:"inline-flex","&-rtl":{direction:"rtl"},"&-vertical":{flexDirection:"column"},"&-align":{flexDirection:"column","&-center":{alignItems:"center"},"&-start":{alignItems:"flex-start"},"&-end":{alignItems:"flex-end"},"&-baseline":{alignItems:"baseline"}},[`${t}-item:empty`]:{display:"none"},[`${t}-item > ${n}-badge-not-a-wrapper:only-child`]:{display:"block"}}}},l=e=>{const{componentCls:t}=e;return{[t]:{"&-gap-row-small":{rowGap:e.spaceGapSmallSize},"&-gap-row-middle":{rowGap:e.spaceGapMiddleSize},"&-gap-row-large":{rowGap:e.spaceGapLargeSize},"&-gap-col-small":{columnGap:e.spaceGapSmallSize},"&-gap-col-middle":{columnGap:e.spaceGapMiddleSize},"&-gap-col-large":{columnGap:e.spaceGapLargeSize}}}};var s=(0,r.OF)("Space",e=>{const t=(0,o.oX)(e,{spaceGapSmallSize:e.paddingXS,spaceGapMiddleSize:e.padding,spaceGapLargeSize:e.paddingLG});return[a(t),l(t),i(t)]},()=>({}),{resetStyle:!1})},5625:function(e,t,n){n.d(t,{J:function(){return h}});var r,o=n(1511),i=n(7134),a=new Uint8Array(16);function l(){if(!r&&!(r="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||"undefined"!=typeof msCrypto&&"function"==typeof msCrypto.getRandomValues&&msCrypto.getRandomValues.bind(msCrypto)))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return r(a)}var s=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;for(var u=function(e){return"string"==typeof e&&s.test(e)},c=[],d=0;d<256;++d)c.push((d+256).toString(16).substr(1));var f=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=(c[e[t+0]]+c[e[t+1]]+c[e[t+2]]+c[e[t+3]]+"-"+c[e[t+4]]+c[e[t+5]]+"-"+c[e[t+6]]+c[e[t+7]]+"-"+c[e[t+8]]+c[e[t+9]]+"-"+c[e[t+10]]+c[e[t+11]]+c[e[t+12]]+c[e[t+13]]+c[e[t+14]]+c[e[t+15]]).toLowerCase();if(!u(n))throw TypeError("Stringified UUID is invalid");return n};var m=function(e,t,n){var r=(e=e||{}).random||(e.rng||l)();if(r[6]=15&r[6]|64,r[8]=63&r[8]|128,t){n=n||0;for(var o=0;o<16;++o)t[n+o]=r[o];return t}return f(r)};const p={direction:"TB",showLabels:!0,showGrid:!0,showTokens:!0,showMessages:!0,showMiniMap:!1},h=(0,o.v)()((0,i.Zr)(e=>({messages:[],setMessages:t=>e({messages:t}),session:null,setSession:t=>e({session:t}),sessions:[],setSessions:t=>e({sessions:t}),version:null,setVersion:t=>e({version:t}),connectionId:m(),header:{title:"",breadcrumbs:[]},setHeader:t=>e(e=>({header:{...e.header,...t}})),setBreadcrumbs:t=>e(e=>({header:{...e.header,breadcrumbs:t}})),agentFlow:p,setAgentFlowSettings:t=>e(e=>({agentFlow:{...e.agentFlow,...t}})),sidebar:{isExpanded:!0,isPinned:!1},setSidebarState:t=>e(e=>({sidebar:{...e.sidebar,...t}})),collapseSidebar:()=>e(e=>({sidebar:{...e.sidebar,isExpanded:!1}})),expandSidebar:()=>e(e=>({sidebar:{...e.sidebar,isExpanded:!0}})),toggleSidebar:()=>e(e=>({sidebar:{...e.sidebar,isExpanded:!e.sidebar.isExpanded}}))}),{name:"app-sidebar-state",storage:(0,i.KU)(()=>localStorage),partialize:e=>({sidebar:e.sidebar,agentFlow:e.agentFlow})}))},6148:function(e,t,n){n.d(t,{A:function(){return r}});const r=(0,n(1788).A)("GalleryHorizontalEnd",[["path",{d:"M2 7v10",key:"a2pl2d"}],["path",{d:"M6 5v14",key:"1kq3d7"}],["rect",{width:"12",height:"18",x:"10",y:"3",rx:"2",key:"13i7bc"}]])},6327:function(e,t,n){n.d(t,{K6:function(){return m},RQ:function(){return f}});var r=n(6540),o=n(6942),i=n.n(o),a=n(2546),l=n(2279),s=n(829),u=n(5447),c=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};const d=r.createContext(null),f=(e,t)=>{const n=r.useContext(d),o=r.useMemo(()=>{if(!n)return"";const{compactDirection:r,isFirstItem:o,isLastItem:a}=n,l="vertical"===r?"-vertical-":"-";return i()(`${e}-compact${l}item`,{[`${e}-compact${l}first-item`]:o,[`${e}-compact${l}last-item`]:a,[`${e}-compact${l}item-rtl`]:"rtl"===t})},[e,t,n]);return{compactSize:null==n?void 0:n.compactSize,compactDirection:null==n?void 0:n.compactDirection,compactItemClassnames:o}},m=e=>{const{children:t}=e;return r.createElement(d.Provider,{value:null},t)},p=e=>{const{children:t}=e,n=c(e,["children"]);return r.createElement(d.Provider,{value:r.useMemo(()=>n,[n])},t)};t.Ay=e=>{const{getPrefixCls:t,direction:n}=r.useContext(l.QO),{size:o,direction:f,block:m,prefixCls:h,className:v,rootClassName:g,children:y}=e,b=c(e,["size","direction","block","prefixCls","className","rootClassName","children"]),w=(0,s.A)(e=>null!=o?o:e),C=t("space-compact",h),[E,A]=(0,u.A)(C),x=i()(C,A,{[`${C}-rtl`]:"rtl"===n,[`${C}-block`]:m,[`${C}-vertical`]:"vertical"===f},v,g),k=r.useContext(d),F=(0,a.A)(y),S=r.useMemo(()=>F.map((e,t)=>{const n=(null==e?void 0:e.key)||`${C}-item-${t}`;return r.createElement(p,{key:n,compactSize:w,compactDirection:f,isFirstItem:0===t&&(!k||(null==k?void 0:k.isFirstItem)),isLastItem:t===F.length-1&&(!k||(null==k?void 0:k.isLastItem))},e)}),[o,F,k]);return 0===F.length?null:E(r.createElement("div",Object.assign({className:x},b),S))}},6635:function(e,t,n){function r(){return"undefined"!=typeof window}function o(e){return l(e)?(e.nodeName||"").toLowerCase():"#document"}function i(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function a(e){var t;return null==(t=(l(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function l(e){return!!r()&&(e instanceof Node||e instanceof i(e).Node)}function s(e){return!!r()&&(e instanceof Element||e instanceof i(e).Element)}function u(e){return!!r()&&(e instanceof HTMLElement||e instanceof i(e).HTMLElement)}function c(e){return!(!r()||"undefined"==typeof ShadowRoot)&&(e instanceof ShadowRoot||e instanceof i(e).ShadowRoot)}n.d(t,{$4:function(){return S},CP:function(){return F},L9:function(){return k},Lv:function(){return p},Tc:function(){return E},Tf:function(){return v},ZU:function(){return f},_m:function(){return M},ep:function(){return a},eu:function(){return x},gJ:function(){return C},mq:function(){return o},sQ:function(){return w},sb:function(){return u},v9:function(){return O},vq:function(){return s},zk:function(){return i}});const d=new Set(["inline","contents"]);function f(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=k(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!d.has(o)}const m=new Set(["table","td","th"]);function p(e){return m.has(o(e))}const h=[":popover-open",":modal"];function v(e){return h.some(t=>{try{return e.matches(t)}catch(n){return!1}})}const g=["transform","translate","scale","rotate","perspective"],y=["transform","translate","scale","rotate","perspective","filter"],b=["paint","layout","strict","content"];function w(e){const t=E(),n=s(e)?k(e):e;return g.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||y.some(e=>(n.willChange||"").includes(e))||b.some(e=>(n.contain||"").includes(e))}function C(e){let t=S(e);for(;u(t)&&!x(t);){if(w(t))return t;if(v(t))return null;t=S(t)}return null}function E(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}const A=new Set(["html","body","#document"]);function x(e){return A.has(o(e))}function k(e){return i(e).getComputedStyle(e)}function F(e){return s(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function S(e){if("html"===o(e))return e;const t=e.assignedSlot||e.parentNode||c(e)&&e.host||a(e);return c(t)?t.host:t}function P(e){const t=S(e);return x(t)?e.ownerDocument?e.ownerDocument.body:e.body:u(t)&&f(t)?t:P(t)}function O(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);const o=P(e),a=o===(null==(r=e.ownerDocument)?void 0:r.body),l=i(o);if(a){const e=M(l);return t.concat(l,l.visualViewport||[],f(o)?o:[],e&&n?O(e):[])}return t.concat(o,O(o,[],n))}function M(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}},6647:function(e,t,n){var r=n(6540);t.A=e=>{let{icon:t="app",size:n=4,className:o=""}=e;const i=`h-${n} w-${n}  ${o}`;return"github"===t?r.createElement("svg",{className:` ${i} inline-block  `,"aria-hidden":"true",fill:"currentColor",viewBox:"0 0 20 20"},r.createElement("path",{fillRule:"evenodd",d:"M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z",clipRule:"evenodd"})):"python"===t?r.createElement("svg",{className:` ${i} inline-block  `,"aria-hidden":"true",fill:"currentColor",viewBox:"0 0 50 63"},r.createElement("path",{d:"M42.6967 62.1044H13.464C11.5281 62.1021 9.67207 61.3321 8.30315 59.9632C6.93422 58.5942 6.16417 56.7382 6.16193 54.8023V45.2659C6.16193 44.9442 6.28972 44.6357 6.5172 44.4082C6.74467 44.1807 7.0532 44.0529 7.3749 44.0529C7.6966 44.0529 8.00513 44.1807 8.2326 44.4082C8.46008 44.6357 8.58787 44.9442 8.58787 45.2659V54.8023C8.58948 56.095 9.10373 57.3344 10.0178 58.2485C10.9319 59.1626 12.1713 59.6768 13.464 59.6784H42.6967C43.9896 59.6768 45.229 59.1626 46.1433 58.2485C47.0576 57.3345 47.5721 56.0951 47.5741 54.8023V43.8746C47.5741 43.5529 47.7019 43.2444 47.9293 43.0169C48.1568 42.7894 48.4653 42.6616 48.787 42.6616C49.1087 42.6616 49.4173 42.7894 49.6447 43.0169C49.8722 43.2444 50 43.5529 50 43.8746V54.8023C49.9975 56.7383 49.2271 58.5944 47.858 59.9632C46.4889 61.3321 44.6328 62.1021 42.6967 62.1044Z",fill:"currentColor"}),r.createElement("path",{d:"M48.7822 41.6183C48.4605 41.6183 48.152 41.4906 47.9245 41.2631C47.697 41.0356 47.5692 40.7271 47.5692 40.4054V12.5967C47.5681 12.2529 47.4946 11.9131 47.3533 11.5995C47.212 11.286 47.0062 11.0058 46.7492 10.7773L38.0522 3.04699C37.6064 2.64832 37.0297 2.42731 36.4317 2.42595H14.677C13.0616 2.42852 11.5131 3.07163 10.3712 4.21425C9.22922 5.35686 8.58704 6.90572 8.58543 8.52114V23.3037C8.58543 23.6254 8.45764 23.9339 8.23016 24.1614C8.00268 24.3888 7.69416 24.5166 7.37246 24.5166C7.05076 24.5166 6.74223 24.3888 6.51476 24.1614C6.28728 23.9339 6.15948 23.6254 6.15948 23.3037V8.52114C6.16173 6.26251 7.05971 4.09698 8.65646 2.49955C10.2532 0.902118 12.4184 0.0032109 14.677 9.10874e-08H36.4317C37.6233 -0.000230408 38.7736 0.437008 39.6643 1.22874L48.3613 8.96024C48.8752 9.41695 49.2866 9.97738 49.5682 10.6046C49.8498 11.2318 49.9953 11.9116 49.9952 12.5992V40.4054C49.9952 40.7271 49.8674 41.0356 49.6399 41.2631C49.4124 41.4906 49.1039 41.6183 48.7822 41.6183Z",fill:"currentColor"}),r.createElement("path",{d:"M48.7203 13.1681H41.474C40.1838 13.1665 38.947 12.6533 38.0347 11.741C37.1224 10.8287 36.6091 9.59184 36.6075 8.30167V1.49325C36.6075 1.17155 36.7353 0.863022 36.9628 0.635545C37.1903 0.408069 37.4988 0.280273 37.8205 0.280273C38.1422 0.280273 38.4507 0.408069 38.6782 0.635545C38.9057 0.863022 39.0335 1.17155 39.0335 1.49325V8.30167C39.0341 8.94874 39.2915 9.56911 39.749 10.0267C40.2066 10.4842 40.8269 10.7415 41.474 10.7422H48.7203C49.042 10.7422 49.3505 10.87 49.578 11.0974C49.8055 11.3249 49.9333 11.6334 49.9333 11.9551C49.9333 12.2768 49.8055 12.5854 49.578 12.8129C49.3505 13.0403 49.042 13.1681 48.7203 13.1681Z",fill:"currentColor"}),r.createElement("path",{d:"M17.1575 40.3774C16.8358 40.3774 16.5273 40.2496 16.2998 40.0222C16.0723 39.7947 15.9445 39.4862 15.9445 39.1644V29.4036C15.9445 29.0819 16.0723 28.7734 16.2998 28.5459C16.5273 28.3185 16.8358 28.1907 17.1575 28.1907C17.4792 28.1907 17.7877 28.3185 18.0152 28.5459C18.2427 28.7734 18.3705 29.0819 18.3705 29.4036V39.1644C18.3705 39.4862 18.2427 39.7947 18.0152 40.0222C17.7877 40.2496 17.4792 40.3774 17.1575 40.3774Z",fill:"currentColor"}),r.createElement("path",{d:"M17.1757 36.1381C16.8552 36.1381 16.5478 36.0113 16.3205 35.7854C16.0933 35.5595 15.9646 35.2528 15.9627 34.9324C15.9627 34.913 15.9506 32.9201 15.9506 32.1583C15.9506 31.53 15.9445 29.4073 15.9445 29.4073C15.9445 29.0856 16.0723 28.7771 16.2998 28.5496C16.5273 28.3221 16.8358 28.1943 17.1575 28.1943H19.8746C22.0919 28.1943 23.8968 29.9738 23.8968 32.162C23.8968 34.3502 22.0919 36.1296 19.8746 36.1296C19.1334 36.1296 17.206 36.1417 17.183 36.1417L17.1757 36.1381ZM18.3741 30.6166C18.3741 31.2086 18.3741 31.8551 18.3741 32.1583C18.3741 32.5125 18.3741 33.1384 18.3802 33.7049H19.8721C20.7418 33.7 21.4696 32.9929 21.4696 32.1583C21.4696 31.3238 20.7418 30.6166 19.8733 30.6166H18.3741Z",fill:"currentColor"}),r.createElement("path",{d:"M29.73 35.3921C29.5283 35.3923 29.3296 35.3421 29.1522 35.2462C28.9747 35.1502 28.8239 35.0115 28.7135 34.8426L25.5938 30.0672C25.4235 29.7979 25.366 29.4725 25.4336 29.1612C25.5013 28.8499 25.6887 28.5777 25.9554 28.4034C26.222 28.2291 26.5466 28.1668 26.8588 28.2298C27.1711 28.2928 27.4461 28.4761 27.6243 28.7402L29.7252 31.9582L31.803 28.7668C31.8899 28.633 32.0023 28.5175 32.1338 28.4271C32.2653 28.3366 32.4134 28.273 32.5695 28.2398C32.7256 28.2065 32.8867 28.2044 33.0437 28.2334C33.2006 28.2625 33.3503 28.3221 33.4842 28.409C33.6181 28.4959 33.7335 28.6083 33.824 28.7398C33.9144 28.8714 33.978 29.0194 34.0113 29.1755C34.0445 29.3316 34.0467 29.4927 34.0176 29.6497C33.9886 29.8066 33.9289 29.9563 33.842 30.0902L30.7501 34.8402C30.64 35.0096 30.4894 35.1487 30.3119 35.2451C30.1344 35.3415 29.9356 35.392 29.7337 35.3921H29.73Z",fill:"currentColor"}),r.createElement("path",{d:"M29.7179 40.3776C29.3962 40.3776 29.0876 40.2498 28.8602 40.0223C28.6327 39.7948 28.5049 39.4863 28.5049 39.1646L28.5182 34.1781C28.5182 33.8564 28.646 33.5478 28.8735 33.3204C29.101 33.0929 29.4095 32.9651 29.7312 32.9651C30.0529 32.9651 30.3614 33.0929 30.5889 33.3204C30.8164 33.5478 30.9442 33.8564 30.9442 34.1781L30.9308 39.1682C30.9299 39.4893 30.8016 39.7969 30.5743 40.0236C30.3469 40.2503 30.0389 40.3776 29.7179 40.3776Z",fill:"currentColor"}),r.createElement("path",{d:"M6.10975 52.2791H4.24541C3.11946 52.2791 2.03962 51.8319 1.24345 51.0357C0.447283 50.2395 0 49.1597 0 48.0337C0 46.9078 0.447283 45.8279 1.24345 45.0318C2.03962 44.2356 3.11946 43.7883 4.24541 43.7883H9.75474C10.0764 43.7883 10.385 43.9161 10.6124 44.1436C10.8399 44.3711 10.9677 44.6796 10.9677 45.0013C10.9677 45.323 10.8399 45.6315 10.6124 45.859C10.385 46.0865 10.0764 46.2143 9.75474 46.2143H4.24541C3.76286 46.2143 3.30007 46.406 2.95886 46.7472C2.61764 47.0884 2.42595 47.5512 2.42595 48.0337C2.42595 48.5163 2.61764 48.9791 2.95886 49.3203C3.30007 49.6615 3.76286 49.8532 4.24541 49.8532H6.11339C6.43509 49.8532 6.74361 49.981 6.97109 50.2085C7.19857 50.4359 7.32636 50.7445 7.32636 51.0662C7.32636 51.3879 7.19857 51.6964 6.97109 51.9239C6.74361 52.1514 6.43509 52.2791 6.11339 52.2791H6.10975Z",fill:"currentColor"}),r.createElement("path",{d:"M1.22872 48.8975C0.90702 48.8975 0.598496 48.7697 0.371019 48.5423C0.143542 48.3148 0.0157471 48.0063 0.0157471 47.6846L0.067905 26.3362C0.069833 25.2115 0.517967 24.1336 1.31393 23.339C2.10989 22.5444 3.18862 22.0981 4.31331 22.0981L48.7846 22.0896C49.1063 22.0896 49.4148 22.2174 49.6423 22.4449C49.8698 22.6723 49.9976 22.9809 49.9976 23.3026V45.0015C49.9976 45.3232 49.8698 45.6317 49.6423 45.8592C49.4148 46.0866 49.1063 46.2144 48.7846 46.2144H5.52386C5.20216 46.2144 4.89364 46.0866 4.66616 45.8592C4.43868 45.6317 4.31089 45.3232 4.31089 45.0015C4.31089 44.6798 4.43868 44.3712 4.66616 44.1438C4.89364 43.9163 5.20216 43.7885 5.52386 43.7885H47.5692V24.5083L4.30967 24.5168C3.82712 24.5168 3.36434 24.7085 3.02312 25.0497C2.68191 25.3909 2.49021 25.8537 2.49021 26.3362L2.43806 47.6846C2.43806 48.0056 2.31076 48.3136 2.08407 48.541C1.85738 48.7684 1.54979 48.8966 1.22872 48.8975Z",fill:"currentColor"})):"csv"===t?r.createElement("svg",{className:` ${i} inline-block  `,"aria-hidden":"true",fill:"currentColor",viewBox:"0 0 50 63"},r.createElement("path",{d:"M42.6967 62.1044H13.464C11.5281 62.1021 9.67207 61.3321 8.30315 59.9632C6.93422 58.5942 6.16417 56.7382 6.16193 54.8023V45.2659C6.16193 44.9442 6.28972 44.6357 6.5172 44.4082C6.74467 44.1807 7.0532 44.0529 7.3749 44.0529C7.6966 44.0529 8.00513 44.1807 8.2326 44.4082C8.46008 44.6357 8.58787 44.9442 8.58787 45.2659V54.8023C8.58948 56.095 9.10373 57.3344 10.0178 58.2485C10.9319 59.1626 12.1713 59.6768 13.464 59.6784H42.6967C43.9896 59.6768 45.229 59.1626 46.1433 58.2485C47.0576 57.3345 47.5721 56.0951 47.5741 54.8023V43.8746C47.5741 43.5529 47.7019 43.2444 47.9293 43.0169C48.1568 42.7894 48.4653 42.6616 48.787 42.6616C49.1087 42.6616 49.4173 42.7894 49.6447 43.0169C49.8722 43.2444 50 43.5529 50 43.8746V54.8023C49.9975 56.7383 49.2271 58.5944 47.858 59.9632C46.4889 61.3321 44.6328 62.1021 42.6967 62.1044Z",fill:"black"}),r.createElement("path",{d:"M48.7822 41.6183C48.4605 41.6183 48.152 41.4906 47.9245 41.2631C47.697 41.0356 47.5692 40.7271 47.5692 40.4054V12.5967C47.5681 12.2529 47.4946 11.9131 47.3533 11.5995C47.212 11.286 47.0062 11.0058 46.7492 10.7773L38.0522 3.04699C37.6064 2.64832 37.0297 2.42731 36.4317 2.42595H14.677C13.0616 2.42852 11.5131 3.07163 10.3712 4.21425C9.22922 5.35686 8.58704 6.90572 8.58543 8.52114V23.3037C8.58543 23.6254 8.45764 23.9339 8.23016 24.1614C8.00268 24.3888 7.69416 24.5166 7.37246 24.5166C7.05076 24.5166 6.74223 24.3888 6.51476 24.1614C6.28728 23.9339 6.15948 23.6254 6.15948 23.3037V8.52114C6.16173 6.26251 7.05971 4.09698 8.65646 2.49955C10.2532 0.902118 12.4184 0.0032109 14.677 9.10874e-08H36.4317C37.6233 -0.000230408 38.7736 0.437008 39.6643 1.22874L48.3613 8.96024C48.8752 9.41695 49.2866 9.97738 49.5682 10.6046C49.8498 11.2318 49.9953 11.9116 49.9952 12.5992V40.4054C49.9952 40.7271 49.8674 41.0356 49.6399 41.2631C49.4124 41.4906 49.1039 41.6183 48.7822 41.6183Z",fill:"black"}),r.createElement("path",{d:"M48.7203 13.1681H41.474C40.1838 13.1665 38.947 12.6533 38.0347 11.741C37.1224 10.8287 36.6091 9.59184 36.6075 8.30167V1.49325C36.6075 1.17155 36.7353 0.863022 36.9628 0.635545C37.1903 0.408069 37.4988 0.280273 37.8205 0.280273C38.1422 0.280273 38.4507 0.408069 38.6782 0.635545C38.9057 0.863022 39.0335 1.17155 39.0335 1.49325V8.30167C39.0341 8.94874 39.2915 9.56911 39.749 10.0267C40.2066 10.4842 40.8269 10.7415 41.474 10.7422H48.7203C49.042 10.7422 49.3505 10.87 49.578 11.0974C49.8055 11.3249 49.9333 11.6334 49.9333 11.9551C49.9333 12.2768 49.8055 12.5854 49.578 12.8129C49.3505 13.0403 49.042 13.1681 48.7203 13.1681Z",fill:"black"}),r.createElement("path",{d:"M6.10975 52.2791H4.24541C3.11946 52.2791 2.03962 51.8319 1.24345 51.0357C0.447283 50.2395 0 49.1597 0 48.0337C0 46.9078 0.447283 45.8279 1.24345 45.0318C2.03962 44.2356 3.11946 43.7883 4.24541 43.7883H9.75474C10.0764 43.7883 10.385 43.9161 10.6124 44.1436C10.8399 44.3711 10.9677 44.6796 10.9677 45.0013C10.9677 45.323 10.8399 45.6315 10.6124 45.859C10.385 46.0865 10.0764 46.2143 9.75474 46.2143H4.24541C3.76286 46.2143 3.30007 46.406 2.95886 46.7472C2.61764 47.0884 2.42595 47.5512 2.42595 48.0337C2.42595 48.5163 2.61764 48.9791 2.95886 49.3203C3.30007 49.6615 3.76286 49.8532 4.24541 49.8532H6.11339C6.43509 49.8532 6.74361 49.981 6.97109 50.2085C7.19857 50.4359 7.32636 50.7445 7.32636 51.0662C7.32636 51.3879 7.19857 51.6964 6.97109 51.9239C6.74361 52.1514 6.43509 52.2791 6.11339 52.2791H6.10975Z",fill:"black"}),r.createElement("path",{d:"M1.22872 48.8975C0.90702 48.8975 0.598496 48.7697 0.371019 48.5423C0.143542 48.3148 0.0157471 48.0063 0.0157471 47.6846L0.067905 26.3362C0.069833 25.2115 0.517967 24.1336 1.31393 23.339C2.10989 22.5444 3.18862 22.0981 4.31331 22.0981L48.7846 22.0896C49.1063 22.0896 49.4148 22.2174 49.6423 22.4449C49.8698 22.6723 49.9976 22.9809 49.9976 23.3026V45.0015C49.9976 45.3232 49.8698 45.6317 49.6423 45.8592C49.4148 46.0866 49.1063 46.2144 48.7846 46.2144H5.52386C5.20216 46.2144 4.89364 46.0866 4.66616 45.8592C4.43868 45.6317 4.31089 45.3232 4.31089 45.0015C4.31089 44.6798 4.43868 44.3712 4.66616 44.1438C4.89364 43.9163 5.20216 43.7885 5.52386 43.7885H47.5692V24.5083L4.30967 24.5168C3.82712 24.5168 3.36434 24.7085 3.02312 25.0497C2.68191 25.3909 2.49021 25.8537 2.49021 26.3362L2.43806 47.6846C2.43806 48.0056 2.31076 48.3136 2.08407 48.541C1.85738 48.7684 1.54979 48.8966 1.22872 48.8975Z",fill:"black"}),r.createElement("path",{d:"M18.1587 36.5474C18.4077 36.5474 18.6291 36.4948 18.8228 36.3896C19.022 36.2845 19.1797 36.1213 19.2959 35.8999C19.4121 35.673 19.4757 35.3797 19.4868 35.02H21.3047C21.2936 35.6564 21.147 36.2126 20.8647 36.6885C20.5825 37.1589 20.2062 37.5241 19.7358 37.7842C19.271 38.0387 18.7536 38.166 18.1836 38.166C17.5915 38.166 17.0741 38.0719 16.6313 37.8838C16.1942 37.6901 15.8317 37.4106 15.5439 37.0454C15.2562 36.6802 15.0404 36.2375 14.8965 35.7173C14.7581 35.1916 14.689 34.5911 14.689 33.916V33.1025C14.689 32.4329 14.7581 31.8353 14.8965 31.3096C15.0404 30.7839 15.2562 30.3384 15.5439 29.9731C15.8317 29.6079 16.1942 29.3312 16.6313 29.1431C17.0685 28.9494 17.5832 28.8525 18.1753 28.8525C18.8062 28.8525 19.3512 28.9854 19.8105 29.251C20.2754 29.5111 20.6379 29.8929 20.8979 30.3965C21.158 30.9001 21.2936 31.5171 21.3047 32.2476H19.4868C19.4757 31.8602 19.4176 31.5365 19.3125 31.2764C19.2074 31.0107 19.0579 30.8115 18.8643 30.6787C18.6761 30.5459 18.4382 30.4795 18.1504 30.4795C17.835 30.4795 17.5749 30.5404 17.3701 30.6621C17.1709 30.7783 17.016 30.9499 16.9053 31.1768C16.8001 31.4036 16.7254 31.6803 16.6812 32.0068C16.6424 32.3278 16.623 32.693 16.623 33.1025V33.916C16.623 34.3366 16.6424 34.7101 16.6812 35.0366C16.7199 35.3576 16.7918 35.6315 16.897 35.8584C17.0076 36.0853 17.1654 36.2568 17.3701 36.373C17.5749 36.4893 17.8377 36.5474 18.1587 36.5474ZM26.9658 35.6343C26.9658 35.4461 26.9105 35.2801 26.7998 35.1362C26.6947 34.9868 26.5231 34.8402 26.2852 34.6963C26.0472 34.5524 25.729 34.3975 25.3306 34.2314C24.9155 34.0599 24.5475 33.8883 24.2266 33.7168C23.9056 33.5452 23.6344 33.3571 23.4131 33.1523C23.1917 32.9421 23.0229 32.7013 22.9067 32.4302C22.7905 32.159 22.7324 31.8436 22.7324 31.4839C22.7324 31.1131 22.8016 30.77 22.9399 30.4546C23.0783 30.1392 23.2775 29.8625 23.5376 29.6245C23.7977 29.381 24.1104 29.1929 24.4756 29.0601C24.8408 28.9217 25.2531 28.8525 25.7124 28.8525C26.3599 28.8525 26.9132 28.9715 27.3726 29.2095C27.8374 29.4419 28.1916 29.7684 28.4351 30.189C28.6841 30.604 28.8086 31.0827 28.8086 31.625H26.8745C26.8745 31.3981 26.833 31.1934 26.75 31.0107C26.667 30.8226 26.5397 30.6732 26.3682 30.5625C26.1966 30.4463 25.978 30.3882 25.7124 30.3882C25.4689 30.3882 25.2642 30.4352 25.0981 30.5293C24.9377 30.6178 24.8159 30.7396 24.7329 30.8945C24.6554 31.0439 24.6167 31.2127 24.6167 31.4009C24.6167 31.5392 24.6416 31.661 24.6914 31.7661C24.7412 31.8713 24.8242 31.9736 24.9404 32.0732C25.0566 32.1673 25.2116 32.2642 25.4053 32.3638C25.6045 32.4634 25.8563 32.5768 26.1606 32.7041C26.7638 32.9365 27.2646 33.1772 27.6631 33.4263C28.0615 33.6753 28.3604 33.9658 28.5596 34.2979C28.7588 34.6299 28.8584 35.0422 28.8584 35.5347C28.8584 35.9331 28.7837 36.2928 28.6343 36.6138C28.4904 36.9347 28.2801 37.2114 28.0034 37.4438C27.7323 37.6763 27.403 37.8561 27.0156 37.9834C26.6338 38.1051 26.2077 38.166 25.7373 38.166C25.0345 38.166 24.4396 38.0277 23.9526 37.751C23.4712 37.4688 23.106 37.1063 22.8569 36.6636C22.6079 36.2209 22.4834 35.7533 22.4834 35.2607H24.3511C24.3677 35.6149 24.4424 35.8916 24.5752 36.0908C24.7135 36.29 24.8879 36.4284 25.0981 36.5059C25.3084 36.5778 25.527 36.6138 25.7539 36.6138C26.0251 36.6138 26.2492 36.5778 26.4263 36.5059C26.6034 36.4284 26.7362 36.3149 26.8247 36.1655C26.9188 36.0161 26.9658 35.839 26.9658 35.6343ZM33.0503 36.3896L34.7104 29.0186H36.7109L34.1543 38H32.9175L33.0503 36.3896ZM31.8716 29.0186L33.5898 36.4146L33.6729 38H32.4443L29.8711 29.0186H31.8716Z",fill:"black"})):"pdf"===t?r.createElement("svg",{className:` ${i} inline-block  `,"aria-hidden":"true",fill:"currentColor",viewBox:"0 0 50 63"},r.createElement("path",{d:"M42.6966 62.1044H13.464C11.528 62.1021 9.67201 61.3321 8.30308 59.9632C6.93416 58.5942 6.16411 56.7382 6.16187 54.8023V45.2659C6.16187 44.9442 6.28966 44.6357 6.51714 44.4082C6.74461 44.1807 7.05314 44.0529 7.37484 44.0529C7.69654 44.0529 8.00507 44.1807 8.23254 44.4082C8.46002 44.6357 8.58781 44.9442 8.58781 45.2659V54.8023C8.58942 56.095 9.10367 57.3344 10.0178 58.2485C10.9319 59.1626 12.1712 59.6768 13.464 59.6784H42.6966C43.9895 59.6768 45.229 59.1626 46.1433 58.2485C47.0576 57.3345 47.5721 56.0951 47.574 54.8023V43.8746C47.574 43.5529 47.7018 43.2444 47.9293 43.0169C48.1568 42.7894 48.4653 42.6616 48.787 42.6616C49.1087 42.6616 49.4172 42.7894 49.6447 43.0169C49.8722 43.2444 50 43.5529 50 43.8746V54.8023C49.9974 56.7383 49.2271 58.5944 47.858 59.9632C46.4888 61.3321 44.6327 62.1021 42.6966 62.1044Z",fill:"currentColor"}),r.createElement("path",{d:"M48.7821 41.6183C48.4604 41.6183 48.1519 41.4906 47.9244 41.2631C47.6969 41.0356 47.5691 40.7271 47.5691 40.4054V12.5967C47.5681 12.2529 47.4945 11.9131 47.3532 11.5995C47.2119 11.286 47.0061 11.0058 46.7492 10.7773L38.0521 3.04699C37.6064 2.64832 37.0297 2.42731 36.4316 2.42595H14.6769C13.0615 2.42852 11.513 3.07163 10.3711 4.21425C9.22916 5.35686 8.58698 6.90572 8.58537 8.52114V23.3037C8.58537 23.6254 8.45758 23.9339 8.2301 24.1614C8.00262 24.3888 7.6941 24.5166 7.3724 24.5166C7.0507 24.5166 6.74217 24.3888 6.5147 24.1614C6.28722 23.9339 6.15942 23.6254 6.15942 23.3037V8.52114C6.16167 6.26251 7.05965 4.09698 8.6564 2.49955C10.2532 0.902118 12.4183 0.0032109 14.6769 9.10874e-08H36.4316C37.6233 -0.000230408 38.7736 0.437008 39.6642 1.22874L48.3612 8.96024C48.8752 9.41695 49.2865 9.97738 49.5681 10.6046C49.8497 11.2318 49.9953 11.9116 49.9951 12.5992V40.4054C49.9951 40.7271 49.8673 41.0356 49.6398 41.2631C49.4123 41.4906 49.1038 41.6183 48.7821 41.6183Z",fill:"currentColor"}),r.createElement("path",{d:"M48.7203 13.1681H41.474C40.1838 13.1665 38.947 12.6533 38.0347 11.741C37.1224 10.8287 36.6091 9.59184 36.6075 8.30167V1.49325C36.6075 1.17155 36.7353 0.863022 36.9628 0.635545C37.1903 0.408069 37.4988 0.280273 37.8205 0.280273C38.1422 0.280273 38.4507 0.408069 38.6782 0.635545C38.9057 0.863022 39.0335 1.17155 39.0335 1.49325V8.30167C39.0341 8.94874 39.2915 9.56911 39.749 10.0267C40.2066 10.4842 40.8269 10.7415 41.474 10.7422H48.7203C49.042 10.7422 49.3505 10.87 49.578 11.0974C49.8055 11.3249 49.9333 11.6334 49.9333 11.9551C49.9333 12.2768 49.8055 12.5854 49.578 12.8129C49.3505 13.0403 49.042 13.1681 48.7203 13.1681Z",fill:"currentColor"}),r.createElement("path",{d:"M34.2362 40.3774C33.9145 40.3774 33.6059 40.2496 33.3785 40.0222C33.151 39.7947 33.0232 39.4862 33.0232 39.1644V29.4036C33.0232 29.0819 33.151 28.7734 33.3785 28.5459C33.6059 28.3185 33.9145 28.1907 34.2362 28.1907H38.1929C38.5146 28.1907 38.8231 28.3185 39.0506 28.5459C39.2781 28.7734 39.4059 29.0819 39.4059 29.4036C39.4059 29.7253 39.2781 30.0339 39.0506 30.2613C38.8231 30.4888 38.5146 30.6166 38.1929 30.6166H35.4491V39.1644C35.4491 39.4862 35.3213 39.7947 35.0939 40.0222C34.8664 40.2496 34.5579 40.3774 34.2362 40.3774Z",fill:"currentColor"}),r.createElement("path",{d:"M37.8788 35.4042H34.2398C33.9181 35.4042 33.6096 35.2764 33.3821 35.0489C33.1546 34.8215 33.0269 34.5129 33.0269 34.1912C33.0269 33.8695 33.1546 33.561 33.3821 33.3335C33.6096 33.1061 33.9181 32.9783 34.2398 32.9783H37.8788C38.2005 32.9783 38.509 33.1061 38.7365 33.3335C38.9639 33.561 39.0917 33.8695 39.0917 34.1912C39.0917 34.5129 38.9639 34.8215 38.7365 35.0489C38.509 35.2764 38.2005 35.4042 37.8788 35.4042Z",fill:"currentColor"}),r.createElement("path",{d:"M22.161 40.2198C21.8393 40.2198 21.5308 40.092 21.3033 39.8645C21.0759 39.6371 20.9481 39.3285 20.9481 39.0068V29.4146C20.9481 29.0929 21.0759 28.7844 21.3033 28.5569C21.5308 28.3295 21.8393 28.2017 22.161 28.2017C22.4827 28.2017 22.7913 28.3295 23.0187 28.5569C23.2462 28.7844 23.374 29.0929 23.374 29.4146V39.0068C23.374 39.3285 23.2462 39.6371 23.0187 39.8645C22.7913 40.092 22.4827 40.2198 22.161 40.2198Z",fill:"currentColor"}),r.createElement("path",{d:"M22.1817 40.3774C21.86 40.3774 21.5514 40.2496 21.324 40.0221C21.0965 39.7947 20.9687 39.4861 20.9687 39.1644C20.9687 39.1293 20.9553 35.6104 20.9553 34.2689C20.9553 33.1554 20.9481 29.4012 20.9481 29.4012C20.9481 29.0795 21.0759 28.771 21.3033 28.5435C21.5308 28.316 21.8393 28.1882 22.161 28.1882H24.9254C28.0088 28.1882 30.0805 30.6372 30.0805 34.2822C30.0805 37.7489 27.9554 40.3022 25.0285 40.3531C24.2401 40.3677 22.269 40.375 22.1853 40.375L22.1817 40.3774ZM23.3764 30.6166C23.3764 31.8162 23.3764 33.5714 23.3764 34.2737C23.3764 35.1082 23.3764 36.7906 23.3849 37.9454C23.9672 37.9454 24.6149 37.9369 24.9812 37.9308C26.8249 37.8981 27.6497 36.0762 27.6497 34.2846C27.6497 32.5113 26.9329 30.6166 24.9205 30.6166H23.3764Z",fill:"currentColor"}),r.createElement("path",{d:"M11.7865 40.3774C11.4648 40.3774 11.1562 40.2496 10.9288 40.0222C10.7013 39.7947 10.5735 39.4862 10.5735 39.1644V29.4036C10.5735 29.0819 10.7013 28.7734 10.9288 28.5459C11.1562 28.3185 11.4648 28.1907 11.7865 28.1907C12.1082 28.1907 12.4167 28.3185 12.6442 28.5459C12.8716 28.7734 12.9994 29.0819 12.9994 29.4036V39.1644C12.9994 39.4862 12.8716 39.7947 12.6442 40.0222C12.4167 40.2496 12.1082 40.3774 11.7865 40.3774Z",fill:"currentColor"}),r.createElement("path",{d:"M11.8047 36.1381C11.4842 36.1381 11.1768 36.0113 10.9496 35.7854C10.7223 35.5595 10.5936 35.2528 10.5917 34.9324C10.5917 34.913 10.5796 32.9201 10.5796 32.1583C10.5796 31.53 10.5796 29.4073 10.5796 29.4073C10.5796 29.0856 10.7074 28.7771 10.9349 28.5496C11.1623 28.3221 11.4709 28.1943 11.7926 28.1943H14.5096C16.7269 28.1943 18.5318 29.9738 18.5318 32.162C18.5318 34.3502 16.7269 36.1296 14.5096 36.1296C13.7685 36.1296 11.8411 36.1417 11.8192 36.1417L11.8047 36.1381ZM13.0031 30.6166C13.0031 31.2086 13.0031 31.8563 13.0031 32.1583C13.0031 32.5125 13.0031 33.1384 13.0031 33.7049H14.4951C15.3599 33.7049 16.0913 32.9989 16.0913 32.1632C16.0913 31.3274 15.3636 30.6215 14.4951 30.6215L13.0031 30.6166Z",fill:"currentColor"}),r.createElement("path",{d:"M6.10975 52.2791H4.24541C3.11946 52.2791 2.03962 51.8319 1.24345 51.0357C0.447283 50.2395 0 49.1597 0 48.0337C0 46.9078 0.447283 45.8279 1.24345 45.0318C2.03962 44.2356 3.11946 43.7883 4.24541 43.7883H9.75474C10.0764 43.7883 10.385 43.9161 10.6124 44.1436C10.8399 44.3711 10.9677 44.6796 10.9677 45.0013C10.9677 45.323 10.8399 45.6315 10.6124 45.859C10.385 46.0865 10.0764 46.2143 9.75474 46.2143H4.24541C3.76286 46.2143 3.30007 46.406 2.95886 46.7472C2.61764 47.0884 2.42595 47.5512 2.42595 48.0337C2.42595 48.5163 2.61764 48.9791 2.95886 49.3203C3.30007 49.6615 3.76286 49.8532 4.24541 49.8532H6.11339C6.43509 49.8532 6.74361 49.981 6.97109 50.2085C7.19857 50.4359 7.32636 50.7445 7.32636 51.0662C7.32636 51.3879 7.19857 51.6964 6.97109 51.9239C6.74361 52.1514 6.43509 52.2791 6.11339 52.2791H6.10975Z",fill:"currentColor"}),r.createElement("path",{d:"M1.22872 48.8973C0.90702 48.8973 0.598496 48.7695 0.371019 48.542C0.143542 48.3145 0.0157471 48.006 0.0157471 47.6843L0.067905 26.336C0.069833 25.2113 0.517967 24.1333 1.31393 23.3387C2.10989 22.5441 3.18862 22.0978 4.31331 22.0978L48.7846 22.0894C49.1063 22.0894 49.4148 22.2171 49.6423 22.4446C49.8698 22.6721 49.9976 22.9806 49.9976 23.3023V45.0012C49.9976 45.3229 49.8698 45.6314 49.6423 45.8589C49.4148 46.0864 49.1063 46.2142 48.7846 46.2142H5.52386C5.20216 46.2142 4.89364 46.0864 4.66616 45.8589C4.43868 45.6314 4.31089 45.3229 4.31089 45.0012C4.31089 44.6795 4.43868 44.371 4.66616 44.1435C4.89364 43.916 5.20216 43.7882 5.52386 43.7882H47.5692V24.508L4.30967 24.5165C3.82712 24.5165 3.36434 24.7082 3.02312 25.0494C2.68191 25.3906 2.49021 25.8534 2.49021 26.336L2.43806 47.6843C2.43806 48.0054 2.31076 48.3134 2.08407 48.5407C1.85738 48.7681 1.54979 48.8963 1.22872 48.8973Z",fill:"currentColor"})):"microsoft"===t?r.createElement("svg",{className:` ${i} inline-block  `,fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 23 23"},r.createElement("g",{clipPath:"url(#clip0_616_221)"},r.createElement("path",{d:"M1 1H11V11H1V1Z",fill:"#F35325"}),r.createElement("path",{d:"M12 1H22V11H12V1Z",fill:"#81BC06"}),r.createElement("path",{d:"M1 12H11V22H1V12Z",fill:"#05A6F0"}),r.createElement("path",{d:"M12 12H22V22H12V12Z",fill:"#FFBA08"})),r.createElement("defs",null,r.createElement("clipPath",{id:"clip0_616_221"},r.createElement("rect",{width:"23",height:"23",fill:"white"})))):"loading"===t?r.createElement("svg",{className:` ${i} inline-block animate-spin  `,xmlns:"http://www.w3.org/2000/svg",fill:"currentColor",viewBox:"0 0 24 24"},r.createElement("path",{d:"M11 3c-1.613 0-3.122.437-4.432 1.185l1.65 2.445-6.702-.378 2.226-6.252 1.703 2.522c1.633-.959 3.525-1.522 5.555-1.522 4.406 0 8.197 2.598 9.953 6.34l-1.642 1.215c-1.355-3.258-4.569-5.555-8.311-5.555zm13 12.486l-2.375-6.157-5.307 3.925 3.389.984c-.982 3.811-4.396 6.651-8.488 6.75l.891 1.955c4.609-.461 8.373-3.774 9.521-8.146l2.369.689zm-18.117 3.906c-2.344-1.625-3.883-4.33-3.883-7.392 0-1.314.29-2.56.799-3.687l-2.108-.12c-.439 1.188-.691 2.467-.691 3.807 0 3.831 1.965 7.192 4.936 9.158l-1.524 2.842 6.516-1.044-2.735-6.006-1.31 2.442z"})):"mcp"===t?r.createElement("svg",{className:` ${i} inline-block  `,xmlns:"http://www.w3.org/2000/svg",fill:"currentColor",viewBox:"0 0 24 24"},r.createElement("path",{d:"M15.688 2.343a2.588 2.588 0 00-3.61 0l-9.626 9.44a.863.863 0 01-1.203 0 .823.823 0 010-1.18l9.626-9.44a4.313 4.313 0 016.016 0 4.116 4.116 0 011.204 3.54 4.3 4.3 0 013.609 1.18l.05.05a4.115 4.115 0 010 5.9l-8.706 8.537a.274.274 0 000 .393l1.788 1.754a.823.823 0 010 1.18.863.863 0 01-1.203 0l-1.788-1.753a1.92 1.92 0 010-2.754l8.706-8.538a2.47 2.47 0 000-3.54l-.05-.049a2.588 2.588 0 00-3.607-.003l-7.172 7.034-.002.002-.098.097a.863.863 0 01-1.204 0 .823.823 0 010-1.18l7.273-7.133a2.47 2.47 0 00-.003-3.537z"}),r.createElement("path",{d:"M14.485 4.703a.823.823 0 000-1.18.863.863 0 00-1.204 0l-7.119 6.982a4.115 4.115 0 000 5.9 4.314 4.314 0 006.016 0l7.12-6.982a.823.823 0 000-1.18.863.863 0 00-1.204 0l-7.119 6.982a2.588 2.588 0 01-3.61 0 2.47 2.47 0 010-3.54l7.12-6.982z"})):"app"===t?r.createElement("img",{src:"/logo.svg",alt:"App Logo",className:` ${i} inline-block  `}):r.createElement("svg",{className:` ${i} inline-block  `,xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},r.createElement("path",{d:"M1 3.488c0-1.926 4.656-3.488 10-3.488 5.345 0 10 1.562 10 3.488s-4.655 3.487-10 3.487c-5.344 0-10-1.561-10-3.487zm10 9.158c5.345 0 10-1.562 10-3.487v-2.44c-2.418 1.738-7.005 2.256-10 2.256-3.006 0-7.588-.523-10-2.256v2.44c0 1.926 4.656 3.487 10 3.487zm0 5.665c.34 0 .678-.007 1.011-.019.045-1.407.537-2.7 1.342-3.745-.839.067-1.643.1-2.353.1-3.006 0-7.588-.523-10-2.256v2.434c0 1.925 4.656 3.486 10 3.486zm1.254 1.97c-.438.02-.861.03-1.254.03-2.995 0-7.582-.518-10-2.256v2.458c0 1.925 4.656 3.487 10 3.487 1.284 0 2.526-.092 3.676-.256-1.155-.844-2.02-2.055-2.422-3.463zm10.746-1.781c0 2.485-2.017 4.5-4.5 4.5s-4.5-2.015-4.5-4.5 2.017-4.5 4.5-4.5 4.5 2.015 4.5 4.5zm-2.166-1.289l-2.063.557.916-1.925-1.387.392-1.466 3.034 1.739-.472-1.177 2.545 3.438-4.131z"}))}},6855:function(e,t,n){var r,o=n(5544),i=n(9379),a=n(6540);var l=0;var s=(0,i.A)({},r||(r=n.t(a,2))).useId;t.A=s?function(e){var t=s();return e||t}:function(e){var t=a.useState("ssr-id"),n=(0,o.A)(t,2),r=n[0],i=n[1];return a.useEffect(function(){var e=l;l+=1,i("rc_unique_".concat(e))},[]),e||r}},6885:function(e,t,n){n.d(t,{ll:function(){return k},rD:function(){return T},__:function(){return F},UU:function(){return O},cY:function(){return S},BN:function(){return P},Ej:function(){return M}});var r=n(7193);function o(e,t,n){let{reference:o,floating:i}=e;const a=(0,r.TV)(t),l=(0,r.Dz)(t),s=(0,r.sq)(l),u=(0,r.C0)(t),c="y"===a,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,m=o[s]/2-i[s]/2;let p;switch(u){case"top":p={x:d,y:o.y-i.height};break;case"bottom":p={x:d,y:o.y+o.height};break;case"right":p={x:o.x+o.width,y:f};break;case"left":p={x:o.x-i.width,y:f};break;default:p={x:o.x,y:o.y}}switch((0,r.Sg)(t)){case"start":p[l]-=m*(n&&c?-1:1);break;case"end":p[l]+=m*(n&&c?-1:1)}return p}async function i(e,t){var n;void 0===t&&(t={});const{x:o,y:i,platform:a,rects:l,elements:s,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:d="viewport",elementContext:f="floating",altBoundary:m=!1,padding:p=0}=(0,r._3)(t,e),h=(0,r.nI)(p),v=s[m?"floating"===f?"reference":"floating":f],g=(0,r.B1)(await a.getClippingRect({element:null==(n=await(null==a.isElement?void 0:a.isElement(v)))||n?v:v.contextElement||await(null==a.getDocumentElement?void 0:a.getDocumentElement(s.floating)),boundary:c,rootBoundary:d,strategy:u})),y="floating"===f?{x:o,y:i,width:l.floating.width,height:l.floating.height}:l.reference,b=await(null==a.getOffsetParent?void 0:a.getOffsetParent(s.floating)),w=await(null==a.isElement?void 0:a.isElement(b))&&await(null==a.getScale?void 0:a.getScale(b))||{x:1,y:1},C=(0,r.B1)(a.convertOffsetParentRelativeRectToViewportRelativeRect?await a.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:y,offsetParent:b,strategy:u}):y);return{top:(g.top-C.top+h.top)/w.y,bottom:(C.bottom-g.bottom+h.bottom)/w.y,left:(g.left-C.left+h.left)/w.x,right:(C.right-g.right+h.right)/w.x}}const a=new Set(["left","top"]);var l=n(6635);function s(e){const t=(0,l.L9)(e);let n=parseFloat(t.width)||0,o=parseFloat(t.height)||0;const i=(0,l.sb)(e),a=i?e.offsetWidth:n,s=i?e.offsetHeight:o,u=(0,r.LI)(n)!==a||(0,r.LI)(o)!==s;return u&&(n=a,o=s),{width:n,height:o,$:u}}function u(e){return(0,l.vq)(e)?e:e.contextElement}function c(e){const t=u(e);if(!(0,l.sb)(t))return(0,r.Jx)(1);const n=t.getBoundingClientRect(),{width:o,height:i,$:a}=s(t);let c=(a?(0,r.LI)(n.width):n.width)/o,d=(a?(0,r.LI)(n.height):n.height)/i;return c&&Number.isFinite(c)||(c=1),d&&Number.isFinite(d)||(d=1),{x:c,y:d}}const d=(0,r.Jx)(0);function f(e){const t=(0,l.zk)(e);return(0,l.Tc)()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:d}function m(e,t,n,o){void 0===t&&(t=!1),void 0===n&&(n=!1);const i=e.getBoundingClientRect(),a=u(e);let s=(0,r.Jx)(1);t&&(o?(0,l.vq)(o)&&(s=c(o)):s=c(e));const d=function(e,t,n){return void 0===t&&(t=!1),!(!n||t&&n!==(0,l.zk)(e))&&t}(a,n,o)?f(a):(0,r.Jx)(0);let m=(i.left+d.x)/s.x,p=(i.top+d.y)/s.y,h=i.width/s.x,v=i.height/s.y;if(a){const e=(0,l.zk)(a),t=o&&(0,l.vq)(o)?(0,l.zk)(o):o;let n=e,r=(0,l._m)(n);for(;r&&o&&t!==n;){const e=c(r),t=r.getBoundingClientRect(),o=(0,l.L9)(r),i=t.left+(r.clientLeft+parseFloat(o.paddingLeft))*e.x,a=t.top+(r.clientTop+parseFloat(o.paddingTop))*e.y;m*=e.x,p*=e.y,h*=e.x,v*=e.y,m+=i,p+=a,n=(0,l.zk)(r),r=(0,l._m)(n)}}return(0,r.B1)({width:h,height:v,x:m,y:p})}function p(e,t){const n=(0,l.CP)(e).scrollLeft;return t?t.left+n:m((0,l.ep)(e)).left+n}function h(e,t,n){void 0===n&&(n=!1);const r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:p(e,r)),y:r.top+t.scrollTop}}const v=new Set(["absolute","fixed"]);function g(e,t,n){let o;if("viewport"===t)o=function(e,t){const n=(0,l.zk)(e),r=(0,l.ep)(e),o=n.visualViewport;let i=r.clientWidth,a=r.clientHeight,s=0,u=0;if(o){i=o.width,a=o.height;const e=(0,l.Tc)();(!e||e&&"fixed"===t)&&(s=o.offsetLeft,u=o.offsetTop)}return{width:i,height:a,x:s,y:u}}(e,n);else if("document"===t)o=function(e){const t=(0,l.ep)(e),n=(0,l.CP)(e),o=e.ownerDocument.body,i=(0,r.T9)(t.scrollWidth,t.clientWidth,o.scrollWidth,o.clientWidth),a=(0,r.T9)(t.scrollHeight,t.clientHeight,o.scrollHeight,o.clientHeight);let s=-n.scrollLeft+p(e);const u=-n.scrollTop;return"rtl"===(0,l.L9)(o).direction&&(s+=(0,r.T9)(t.clientWidth,o.clientWidth)-i),{width:i,height:a,x:s,y:u}}((0,l.ep)(e));else if((0,l.vq)(t))o=function(e,t){const n=m(e,!0,"fixed"===t),o=n.top+e.clientTop,i=n.left+e.clientLeft,a=(0,l.sb)(e)?c(e):(0,r.Jx)(1);return{width:e.clientWidth*a.x,height:e.clientHeight*a.y,x:i*a.x,y:o*a.y}}(t,n);else{const n=f(e);o={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return(0,r.B1)(o)}function y(e,t){const n=(0,l.$4)(e);return!(n===t||!(0,l.vq)(n)||(0,l.eu)(n))&&("fixed"===(0,l.L9)(n).position||y(n,t))}function b(e,t,n){const o=(0,l.sb)(t),i=(0,l.ep)(t),a="fixed"===n,s=m(e,!0,a,t);let u={scrollLeft:0,scrollTop:0};const c=(0,r.Jx)(0);function d(){c.x=p(i)}if(o||!o&&!a)if(("body"!==(0,l.mq)(t)||(0,l.ZU)(i))&&(u=(0,l.CP)(t)),o){const e=m(t,!0,a,t);c.x=e.x+t.clientLeft,c.y=e.y+t.clientTop}else i&&d();a&&!o&&i&&d();const f=!i||o||a?(0,r.Jx)(0):h(i,u);return{x:s.left+u.scrollLeft-c.x-f.x,y:s.top+u.scrollTop-c.y-f.y,width:s.width,height:s.height}}function w(e){return"static"===(0,l.L9)(e).position}function C(e,t){if(!(0,l.sb)(e)||"fixed"===(0,l.L9)(e).position)return null;if(t)return t(e);let n=e.offsetParent;return(0,l.ep)(e)===n&&(n=n.ownerDocument.body),n}function E(e,t){const n=(0,l.zk)(e);if((0,l.Tf)(e))return n;if(!(0,l.sb)(e)){let t=(0,l.$4)(e);for(;t&&!(0,l.eu)(t);){if((0,l.vq)(t)&&!w(t))return t;t=(0,l.$4)(t)}return n}let r=C(e,t);for(;r&&(0,l.Lv)(r)&&w(r);)r=C(r,t);return r&&(0,l.eu)(r)&&w(r)&&!(0,l.sQ)(r)?n:r||(0,l.gJ)(e)||n}const A={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:o,strategy:i}=e;const a="fixed"===i,s=(0,l.ep)(o),u=!!t&&(0,l.Tf)(t.floating);if(o===s||u&&a)return n;let d={scrollLeft:0,scrollTop:0},f=(0,r.Jx)(1);const p=(0,r.Jx)(0),v=(0,l.sb)(o);if((v||!v&&!a)&&(("body"!==(0,l.mq)(o)||(0,l.ZU)(s))&&(d=(0,l.CP)(o)),(0,l.sb)(o))){const e=m(o);f=c(o),p.x=e.x+o.clientLeft,p.y=e.y+o.clientTop}const g=!s||v||a?(0,r.Jx)(0):h(s,d,!0);return{width:n.width*f.x,height:n.height*f.y,x:n.x*f.x-d.scrollLeft*f.x+p.x+g.x,y:n.y*f.y-d.scrollTop*f.y+p.y+g.y}},getDocumentElement:l.ep,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:o,strategy:i}=e;const a=[..."clippingAncestors"===n?(0,l.Tf)(t)?[]:function(e,t){const n=t.get(e);if(n)return n;let r=(0,l.v9)(e,[],!1).filter(e=>(0,l.vq)(e)&&"body"!==(0,l.mq)(e)),o=null;const i="fixed"===(0,l.L9)(e).position;let a=i?(0,l.$4)(e):e;for(;(0,l.vq)(a)&&!(0,l.eu)(a);){const t=(0,l.L9)(a),n=(0,l.sQ)(a);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&o&&v.has(o.position)||(0,l.ZU)(a)&&!n&&y(e,a))?r=r.filter(e=>e!==a):o=t,a=(0,l.$4)(a)}return t.set(e,r),r}(t,this._c):[].concat(n),o],s=a[0],u=a.reduce((e,n)=>{const o=g(t,n,i);return e.top=(0,r.T9)(o.top,e.top),e.right=(0,r.jk)(o.right,e.right),e.bottom=(0,r.jk)(o.bottom,e.bottom),e.left=(0,r.T9)(o.left,e.left),e},g(t,s,i));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:E,getElementRects:async function(e){const t=this.getOffsetParent||E,n=this.getDimensions,r=await n(e.floating);return{reference:b(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){const{width:t,height:n}=s(e);return{width:t,height:n}},getScale:c,isElement:l.vq,isRTL:function(e){return"rtl"===(0,l.L9)(e).direction}};function x(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function k(e,t,n,o){void 0===o&&(o={});const{ancestorScroll:i=!0,ancestorResize:a=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:c="function"==typeof IntersectionObserver,animationFrame:d=!1}=o,f=u(e),p=i||a?[...f?(0,l.v9)(f):[],...(0,l.v9)(t)]:[];p.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),a&&e.addEventListener("resize",n)});const h=f&&c?function(e,t){let n,o=null;const i=(0,l.ep)(e);function a(){var e;clearTimeout(n),null==(e=o)||e.disconnect(),o=null}return function l(s,u){void 0===s&&(s=!1),void 0===u&&(u=1),a();const c=e.getBoundingClientRect(),{left:d,top:f,width:m,height:p}=c;if(s||t(),!m||!p)return;const h={rootMargin:-(0,r.RI)(f)+"px "+-(0,r.RI)(i.clientWidth-(d+m))+"px "+-(0,r.RI)(i.clientHeight-(f+p))+"px "+-(0,r.RI)(d)+"px",threshold:(0,r.T9)(0,(0,r.jk)(1,u))||1};let v=!0;function g(t){const r=t[0].intersectionRatio;if(r!==u){if(!v)return l();r?l(!1,r):n=setTimeout(()=>{l(!1,1e-7)},1e3)}1!==r||x(c,e.getBoundingClientRect())||l(),v=!1}try{o=new IntersectionObserver(g,{...h,root:i.ownerDocument})}catch(y){o=new IntersectionObserver(g,h)}o.observe(e)}(!0),a}(f,n):null;let v,g=-1,y=null;s&&(y=new ResizeObserver(e=>{let[r]=e;r&&r.target===f&&y&&(y.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var e;null==(e=y)||e.observe(t)})),n()}),f&&!d&&y.observe(f),y.observe(t));let b=d?m(e):null;return d&&function t(){const r=m(e);b&&!x(b,r)&&n();b=r,v=requestAnimationFrame(t)}(),n(),()=>{var e;p.forEach(e=>{i&&e.removeEventListener("scroll",n),a&&e.removeEventListener("resize",n)}),null==h||h(),null==(e=y)||e.disconnect(),y=null,d&&cancelAnimationFrame(v)}}const F=i,S=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,o;const{x:i,y:l,placement:s,middlewareData:u}=t,c=await async function(e,t){const{placement:n,platform:o,elements:i}=e,l=await(null==o.isRTL?void 0:o.isRTL(i.floating)),s=(0,r.C0)(n),u=(0,r.Sg)(n),c="y"===(0,r.TV)(n),d=a.has(s)?-1:1,f=l&&c?-1:1,m=(0,r._3)(t,e);let{mainAxis:p,crossAxis:h,alignmentAxis:v}="number"==typeof m?{mainAxis:m,crossAxis:0,alignmentAxis:null}:{mainAxis:m.mainAxis||0,crossAxis:m.crossAxis||0,alignmentAxis:m.alignmentAxis};return u&&"number"==typeof v&&(h="end"===u?-1*v:v),c?{x:h*f,y:p*d}:{x:p*d,y:h*f}}(t,e);return s===(null==(n=u.offset)?void 0:n.placement)&&null!=(o=u.arrow)&&o.alignmentOffset?{}:{x:i+c.x,y:l+c.y,data:{...c,placement:s}}}}},P=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:o,placement:a}=t,{mainAxis:l=!0,crossAxis:s=!1,limiter:u={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...c}=(0,r._3)(e,t),d={x:n,y:o},f=await i(t,c),m=(0,r.TV)((0,r.C0)(a)),p=(0,r.PG)(m);let h=d[p],v=d[m];if(l){const e="y"===p?"bottom":"right",t=h+f["y"===p?"top":"left"],n=h-f[e];h=(0,r.qE)(t,h,n)}if(s){const e="y"===m?"bottom":"right",t=v+f["y"===m?"top":"left"],n=v-f[e];v=(0,r.qE)(t,v,n)}const g=u.fn({...t,[p]:h,[m]:v});return{...g,data:{x:g.x-n,y:g.y-o,enabled:{[p]:l,[m]:s}}}}}},O=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,o;const{placement:a,middlewareData:l,rects:s,initialPlacement:u,platform:c,elements:d}=t,{mainAxis:f=!0,crossAxis:m=!0,fallbackPlacements:p,fallbackStrategy:h="bestFit",fallbackAxisSideDirection:v="none",flipAlignment:g=!0,...y}=(0,r._3)(e,t);if(null!=(n=l.arrow)&&n.alignmentOffset)return{};const b=(0,r.C0)(a),w=(0,r.TV)(u),C=(0,r.C0)(u)===u,E=await(null==c.isRTL?void 0:c.isRTL(d.floating)),A=p||(C||!g?[(0,r.bV)(u)]:(0,r.WJ)(u)),x="none"!==v;!p&&x&&A.push(...(0,r.lP)(u,g,v,E));const k=[u,...A],F=await i(t,y),S=[];let P=(null==(o=l.flip)?void 0:o.overflows)||[];if(f&&S.push(F[b]),m){const e=(0,r.w7)(a,s,E);S.push(F[e[0]],F[e[1]])}if(P=[...P,{placement:a,overflows:S}],!S.every(e=>e<=0)){var O,M;const e=((null==(O=l.flip)?void 0:O.index)||0)+1,t=k[e];if(t){if(!("alignment"===m&&w!==(0,r.TV)(t))||P.every(e=>e.overflows[0]>0&&(0,r.TV)(e.placement)===w))return{data:{index:e,overflows:P},reset:{placement:t}}}let n=null==(M=P.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:M.placement;if(!n)switch(h){case"bestFit":{var T;const e=null==(T=P.filter(e=>{if(x){const t=(0,r.TV)(e.placement);return t===w||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:T[0];e&&(n=e);break}case"initialPlacement":n=u}if(a!==n)return{reset:{placement:n}}}return{}}}},M=function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,o;const{placement:a,rects:l,platform:s,elements:u}=t,{apply:c=()=>{},...d}=(0,r._3)(e,t),f=await i(t,d),m=(0,r.C0)(a),p=(0,r.Sg)(a),h="y"===(0,r.TV)(a),{width:v,height:g}=l.floating;let y,b;"top"===m||"bottom"===m?(y=m,b=p===(await(null==s.isRTL?void 0:s.isRTL(u.floating))?"start":"end")?"left":"right"):(b=m,y="end"===p?"top":"bottom");const w=g-f.top-f.bottom,C=v-f.left-f.right,E=(0,r.jk)(g-f[y],w),A=(0,r.jk)(v-f[b],C),x=!t.middlewareData.shift;let k=E,F=A;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(F=C),null!=(o=t.middlewareData.shift)&&o.enabled.y&&(k=w),x&&!p){const e=(0,r.T9)(f.left,0),t=(0,r.T9)(f.right,0),n=(0,r.T9)(f.top,0),o=(0,r.T9)(f.bottom,0);h?F=v-2*(0!==e||0!==t?e+t:(0,r.T9)(f.left,f.right)):k=g-2*(0!==n||0!==o?n+o:(0,r.T9)(f.top,f.bottom))}await c({...t,availableWidth:F,availableHeight:k});const S=await s.getDimensions(u.floating);return v!==S.width||g!==S.height?{reset:{rects:!0}}:{}}}},T=(e,t,n)=>{const r=new Map,i={platform:A,...n},a={...i.platform,_c:r};return(async(e,t,n)=>{const{placement:r="bottom",strategy:i="absolute",middleware:a=[],platform:l}=n,s=a.filter(Boolean),u=await(null==l.isRTL?void 0:l.isRTL(t));let c=await l.getElementRects({reference:e,floating:t,strategy:i}),{x:d,y:f}=o(c,r,u),m=r,p={},h=0;for(let v=0;v<s.length;v++){const{name:n,fn:a}=s[v],{x:g,y:y,data:b,reset:w}=await a({x:d,y:f,initialPlacement:r,placement:m,strategy:i,middlewareData:p,rects:c,platform:l,elements:{reference:e,floating:t}});d=null!=g?g:d,f=null!=y?y:f,p={...p,[n]:{...p[n],...b}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(m=w.placement),w.rects&&(c=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:i}):w.rects),({x:d,y:f}=o(c,m,u))),v=-1)}return{x:d,y:f,placement:m,strategy:i,middlewareData:p}})(e,t,{...i,platform:a})}},6984:function(e,t,n){function r(){const e=navigator.userAgentData;return e&&Array.isArray(e.brands)?e.brands.map(e=>{let{brand:t,version:n}=e;return t+"/"+n}).join(" "):navigator.userAgent}n.d(t,{$t:function(){return r}})},7134:function(e,t,n){n.d(t,{KU:function(){return r},Zr:function(){return i}});function r(e,t){let n;try{n=e()}catch(r){return}return{getItem:e=>{var r;const o=e=>null===e?null:JSON.parse(e,null==t?void 0:t.reviver),i=null!=(r=n.getItem(e))?r:null;return i instanceof Promise?i.then(o):o(i)},setItem:(e,r)=>n.setItem(e,JSON.stringify(r,null==t?void 0:t.replacer)),removeItem:e=>n.removeItem(e)}}const o=e=>t=>{try{const n=e(t);return n instanceof Promise?n:{then(e){return o(e)(n)},catch(e){return this}}}catch(n){return{then(e){return this},catch(e){return o(e)(n)}}}},i=(e,t)=>(n,i,a)=>{let l={storage:r(()=>localStorage),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},s=!1;const u=new Set,c=new Set;let d=l.storage;if(!d)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${l.name}', the given storage is currently unavailable.`),n(...e)},i,a);const f=()=>{const e=l.partialize({...i()});return d.setItem(l.name,{state:e,version:l.version})},m=a.setState;a.setState=(e,t)=>{m(e,t),f()};const p=e((...e)=>{n(...e),f()},i,a);let h;a.getInitialState=()=>p;const v=()=>{var e,t;if(!d)return;s=!1,u.forEach(e=>{var t;return e(null!=(t=i())?t:p)});const r=(null==(t=l.onRehydrateStorage)?void 0:t.call(l,null!=(e=i())?e:p))||void 0;return o(d.getItem.bind(d))(l.name).then(e=>{if(e){if("number"!=typeof e.version||e.version===l.version)return[!1,e.state];if(l.migrate){const t=l.migrate(e.state,e.version);return t instanceof Promise?t.then(e=>[!0,e]):[!0,t]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(e=>{var t;const[r,o]=e;if(h=l.merge(o,null!=(t=i())?t:p),n(h,!0),r)return f()}).then(()=>{null==r||r(h,void 0),h=i(),s=!0,c.forEach(e=>e(h))}).catch(e=>{null==r||r(void 0,e)})};return a.persist={setOptions:e=>{l={...l,...e},e.storage&&(d=e.storage)},clearStorage:()=>{null==d||d.removeItem(l.name)},getOptions:()=>l,rehydrate:()=>v(),hasHydrated:()=>s,onHydrate:e=>(u.add(e),()=>{u.delete(e)}),onFinishHydration:e=>(c.add(e),()=>{c.delete(e)})},l.skipHydration||v(),h||p}},7193:function(e,t,n){n.d(t,{B1:function(){return O},C0:function(){return f},Dz:function(){return y},Jx:function(){return l},LI:function(){return i},PG:function(){return p},RI:function(){return a},Sg:function(){return m},T9:function(){return o},TV:function(){return g},WJ:function(){return w},_3:function(){return d},bV:function(){return S},jk:function(){return r},lP:function(){return F},nI:function(){return P},qE:function(){return c},sq:function(){return h},w7:function(){return b}});const r=Math.min,o=Math.max,i=Math.round,a=Math.floor,l=e=>({x:e,y:e}),s={left:"right",right:"left",bottom:"top",top:"bottom"},u={start:"end",end:"start"};function c(e,t,n){return o(e,r(t,n))}function d(e,t){return"function"==typeof e?e(t):e}function f(e){return e.split("-")[0]}function m(e){return e.split("-")[1]}function p(e){return"x"===e?"y":"x"}function h(e){return"y"===e?"height":"width"}const v=new Set(["top","bottom"]);function g(e){return v.has(f(e))?"y":"x"}function y(e){return p(g(e))}function b(e,t,n){void 0===n&&(n=!1);const r=m(e),o=y(e),i=h(o);let a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=S(a)),[a,S(a)]}function w(e){const t=S(e);return[C(e),t,C(t)]}function C(e){return e.replace(/start|end/g,e=>u[e])}const E=["left","right"],A=["right","left"],x=["top","bottom"],k=["bottom","top"];function F(e,t,n,r){const o=m(e);let i=function(e,t,n){switch(e){case"top":case"bottom":return n?t?A:E:t?E:A;case"left":case"right":return t?x:k;default:return[]}}(f(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(C)))),i}function S(e){return e.replace(/left|right|bottom|top/g,e=>s[e])}function P(e){return"number"!=typeof e?function(e){return{top:0,right:0,bottom:0,left:0,...e}}(e):{top:e,right:e,bottom:e,left:e}}function O(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}},8418:function(e,t,n){e.exports=n(5160)},8430:function(e,t){t.A=function(){if("undefined"==typeof navigator||"undefined"==typeof window)return!1;var e=navigator.userAgent||navigator.vendor||window.opera;return/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino|android|ipad|playbook|silk/i.test(e)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw-(n|u)|c55\/|capi|ccwa|cdm-|cell|chtm|cldc|cmd-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc-s|devi|dica|dmob|do(c|p)o|ds(12|-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(-|_)|g1 u|g560|gene|gf-5|g-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd-(m|p|t)|hei-|hi(pt|ta)|hp( i|ip)|hs-c|ht(c(-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i-(20|go|ma)|i230|iac( |-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|-[a-w])|libw|lynx|m1-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|-([1-8]|c))|phil|pire|pl(ay|uc)|pn-2|po(ck|rt|se)|prox|psio|pt-g|qa-a|qc(07|12|21|32|60|-[2-7]|i-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h-|oo|p-)|sdk\/|se(c(-|0|1)|47|mc|nd|ri)|sgh-|shar|sie(-|m)|sk-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h-|v-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl-|tdg-|tel(i|m)|tim-|t-mo|to(pl|sh)|ts(70|m-|m3|m5)|tx-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas-|your|zeto|zte-/i.test(null==e?void 0:e.substr(0,4))}},8462:function(e,t,n){n.d(t,{A:function(){return q}});var r=n(8168),o=n(6540),i=n(2546),a=(n(8210),n(9379)),l=n(2284),s=n(6588),u=n(8719),c=o.createContext(null);var d=function(){if("undefined"!=typeof Map)return Map;function e(e,t){var n=-1;return e.some(function(e,r){return e[0]===t&&(n=r,!0)}),n}return function(){function t(){this.__entries__=[]}return Object.defineProperty(t.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),t.prototype.get=function(t){var n=e(this.__entries__,t),r=this.__entries__[n];return r&&r[1]},t.prototype.set=function(t,n){var r=e(this.__entries__,t);~r?this.__entries__[r][1]=n:this.__entries__.push([t,n])},t.prototype.delete=function(t){var n=this.__entries__,r=e(n,t);~r&&n.splice(r,1)},t.prototype.has=function(t){return!!~e(this.__entries__,t)},t.prototype.clear=function(){this.__entries__.splice(0)},t.prototype.forEach=function(e,t){void 0===t&&(t=null);for(var n=0,r=this.__entries__;n<r.length;n++){var o=r[n];e.call(t,o[1],o[0])}},t}()}(),f="undefined"!=typeof window&&"undefined"!=typeof document&&window.document===document,m=void 0!==n.g&&n.g.Math===Math?n.g:"undefined"!=typeof self&&self.Math===Math?self:"undefined"!=typeof window&&window.Math===Math?window:Function("return this")(),p="function"==typeof requestAnimationFrame?requestAnimationFrame.bind(m):function(e){return setTimeout(function(){return e(Date.now())},1e3/60)};var h=["top","right","bottom","left","width","height","size","weight"],v="undefined"!=typeof MutationObserver,g=function(){function e(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=function(e,t){var n=!1,r=!1,o=0;function i(){n&&(n=!1,e()),r&&l()}function a(){p(i)}function l(){var e=Date.now();if(n){if(e-o<2)return;r=!0}else n=!0,r=!1,setTimeout(a,t);o=e}return l}(this.refresh.bind(this),20)}return e.prototype.addObserver=function(e){~this.observers_.indexOf(e)||this.observers_.push(e),this.connected_||this.connect_()},e.prototype.removeObserver=function(e){var t=this.observers_,n=t.indexOf(e);~n&&t.splice(n,1),!t.length&&this.connected_&&this.disconnect_()},e.prototype.refresh=function(){this.updateObservers_()&&this.refresh()},e.prototype.updateObservers_=function(){var e=this.observers_.filter(function(e){return e.gatherActive(),e.hasActive()});return e.forEach(function(e){return e.broadcastActive()}),e.length>0},e.prototype.connect_=function(){f&&!this.connected_&&(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),v?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},e.prototype.disconnect_=function(){f&&this.connected_&&(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},e.prototype.onTransitionEnd_=function(e){var t=e.propertyName,n=void 0===t?"":t;h.some(function(e){return!!~n.indexOf(e)})&&this.refresh()},e.getInstance=function(){return this.instance_||(this.instance_=new e),this.instance_},e.instance_=null,e}(),y=function(e,t){for(var n=0,r=Object.keys(t);n<r.length;n++){var o=r[n];Object.defineProperty(e,o,{value:t[o],enumerable:!1,writable:!1,configurable:!0})}return e},b=function(e){return e&&e.ownerDocument&&e.ownerDocument.defaultView||m},w=F(0,0,0,0);function C(e){return parseFloat(e)||0}function E(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return t.reduce(function(t,n){return t+C(e["border-"+n+"-width"])},0)}function A(e){var t=e.clientWidth,n=e.clientHeight;if(!t&&!n)return w;var r=b(e).getComputedStyle(e),o=function(e){for(var t={},n=0,r=["top","right","bottom","left"];n<r.length;n++){var o=r[n],i=e["padding-"+o];t[o]=C(i)}return t}(r),i=o.left+o.right,a=o.top+o.bottom,l=C(r.width),s=C(r.height);if("border-box"===r.boxSizing&&(Math.round(l+i)!==t&&(l-=E(r,"left","right")+i),Math.round(s+a)!==n&&(s-=E(r,"top","bottom")+a)),!function(e){return e===b(e).document.documentElement}(e)){var u=Math.round(l+i)-t,c=Math.round(s+a)-n;1!==Math.abs(u)&&(l-=u),1!==Math.abs(c)&&(s-=c)}return F(o.left,o.top,l,s)}var x="undefined"!=typeof SVGGraphicsElement?function(e){return e instanceof b(e).SVGGraphicsElement}:function(e){return e instanceof b(e).SVGElement&&"function"==typeof e.getBBox};function k(e){return f?x(e)?function(e){var t=e.getBBox();return F(0,0,t.width,t.height)}(e):A(e):w}function F(e,t,n,r){return{x:e,y:t,width:n,height:r}}var S=function(){function e(e){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=F(0,0,0,0),this.target=e}return e.prototype.isActive=function(){var e=k(this.target);return this.contentRect_=e,e.width!==this.broadcastWidth||e.height!==this.broadcastHeight},e.prototype.broadcastRect=function(){var e=this.contentRect_;return this.broadcastWidth=e.width,this.broadcastHeight=e.height,e},e}(),P=function(e,t){var n,r,o,i,a,l,s,u=(r=(n=t).x,o=n.y,i=n.width,a=n.height,l="undefined"!=typeof DOMRectReadOnly?DOMRectReadOnly:Object,s=Object.create(l.prototype),y(s,{x:r,y:o,width:i,height:a,top:o,right:r+i,bottom:a+o,left:r}),s);y(this,{target:e,contentRect:u})},O=function(){function e(e,t,n){if(this.activeObservations_=[],this.observations_=new d,"function"!=typeof e)throw new TypeError("The callback provided as parameter 1 is not a function.");this.callback_=e,this.controller_=t,this.callbackCtx_=n}return e.prototype.observe=function(e){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(e instanceof b(e).Element))throw new TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(e)||(t.set(e,new S(e)),this.controller_.addObserver(this),this.controller_.refresh())}},e.prototype.unobserve=function(e){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(e instanceof b(e).Element))throw new TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(e)&&(t.delete(e),t.size||this.controller_.removeObserver(this))}},e.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},e.prototype.gatherActive=function(){var e=this;this.clearActive(),this.observations_.forEach(function(t){t.isActive()&&e.activeObservations_.push(t)})},e.prototype.broadcastActive=function(){if(this.hasActive()){var e=this.callbackCtx_,t=this.activeObservations_.map(function(e){return new P(e.target,e.broadcastRect())});this.callback_.call(e,t,e),this.clearActive()}},e.prototype.clearActive=function(){this.activeObservations_.splice(0)},e.prototype.hasActive=function(){return this.activeObservations_.length>0},e}(),M="undefined"!=typeof WeakMap?new WeakMap:new d,T=function e(t){if(!(this instanceof e))throw new TypeError("Cannot call a class as a function.");if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");var n=g.getInstance(),r=new O(t,n,this);M.set(this,r)};["observe","unobserve","disconnect"].forEach(function(e){T.prototype[e]=function(){var t;return(t=M.get(this))[e].apply(t,arguments)}});var N=void 0!==m.ResizeObserver?m.ResizeObserver:T,R=new Map;var L=new N(function(e){e.forEach(function(e){var t,n=e.target;null===(t=R.get(n))||void 0===t||t.forEach(function(e){return e(n)})})});var I=n(3029),$=n(2901),V=n(5501),D=n(9426),j=function(e){(0,V.A)(n,e);var t=(0,D.A)(n);function n(){return(0,I.A)(this,n),t.apply(this,arguments)}return(0,$.A)(n,[{key:"render",value:function(){return this.props.children}}]),n}(o.Component);function _(e,t){var n=e.children,r=e.disabled,i=o.useRef(null),d=o.useRef(null),f=o.useContext(c),m="function"==typeof n,p=m?n(i):n,h=o.useRef({width:-1,height:-1,offsetWidth:-1,offsetHeight:-1}),v=!m&&o.isValidElement(p)&&(0,u.f3)(p),g=v?(0,u.A9)(p):null,y=(0,u.xK)(g,i),b=function(){var e;return(0,s.Ay)(i.current)||(i.current&&"object"===(0,l.A)(i.current)?(0,s.Ay)(null===(e=i.current)||void 0===e?void 0:e.nativeElement):null)||(0,s.Ay)(d.current)};o.useImperativeHandle(t,function(){return b()});var w=o.useRef(e);w.current=e;var C=o.useCallback(function(e){var t=w.current,n=t.onResize,r=t.data,o=e.getBoundingClientRect(),i=o.width,l=o.height,s=e.offsetWidth,u=e.offsetHeight,c=Math.floor(i),d=Math.floor(l);if(h.current.width!==c||h.current.height!==d||h.current.offsetWidth!==s||h.current.offsetHeight!==u){var m={width:c,height:d,offsetWidth:s,offsetHeight:u};h.current=m;var p=s===Math.round(i)?i:s,v=u===Math.round(l)?l:u,g=(0,a.A)((0,a.A)({},m),{},{offsetWidth:p,offsetHeight:v});null==f||f(g,e,r),n&&Promise.resolve().then(function(){n(g,e)})}},[]);return o.useEffect(function(){var e,t,n=b();return n&&!r&&(e=n,t=C,R.has(e)||(R.set(e,new Set),L.observe(e)),R.get(e).add(t)),function(){return function(e,t){R.has(e)&&(R.get(e).delete(t),R.get(e).size||(L.unobserve(e),R.delete(e)))}(n,C)}},[i.current,r]),o.createElement(j,{ref:d},v?o.cloneElement(p,{ref:y}):p)}var H=o.forwardRef(_);function z(e,t){var n=e.children;return("function"==typeof n?[n]:(0,i.A)(n)).map(function(n,i){var a=(null==n?void 0:n.key)||"".concat("rc-observer-key","-").concat(i);return o.createElement(H,(0,r.A)({},e,{key:a,ref:0===i?t:void 0}),n)})}var B=o.forwardRef(z);B.Collection=function(e){var t=e.children,n=e.onBatchResize,r=o.useRef(0),i=o.useRef([]),a=o.useContext(c),l=o.useCallback(function(e,t,o){r.current+=1;var l=r.current;i.current.push({size:e,element:t,data:o}),Promise.resolve().then(function(){l===r.current&&(null==n||n(i.current),i.current=[])}),null==a||a(e,t,o)},[n,a]);return o.createElement(c.Provider,{value:l},t)};var q=B},8697:function(e,t,n){n.d(t,{A:function(){return r}});const r=(0,n(1788).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},9077:function(e,t,n){n.d(t,{aB:function(){return y},nF:function(){return i}});var r=n(2187),o=n(4980);const i=new r.Mo("antZoomIn",{"0%":{transform:"scale(0.2)",opacity:0},"100%":{transform:"scale(1)",opacity:1}}),a=new r.Mo("antZoomOut",{"0%":{transform:"scale(1)"},"100%":{transform:"scale(0.2)",opacity:0}}),l=new r.Mo("antZoomBigIn",{"0%":{transform:"scale(0.8)",opacity:0},"100%":{transform:"scale(1)",opacity:1}}),s=new r.Mo("antZoomBigOut",{"0%":{transform:"scale(1)"},"100%":{transform:"scale(0.8)",opacity:0}}),u=new r.Mo("antZoomUpIn",{"0%":{transform:"scale(0.8)",transformOrigin:"50% 0%",opacity:0},"100%":{transform:"scale(1)",transformOrigin:"50% 0%"}}),c=new r.Mo("antZoomUpOut",{"0%":{transform:"scale(1)",transformOrigin:"50% 0%"},"100%":{transform:"scale(0.8)",transformOrigin:"50% 0%",opacity:0}}),d=new r.Mo("antZoomLeftIn",{"0%":{transform:"scale(0.8)",transformOrigin:"0% 50%",opacity:0},"100%":{transform:"scale(1)",transformOrigin:"0% 50%"}}),f=new r.Mo("antZoomLeftOut",{"0%":{transform:"scale(1)",transformOrigin:"0% 50%"},"100%":{transform:"scale(0.8)",transformOrigin:"0% 50%",opacity:0}}),m=new r.Mo("antZoomRightIn",{"0%":{transform:"scale(0.8)",transformOrigin:"100% 50%",opacity:0},"100%":{transform:"scale(1)",transformOrigin:"100% 50%"}}),p=new r.Mo("antZoomRightOut",{"0%":{transform:"scale(1)",transformOrigin:"100% 50%"},"100%":{transform:"scale(0.8)",transformOrigin:"100% 50%",opacity:0}}),h=new r.Mo("antZoomDownIn",{"0%":{transform:"scale(0.8)",transformOrigin:"50% 100%",opacity:0},"100%":{transform:"scale(1)",transformOrigin:"50% 100%"}}),v=new r.Mo("antZoomDownOut",{"0%":{transform:"scale(1)",transformOrigin:"50% 100%"},"100%":{transform:"scale(0.8)",transformOrigin:"50% 100%",opacity:0}}),g={zoom:{inKeyframes:i,outKeyframes:a},"zoom-big":{inKeyframes:l,outKeyframes:s},"zoom-big-fast":{inKeyframes:l,outKeyframes:s},"zoom-left":{inKeyframes:d,outKeyframes:f},"zoom-right":{inKeyframes:m,outKeyframes:p},"zoom-up":{inKeyframes:u,outKeyframes:c},"zoom-down":{inKeyframes:h,outKeyframes:v}},y=(e,t)=>{const{antCls:n}=e,r=`${n}-${t}`,{inKeyframes:i,outKeyframes:a}=g[t];return[(0,o.b)(r,i,a,"zoom-big-fast"===t?e.motionDurationFast:e.motionDurationMid),{[`\n        ${r}-enter,\n        ${r}-appear\n      `]:{transform:"scale(0)",opacity:0,animationTimingFunction:e.motionEaseOutCirc,"&-prepare":{transform:"none"}},[`${r}-leave`]:{animationTimingFunction:e.motionEaseInOutCirc}}]}},9644:function(e,t,n){n.d(t,{A:function(){return r}});const r=(0,n(1788).A)("PanelLeftClose",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"m16 15-3-3 3-3",key:"14y99z"}]])},9853:function(e,t,n){function r(e,t){var n=Object.assign({},e);return Array.isArray(t)&&t.forEach(function(e){delete n[e]}),n}n.d(t,{A:function(){return r}})},9910:function(e,t,n){n.d(t,{A:function(){return r}});const r=(0,n(1788).A)("PanelLeftOpen",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"m14 9 3 3-3 3",key:"8010ee"}]])}}]);
//# sourceMappingURL=18072fb3317f9e055d60965c3e06951cded0477e-2786126d45e7b7b5f19c.js.map