FROM python:3.10-slim
WORKDIR /code

RUN pip install -U gunicorn autogenstudio

# Create a non-root user
RUN useradd -m -u 1000 user
USER user
ENV HOME=/home/<USER>
    PATH=/home/<USER>/.local/bin:$PATH \
    AUTOGENSTUDIO_APPDIR=/home/<USER>/app

WORKDIR $HOME/app

COPY --chown=user . $HOME/app

CMD gunicorn -w $((2 * $(getconf _NPROCESSORS_ONLN) + 1)) --timeout 12600 -k uvicorn.workers.UvicornWorker autogenstudio.web.app:app --bind "0.0.0.0:8081"
