"use strict";(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[453],{731:function(e,t,n){n.r(t),n.d(t,{Head:function(){return i}});var l=n(6540),a=n(4810);const o={color:"#232129",padding:"96px",fontFamily:"-apple-system, Roboto, sans-serif, serif"},r={marginTop:0,marginBottom:64,maxWidth:320},s={marginBottom:48};t.default=()=>l.createElement("main",{style:o},l.createElement("h1",{style:r},"页面未找到"),l.createElement("p",{style:s},"抱歉 😔，我们找不到您要查找的内容。",l.createElement("br",null),null,l.createElement("br",null),l.createElement(a.<PERSON>,{to:"/"},"返回首页"),"。"));const i=()=>l.createElement("title",null,"页面未找到")}}]);
//# sourceMappingURL=component---src-pages-404-tsx-24eede75141e167b9bc2.js.map