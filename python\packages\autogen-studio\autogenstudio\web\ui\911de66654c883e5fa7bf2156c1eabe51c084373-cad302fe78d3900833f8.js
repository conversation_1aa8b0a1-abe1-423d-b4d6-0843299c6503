(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[559],{230:function(e,t,n){"use strict";n.d(t,{A:function(){return $e}});var o=n(6540),r=n(436),l=n(8168),i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z"}}]},name:"edit",theme:"outlined"},a=n(7064),c=function(e,t){return o.createElement(a.A,(0,l.A)({},e,{ref:t,icon:i}))};var s=o.forwardRef(c),u=n(6942),d=n.n(u),p=n(8462),f=n(2546),m=n(981),g=n(2533),b=n(9853),y=n(8719),v=n(998),h=function(e){if((0,v.A)()&&window.document.documentElement){var t=Array.isArray(e)?e:[e],n=window.document.documentElement;return t.some(function(e){return e in n.style})}return!1};function x(e,t){return Array.isArray(e)||void 0===t?h(e):function(e,t){if(!h(e))return!1;var n=document.createElement("div"),o=n.style[e];return n.style[e]=t,n.style[e]!==o}(e,t)}var O=n(2279),w=n(9155),E=n(367),S={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M864 170h-60c-4.4 0-8 3.6-8 8v518H310v-73c0-6.7-7.8-10.5-13-6.3l-141.9 112a8 8 0 000 12.6l141.9 112c5.3 4.2 13 .4 13-6.3v-75h498c35.3 0 64-28.7 64-64V178c0-4.4-3.6-8-8-8z"}}]},name:"enter",theme:"outlined"},C=function(e,t){return o.createElement(a.A,(0,l.A)({},e,{ref:t,icon:S}))};var j=o.forwardRef(C),k=n(6928),A=n(682),R=n(5144),$=n(5905),T=n(7358),I=n(5748),M=n(2187);const D=e=>{const t={};return[1,2,3,4,5].forEach(n=>{t[`\n      h${n}&,\n      div&-h${n},\n      div&-h${n} > textarea,\n      h${n}\n    `]=((e,t,n,o)=>{const{titleMarginBottom:r,fontWeightStrong:l}=o;return{marginBottom:r,color:n,fontWeight:l,fontSize:e,lineHeight:t}})(e[`fontSizeHeading${n}`],e[`lineHeightHeading${n}`],e.colorTextHeading,e)}),t},z=e=>{const{componentCls:t}=e;return{"a&, a":Object.assign(Object.assign({},(0,$.Y1)(e)),{userSelect:"text",[`&[disabled], &${t}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed","&:active, &:hover":{color:e.colorTextDisabled},"&:active":{pointerEvents:"none"}}})}},H=e=>({code:{margin:"0 0.2em",paddingInline:"0.4em",paddingBlock:"0.2em 0.1em",fontSize:"85%",fontFamily:e.fontFamilyCode,background:"rgba(150, 150, 150, 0.1)",border:"1px solid rgba(100, 100, 100, 0.2)",borderRadius:3},kbd:{margin:"0 0.2em",paddingInline:"0.4em",paddingBlock:"0.15em 0.1em",fontSize:"90%",fontFamily:e.fontFamilyCode,background:"rgba(150, 150, 150, 0.06)",border:"1px solid rgba(100, 100, 100, 0.2)",borderBottomWidth:2,borderRadius:3},mark:{padding:0,backgroundColor:I.bK[2]},"u, ins":{textDecoration:"underline",textDecorationSkipInk:"auto"},"s, del":{textDecoration:"line-through"},strong:{fontWeight:e.fontWeightStrong},"ul, ol":{marginInline:0,marginBlock:"0 1em",padding:0,li:{marginInline:"20px 0",marginBlock:0,paddingInline:"4px 0",paddingBlock:0}},ul:{listStyleType:"circle",ul:{listStyleType:"disc"}},ol:{listStyleType:"decimal"},"pre, blockquote":{margin:"1em 0"},pre:{padding:"0.4em 0.6em",whiteSpace:"pre-wrap",wordWrap:"break-word",background:"rgba(150, 150, 150, 0.1)",border:"1px solid rgba(100, 100, 100, 0.2)",borderRadius:3,fontFamily:e.fontFamilyCode,code:{display:"inline",margin:0,padding:0,fontSize:"inherit",fontFamily:"inherit",background:"transparent",border:0}},blockquote:{paddingInline:"0.6em 0",paddingBlock:0,borderInlineStart:"4px solid rgba(100, 100, 100, 0.2)",opacity:.85}}),P=e=>{const{componentCls:t,paddingSM:n}=e,o=n;return{"&-edit-content":{position:"relative","div&":{insetInlineStart:e.calc(e.paddingSM).mul(-1).equal(),marginTop:e.calc(o).mul(-1).equal(),marginBottom:`calc(1em - ${(0,M.zA)(o)})`},[`${t}-edit-content-confirm`]:{position:"absolute",insetInlineEnd:e.calc(e.marginXS).add(2).equal(),insetBlockEnd:e.marginXS,color:e.colorIcon,fontWeight:"normal",fontSize:e.fontSize,fontStyle:"normal",pointerEvents:"none"},textarea:{margin:"0!important",MozTransition:"none",height:"1em"}}}},B=e=>({[`${e.componentCls}-copy-success`]:{"\n    &,\n    &:hover,\n    &:focus":{color:e.colorSuccess}},[`${e.componentCls}-copy-icon-only`]:{marginInlineStart:0}}),L=e=>{const{componentCls:t,titleMarginTop:n}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({color:e.colorText,wordBreak:"break-word",lineHeight:e.lineHeight,[`&${t}-secondary`]:{color:e.colorTextDescription},[`&${t}-success`]:{color:e.colorSuccessText},[`&${t}-warning`]:{color:e.colorWarningText},[`&${t}-danger`]:{color:e.colorErrorText,"a&:active, a&:focus":{color:e.colorErrorTextActive},"a&:hover":{color:e.colorErrorTextHover}},[`&${t}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed",userSelect:"none"},"\n        div&,\n        p\n      ":{marginBottom:"1em"}},D(e)),{[`\n      & + h1${t},\n      & + h2${t},\n      & + h3${t},\n      & + h4${t},\n      & + h5${t}\n      `]:{marginTop:n},"\n      div,\n      ul,\n      li,\n      p,\n      h1,\n      h2,\n      h3,\n      h4,\n      h5":{"\n        + h1,\n        + h2,\n        + h3,\n        + h4,\n        + h5\n        ":{marginTop:n}}}),H(e)),z(e)),{[`\n        ${t}-expand,\n        ${t}-collapse,\n        ${t}-edit,\n        ${t}-copy\n      `]:Object.assign(Object.assign({},(0,$.Y1)(e)),{marginInlineStart:e.marginXXS})}),P(e)),B(e)),{"\n  a&-ellipsis,\n  span&-ellipsis\n  ":{display:"inline-block",maxWidth:"100%"},"&-ellipsis-single-line":{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis","a&, span&":{verticalAlign:"bottom"},"> code":{paddingBlock:0,maxWidth:"calc(100% - 1.2em)",display:"inline-block",overflow:"hidden",textOverflow:"ellipsis",verticalAlign:"bottom",boxSizing:"content-box"}},"&-ellipsis-multiple-line":{display:"-webkit-box",overflow:"hidden",WebkitLineClamp:3,WebkitBoxOrient:"vertical"}}),{"&-rtl":{direction:"rtl"}})}};var N=(0,T.OF)("Typography",e=>[L(e)],()=>({titleMarginTop:"1.2em",titleMarginBottom:"0.5em"}));var W=e=>{const{prefixCls:t,"aria-label":n,className:r,style:l,direction:i,maxLength:a,autoSize:c=!0,value:s,onSave:u,onCancel:p,onEnd:f,component:m,enterIcon:g=o.createElement(j,null)}=e,b=o.useRef(null),y=o.useRef(!1),v=o.useRef(null),[h,x]=o.useState(s);o.useEffect(()=>{x(s)},[s]),o.useEffect(()=>{var e;if(null===(e=b.current)||void 0===e?void 0:e.resizableTextArea){const{textArea:e}=b.current.resizableTextArea;e.focus();const{length:t}=e.value;e.setSelectionRange(t,t)}},[]);const O=()=>{u(h.trim())},[w,E,S]=N(t),C=d()(t,`${t}-edit-content`,{[`${t}-rtl`]:"rtl"===i,[`${t}-${m}`]:!!m},r,E,S);return w(o.createElement("div",{className:C,style:l},o.createElement(R.A,{ref:b,maxLength:a,value:h,onChange:({target:e})=>{x(e.value.replace(/[\n\r]/g,""))},onKeyDown:({keyCode:e})=>{y.current||(v.current=e)},onKeyUp:({keyCode:e,ctrlKey:t,altKey:n,metaKey:o,shiftKey:r})=>{v.current!==e||y.current||t||n||o||r||(e===k.A.ENTER?(O(),null==f||f()):e===k.A.ESC&&p())},onCompositionStart:()=>{y.current=!0},onCompositionEnd:()=>{y.current=!1},onBlur:()=>{O()},"aria-label":n,rows:1,autoSize:c}),null!==g?(0,A.Ob)(g,{className:`${t}-edit-content-confirm`}):null))},F=n(7965),K=n.n(F),U=n(6956);var V=(e,t=!1)=>t&&null==e?[]:Array.isArray(e)?e:[e],q=function(e,t,n,o){return new(n||(n=Promise))(function(r,l){function i(e){try{c(o.next(e))}catch(t){l(t)}}function a(e){try{c(o.throw(e))}catch(t){l(t)}}function c(e){var t;e.done?r(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(i,a)}c((o=o.apply(e,t||[])).next())})};var X=({copyConfig:e,children:t})=>{const[n,r]=o.useState(!1),[l,i]=o.useState(!1),a=o.useRef(null),c=()=>{a.current&&clearTimeout(a.current)},s={};e.format&&(s.format=e.format),o.useEffect(()=>c,[]);return{copied:n,copyLoading:l,onClick:(0,U.A)(n=>q(void 0,void 0,void 0,function*(){var o;null==n||n.preventDefault(),null==n||n.stopPropagation(),i(!0);try{const l="function"==typeof e.text?yield e.text():e.text;K()(l||V(t,!0).join("")||"",s),i(!1),r(!0),c(),a.current=setTimeout(()=>{r(!1)},3e3),null===(o=e.onCopy)||void 0===o||o.call(e,n)}catch(l){throw i(!1),l}}))}};function Y(e,t){return o.useMemo(()=>{const n=!!e;return[n,Object.assign(Object.assign({},t),n&&"object"==typeof e?e:null)]},[e])}var Q=e=>{const t=(0,o.useRef)(void 0);return(0,o.useEffect)(()=>{t.current=e}),t.current};var _=(e,t,n)=>(0,o.useMemo)(()=>!0===e?{title:null!=t?t:n}:(0,o.isValidElement)(e)?{title:e}:"object"==typeof e?Object.assign({title:null!=t?t:n},e):{title:e},[e,t,n]),G=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const J=o.forwardRef((e,t)=>{const{prefixCls:n,component:r="article",className:l,rootClassName:i,setContentRef:a,children:c,direction:s,style:u}=e,p=G(e,["prefixCls","component","className","rootClassName","setContentRef","children","direction","style"]),{getPrefixCls:f,direction:m,className:g,style:b}=(0,O.TP)("typography"),v=null!=s?s:m,h=a?(0,y.K4)(t,a):t,x=f("typography",n);const[w,E,S]=N(x),C=d()(x,g,{[`${x}-rtl`]:"rtl"===v},l,i,E,S),j=Object.assign(Object.assign({},b),u);return w(o.createElement(r,Object.assign({className:C,style:j,ref:h},p),c))});var Z=J,ee=n(6067),te={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.2c3.5 1.3 7.2 2 11 2H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM664 888H414V746c0-22.1-17.9-40-40-40H232V264h432v624z"}}]},name:"copy",theme:"outlined"},ne=function(e,t){return o.createElement(a.A,(0,l.A)({},e,{ref:t,icon:te}))};var oe=o.forwardRef(ne),re=n(3567);function le(e){return!1===e?[!1,!1]:Array.isArray(e)?e:[e]}function ie(e,t,n){return!0===e||void 0===e?t:e||n&&t}const ae=e=>["string","number"].includes(typeof e);var ce=({prefixCls:e,copied:t,locale:n,iconOnly:r,tooltips:l,icon:i,tabIndex:a,onCopy:c,loading:s})=>{const u=le(l),p=le(i),{copied:f,copy:m}=null!=n?n:{},g=t?f:m,b=ie(u[t?1:0],g),y="string"==typeof b?b:g;return o.createElement(E.A,{title:b},o.createElement("button",{type:"button",className:d()(`${e}-copy`,{[`${e}-copy-success`]:t,[`${e}-copy-icon-only`]:r}),onClick:c,"aria-label":y,tabIndex:a},t?ie(p[1],o.createElement(ee.A,null),!0):ie(p[0],s?o.createElement(re.A,null):o.createElement(oe,null),!0)))};const se=o.forwardRef(({style:e,children:t},n)=>{const r=o.useRef(null);return o.useImperativeHandle(n,()=>({isExceed:()=>{const e=r.current;return e.scrollHeight>e.clientHeight},getHeight:()=>r.current.clientHeight})),o.createElement("span",{"aria-hidden":!0,ref:r,style:Object.assign({position:"fixed",display:"block",left:0,top:0,pointerEvents:"none",backgroundColor:"rgba(255, 0, 0, 0.65)"},e)},t)});function ue(e,t){let n=0;const o=[];for(let r=0;r<e.length;r+=1){if(n===t)return o;const l=e[r],i=n+(ae(l)?String(l).length:1);if(i>t){const e=t-n;return o.push(String(l).slice(0,e)),o}o.push(l),n=i}return e}const de=0,pe=4,fe={display:"-webkit-box",overflow:"hidden",WebkitBoxOrient:"vertical"};function me(e){const{enableMeasure:t,width:n,text:l,children:i,rows:a,expanded:c,miscDeps:s,onEllipsis:u}=e,d=o.useMemo(()=>(0,f.A)(l),[l]),p=o.useMemo(()=>(e=>e.reduce((e,t)=>e+(ae(t)?String(t).length:1),0))(d),[l]),g=o.useMemo(()=>i(d,!1),[l]),[b,y]=o.useState(null),v=o.useRef(null),h=o.useRef(null),x=o.useRef(null),O=o.useRef(null),w=o.useRef(null),[E,S]=o.useState(!1),[C,j]=o.useState(de),[k,A]=o.useState(0),[R,$]=o.useState(null);(0,m.A)(()=>{j(t&&n&&p?1:de)},[n,l,a,t,d]),(0,m.A)(()=>{var e,t,n,o;if(1===C){j(2);const e=h.current&&getComputedStyle(h.current).whiteSpace;$(e)}else if(2===C){const r=!!(null===(e=x.current)||void 0===e?void 0:e.isExceed());j(r?3:pe),y(r?[0,p]:null),S(r);const l=(null===(t=x.current)||void 0===t?void 0:t.getHeight())||0,i=1===a?0:(null===(n=O.current)||void 0===n?void 0:n.getHeight())||0,c=(null===(o=w.current)||void 0===o?void 0:o.getHeight())||0,s=Math.max(l,i+c);A(s+1),u(r)}},[C]);const T=b?Math.ceil((b[0]+b[1])/2):0;(0,m.A)(()=>{var e;const[t,n]=b||[0,0];if(t!==n){const o=((null===(e=v.current)||void 0===e?void 0:e.getHeight())||0)>k;let r=T;n-t===1&&(r=o?t:n),y(o?[t,r]:[r,n])}},[b,T]);const I=o.useMemo(()=>{if(!t)return i(d,!1);if(3!==C||!b||b[0]!==b[1]){const e=i(d,!1);return[pe,de].includes(C)?e:o.createElement("span",{style:Object.assign(Object.assign({},fe),{WebkitLineClamp:a})},e)}return i(c?d:ue(d,b[0]),E)},[c,C,b,d].concat((0,r.A)(s))),M={width:n,margin:0,padding:0,whiteSpace:"nowrap"===R?"normal":"inherit"};return o.createElement(o.Fragment,null,I,2===C&&o.createElement(o.Fragment,null,o.createElement(se,{style:Object.assign(Object.assign(Object.assign({},M),fe),{WebkitLineClamp:a}),ref:x},g),o.createElement(se,{style:Object.assign(Object.assign(Object.assign({},M),fe),{WebkitLineClamp:a-1}),ref:O},g),o.createElement(se,{style:Object.assign(Object.assign(Object.assign({},M),fe),{WebkitLineClamp:1}),ref:w},i([],!0))),3===C&&b&&b[0]!==b[1]&&o.createElement(se,{style:Object.assign(Object.assign({},M),{top:400}),ref:v},i(ue(d,T),!0)),1===C&&o.createElement("span",{style:{whiteSpace:"inherit"},ref:h}))}var ge=({enableEllipsis:e,isEllipsis:t,children:n,tooltipProps:r})=>(null==r?void 0:r.title)&&e?o.createElement(E.A,Object.assign({open:!!t&&void 0},r),n):n,be=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const ye=["delete","mark","code","underline","strong","keyboard","italic"],ve=o.forwardRef((e,t)=>{var n;const{prefixCls:l,className:i,style:a,type:c,disabled:u,children:v,ellipsis:h,editable:S,copyable:C,component:j,title:k}=e,A=be(e,["prefixCls","className","style","type","disabled","children","ellipsis","editable","copyable","component","title"]),{getPrefixCls:R,direction:$}=o.useContext(O.QO),[T]=(0,w.A)("Text"),I=o.useRef(null),M=o.useRef(null),D=R("typography",l),z=(0,b.A)(A,ye),[H,P]=Y(S),[B,L]=(0,g.A)(!1,{value:P.editing}),{triggerType:N=["icon"]}=P,F=e=>{var t;e&&(null===(t=P.onStart)||void 0===t||t.call(P)),L(e)},K=Q(B);(0,m.A)(()=>{var e;!B&&K&&(null===(e=M.current)||void 0===e||e.focus())},[B]);const U=e=>{null==e||e.preventDefault(),F(!0)},V=e=>{var t;null===(t=P.onChange)||void 0===t||t.call(P,e),F(!1)},q=()=>{var e;null===(e=P.onCancel)||void 0===e||e.call(P),F(!1)},[G,J]=Y(C),{copied:ee,copyLoading:te,onClick:ne}=X({copyConfig:J,children:v}),[oe,re]=o.useState(!1),[le,ie]=o.useState(!1),[se,ue]=o.useState(!1),[de,pe]=o.useState(!1),[fe,ve]=o.useState(!0),[he,xe]=Y(h,{expandable:!1,symbol:e=>e?null==T?void 0:T.collapse:null==T?void 0:T.expand}),[Oe,we]=(0,g.A)(xe.defaultExpanded||!1,{value:xe.expanded}),Ee=he&&(!Oe||"collapsible"===xe.expandable),{rows:Se=1}=xe,Ce=o.useMemo(()=>Ee&&(void 0!==xe.suffix||xe.onEllipsis||xe.expandable||H||G),[Ee,xe,H,G]);(0,m.A)(()=>{he&&!Ce&&(re(x("webkitLineClamp")),ie(x("textOverflow")))},[Ce,he]);const[je,ke]=o.useState(Ee),Ae=o.useMemo(()=>!Ce&&(1===Se?le:oe),[Ce,le,oe]);(0,m.A)(()=>{ke(Ae&&Ee)},[Ae,Ee]);const Re=Ee&&(je?de:se),$e=Ee&&1===Se&&je,Te=Ee&&Se>1&&je,[Ie,Me]=o.useState(0),De=e=>{var t;ue(e),se!==e&&(null===(t=xe.onEllipsis)||void 0===t||t.call(xe,e))};o.useEffect(()=>{const e=I.current;if(he&&je&&e){const t=function(e){const t=document.createElement("em");e.appendChild(t);const n=e.getBoundingClientRect(),o=t.getBoundingClientRect();return e.removeChild(t),n.left>o.left||o.right>n.right||n.top>o.top||o.bottom>n.bottom}(e);de!==t&&pe(t)}},[he,je,v,Te,fe,Ie]),o.useEffect(()=>{const e=I.current;if("undefined"==typeof IntersectionObserver||!e||!je||!Ee)return;const t=new IntersectionObserver(()=>{ve(!!e.offsetParent)});return t.observe(e),()=>{t.disconnect()}},[je,Ee]);const ze=_(xe.tooltip,P.text,v),He=o.useMemo(()=>{if(he&&!je)return[P.text,v,k,ze.title].find(ae)},[he,je,k,ze.title,Re]);if(B)return o.createElement(W,{value:null!==(n=P.text)&&void 0!==n?n:"string"==typeof v?v:"",onSave:V,onCancel:q,onEnd:P.onEnd,prefixCls:D,className:i,style:a,direction:$,component:j,maxLength:P.maxLength,autoSize:P.autoSize,enterIcon:P.enterIcon});const Pe=()=>{const{expandable:e,symbol:t}=xe;return e?o.createElement("button",{type:"button",key:"expand",className:`${D}-${Oe?"collapse":"expand"}`,onClick:e=>((e,t)=>{var n;we(t.expanded),null===(n=xe.onExpand)||void 0===n||n.call(xe,e,t)})(e,{expanded:!Oe}),"aria-label":Oe?T.collapse:null==T?void 0:T.expand},"function"==typeof t?t(Oe):t):null},Be=()=>{if(!H)return;const{icon:e,tooltip:t,tabIndex:n}=P,r=(0,f.A)(t)[0]||(null==T?void 0:T.edit),l="string"==typeof r?r:"";return N.includes("icon")?o.createElement(E.A,{key:"edit",title:!1===t?"":r},o.createElement("button",{type:"button",ref:M,className:`${D}-edit`,onClick:U,"aria-label":l,tabIndex:n},e||o.createElement(s,{role:"button"}))):null},Le=e=>[e&&Pe(),Be(),G?o.createElement(ce,Object.assign({key:"copy"},J,{prefixCls:D,copied:ee,locale:T,onCopy:ne,loading:te,iconOnly:null==v})):null];return o.createElement(p.A,{onResize:({offsetWidth:e})=>{Me(e)},disabled:!Ee},n=>o.createElement(ge,{tooltipProps:ze,enableEllipsis:Ee,isEllipsis:Re},o.createElement(Z,Object.assign({className:d()({[`${D}-${c}`]:c,[`${D}-disabled`]:u,[`${D}-ellipsis`]:he,[`${D}-ellipsis-single-line`]:$e,[`${D}-ellipsis-multiple-line`]:Te},i),prefixCls:l,style:Object.assign(Object.assign({},a),{WebkitLineClamp:Te?Se:void 0}),component:j,ref:(0,y.K4)(n,I,t),direction:$,onClick:N.includes("text")?U:void 0,"aria-label":null==He?void 0:He.toString(),title:k},z),o.createElement(me,{enableMeasure:Ee&&!je,text:v,rows:Se,width:Ie,onEllipsis:De,expanded:Oe,miscDeps:[ee,Oe,te,H,G,T].concat((0,r.A)(ye.map(t=>e[t])))},(t,n)=>function({mark:e,code:t,underline:n,delete:r,strong:l,keyboard:i,italic:a},c){let s=c;function u(e,t){t&&(s=o.createElement(e,{},s))}return u("strong",l),u("u",n),u("del",r),u("code",t),u("mark",e),u("kbd",i),u("i",a),s}(e,o.createElement(o.Fragment,null,t.length>0&&n&&!Oe&&He?o.createElement("span",{key:"show-content","aria-hidden":!0},t):t,(e=>[e&&!Oe&&o.createElement("span",{"aria-hidden":!0,key:"ellipsis"},"..."),xe.suffix,Le(e)])(n)))))))});var he=ve,xe=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};var Oe=o.forwardRef((e,t)=>{var{ellipsis:n,rel:r}=e,l=xe(e,["ellipsis","rel"]);const i=Object.assign(Object.assign({},l),{rel:void 0===r&&"_blank"===l.target?"noopener noreferrer":r});return delete i.navigate,o.createElement(he,Object.assign({},i,{ref:t,ellipsis:!!n,component:"a"}))});var we=o.forwardRef((e,t)=>o.createElement(he,Object.assign({ref:t},e,{component:"div"}))),Ee=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const Se=(e,t)=>{var{ellipsis:n}=e,r=Ee(e,["ellipsis"]);const l=o.useMemo(()=>n&&"object"==typeof n?(0,b.A)(n,["expandable","rows"]):n,[n]);return o.createElement(he,Object.assign({ref:t},r,{ellipsis:l,component:"span"}))};var Ce=o.forwardRef(Se),je=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const ke=[1,2,3,4,5];var Ae=o.forwardRef((e,t)=>{const{level:n=1}=e,r=je(e,["level"]);const l=ke.includes(n)?`h${n}`:"h1";return o.createElement(he,Object.assign({ref:t},r,{component:l}))});const Re=Z;Re.Text=Ce,Re.Link=Oe,Re.Title=Ae,Re.Paragraph=we;var $e=Re},6067:function(e,t,n){"use strict";n.d(t,{A:function(){return c}});var o=n(8168),r=n(6540),l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"}}]},name:"check",theme:"outlined"},i=n(7064),a=function(e,t){return r.createElement(i.A,(0,o.A)({},e,{ref:t,icon:l}))};var c=r.forwardRef(a)},6426:function(e){e.exports=function(){var e=document.getSelection();if(!e.rangeCount)return function(){};for(var t=document.activeElement,n=[],o=0;o<e.rangeCount;o++)n.push(e.getRangeAt(o));switch(t.tagName.toUpperCase()){case"INPUT":case"TEXTAREA":t.blur();break;default:t=null}return e.removeAllRanges(),function(){"Caret"===e.type&&e.removeAllRanges(),e.rangeCount||n.forEach(function(t){e.addRange(t)}),t&&t.focus()}}},7965:function(e,t,n){"use strict";var o=n(6426),r={"text/plain":"Text","text/html":"Url",default:"Text"};e.exports=function(e,t){var n,l,i,a,c,s,u=!1;t||(t={}),n=t.debug||!1;try{if(i=o(),a=document.createRange(),c=document.getSelection(),(s=document.createElement("span")).textContent=e,s.ariaHidden="true",s.style.all="unset",s.style.position="fixed",s.style.top=0,s.style.clip="rect(0, 0, 0, 0)",s.style.whiteSpace="pre",s.style.webkitUserSelect="text",s.style.MozUserSelect="text",s.style.msUserSelect="text",s.style.userSelect="text",s.addEventListener("copy",function(o){if(o.stopPropagation(),t.format)if(o.preventDefault(),void 0===o.clipboardData){n&&console.warn("unable to use e.clipboardData"),n&&console.warn("trying IE specific stuff"),window.clipboardData.clearData();var l=r[t.format]||r.default;window.clipboardData.setData(l,e)}else o.clipboardData.clearData(),o.clipboardData.setData(t.format,e);t.onCopy&&(o.preventDefault(),t.onCopy(o.clipboardData))}),document.body.appendChild(s),a.selectNodeContents(s),c.addRange(a),!document.execCommand("copy"))throw new Error("copy command was unsuccessful");u=!0}catch(d){n&&console.error("unable to copy using execCommand: ",d),n&&console.warn("trying IE specific stuff");try{window.clipboardData.setData(t.format||"text",e),t.onCopy&&t.onCopy(window.clipboardData),u=!0}catch(d){n&&console.error("unable to copy using clipboardData: ",d),n&&console.error("falling back to prompt"),l=function(e){var t=(/mac os x/i.test(navigator.userAgent)?"⌘":"Ctrl")+"+C";return e.replace(/#{\s*key\s*}/g,t)}("message"in t?t.message:"Copy to clipboard: #{key}, Enter"),window.prompt(l,e)}}finally{c&&("function"==typeof c.removeRange?c.removeRange(a):c.removeAllRanges()),s&&document.body.removeChild(s),i()}return u}}}]);
//# sourceMappingURL=911de66654c883e5fa7bf2156c1eabe51c084373-cad302fe78d3900833f8.js.map