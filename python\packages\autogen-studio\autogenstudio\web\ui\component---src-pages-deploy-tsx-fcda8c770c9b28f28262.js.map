{"version": 3, "file": "component---src-pages-deploy-tsx-fcda8c770c9b28f28262.js", "mappings": ";oJASA,MAAMA,GAAgB,E,QAAA,GAAiB,gBAAiB,CACtD,CACE,OACA,CACEC,EAAG,2EACHC,IAAK,WAGT,CAAC,OAAQ,CAAED,EAAG,UAAWC,IAAK,WAC9B,CAAC,OAAQ,CAAED,EAAG,aAAcC,IAAK,Y,uDCTnC,MAAMC,GAAa,E,QAAA,GAAiB,aAAc,CAChD,CAAC,OAAQ,CAAEF,EAAG,qDAAsDC,IAAK,WACzE,CAAC,OAAQ,CAAED,EAAG,WAAYC,IAAK,WAC/B,CAAC,OAAQ,CAAED,EAAG,sDAAuDC,IAAK,WAC1E,CAAC,OAAQ,CAAED,EAAG,aAAcC,IAAK,Y,uDCJnC,MAAME,GAAO,E,QAAA,GAAiB,OAAQ,CACpC,CAAC,OAAQ,CAAEC,MAAO,KAAMC,OAAQ,KAAMC,EAAG,IAAKC,EAAG,IAAKC,GAAI,IAAKC,GAAI,IAAKR,IAAK,WAC7E,CAAC,OAAQ,CAAED,EAAG,0DAA2DC,IAAK,Y,gKCSzE,MAAMS,EAA8CC,IAOpD,IAPqD,OAC1DC,EAAM,OACNC,EAAM,aACNC,EAAY,SACZC,EAAQ,cACRC,EAAa,UACbC,GAAY,GACbN,EAEC,OAAKC,EAkBHM,EAAAA,cAAA,OAAKC,UAAU,oCAEbD,EAAAA,cAAA,OAAKC,UAAU,kFACbD,EAAAA,cAAA,OAAKC,UAAU,2BAEbD,EAAAA,cAAA,QAAMC,UAAU,4BAA2B,OAK7CD,EAAAA,cAACE,EAAAA,EAAO,CAACC,MAAM,SACbH,EAAAA,cAAA,UACEI,QAASP,EACTI,UAAU,gKAEVD,EAAAA,cAACK,EAAAA,EAAc,CAACC,YAAa,IAAKL,UAAU,eAMjDF,GACCC,EAAAA,cAAA,OAAKC,UAAU,OACbD,EAAAA,cAAChB,EAAAA,EAAU,CAACiB,UAAU,wCAKxBF,GAA+B,IAAlBJ,EAAOY,QACpBP,EAAAA,cAAA,OAAKC,UAAU,2EACbD,EAAAA,cAACQ,EAAAA,EAAQ,CAACP,UAAU,wCAAwC,YAMhED,EAAAA,cAAA,OAAKC,UAAU,4CACZN,EAAOc,IAAKC,GACXV,EAAAA,cAAA,OAAKjB,IAAK2B,EAAMC,GAAIV,UAAU,YAC5BD,EAAAA,cAAA,OACEC,UAAW,+FAERL,aAAY,EAAZA,EAAce,MAAOD,EAAMC,GAAK,YAAc,iBAGnDX,EAAAA,cAAA,OACEC,UAAW,8EACTL,aAAY,EAAZA,EAAce,MAAOD,EAAMC,GACvB,6BACA,sBAENP,QAASA,IAAMN,EAAcY,IAG7BV,EAAAA,cAAA,OAAKC,UAAU,qCACbD,EAAAA,cAAA,QAAMC,UAAU,oBAAoBS,EAAMP,aAvEpDH,EAAAA,cAAA,OAAKC,UAAU,oCACbD,EAAAA,cAAA,OAAKC,UAAU,aACbD,EAAAA,cAACE,EAAAA,EAAO,CAACC,MAAM,MACbH,EAAAA,cAAA,UACEI,QAASP,EACTI,UAAU,gKAEVD,EAAAA,cAACY,EAAAA,EAAa,CAACN,YAAa,IAAKL,UAAU,iBChC5CY,EAAyB,CACpC,CACEF,GAAI,eACJR,MAAO,SACPW,KAAM,UAER,CACEH,GAAI,eACJR,MAAO,SACPW,KAAM,W,kCCyEV,MAnF8BC,IAE1Bf,EAAAA,cAAA,OAAKC,UAAU,IACbD,EAAAA,cAAA,MAAIC,UAAU,4BAA2B,wCAIzCD,EAAAA,cAACgB,EAAAA,EAAK,CACJf,UAAU,OACVgB,QAAQ,OACRC,YACElB,EAAAA,cAAA,MAAIC,UAAU,iCACZD,EAAAA,cAAA,UAAI,gBAGRc,KAAK,SAGPd,EAAAA,cAAA,OAAKC,UAAU,gBACZ,IAAI,yFACkF,IACvFD,EAAAA,cAACmB,EAAAA,EAAQ,CAAClB,UAAU,yBAA0B,KAIhDD,EAAAA,cAACoB,EAAW,CACVjB,MAAM,+BACNe,YAAY,yCACZG,KAAM,inBAgBNC,OAAQC,IAIVvB,EAAAA,cAAA,OAAKC,UAAU,aAEbD,EAAAA,cAACoB,EAAW,CACVjB,MAAM,oBACNe,YAAY,mDACZG,KAAM,oRAYNC,OAAQC,IAGVvB,EAAAA,cAACoB,EAAW,CACVjB,MAAM,yBACNe,YAAYlB,EAAAA,cAAA,WAAK,mDACkC,KAEnDqB,KAAM,2EAGNC,OAAQC,MChBlB,MA7D8BC,IAE1BxB,EAAAA,cAAA,OAAKC,UAAU,aACbD,EAAAA,cAAA,MAAIC,UAAU,4BAA2B,eAEzCD,EAAAA,cAACgB,EAAAA,EAAK,CACJf,UAAU,OACVgB,QAAQ,OACRC,YACElB,EAAAA,cAAA,MAAIC,UAAU,iCACZD,EAAAA,cAAA,UAAI,kBAGRc,KAAK,SAEPd,EAAAA,cAACoB,EAAW,CACVjB,MAAM,gBACNe,YAAYlB,EAAAA,cAAA,WAAK,gBAEfA,EAAAA,cAAA,KACEyB,KAAK,2FACLC,OAAO,SACPC,IAAI,aACJ1B,UAAU,8BACX,cAEG,0BACoB,KAE1BoB,KAAM,0bAiBNC,OAAQC,IAIVvB,EAAAA,cAACoB,EAAW,CACVjB,MAAM,WACNe,YAAY,qBACZG,KAAM,yEAENC,OAAQC,K,UC/CT,MAAMA,EAAmBK,IAC9BC,UAAUC,UAAUC,UAAUH,IAEnBI,EAA4CvC,IAAgB,IAAf,MAAEiB,GAAOjB,EAEjE,OAAQiB,EAAMC,IACZ,IAAK,eACH,OAAOX,EAAAA,cAACe,EAAW,MAErB,IAAK,eACH,OAAOf,EAAAA,cAACwB,EAAW,MAIrB,QACE,OACExB,EAAAA,cAAA,OAAKC,UAAU,kBAAiB,OAC1BD,EAAAA,cAAA,cAASU,EAAMP,OAAe,gBActC8B,EAAYjC,EAAAA,YAELoB,EAA0Cc,IAAA,IAAC,MACtD/B,EAAK,YACLe,EAAW,KACXG,EAAI,OACJC,EAAM,SACNa,EAAW,UACZD,EAAA,OACClC,EAAAA,cAAA,WAASC,UAAU,gBACjBD,EAAAA,cAAA,MAAIC,UAAU,8BAA8BE,GAC3Ce,GAAelB,EAAAA,cAAA,OAAKC,UAAU,UAAUiB,GACxCG,GACCrB,EAAAA,cAAA,OAAKC,UAAU,uEACbD,EAAAA,cAACoC,EAAAA,EAAY,CAACD,SAAUA,EAAUF,UAAWA,EAAWI,MAAOhB,IAC/DrB,EAAAA,cAAA,UACEI,QAASA,IAAMkB,EAAOD,GACtBpB,UAAU,wEAEVD,EAAAA,cAACf,EAAAA,EAAI,CAACgB,UAAU,0DCuB1B,MA/EuCqC,KACrC,MAAM,EAACvC,EAAU,EAACwC,IAAgBC,EAAAA,EAAAA,WAAS,IACrC,EAAC7C,EAAO,EAAC8C,IAAaD,EAAAA,EAAAA,UAAkB3B,IACxC,EAACjB,EAAa,EAAC8C,IAAmBF,EAAAA,EAAAA,UAAuB,OACzD,EAACG,EAAc,EAACC,IAAoBJ,EAAAA,EAAAA,UAAS,KACjD,GAAsB,oBAAXK,OAAwB,CACjC,MAAMC,EAASC,aAAaC,QAAQ,iBACpC,OAAkB,OAAXF,GAAkBG,KAAKC,MAAMJ,EACtC,CACA,OAAO,IAiBT,OAbAK,EAAAA,EAAAA,WAAU,KACc,oBAAXN,QACTE,aAAaK,QAAQ,gBAAiBH,KAAKI,UAAUV,KAEtD,CAACA,KAGJQ,EAAAA,EAAAA,WAAU,MACHvD,GAAgBD,EAAOY,OAAS,GACnCmC,EAAgB/C,EAAO,KAExB,CAACA,EAAQC,IAGVI,EAAAA,cAAA,OAAKC,UAAU,kCAEbD,EAAAA,cAAA,OACEC,UAAW,0EACT0C,EAAgB,OAAS,SAG3B3C,EAAAA,cAACR,EAAa,CACZE,OAAQiD,EACRhD,OAAQA,EACRC,aAAcA,EACdC,SAAUA,IAAM+C,GAAkBD,GAClC7C,cAAe4C,EACf3C,UAAWA,KAKfC,EAAAA,cAAA,OACEC,UAAW,wDACT0C,EAAgB,QAAU,UAG5B3C,EAAAA,cAAA,OAAKC,UAAU,YAEbD,EAAAA,cAAA,OAAKC,UAAU,wCACbD,EAAAA,cAAA,QAAMC,UAAU,4BAA2B,MAC1CL,GACCI,EAAAA,cAAAA,EAAAA,SAAA,KACEA,EAAAA,cAACsD,EAAAA,EAAY,CAACrD,UAAU,2BACxBD,EAAAA,cAAA,QAAMC,UAAU,kBAAkBL,EAAaO,SAIrDH,EAAAA,cAAA,OAAKC,UAAU,kEACbD,EAAAA,cAACnB,EAAAA,EAAa,CAACoB,UAAU,oDAAqD,IAAI,gBAInFL,EACCI,EAAAA,cAACgC,EAAY,CAACtB,MAAOd,IAErBI,EAAAA,cAAA,OAAKC,UAAU,yEAAwE,oBChDnG,MArBmBR,IAAmB,IAAlB,KAAE8D,GAAW9D,EAC/B,OACEO,EAAAA,cAACwD,EAAAA,EAAM,CAACC,KAAMF,EAAKG,KAAKC,aAAcxD,MAAM,KAAKyD,KAAM,WACrD5D,EAAAA,cAAA,QAAM6D,MAAO,CAAE1E,OAAQ,QAAUc,UAAU,YACzCD,EAAAA,cAACsC,EAAa,Q,uDCDtB,MAAMwB,GAAO,E,QAAA,GAAiB,OAAQ,CACpC,CAAC,SAAU,CAAEC,GAAI,KAAMC,GAAI,KAAMC,EAAG,KAAMlF,IAAK,WAC/C,CAAC,OAAQ,CAAED,EAAG,YAAaC,IAAK,WAChC,CAAC,OAAQ,CAAED,EAAG,YAAaC,IAAK,Y,uDCHlC,MAAMuE,GAAe,E,QAAA,GAAiB,eAAgB,CACpD,CAAC,OAAQ,CAAExE,EAAG,gBAAiBC,IAAK,Y", "sources": ["webpack://autogentstudio/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js", "webpack://autogentstudio/./node_modules/lucide-react/dist/esm/icons/refresh-ccw.js", "webpack://autogentstudio/./node_modules/lucide-react/dist/esm/icons/copy.js", "webpack://autogentstudio/./src/components/views/deploy/sidebar.tsx", "webpack://autogentstudio/./src/components/views/deploy/types.tsx", "webpack://autogentstudio/./src/components/views/deploy/guides/python.tsx", "webpack://autogentstudio/./src/components/views/deploy/guides/docker.tsx", "webpack://autogentstudio/./src/components/views/deploy/guides/guides.tsx", "webpack://autogentstudio/./src/components/views/deploy/manager.tsx", "webpack://autogentstudio/./src/pages/deploy.tsx", "webpack://autogentstudio/./node_modules/lucide-react/dist/esm/icons/info.js", "webpack://autogentstudio/./node_modules/lucide-react/dist/esm/icons/chevron-right.js"], "sourcesContent": ["/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst TriangleAlert = createLucideIcon(\"TriangleAlert\", [\n  [\n    \"path\",\n    {\n      d: \"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3\",\n      key: \"wmoenq\"\n    }\n  ],\n  [\"path\", { d: \"M12 9v4\", key: \"juzpu7\" }],\n  [\"path\", { d: \"M12 17h.01\", key: \"p32p05\" }]\n]);\n\nexport { TriangleAlert as default };\n//# sourceMappingURL=triangle-alert.js.map\n", "/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst RefreshCcw = createLucideIcon(\"RefreshCcw\", [\n  [\"path\", { d: \"M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8\", key: \"14sxne\" }],\n  [\"path\", { d: \"M3 3v5h5\", key: \"1xhq8a\" }],\n  [\"path\", { d: \"M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16\", key: \"1hlbsb\" }],\n  [\"path\", { d: \"M16 16h5v5\", key: \"ccwih5\" }]\n]);\n\nexport { RefreshCcw as default };\n//# sourceMappingURL=refresh-ccw.js.map\n", "/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst Copy = createLucideIcon(\"Copy\", [\n  [\"rect\", { width: \"14\", height: \"14\", x: \"8\", y: \"8\", rx: \"2\", ry: \"2\", key: \"17jyea\" }],\n  [\"path\", { d: \"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2\", key: \"zix9uf\" }]\n]);\n\nexport { Copy as default };\n//# sourceMappingURL=copy.js.map\n", "import React from \"react\";\r\nimport { <PERSON><PERSON>, Tooltip } from \"antd\";\r\nimport {\r\n  PanelLeftClose,\r\n  PanelLeftOpen,\r\n  Book,\r\n  InfoIcon,\r\n  RefreshCcw,\r\n} from \"lucide-react\";\r\nimport type { Guide } from \"./types\";\r\n\r\ninterface DeploySidebarProps {\r\n  isOpen: boolean;\r\n  guides: Guide[];\r\n  currentGuide: Guide | null;\r\n  onToggle: () => void;\r\n  onSelectGuide: (guide: Guide) => void;\r\n  isLoading?: boolean;\r\n}\r\n\r\nexport const DeploySidebar: React.FC<DeploySidebarProps> = ({\r\n  isOpen,\r\n  guides,\r\n  currentGuide,\r\n  onToggle,\r\n  onSelectGuide,\r\n  isLoading = false,\r\n}) => {\r\n  // Render collapsed state\r\n  if (!isOpen) {\r\n    return (\r\n      <div className=\"h-full border-r border-secondary\">\r\n        <div className=\"p-2 -ml-2\">\r\n          <Tooltip title=\"文档\">\r\n            <button\r\n              onClick={onToggle}\r\n              className=\"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50\"\r\n            >\r\n              <PanelLeftOpen strokeWidth={1.5} className=\"h-6 w-6\" />\r\n            </button>\r\n          </Tooltip>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"h-full border-r border-secondary\">\r\n      {/* Header */}\r\n      <div className=\"flex items-center justify-between pt-0 p-4 pl-2 pr-2 border-b border-secondary\">\r\n        <div className=\"flex items-center gap-2\">\r\n          {/* <Book className=\"w-4 h-4\" /> */}\r\n          <span className=\"text-primary font-medium\">指南</span>\r\n          {/* <span className=\"px-2 py-0.5 text-xs bg-accent/10 text-accent rounded\">\r\n            {guides.length}\r\n          </span> */}\r\n        </div>\r\n        <Tooltip title=\"关闭侧边栏\">\r\n          <button\r\n            onClick={onToggle}\r\n            className=\"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50\"\r\n          >\r\n            <PanelLeftClose strokeWidth={1.5} className=\"h-6 w-6\" />\r\n          </button>\r\n        </Tooltip>\r\n      </div>\r\n\r\n      {/* Loading State */}\r\n      {isLoading && (\r\n        <div className=\"p-4\">\r\n          <RefreshCcw className=\"w-4 h-4 inline-block animate-spin\" />\r\n        </div>\r\n      )}\r\n\r\n      {/* Empty State */}\r\n      {!isLoading && guides.length === 0 && (\r\n        <div className=\"p-2 m-2 text-center text-secondary text-sm border border-dashed rounded\">\r\n          <InfoIcon className=\"w-4 h-4 inline-block mr-1.5 -mt-0.5\" />\r\n          无可用的部署指南\r\n        </div>\r\n      )}\r\n\r\n      {/* Guides List */}\r\n      <div className=\"overflow-y-auto h-[calc(100%-64px)] mt-4\">\r\n        {guides.map((guide) => (\r\n          <div key={guide.id} className=\"relative\">\r\n            <div\r\n              className={`absolute top-1 left-0.5 z-50 h-[calc(100%-8px)]\r\n               w-1 bg-opacity-80 rounded ${\r\n                 currentGuide?.id === guide.id ? \"bg-accent\" : \"bg-tertiary\"\r\n               }`}\r\n            />\r\n            <div\r\n              className={`group ml-1 flex flex-col p-2 rounded-l cursor-pointer hover:bg-secondary ${\r\n                currentGuide?.id === guide.id\r\n                  ? \"border-accent bg-secondary\"\r\n                  : \"border-transparent\"\r\n              }`}\r\n              onClick={() => onSelectGuide(guide)}\r\n            >\r\n              {/* Guide Title */}\r\n              <div className=\"flex items-center justify-between\">\r\n                <span className=\"text-sm truncate\">{guide.title}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n", "export interface Guide {\r\n  id: string;\r\n  title: string;\r\n  type: \"python\" | \"docker\" | \"cloud\";\r\n}\r\n\r\nexport const defaultGuides: Guide[] = [\r\n  {\r\n    id: \"python-setup\",\r\n    title: \"Python\",\r\n    type: \"python\",\r\n  },\r\n  {\r\n    id: \"docker-setup\",\r\n    title: \"Docker\",\r\n    type: \"docker\",\r\n  },\r\n  // {\r\n  //   id: \"cloud-deploy\",\r\n  //   title: \"Cloud\",\r\n  //   type: \"cloud\",\r\n  // },\r\n];\r\n", "import React from \"react\";\r\nimport { Alert } from \"antd\";\r\nimport { CodeSection, copyToClipboard } from \"./guides\";\r\nimport { Download } from \"lucide-react\";\r\n\r\nconst PythonGuide: React.FC = () => {\r\n  return (\r\n    <div className=\"\">\r\n      <h1 className=\"tdext-2xl font-bold mb-6\">\r\n        在 Python 代码和 REST API 中使用 多智能体工作室 团队\r\n      </h1>\r\n\r\n      <Alert\r\n        className=\"mb-6\"\r\n        message=\"前置条件\"\r\n        description={\r\n          <ul className=\"list-disc pl-4 mt-2 space-y-1\">\r\n            <li>已安装 多智能体工作室</li>\r\n          </ul>\r\n        }\r\n        type=\"info\"\r\n      />\r\n\r\n      <div className=\"my-3 text-sm\">\r\n        {\" \"}\r\n        您可以通过使用 TeamManager 类在您的 Python 应用程序中重用在 多智能体工作室 中创建的代理团队的声明性规范。在团队构建器中，选择一个团队配置并点击下载。{\" \"}\r\n        <Download className=\"h-4 w-4 inline-block\" />{\" \"}\r\n      </div>\r\n\r\n      {/* Basic Usage */}\r\n      <CodeSection\r\n        title=\"1. 在 Python 中构建您的团队，导出为 JSON\"\r\n        description=\"这是一个在 Python 中构建代理团队并将其导出为 JSON 文件的示例。\"\r\n        code={`\r\nfrom autogen_agentchat.agents import AssistantAgent\r\nfrom autogen_agentchat.teams import RoundRobinGroupChat\r\nfrom autogen_agentchat.ui import Console\r\nfrom autogen_ext.models.openai import OpenAIChatCompletionClient\r\nfrom autogen_agentchat.conditions import  TextMentionTermination\r\n \r\nagent = AssistantAgent(\r\n        name=\"weather_agent\",\r\n        model_client=OpenAIChatCompletionClient(\r\n            model=\"gpt-4o-mini\", \r\n        ), \r\n    ) \r\nagent_team = RoundRobinGroupChat([agent], termination_condition=TextMentionTermination(\"TERMINATE\"))\r\nconfig = agent_team.dump_component()\r\nprint(config.model_dump_json())`}\r\n        onCopy={copyToClipboard}\r\n      />\r\n\r\n      {/* Installation Steps */}\r\n      <div className=\"space-y-6\">\r\n        {/* Basic Usage */}\r\n        <CodeSection\r\n          title=\"2. 在 Python 中运行团队\"\r\n          description=\"这是在您的 Python 代码中使用 多智能体工作室 的 TeamManager 类的简单示例。\"\r\n          code={`\r\nfrom autogenstudio.teammanager import TeamManager\r\n\r\n# Initialize the TeamManager\r\nmanager = TeamManager()\r\n\r\n# Run a task with a specific team configuration\r\nresult = await manager.run(\r\ntask=\"What is the weather in New York?\",\r\nteam_config=\"team.json\"\r\n)\r\nprint(result)`}\r\n          onCopy={copyToClipboard}\r\n        />\r\n\r\n        <CodeSection\r\n          title=\"3. 将团队作为 REST API 提供服务\"\r\n          description=<div>\r\n            多智能体工作室 提供了一个便捷的 CLI 命令，可以将团队作为 REST API 端点提供服务。{\" \"}\r\n          </div>\r\n          code={`\r\nautogenstudio serve --team path/to/team.json --port 8084  \r\n          `}\r\n          onCopy={copyToClipboard}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PythonGuide;\r\n", "import React from \"react\";\r\nimport { Alert } from \"antd\";\r\nimport { CodeSection, copyToClipboard } from \"./guides\";\r\n\r\nconst DockerGuide: React.FC = () => {\r\n  return (\r\n    <div className=\"max-w-4xl\">\r\n      <h1 className=\"tdext-2xl font-bold mb-6\">Docker 容器设置</h1>\r\n\r\n      <Alert\r\n        className=\"mb-6\"\r\n        message=\"前置条件\"\r\n        description={\r\n          <ul className=\"list-disc pl-4 mt-2 space-y-1\">\r\n            <li>系统上已安装 Docker</li>\r\n          </ul>\r\n        }\r\n        type=\"info\"\r\n      />\r\n      <CodeSection\r\n        title=\"1. Dockerfile\"\r\n        description=<div>\r\n          多智能体工作室 提供了一个\r\n          <a\r\n            href=\"https://github.com/microsoft/autogen/blob/main/python/packages/autogen-studio/Dockerfile\"\r\n            target=\"_blank\"\r\n            rel=\"noreferrer\"\r\n            className=\"text-accent underline px-1\"\r\n          >\r\n            Dockerfile\r\n          </a>\r\n          ，您可以使用它来构建您的 Docker 容器。{\" \"}\r\n        </div>\r\n        code={`FROM python:3.10-slim\r\n\r\nWORKDIR /code\r\n\r\nRUN pip install -U gunicorn autogenstudio\r\n\r\nRUN useradd -m -u 1000 user\r\nUSER user\r\nENV HOME=/home/<USER>\n    PATH=/home/<USER>/.local/bin:$PATH \r\n    AUTOGENSTUDIO_APPDIR=/home/<USER>/app\r\n\r\nWORKDIR $HOME/app\r\n\r\nCOPY --chown=user . $HOME/app\r\n\r\nCMD gunicorn -w $((2 * $(getconf _NPROCESSORS_ONLN) + 1)) --timeout 12600 -k uvicorn.workers.UvicornWorker autogenstudio.web.app:app --bind \"0.0.0.0:8081\"`}\r\n        onCopy={copyToClipboard}\r\n      />\r\n\r\n      {/* Build and Run */}\r\n      <CodeSection\r\n        title=\"2. 构建和运行\"\r\n        description=\"构建并运行您的 Docker 容器：\"\r\n        code={`docker build -t autogenstudio .\r\ndocker run -p 8081:8081 autogenstudio`}\r\n        onCopy={copyToClipboard}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DockerGuide;\r\n", "import React from \"react\";\r\nimport { Copy } from \"lucide-react\";\r\nimport { Guide } from \"../types\";\r\nimport PythonGuide from \"./python\";\r\nimport DockerGuide from \"./docker\";\r\n\r\nimport { MonacoEditor } from \"../../monaco\";\r\n\r\ninterface GuideContentProps {\r\n  guide: Guide;\r\n}\r\n\r\nexport const copyToClipboard = (text: string) => {\r\n  navigator.clipboard.writeText(text);\r\n};\r\nexport const GuideContent: React.FC<GuideContentProps> = ({ guide }) => {\r\n  // Render different content based on guide type and id\r\n  switch (guide.id) {\r\n    case \"python-setup\":\r\n      return <PythonGuide />;\r\n\r\n    case \"docker-setup\":\r\n      return <DockerGuide />;\r\n\r\n    // Add more cases for other guides...\r\n\r\n    default:\r\n      return (\r\n        <div className=\"text-secondary\">\r\n          标题为 <strong>{guide.title}</strong> 的指南正在开发中！\r\n        </div>\r\n      );\r\n  }\r\n};\r\n\r\ninterface CodeSectionProps {\r\n  title: string;\r\n  description?: string | React.ReactNode;\r\n  code?: string;\r\n  onCopy: (text: string) => void;\r\n  language?: string;\r\n}\r\n\r\nconst editorRef = React.createRef<any>();\r\n\r\nexport const CodeSection: React.FC<CodeSectionProps> = ({\r\n  title,\r\n  description,\r\n  code,\r\n  onCopy,\r\n  language = \"python\",\r\n}) => (\r\n  <section className=\"mt-6 bg-seco\">\r\n    <h2 className=\"text-md font-semibold mb-3\">{title}</h2>\r\n    {description && <div className=\"  mb-3\">{description}</div>}\r\n    {code && (\r\n      <div className=\"relative bg-secondary text-sm p-4 rounded overflow-auto scroll h-72\">\r\n        <MonacoEditor language={language} editorRef={editorRef} value={code} />\r\n        <button\r\n          onClick={() => onCopy(code)}\r\n          className=\"absolute right-2 top-2 p-2  bg-secondary hover:bg-primary rounded-md\"\r\n        >\r\n          <Copy className=\"w-4 h-4 hover:text-accent transition duration-100\" />\r\n        </button>\r\n      </div>\r\n    )}\r\n  </section>\r\n);\r\n\r\nexport default GuideContent;\r\n", "import React, { useState, useEffect } from \"react\";\r\nimport { <PERSON><PERSON>ronR<PERSON>, TriangleAlert } from \"lucide-react\";\r\nimport { DeploySidebar } from \"./sidebar\";\r\nimport { Guide, defaultGuides } from \"./types\";\r\nimport { GuideContent } from \"./guides/guides\";\r\n\r\nexport const DeployManager: React.FC = () => {\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [guides, setGuides] = useState<Guide[]>(defaultGuides);\r\n  const [currentGuide, setCurrentGuide] = useState<Guide | null>(null);\r\n  const [isSidebarOpen, setIsSidebarOpen] = useState(() => {\r\n    if (typeof window !== \"undefined\") {\r\n      const stored = localStorage.getItem(\"deploySidebar\");\r\n      return stored !== null ? JSON.parse(stored) : true;\r\n    }\r\n    return true;\r\n  });\r\n\r\n  // Persist sidebar state\r\n  useEffect(() => {\r\n    if (typeof window !== \"undefined\") {\r\n      localStorage.setItem(\"deploySidebar\", JSON.stringify(isSidebarOpen));\r\n    }\r\n  }, [isSidebarOpen]);\r\n\r\n  // Set first guide as current if none selected\r\n  useEffect(() => {\r\n    if (!currentGuide && guides.length > 0) {\r\n      setCurrentGuide(guides[0]);\r\n    }\r\n  }, [guides, currentGuide]);\r\n\r\n  return (\r\n    <div className=\"relative    flex h-full w-full\">\r\n      {/* Sidebar */}\r\n      <div\r\n        className={`absolute  left-0 top-0 h-full transition-all duration-200 ease-in-out ${\r\n          isSidebarOpen ? \"w-64\" : \"w-12\"\r\n        }`}\r\n      >\r\n        <DeploySidebar\r\n          isOpen={isSidebarOpen}\r\n          guides={guides}\r\n          currentGuide={currentGuide}\r\n          onToggle={() => setIsSidebarOpen(!isSidebarOpen)}\r\n          onSelectGuide={setCurrentGuide}\r\n          isLoading={isLoading}\r\n        />\r\n      </div>\r\n\r\n      {/* Main Content */}\r\n      <div\r\n        className={`flex-1 transition-all max-w-5xl  -mr-6 duration-200 ${\r\n          isSidebarOpen ? \"ml-64\" : \"ml-12\"\r\n        }`}\r\n      >\r\n        <div className=\"p-4 pt-2\">\r\n          {/* Breadcrumb */}\r\n          <div className=\"flex items-center gap-2 mb-4 text-sm\">\r\n            <span className=\"text-primary font-medium\">部署</span>\r\n            {currentGuide && (\r\n              <>\r\n                <ChevronRight className=\"w-4 h-4 text-secondary\" />\r\n                <span className=\"text-secondary\">{currentGuide.title}</span>\r\n              </>\r\n            )}\r\n          </div>\r\n          <div className=\"rounded border border-secondary border-dashed p-2 text-sm mb-4\">\r\n            <TriangleAlert className=\"w-4 h-4 inline-block mr-2 -mt-1 text-secondary \" />{\" \"}\r\n            部署指南部分正在开发中。\r\n          </div>\r\n          {/* Content Area */}\r\n          {currentGuide ? (\r\n            <GuideContent guide={currentGuide} />\r\n          ) : (\r\n            <div className=\"flex items-center justify-center h-[calc(100vh-190px)] text-secondary\">\r\n              从侧边栏选择一个指南开始\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DeployManager;\r\n", "import * as React from \"react\";\r\nimport Layout from \"../components/layout\";\r\nimport { graphql } from \"gatsby\";\r\nimport DeployManager from \"../components/views/deploy/manager\";\r\n\r\n// markup\r\nconst DeployPage = ({ data }: any) => {\r\n  return (\r\n    <Layout meta={data.site.siteMetadata} title=\"部署\" link={\"/deploy\"}>\r\n      <main style={{ height: \"100%\" }} className=\" h-full \">\r\n        <DeployManager />\r\n      </main>\r\n    </Layout>\r\n  );\r\n};\r\n\r\nexport const query = graphql`\r\n  query HomePageQuery {\r\n    site {\r\n      siteMetadata {\r\n        description\r\n        title\r\n      }\r\n    }\r\n  }\r\n`;\r\n\r\nexport default DeployPage;\r\n", "/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst Info = createLucideIcon(\"Info\", [\n  [\"circle\", { cx: \"12\", cy: \"12\", r: \"10\", key: \"1mglay\" }],\n  [\"path\", { d: \"M12 16v-4\", key: \"1dtifu\" }],\n  [\"path\", { d: \"M12 8h.01\", key: \"e9boi3\" }]\n]);\n\nexport { Info as default };\n//# sourceMappingURL=info.js.map\n", "/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst ChevronRight = createLucideIcon(\"ChevronRight\", [\n  [\"path\", { d: \"m9 18 6-6-6-6\", key: \"mthhwq\" }]\n]);\n\nexport { ChevronRight as default };\n//# sourceMappingURL=chevron-right.js.map\n"], "names": ["Triangle<PERSON><PERSON><PERSON>", "d", "key", "RefreshCcw", "Copy", "width", "height", "x", "y", "rx", "ry", "DeploySidebar", "_ref", "isOpen", "guides", "currentGuide", "onToggle", "onSelectGuide", "isLoading", "React", "className", "<PERSON><PERSON><PERSON>", "title", "onClick", "PanelLeftClose", "strokeWidth", "length", "InfoIcon", "map", "guide", "id", "PanelLeftOpen", "defaultGuides", "type", "PythonGuide", "<PERSON><PERSON>", "message", "description", "Download", "CodeSection", "code", "onCopy", "copyToClipboard", "DockerGuide", "href", "target", "rel", "text", "navigator", "clipboard", "writeText", "GuideContent", "editor<PERSON><PERSON>", "_ref2", "language", "MonacoEditor", "value", "DeployManager", "setIsLoading", "useState", "setGuides", "setCurrentGuide", "isSidebarOpen", "setIsSidebarOpen", "window", "stored", "localStorage", "getItem", "JSON", "parse", "useEffect", "setItem", "stringify", "ChevronRight", "data", "Layout", "meta", "site", "siteMetadata", "link", "style", "Info", "cx", "cy", "r"], "sourceRoot": ""}