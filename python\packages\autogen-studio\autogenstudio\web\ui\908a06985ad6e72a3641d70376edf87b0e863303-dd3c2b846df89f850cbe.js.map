{"version": 3, "file": "908a06985ad6e72a3641d70376edf87b0e863303-dd3c2b846df89f850cbe.js", "mappings": "kSAYO,MAAMA,EAA8CC,IAMpD,IANqD,QAC1DC,EAAO,OACPC,EAAM,SACNC,EAAQ,OACRC,EAAM,MACNC,GACDL,EACC,MAAOM,GAAQC,EAAAA,EAAKC,WACd,EAACC,EAAQ,EAACC,IAAcC,EAAAA,EAAAA,WAAS,IAChCC,EAAYC,GAAiBC,EAAAA,GAAQC,cAG5CC,EAAAA,EAAAA,WAAU,KACJZ,EACFE,EAAKW,eAAe,CAClBC,MAAMjB,aAAO,EAAPA,EAASiB,OAAQ,GACvBC,SAASlB,aAAO,EAAPA,EAASkB,eAAWC,IAG/Bd,EAAKe,eAEN,CAACf,EAAML,EAASG,IAEnB,MAuBMkB,GAAcb,GAA4B,IAAjBJ,EAAMkB,OAErC,OACEC,EAAAA,cAACC,EAAAA,EAAK,CACJC,MAAOzB,EAAU,eAAiB,iBAClC0B,KAAMvB,EACND,SAAUA,EACVyB,OAAQ,KACRC,UAAU,eACVC,aAAW,GAEVjB,EACDW,EAAAA,cAACjB,EAAAA,EAAI,CACHD,KAAMA,EACNY,KAAK,eACLa,OAAO,WACPC,SAvC6CC,UACjD,UACQ/B,EAAO,IACRgC,EACHC,GAAIlC,aAAO,EAAPA,EAASkC,KAEfvB,EAAWwB,QACT,WAAWnC,EAAU,UAAY,yBAErC,CAAE,MAAOoC,GACHA,aAAiBC,OACnB1B,EAAWyB,MAAMA,EAAMvB,QAE3B,GA2BIyB,eAvBJC,IAEA5B,EAAWyB,MAAM,oCACjBI,QAAQJ,MAAM,0BAA2BG,IAqBrCE,aAAa,OAEblB,EAAAA,cAACjB,EAAAA,EAAKoC,KAAI,CACRC,MAAM,eACN1B,KAAK,OACL2B,MAAO,CACL,CAAEC,UAAU,EAAMhC,QAAS,+BAC3B,CAAEiC,IAAK,IAAKjC,QAAS,+CAGvBU,EAAAA,cAACwB,EAAAA,EAAK,OAGRxB,EAAAA,cAAA,OAAKK,UAAU,sBACbL,EAAAA,cAACjB,EAAAA,EAAKoC,KAAI,CACRd,UAAU,SACVe,MAAM,OACN1B,KAAK,UACL2B,MAAO,CAAC,CAAEC,UAAU,EAAMhC,QAAS,0BAEnCU,EAAAA,cAACyB,EAAAA,EAAM,CACLC,YAAY,gBACZzC,QAASA,EACT0C,SAAU1C,GAAWa,EACrB8B,YAAU,EACVC,iBAAiB,WACjBC,aAAcA,CAACC,EAAOC,KAAM,IAAAC,EAAA,OACZ,QAAdA,EAACD,aAAM,EAANA,EAAQZ,aAAK,IAAAa,EAAAA,EAAI,IACfC,cACAC,SAASJ,EAAMG,gBAEpBE,QAASvD,EAAMwD,IAAKC,IAAI,CACtBC,MAAOD,EAAK3B,GACZS,MAAO,GAAGkB,EAAKE,UAAUpB,UAAUkB,EAAKE,UAAUC,qBAEpDC,gBAAiBzD,EAAUe,EAAAA,cAAC2C,EAAAA,EAAI,CAACC,KAAK,UAAa,SAKzD5C,EAAAA,cAAA,OAAKK,UAAU,wBACbL,EAAAA,cAAC6C,EAAAA,KAAI,CAACC,GAAG,UAAS,mBAGnBhD,GACCE,EAAAA,cAAA,OAAKK,UAAU,mFACbL,EAAAA,cAAC+C,EAAAA,EAAiB,CAAC1C,UAAU,YAC7BL,EAAAA,cAAA,YAAM,gDAIVA,EAAAA,cAACjB,EAAAA,EAAKoC,KAAI,CAACd,UAAU,yBACnBL,EAAAA,cAAA,OAAKK,UAAU,cACbL,EAAAA,cAACgD,EAAAA,GAAM,CAACC,QAAStE,GAAU,UAC3BqB,EAAAA,cAACgD,EAAAA,GAAM,CAACE,KAAK,UAAUC,SAAS,SAASxB,SAAU7B,GAChDrB,EAAU,SAAW,eASpC,I,qKCiBA,MAhJ2BD,IAIK,IAJJ,MAC1BK,EAAK,UACLuE,EAAS,eACTC,GACwB7E,EACxB,MAAM,EAAC8E,EAAe,EAACC,IAAqBpE,EAAAA,EAAAA,aACtC,EAACqE,EAAe,EAACC,IAAqBtE,EAAAA,EAAAA,UAC1C,KACE,GAAsB,oBAAXuE,OAAwB,CACjC,MAAMC,EAASC,aAAaC,QAAQ,kBACpC,OAAOF,EAASG,SAASH,QAAU/D,CACrC,KAGE,EAACmE,EAAO,EAACC,IAAa7E,EAAAA,EAAAA,UAAiB,IAGvC8E,EAAgBpF,EAAMqF,OAAQ5B,IAAU,IAAD6B,EAAAC,EAC3C,OACsB,QAApBD,EAAA7B,EAAKE,UAAUpB,aAAK,IAAA+C,OAAA,EAApBA,EAAsBjC,cAAcC,SAAS4B,EAAO7B,kBAC1B,QADwCkC,EAClE9B,EAAKE,UAAU6B,mBAAW,IAAAD,OAAA,EAA1BA,EAA4BlC,cAAcC,SAAS4B,EAAO7B,mBAK9D1C,EAAAA,EAAAA,WAAU,KACJgE,GAAkB3E,EAAMyF,KAAMhC,GAASA,EAAK3B,KAAO6C,GACrDD,EAAkBC,GACT3E,EAAMkB,OAAS,GACxBwD,EAAkB1E,EAAM,GAAG8B,KAE5B,CAAC9B,EAAO2E,IAEX,MAqCM1D,GAAcsD,GAA8B,IAAjBvE,EAAMkB,OAkCjCwE,EAAY,CAChBC,MAjC6B,CAC7B,CACEtB,KAAM,QACN9B,MACEpB,EAAAA,cAAA,WACEA,EAAAA,cAAA,OAAKK,UAAU,+BAA8B,QAC7CL,EAAAA,cAACwB,EAAAA,EAAK,CACJiD,OAAQzE,EAAAA,cAAC0E,EAAAA,EAAU,CAACrE,UAAU,YAC9BqB,YAAY,OACZiD,SAAWC,GAAMZ,EAAUY,EAAEC,OAAOtC,UAI1CuC,IAAK,aAEP,CACE5B,KAAM,YACP6B,QAAAC,EAAAA,EAAAA,GACEf,EAAc5B,IAAKC,IAAI,IAAA2C,EAAA,MAAM,CAC9B7D,MACEpB,EAAAA,cAAA,WACEA,EAAAA,cAAA,YAAMkF,EAAAA,EAAAA,IAAa5C,EAAKE,UAAUpB,OAAS,GAAI,KAC/CpB,EAAAA,cAAA,OAAKK,UAAU,0BACZiC,EAAKE,UAAUC,iBAItBqC,KAAKxC,SAAQ,QAAJ2C,EAAJ3C,EAAM3B,UAAE,IAAAsE,OAAJ,EAAJA,EAAUE,aAAc,GAC7BC,KAAMpF,EAAAA,cAACqF,EAAAA,EAAG,CAAChF,UAAU,iBAMvB4C,QA1D4CxC,UAC5C,MAAM6E,EAAYxB,SAASc,EAAEE,KACRjG,EAAM0G,KAAMjD,GAASA,EAAK3B,KAAO2E,GAQtD/B,EAAkB+B,GALhBrE,QAAQJ,MAAM,2BAA4ByE,KAwDxCE,EAAe3G,EAAM0G,KAAMjD,GAASA,EAAK3B,KAAO2C,GAEtD,OACEtD,EAAAA,cAAA,OAAKK,UAAU,oBACbL,EAAAA,cAACyF,EAAAA,EAASzC,OAAM,CACd0C,KAAMnB,EACNrB,KAAK,UACL7C,UAAU,SACVsF,UAAU,cACVP,KAAMpF,EAAAA,cAAC4F,EAAAA,EAAW,CAACvF,UAAU,YAC7B4C,QAtFqBxC,UACzB,IAAK6C,EAAgB,OAEC,oBAAXI,QACTE,aAAaiC,QAAQ,iBAAkBvC,EAAe6B,YAGxD,MAAMK,EAAe3G,EAAM0G,KAAMjD,GAASA,EAAK3B,KAAO2C,GACjDkC,UAGC,IAAIM,QAASC,GAAYC,WAAWD,EAAS,MACnD1C,EAAeC,EAAgBkC,EAAahD,UAAUpB,OAAS,MA2E3DO,UAAW2B,GAAkBF,GAE7BpD,EAAAA,cAAA,OAAKK,UAAU,GAAG4F,MAAO,CAAEC,MAAO,UAChClG,EAAAA,cAACmG,EAAAA,EAAI,CAAC9F,UAAU,+BAA+B,UAInDL,EAAAA,cAAA,OACEK,UAAU,yBACVH,MAAOsF,aAAY,EAAZA,EAAchD,UAAUpB,QAE9B8D,EAAAA,EAAAA,KAAaM,aAAY,EAAZA,EAAchD,UAAUpB,QAAS,GAAI,KAGpDtB,GACCE,EAAAA,cAAA,OAAKK,UAAU,0DACbL,EAAAA,cAACoG,EAAAA,EAAQ,CAAC/F,UAAU,YACpBL,EAAAA,cAAA,YAAM,gBCzHT,MAAMqG,EAAkC7H,IAWxC,IAXyC,OAC9CI,EAAM,SACN0H,EAAQ,eACRC,EAAc,SACdC,EAAQ,gBACRC,EAAe,cACfC,EAAa,gBACbC,EAAe,UACfvD,GAAY,EAAK,eACjBC,EAAc,MACdxE,GACDL,EACC,OAAKI,EAiCHoB,EAAAA,cAAA,OAAKK,UAAU,qCACbL,EAAAA,cAAA,OAAKK,UAAU,kFACbL,EAAAA,cAAA,OAAKK,UAAU,2BACbL,EAAAA,cAAA,QAAMK,UAAU,4BAA2B,MAC3CL,EAAAA,cAAA,QAAMK,UAAU,wDACbiG,EAASvG,SAGdC,EAAAA,cAAC4G,EAAAA,EAAO,CAAC1G,MAAM,iBACbF,EAAAA,cAAA,UACEiD,QAASuD,EACTnG,UAAU,gKAEVL,EAAAA,cAAC6G,EAAAA,EAAc,CAACC,YAAa,IAAKzG,UAAU,eAKlDL,EAAAA,cAAA,OAAKK,UAAU,uBACbL,EAAAA,cAAA,OAAKK,UAAU,qBACZzB,GACCoB,EAAAA,cAAC+G,EAAkB,CACjBlI,MAAOA,EACPuE,UAAWA,EACXC,eAAgBA,MAMxBrD,EAAAA,cAAA,OAAKK,UAAU,oCACbL,EAAAA,cAACgH,EAAAA,EAAO,CAAC3G,UAAU,gCACnBL,EAAAA,cAAA,OAAKK,UAAU,wBAAuB,KACjC,IACHL,EAAAA,cAAA,QAAMK,UAAU,mCACb,IAAI,IACHiG,EAASvG,OAAO,IAAE,KACd,KAGTqD,GACCpD,EAAAA,cAACiH,EAAAA,EAAU,CAAC5G,UAAU,6CAMxB+C,GAAiC,IAApBkD,EAASvG,QACtBC,EAAAA,cAAA,OAAKK,UAAU,6EACbL,EAAAA,cAACoG,EAAAA,EAAQ,CAAC/F,UAAU,wCAAwC,YAKhEL,EAAAA,cAAA,OAAKK,UAAU,mDACZiG,EAASjE,IAAK6E,GACblH,EAAAA,cAAA,OAAK8E,IAAKoC,EAAEvG,GAAIN,UAAU,YACxBL,EAAAA,cAAA,OACEK,UAAW,0GAERkG,aAAc,EAAdA,EAAgB5F,MAAOuG,EAAEvG,GAAK,YAAc,gBAG9C,KAEHX,EAAAA,cAAA,OACEK,UAAW,8GACTkG,aAAc,EAAdA,EAAgB5F,MAAOuG,EAAEvG,GAAK,6BAA+B,IAE/DsC,QAASA,IAAMwD,EAAgBS,IAE/BlH,EAAAA,cAAA,OAAKK,UAAU,qCACbL,EAAAA,cAAA,OAAKK,UAAU,oBAAoB6G,EAAExH,MACrCM,EAAAA,cAAA,QAAMK,UAAU,oCACb8G,EAAAA,EAAAA,IAAsBD,EAAEE,YAAc,MAG3CpH,EAAAA,cAAA,OAAKK,UAAU,iFACbL,EAAAA,cAAC4G,EAAAA,EAAO,CAAC1G,MAAM,gBACbF,EAAAA,cAACgD,EAAAA,GAAM,CACLE,KAAK,OACLN,KAAK,QACLvC,UAAU,uBACV+E,KAAMpF,EAAAA,cAACqH,EAAAA,EAAI,CAAChH,UAAU,YACtB4C,QAAU2B,IACRA,EAAE0C,kBACFZ,EAAcQ,OAIpBlH,EAAAA,cAAC4G,EAAAA,EAAO,CAAC1G,MAAM,kBACbF,EAAAA,cAACgD,EAAAA,GAAM,CACLE,KAAK,OACLN,KAAK,QACLvC,UAAU,uBACVkH,QAAM,EACNnC,KAAMpF,EAAAA,cAACwH,EAAAA,EAAM,CAACnH,UAAU,yBACxB4C,QAAU2B,IACRA,EAAE0C,kBACEJ,EAAEvG,IAAIgG,EAAgBO,EAAEvG,cAlI5CX,EAAAA,cAAA,OAAKK,UAAU,qCACbL,EAAAA,cAAA,OAAKK,UAAU,cACbL,EAAAA,cAAC4G,EAAAA,EAAO,CACN1G,MAAMF,EAAAA,cAAA,YAAM,KACP,IACHA,EAAAA,cAAA,QAAMK,UAAU,oBAAmB,IAAEiG,EAASvG,OAAO,KAAS,MAGhEC,EAAAA,cAAA,UACEiD,QAASuD,EACTnG,UAAU,gKAEVL,EAAAA,cAACyH,EAAAA,EAAa,CAACX,YAAa,IAAKzG,UAAU,eAIjDL,EAAAA,cAAA,OAAKK,UAAU,mBACbL,EAAAA,cAAC4G,EAAAA,EAAO,CAAC1G,MAAM,SACbF,EAAAA,cAACgD,EAAAA,GAAM,CACLE,KAAK,OACL7C,UAAU,iCACV4C,QAASA,IAAMyD,IACftB,KAAMpF,EAAAA,cAACmG,EAAAA,EAAI,CAAC9F,UAAU,kB,kCChD7B,MAAMqH,GAAkBC,EAAAA,EAAAA,GAAqB,CAACC,EAAKC,KAAG,CAE3DC,UAAW,GACXC,gBAAiB,KACjB3E,WAAW,EACXvC,MAAO,KAGPmH,eAAgBvH,UACd,IACEmH,EAAI,CAAExE,WAAW,EAAMvC,MAAO,OAC9B,MAAMiH,QAAkBG,EAAAA,EAAWC,cAAcC,GAEjDP,EAAI,CACFE,YAEAC,gBAAiBF,IAAME,iBAAmBD,EAAU,IAAM,KAC1D1E,WAAW,GAEf,CAAE,MAAOvC,GACP+G,EAAI,CACF/G,MACEA,aAAiBC,MAAQD,EAAMvB,QAAU,4BAC3C8D,WAAW,GAEf,GAGFgF,cAAgBC,IACdT,EAAI,CAAEG,gBAAiBM,KAGzBC,mBAAoBA,IACXT,IAAME,mBCtCJQ,EAA2BA,KACtC,MAAM,EAAC1J,EAAM,EAAC2J,IAAYrJ,EAAAA,EAAAA,UAAiB,KACrC,EAACiE,EAAU,EAACqF,IAAgBtJ,EAAAA,EAAAA,WAAS,IACrC,EAACuJ,EAAa,EAACC,IAAmBxJ,EAAAA,EAAAA,WAAS,IAC3C,EAACyJ,EAAe,EAACC,IAAqB1J,EAAAA,EAAAA,aACtC,EAAC2J,EAAc,EAACC,IAAoB5J,EAAAA,EAAAA,UAAS,KACjD,GAAsB,oBAAXuE,OAAwB,CACjC,MAAMC,EAASC,aAAaC,QAAQ,kBACpC,OAAkB,OAAXF,GAAkBqF,KAAKC,MAAMtF,EACtC,CACA,OAAO,KAEFvE,EAAYC,GAAiBC,EAAAA,GAAQC,cAEtC,KAAE2J,IAASC,EAAAA,EAAAA,YAAWC,EAAAA,IACtB,QAAE3K,EAAO,WAAE4K,EAAU,SAAE/C,EAAQ,YAAEgD,IAAgBC,EAAAA,EAAAA,MACjD,EAACC,EAAc,EAACC,IAAoBtK,EAAAA,EAAAA,WAAS,IAC7C,EAACuK,EAAkB,EAACC,IAAwBxK,EAAAA,EAAAA,UAChD,MAGIyK,EAAelC,KAmBrBlI,EAAAA,EAAAA,WAAU,KACc,oBAAXkE,QACTE,aAAaiC,QAAQ,iBAAkBmD,KAAKa,UAAUf,KAEvD,CAACA,IAEJ,MAAMgB,GAAgBC,EAAAA,EAAAA,aAAYtJ,UAChC,GAAKyI,SAAAA,EAAMvI,GAEX,IACE8H,GAAa,GACb,MAAMuB,QAAaC,EAAAA,EAAWC,aAAahB,EAAKvI,IAChD2I,EAAYU,GAGZ,MACMG,EADS,IAAIC,gBAAgB1G,OAAO2G,SAAStG,QAC1B8D,IAAI,cACxBpJ,GAAWuL,EAAKjK,OAAS,IAAMoK,GAClCd,EAAWW,EAAK,GAEpB,CAAE,MAAOnJ,GACPI,QAAQJ,MAAM,2BAA4BA,GAC1CzB,EAAWyB,MAAM,yBACnB,CAAC,QACC4H,GAAa,EACf,GACC,CAACS,aAAI,EAAJA,EAAMvI,GAAI2I,EAAa7K,EAAS4K,KAGpC7J,EAAAA,EAAAA,WAAU,KACR,MACM2K,EADS,IAAIC,gBAAgB1G,OAAO2G,SAAStG,QAC1B8D,IAAI,aAEzBsC,IAAc1L,GAChB6L,EAAoB,CAAE3J,GAAImD,SAASqG,MAEpC,KAGH3K,EAAAA,EAAAA,WAAU,KACR,MAAM+K,EAAuBA,MACZ,IAAIH,gBAAgB1G,OAAO2G,SAAStG,QAC1B8D,IAAI,cAEXpJ,GAChB4K,EAAW,OAKf,OADA3F,OAAO8G,iBAAiB,WAAYD,GAC7B,IAAM7G,OAAO+G,oBAAoB,WAAYF,IACnD,CAAC9L,IAEJ,MAoEM6L,EAAsB7J,UAC1B,GAAKyI,SAAAA,EAAMvI,IAAO+J,EAAgB/J,GAElC,IACE8H,GAAa,GACb,MAAMuB,QAAaC,EAAAA,EAAWU,WAAWD,EAAgB/J,GAAIuI,EAAKvI,IAClE,IAAKqJ,EAQH,OAPA5K,EAAWyB,MAAM,qBACjB6C,OAAOkH,QAAQC,UAAU,CAAC,EAAG,GAAInH,OAAO2G,SAASS,eAC7CxE,EAASvG,OAAS,EACpBsJ,EAAW/C,EAAS,IAEpB+C,EAAW,OAIfA,EAAWW,GACXtG,OAAOkH,QAAQC,UAAU,CAAC,EAAG,GAAI,cAAcH,EAAgB/J,KACjE,CAAE,MAAOE,GACPI,QAAQJ,MAAM,yBAA0BA,GACxCzB,EAAWyB,MAAM,wBACnB,CAAC,QACC4H,GAAa,EACf,IAGFjJ,EAAAA,EAAAA,WAAU,KACRsK,KACC,CAACA,IAGJ,MAAMiB,GAAahB,EAAAA,EAAAA,aAAYtJ,UAE7B,GAAKyI,SAAAA,EAAMvI,GAEX,IACE8H,GAAa,GACb,MAAMuC,QAAkBC,EAAAA,GAAQC,UAAUhC,EAAKvI,IAC/C,GAAIqK,EAAUjL,OAAS,EACrByI,EAASwC,OACJ,CACL/J,QAAQkK,IAAI,+CACNvB,EAAa5B,eAAekB,EAAKvI,IACvC,MAAMyK,EAAiBxB,EAAatB,qBAE9B+C,EAAaD,aAAc,EAAdA,EAAgBE,OAAOC,WAAW1M,MAAM,GAG3D,GAFAoC,QAAQkK,IAAI,uCAAwCE,GAEhDA,EAAY,CACd,MAAMG,EAAiB,CACrBhJ,UAAW6I,GAEPI,QAAoBR,EAAAA,GAAQS,WAAWF,EAAUtC,EAAKvI,IAC5DM,QAAQkK,IAAI,wBAAyBK,GAErChD,EAAS,CAACiD,GACZ,CACF,CACF,CAAE,MAAO5K,GACPI,QAAQJ,MAAM,wBAAyBA,GACvCzB,EAAWyB,MAAM,sBACnB,CAAC,QACC4H,GAAa,EACf,GACC,CAACS,aAAI,EAAJA,EAAMvI,GAAIvB,IAOd,OAJAI,EAAAA,EAAAA,WAAU,KACRuL,KACC,CAACA,IAGF/K,EAAAA,cAAA,OAAKK,UAAU,+BACZhB,EACDW,EAAAA,cAAA,OACEK,UAAW,yEACTyI,EAAgB,OAAS,SAG3B9I,EAAAA,cAACqG,EAAO,CACNzH,OAAQkK,EACRjK,MAAOA,EACPwE,eA1GiB5C,MAAOkL,EAAgBC,KAC9C,GAAK1C,SAAAA,EAAMvI,GACX,IACE,MAAMkL,EAAc,GAAGD,EAASE,UAC9B,EACA,UACK,IAAIC,MAAOC,2BACZC,QAAgBhC,EAAAA,EAAWiC,cAC/B,CACExM,KAAMmM,EACNlM,QAASgM,GAEXzC,EAAKvI,IAGP2I,EAAY,CAAC2C,GAAOlH,QAAAC,EAAAA,EAAAA,GAAKsB,KACzB+C,EAAW4C,GACX7M,EAAWwB,QAAQ,mBACrB,CAAE,MAAOC,GACPzB,EAAWyB,MAAM,yBACnB,GAuFMyF,SAAUA,EACVC,eAAgB9H,EAChB+H,SAAUA,IAAMuC,GAAkBD,GAClCrC,gBAAiB6D,EACjB5D,cAAgBjI,IACdoK,EAAkBpK,GAClBkK,GAAgB,IAElBhC,gBApIoBlG,UAC1B,GAAKyI,SAAAA,EAAMvI,GAEX,UACyBsJ,EAAAA,EAAWkC,cAAchC,EAAWjB,EAAKvI,IAChE2I,EAAYhD,EAASpC,OAAQgD,GAAMA,EAAEvG,KAAOwJ,KACxC1L,aAAO,EAAPA,EAASkC,MAAOwJ,GAAiC,IAApB7D,EAASvG,SACxCsJ,EAAW/C,EAAS,IAAM,MAC1B5C,OAAOkH,QAAQC,UAAU,CAAC,EAAG,GAAInH,OAAO2G,SAASS,WAEnD1L,EAAWwB,QAAQ,kBACrB,CAAE,MAAOC,GACPI,QAAQJ,MAAM,0BAA2BA,GACzCzB,EAAWyB,MAAM,yBACnB,GAuHMuC,UAAWA,KAIfpD,EAAAA,cAAA,OACEK,UAAW,uCACTyI,EAAgB,QAAU,UAG3BrK,GAAW6H,EAASvG,OAAS,EAC5BC,EAAAA,cAAA,OAAKK,UAAU,mBAEbL,EAAAA,cAAA,OAAKK,UAAW,WAAUmJ,EAAgB,QAAU,WAClDxJ,EAAAA,cAACoM,EAAAA,EAAQ,CACP3N,QAASA,EACT+K,cAAeA,EACf6C,eAtPaC,KACzB,GAAIhG,EAASvG,OAAS,EAAG,CAEvB,MAAMwM,EAAejG,EAASf,KAAM2B,GAAMA,EAAEvG,MAAOlC,aAAO,EAAPA,EAASkC,KAC5DgJ,EAAqB4C,GAAgB9N,EACvC,MAEEkL,EAAqBlL,GAEvBgL,GAAiB,IA8OL+C,gBAAiBlC,EACjBmC,kBAAmBnG,KAKtBkD,GACCxJ,EAAAA,cAAA,OAAKK,UAAU,kDACbL,EAAAA,cAACoM,EAAAA,EAAQ,CACP3N,QAASiL,EACTF,eAAe,EACfkD,iBAAiB,EACjBC,cAvPUC,KACxBnD,GAAiB,GACjBE,EAAqB,OAsPP6C,gBAAiB7C,EACjB8C,kBAAmBnG,MAM3BtG,EAAAA,cAAA,OAAKK,UAAU,0DAAyD,4BAM5EL,EAAAA,cAACzB,EAAa,CACZM,MAAOA,EACPJ,QAASmK,EACThK,OAAQ8J,EACRhK,OA/MoB+B,UACxB,GAAKyI,SAAAA,EAAMvI,GAEX,IACE,GAAIkM,EAAYlM,GAAI,CAClB,MAAMmM,QAAgB7C,EAAAA,EAAW8C,cAC/BF,EAAYlM,GACZkM,EACA3D,EAAKvI,IAEP2I,EAAYhD,EAASjE,IAAK6E,GAAOA,EAAEvG,KAAOmM,EAAQnM,GAAKmM,EAAU5F,KAC7DzI,aAAO,EAAPA,EAASkC,MAAOmM,EAAQnM,IAC1B0I,EAAWyD,EAEf,KAAO,CACL,MAAMb,QAAgBhC,EAAAA,EAAWiC,cAAcW,EAAa3D,EAAKvI,IACjE2I,EAAY,CAAC2C,GAAOlH,QAAAC,EAAAA,EAAAA,GAAKsB,KACzB+C,EAAW4C,EACb,CACAtD,GAAgB,GAChBE,OAAkBjJ,EACpB,CAAE,MAAOiB,GACPzB,EAAWyB,MAAM,wBACjBI,QAAQJ,MAAMA,EAChB,GAwLIlC,SAAUA,KACRgK,GAAgB,GAChBE,OAAkBjJ,O,+ECzT5B,EADoB,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,SAAW,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,+LAAmM,KAAQ,QAAS,MAAS,Y,UCMpX,EAAgB,SAAuBoN,EAAOC,GAChD,OAAoB,gBAAoBC,EAAAC,GAAU,OAAS,CAAC,EAAGH,EAAO,CACpEC,IAAKA,EACL7H,KAAM,IAEV,EAOA,MAJ2B,aAAiB,E", "sources": ["webpack://autogentstudio/./src/components/views/playground/editor.tsx", "webpack://autogentstudio/./src/components/views/playground/newsession.tsx", "webpack://autogentstudio/./src/components/views/playground/sidebar.tsx", "webpack://autogentstudio/./src/components/views/gallery/store.tsx", "webpack://autogentstudio/./src/components/views/playground/manager.tsx", "webpack://autogentstudio/./node_modules/@ant-design/icons-svg/es/asn/CheckOutlined.js", "webpack://autogentstudio/./node_modules/@ant-design/icons/es/icons/CheckOutlined.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { Modal, Form, message, Input, Button, Select, Spin } from \"antd\";\r\nimport { TriangleAlertIcon } from \"lucide-react\";\r\nimport type { FormProps } from \"antd\";\r\nimport { SessionEditorProps } from \"./types\";\r\nimport { Link } from \"gatsby\";\r\n\r\ntype FieldType = {\r\n  name: string;\r\n  team_id?: number;\r\n};\r\n\r\nexport const SessionEditor: React.FC<SessionEditorProps> = ({\r\n  session,\r\n  onSave,\r\n  onCancel,\r\n  isOpen,\r\n  teams,\r\n}) => {\r\n  const [form] = Form.useForm();\r\n  const [loading, setLoading] = useState(false);\r\n  const [messageApi, contextHolder] = message.useMessage();\r\n\r\n  // Set form values when modal opens or session changes\r\n  useEffect(() => {\r\n    if (isOpen) {\r\n      form.setFieldsValue({\r\n        name: session?.name || \"\",\r\n        team_id: session?.team_id || undefined,\r\n      });\r\n    } else {\r\n      form.resetFields();\r\n    }\r\n  }, [form, session, isOpen]);\r\n\r\n  const onFinish: FormProps<FieldType>[\"onFinish\"] = async (values) => {\r\n    try {\r\n      await onSave({\r\n        ...values,\r\n        id: session?.id,\r\n      });\r\n      messageApi.success(\r\n        `Session ${session ? \"updated\" : \"created\"} successfully`\r\n      );\r\n    } catch (error) {\r\n      if (error instanceof Error) {\r\n        messageApi.error(error.message);\r\n      }\r\n    }\r\n  };\r\n\r\n  const onFinishFailed: FormProps<FieldType>[\"onFinishFailed\"] = (\r\n    errorInfo\r\n  ) => {\r\n    messageApi.error(\"Please check the form for errors\");\r\n    console.error(\"Form validation failed:\", errorInfo);\r\n  };\r\n\r\n  const hasNoTeams = !loading && teams.length === 0;\r\n\r\n  return (\r\n    <Modal\r\n      title={session ? \"Edit Session\" : \"Create Session\"}\r\n      open={isOpen}\r\n      onCancel={onCancel}\r\n      footer={null}\r\n      className=\"text-primary\"\r\n      forceRender\r\n    >\r\n      {contextHolder}\r\n      <Form\r\n        form={form}\r\n        name=\"session-form\"\r\n        layout=\"vertical\"\r\n        onFinish={onFinish}\r\n        onFinishFailed={onFinishFailed}\r\n        autoComplete=\"off\"\r\n      >\r\n        <Form.Item<FieldType>\r\n          label=\"Session Name\"\r\n          name=\"name\"\r\n          rules={[\r\n            { required: true, message: \"Please enter a session name\" },\r\n            { max: 100, message: \"Session name cannot exceed 100 characters\" },\r\n          ]}\r\n        >\r\n          <Input />\r\n        </Form.Item>\r\n\r\n        <div className=\"space-y-2   w-full\">\r\n          <Form.Item<FieldType>\r\n            className=\"w-full\"\r\n            label=\"Team\"\r\n            name=\"team_id\"\r\n            rules={[{ required: true, message: \"Please select a team\" }]}\r\n          >\r\n            <Select\r\n              placeholder=\"Select a team\"\r\n              loading={loading}\r\n              disabled={loading || hasNoTeams}\r\n              showSearch\r\n              optionFilterProp=\"children\"\r\n              filterOption={(input, option) =>\r\n                (option?.label ?? \"\")\r\n                  .toLowerCase()\r\n                  .includes(input.toLowerCase())\r\n              }\r\n              options={teams.map((team) => ({\r\n                value: team.id,\r\n                label: `${team.component.label} (${team.component.component_type})`,\r\n              }))}\r\n              notFoundContent={loading ? <Spin size=\"small\" /> : null}\r\n            />\r\n          </Form.Item>\r\n        </div>\r\n\r\n        <div className=\"text-sm text-accent \">\r\n          <Link to=\"/build\">view all teams</Link>\r\n        </div>\r\n\r\n        {hasNoTeams && (\r\n          <div className=\"flex border p-1 rounded -mt-2 mb-4 items-center gap-1.5 text-sm text-yellow-600\">\r\n            <TriangleAlertIcon className=\"h-4 w-4\" />\r\n            <span>No teams found. Please create a team first.</span>\r\n          </div>\r\n        )}\r\n\r\n        <Form.Item className=\"flex justify-end mb-0\">\r\n          <div className=\"flex gap-2\">\r\n            <Button onClick={onCancel}>Cancel</Button>\r\n            <Button type=\"primary\" htmlType=\"submit\" disabled={hasNoTeams}>\r\n              {session ? \"Update\" : \"Create\"}\r\n            </Button>\r\n          </div>\r\n        </Form.Item>\r\n      </Form>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport default SessionEditor;\r\n", "import React, { useState, useEffect } from \"react\";\r\nimport { <PERSON><PERSON>, Dropdown, MenuProps, message, Select, Space } from \"antd\";\r\nimport { Plus, InfoIcon, Bot, TextSearch, ChevronDown } from \"lucide-react\";\r\nimport { Team } from \"../../types/datamodel\";\r\nimport { truncateText } from \"../../utils/utils\";\r\nimport Input from \"antd/es/input/Input\";\r\n\r\ninterface NewSessionControlsProps {\r\n  teams: Team[];\r\n  isLoading: boolean;\r\n  onStartSession: (teamId: number, teamName: string) => void;\r\n}\r\n\r\nconst NewSessionControls = ({\r\n  teams,\r\n  isLoading,\r\n  onStartSession,\r\n}: NewSessionControlsProps) => {\r\n  const [selectedTeamId, setSelectedTeamId] = useState<number | undefined>();\r\n  const [lastUsedTeamId, setLastUsedTeamId] = useState<number | undefined>(\r\n    () => {\r\n      if (typeof window !== \"undefined\") {\r\n        const stored = localStorage.getItem(\"lastUsedTeamId\");\r\n        return stored ? parseInt(stored) : undefined;\r\n      }\r\n    }\r\n  );\r\n  const [search, setSearch] = useState<string>(\"\");\r\n\r\n  // Filter teams based on search\r\n  const filteredTeams = teams.filter((team) => {\r\n    return (\r\n      team.component.label?.toLowerCase().includes(search.toLowerCase()) ||\r\n      team.component.description?.toLowerCase().includes(search.toLowerCase())\r\n    );\r\n  });\r\n\r\n  // Auto-select last used team on load\r\n  useEffect(() => {\r\n    if (lastUsedTeamId && teams.some((team) => team.id === lastUsedTeamId)) {\r\n      setSelectedTeamId(lastUsedTeamId);\r\n    } else if (teams.length > 0) {\r\n      setSelectedTeamId(teams[0].id);\r\n    }\r\n  }, [teams, lastUsedTeamId]);\r\n\r\n  const handleStartSession = async () => {\r\n    if (!selectedTeamId) return;\r\n\r\n    if (typeof window !== \"undefined\") {\r\n      localStorage.setItem(\"lastUsedTeamId\", selectedTeamId.toString());\r\n    }\r\n\r\n    const selectedTeam = teams.find((team) => team.id === selectedTeamId);\r\n    if (!selectedTeam) return;\r\n\r\n    // Give UI time to update before starting session\r\n    await new Promise((resolve) => setTimeout(resolve, 100));\r\n    onStartSession(selectedTeamId, selectedTeam.component.label || \"\");\r\n  };\r\n\r\n  const handleMenuClick: MenuProps[\"onClick\"] = async (e) => {\r\n    const newTeamId = parseInt(e.key);\r\n    const selectedTeam = teams.find((team) => team.id === newTeamId);\r\n\r\n    if (!selectedTeam) {\r\n      console.error(\"Selected team not found:\", newTeamId);\r\n      return;\r\n    }\r\n\r\n    // Update state first\r\n    setSelectedTeamId(newTeamId);\r\n\r\n    // // Save to localStorage\r\n    // if (typeof window !== \"undefined\") {\r\n    //   localStorage.setItem(\"lastUsedTeamId\", e.key);\r\n    // }\r\n\r\n    // // Delay the session start to allow UI to update\r\n    // await new Promise((resolve) => setTimeout(resolve, 100));\r\n    // onStartSession(newTeamId, selectedTeam.component.label || \"\");\r\n  };\r\n\r\n  const hasNoTeams = !isLoading && teams.length === 0;\r\n\r\n  const items: MenuProps[\"items\"] = [\r\n    {\r\n      type: \"group\",\r\n      label: (\r\n        <div>\r\n          <div className=\"text-xs text-secondary mb-1\">选择团队</div>\r\n          <Input\r\n            prefix={<TextSearch className=\"w-4 h-4\" />}\r\n            placeholder=\"搜索团队\"\r\n            onChange={(e) => setSearch(e.target.value)}\r\n          />\r\n        </div>\r\n      ),\r\n      key: \"from-team\",\r\n    },\r\n    {\r\n      type: \"divider\",\r\n    },\r\n    ...filteredTeams.map((team) => ({\r\n      label: (\r\n        <div>\r\n          <div>{truncateText(team.component.label || \"\", 20)}</div>\r\n          <div className=\"text-xs text-secondary\">\r\n            {team.component.component_type}\r\n          </div>\r\n        </div>\r\n      ),\r\n      key: team?.id?.toString() || \"\",\r\n      icon: <Bot className=\"w-4 h-4\" />,\r\n    })),\r\n  ];\r\n\r\n  const menuProps = {\r\n    items,\r\n    onClick: handleMenuClick,\r\n  };\r\n\r\n  const selectedTeam = teams.find((team) => team.id === selectedTeamId);\r\n\r\n  return (\r\n    <div className=\"space-y-2 w-full\">\r\n      <Dropdown.Button\r\n        menu={menuProps}\r\n        type=\"primary\"\r\n        className=\"w-full\"\r\n        placement=\"bottomRight\"\r\n        icon={<ChevronDown className=\"w-4 h-4\" />}\r\n        onClick={handleStartSession}\r\n        disabled={!selectedTeamId || isLoading}\r\n      >\r\n        <div className=\"\" style={{ width: \"183px\" }}>\r\n          <Plus className=\"w-4 h-4 inline-block -mt-1\" /> 新建会话\r\n        </div>\r\n      </Dropdown.Button>\r\n\r\n      <div\r\n        className=\"text-xs text-secondary\"\r\n        title={selectedTeam?.component.label}\r\n      >\r\n        {truncateText(selectedTeam?.component.label || \"\", 30)}\r\n      </div>\r\n\r\n      {hasNoTeams && (\r\n        <div className=\"flex items-center gap-1.5 text-xs text-yellow-600 mt-1\">\r\n          <InfoIcon className=\"h-3 w-3\" />\r\n          <span>创建团队以开始使用</span>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default NewSessionControls;\r\n", "import React from \"react\";\r\nimport { <PERSON><PERSON>, Tooltip } from \"antd\";\r\nimport {\r\n  Plus,\r\n  Edit,\r\n  Trash2,\r\n  PanelLeftClose,\r\n  PanelLeftOpen,\r\n  InfoIcon,\r\n  RefreshCcw,\r\n  History,\r\n} from \"lucide-react\";\r\nimport type { Session, Team } from \"../../types/datamodel\";\r\nimport { getRelativeTimeString } from \"../atoms\";\r\nimport NewSessionControls from \"./newsession\";\r\n\r\ninterface SidebarProps {\r\n  isOpen: boolean;\r\n  sessions: Session[];\r\n  currentSession: Session | null;\r\n  onToggle: () => void;\r\n  onSelectSession: (session: Session) => void;\r\n  onEditSession: (session?: Session) => void;\r\n  onDeleteSession: (sessionId: number) => void;\r\n  isLoading?: boolean;\r\n  onStartSession: (teamId: number, teamName: string) => void;\r\n  teams: Team[];\r\n}\r\n\r\nexport const Sidebar: React.FC<SidebarProps> = ({\r\n  isOpen,\r\n  sessions,\r\n  currentSession,\r\n  onToggle,\r\n  onSelectSession,\r\n  onEditSession,\r\n  onDeleteSession,\r\n  isLoading = false,\r\n  onStartSession,\r\n  teams,\r\n}) => {\r\n  if (!isOpen) {\r\n    return (\r\n      <div className=\"h-full  border-r border-secondary\">\r\n        <div className=\"p-2 -ml-2 \">\r\n          <Tooltip\r\n            title=<span>\r\n              会话{\" \"}\r\n              <span className=\"text-accent mx-1\"> {sessions.length} </span>{\" \"}\r\n            </span>\r\n          >\r\n            <button\r\n              onClick={onToggle}\r\n              className=\"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50\"\r\n            >\r\n              <PanelLeftOpen strokeWidth={1.5} className=\"h-6 w-6\" />\r\n            </button>\r\n          </Tooltip>\r\n        </div>\r\n        <div className=\"mt-4 px-2 -ml-1\">\r\n          <Tooltip title=\"创建新会话\">\r\n            <Button\r\n              type=\"text\"\r\n              className=\"w-full p-2 flex justify-center\"\r\n              onClick={() => onEditSession()}\r\n              icon={<Plus className=\"w-4 h-4\" />}\r\n            />\r\n          </Tooltip>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"h-full border-r border-secondary \">\r\n      <div className=\"flex items-center justify-between pt-0 p-4 pl-2 pr-2 border-b border-secondary\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <span className=\"text-primary font-medium\">会话</span>\r\n          <span className=\"px-2 py-0.5 text-xs bg-accent/10 text-accent rounded\">\r\n            {sessions.length}\r\n          </span>\r\n        </div>\r\n        <Tooltip title=\"Close Sidebar\">\r\n          <button\r\n            onClick={onToggle}\r\n            className=\"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50\"\r\n          >\r\n            <PanelLeftClose strokeWidth={1.5} className=\"h-6 w-6\" />\r\n          </button>\r\n        </Tooltip>\r\n      </div>\r\n\r\n      <div className=\"my-4 flex text-sm  \">\r\n        <div className=\" mr-2 w-full pr-2\">\r\n          {isOpen && (\r\n            <NewSessionControls\r\n              teams={teams}\r\n              isLoading={isLoading}\r\n              onStartSession={onStartSession}\r\n            />\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"py-2 flex text-sm text-secondary\">\r\n        <History className=\"w-4 h-4 inline-block mr-1.5\" />\r\n        <div className=\"inline-block -mt-0.5\">\r\n          最近{\" \"}\r\n          <span className=\"text-accent text-xs mx-1 mt-0.5\">\r\n            {\" \"}\r\n            ({sessions.length}){\" \"}\r\n          </span>{\" \"}\r\n        </div>\r\n\r\n        {isLoading && (\r\n          <RefreshCcw className=\"w-4 h-4 inline-block ml-2 animate-spin\" />\r\n        )}\r\n      </div>\r\n\r\n      {/* no sessions found */}\r\n\r\n      {!isLoading && sessions.length === 0 && (\r\n        <div className=\"p-2 mr-2 text-center text-secondary text-sm border border-dashed rounded \">\r\n          <InfoIcon className=\"w-4 h-4 inline-block mr-1.5 -mt-0.5\" />\r\n          未找到最近的会话\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"overflow-y-auto   scroll   h-[calc(100%-181px)]\">\r\n        {sessions.map((s) => (\r\n          <div key={s.id} className=\"relative\">\r\n            <div\r\n              className={`bg-accent absolute top-1 left-0.5 z-50 h-[calc(100%-8px)]\r\n               w-1 bg-opacity-80  rounded ${\r\n                 currentSession?.id === s.id ? \"bg-accent\" : \"bg-tertiary\"\r\n               }`}\r\n            >\r\n              {\" \"}\r\n            </div>\r\n            <div\r\n              className={`group ml-1 flex items-center justify-between rounded-l p-2 py-1 text-sm cursor-pointer hover:bg-tertiary ${\r\n                currentSession?.id === s.id ? \"border-accent bg-secondary\" : \"\"\r\n              }`}\r\n              onClick={() => onSelectSession(s)}\r\n            >\r\n              <div className=\"flex flex-col min-w-0 flex-1 mr-2\">\r\n                <div className=\"truncate text-sm\">{s.name}</div>\r\n                <span className=\"truncate text-xs text-secondary\">\r\n                  {getRelativeTimeString(s.updated_at || \"\")}\r\n                </span>\r\n              </div>\r\n              <div className=\"py-3 flex gap-1 shrink-0 opacity-0 group-hover:opacity-100 transition-opacity\">\r\n                <Tooltip title=\"Edit session\">\r\n                  <Button\r\n                    type=\"text\"\r\n                    size=\"small\"\r\n                    className=\"p-1 min-w-[24px] h-6\"\r\n                    icon={<Edit className=\"w-4 h-4\" />}\r\n                    onClick={(e) => {\r\n                      e.stopPropagation();\r\n                      onEditSession(s);\r\n                    }}\r\n                  />\r\n                </Tooltip>\r\n                <Tooltip title=\"Delete session\">\r\n                  <Button\r\n                    type=\"text\"\r\n                    size=\"small\"\r\n                    className=\"p-1 min-w-[24px] h-6\"\r\n                    danger\r\n                    icon={<Trash2 className=\"w-4 h-4 text-red-500\" />}\r\n                    onClick={(e) => {\r\n                      e.stopPropagation();\r\n                      if (s.id) onDeleteSession(s.id);\r\n                    }}\r\n                  />\r\n                </Tooltip>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n", "import { create } from \"zustand\";\r\nimport { Gallery } from \"../../types/datamodel\";\r\nimport { galleryAPI } from \"./api\";\r\n\r\ninterface GalleryState {\r\n  // State\r\n  galleries: Gallery[];\r\n  selectedGallery: Gallery | null;\r\n  isLoading: boolean;\r\n  error: string | null;\r\n\r\n  // Actions\r\n  fetchGalleries: (userId: string) => Promise<void>;\r\n  selectGallery: (gallery: Gallery) => void;\r\n  getSelectedGallery: () => Gallery | null;\r\n}\r\n\r\nexport const useGalleryStore = create<GalleryState>((set, get) => ({\r\n  // Initial state\r\n  galleries: [],\r\n  selectedGallery: null,\r\n  isLoading: false,\r\n  error: null,\r\n\r\n  // Actions\r\n  fetchGalleries: async (userId: string) => {\r\n    try {\r\n      set({ isLoading: true, error: null });\r\n      const galleries = await galleryAPI.listGalleries(userId);\r\n\r\n      set({\r\n        galleries,\r\n        // Automatically select first gallery if none selected\r\n        selectedGallery: get().selectedGallery || galleries[0] || null,\r\n        isLoading: false,\r\n      });\r\n    } catch (error) {\r\n      set({\r\n        error:\r\n          error instanceof Error ? error.message : \"Failed to fetch galleries\",\r\n        isLoading: false,\r\n      });\r\n    }\r\n  },\r\n\r\n  selectGallery: (gallery: Gallery) => {\r\n    set({ selectedGallery: gallery });\r\n  },\r\n\r\n  getSelectedGallery: () => {\r\n    return get().selectedGallery;\r\n  },\r\n}));\r\n", "import React, { useCallback, useEffect, useState, useContext } from \"react\";\r\nimport { Button, message } from \"antd\";\r\nimport { useConfigStore } from \"../../../hooks/store\";\r\nimport { appContext } from \"../../../hooks/provider\";\r\nimport { sessionAPI } from \"./api\";\r\nimport { SessionEditor } from \"./editor\";\r\nimport type { Session, Team } from \"../../types/datamodel\";\r\nimport ChatView from \"./chat/chat\";\r\nimport { Sidebar } from \"./sidebar\";\r\nimport { teamAPI } from \"../teambuilder/api\";\r\nimport { useGalleryStore } from \"../gallery/store\";\r\n\r\nexport const SessionManager: React.FC = () => {\r\n  const [teams, setTeams] = useState<Team[]>([]);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [isEditorOpen, setIsEditorOpen] = useState(false);\r\n  const [editingSession, setEditingSession] = useState<Session | undefined>();\r\n  const [isSidebarOpen, setIsSidebarOpen] = useState(() => {\r\n    if (typeof window !== \"undefined\") {\r\n      const stored = localStorage.getItem(\"sessionSidebar\");\r\n      return stored !== null ? JSON.parse(stored) : true;\r\n    }\r\n    return true; // Default value during SSR\r\n  });\r\n  const [messageApi, contextHolder] = message.useMessage();\r\n\r\n  const { user } = useContext(appContext);\r\n  const { session, setSession, sessions, setSessions } = useConfigStore();\r\n  const [isCompareMode, setIsCompareMode] = useState(false);\r\n  const [comparisonSession, setComparisonSession] = useState<Session | null>(\r\n    null\r\n  );\r\n\r\n  const galleryStore = useGalleryStore();\r\n\r\n  const handleCompareClick = () => {\r\n    if (sessions.length > 1) {\r\n      // Find the first session that isn't the current one\r\n      const otherSession = sessions.find((s) => s.id !== session?.id);\r\n      setComparisonSession(otherSession || session);\r\n    } else {\r\n      // If only one session, show it in both panels\r\n      setComparisonSession(session);\r\n    }\r\n    setIsCompareMode(true);\r\n  };\r\n\r\n  const handleExitCompare = () => {\r\n    setIsCompareMode(false);\r\n    setComparisonSession(null);\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (typeof window !== \"undefined\") {\r\n      localStorage.setItem(\"sessionSidebar\", JSON.stringify(isSidebarOpen));\r\n    }\r\n  }, [isSidebarOpen]);\r\n\r\n  const fetchSessions = useCallback(async () => {\r\n    if (!user?.id) return;\r\n\r\n    try {\r\n      setIsLoading(true);\r\n      const data = await sessionAPI.listSessions(user.id);\r\n      setSessions(data);\r\n\r\n      // Only set first session if there's no sessionId in URL\r\n      const params = new URLSearchParams(window.location.search);\r\n      const sessionId = params.get(\"sessionId\");\r\n      if (!session && data.length > 0 && !sessionId) {\r\n        setSession(data[0]);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching sessions:\", error);\r\n      messageApi.error(\"Error loading sessions\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, [user?.id, setSessions, session, setSession]);\r\n\r\n  // Handle initial URL params\r\n  useEffect(() => {\r\n    const params = new URLSearchParams(window.location.search);\r\n    const sessionId = params.get(\"sessionId\");\r\n\r\n    if (sessionId && !session) {\r\n      handleSelectSession({ id: parseInt(sessionId) } as Session);\r\n    }\r\n  }, []);\r\n\r\n  // Handle browser back/forward\r\n  useEffect(() => {\r\n    const handleLocationChange = () => {\r\n      const params = new URLSearchParams(window.location.search);\r\n      const sessionId = params.get(\"sessionId\");\r\n\r\n      if (!sessionId && session) {\r\n        setSession(null);\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"popstate\", handleLocationChange);\r\n    return () => window.removeEventListener(\"popstate\", handleLocationChange);\r\n  }, [session]);\r\n\r\n  const handleSaveSession = async (sessionData: Partial<Session>) => {\r\n    if (!user?.id) return;\r\n\r\n    try {\r\n      if (sessionData.id) {\r\n        const updated = await sessionAPI.updateSession(\r\n          sessionData.id,\r\n          sessionData,\r\n          user.id\r\n        );\r\n        setSessions(sessions.map((s) => (s.id === updated.id ? updated : s)));\r\n        if (session?.id === updated.id) {\r\n          setSession(updated);\r\n        }\r\n      } else {\r\n        const created = await sessionAPI.createSession(sessionData, user.id);\r\n        setSessions([created, ...sessions]);\r\n        setSession(created);\r\n      }\r\n      setIsEditorOpen(false);\r\n      setEditingSession(undefined);\r\n    } catch (error) {\r\n      messageApi.error(\"Error saving session\");\r\n      console.error(error);\r\n    }\r\n  };\r\n\r\n  const handleDeleteSession = async (sessionId: number) => {\r\n    if (!user?.id) return;\r\n\r\n    try {\r\n      const response = await sessionAPI.deleteSession(sessionId, user.id);\r\n      setSessions(sessions.filter((s) => s.id !== sessionId));\r\n      if (session?.id === sessionId || sessions.length === 0) {\r\n        setSession(sessions[0] || null);\r\n        window.history.pushState({}, \"\", window.location.pathname); // Clear URL params\r\n      }\r\n      messageApi.success(\"Session deleted\");\r\n    } catch (error) {\r\n      console.error(\"Error deleting session:\", error);\r\n      messageApi.error(\"Error deleting session\");\r\n    }\r\n  };\r\n\r\n  const handleQuickStart = async (teamId: number, teamName: string) => {\r\n    if (!user?.id) return;\r\n    try {\r\n      const defaultName = `${teamName.substring(\r\n        0,\r\n        20\r\n      )} - ${new Date().toLocaleString()} Session`;\r\n      const created = await sessionAPI.createSession(\r\n        {\r\n          name: defaultName,\r\n          team_id: teamId,\r\n        },\r\n        user.id\r\n      );\r\n\r\n      setSessions([created, ...sessions]);\r\n      setSession(created);\r\n      messageApi.success(\"Session created!\");\r\n    } catch (error) {\r\n      messageApi.error(\"Error creating session\");\r\n    }\r\n  };\r\n\r\n  // Modify the existing session selection handler\r\n  const handleSelectSession = async (selectedSession: Session) => {\r\n    if (!user?.id || !selectedSession.id) return;\r\n\r\n    try {\r\n      setIsLoading(true);\r\n      const data = await sessionAPI.getSession(selectedSession.id, user.id);\r\n      if (!data) {\r\n        messageApi.error(\"Session not found\");\r\n        window.history.pushState({}, \"\", window.location.pathname);\r\n        if (sessions.length > 0) {\r\n          setSession(sessions[0]);\r\n        } else {\r\n          setSession(null);\r\n        }\r\n        return;\r\n      }\r\n      setSession(data);\r\n      window.history.pushState({}, \"\", `?sessionId=${selectedSession.id}`);\r\n    } catch (error) {\r\n      console.error(\"Error loading session:\", error);\r\n      messageApi.error(\"Error loading session\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchSessions();\r\n  }, [fetchSessions]);\r\n\r\n  // Add teams fetching\r\n  const fetchTeams = useCallback(async () => {\r\n    // console.log(\"Fetching teams\", user);\r\n    if (!user?.id) return;\r\n\r\n    try {\r\n      setIsLoading(true);\r\n      const teamsData = await teamAPI.listTeams(user.id);\r\n      if (teamsData.length > 0) {\r\n        setTeams(teamsData);\r\n      } else {\r\n        console.log(\"No teams found, creating default team\");\r\n        await galleryStore.fetchGalleries(user.id);\r\n        const defaultGallery = galleryStore.getSelectedGallery();\r\n\r\n        const sampleTeam = defaultGallery?.config.components.teams[0];\r\n        console.log(\"Default Gallery .. manager fetching \", sampleTeam);\r\n        // // If no teams, create a default team\r\n        if (sampleTeam) {\r\n          const teamData: Team = {\r\n            component: sampleTeam,\r\n          };\r\n          const defaultTeam = await teamAPI.createTeam(teamData, user.id);\r\n          console.log(\"Default team created:\", teamData);\r\n\r\n          setTeams([defaultTeam]);\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching teams:\", error);\r\n      messageApi.error(\"Error loading teams\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, [user?.id, messageApi]);\r\n\r\n  // Fetch teams on mount\r\n  useEffect(() => {\r\n    fetchTeams();\r\n  }, [fetchTeams]);\r\n\r\n  return (\r\n    <div className=\"relative flex h-full w-full\">\r\n      {contextHolder}\r\n      <div\r\n        className={`absolute left-0 top-0 h-full transition-all duration-200 ease-in-out ${\r\n          isSidebarOpen ? \"w-64\" : \"w-12\"\r\n        }`}\r\n      >\r\n        <Sidebar\r\n          isOpen={isSidebarOpen}\r\n          teams={teams}\r\n          onStartSession={handleQuickStart}\r\n          sessions={sessions}\r\n          currentSession={session}\r\n          onToggle={() => setIsSidebarOpen(!isSidebarOpen)}\r\n          onSelectSession={handleSelectSession}\r\n          onEditSession={(session) => {\r\n            setEditingSession(session);\r\n            setIsEditorOpen(true);\r\n          }}\r\n          onDeleteSession={handleDeleteSession}\r\n          isLoading={isLoading}\r\n        />\r\n      </div>\r\n\r\n      <div\r\n        className={`flex-1 transition-all duration-200 ${\r\n          isSidebarOpen ? \"ml-64\" : \"ml-12\"\r\n        }`}\r\n      >\r\n        {session && sessions.length > 0 ? (\r\n          <div className=\"flex gap-4 pl-4\">\r\n            {/* Primary ChatView */}\r\n            <div className={`flex-1 ${isCompareMode ? \"w-1/2\" : \"w-full\"}`}>\r\n              <ChatView\r\n                session={session}\r\n                isCompareMode={isCompareMode}\r\n                onCompareClick={handleCompareClick}\r\n                onSessionChange={handleSelectSession}\r\n                availableSessions={sessions}\r\n              />\r\n            </div>\r\n\r\n            {/* Comparison ChatView */}\r\n            {isCompareMode && (\r\n              <div className=\"flex-1 w-1/2 border-l border-secondary/20 pl-4\">\r\n                <ChatView\r\n                  session={comparisonSession}\r\n                  isCompareMode={true}\r\n                  isSecondaryView={true}\r\n                  onExitCompare={handleExitCompare}\r\n                  onSessionChange={setComparisonSession}\r\n                  availableSessions={sessions}\r\n                />\r\n              </div>\r\n            )}\r\n          </div>\r\n        ) : (\r\n          <div className=\"flex items-center justify-center h-full text-secondary\">\r\n            \"未选择会话。请从侧边栏创建或选择一个会话。\"\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      <SessionEditor\r\n        teams={teams}\r\n        session={editingSession}\r\n        isOpen={isEditorOpen}\r\n        onSave={handleSaveSession}\r\n        onCancel={() => {\r\n          setIsEditorOpen(false);\r\n          setEditingSession(undefined);\r\n        }}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n", "// This icon file is generated automatically.\nvar CheckOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z\" } }] }, \"name\": \"check\", \"theme\": \"outlined\" };\nexport default CheckOutlined;\n", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport CheckOutlinedSvg from \"@ant-design/icons-svg/es/asn/CheckOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar CheckOutlined = function CheckOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: CheckOutlinedSvg\n  }));\n};\n\n/**![check](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkxMiAxOTBoLTY5LjljLTkuOCAwLTE5LjEgNC41LTI1LjEgMTIuMkw0MDQuNyA3MjQuNSAyMDcgNDc0YTMyIDMyIDAgMDAtMjUuMS0xMi4ySDExMmMtNi43IDAtMTAuNCA3LjctNi4zIDEyLjlsMjczLjkgMzQ3YzEyLjggMTYuMiAzNy40IDE2LjIgNTAuMyAwbDQ4OC40LTYxOC45YzQuMS01LjEuNC0xMi44LTYuMy0xMi44eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(CheckOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'CheckOutlined';\n}\nexport default RefIcon;"], "names": ["SessionEditor", "_ref", "session", "onSave", "onCancel", "isOpen", "teams", "form", "Form", "useForm", "loading", "setLoading", "useState", "messageApi", "contextHolder", "message", "useMessage", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "team_id", "undefined", "resetFields", "hasNoTeams", "length", "React", "Modal", "title", "open", "footer", "className", "forceRender", "layout", "onFinish", "async", "values", "id", "success", "error", "Error", "onFinishFailed", "errorInfo", "console", "autoComplete", "<PERSON><PERSON>", "label", "rules", "required", "max", "Input", "Select", "placeholder", "disabled", "showSearch", "optionFilterProp", "filterOption", "input", "option", "_option$label", "toLowerCase", "includes", "options", "map", "team", "value", "component", "component_type", "notFoundContent", "Spin", "size", "Link", "to", "TriangleAlertIcon", "<PERSON><PERSON>", "onClick", "type", "htmlType", "isLoading", "onStartSession", "selectedTeamId", "setSelectedTeamId", "lastUsedTeamId", "setLastUsedTeamId", "window", "stored", "localStorage", "getItem", "parseInt", "search", "setSearch", "filteredTeams", "filter", "_team$component$label", "_team$component$descr", "description", "some", "menuProps", "items", "prefix", "TextSearch", "onChange", "e", "target", "key", "concat", "_toConsumableArray", "_team$id", "truncateText", "toString", "icon", "Bot", "newTeamId", "find", "selectedTeam", "Dropdown", "menu", "placement", "ChevronDown", "setItem", "Promise", "resolve", "setTimeout", "style", "width", "Plus", "InfoIcon", "Sidebar", "sessions", "currentSession", "onToggle", "onSelectSession", "onEditSession", "onDeleteSession", "<PERSON><PERSON><PERSON>", "PanelLeftClose", "strokeWidth", "NewSessionControls", "History", "RefreshCcw", "s", "getRelativeTimeString", "updated_at", "Edit", "stopPropagation", "danger", "Trash2", "PanelLeftOpen", "useGalleryStore", "create", "set", "get", "galleries", "selectedGallery", "fetchGalleries", "galleryAPI", "listGalleries", "userId", "selectGallery", "gallery", "getSelectedGallery", "Session<PERSON>anager", "setTeams", "setIsLoading", "isEditorOpen", "setIsEditorOpen", "editingSession", "setEditingSession", "isSidebarOpen", "setIsSidebarOpen", "JSON", "parse", "user", "useContext", "appContext", "setSession", "setSessions", "useConfigStore", "isCompareMode", "setIsCompareMode", "comparisonSession", "setComparisonSession", "galleryStore", "stringify", "fetchSessions", "useCallback", "data", "sessionAPI", "listSessions", "sessionId", "URLSearchParams", "location", "handleSelectSession", "handleLocationChange", "addEventListener", "removeEventListener", "selectedSession", "getSession", "history", "pushState", "pathname", "fetchTeams", "teamsData", "teamAPI", "listTeams", "log", "defaultGallery", "sampleTeam", "config", "components", "teamData", "defaultTeam", "createTeam", "teamId", "teamName", "defaultName", "substring", "Date", "toLocaleString", "created", "createSession", "deleteSession", "ChatView", "onCompareClick", "handleCompareClick", "otherSession", "onSessionChange", "availableSessions", "isSecondaryView", "onExitCompare", "handleExitCompare", "sessionData", "updated", "updateSession", "props", "ref", "AntdIcon", "A"], "sourceRoot": ""}