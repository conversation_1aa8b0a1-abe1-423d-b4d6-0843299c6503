{"version": 3, "file": "component---src-pages-index-tsx-afbbc81fa056529b0e7f.js", "mappings": "0LAuEA,UA7DkBA,IAA6B,IAA5B,KAAEC,EAAI,SAAEC,GAAeF,EACxC,MAAM,eAAEG,EAAc,gBAAEC,EAAe,UAAEC,IAAcC,EAAAA,EAAAA,MACjD,EAACC,EAAiB,EAAEC,IAAwBC,EAAAA,EAAAA,WAAS,GA6B3D,OA3BAC,EAAAA,EAAAA,WAAU,KACkBC,WAExB,MACMC,EADS,IAAIC,gBAAgBX,EAASY,QACvBC,IAAI,SAEzB,GAAIH,IAAUR,IAAoBC,EAChC,IACEG,GAAqB,SACfL,EAAeS,GAGrB,MAAMI,EAASC,OAAOf,SAASgB,SAC/BD,OAAOE,QAAQC,aAAa,CAAC,EAAGC,SAASC,MAAON,EAClD,CAAE,MAAOO,GACPC,QAAQD,MAAM,sBAAuBA,GACrCE,EAAAA,GAAQF,MAAM,wCAChB,CAAE,QACAf,GAAqB,EACvB,GAIJkB,IACC,CAACxB,EAASY,OAAQV,EAAiBC,EAAWF,IAG7CI,EAEAoB,EAAAA,cAACC,EAAAA,EAAM,CAACC,KAAM5B,EAAK6B,KAAKC,aAAcT,MAAM,KAAKU,KAAM,KACrDL,EAAAA,cAAA,OAAKM,UAAU,6CACbN,EAAAA,cAACO,EAAAA,EAAI,CAACC,KAAK,QAAQC,IAAI,cAO7BT,EAAAA,cAACC,EAAAA,EAAM,CAACC,KAAM5B,EAAK6B,KAAKC,aAAcT,MAAM,KAAKU,KAAM,KACrDL,EAAAA,cAAA,QAAMU,MAAO,CAAEC,OAAQ,QAAUL,UAAU,YACzCN,EAAAA,cAACY,EAAAA,EAAc,Q", "sources": ["webpack://autogentstudio/./src/pages/index.tsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport { useEffect, useState } from \"react\";\r\nimport Layout from \"../components/layout\";\r\nimport { graphql, navigate } from \"gatsby\";\r\nimport ChatView from \"../components/views/playground/chat/chat\";\r\nimport { SessionManager } from \"../components/views/playground/manager\";\r\nimport { useAuth } from \"../auth/context\";\r\nimport { Spin, message } from \"antd\";\r\n\r\n// markup\r\nconst IndexPage = ({ data, location }: any) => {\r\n  const { loginWithToken, isAuthenticated, isLoading } = useAuth();\r\n  const [isProcessingToken, setIsProcessingToken] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const processTokenLogin = async () => {\r\n      // Check if there's a token parameter in the URL\r\n      const params = new URLSearchParams(location.search);\r\n      const token = params.get(\"token\");\r\n\r\n      if (token && !isAuthenticated && !isLoading) {\r\n        try {\r\n          setIsProcessingToken(true);\r\n          await loginWithToken(token);\r\n\r\n          // Remove token from URL after successful login\r\n          const newUrl = window.location.pathname;\r\n          window.history.replaceState({}, document.title, newUrl);\r\n        } catch (error) {\r\n          console.error(\"Token login failed:\", error);\r\n          message.error(\"Token login failed. Please try again.\");\r\n        } finally {\r\n          setIsProcessingToken(false);\r\n        }\r\n      }\r\n    };\r\n\r\n    processTokenLogin();\r\n  }, [location.search, isAuthenticated, isLoading, loginWithToken]);\r\n\r\n  // Show loading spinner while processing token login\r\n  if (isProcessingToken) {\r\n    return (\r\n      <Layout meta={data.site.siteMetadata} title=\"首页\" link={\"/\"}>\r\n        <div className=\"flex items-center justify-center h-screen\">\r\n          <Spin size=\"large\" tip=\"正在登录...\" />\r\n        </div>\r\n      </Layout>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Layout meta={data.site.siteMetadata} title=\"首页\" link={\"/\"}>\r\n      <main style={{ height: \"100%\" }} className=\" h-full \">\r\n        <SessionManager />\r\n      </main>\r\n    </Layout>\r\n  );\r\n};\r\n\r\nexport const query = graphql`\r\n  query HomePageQuery {\r\n    site {\r\n      siteMetadata {\r\n        description\r\n        title\r\n      }\r\n    }\r\n  }\r\n`;\r\n\r\nexport default IndexPage;\r\n"], "names": ["_ref", "data", "location", "loginWithToken", "isAuthenticated", "isLoading", "useAuth", "isProcessingToken", "setIsProcessingToken", "useState", "useEffect", "async", "token", "URLSearchParams", "search", "get", "newUrl", "window", "pathname", "history", "replaceState", "document", "title", "error", "console", "message", "processTokenLogin", "React", "Layout", "meta", "site", "siteMetadata", "link", "className", "Spin", "size", "tip", "style", "height", "Session<PERSON>anager"], "sourceRoot": ""}