/*! For license information please see 50b6c9a48db3f2c12a9112e6cf56a481ffb5324c-212575c12d7d62d8d99e.js.LICENSE.txt */
(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[408],{73:function(e,t,n){"use strict";n.d(t,{A:function(){return o}});const o=(0,n(1788).A)("Hash",[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]])},315:function(e){var t=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,n=/\n/g,o=/^\s*/,r=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,i=/^:\s*/,l=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,a=/^[;\s]*/,s=/^\s+|\s+$/g,c="";function u(e){return e?e.replace(s,c):c}e.exports=function(e,s){if("string"!=typeof e)throw new TypeError("First argument must be a string");if(!e)return[];s=s||{};var d=1,f=1;function p(e){var t=e.match(n);t&&(d+=t.length);var o=e.lastIndexOf("\n");f=~o?e.length-o:f+e.length}function m(){var e={line:d,column:f};return function(t){return t.position=new h(e),y(),t}}function h(e){this.start=e,this.end={line:d,column:f},this.source=s.source}h.prototype.content=e;var g=[];function v(t){var n=new Error(s.source+":"+d+":"+f+": "+t);if(n.reason=t,n.filename=s.source,n.line=d,n.column=f,n.source=e,!s.silent)throw n;g.push(n)}function b(t){var n=t.exec(e);if(n){var o=n[0];return p(o),e=e.slice(o.length),n}}function y(){b(o)}function x(e){var t;for(e=e||[];t=w();)!1!==t&&e.push(t);return e}function w(){var t=m();if("/"==e.charAt(0)&&"*"==e.charAt(1)){for(var n=2;c!=e.charAt(n)&&("*"!=e.charAt(n)||"/"!=e.charAt(n+1));)++n;if(n+=2,c===e.charAt(n-1))return v("End of comment missing");var o=e.slice(2,n-2);return f+=2,p(o),e=e.slice(n),f+=2,t({type:"comment",comment:o})}}function C(){var e=m(),n=b(r);if(n){if(w(),!b(i))return v("property missing ':'");var o=b(l),s=e({type:"declaration",property:u(n[0].replace(t,c)),value:o?u(o[0].replace(t,c)):c});return b(a),s}}return y(),function(){var e,t=[];for(x(t);e=C();)!1!==e&&(t.push(e),x(t));return t}()}},329:function(e,t,n){"use strict";n.d(t,{A:function(){return s}});var o=n(8168),r=n(6540),i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"}}]},name:"left",theme:"outlined"},l=n(7064),a=function(e,t){return r.createElement(l.A,(0,o.A)({},e,{ref:t,icon:i}))};var s=r.forwardRef(a)},827:function(e,t,n){"use strict";n.d(t,{A:function(){return o}});const o=(0,n(1788).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},977:function(e,t){"use strict";t.A=e=>({[e.componentCls]:{[`${e.antCls}-motion-collapse-legacy`]:{overflow:"hidden","&-active":{transition:`height ${e.motionDurationMid} ${e.motionEaseInOut},\n        opacity ${e.motionDurationMid} ${e.motionEaseInOut} !important`}},[`${e.antCls}-motion-collapse`]:{overflow:"hidden",transition:`height ${e.motionDurationMid} ${e.motionEaseInOut},\n        opacity ${e.motionDurationMid} ${e.motionEaseInOut} !important`}}})},2102:function(e,t,n){"use strict";n.d(t,{A:function(){return o}});const o=(0,n(1788).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},2571:function(e,t,n){"use strict";n.d(t,{Z:function(){return r},f:function(){return i}});var o=n(7387);let r=function(e){function t(){return e.apply(this,arguments)||this}(0,o.A)(t,e);var n=t.prototype;return n.listGalleries=async function(e){const t=await fetch(`${this.getBaseUrl()}/gallery/?user_id=${e}`,{headers:this.getHeaders()}),n=await t.json();if(!n.status)throw new Error(n.message||"Failed to fetch galleries");return n.data},n.getGallery=async function(e,t){const n=await fetch(`${this.getBaseUrl()}/gallery/${e}?user_id=${t}`,{headers:this.getHeaders()}),o=await n.json();if(!o.status)throw new Error(o.message||"Failed to fetch gallery");return o.data},n.createGallery=async function(e,t){const n={...e,user_id:t};console.log("Creating gallery with data:",n);const o=await fetch(`${this.getBaseUrl()}/gallery/`,{method:"POST",headers:this.getHeaders(),body:JSON.stringify(n)}),r=await o.json();if(!r.status)throw new Error(r.message||"Failed to create gallery");return r.data},n.updateGallery=async function(e,t,n){const o={...t,user_id:n},r=await fetch(`${this.getBaseUrl()}/gallery/${e}?user_id=${n}`,{method:"PUT",headers:this.getHeaders(),body:JSON.stringify(o)}),i=await r.json();if(!i.status)throw new Error(i.message||"Failed to update gallery");return i.data},n.deleteGallery=async function(e,t){const n=await fetch(`${this.getBaseUrl()}/gallery/${e}?user_id=${t}`,{method:"DELETE",headers:this.getHeaders()}),o=await n.json();if(!o.status)throw new Error(o.message||"Failed to delete gallery")},n.syncGallery=async function(e){const t=await fetch(e);if(!t.ok)throw new Error(`Failed to sync gallery from ${e}`);return await t.json()},t}(n(3838).y);const i=new r},2702:function(e,t,n){"use strict";n.d(t,{A:function(){return v}});var o=n(6540),r=n(6942),i=n.n(r),l=n(2546);function a(e){return["small","middle","large"].includes(e)}function s(e){return!!e&&("number"==typeof e&&!Number.isNaN(e))}var c=n(2279),u=n(6327);const d=o.createContext({latestIndex:0}),f=d.Provider;var p=({className:e,index:t,children:n,split:r,style:i})=>{const{latestIndex:l}=o.useContext(d);return null==n?null:o.createElement(o.Fragment,null,o.createElement("div",{className:e,style:i},n),t<l&&r&&o.createElement("span",{className:`${e}-split`},r))},m=n(5447),h=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const g=o.forwardRef((e,t)=>{var n;const{getPrefixCls:r,direction:u,size:d,className:g,style:v,classNames:b,styles:y}=(0,c.TP)("space"),{size:x=(null!=d?d:"small"),align:w,className:C,rootClassName:k,children:$,direction:S="horizontal",prefixCls:A,split:E,style:I,wrap:O=!1,classNames:z,styles:M}=e,P=h(e,["size","align","className","rootClassName","children","direction","prefixCls","split","style","wrap","classNames","styles"]),[T,j]=Array.isArray(x)?x:[x,x],N=a(j),B=a(T),R=s(j),D=s(T),L=(0,l.A)($,{keepEmpty:!0}),H=void 0===w&&"horizontal"===S?"center":w,F=r("space",A),[_,W,V]=(0,m.A)(F),q=i()(F,g,W,`${F}-${S}`,{[`${F}-rtl`]:"rtl"===u,[`${F}-align-${H}`]:H,[`${F}-gap-row-${j}`]:N,[`${F}-gap-col-${T}`]:B},C,k,V),U=i()(`${F}-item`,null!==(n=null==z?void 0:z.item)&&void 0!==n?n:b.item);let X=0;const G=L.map((e,t)=>{var n;null!=e&&(X=t);const r=(null==e?void 0:e.key)||`${U}-${t}`;return o.createElement(p,{className:U,key:r,index:t,split:E,style:null!==(n=null==M?void 0:M.item)&&void 0!==n?n:y.item},e)}),K=o.useMemo(()=>({latestIndex:X}),[X]);if(0===L.length)return null;const Y={};return O&&(Y.flexWrap="wrap"),!B&&D&&(Y.columnGap=T),!N&&R&&(Y.rowGap=j),_(o.createElement("div",Object.assign({ref:t,className:q,style:Object.assign(Object.assign(Object.assign({},Y),v),I)},P),o.createElement(f,{value:K},G)))});g.Compact=u.Ay;var v=g},2708:function(e,t,n){"use strict";n.d(t,{A:function(){return o}});const o=(0,n(1788).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},2849:function(e){"use strict";var t=Object.prototype.hasOwnProperty,n=Object.prototype.toString,o=Object.defineProperty,r=Object.getOwnPropertyDescriptor,i=function(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===n.call(e)},l=function(e){if(!e||"[object Object]"!==n.call(e))return!1;var o,r=t.call(e,"constructor"),i=e.constructor&&e.constructor.prototype&&t.call(e.constructor.prototype,"isPrototypeOf");if(e.constructor&&!r&&!i)return!1;for(o in e);return void 0===o||t.call(e,o)},a=function(e,t){o&&"__proto__"===t.name?o(e,t.name,{enumerable:!0,configurable:!0,value:t.newValue,writable:!0}):e[t.name]=t.newValue},s=function(e,n){if("__proto__"===n){if(!t.call(e,n))return;if(r)return r(e,n).value}return e[n]};e.exports=function e(){var t,n,o,r,c,u,d=arguments[0],f=1,p=arguments.length,m=!1;for("boolean"==typeof d&&(m=d,d=arguments[1]||{},f=2),(null==d||"object"!=typeof d&&"function"!=typeof d)&&(d={});f<p;++f)if(null!=(t=arguments[f]))for(n in t)o=s(d,n),d!==(r=s(t,n))&&(m&&r&&(l(r)||(c=i(r)))?(c?(c=!1,u=o&&i(o)?o:[]):u=o&&l(o)?o:{},a(d,{name:n,newValue:e(m,u,r)})):void 0!==r&&a(d,{name:n,newValue:r}));return d}},3324:function(e,t,n){"use strict";n.d(t,{A:function(){return o}});const o=(0,n(1788).A)("Maximize2",[["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["polyline",{points:"9 21 3 21 3 15",key:"1avn1i"}],["line",{x1:"21",x2:"14",y1:"3",y2:"10",key:"ota7mn"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]])},3425:function(e,t,n){"use strict";n.d(t,{U:function(){return a}});var o=n(6540),r=n(2533),i=n(867),l=n(2279);function a(e){return t=>o.createElement(i.Ay,{theme:{token:{motion:!1,zIndexPopupBase:0}}},o.createElement(e,Object.assign({},t)))}t.A=(e,t,n,i,s)=>a(a=>{const{prefixCls:c,style:u}=a,d=o.useRef(null),[f,p]=o.useState(0),[m,h]=o.useState(0),[g,v]=(0,r.A)(!1,{value:a.open}),{getPrefixCls:b}=o.useContext(l.QO),y=b(i||"select",c);o.useEffect(()=>{if(v(!0),"undefined"!=typeof ResizeObserver){const e=new ResizeObserver(e=>{const t=e[0].target;p(t.offsetHeight+8),h(t.offsetWidth)}),t=setInterval(()=>{var n;const o=s?`.${s(y)}`:`.${y}-dropdown`,r=null===(n=d.current)||void 0===n?void 0:n.querySelector(o);r&&(clearInterval(t),e.observe(r))},10);return()=>{clearInterval(t),e.disconnect()}}},[]);let x=Object.assign(Object.assign({},a),{style:Object.assign(Object.assign({},u),{margin:0}),open:g,visible:g,getPopupContainer:()=>d.current});n&&(x=n(x)),t&&Object.assign(x,{[t]:{overflow:{adjustX:!1,adjustY:!1}}});const w={paddingBottom:f,position:"relative",minWidth:m};return o.createElement("div",{ref:d,style:w},o.createElement(e,Object.assign({},x)))})},3618:function(e,t,n){"use strict";n.d(t,{A:function(){return ct}});var o=n(4241),r=n(436),i=n(6540),l=n(6942),a=n.n(l),s=n(754),c=n(3723),u=n(934);function d(e){const[t,n]=i.useState(e);return i.useEffect(()=>{const t=setTimeout(()=>{n(e)},e.length?0:10);return()=>{clearTimeout(t)}},[e]),t}var f=n(2187),p=n(5905),m=n(9077),h=n(977),g=n(4277),v=n(7358);var b=e=>{const{componentCls:t}=e,n=`${t}-show-help`,o=`${t}-show-help-item`;return{[n]:{transition:`opacity ${e.motionDurationFast} ${e.motionEaseInOut}`,"&-appear, &-enter":{opacity:0,"&-active":{opacity:1}},"&-leave":{opacity:1,"&-active":{opacity:0}},[o]:{overflow:"hidden",transition:`height ${e.motionDurationFast} ${e.motionEaseInOut},\n                     opacity ${e.motionDurationFast} ${e.motionEaseInOut},\n                     transform ${e.motionDurationFast} ${e.motionEaseInOut} !important`,[`&${o}-appear, &${o}-enter`]:{transform:"translateY(-5px)",opacity:0,"&-active":{transform:"translateY(0)",opacity:1}},[`&${o}-leave-active`]:{transform:"translateY(-5px)"}}}}};const y=e=>({legend:{display:"block",width:"100%",marginBottom:e.marginLG,padding:0,color:e.colorTextDescription,fontSize:e.fontSizeLG,lineHeight:"inherit",border:0,borderBottom:`${(0,f.zA)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},'input[type="search"]':{boxSizing:"border-box"},'input[type="radio"], input[type="checkbox"]':{lineHeight:"normal"},'input[type="file"]':{display:"block"},'input[type="range"]':{display:"block",width:"100%"},"select[multiple], select[size]":{height:"auto"},"input[type='file']:focus,\n  input[type='radio']:focus,\n  input[type='checkbox']:focus":{outline:0,boxShadow:`0 0 0 ${(0,f.zA)(e.controlOutlineWidth)} ${e.controlOutline}`},output:{display:"block",paddingTop:15,color:e.colorText,fontSize:e.fontSize,lineHeight:e.lineHeight}}),x=(e,t)=>{const{formItemCls:n}=e;return{[n]:{[`${n}-label > label`]:{height:t},[`${n}-control-input`]:{minHeight:t}}}},w=e=>{const{componentCls:t}=e;return{[e.componentCls]:Object.assign(Object.assign(Object.assign({},(0,p.dF)(e)),y(e)),{[`${t}-text`]:{display:"inline-block",paddingInlineEnd:e.paddingSM},"&-small":Object.assign({},x(e,e.controlHeightSM)),"&-large":Object.assign({},x(e,e.controlHeightLG))})}},C=e=>{const{formItemCls:t,iconCls:n,rootPrefixCls:o,antCls:r,labelRequiredMarkColor:i,labelColor:l,labelFontSize:a,labelHeight:s,labelColonMarginInlineStart:c,labelColonMarginInlineEnd:u,itemMarginBottom:d}=e;return{[t]:Object.assign(Object.assign({},(0,p.dF)(e)),{marginBottom:d,verticalAlign:"top","&-with-help":{transition:"none"},[`&-hidden,\n        &-hidden${r}-row`]:{display:"none"},"&-has-warning":{[`${t}-split`]:{color:e.colorError}},"&-has-error":{[`${t}-split`]:{color:e.colorWarning}},[`${t}-label`]:{flexGrow:0,overflow:"hidden",whiteSpace:"nowrap",textAlign:"end",verticalAlign:"middle","&-left":{textAlign:"start"},"&-wrap":{overflow:"unset",lineHeight:e.lineHeight,whiteSpace:"unset","> label":{verticalAlign:"middle",textWrap:"balance"}},"> label":{position:"relative",display:"inline-flex",alignItems:"center",maxWidth:"100%",height:s,color:l,fontSize:a,[`> ${n}`]:{fontSize:e.fontSize,verticalAlign:"top"},[`&${t}-required`]:{"&::before":{display:"inline-block",marginInlineEnd:e.marginXXS,color:i,fontSize:e.fontSize,fontFamily:"SimSun, sans-serif",lineHeight:1,content:'"*"'},[`&${t}-required-mark-hidden, &${t}-required-mark-optional`]:{"&::before":{display:"none"}}},[`${t}-optional`]:{display:"inline-block",marginInlineStart:e.marginXXS,color:e.colorTextDescription,[`&${t}-required-mark-hidden`]:{display:"none"}},[`${t}-tooltip`]:{color:e.colorTextDescription,cursor:"help",writingMode:"horizontal-tb",marginInlineStart:e.marginXXS},"&::after":{content:'":"',position:"relative",marginBlock:0,marginInlineStart:c,marginInlineEnd:u},[`&${t}-no-colon::after`]:{content:'"\\a0"'}}},[`${t}-control`]:{"--ant-display":"flex",flexDirection:"column",flexGrow:1,[`&:first-child:not([class^="'${o}-col-'"]):not([class*="' ${o}-col-'"])`]:{width:"100%"},"&-input":{position:"relative",display:"flex",alignItems:"center",minHeight:e.controlHeight,"&-content":{flex:"auto",maxWidth:"100%",[`&:has(> ${r}-switch:only-child, > ${r}-rate:only-child)`]:{display:"flex",alignItems:"center"}}}},[t]:{"&-additional":{display:"flex",flexDirection:"column"},"&-explain, &-extra":{clear:"both",color:e.colorTextDescription,fontSize:e.fontSize,lineHeight:e.lineHeight},"&-explain-connected":{width:"100%"},"&-extra":{minHeight:e.controlHeightSM,transition:`color ${e.motionDurationMid} ${e.motionEaseOut}`},"&-explain":{"&-error":{color:e.colorError},"&-warning":{color:e.colorWarning}}},[`&-with-help ${t}-explain`]:{height:"auto",opacity:1},[`${t}-feedback-icon`]:{fontSize:e.fontSize,textAlign:"center",visibility:"visible",animationName:m.nF,animationDuration:e.motionDurationMid,animationTimingFunction:e.motionEaseOutBack,pointerEvents:"none","&-success":{color:e.colorSuccess},"&-error":{color:e.colorError},"&-warning":{color:e.colorWarning},"&-validating":{color:e.colorPrimary}}})}},k=(e,t)=>{const{formItemCls:n}=e;return{[`${t}-horizontal`]:{[`${n}-label`]:{flexGrow:0},[`${n}-control`]:{flex:"1 1 0",minWidth:0},[`${n}-label[class$='-24'], ${n}-label[class*='-24 ']`]:{[`& + ${n}-control`]:{minWidth:"unset"}}}}},$=e=>{const{componentCls:t,formItemCls:n,inlineItemMarginBottom:o}=e;return{[`${t}-inline`]:{display:"flex",flexWrap:"wrap",[n]:{flex:"none",marginInlineEnd:e.margin,marginBottom:o,"&-row":{flexWrap:"nowrap"},[`> ${n}-label,\n        > ${n}-control`]:{display:"inline-block",verticalAlign:"top"},[`> ${n}-label`]:{flex:"none"},[`${t}-text`]:{display:"inline-block"},[`${n}-has-feedback`]:{display:"inline-block"}}}}},S=e=>({padding:e.verticalLabelPadding,margin:e.verticalLabelMargin,whiteSpace:"initial",textAlign:"start","> label":{margin:0,"&::after":{visibility:"hidden"}}}),A=e=>{const{componentCls:t,formItemCls:n,rootPrefixCls:o}=e;return{[`${n} ${n}-label`]:S(e),[`${t}:not(${t}-inline)`]:{[n]:{flexWrap:"wrap",[`${n}-label, ${n}-control`]:{[`&:not([class*=" ${o}-col-xs"])`]:{flex:"0 0 100%",maxWidth:"100%"}}}}}},E=e=>{const{componentCls:t,formItemCls:n,antCls:o}=e;return{[`${t}-vertical`]:{[`${n}:not(${n}-horizontal)`]:{[`${n}-row`]:{flexDirection:"column"},[`${n}-label > label`]:{height:"auto"},[`${n}-control`]:{width:"100%"},[`${n}-label,\n        ${o}-col-24${n}-label,\n        ${o}-col-xl-24${n}-label`]:S(e)}},[`@media (max-width: ${(0,f.zA)(e.screenXSMax)})`]:[A(e),{[t]:{[`${n}:not(${n}-horizontal)`]:{[`${o}-col-xs-24${n}-label`]:S(e)}}}],[`@media (max-width: ${(0,f.zA)(e.screenSMMax)})`]:{[t]:{[`${n}:not(${n}-horizontal)`]:{[`${o}-col-sm-24${n}-label`]:S(e)}}},[`@media (max-width: ${(0,f.zA)(e.screenMDMax)})`]:{[t]:{[`${n}:not(${n}-horizontal)`]:{[`${o}-col-md-24${n}-label`]:S(e)}}},[`@media (max-width: ${(0,f.zA)(e.screenLGMax)})`]:{[t]:{[`${n}:not(${n}-horizontal)`]:{[`${o}-col-lg-24${n}-label`]:S(e)}}}}},I=e=>{const{formItemCls:t,antCls:n}=e;return{[`${t}-vertical`]:{[`${t}-row`]:{flexDirection:"column"},[`${t}-label > label`]:{height:"auto"},[`${t}-control`]:{width:"100%"}},[`${t}-vertical ${t}-label,\n      ${n}-col-24${t}-label,\n      ${n}-col-xl-24${t}-label`]:S(e),[`@media (max-width: ${(0,f.zA)(e.screenXSMax)})`]:[A(e),{[t]:{[`${n}-col-xs-24${t}-label`]:S(e)}}],[`@media (max-width: ${(0,f.zA)(e.screenSMMax)})`]:{[t]:{[`${n}-col-sm-24${t}-label`]:S(e)}},[`@media (max-width: ${(0,f.zA)(e.screenMDMax)})`]:{[t]:{[`${n}-col-md-24${t}-label`]:S(e)}},[`@media (max-width: ${(0,f.zA)(e.screenLGMax)})`]:{[t]:{[`${n}-col-lg-24${t}-label`]:S(e)}}}},O=(e,t)=>(0,g.oX)(e,{formItemCls:`${e.componentCls}-item`,rootPrefixCls:t});var z=(0,v.OF)("Form",(e,{rootPrefixCls:t})=>{const n=O(e,t);return[w(n),C(n),b(n),k(n,n.componentCls),k(n,n.formItemCls),$(n),E(n),I(n),(0,h.A)(n),m.nF]},e=>({labelRequiredMarkColor:e.colorError,labelColor:e.colorTextHeading,labelFontSize:e.fontSize,labelHeight:e.controlHeight,labelColonMarginInlineStart:e.marginXXS/2,labelColonMarginInlineEnd:e.marginXS,itemMarginBottom:e.marginLG,verticalLabelPadding:`0 0 ${e.paddingXS}px`,verticalLabelMargin:0,inlineItemMarginBottom:0}),{order:-1e3});const M=[];function P(e,t,n,o=0){return{key:"string"==typeof e?e:`${t}-${o}`,error:e,errorStatus:n}}var T=({help:e,helpStatus:t,errors:n=M,warnings:l=M,className:f,fieldId:p,onVisibleChanged:m})=>{const{prefixCls:h}=i.useContext(o.hb),g=`${h}-item-explain`,v=(0,u.A)(h),[b,y,x]=z(h,v),w=i.useMemo(()=>(0,c.A)(h),[h]),C=d(n),k=d(l),$=i.useMemo(()=>null!=e?[P(e,"help",t)]:[].concat((0,r.A)(C.map((e,t)=>P(e,"error","error",t))),(0,r.A)(k.map((e,t)=>P(e,"warning","warning",t)))),[e,t,C,k]),S=i.useMemo(()=>{const e={};return $.forEach(({key:t})=>{e[t]=(e[t]||0)+1}),$.map((t,n)=>Object.assign(Object.assign({},t),{key:e[t.key]>1?`${t.key}-fallback-${n}`:t.key}))},[$]),A={};return p&&(A.id=`${p}_help`),b(i.createElement(s.Ay,{motionDeadline:w.motionDeadline,motionName:`${h}-show-help`,visible:!!S.length,onVisibleChanged:m},e=>{const{className:t,style:n}=e;return i.createElement("div",Object.assign({},A,{className:a()(g,t,x,v,f,y),style:n}),i.createElement(s.aF,Object.assign({keys:S},(0,c.A)(h),{motionName:`${h}-show-help-item`,component:!1}),e=>{const{key:t,error:n,errorStatus:o,className:r,style:l}=e;return i.createElement("div",{key:t,className:a()(r,{[`${g}-${o}`]:o}),style:l},n)}))}))},j=n(3592),N=n(2279),B=n(8119),R=n(829),D=n(8224),L=n(6588);const H=e=>"object"==typeof e&&null!=e&&1===e.nodeType,F=(e,t)=>(!t||"hidden"!==e)&&"visible"!==e&&"clip"!==e,_=(e,t)=>{if(e.clientHeight<e.scrollHeight||e.clientWidth<e.scrollWidth){const n=getComputedStyle(e,null);return F(n.overflowY,t)||F(n.overflowX,t)||(e=>{const t=(e=>{if(!e.ownerDocument||!e.ownerDocument.defaultView)return null;try{return e.ownerDocument.defaultView.frameElement}catch(e){return null}})(e);return!!t&&(t.clientHeight<e.scrollHeight||t.clientWidth<e.scrollWidth)})(e)}return!1},W=(e,t,n,o,r,i,l,a)=>i<e&&l>t||i>e&&l<t?0:i<=e&&a<=n||l>=t&&a>=n?i-e-o:l>t&&a<n||i<e&&a>n?l-t+r:0,V=e=>{const t=e.parentElement;return null==t?e.getRootNode().host||null:t},q=(e,t)=>{var n,o,r,i;if("undefined"==typeof document)return[];const{scrollMode:l,block:a,inline:s,boundary:c,skipOverflowHiddenElements:u}=t,d="function"==typeof c?c:e=>e!==c;if(!H(e))throw new TypeError("Invalid target");const f=document.scrollingElement||document.documentElement,p=[];let m=e;for(;H(m)&&d(m);){if(m=V(m),m===f){p.push(m);break}null!=m&&m===document.body&&_(m)&&!_(document.documentElement)||null!=m&&_(m,u)&&p.push(m)}const h=null!=(o=null==(n=window.visualViewport)?void 0:n.width)?o:innerWidth,g=null!=(i=null==(r=window.visualViewport)?void 0:r.height)?i:innerHeight,{scrollX:v,scrollY:b}=window,{height:y,width:x,top:w,right:C,bottom:k,left:$}=e.getBoundingClientRect(),{top:S,right:A,bottom:E,left:I}=(e=>{const t=window.getComputedStyle(e);return{top:parseFloat(t.scrollMarginTop)||0,right:parseFloat(t.scrollMarginRight)||0,bottom:parseFloat(t.scrollMarginBottom)||0,left:parseFloat(t.scrollMarginLeft)||0}})(e);let O="start"===a||"nearest"===a?w-S:"end"===a?k+E:w+y/2-S+E,z="center"===s?$+x/2-I+A:"end"===s?C+A:$-I;const M=[];for(let P=0;P<p.length;P++){const e=p[P],{height:t,width:n,top:o,right:r,bottom:i,left:c}=e.getBoundingClientRect();if("if-needed"===l&&w>=0&&$>=0&&k<=g&&C<=h&&(e===f&&!_(e)||w>=o&&k<=i&&$>=c&&C<=r))return M;const u=getComputedStyle(e),d=parseInt(u.borderLeftWidth,10),m=parseInt(u.borderTopWidth,10),S=parseInt(u.borderRightWidth,10),A=parseInt(u.borderBottomWidth,10);let E=0,I=0;const T="offsetWidth"in e?e.offsetWidth-e.clientWidth-d-S:0,j="offsetHeight"in e?e.offsetHeight-e.clientHeight-m-A:0,N="offsetWidth"in e?0===e.offsetWidth?0:n/e.offsetWidth:0,B="offsetHeight"in e?0===e.offsetHeight?0:t/e.offsetHeight:0;if(f===e)E="start"===a?O:"end"===a?O-g:"nearest"===a?W(b,b+g,g,m,A,b+O,b+O+y,y):O-g/2,I="start"===s?z:"center"===s?z-h/2:"end"===s?z-h:W(v,v+h,h,d,S,v+z,v+z+x,x),E=Math.max(0,E+b),I=Math.max(0,I+v);else{E="start"===a?O-o-m:"end"===a?O-i+A+j:"nearest"===a?W(o,i,t,m,A+j,O,O+y,y):O-(o+t/2)+j/2,I="start"===s?z-c-d:"center"===s?z-(c+n/2)+T/2:"end"===s?z-r+S+T:W(c,r,n,d,S+T,z,z+x,x);const{scrollLeft:l,scrollTop:u}=e;E=0===B?0:Math.max(0,Math.min(u+E/B,e.scrollHeight-t/B+j)),I=0===N?0:Math.max(0,Math.min(l+I/N,e.scrollWidth-n/N+T)),O+=u-E,z+=l-I}M.push({el:e,top:E,left:I})}return M};function U(e,t){if(!e.isConnected||!(e=>{let t=e;for(;t&&t.parentNode;){if(t.parentNode===document)return!0;t=t.parentNode instanceof ShadowRoot?t.parentNode.host:t.parentNode}return!1})(e))return;const n=(e=>{const t=window.getComputedStyle(e);return{top:parseFloat(t.scrollMarginTop)||0,right:parseFloat(t.scrollMarginRight)||0,bottom:parseFloat(t.scrollMarginBottom)||0,left:parseFloat(t.scrollMarginLeft)||0}})(e);if((e=>"object"==typeof e&&"function"==typeof e.behavior)(t))return t.behavior(q(e,t));const o="boolean"==typeof t||null==t?void 0:t.behavior;for(const{el:r,top:i,left:l}of q(e,(e=>!1===e?{block:"end",inline:"nearest"}:(e=>e===Object(e)&&0!==Object.keys(e).length)(e)?e:{block:"start",inline:"nearest"})(t))){const e=i-n.top+n.bottom,t=l-n.left+n.right;r.scroll({top:e,left:t,behavior:o})}}const X=["parentNode"];function G(e){return void 0===e||!1===e?[]:Array.isArray(e)?e:[e]}function K(e,t){if(!e.length)return;const n=e.join("_");if(t)return`${t}_${n}`;return X.includes(n)?`form_item_${n}`:n}function Y(e,t,n,o,r,i){let l=o;return void 0!==i?l=i:n.validating?l="validating":e.length?l="error":t.length?l="warning":(n.touched||r&&n.validated)&&(l="success"),l}var Q=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};function Z(e){return G(e).join("_")}function J(e,t){const n=t.getFieldInstance(e),o=(0,L.rb)(n);if(o)return o;const r=K(G(e),t.__INTERNAL__.name);return r?document.getElementById(r):void 0}function ee(e){const[t]=(0,j.mN)(),n=i.useRef({}),o=i.useMemo(()=>null!=e?e:Object.assign(Object.assign({},t),{__INTERNAL__:{itemRef:e=>t=>{const o=Z(e);t?n.current[o]=t:delete n.current[o]}},scrollToField:(e,t={})=>{const{focus:n}=t,r=Q(t,["focus"]),i=J(e,o);i&&(U(i,Object.assign({scrollMode:"if-needed",block:"nearest"},r)),n&&o.focusField(e))},focusField:e=>{var t,n;const r=o.getFieldInstance(e);"function"==typeof(null==r?void 0:r.focus)?r.focus():null===(n=null===(t=J(e,o))||void 0===t?void 0:t.focus)||void 0===n||n.call(t)},getFieldInstance:e=>{const t=Z(e);return n.current[t]}}),[e,t]);return[o]}var te=n(9407),ne=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const oe=(e,t)=>{const n=i.useContext(B.A),{getPrefixCls:r,direction:l,requiredMark:s,colon:c,scrollToFirstError:d,className:f,style:p}=(0,N.TP)("form"),{prefixCls:m,className:h,rootClassName:g,size:v,disabled:b=n,form:y,colon:x,labelAlign:w,labelWrap:C,labelCol:k,wrapperCol:$,hideRequiredMark:S,layout:A="horizontal",scrollToFirstError:E,requiredMark:I,onFinishFailed:O,name:M,style:P,feedbackIcons:T,variant:L}=e,H=ne(e,["prefixCls","className","rootClassName","size","disabled","form","colon","labelAlign","labelWrap","labelCol","wrapperCol","hideRequiredMark","layout","scrollToFirstError","requiredMark","onFinishFailed","name","style","feedbackIcons","variant"]),F=(0,R.A)(v),_=i.useContext(te.A);const W=i.useMemo(()=>void 0!==I?I:!S&&(void 0===s||s),[S,I,s]),V=null!=x?x:c,q=r("form",m),U=(0,u.A)(q),[X,G,K]=z(q,U),Y=a()(q,`${q}-${A}`,{[`${q}-hide-required-mark`]:!1===W,[`${q}-rtl`]:"rtl"===l,[`${q}-${F}`]:F},K,U,G,f,h,g),[Q]=ee(y),{__INTERNAL__:Z}=Q;Z.name=M;const J=i.useMemo(()=>({name:M,labelAlign:w,labelCol:k,labelWrap:C,wrapperCol:$,vertical:"vertical"===A,colon:V,requiredMark:W,itemRef:Z.itemRef,form:Q,feedbackIcons:T}),[M,w,k,$,A,V,W,Q,T]),oe=i.useRef(null);i.useImperativeHandle(t,()=>{var e;return Object.assign(Object.assign({},Q),{nativeElement:null===(e=oe.current)||void 0===e?void 0:e.nativeElement})});const re=(e,t)=>{if(e){let n={block:"nearest"};"object"==typeof e&&(n=Object.assign(Object.assign({},n),e)),Q.scrollToField(t,n)}};return X(i.createElement(o.Pp.Provider,{value:L},i.createElement(B.X,{disabled:b},i.createElement(D.A.Provider,{value:F},i.createElement(o.Op,{validateMessages:_},i.createElement(o.cK.Provider,{value:J},i.createElement(j.Ay,Object.assign({id:M},H,{name:M,onFinishFailed:e=>{if(null==O||O(e),e.errorFields.length){const t=e.errorFields[0].name;if(void 0!==E)return void re(E,t);void 0!==d&&re(d,t)}},form:Q,ref:oe,style:Object.assign(Object.assign({},p),P),className:Y}))))))))};var re=i.forwardRef(oe),ie=n(1233),le=n(8719),ae=n(682),se=n(8877),ce=n(2546);const ue=()=>{const{status:e,errors:t=[],warnings:n=[]}=i.useContext(o.$W);return{status:e,errors:t,warnings:n}};ue.Context=o.$W;var de=ue,fe=n(5371);var pe=n(2467),me=n(981),he=n(9853),ge=n(1320),ve=n(4849);const be=["xxl","xl","lg","md","sm","xs"];var ye=()=>{const[,e]=(0,ge.Ay)(),t=(e=>({xs:`(max-width: ${e.screenXSMax}px)`,sm:`(min-width: ${e.screenSM}px)`,md:`(min-width: ${e.screenMD}px)`,lg:`(min-width: ${e.screenLG}px)`,xl:`(min-width: ${e.screenXL}px)`,xxl:`(min-width: ${e.screenXXL}px)`}))((e=>{const t=e,n=[].concat(be).reverse();return n.forEach((e,o)=>{const r=e.toUpperCase(),i=`screen${r}Min`,l=`screen${r}`;if(!(t[i]<=t[l]))throw new Error(`${i}<=${l} fails : !(${t[i]}<=${t[l]})`);if(o<n.length-1){const e=`screen${r}Max`;if(!(t[l]<=t[e]))throw new Error(`${l}<=${e} fails : !(${t[l]}<=${t[e]})`);const i=`screen${n[o+1].toUpperCase()}Min`;if(!(t[e]<=t[i]))throw new Error(`${e}<=${i} fails : !(${t[e]}<=${t[i]})`)}}),e})(e));return i.useMemo(()=>{const e=new Map;let n=-1,o={};return{responsiveMap:t,matchHandlers:{},dispatch(t){return o=t,e.forEach(e=>e(o)),e.size>=1},subscribe(t){return e.size||this.register(),n+=1,e.set(n,t),t(o),n},unsubscribe(t){e.delete(t),e.size||this.unregister()},register(){Object.entries(t).forEach(([e,t])=>{const n=({matches:t})=>{this.dispatch(Object.assign(Object.assign({},o),{[e]:t}))},r=window.matchMedia(t);(0,ve.e)(r,n),this.matchHandlers[t]={mql:r,listener:n},n(r)})},unregister(){Object.values(t).forEach(e=>{const t=this.matchHandlers[e];(0,ve.p)(null==t?void 0:t.mql,null==t?void 0:t.listener)}),e.clear()}}},[e])},xe=n(7447);var we=function(e=!0,t={}){const n=(0,i.useRef)(t),o=(0,xe.A)(),r=ye();return(0,me.A)(()=>{const t=r.subscribe(t=>{n.current=t,e&&o()});return()=>r.unsubscribe(t)},[]),n.current};var Ce=(0,i.createContext)({}),ke=n(5006),$e=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};function Se(e,t){const[n,o]=i.useState("string"==typeof e?e:"");return i.useEffect(()=>{(()=>{if("string"==typeof e&&o(e),"object"==typeof e)for(let n=0;n<be.length;n++){const r=be[n];if(!t||!t[r])continue;const i=e[r];if(void 0!==i)return void o(i)}})()},[JSON.stringify(e),t]),n}const Ae=i.forwardRef((e,t)=>{const{prefixCls:n,justify:o,align:r,className:l,style:s,children:c,gutter:u=0,wrap:d}=e,f=$e(e,["prefixCls","justify","align","className","style","children","gutter","wrap"]),{getPrefixCls:p,direction:m}=i.useContext(N.QO),h=we(!0,null),g=Se(r,h),v=Se(o,h),b=p("row",n),[y,x,w]=(0,ke.L3)(b),C=function(e,t){const n=[void 0,void 0],o=Array.isArray(e)?e:[e,void 0],r=t||{xs:!0,sm:!0,md:!0,lg:!0,xl:!0,xxl:!0};return o.forEach((e,t)=>{if("object"==typeof e&&null!==e)for(let o=0;o<be.length;o++){const i=be[o];if(r[i]&&void 0!==e[i]){n[t]=e[i];break}}else n[t]=e}),n}(u,h),k=a()(b,{[`${b}-no-wrap`]:!1===d,[`${b}-${v}`]:v,[`${b}-${g}`]:g,[`${b}-rtl`]:"rtl"===m},l,x,w),$={},S=null!=C[0]&&C[0]>0?C[0]/-2:void 0;S&&($.marginLeft=S,$.marginRight=S);const[A,E]=C;$.rowGap=E;const I=i.useMemo(()=>({gutter:[A,E],wrap:d}),[A,E,d]);return y(i.createElement(Ce.Provider,{value:I},i.createElement("div",Object.assign({},f,{className:k,style:Object.assign(Object.assign({},$),s),ref:t}),c)))});var Ee=Ae,Ie=n(1470),Oe=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};function ze(e){return"number"==typeof e?`${e} ${e} auto`:/^\d+(\.\d+)?(px|em|rem|%)$/.test(e)?`0 0 ${e}`:e}const Me=["xs","sm","md","lg","xl","xxl"],Pe=i.forwardRef((e,t)=>{const{getPrefixCls:n,direction:o}=i.useContext(N.QO),{gutter:r,wrap:l}=i.useContext(Ce),{prefixCls:s,span:c,order:u,offset:d,push:f,pull:p,className:m,children:h,flex:g,style:v}=e,b=Oe(e,["prefixCls","span","order","offset","push","pull","className","children","flex","style"]),y=n("col",s),[x,w,C]=(0,ke.xV)(y),k={};let $={};Me.forEach(t=>{let n={};const r=e[t];"number"==typeof r?n.span=r:"object"==typeof r&&(n=r||{}),delete b[t],$=Object.assign(Object.assign({},$),{[`${y}-${t}-${n.span}`]:void 0!==n.span,[`${y}-${t}-order-${n.order}`]:n.order||0===n.order,[`${y}-${t}-offset-${n.offset}`]:n.offset||0===n.offset,[`${y}-${t}-push-${n.push}`]:n.push||0===n.push,[`${y}-${t}-pull-${n.pull}`]:n.pull||0===n.pull,[`${y}-rtl`]:"rtl"===o}),n.flex&&($[`${y}-${t}-flex`]=!0,k[`--${y}-${t}-flex`]=ze(n.flex))});const S=a()(y,{[`${y}-${c}`]:void 0!==c,[`${y}-order-${u}`]:u,[`${y}-offset-${d}`]:d,[`${y}-push-${f}`]:f,[`${y}-pull-${p}`]:p},m,$,w,C),A={};if(r&&r[0]>0){const e=r[0]/2;A.paddingLeft=e,A.paddingRight=e}return g&&(A.flex=ze(g),!1!==l||A.minWidth||(A.minWidth=0)),x(i.createElement("div",Object.assign({},b,{style:Object.assign(Object.assign(Object.assign({},A),v),k),className:S,ref:t}),h))});var Te=Pe;const je=e=>{const{formItemCls:t}=e;return{"@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none)":{[`${t}-control`]:{display:"flex"}}}};var Ne=(0,v.bf)(["Form","item-item"],(e,{rootPrefixCls:t})=>{const n=O(e,t);return[je(n)]}),Be=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};var Re=e=>{const{prefixCls:t,status:n,labelCol:r,wrapperCol:l,children:s,errors:c,warnings:u,_internalItemRender:d,extra:f,help:p,fieldId:m,marginBottom:h,onErrorVisibleChanged:g,label:v}=e,b=`${t}-item`,y=i.useContext(o.cK),x=i.useMemo(()=>{let e=Object.assign({},l||y.wrapperCol||{});if(null===v&&!r&&!l&&y.labelCol){[void 0,"xs","sm","md","lg","xl","xxl"].forEach(t=>{const n=t?[t]:[],o=(0,Ie.Jt)(y.labelCol,n),r="object"==typeof o?o:{},i=(0,Ie.Jt)(e,n);"span"in r&&!("offset"in("object"==typeof i?i:{}))&&r.span<24&&(e=(0,Ie.hZ)(e,[].concat(n,["offset"]),r.span))})}return e},[l,y]),w=a()(`${b}-control`,x.className),C=i.useMemo(()=>{const{labelCol:e,wrapperCol:t}=y;return Be(y,["labelCol","wrapperCol"])},[y]),k=i.useRef(null),[$,S]=i.useState(0);(0,me.A)(()=>{f&&k.current?S(k.current.clientHeight):S(0)},[f]);const A=i.createElement("div",{className:`${b}-control-input`},i.createElement("div",{className:`${b}-control-input-content`},s)),E=i.useMemo(()=>({prefixCls:t,status:n}),[t,n]),I=null!==h||c.length||u.length?i.createElement(o.hb.Provider,{value:E},i.createElement(T,{fieldId:m,errors:c,warnings:u,help:p,helpStatus:n,className:`${b}-explain-connected`,onVisibleChanged:g})):null,O={};m&&(O.id=`${m}_extra`);const z=f?i.createElement("div",Object.assign({},O,{className:`${b}-extra`,ref:k}),f):null,M=I||z?i.createElement("div",{className:`${b}-additional`,style:h?{minHeight:h+$}:{}},I,z):null,P=d&&"pro_table_render"===d.mark&&d.render?d.render(e,{input:A,errorList:I,extra:z}):i.createElement(i.Fragment,null,A,M);return i.createElement(o.cK.Provider,{value:C},i.createElement(Te,Object.assign({},x,{className:w}),P),i.createElement(Ne,{prefixCls:t}))},De=n(8168),Le={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M623.6 316.7C593.6 290.4 554 276 512 276s-81.6 14.5-111.6 40.7C369.2 344 352 380.7 352 420v7.6c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V420c0-44.1 43.1-80 96-80s96 35.9 96 80c0 31.1-22 59.6-56.1 72.7-21.2 8.1-39.2 22.3-52.1 40.9-13.1 19-19.9 41.8-19.9 64.9V620c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8v-22.7a48.3 48.3 0 0130.9-44.8c59-22.7 97.1-74.7 97.1-132.5.1-39.3-17.1-76-48.3-103.3zM472 732a40 40 0 1080 0 40 40 0 10-80 0z"}}]},name:"question-circle",theme:"outlined"},He=n(7064),Fe=function(e,t){return i.createElement(He.A,(0,De.A)({},e,{ref:t,icon:Le}))};var _e=i.forwardRef(Fe);var We=function(e){return null==e?null:"object"!=typeof e||(0,i.isValidElement)(e)?{title:e}:e},Ve=n(9155),qe=n(5678),Ue=n(367),Xe=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};var Ge=({prefixCls:e,label:t,htmlFor:n,labelCol:r,labelAlign:l,colon:s,required:c,requiredMark:u,tooltip:d,vertical:f})=>{var p;const[m]=(0,Ve.A)("Form"),{labelAlign:h,labelCol:g,labelWrap:v,colon:b}=i.useContext(o.cK);if(!t)return null;const y=r||g||{},x=l||h,w=`${e}-item-label`,C=a()(w,"left"===x&&`${w}-left`,y.className,{[`${w}-wrap`]:!!v});let k=t;const $=!0===s||!1!==b&&!1!==s;$&&!f&&"string"==typeof t&&t.trim()&&(k=t.replace(/[:|：]\s*$/,""));const S=We(d);if(S){const{icon:t=i.createElement(_e,null)}=S,n=Xe(S,["icon"]),o=i.createElement(Ue.A,Object.assign({},n),i.cloneElement(t,{className:`${e}-item-tooltip`,title:"",onClick:e=>{e.preventDefault()},tabIndex:null}));k=i.createElement(i.Fragment,null,k,o)}const A="optional"===u,E="function"==typeof u,I=!1===u;let O;E?k=u(k,{required:!!c}):A&&!c&&(k=i.createElement(i.Fragment,null,k,i.createElement("span",{className:`${e}-item-optional`,title:""},(null==m?void 0:m.optional)||(null===(p=qe.A.Form)||void 0===p?void 0:p.optional)))),I?O="hidden":(A||E)&&(O="optional");const z=a()({[`${e}-item-required`]:c,[`${e}-item-required-mark-${O}`]:O,[`${e}-item-no-colon`]:!$});return i.createElement(Te,Object.assign({},y,{className:C}),i.createElement("label",{htmlFor:n,className:z,title:"string"==typeof t?t:""},k))},Ke=n(8811),Ye=n(6029),Qe=n(7541),Ze=n(3567);const Je={success:Ke.A,warning:Qe.A,error:Ye.A,validating:Ze.A};function et({children:e,errors:t,warnings:n,hasFeedback:r,validateStatus:l,prefixCls:s,meta:c,noStyle:u,name:d}){const f=`${s}-item`,{feedbackIcons:p}=i.useContext(o.cK),m=Y(t,n,c,null,!!r,l),{isFormItemInput:h,status:g,hasFeedback:v,feedbackIcon:b,name:y}=i.useContext(o.$W),x=i.useMemo(()=>{var e;let o;if(r){const l=!0!==r&&r.icons||p,s=m&&(null===(e=null==l?void 0:l({status:m,errors:t,warnings:n}))||void 0===e?void 0:e[m]),c=m&&Je[m];o=!1!==s&&c?i.createElement("span",{className:a()(`${f}-feedback-icon`,`${f}-feedback-icon-${m}`)},s||i.createElement(c,null)):null}const l={status:m||"",errors:t,warnings:n,hasFeedback:!!r,feedbackIcon:o,isFormItemInput:!0,name:d};return u&&(l.status=(null!=m?m:g)||"",l.isFormItemInput=h,l.hasFeedback=!!(null!=r?r:v),l.feedbackIcon=void 0!==r?l.feedbackIcon:b,l.name=null!=d?d:y),l},[m,r,u,h,g]);return i.createElement(o.$W.Provider,{value:x},e)}var tt=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};function nt(e){const{prefixCls:t,className:n,rootClassName:r,style:l,help:s,errors:c,warnings:u,validateStatus:f,meta:p,hasFeedback:m,hidden:h,children:g,fieldId:v,required:b,isRequired:y,onSubItemMetaChange:x,layout:w,name:C}=e,k=tt(e,["prefixCls","className","rootClassName","style","help","errors","warnings","validateStatus","meta","hasFeedback","hidden","children","fieldId","required","isRequired","onSubItemMetaChange","layout","name"]),$=`${t}-item`,{requiredMark:S,vertical:A}=i.useContext(o.cK),E=A||"vertical"===w,I=i.useRef(null),O=d(c),z=d(u),M=null!=s,P=!!(M||c.length||u.length),T=!!I.current&&(0,pe.A)(I.current),[j,N]=i.useState(null);(0,me.A)(()=>{if(P&&I.current){const e=getComputedStyle(I.current);N(parseInt(e.marginBottom,10))}},[P,T]);const B=((e=!1)=>Y(e?O:p.errors,e?z:p.warnings,p,"",!!m,f))(),R=a()($,n,r,{[`${$}-with-help`]:M||O.length||z.length,[`${$}-has-feedback`]:B&&m,[`${$}-has-success`]:"success"===B,[`${$}-has-warning`]:"warning"===B,[`${$}-has-error`]:"error"===B,[`${$}-is-validating`]:"validating"===B,[`${$}-hidden`]:h,[`${$}-${w}`]:w});return i.createElement("div",{className:R,style:l,ref:I},i.createElement(Ee,Object.assign({className:`${$}-row`},(0,he.A)(k,["_internalItemRender","colon","dependencies","extra","fieldKey","getValueFromEvent","getValueProps","htmlFor","id","initialValue","isListField","label","labelAlign","labelCol","labelWrap","messageVariables","name","normalize","noStyle","preserve","requiredMark","rules","shouldUpdate","trigger","tooltip","validateFirst","validateTrigger","valuePropName","wrapperCol","validateDebounce"])),i.createElement(Ge,Object.assign({htmlFor:v},e,{requiredMark:S,required:null!=b?b:y,prefixCls:t,vertical:E})),i.createElement(Re,Object.assign({},e,p,{errors:O,warnings:z,prefixCls:t,status:B,help:s,marginBottom:j,onErrorVisibleChanged:e=>{e||N(null)}}),i.createElement(o.jC.Provider,{value:x},i.createElement(et,{prefixCls:t,meta:p,errors:p.errors,warnings:p.warnings,hasFeedback:m,validateStatus:B,name:C},g)))),!!j&&i.createElement("div",{className:`${$}-margin-offset`,style:{marginBottom:-j}}))}const ot=i.memo(({children:e})=>e,(e,t)=>function(e,t){const n=Object.keys(e),o=Object.keys(t);return n.length===o.length&&n.every(n=>{const o=e[n],r=t[n];return o===r||"function"==typeof o||"function"==typeof r})}(e.control,t.control)&&e.update===t.update&&e.childProps.length===t.childProps.length&&e.childProps.every((e,n)=>e===t.childProps[n]));const rt=function(e){const{name:t,noStyle:n,className:l,dependencies:s,prefixCls:c,shouldUpdate:d,rules:f,children:p,required:m,label:h,messageVariables:g,trigger:v="onChange",validateTrigger:b,hidden:y,help:x,layout:w}=e,{getPrefixCls:C}=i.useContext(N.QO),{name:k}=i.useContext(o.cK),$=function(e){if("function"==typeof e)return e;const t=(0,ce.A)(e);return t.length<=1?t[0]:t}(p),S="function"==typeof $,A=i.useContext(o.jC),{validateTrigger:E}=i.useContext(j._z),I=void 0!==b?b:E,O=!(null==t),M=C("form",c),P=(0,u.A)(M),[T,B,R]=z(M,P);(0,se.rJ)("Form.Item");const D=i.useContext(j.EF),L=i.useRef(null),[H,F]=function(e){const[t,n]=i.useState(e),o=i.useRef(null),r=i.useRef([]),l=i.useRef(!1);return i.useEffect(()=>(l.current=!1,()=>{l.current=!0,fe.A.cancel(o.current),o.current=null}),[]),[t,function(e){l.current||(null===o.current&&(r.current=[],o.current=(0,fe.A)(()=>{o.current=null,n(e=>{let t=e;return r.current.forEach(e=>{t=e(t)}),t})})),r.current.push(e))}]}({}),[_,W]=(0,ie.A)(()=>({errors:[],warnings:[],touched:!1,validating:!1,name:[],validated:!1})),V=(e,t)=>{F(n=>{const o=Object.assign({},n),i=[].concat((0,r.A)(e.name.slice(0,-1)),(0,r.A)(t)).join("__SPLIT__");return e.destroy?delete o[i]:o[i]=e,o})},[q,U]=i.useMemo(()=>{const e=(0,r.A)(_.errors),t=(0,r.A)(_.warnings);return Object.values(H).forEach(n=>{e.push.apply(e,(0,r.A)(n.errors||[])),t.push.apply(t,(0,r.A)(n.warnings||[]))}),[e,t]},[H,_.errors,_.warnings]),X=function(){const{itemRef:e}=i.useContext(o.cK),t=i.useRef({});return function(n,o){const r=o&&"object"==typeof o&&(0,le.A9)(o),i=n.join("_");return t.current.name===i&&t.current.originRef===r||(t.current.name=i,t.current.originRef=r,t.current.ref=(0,le.K4)(e(n),r)),t.current.ref}}();function Y(o,r,s){return n&&!y?i.createElement(et,{prefixCls:M,hasFeedback:e.hasFeedback,validateStatus:e.validateStatus,meta:_,errors:q,warnings:U,noStyle:!0,name:t},o):i.createElement(nt,Object.assign({key:"row"},e,{className:a()(l,R,P,B),prefixCls:M,fieldId:r,isRequired:s,errors:q,warnings:U,meta:_,onSubItemMetaChange:V,layout:w,name:t}),o)}if(!O&&!S&&!s)return T(Y($));let Q={};return"string"==typeof h?Q.label=h:t&&(Q.label=String(t)),g&&(Q=Object.assign(Object.assign({},Q),g)),T(i.createElement(j.D0,Object.assign({},e,{messageVariables:Q,trigger:v,validateTrigger:I,onMetaChange:e=>{const t=null==D?void 0:D.getKey(e.name);if(W(e.destroy?{errors:[],warnings:[],touched:!1,validating:!1,name:[],validated:!1}:e,!0),n&&!1!==x&&A){let n=e.name;if(e.destroy)n=L.current||n;else if(void 0!==t){const[e,o]=t;n=[e].concat((0,r.A)(o)),L.current=n}A(e,n)}}}),(n,o,l)=>{const a=G(t).length&&o?o.name:[],c=K(a,k),u=void 0!==m?m:!!(null==f?void 0:f.some(e=>{if(e&&"object"==typeof e&&e.required&&!e.warningOnly)return!0;if("function"==typeof e){const t=e(l);return(null==t?void 0:t.required)&&!(null==t?void 0:t.warningOnly)}return!1})),p=Object.assign({},n);let h=null;if(Array.isArray($)&&O)h=$;else if(S&&(!d&&!s||O));else if(!s||S||O)if(i.isValidElement($)){const t=Object.assign(Object.assign({},$.props),p);if(t.id||(t.id=c),x||q.length>0||U.length>0||e.extra){const n=[];(x||q.length>0)&&n.push(`${c}_help`),e.extra&&n.push(`${c}_extra`),t["aria-describedby"]=n.join(" ")}q.length>0&&(t["aria-invalid"]="true"),u&&(t["aria-required"]="true"),(0,le.f3)($)&&(t.ref=X(a,$));new Set([].concat((0,r.A)(G(v)),(0,r.A)(G(I)))).forEach(e=>{t[e]=(...t)=>{var n,o,r,i,l;null===(r=p[e])||void 0===r||(n=r).call.apply(n,[p].concat(t)),null===(l=(i=$.props)[e])||void 0===l||(o=l).call.apply(o,[i].concat(t))}});const n=[t["aria-required"],t["aria-invalid"],t["aria-describedby"]];h=i.createElement(ot,{control:p,update:$,childProps:n},(0,ae.Ob)($,t))}else h=S&&(d||s)&&!O?$(l):$;else;return Y(h,c,u)}))};rt.useStatus=de;var it=rt,lt=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};var at=e=>{var{prefixCls:t,children:n}=e,r=lt(e,["prefixCls","children"]);const{getPrefixCls:l}=i.useContext(N.QO),a=l("form",t),s=i.useMemo(()=>({prefixCls:a,status:"error"}),[a]);return i.createElement(j.B8,Object.assign({},r),(e,t,r)=>i.createElement(o.hb.Provider,{value:s},n(e.map(e=>Object.assign(Object.assign({},e),{fieldKey:e.key})),t,{errors:r.errors,warnings:r.warnings})))};const st=re;st.Item=it,st.List=at,st.ErrorList=T,st.useForm=ee,st.useFormInstance=function(){const{form:e}=i.useContext(o.cK);return e},st.useWatch=j.FH,st.Provider=o.Op,st.create=()=>{};var ct=st},3766:function(e,t,n){"use strict";n.d(t,{Z:function(){return k},A:function(){return O}});var o=n(8168),r=n(5544),i=n(2595),l=n(6540),a=l.createContext({}),s=n(9379),c=n(6942),u=n.n(c),d=n(4808),f=n(6855),p=n(6928),m=n(2065);function h(e,t,n){var o=t;return!o&&n&&(o="".concat(e,"-").concat(n)),o}function g(e,t){var n=e["page".concat(t?"Y":"X","Offset")],o="scroll".concat(t?"Top":"Left");if("number"!=typeof n){var r=e.document;"number"!=typeof(n=r.documentElement[o])&&(n=r.body[o])}return n}var v=n(754),b=n(2284),y=n(8719),x=l.memo(function(e){return e.children},function(e,t){return!t.shouldUpdate}),w={width:0,height:0,overflow:"hidden",outline:"none"},C={outline:"none"};var k=l.forwardRef(function(e,t){var n=e.prefixCls,r=e.className,i=e.style,c=e.title,d=e.ariaId,f=e.footer,p=e.closable,h=e.closeIcon,g=e.onClose,v=e.children,k=e.bodyStyle,$=e.bodyProps,S=e.modalRender,A=e.onMouseDown,E=e.onMouseUp,I=e.holderRef,O=e.visible,z=e.forceRender,M=e.width,P=e.height,T=e.classNames,j=e.styles,N=l.useContext(a).panel,B=(0,y.xK)(I,N),R=(0,l.useRef)(),D=(0,l.useRef)();l.useImperativeHandle(t,function(){return{focus:function(){var e;null===(e=R.current)||void 0===e||e.focus({preventScroll:!0})},changeActive:function(e){var t=document.activeElement;e&&t===D.current?R.current.focus({preventScroll:!0}):e||t!==R.current||D.current.focus({preventScroll:!0})}}});var L={};void 0!==M&&(L.width=M),void 0!==P&&(L.height=P);var H=f?l.createElement("div",{className:u()("".concat(n,"-footer"),null==T?void 0:T.footer),style:(0,s.A)({},null==j?void 0:j.footer)},f):null,F=c?l.createElement("div",{className:u()("".concat(n,"-header"),null==T?void 0:T.header),style:(0,s.A)({},null==j?void 0:j.header)},l.createElement("div",{className:"".concat(n,"-title"),id:d},c)):null,_=(0,l.useMemo)(function(){return"object"===(0,b.A)(p)&&null!==p?p:p?{closeIcon:null!=h?h:l.createElement("span",{className:"".concat(n,"-close-x")})}:{}},[p,h,n]),W=(0,m.A)(_,!0),V="object"===(0,b.A)(p)&&p.disabled,q=p?l.createElement("button",(0,o.A)({type:"button",onClick:g,"aria-label":"Close"},W,{className:"".concat(n,"-close"),disabled:V}),_.closeIcon):null,U=l.createElement("div",{className:u()("".concat(n,"-content"),null==T?void 0:T.content),style:null==j?void 0:j.content},q,F,l.createElement("div",(0,o.A)({className:u()("".concat(n,"-body"),null==T?void 0:T.body),style:(0,s.A)((0,s.A)({},k),null==j?void 0:j.body)},$),v),H);return l.createElement("div",{key:"dialog-element",role:"dialog","aria-labelledby":c?d:null,"aria-modal":"true",ref:B,style:(0,s.A)((0,s.A)({},i),L),className:u()(n,r),onMouseDown:A,onMouseUp:E},l.createElement("div",{ref:R,tabIndex:0,style:C},l.createElement(x,{shouldUpdate:O||z},S?S(U):U)),l.createElement("div",{tabIndex:0,ref:D,style:w}))}),$=l.forwardRef(function(e,t){var n=e.prefixCls,i=e.title,a=e.style,c=e.className,d=e.visible,f=e.forceRender,p=e.destroyOnClose,m=e.motionName,h=e.ariaId,b=e.onVisibleChanged,y=e.mousePosition,x=(0,l.useRef)(),w=l.useState(),C=(0,r.A)(w,2),$=C[0],S=C[1],A={};function E(){var e,t,n,o,r,i=(e=x.current,t=e.getBoundingClientRect(),n={left:t.left,top:t.top},o=e.ownerDocument,r=o.defaultView||o.parentWindow,n.left+=g(r),n.top+=g(r,!0),n);S(y&&(y.x||y.y)?"".concat(y.x-i.left,"px ").concat(y.y-i.top,"px"):"")}return $&&(A.transformOrigin=$),l.createElement(v.Ay,{visible:d,onVisibleChanged:b,onAppearPrepare:E,onEnterPrepare:E,forceRender:f,motionName:m,removeOnLeave:p,ref:x},function(r,d){var f=r.className,p=r.style;return l.createElement(k,(0,o.A)({},e,{ref:t,title:i,ariaId:h,prefixCls:n,holderRef:d,style:(0,s.A)((0,s.A)((0,s.A)({},p),a),A),className:u()(c,f)}))})});$.displayName="Content";var S=$,A=function(e){var t=e.prefixCls,n=e.style,r=e.visible,i=e.maskProps,a=e.motionName,c=e.className;return l.createElement(v.Ay,{key:"mask",visible:r,motionName:a,leavedClassName:"".concat(t,"-mask-hidden")},function(e,r){var a=e.className,d=e.style;return l.createElement("div",(0,o.A)({ref:r,style:(0,s.A)((0,s.A)({},d),n),className:u()("".concat(t,"-mask"),a,c)},i))})},E=(n(8210),function(e){var t=e.prefixCls,n=void 0===t?"rc-dialog":t,i=e.zIndex,a=e.visible,c=void 0!==a&&a,g=e.keyboard,v=void 0===g||g,b=e.focusTriggerAfterClose,y=void 0===b||b,x=e.wrapStyle,w=e.wrapClassName,C=e.wrapProps,k=e.onClose,$=e.afterOpenChange,E=e.afterClose,I=e.transitionName,O=e.animation,z=e.closable,M=void 0===z||z,P=e.mask,T=void 0===P||P,j=e.maskTransitionName,N=e.maskAnimation,B=e.maskClosable,R=void 0===B||B,D=e.maskStyle,L=e.maskProps,H=e.rootClassName,F=e.classNames,_=e.styles;var W=(0,l.useRef)(),V=(0,l.useRef)(),q=(0,l.useRef)(),U=l.useState(c),X=(0,r.A)(U,2),G=X[0],K=X[1],Y=(0,f.A)();function Q(e){null==k||k(e)}var Z=(0,l.useRef)(!1),J=(0,l.useRef)(),ee=null;R&&(ee=function(e){Z.current?Z.current=!1:V.current===e.target&&Q(e)}),(0,l.useEffect)(function(){c&&(K(!0),(0,d.A)(V.current,document.activeElement)||(W.current=document.activeElement))},[c]),(0,l.useEffect)(function(){return function(){clearTimeout(J.current)}},[]);var te=(0,s.A)((0,s.A)((0,s.A)({zIndex:i},x),null==_?void 0:_.wrapper),{},{display:G?null:"none"});return l.createElement("div",(0,o.A)({className:u()("".concat(n,"-root"),H)},(0,m.A)(e,{data:!0})),l.createElement(A,{prefixCls:n,visible:T&&c,motionName:h(n,j,N),style:(0,s.A)((0,s.A)({zIndex:i},D),null==_?void 0:_.mask),maskProps:L,className:null==F?void 0:F.mask}),l.createElement("div",(0,o.A)({tabIndex:-1,onKeyDown:function(e){if(v&&e.keyCode===p.A.ESC)return e.stopPropagation(),void Q(e);c&&e.keyCode===p.A.TAB&&q.current.changeActive(!e.shiftKey)},className:u()("".concat(n,"-wrap"),w,null==F?void 0:F.wrapper),ref:V,onClick:ee,style:te},C),l.createElement(S,(0,o.A)({},e,{onMouseDown:function(){clearTimeout(J.current),Z.current=!0},onMouseUp:function(){J.current=setTimeout(function(){Z.current=!1})},ref:q,closable:M,ariaId:Y,prefixCls:n,visible:c&&G,onClose:Q,onVisibleChanged:function(e){if(e)(0,d.A)(V.current,document.activeElement)||null===(t=q.current)||void 0===t||t.focus();else{if(K(!1),T&&W.current&&y){try{W.current.focus({preventScroll:!0})}catch(n){}W.current=null}G&&(null==E||E())}var t;null==$||$(e)},motionName:h(n,I,O)}))))}),I=function(e){var t=e.visible,n=e.getContainer,s=e.forceRender,c=e.destroyOnClose,u=void 0!==c&&c,d=e.afterClose,f=e.panelRef,p=l.useState(t),m=(0,r.A)(p,2),h=m[0],g=m[1],v=l.useMemo(function(){return{panel:f}},[f]);return l.useEffect(function(){t&&g(!0)},[t]),s||!u||h?l.createElement(a.Provider,{value:v},l.createElement(i.A,{open:t||s||h,autoDestroy:!1,getContainer:n,autoLock:t||h},l.createElement(E,(0,o.A)({},e,{destroyOnClose:u,afterClose:function(){null==d||d(),g(!1)}})))):null};I.displayName="Dialog";var O=I},4103:function(e,t,n){"use strict";n.d(t,{A:function(){return s}});var o=n(8168),r=n(6540),i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"}}]},name:"down",theme:"outlined"},l=n(7064),a=function(e,t){return r.createElement(l.A,(0,o.A)({},e,{ref:t,icon:i}))};var s=r.forwardRef(a)},4129:function(e,t,n){"use strict";n.d(t,{M:function(){return o}});const o=n(6540).createContext({siderHook:{addSider:()=>null,removeSider:()=>null}})},4211:function(e,t,n){"use strict";n.d(t,{Mh:function(){return f}});var o=n(2187),r=n(4980);const i=new o.Mo("antMoveDownIn",{"0%":{transform:"translate3d(0, 100%, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),l=new o.Mo("antMoveDownOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(0, 100%, 0)",transformOrigin:"0 0",opacity:0}}),a=new o.Mo("antMoveLeftIn",{"0%":{transform:"translate3d(-100%, 0, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),s=new o.Mo("antMoveLeftOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(-100%, 0, 0)",transformOrigin:"0 0",opacity:0}}),c=new o.Mo("antMoveRightIn",{"0%":{transform:"translate3d(100%, 0, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),u=new o.Mo("antMoveRightOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(100%, 0, 0)",transformOrigin:"0 0",opacity:0}}),d={"move-up":{inKeyframes:new o.Mo("antMoveUpIn",{"0%":{transform:"translate3d(0, -100%, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),outKeyframes:new o.Mo("antMoveUpOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(0, -100%, 0)",transformOrigin:"0 0",opacity:0}})},"move-down":{inKeyframes:i,outKeyframes:l},"move-left":{inKeyframes:a,outKeyframes:s},"move-right":{inKeyframes:c,outKeyframes:u}},f=(e,t)=>{const{antCls:n}=e,o=`${n}-${t}`,{inKeyframes:i,outKeyframes:l}=d[t];return[(0,r.b)(o,i,l,e.motionDurationMid),{[`\n        ${o}-enter,\n        ${o}-appear\n      `]:{opacity:0,animationTimingFunction:e.motionEaseOutCirc},[`${o}-leave`]:{animationTimingFunction:e.motionEaseInOutCirc}}]}},4440:function(e,t,n){"use strict";n.d(t,{cH:function(){return l},lB:function(){return a}});var o=n(2187),r=n(7358);const i=e=>{const{antCls:t,componentCls:n,colorText:r,footerBg:i,headerHeight:l,headerPadding:a,headerColor:s,footerPadding:c,fontSize:u,bodyBg:d,headerBg:f}=e;return{[n]:{display:"flex",flex:"auto",flexDirection:"column",minHeight:0,background:d,"&, *":{boxSizing:"border-box"},[`&${n}-has-sider`]:{flexDirection:"row",[`> ${n}, > ${n}-content`]:{width:0}},[`${n}-header, &${n}-footer`]:{flex:"0 0 auto"},"&-rtl":{direction:"rtl"}},[`${n}-header`]:{height:l,padding:a,color:s,lineHeight:(0,o.zA)(l),background:f,[`${t}-menu`]:{lineHeight:"inherit"}},[`${n}-footer`]:{padding:c,color:r,fontSize:u,background:i},[`${n}-content`]:{flex:"auto",color:r,minHeight:0}}},l=e=>{const{colorBgLayout:t,controlHeight:n,controlHeightLG:o,colorText:r,controlHeightSM:i,marginXXS:l,colorTextLightSolid:a,colorBgContainer:s}=e,c=1.25*o;return{colorBgHeader:"#001529",colorBgBody:t,colorBgTrigger:"#002140",bodyBg:t,headerBg:"#001529",headerHeight:2*n,headerPadding:`0 ${c}px`,headerColor:r,footerPadding:`${i}px ${c}px`,footerBg:t,siderBg:"#001529",triggerHeight:o+2*l,triggerBg:"#002140",triggerColor:a,zeroTriggerWidth:o,zeroTriggerHeight:o,lightSiderBg:s,lightTriggerBg:s,lightTriggerColor:r}},a=[["colorBgBody","bodyBg"],["colorBgHeader","headerBg"],["colorBgTrigger","triggerBg"]];t.Ay=(0,r.OF)("Layout",e=>[i(e)],l,{deprecatedTokens:a})},4471:function(e,t,n){"use strict";n.d(t,{A:function(){return o}});const o=(0,n(1788).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},4849:function(e,t,n){"use strict";n.d(t,{e:function(){return o},p:function(){return r}});const o=(e,t)=>{void 0!==(null==e?void 0:e.addEventListener)?e.addEventListener("change",t):void 0!==(null==e?void 0:e.addListener)&&e.addListener(t)},r=(e,t)=>{void 0!==(null==e?void 0:e.removeEventListener)?e.removeEventListener("change",t):void 0!==(null==e?void 0:e.removeListener)&&e.removeListener(t)}},4870:function(e,t,n){"use strict";var o=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var n=null;if(!e||"string"!=typeof e)return n;var o=(0,r.default)(e),i="function"==typeof t;return o.forEach(function(e){if("declaration"===e.type){var o=e.property,r=e.value;i?t(o,r,e):r&&((n=n||{})[o]=r)}}),n};var r=o(n(315))},5006:function(e,t,n){"use strict";n.d(t,{L3:function(){return s},i4:function(){return c},xV:function(){return u}});var o=n(2187),r=n(7358),i=n(4277);const l=e=>{const{componentCls:t}=e;return{[t]:{position:"relative",maxWidth:"100%",minHeight:1}}},a=(e,t)=>((e,t)=>{const{prefixCls:n,componentCls:o,gridColumns:r}=e,i={};for(let l=r;l>=0;l--)0===l?(i[`${o}${t}-${l}`]={display:"none"},i[`${o}-push-${l}`]={insetInlineStart:"auto"},i[`${o}-pull-${l}`]={insetInlineEnd:"auto"},i[`${o}${t}-push-${l}`]={insetInlineStart:"auto"},i[`${o}${t}-pull-${l}`]={insetInlineEnd:"auto"},i[`${o}${t}-offset-${l}`]={marginInlineStart:0},i[`${o}${t}-order-${l}`]={order:0}):(i[`${o}${t}-${l}`]=[{"--ant-display":"block",display:"block"},{display:"var(--ant-display)",flex:`0 0 ${l/r*100}%`,maxWidth:l/r*100+"%"}],i[`${o}${t}-push-${l}`]={insetInlineStart:l/r*100+"%"},i[`${o}${t}-pull-${l}`]={insetInlineEnd:l/r*100+"%"},i[`${o}${t}-offset-${l}`]={marginInlineStart:l/r*100+"%"},i[`${o}${t}-order-${l}`]={order:l});return i[`${o}${t}-flex`]={flex:`var(--${n}${t}-flex)`},i})(e,t),s=(0,r.OF)("Grid",e=>{const{componentCls:t}=e;return{[t]:{display:"flex",flexFlow:"row wrap",minWidth:0,"&::before, &::after":{display:"flex"},"&-no-wrap":{flexWrap:"nowrap"},"&-start":{justifyContent:"flex-start"},"&-center":{justifyContent:"center"},"&-end":{justifyContent:"flex-end"},"&-space-between":{justifyContent:"space-between"},"&-space-around":{justifyContent:"space-around"},"&-space-evenly":{justifyContent:"space-evenly"},"&-top":{alignItems:"flex-start"},"&-middle":{alignItems:"center"},"&-bottom":{alignItems:"flex-end"}}}},()=>({})),c=e=>({xs:e.screenXSMin,sm:e.screenSMMin,md:e.screenMDMin,lg:e.screenLGMin,xl:e.screenXLMin,xxl:e.screenXXLMin}),u=(0,r.OF)("Grid",e=>{const t=(0,i.oX)(e,{gridColumns:24}),n=c(t);return delete n.xs,[l(t),a(t,""),a(t,"-xs"),Object.keys(n).map(e=>((e,t,n)=>({[`@media (min-width: ${(0,o.zA)(t)})`]:Object.assign({},a(e,n))}))(t,n[e],`-${e}`)).reduce((e,t)=>Object.assign(Object.assign({},e),t),{})]},()=>({}))},5229:function(e,t,n){"use strict";var o=(this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}})(n(4870)),r=n(8917);function i(e,t){var n={};return e&&"string"==typeof e?((0,o.default)(e,function(e,o){e&&o&&(n[(0,r.camelCase)(e,t)]=o)}),n):n}i.default=i,e.exports=i},5731:function(e,t,n){"use strict";n.d(t,{A:function(){return o}});const o=(0,n(1788).A)("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},6108:function(e,t,n){"use strict";n.d(t,{A:function(){return $e}});var o=n(436),r=n(6540),i=n(2279),l=n(867),a=n(4642),s=n(8811),c=n(6029),u=n(7541),d=n(7850),f=n(6942),p=n.n(f),m=n(275),h=n(3723),g=n(9155),v=n(1320),b=n(1233),y=n(2941),x=n(9449);function w(e){return!!(null==e?void 0:e.then)}var C=e=>{const{type:t,children:n,prefixCls:o,buttonProps:i,close:l,autoFocus:a,emitEvent:s,isSilent:c,quitOnNullishReturnValue:u,actionFn:d}=e,f=r.useRef(!1),p=r.useRef(null),[m,h]=(0,b.A)(!1),g=(...e)=>{null==l||l.apply(void 0,e)};r.useEffect(()=>{let e=null;return a&&(e=setTimeout(()=>{var e;null===(e=p.current)||void 0===e||e.focus({preventScroll:!0})})),()=>{e&&clearTimeout(e)}},[]);return r.createElement(y.Ay,Object.assign({},(0,x.DU)(t),{onClick:e=>{if(f.current)return;if(f.current=!0,!d)return void g();let t;if(s){if(t=d(e),u&&!w(t))return f.current=!1,void g(e)}else if(d.length)t=d(l),f.current=!1;else if(t=d(),!w(t))return void g();(e=>{w(e)&&(h(!0),e.then((...e)=>{h(!1,!0),g.apply(void 0,e),f.current=!1},e=>{if(h(!1,!0),f.current=!1,!(null==c?void 0:c()))return Promise.reject(e)}))})(t)},loading:m,prefixCls:o},i,{ref:p}),n)};const k=r.createContext({}),{Provider:$}=k;var S=()=>{const{autoFocusButton:e,cancelButtonProps:t,cancelTextLocale:n,isSilent:o,mergedOkCancel:i,rootPrefixCls:l,close:a,onCancel:s,onConfirm:c}=(0,r.useContext)(k);return i?r.createElement(C,{isSilent:o,actionFn:s,close:(...e)=>{null==a||a.apply(void 0,e),null==c||c(!1)},autoFocus:"cancel"===e,buttonProps:t,prefixCls:`${l}-btn`},n):null};var A=()=>{const{autoFocusButton:e,close:t,isSilent:n,okButtonProps:o,rootPrefixCls:i,okTextLocale:l,okType:a,onConfirm:s,onOk:c}=(0,r.useContext)(k);return r.createElement(C,{isSilent:n,type:a||"primary",actionFn:c,close:(...e)=>{null==t||t.apply(void 0,e),null==s||s(!0)},autoFocus:"ok"===e,buttonProps:o,prefixCls:`${i}-btn`},l)},E=n(7852),I=n(3766),O=n(2897),z=n(8055),M=n(998);var P=n(235),T=n(934),j=n(7072),N=n(8557),B=n(8119);var R=()=>{const{cancelButtonProps:e,cancelTextLocale:t,onCancel:n}=(0,r.useContext)(k);return r.createElement(y.Ay,Object.assign({onClick:n},e),t)};var D=()=>{const{confirmLoading:e,okButtonProps:t,okType:n,okTextLocale:o,onOk:i}=(0,r.useContext)(k);return r.createElement(y.Ay,Object.assign({},(0,x.DU)(n),{loading:e,onClick:i},t),o)},L=n(1815);function H(e,t){return r.createElement("span",{className:`${e}-close-x`},t||r.createElement(E.A,{className:`${e}-close-icon`}))}const F=e=>{const{okText:t,okType:n="primary",cancelText:i,confirmLoading:l,onOk:a,onCancel:s,okButtonProps:c,cancelButtonProps:u,footer:d}=e,[f]=(0,g.A)("Modal",(0,L.l)()),p={confirmLoading:l,okButtonProps:c,cancelButtonProps:u,okTextLocale:t||(null==f?void 0:f.okText),cancelTextLocale:i||(null==f?void 0:f.cancelText),okType:n,onOk:a,onCancel:s},m=r.useMemo(()=>p,(0,o.A)(Object.values(p)));let h;return"function"==typeof d||void 0===d?(h=r.createElement(r.Fragment,null,r.createElement(R,null),r.createElement(D,null)),"function"==typeof d&&(h=d(h,{OkBtn:D,CancelBtn:R})),h=r.createElement($,{value:m},h)):h=d,r.createElement(B.X,{disabled:!1},h)};var _=n(8071),W=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};let V;const q=e=>{V={x:e.pageX,y:e.pageY},setTimeout(()=>{V=null},100)};(0,M.A)()&&window.document.documentElement&&document.documentElement.addEventListener("click",q,!0);var U=e=>{const{prefixCls:t,className:n,rootClassName:o,open:l,wrapClassName:a,centered:s,getContainer:c,focusTriggerAfterClose:u=!0,style:d,visible:f,width:g=520,footer:v,classNames:b,styles:y,children:x,loading:w,confirmLoading:C,zIndex:k,mousePosition:$,onOk:S,onCancel:A,destroyOnHidden:M,destroyOnClose:B}=e,R=W(e,["prefixCls","className","rootClassName","open","wrapClassName","centered","getContainer","focusTriggerAfterClose","style","visible","width","footer","classNames","styles","children","loading","confirmLoading","zIndex","mousePosition","onOk","onCancel","destroyOnHidden","destroyOnClose"]),{getPopupContainer:D,getPrefixCls:L,direction:q,modal:U}=r.useContext(i.QO),X=e=>{C||null==A||A(e)};const G=L("modal",t),K=L(),Y=(0,T.A)(G),[Q,Z,J]=(0,_.Ay)(G,Y),ee=p()(a,{[`${G}-centered`]:null!=s?s:null==U?void 0:U.centered,[`${G}-wrap-rtl`]:"rtl"===q}),te=null===v||w?null:r.createElement(F,Object.assign({},e,{onOk:e=>{null==S||S(e)},onCancel:X})),[ne,oe,re,ie]=(0,z.A)((0,z.d)(e),(0,z.d)(U),{closable:!0,closeIcon:r.createElement(E.A,{className:`${G}-close-icon`}),closeIconRender:e=>H(G,e)}),le=(0,N.f)(`.${G}-content`),[ae,se]=(0,m.YK)("Modal",k),[ce,ue]=r.useMemo(()=>g&&"object"==typeof g?[void 0,g]:[g,void 0],[g]),de=r.useMemo(()=>{const e={};return ue&&Object.keys(ue).forEach(t=>{const n=ue[t];void 0!==n&&(e[`--${G}-${t}-width`]="number"==typeof n?`${n}px`:n)}),e},[ue]);return Q(r.createElement(O.A,{form:!0,space:!0},r.createElement(P.A.Provider,{value:se},r.createElement(I.A,Object.assign({width:ce},R,{zIndex:ae,getContainer:void 0===c?D:c,prefixCls:G,rootClassName:p()(Z,o,J,Y),footer:te,visible:null!=l?l:f,mousePosition:null!=$?$:V,onClose:X,closable:ne?Object.assign({disabled:re,closeIcon:oe},ie):ne,closeIcon:oe,focusTriggerAfterClose:u,transitionName:(0,h.b)(K,"zoom",e.transitionName),maskTransitionName:(0,h.b)(K,"fade",e.maskTransitionName),className:p()(Z,n,null==U?void 0:U.className),style:Object.assign(Object.assign(Object.assign({},null==U?void 0:U.style),d),de),classNames:Object.assign(Object.assign(Object.assign({},null==U?void 0:U.classNames),b),{wrapper:p()(ee,null==b?void 0:b.wrapper)}),styles:Object.assign(Object.assign({},null==U?void 0:U.styles),y),panelRef:le,destroyOnClose:null!=M?M:B}),w?r.createElement(j.A,{active:!0,title:!1,paragraph:{rows:4},className:`${G}-body-skeleton`}):x))))},X=n(2187),G=n(5905),K=n(7358);const Y=e=>{const{componentCls:t,titleFontSize:n,titleLineHeight:o,modalConfirmIconSize:r,fontSize:i,lineHeight:l,modalTitleHeight:a,fontHeight:s,confirmBodyPadding:c}=e,u=`${t}-confirm`;return{[u]:{"&-rtl":{direction:"rtl"},[`${e.antCls}-modal-header`]:{display:"none"},[`${u}-body-wrapper`]:Object.assign({},(0,G.t6)()),[`&${t} ${t}-body`]:{padding:c},[`${u}-body`]:{display:"flex",flexWrap:"nowrap",alignItems:"start",[`> ${e.iconCls}`]:{flex:"none",fontSize:r,marginInlineEnd:e.confirmIconMarginInlineEnd,marginTop:e.calc(e.calc(s).sub(r).equal()).div(2).equal()},[`&-has-title > ${e.iconCls}`]:{marginTop:e.calc(e.calc(a).sub(r).equal()).div(2).equal()}},[`${u}-paragraph`]:{display:"flex",flexDirection:"column",flex:"auto",rowGap:e.marginXS,maxWidth:`calc(100% - ${(0,X.zA)(e.marginSM)})`},[`${e.iconCls} + ${u}-paragraph`]:{maxWidth:`calc(100% - ${(0,X.zA)(e.calc(e.modalConfirmIconSize).add(e.marginSM).equal())})`},[`${u}-title`]:{color:e.colorTextHeading,fontWeight:e.fontWeightStrong,fontSize:n,lineHeight:o},[`${u}-content`]:{color:e.colorText,fontSize:i,lineHeight:l},[`${u}-btns`]:{textAlign:"end",marginTop:e.confirmBtnsMarginTop,[`${e.antCls}-btn + ${e.antCls}-btn`]:{marginBottom:0,marginInlineStart:e.marginXS}}},[`${u}-error ${u}-body > ${e.iconCls}`]:{color:e.colorError},[`${u}-warning ${u}-body > ${e.iconCls},\n        ${u}-confirm ${u}-body > ${e.iconCls}`]:{color:e.colorWarning},[`${u}-info ${u}-body > ${e.iconCls}`]:{color:e.colorInfo},[`${u}-success ${u}-body > ${e.iconCls}`]:{color:e.colorSuccess}}};var Q=(0,K.bf)(["Modal","confirm"],e=>{const t=(0,_.FY)(e);return[Y(t)]},_.cH,{order:-1e3}),Z=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};function J(e){const{prefixCls:t,icon:n,okText:i,cancelText:l,confirmPrefixCls:a,type:f,okCancel:m,footer:h,locale:v}=e,b=Z(e,["prefixCls","icon","okText","cancelText","confirmPrefixCls","type","okCancel","footer","locale"]);let y=n;if(!n&&null!==n)switch(f){case"info":y=r.createElement(d.A,null);break;case"success":y=r.createElement(s.A,null);break;case"error":y=r.createElement(c.A,null);break;default:y=r.createElement(u.A,null)}const x=null!=m?m:"confirm"===f,w=null!==e.autoFocusButton&&(e.autoFocusButton||"ok"),[C]=(0,g.A)("Modal"),k=v||C,E=i||(x?null==k?void 0:k.okText:null==k?void 0:k.justOkText),I=l||(null==k?void 0:k.cancelText),O=Object.assign({autoFocusButton:w,cancelTextLocale:I,okTextLocale:E,mergedOkCancel:x},b),z=r.useMemo(()=>O,(0,o.A)(Object.values(O))),M=r.createElement(r.Fragment,null,r.createElement(S,null),r.createElement(A,null)),P=void 0!==e.title&&null!==e.title,T=`${a}-body`;return r.createElement("div",{className:`${a}-body-wrapper`},r.createElement("div",{className:p()(T,{[`${T}-has-title`]:P})},y,r.createElement("div",{className:`${a}-paragraph`},P&&r.createElement("span",{className:`${a}-title`},e.title),r.createElement("div",{className:`${a}-content`},e.content))),void 0===h||"function"==typeof h?r.createElement($,{value:z},r.createElement("div",{className:`${a}-btns`},"function"==typeof h?h(M,{OkBtn:A,CancelBtn:S}):M)):h,r.createElement(Q,{prefixCls:t}))}const ee=e=>{const{close:t,zIndex:n,maskStyle:o,direction:i,prefixCls:l,wrapClassName:a,rootPrefixCls:s,bodyStyle:c,closable:u=!1,onConfirm:d,styles:f}=e;const g=`${l}-confirm`,b=e.width||416,y=e.style||{},x=void 0===e.mask||e.mask,w=void 0!==e.maskClosable&&e.maskClosable,C=p()(g,`${g}-${e.type}`,{[`${g}-rtl`]:"rtl"===i},e.className),[,k]=(0,v.Ay)(),$=r.useMemo(()=>void 0!==n?n:k.zIndexPopupBase+m.jH,[n,k]);return r.createElement(U,Object.assign({},e,{className:C,wrapClassName:p()({[`${g}-centered`]:!!e.centered},a),onCancel:()=>{null==t||t({triggerCancel:!0}),null==d||d(!1)},title:"",footer:null,transitionName:(0,h.b)(s||"","zoom",e.transitionName),maskTransitionName:(0,h.b)(s||"","fade",e.maskTransitionName),mask:x,maskClosable:w,style:y,styles:Object.assign({body:c,mask:o},f),width:b,zIndex:$,closable:u}),r.createElement(J,Object.assign({},e,{confirmPrefixCls:g})))};var te=e=>{const{rootPrefixCls:t,iconPrefixCls:n,direction:o,theme:i}=e;return r.createElement(l.Ay,{prefixCls:t,iconPrefixCls:n,direction:o,theme:i},r.createElement(ee,Object.assign({},e)))};var ne=[];let oe="";function re(){return oe}const ie=e=>{var t,n;const{prefixCls:o,getContainer:l,direction:a}=e,s=(0,L.l)(),c=(0,r.useContext)(i.QO),u=re()||c.getPrefixCls(),d=o||`${u}-modal`;let f=l;return!1===f&&(f=void 0),r.createElement(te,Object.assign({},e,{rootPrefixCls:u,prefixCls:d,iconPrefixCls:c.iconPrefixCls,theme:c.theme,direction:null!=a?a:c.direction,locale:null!==(n=null===(t=c.locale)||void 0===t?void 0:t.Modal)&&void 0!==n?n:s,getContainer:f}))};function le(e){const t=(0,l.cr)();const n=document.createDocumentFragment();let i,s,c=Object.assign(Object.assign({},e),{close:f,open:!0});function u(...t){var n;var r;t.some(e=>null==e?void 0:e.triggerCancel)&&(null===(n=e.onCancel)||void 0===n||(r=n).call.apply(r,[e,()=>{}].concat((0,o.A)(t.slice(1)))));for(let e=0;e<ne.length;e++){if(ne[e]===f){ne.splice(e,1);break}}s()}function d(e){clearTimeout(i),i=setTimeout(()=>{const o=t.getPrefixCls(void 0,re()),i=t.getIconPrefixCls(),c=t.getTheme(),u=r.createElement(ie,Object.assign({},e)),d=(0,a.L)();s=d(r.createElement(l.Ay,{prefixCls:o,iconPrefixCls:i,theme:c},t.holderRender?t.holderRender(u):u),n)})}function f(...t){c=Object.assign(Object.assign({},c),{open:!1,afterClose:()=>{"function"==typeof e.afterClose&&e.afterClose(),u.apply(this,t)}}),c.visible&&delete c.visible,d(c)}return d(c),ne.push(f),{destroy:f,update:function(e){c="function"==typeof e?e(c):Object.assign(Object.assign({},c),e),d(c)}}}function ae(e){return Object.assign(Object.assign({},e),{type:"warning"})}function se(e){return Object.assign(Object.assign({},e),{type:"info"})}function ce(e){return Object.assign(Object.assign({},e),{type:"success"})}function ue(e){return Object.assign(Object.assign({},e),{type:"error"})}function de(e){return Object.assign(Object.assign({},e),{type:"confirm"})}var fe=n(3425),pe=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};var me=(0,fe.U)(e=>{const{prefixCls:t,className:n,closeIcon:o,closable:l,type:a,title:s,children:c,footer:u}=e,d=pe(e,["prefixCls","className","closeIcon","closable","type","title","children","footer"]),{getPrefixCls:f}=r.useContext(i.QO),m=f(),h=t||f("modal"),g=(0,T.A)(m),[v,b,y]=(0,_.Ay)(h,g),x=`${h}-confirm`;let w={};return w=a?{closable:null!=l&&l,title:"",footer:"",children:r.createElement(J,Object.assign({},e,{prefixCls:h,confirmPrefixCls:x,rootPrefixCls:m,content:c}))}:{closable:null==l||l,title:s,footer:null!==u&&r.createElement(F,Object.assign({},e)),children:c},v(r.createElement(I.Z,Object.assign({prefixCls:h,className:p()(b,`${h}-pure-panel`,a&&x,a&&`${x}-${a}`,n,y,g)},d,{closeIcon:H(h,o),closable:l},w)))});var he=n(5678),ge=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const ve=(e,t)=>{var n,{afterClose:l,config:a}=e,s=ge(e,["afterClose","config"]);const[c,u]=r.useState(!0),[d,f]=r.useState(a),{direction:p,getPrefixCls:m}=r.useContext(i.QO),h=m("modal"),v=m(),b=(...e)=>{var t;u(!1);var n;e.some(e=>null==e?void 0:e.triggerCancel)&&(null===(t=d.onCancel)||void 0===t||(n=t).call.apply(n,[d,()=>{}].concat((0,o.A)(e.slice(1)))))};r.useImperativeHandle(t,()=>({destroy:b,update:e=>{f(t=>{const n="function"==typeof e?e(t):e;return Object.assign(Object.assign({},t),n)})}}));const y=null!==(n=d.okCancel)&&void 0!==n?n:"confirm"===d.type,[x]=(0,g.A)("Modal",he.A.Modal);return r.createElement(te,Object.assign({prefixCls:h,rootPrefixCls:v},d,{close:b,open:c,afterClose:()=>{var e;l(),null===(e=d.afterClose)||void 0===e||e.call(d)},okText:d.okText||(y?null==x?void 0:x.okText:null==x?void 0:x.justOkText),direction:d.direction||p,cancelText:d.cancelText||(null==x?void 0:x.cancelText)},s))};var be=r.forwardRef(ve);let ye=0;const xe=r.memo(r.forwardRef((e,t)=>{const[n,i]=function(){const[e,t]=r.useState([]);return[e,r.useCallback(e=>(t(t=>[].concat((0,o.A)(t),[e])),()=>{t(t=>t.filter(t=>t!==e))}),[])]}();return r.useImperativeHandle(t,()=>({patchElement:i}),[]),r.createElement(r.Fragment,null,n)}));var we=function(){const e=r.useRef(null),[t,n]=r.useState([]);r.useEffect(()=>{if(t.length){(0,o.A)(t).forEach(e=>{e()}),n([])}},[t]);const i=r.useCallback(t=>function(i){var l;ye+=1;const a=r.createRef();let s;const c=new Promise(e=>{s=e});let u,d=!1;const f=r.createElement(be,{key:`modal-${ye}`,config:t(i),ref:a,afterClose:()=>{null==u||u()},isSilent:()=>d,onConfirm:e=>{s(e)}});u=null===(l=e.current)||void 0===l?void 0:l.patchElement(f),u&&ne.push(u);const p={destroy:()=>{function e(){var e;null===(e=a.current)||void 0===e||e.destroy()}a.current?e():n(t=>[].concat((0,o.A)(t),[e]))},update:e=>{function t(){var t;null===(t=a.current)||void 0===t||t.update(e)}a.current?t():n(e=>[].concat((0,o.A)(e),[t]))},then:e=>(d=!0,c.then(e))};return p},[]);return[r.useMemo(()=>({info:i(se),success:i(ce),error:i(ue),warning:i(ae),confirm:i(de)}),[]),r.createElement(xe,{key:"modal-holder",ref:e})]};function Ce(e){return le(ae(e))}const ke=U;ke.useModal=we,ke.info=function(e){return le(se(e))},ke.success=function(e){return le(ce(e))},ke.error=function(e){return le(ue(e))},ke.warning=Ce,ke.warn=Ce,ke.confirm=function(e){return le(de(e))},ke.destroyAll=function(){for(;ne.length;){const e=ne.pop();e&&e()}},ke.config=function({rootPrefixCls:e}){oe=e},ke._InternalPanelDoNotUseOrYouWillBeFired=me;var $e=ke},6305:function(e,t,n){"use strict";n.d(t,{A:function(){return o}});const o=(0,n(1788).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},7163:function(e,t,n){"use strict";n.d(t,{wx:function(){return El},XC:function(){return $l},PA:function(){return Sl},vq:function(){return Il}});var o={};n.r(o),n.d(o,{boolean:function(){return k},booleanish:function(){return $},commaOrSpaceSeparated:function(){return O},commaSeparated:function(){return I},number:function(){return A},overloadedBoolean:function(){return S},spaceSeparated:function(){return E}});var r={};n.r(r),n.d(r,{attentionMarkers:function(){return ln},contentInitial:function(){return Jt},disable:function(){return an},document:function(){return Zt},flow:function(){return tn},flowInitial:function(){return en},insideSpan:function(){return rn},string:function(){return nn},text:function(){return on}});var i=n(6540),l=n(2102),a=n(5107),s=n(3324),c=n(8697);function u(){}function d(){}const f=/^[$_\p{ID_Start}][$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,p=/^[$_\p{ID_Start}][-$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,m={};function h(e,t){return((t||m).jsx?p:f).test(e)}const g=/[ \t\n\f\r]/g;function v(e){return""===e.replace(g,"")}class b{constructor(e,t,n){this.normal=t,this.property=e,n&&(this.space=n)}}function y(e,t){const n={},o={};for(const r of e)Object.assign(n,r.property),Object.assign(o,r.normal);return new b(n,o,t)}function x(e){return e.toLowerCase()}b.prototype.normal={},b.prototype.property={},b.prototype.space=void 0;class w{constructor(e,t){this.attribute=t,this.property=e}}w.prototype.attribute="",w.prototype.booleanish=!1,w.prototype.boolean=!1,w.prototype.commaOrSpaceSeparated=!1,w.prototype.commaSeparated=!1,w.prototype.defined=!1,w.prototype.mustUseProperty=!1,w.prototype.number=!1,w.prototype.overloadedBoolean=!1,w.prototype.property="",w.prototype.spaceSeparated=!1,w.prototype.space=void 0;let C=0;const k=z(),$=z(),S=z(),A=z(),E=z(),I=z(),O=z();function z(){return 2**++C}const M=Object.keys(o);class P extends w{constructor(e,t,n,r){let i=-1;if(super(e,t),T(this,"space",r),"number"==typeof n)for(;++i<M.length;){const e=M[i];T(this,M[i],(n&o[e])===o[e])}}}function T(e,t,n){n&&(e[t]=n)}function j(e){const t={},n={};for(const[o,r]of Object.entries(e.properties)){const i=new P(o,e.transform(e.attributes||{},o),r,e.space);e.mustUseProperty&&e.mustUseProperty.includes(o)&&(i.mustUseProperty=!0),t[o]=i,n[x(o)]=o,n[x(i.attribute)]=o}return new b(t,n,e.space)}P.prototype.defined=!0;const N=j({properties:{ariaActiveDescendant:null,ariaAtomic:$,ariaAutoComplete:null,ariaBusy:$,ariaChecked:$,ariaColCount:A,ariaColIndex:A,ariaColSpan:A,ariaControls:E,ariaCurrent:null,ariaDescribedBy:E,ariaDetails:null,ariaDisabled:$,ariaDropEffect:E,ariaErrorMessage:null,ariaExpanded:$,ariaFlowTo:E,ariaGrabbed:$,ariaHasPopup:null,ariaHidden:$,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:E,ariaLevel:A,ariaLive:null,ariaModal:$,ariaMultiLine:$,ariaMultiSelectable:$,ariaOrientation:null,ariaOwns:E,ariaPlaceholder:null,ariaPosInSet:A,ariaPressed:$,ariaReadOnly:$,ariaRelevant:null,ariaRequired:$,ariaRoleDescription:E,ariaRowCount:A,ariaRowIndex:A,ariaRowSpan:A,ariaSelected:$,ariaSetSize:A,ariaSort:null,ariaValueMax:A,ariaValueMin:A,ariaValueNow:A,ariaValueText:null,role:null},transform(e,t){return"role"===t?t:"aria-"+t.slice(4).toLowerCase()}});function B(e,t){return t in e?e[t]:t}function R(e,t){return B(e,t.toLowerCase())}const D=j({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:I,acceptCharset:E,accessKey:E,action:null,allow:null,allowFullScreen:k,allowPaymentRequest:k,allowUserMedia:k,alt:null,as:null,async:k,autoCapitalize:null,autoComplete:E,autoFocus:k,autoPlay:k,blocking:E,capture:null,charSet:null,checked:k,cite:null,className:E,cols:A,colSpan:null,content:null,contentEditable:$,controls:k,controlsList:E,coords:A|I,crossOrigin:null,data:null,dateTime:null,decoding:null,default:k,defer:k,dir:null,dirName:null,disabled:k,download:S,draggable:$,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:k,formTarget:null,headers:E,height:A,hidden:S,high:A,href:null,hrefLang:null,htmlFor:E,httpEquiv:E,id:null,imageSizes:null,imageSrcSet:null,inert:k,inputMode:null,integrity:null,is:null,isMap:k,itemId:null,itemProp:E,itemRef:E,itemScope:k,itemType:E,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:k,low:A,manifest:null,max:null,maxLength:A,media:null,method:null,min:null,minLength:A,multiple:k,muted:k,name:null,nonce:null,noModule:k,noValidate:k,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:k,optimum:A,pattern:null,ping:E,placeholder:null,playsInline:k,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:k,referrerPolicy:null,rel:E,required:k,reversed:k,rows:A,rowSpan:A,sandbox:E,scope:null,scoped:k,seamless:k,selected:k,shadowRootClonable:k,shadowRootDelegatesFocus:k,shadowRootMode:null,shape:null,size:A,sizes:null,slot:null,span:A,spellCheck:$,src:null,srcDoc:null,srcLang:null,srcSet:null,start:A,step:null,style:null,tabIndex:A,target:null,title:null,translate:null,type:null,typeMustMatch:k,useMap:null,value:$,width:A,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:E,axis:null,background:null,bgColor:null,border:A,borderColor:null,bottomMargin:A,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:k,declare:k,event:null,face:null,frame:null,frameBorder:null,hSpace:A,leftMargin:A,link:null,longDesc:null,lowSrc:null,marginHeight:A,marginWidth:A,noResize:k,noHref:k,noShade:k,noWrap:k,object:null,profile:null,prompt:null,rev:null,rightMargin:A,rules:null,scheme:null,scrolling:$,standby:null,summary:null,text:null,topMargin:A,valueType:null,version:null,vAlign:null,vLink:null,vSpace:A,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:k,disableRemotePlayback:k,prefix:null,property:null,results:A,security:null,unselectable:null},space:"html",transform:R}),L=j({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:O,accentHeight:A,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:A,amplitude:A,arabicForm:null,ascent:A,attributeName:null,attributeType:null,azimuth:A,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:A,by:null,calcMode:null,capHeight:A,className:E,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:A,diffuseConstant:A,direction:null,display:null,dur:null,divisor:A,dominantBaseline:null,download:k,dx:null,dy:null,edgeMode:null,editable:null,elevation:A,enableBackground:null,end:null,event:null,exponent:A,externalResourcesRequired:null,fill:null,fillOpacity:A,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:I,g2:I,glyphName:I,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:A,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:A,horizOriginX:A,horizOriginY:A,id:null,ideographic:A,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:A,k:A,k1:A,k2:A,k3:A,k4:A,kernelMatrix:O,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:A,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:A,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:A,overlineThickness:A,paintOrder:null,panose1:null,path:null,pathLength:A,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:E,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:A,pointsAtY:A,pointsAtZ:A,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:O,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:O,rev:O,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:O,requiredFeatures:O,requiredFonts:O,requiredFormats:O,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:A,specularExponent:A,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:A,strikethroughThickness:A,string:null,stroke:null,strokeDashArray:O,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:A,strokeOpacity:A,strokeWidth:null,style:null,surfaceScale:A,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:O,tabIndex:A,tableValues:null,target:null,targetX:A,targetY:A,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:O,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:A,underlineThickness:A,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:A,values:null,vAlphabetic:A,vMathematical:A,vectorEffect:null,vHanging:A,vIdeographic:A,version:null,vertAdvY:A,vertOriginX:A,vertOriginY:A,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:A,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:B}),H=j({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform(e,t){return"xlink:"+t.slice(5).toLowerCase()}}),F=j({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:R}),_=j({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform(e,t){return"xml:"+t.slice(3).toLowerCase()}}),W=y([N,D,H,F,_],"html"),V=y([N,L,H,F,_],"svg"),q=/[A-Z]/g,U=/-[a-z]/g,X=/^data[-\w.:]+$/i;function G(e){return"-"+e.toLowerCase()}function K(e){return e.charAt(1).toUpperCase()}const Y={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"};var Q=n(5229);J("end");const Z=J("start");function J(e){return function(t){const n=t&&t.position&&t.position[e]||{};if("number"==typeof n.line&&n.line>0&&"number"==typeof n.column&&n.column>0)return{line:n.line,column:n.column,offset:"number"==typeof n.offset&&n.offset>-1?n.offset:void 0}}}function ee(e){return ne(e&&e.line)+":"+ne(e&&e.column)}function te(e){return ee(e&&e.start)+"-"+ee(e&&e.end)}function ne(e){return e&&"number"==typeof e?e:1}class oe extends Error{constructor(e,t,n){super(),"string"==typeof t&&(n=t,t=void 0);let o="",r={},i=!1;if(t&&(r="line"in t&&"column"in t||"start"in t&&"end"in t?{place:t}:"type"in t?{ancestors:[t],place:t.position}:{...t}),"string"==typeof e?o=e:!r.cause&&e&&(i=!0,o=e.message,r.cause=e),!r.ruleId&&!r.source&&"string"==typeof n){const e=n.indexOf(":");-1===e?r.ruleId=n:(r.source=n.slice(0,e),r.ruleId=n.slice(e+1))}if(!r.place&&r.ancestors&&r.ancestors){const e=r.ancestors[r.ancestors.length-1];e&&(r.place=e.position)}const l=r.place&&"start"in r.place?r.place.start:r.place;var a;this.ancestors=r.ancestors||void 0,this.cause=r.cause||void 0,this.column=l?l.column:void 0,this.fatal=void 0,this.file="",this.message=o,this.line=l?l.line:void 0,this.name=((a=r.place)&&"object"==typeof a?"position"in a||"type"in a?te(a.position):"start"in a||"end"in a?te(a):"line"in a||"column"in a?ee(a):"":"")||"1:1",this.place=r.place||void 0,this.reason=this.message,this.ruleId=r.ruleId||void 0,this.source=r.source||void 0,this.stack=i&&r.cause&&"string"==typeof r.cause.stack?r.cause.stack:"",this.actual=void 0,this.expected=void 0,this.note=void 0,this.url=void 0}}oe.prototype.file="",oe.prototype.name="",oe.prototype.reason="",oe.prototype.message="",oe.prototype.stack="",oe.prototype.column=void 0,oe.prototype.line=void 0,oe.prototype.ancestors=void 0,oe.prototype.cause=void 0,oe.prototype.fatal=void 0,oe.prototype.place=void 0,oe.prototype.ruleId=void 0,oe.prototype.source=void 0;const re={}.hasOwnProperty,ie=new Map,le=/[A-Z]/g,ae=new Set(["table","tbody","thead","tfoot","tr"]),se=new Set(["td","th"]),ce="https://github.com/syntax-tree/hast-util-to-jsx-runtime";function ue(e,t){if(!t||void 0===t.Fragment)throw new TypeError("Expected `Fragment` in options");const n=t.filePath||void 0;let o;if(t.development){if("function"!=typeof t.jsxDEV)throw new TypeError("Expected `jsxDEV` in options when `development: true`");o=function(e,t){return n;function n(n,o,r,i){const l=Array.isArray(r.children),a=Z(n);return t(o,r,i,l,{columnNumber:a?a.column-1:void 0,fileName:e,lineNumber:a?a.line:void 0},void 0)}}(n,t.jsxDEV)}else{if("function"!=typeof t.jsx)throw new TypeError("Expected `jsx` in production options");if("function"!=typeof t.jsxs)throw new TypeError("Expected `jsxs` in production options");o=function(e,t,n){return o;function o(e,o,r,i){const l=Array.isArray(r.children)?n:t;return i?l(o,r,i):l(o,r)}}(0,t.jsx,t.jsxs)}const r={Fragment:t.Fragment,ancestors:[],components:t.components||{},create:o,elementAttributeNameCase:t.elementAttributeNameCase||"react",evaluater:t.createEvaluater?t.createEvaluater():void 0,filePath:n,ignoreInvalidStyle:t.ignoreInvalidStyle||!1,passKeys:!1!==t.passKeys,passNode:t.passNode||!1,schema:"svg"===t.space?V:W,stylePropertyNameCase:t.stylePropertyNameCase||"dom",tableCellAlignToStyle:!1!==t.tableCellAlignToStyle},i=de(r,e,void 0);return i&&"string"!=typeof i?i:r.create(e,r.Fragment,{children:i||void 0},void 0)}function de(e,t,n){return"element"===t.type?function(e,t,n){const o=e.schema;let r=o;"svg"===t.tagName.toLowerCase()&&"html"===o.space&&(r=V,e.schema=r);e.ancestors.push(t);const i=ge(e,t.tagName,!1),l=function(e,t){const n={};let o,r;for(r in t.properties)if("children"!==r&&re.call(t.properties,r)){const i=he(e,r,t.properties[r]);if(i){const[r,l]=i;e.tableCellAlignToStyle&&"align"===r&&"string"==typeof l&&se.has(t.tagName)?o=l:n[r]=l}}if(o){(n.style||(n.style={}))["css"===e.stylePropertyNameCase?"text-align":"textAlign"]=o}return n}(e,t);let a=me(e,t);ae.has(t.tagName)&&(a=a.filter(function(e){return"string"!=typeof e||!("object"==typeof(t=e)?"text"===t.type&&v(t.value):v(t));var t}));return fe(e,l,i,t),pe(l,a),e.ancestors.pop(),e.schema=o,e.create(t,i,l,n)}(e,t,n):"mdxFlowExpression"===t.type||"mdxTextExpression"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater){const n=t.data.estree.body[0];return n.type,e.evaluater.evaluateExpression(n.expression)}ve(e,t.position)}(e,t):"mdxJsxFlowElement"===t.type||"mdxJsxTextElement"===t.type?function(e,t,n){const o=e.schema;let r=o;"svg"===t.name&&"html"===o.space&&(r=V,e.schema=r);e.ancestors.push(t);const i=null===t.name?e.Fragment:ge(e,t.name,!0),l=function(e,t){const n={};for(const o of t.attributes)if("mdxJsxExpressionAttribute"===o.type)if(o.data&&o.data.estree&&e.evaluater){const t=o.data.estree.body[0];u(t.type);const r=t.expression;u(r.type);const i=r.properties[0];u(i.type),Object.assign(n,e.evaluater.evaluateExpression(i.argument))}else ve(e,t.position);else{const r=o.name;let i;if(o.value&&"object"==typeof o.value)if(o.value.data&&o.value.data.estree&&e.evaluater){const t=o.value.data.estree.body[0];u(t.type),i=e.evaluater.evaluateExpression(t.expression)}else ve(e,t.position);else i=null===o.value||o.value;n[r]=i}return n}(e,t),a=me(e,t);return fe(e,l,i,t),pe(l,a),e.ancestors.pop(),e.schema=o,e.create(t,i,l,n)}(e,t,n):"mdxjsEsm"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater)return e.evaluater.evaluateProgram(t.data.estree);ve(e,t.position)}(e,t):"root"===t.type?function(e,t,n){const o={};return pe(o,me(e,t)),e.create(t,e.Fragment,o,n)}(e,t,n):"text"===t.type?function(e,t){return t.value}(0,t):void 0}function fe(e,t,n,o){"string"!=typeof n&&n!==e.Fragment&&e.passNode&&(t.node=o)}function pe(e,t){if(t.length>0){const n=t.length>1?t:t[0];n&&(e.children=n)}}function me(e,t){const n=[];let o=-1;const r=e.passKeys?new Map:ie;for(;++o<t.children.length;){const i=t.children[o];let l;if(e.passKeys){const e="element"===i.type?i.tagName:"mdxJsxFlowElement"===i.type||"mdxJsxTextElement"===i.type?i.name:void 0;if(e){const t=r.get(e)||0;l=e+"-"+t,r.set(e,t+1)}}const a=de(e,i,l);void 0!==a&&n.push(a)}return n}function he(e,t,n){const o=function(e,t){const n=x(t);let o=t,r=w;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&"data"===n.slice(0,4)&&X.test(t)){if("-"===t.charAt(4)){const e=t.slice(5).replace(U,K);o="data"+e.charAt(0).toUpperCase()+e.slice(1)}else{const e=t.slice(4);if(!U.test(e)){let n=e.replace(q,G);"-"!==n.charAt(0)&&(n="-"+n),t="data"+n}}r=P}return new r(o,t)}(e.schema,t);if(!(null==n||"number"==typeof n&&Number.isNaN(n))){if(Array.isArray(n)&&(n=o.commaSeparated?function(e,t){const n=t||{};return(""===e[e.length-1]?[...e,""]:e).join((n.padRight?" ":"")+","+(!1===n.padLeft?"":" ")).trim()}(n):n.join(" ").trim()),"style"===o.property){let t="object"==typeof n?n:function(e,t){try{return Q(t,{reactCompat:!0})}catch(n){if(e.ignoreInvalidStyle)return{};const t=n,o=new oe("Cannot parse `style` attribute",{ancestors:e.ancestors,cause:t,ruleId:"style",source:"hast-util-to-jsx-runtime"});throw o.file=e.filePath||void 0,o.url=ce+"#cannot-parse-style-attribute",o}}(e,String(n));return"css"===e.stylePropertyNameCase&&(t=function(e){const t={};let n;for(n in e)re.call(e,n)&&(t[be(n)]=e[n]);return t}(t)),["style",t]}return["react"===e.elementAttributeNameCase&&o.space?Y[o.property]||o.property:o.attribute,n]}}function ge(e,t,n){let o;if(n)if(t.includes(".")){const e=t.split(".");let n,r=-1;for(;++r<e.length;){const t=h(e[r])?{type:"Identifier",name:e[r]}:{type:"Literal",value:e[r]};n=n?{type:"MemberExpression",object:n,property:t,computed:Boolean(r&&"Literal"===t.type),optional:!1}:t}o=n}else o=h(t)&&!/^[a-z]/.test(t)?{type:"Identifier",name:t}:{type:"Literal",value:t};else o={type:"Literal",value:t};if("Literal"===o.type){const t=o.value;return re.call(e.components,t)?e.components[t]:t}if(e.evaluater)return e.evaluater.evaluateExpression(o);ve(e)}function ve(e,t){const n=new oe("Cannot handle MDX estrees without `createEvaluater`",{ancestors:e.ancestors,place:t,ruleId:"mdx-estree",source:"hast-util-to-jsx-runtime"});throw n.file=e.filePath||void 0,n.url=ce+"#cannot-handle-mdx-estrees-without-createevaluater",n}function be(e){let t=e.replace(le,ye);return"ms-"===t.slice(0,3)&&(t="-"+t),t}function ye(e){return"-"+e.toLowerCase()}const xe={action:["form"],cite:["blockquote","del","ins","q"],data:["object"],formAction:["button","input"],href:["a","area","base","link"],icon:["menuitem"],itemId:null,manifest:["html"],ping:["a","area"],poster:["video"],src:["audio","embed","iframe","img","input","script","source","track","video"]};var we=n(4848);const Ce={};function ke(e,t,n){if(function(e){return Boolean(e&&"object"==typeof e)}(e)){if("value"in e)return"html"!==e.type||n?e.value:"";if(t&&"alt"in e&&e.alt)return e.alt;if("children"in e)return $e(e.children,t,n)}return Array.isArray(e)?$e(e,t,n):""}function $e(e,t,n){const o=[];let r=-1;for(;++r<e.length;)o[r]=ke(e[r],t,n);return o.join("")}function Se(e,t,n,o){const r=e.length;let i,l=0;if(t=t<0?-t>r?0:r+t:t>r?r:t,n=n>0?n:0,o.length<1e4)i=Array.from(o),i.unshift(t,n),e.splice(...i);else for(n&&e.splice(t,n);l<o.length;)i=o.slice(l,l+1e4),i.unshift(t,0),e.splice(...i),l+=1e4,t+=1e4}function Ae(e,t){return e.length>0?(Se(e,e.length,0,t),e):t}class Ee{constructor(e){this.left=e?[...e]:[],this.right=[]}get(e){if(e<0||e>=this.left.length+this.right.length)throw new RangeError("Cannot access index `"+e+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return e<this.left.length?this.left[e]:this.right[this.right.length-e+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(e,t){const n=null==t?Number.POSITIVE_INFINITY:t;return n<this.left.length?this.left.slice(e,n):e>this.left.length?this.right.slice(this.right.length-n+this.left.length,this.right.length-e+this.left.length).reverse():this.left.slice(e).concat(this.right.slice(this.right.length-n+this.left.length).reverse())}splice(e,t,n){const o=t||0;this.setCursor(Math.trunc(e));const r=this.right.splice(this.right.length-o,Number.POSITIVE_INFINITY);return n&&Ie(this.left,n),r.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(e){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(e)}pushMany(e){this.setCursor(Number.POSITIVE_INFINITY),Ie(this.left,e)}unshift(e){this.setCursor(0),this.right.push(e)}unshiftMany(e){this.setCursor(0),Ie(this.right,e.reverse())}setCursor(e){if(!(e===this.left.length||e>this.left.length&&0===this.right.length||e<0&&0===this.left.length))if(e<this.left.length){const t=this.left.splice(e,Number.POSITIVE_INFINITY);Ie(this.right,t.reverse())}else{const t=this.right.splice(this.left.length+this.right.length-e,Number.POSITIVE_INFINITY);Ie(this.left,t.reverse())}}}function Ie(e,t){let n=0;if(t.length<1e4)e.push(...t);else for(;n<t.length;)e.push(...t.slice(n,n+1e4)),n+=1e4}function Oe(e){const t={};let n,o,r,i,l,a,s,c=-1;const u=new Ee(e);for(;++c<u.length;){for(;c in t;)c=t[c];if(n=u.get(c),c&&"chunkFlow"===n[1].type&&"listItemPrefix"===u.get(c-1)[1].type&&(a=n[1]._tokenizer.events,r=0,r<a.length&&"lineEndingBlank"===a[r][1].type&&(r+=2),r<a.length&&"content"===a[r][1].type))for(;++r<a.length&&"content"!==a[r][1].type;)"chunkText"===a[r][1].type&&(a[r][1]._isInFirstContentOfListItem=!0,r++);if("enter"===n[0])n[1].contentType&&(Object.assign(t,ze(u,c)),c=t[c],s=!0);else if(n[1]._container){for(r=c,o=void 0;r--;)if(i=u.get(r),"lineEnding"===i[1].type||"lineEndingBlank"===i[1].type)"enter"===i[0]&&(o&&(u.get(o)[1].type="lineEndingBlank"),i[1].type="lineEnding",o=r);else if("linePrefix"!==i[1].type&&"listItemIndent"!==i[1].type)break;o&&(n[1].end={...u.get(o)[1].start},l=u.slice(o,c),l.unshift(n),u.splice(o,c-o+1,l))}}return Se(e,0,Number.POSITIVE_INFINITY,u.slice(0)),!s}function ze(e,t){const n=e.get(t)[1],o=e.get(t)[2];let r=t-1;const i=[];let l=n._tokenizer;l||(l=o.parser[n.contentType](n.start),n._contentTypeTextTrailing&&(l._contentTypeTextTrailing=!0));const a=l.events,s=[],c={};let u,d,f=-1,p=n,m=0,h=0;const g=[h];for(;p;){for(;e.get(++r)[1]!==p;);i.push(r),p._tokenizer||(u=o.sliceStream(p),p.next||u.push(null),d&&l.defineSkip(p.start),p._isInFirstContentOfListItem&&(l._gfmTasklistFirstContentOfListItem=!0),l.write(u),p._isInFirstContentOfListItem&&(l._gfmTasklistFirstContentOfListItem=void 0)),d=p,p=p.next}for(p=n;++f<a.length;)"exit"===a[f][0]&&"enter"===a[f-1][0]&&a[f][1].type===a[f-1][1].type&&a[f][1].start.line!==a[f][1].end.line&&(h=f+1,g.push(h),p._tokenizer=void 0,p.previous=void 0,p=p.next);for(l.events=[],p?(p._tokenizer=void 0,p.previous=void 0):g.pop(),f=g.length;f--;){const t=a.slice(g[f],g[f+1]),n=i.pop();s.push([n,n+t.length-1]),e.splice(n,2,t)}for(s.reverse(),f=-1;++f<s.length;)c[m+s[f][0]]=m+s[f][1],m+=s[f][1]-s[f][0]-1;return c}const Me={}.hasOwnProperty;function Pe(e,t){let n;for(n in t){const o=(Me.call(e,n)?e[n]:void 0)||(e[n]={}),r=t[n];let i;if(r)for(i in r){Me.call(o,i)||(o[i]=[]);const e=r[i];Te(o[i],Array.isArray(e)?e:e?[e]:[])}}}function Te(e,t){let n=-1;const o=[];for(;++n<t.length;)("after"===t[n].add?e:o).push(t[n]);Se(e,0,0,o)}Ne(/[A-Za-z]/),Ne(/[\dA-Za-z]/),Ne(/[#-'*+\--9=?A-Z^-~]/);Ne(/\d/),Ne(/[\dA-Fa-f]/),Ne(/[!-/:-@[-`{-~]/);function je(e){return-2===e||-1===e||32===e}Ne(/\p{P}|\p{S}/u),Ne(/\s/);function Ne(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}function Be(e,t,n,o){const r=o?o-1:Number.POSITIVE_INFINITY;let i=0;return function(o){if(je(o))return e.enter(n),l(o);return t(o)};function l(o){return je(o)&&i++<r?(e.consume(o),l):(e.exit(n),t(o))}}const Re=Xe(/[A-Za-z]/),De=Xe(/[\dA-Za-z]/),Le=Xe(/[#-'*+\--9=?A-Z^-~]/);function He(e){return null!==e&&(e<32||127===e)}const Fe=Xe(/\d/),_e=Xe(/[\dA-Fa-f]/),We=Xe(/[!-/:-@[-`{-~]/);function Ve(e){return null!==e&&e<-2}function qe(e){return null!==e&&(e<0||32===e)}function Ue(e){return-2===e||-1===e||32===e}Xe(/\p{P}|\p{S}/u),Xe(/\s/);function Xe(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}const Ge={tokenize:function(e){const t=e.attempt(this.parser.constructs.contentInitial,function(n){if(null===n)return void e.consume(n);return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),Be(e,t,"linePrefix")},function(t){return e.enter("paragraph"),o(t)});let n;return t;function o(t){const o=e.enter("chunkText",{contentType:"text",previous:n});return n&&(n.next=o),n=o,r(t)}function r(t){return null===t?(e.exit("chunkText"),e.exit("paragraph"),void e.consume(t)):Ve(t)?(e.consume(t),e.exit("chunkText"),o):(e.consume(t),r)}}};const Ke={tokenize:function(e){const t=this,n=[];let o,r,i,l=0;return a;function a(o){if(l<n.length){const r=n[l];return t.containerState=r[1],e.attempt(r[0].continuation,s,c)(o)}return c(o)}function s(e){if(l++,t.containerState._closeFlow){t.containerState._closeFlow=void 0,o&&b();const n=t.events.length;let r,i=n;for(;i--;)if("exit"===t.events[i][0]&&"chunkFlow"===t.events[i][1].type){r=t.events[i][1].end;break}v(l);let a=n;for(;a<t.events.length;)t.events[a][1].end={...r},a++;return Se(t.events,i+1,0,t.events.slice(n)),t.events.length=a,c(e)}return a(e)}function c(r){if(l===n.length){if(!o)return f(r);if(o.currentConstruct&&o.currentConstruct.concrete)return m(r);t.interrupt=Boolean(o.currentConstruct&&!o._gfmTableDynamicInterruptHack)}return t.containerState={},e.check(Ye,u,d)(r)}function u(e){return o&&b(),v(l),f(e)}function d(e){return t.parser.lazy[t.now().line]=l!==n.length,i=t.now().offset,m(e)}function f(n){return t.containerState={},e.attempt(Ye,p,m)(n)}function p(e){return l++,n.push([t.currentConstruct,t.containerState]),f(e)}function m(n){return null===n?(o&&b(),v(0),void e.consume(n)):(o=o||t.parser.flow(t.now()),e.enter("chunkFlow",{_tokenizer:o,contentType:"flow",previous:r}),h(n))}function h(n){return null===n?(g(e.exit("chunkFlow"),!0),v(0),void e.consume(n)):Ve(n)?(e.consume(n),g(e.exit("chunkFlow")),l=0,t.interrupt=void 0,a):(e.consume(n),h)}function g(e,n){const a=t.sliceStream(e);if(n&&a.push(null),e.previous=r,r&&(r.next=e),r=e,o.defineSkip(e.start),o.write(a),t.parser.lazy[e.start.line]){let e=o.events.length;for(;e--;)if(o.events[e][1].start.offset<i&&(!o.events[e][1].end||o.events[e][1].end.offset>i))return;const n=t.events.length;let r,a,s=n;for(;s--;)if("exit"===t.events[s][0]&&"chunkFlow"===t.events[s][1].type){if(r){a=t.events[s][1].end;break}r=!0}for(v(l),e=n;e<t.events.length;)t.events[e][1].end={...a},e++;Se(t.events,s+1,0,t.events.slice(n)),t.events.length=e}}function v(o){let r=n.length;for(;r-- >o;){const o=n[r];t.containerState=o[1],o[0].exit.call(t,e)}n.length=o}function b(){o.write([null]),r=void 0,o=void 0,t.containerState._closeFlow=void 0}}},Ye={tokenize:function(e,t,n){return Be(e,e.attempt(this.parser.constructs.document,t,n),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}};const Qe={partial:!0,tokenize:function(e,t,n){return function(t){return Ue(t)?Be(e,o,"linePrefix")(t):o(t)};function o(e){return null===e||Ve(e)?t(e):n(e)}}};const Ze={resolve:function(e){return Oe(e),e},tokenize:function(e,t){let n;return function(t){return e.enter("content"),n=e.enter("chunkContent",{contentType:"content"}),o(t)};function o(t){return null===t?r(t):Ve(t)?e.check(Je,i,r)(t):(e.consume(t),o)}function r(n){return e.exit("chunkContent"),e.exit("content"),t(n)}function i(t){return e.consume(t),e.exit("chunkContent"),n.next=e.enter("chunkContent",{contentType:"content",previous:n}),n=n.next,o}}},Je={partial:!0,tokenize:function(e,t,n){const o=this;return function(t){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),Be(e,r,"linePrefix")};function r(r){if(null===r||Ve(r))return n(r);const i=o.events[o.events.length-1];return!o.parser.constructs.disable.null.includes("codeIndented")&&i&&"linePrefix"===i[1].type&&i[2].sliceSerialize(i[1],!0).length>=4?t(r):e.interrupt(o.parser.constructs.flow,n,t)(r)}}};const et={tokenize:function(e){const t=this,n=e.attempt(Qe,function(o){if(null===o)return void e.consume(o);return e.enter("lineEndingBlank"),e.consume(o),e.exit("lineEndingBlank"),t.currentConstruct=void 0,n},e.attempt(this.parser.constructs.flowInitial,o,Be(e,e.attempt(this.parser.constructs.flow,o,e.attempt(Ze,o)),"linePrefix")));return n;function o(o){if(null!==o)return e.enter("lineEnding"),e.consume(o),e.exit("lineEnding"),t.currentConstruct=void 0,n;e.consume(o)}}};const tt={resolveAll:it()},nt=rt("string"),ot=rt("text");function rt(e){return{resolveAll:it("text"===e?lt:void 0),tokenize:function(t){const n=this,o=this.parser.constructs[e],r=t.attempt(o,i,l);return i;function i(e){return s(e)?r(e):l(e)}function l(e){if(null!==e)return t.enter("data"),t.consume(e),a;t.consume(e)}function a(e){return s(e)?(t.exit("data"),r(e)):(t.consume(e),a)}function s(e){if(null===e)return!0;const t=o[e];let r=-1;if(t)for(;++r<t.length;){const e=t[r];if(!e.previous||e.previous.call(n,n.previous))return!0}return!1}}}}function it(e){return function(t,n){let o,r=-1;for(;++r<=t.length;)void 0===o?t[r]&&"data"===t[r][1].type&&(o=r,r++):t[r]&&"data"===t[r][1].type||(r!==o+2&&(t[o][1].end=t[r-1][1].end,t.splice(o+2,r-o-2),r=o+2),o=void 0);return e?e(t,n):t}}function lt(e,t){let n=0;for(;++n<=e.length;)if((n===e.length||"lineEnding"===e[n][1].type)&&"data"===e[n-1][1].type){const o=e[n-1][1],r=t.sliceStream(o);let i,l=r.length,a=-1,s=0;for(;l--;){const e=r[l];if("string"==typeof e){for(a=e.length;32===e.charCodeAt(a-1);)s++,a--;if(a)break;a=-1}else if(-2===e)i=!0,s++;else if(-1!==e){l++;break}}if(t._contentTypeTextTrailing&&n===e.length&&(s=0),s){const r={type:n===e.length||i||s<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:l?a:o.start._bufferIndex+a,_index:o.start._index+l,line:o.end.line,column:o.end.column-s,offset:o.end.offset-s},end:{...o.end}};o.end={...r.start},o.start.offset===o.end.offset?Object.assign(o,r):(e.splice(n,0,["enter",r,t],["exit",r,t]),n+=2)}n++}return e}const at={name:"thematicBreak",tokenize:function(e,t,n){let o,r=0;return function(t){return e.enter("thematicBreak"),function(e){return o=e,i(e)}(t)};function i(i){return i===o?(e.enter("thematicBreakSequence"),l(i)):r>=3&&(null===i||Ve(i))?(e.exit("thematicBreak"),t(i)):n(i)}function l(t){return t===o?(e.consume(t),r++,l):(e.exit("thematicBreakSequence"),Ue(t)?Be(e,i,"whitespace")(t):i(t))}}};const st={continuation:{tokenize:function(e,t,n){const o=this;return o.containerState._closeFlow=void 0,e.check(Qe,r,i);function r(n){return o.containerState.furtherBlankLines=o.containerState.furtherBlankLines||o.containerState.initialBlankLine,Be(e,t,"listItemIndent",o.containerState.size+1)(n)}function i(n){return o.containerState.furtherBlankLines||!Ue(n)?(o.containerState.furtherBlankLines=void 0,o.containerState.initialBlankLine=void 0,l(n)):(o.containerState.furtherBlankLines=void 0,o.containerState.initialBlankLine=void 0,e.attempt(ut,t,l)(n))}function l(r){return o.containerState._closeFlow=!0,o.interrupt=void 0,Be(e,e.attempt(st,t,n),"linePrefix",o.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(r)}}},exit:function(e){e.exit(this.containerState.type)},name:"list",tokenize:function(e,t,n){const o=this,r=o.events[o.events.length-1];let i=r&&"linePrefix"===r[1].type?r[2].sliceSerialize(r[1],!0).length:0,l=0;return function(t){const r=o.containerState.type||(42===t||43===t||45===t?"listUnordered":"listOrdered");if("listUnordered"===r?!o.containerState.marker||t===o.containerState.marker:Fe(t)){if(o.containerState.type||(o.containerState.type=r,e.enter(r,{_container:!0})),"listUnordered"===r)return e.enter("listItemPrefix"),42===t||45===t?e.check(at,n,s)(t):s(t);if(!o.interrupt||49===t)return e.enter("listItemPrefix"),e.enter("listItemValue"),a(t)}return n(t)};function a(t){return Fe(t)&&++l<10?(e.consume(t),a):(!o.interrupt||l<2)&&(o.containerState.marker?t===o.containerState.marker:41===t||46===t)?(e.exit("listItemValue"),s(t)):n(t)}function s(t){return e.enter("listItemMarker"),e.consume(t),e.exit("listItemMarker"),o.containerState.marker=o.containerState.marker||t,e.check(Qe,o.interrupt?n:c,e.attempt(ct,d,u))}function c(e){return o.containerState.initialBlankLine=!0,i++,d(e)}function u(t){return Ue(t)?(e.enter("listItemPrefixWhitespace"),e.consume(t),e.exit("listItemPrefixWhitespace"),d):n(t)}function d(n){return o.containerState.size=i+o.sliceSerialize(e.exit("listItemPrefix"),!0).length,t(n)}}},ct={partial:!0,tokenize:function(e,t,n){const o=this;return Be(e,function(e){const r=o.events[o.events.length-1];return!Ue(e)&&r&&"listItemPrefixWhitespace"===r[1].type?t(e):n(e)},"listItemPrefixWhitespace",o.parser.constructs.disable.null.includes("codeIndented")?void 0:5)}},ut={partial:!0,tokenize:function(e,t,n){const o=this;return Be(e,function(e){const r=o.events[o.events.length-1];return r&&"listItemIndent"===r[1].type&&r[2].sliceSerialize(r[1],!0).length===o.containerState.size?t(e):n(e)},"listItemIndent",o.containerState.size+1)}};const dt={continuation:{tokenize:function(e,t,n){const o=this;return function(t){if(Ue(t))return Be(e,r,"linePrefix",o.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t);return r(t)};function r(o){return e.attempt(dt,t,n)(o)}}},exit:function(e){e.exit("blockQuote")},name:"blockQuote",tokenize:function(e,t,n){const o=this;return function(t){if(62===t){const n=o.containerState;return n.open||(e.enter("blockQuote",{_container:!0}),n.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(t),e.exit("blockQuoteMarker"),r}return n(t)};function r(n){return Ue(n)?(e.enter("blockQuotePrefixWhitespace"),e.consume(n),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),t):(e.exit("blockQuotePrefix"),t(n))}}};function ft(e,t,n,o,r,i,l,a,s){const c=s||Number.POSITIVE_INFINITY;let u=0;return function(t){if(60===t)return e.enter(o),e.enter(r),e.enter(i),e.consume(t),e.exit(i),d;if(null===t||32===t||41===t||He(t))return n(t);return e.enter(o),e.enter(l),e.enter(a),e.enter("chunkString",{contentType:"string"}),m(t)};function d(n){return 62===n?(e.enter(i),e.consume(n),e.exit(i),e.exit(r),e.exit(o),t):(e.enter(a),e.enter("chunkString",{contentType:"string"}),f(n))}function f(t){return 62===t?(e.exit("chunkString"),e.exit(a),d(t)):null===t||60===t||Ve(t)?n(t):(e.consume(t),92===t?p:f)}function p(t){return 60===t||62===t||92===t?(e.consume(t),f):f(t)}function m(r){return u||null!==r&&41!==r&&!qe(r)?u<c&&40===r?(e.consume(r),u++,m):41===r?(e.consume(r),u--,m):null===r||32===r||40===r||He(r)?n(r):(e.consume(r),92===r?h:m):(e.exit("chunkString"),e.exit(a),e.exit(l),e.exit(o),t(r))}function h(t){return 40===t||41===t||92===t?(e.consume(t),m):m(t)}}function pt(e,t,n,o,r,i){const l=this;let a,s=0;return function(t){return e.enter(o),e.enter(r),e.consume(t),e.exit(r),e.enter(i),c};function c(d){return s>999||null===d||91===d||93===d&&!a||94===d&&!s&&"_hiddenFootnoteSupport"in l.parser.constructs?n(d):93===d?(e.exit(i),e.enter(r),e.consume(d),e.exit(r),e.exit(o),t):Ve(d)?(e.enter("lineEnding"),e.consume(d),e.exit("lineEnding"),c):(e.enter("chunkString",{contentType:"string"}),u(d))}function u(t){return null===t||91===t||93===t||Ve(t)||s++>999?(e.exit("chunkString"),c(t)):(e.consume(t),a||(a=!Ue(t)),92===t?d:u)}function d(t){return 91===t||92===t||93===t?(e.consume(t),s++,u):u(t)}}function mt(e,t,n,o,r,i){let l;return function(t){if(34===t||39===t||40===t)return e.enter(o),e.enter(r),e.consume(t),e.exit(r),l=40===t?41:t,a;return n(t)};function a(n){return n===l?(e.enter(r),e.consume(n),e.exit(r),e.exit(o),t):(e.enter(i),s(n))}function s(t){return t===l?(e.exit(i),a(l)):null===t?n(t):Ve(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),Be(e,s,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),c(t))}function c(t){return t===l||null===t||Ve(t)?(e.exit("chunkString"),s(t)):(e.consume(t),92===t?u:c)}function u(t){return t===l||92===t?(e.consume(t),c):c(t)}}function ht(e,t){let n;return function o(r){if(Ve(r))return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),n=!0,o;if(Ue(r))return Be(e,o,n?"linePrefix":"lineSuffix")(r);return t(r)}}function gt(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}const vt={name:"definition",tokenize:function(e,t,n){const o=this;let r;return function(t){return e.enter("definition"),function(t){return pt.call(o,e,i,n,"definitionLabel","definitionLabelMarker","definitionLabelString")(t)}(t)};function i(t){return r=gt(o.sliceSerialize(o.events[o.events.length-1][1]).slice(1,-1)),58===t?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),l):n(t)}function l(t){return qe(t)?ht(e,a)(t):a(t)}function a(t){return ft(e,s,n,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(t)}function s(t){return e.attempt(bt,c,c)(t)}function c(t){return Ue(t)?Be(e,u,"whitespace")(t):u(t)}function u(i){return null===i||Ve(i)?(e.exit("definition"),o.parser.defined.push(r),t(i)):n(i)}}},bt={partial:!0,tokenize:function(e,t,n){return function(t){return qe(t)?ht(e,o)(t):n(t)};function o(t){return mt(e,r,n,"definitionTitle","definitionTitleMarker","definitionTitleString")(t)}function r(t){return Ue(t)?Be(e,i,"whitespace")(t):i(t)}function i(e){return null===e||Ve(e)?t(e):n(e)}}};const yt={name:"codeIndented",tokenize:function(e,t,n){const o=this;return function(t){return e.enter("codeIndented"),Be(e,r,"linePrefix",5)(t)};function r(e){const t=o.events[o.events.length-1];return t&&"linePrefix"===t[1].type&&t[2].sliceSerialize(t[1],!0).length>=4?i(e):n(e)}function i(t){return null===t?a(t):Ve(t)?e.attempt(xt,i,a)(t):(e.enter("codeFlowValue"),l(t))}function l(t){return null===t||Ve(t)?(e.exit("codeFlowValue"),i(t)):(e.consume(t),l)}function a(n){return e.exit("codeIndented"),t(n)}}},xt={partial:!0,tokenize:function(e,t,n){const o=this;return r;function r(t){return o.parser.lazy[o.now().line]?n(t):Ve(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),r):Be(e,i,"linePrefix",5)(t)}function i(e){const i=o.events[o.events.length-1];return i&&"linePrefix"===i[1].type&&i[2].sliceSerialize(i[1],!0).length>=4?t(e):Ve(e)?r(e):n(e)}}};const wt={name:"headingAtx",resolve:function(e,t){let n,o,r=e.length-2,i=3;"whitespace"===e[i][1].type&&(i+=2);r-2>i&&"whitespace"===e[r][1].type&&(r-=2);"atxHeadingSequence"===e[r][1].type&&(i===r-1||r-4>i&&"whitespace"===e[r-2][1].type)&&(r-=i+1===r?2:4);r>i&&(n={type:"atxHeadingText",start:e[i][1].start,end:e[r][1].end},o={type:"chunkText",start:e[i][1].start,end:e[r][1].end,contentType:"text"},Se(e,i,r-i+1,[["enter",n,t],["enter",o,t],["exit",o,t],["exit",n,t]]));return e},tokenize:function(e,t,n){let o=0;return function(t){return e.enter("atxHeading"),function(t){return e.enter("atxHeadingSequence"),r(t)}(t)};function r(t){return 35===t&&o++<6?(e.consume(t),r):null===t||qe(t)?(e.exit("atxHeadingSequence"),i(t)):n(t)}function i(n){return 35===n?(e.enter("atxHeadingSequence"),l(n)):null===n||Ve(n)?(e.exit("atxHeading"),t(n)):Ue(n)?Be(e,i,"whitespace")(n):(e.enter("atxHeadingText"),a(n))}function l(t){return 35===t?(e.consume(t),l):(e.exit("atxHeadingSequence"),i(t))}function a(t){return null===t||35===t||qe(t)?(e.exit("atxHeadingText"),i(t)):(e.consume(t),a)}}};const Ct={name:"setextUnderline",resolveTo:function(e,t){let n,o,r,i=e.length;for(;i--;)if("enter"===e[i][0]){if("content"===e[i][1].type){n=i;break}"paragraph"===e[i][1].type&&(o=i)}else"content"===e[i][1].type&&e.splice(i,1),r||"definition"!==e[i][1].type||(r=i);const l={type:"setextHeading",start:{...e[n][1].start},end:{...e[e.length-1][1].end}};e[o][1].type="setextHeadingText",r?(e.splice(o,0,["enter",l,t]),e.splice(r+1,0,["exit",e[n][1],t]),e[n][1].end={...e[r][1].end}):e[n][1]=l;return e.push(["exit",l,t]),e},tokenize:function(e,t,n){const o=this;let r;return function(t){let l,a=o.events.length;for(;a--;)if("lineEnding"!==o.events[a][1].type&&"linePrefix"!==o.events[a][1].type&&"content"!==o.events[a][1].type){l="paragraph"===o.events[a][1].type;break}if(!o.parser.lazy[o.now().line]&&(o.interrupt||l))return e.enter("setextHeadingLine"),r=t,function(t){return e.enter("setextHeadingLineSequence"),i(t)}(t);return n(t)};function i(t){return t===r?(e.consume(t),i):(e.exit("setextHeadingLineSequence"),Ue(t)?Be(e,l,"lineSuffix")(t):l(t))}function l(o){return null===o||Ve(o)?(e.exit("setextHeadingLine"),t(o)):n(o)}}};const kt=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],$t=["pre","script","style","textarea"],St={concrete:!0,name:"htmlFlow",resolveTo:function(e){let t=e.length;for(;t--&&("enter"!==e[t][0]||"htmlFlow"!==e[t][1].type););t>1&&"linePrefix"===e[t-2][1].type&&(e[t][1].start=e[t-2][1].start,e[t+1][1].start=e[t-2][1].start,e.splice(t-2,2));return e},tokenize:function(e,t,n){const o=this;let r,i,l,a,s;return function(t){return function(t){return e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(t),c}(t)};function c(a){return 33===a?(e.consume(a),u):47===a?(e.consume(a),i=!0,p):63===a?(e.consume(a),r=3,o.interrupt?t:j):Re(a)?(e.consume(a),l=String.fromCharCode(a),m):n(a)}function u(i){return 45===i?(e.consume(i),r=2,d):91===i?(e.consume(i),r=5,a=0,f):Re(i)?(e.consume(i),r=4,o.interrupt?t:j):n(i)}function d(r){return 45===r?(e.consume(r),o.interrupt?t:j):n(r)}function f(r){const i="CDATA[";return r===i.charCodeAt(a++)?(e.consume(r),6===a?o.interrupt?t:A:f):n(r)}function p(t){return Re(t)?(e.consume(t),l=String.fromCharCode(t),m):n(t)}function m(a){if(null===a||47===a||62===a||qe(a)){const s=47===a,c=l.toLowerCase();return s||i||!$t.includes(c)?kt.includes(l.toLowerCase())?(r=6,s?(e.consume(a),h):o.interrupt?t(a):A(a)):(r=7,o.interrupt&&!o.parser.lazy[o.now().line]?n(a):i?g(a):v(a)):(r=1,o.interrupt?t(a):A(a))}return 45===a||De(a)?(e.consume(a),l+=String.fromCharCode(a),m):n(a)}function h(r){return 62===r?(e.consume(r),o.interrupt?t:A):n(r)}function g(t){return Ue(t)?(e.consume(t),g):$(t)}function v(t){return 47===t?(e.consume(t),$):58===t||95===t||Re(t)?(e.consume(t),b):Ue(t)?(e.consume(t),v):$(t)}function b(t){return 45===t||46===t||58===t||95===t||De(t)?(e.consume(t),b):y(t)}function y(t){return 61===t?(e.consume(t),x):Ue(t)?(e.consume(t),y):v(t)}function x(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),s=t,w):Ue(t)?(e.consume(t),x):C(t)}function w(t){return t===s?(e.consume(t),s=null,k):null===t||Ve(t)?n(t):(e.consume(t),w)}function C(t){return null===t||34===t||39===t||47===t||60===t||61===t||62===t||96===t||qe(t)?y(t):(e.consume(t),C)}function k(e){return 47===e||62===e||Ue(e)?v(e):n(e)}function $(t){return 62===t?(e.consume(t),S):n(t)}function S(t){return null===t||Ve(t)?A(t):Ue(t)?(e.consume(t),S):n(t)}function A(t){return 45===t&&2===r?(e.consume(t),z):60===t&&1===r?(e.consume(t),M):62===t&&4===r?(e.consume(t),N):63===t&&3===r?(e.consume(t),j):93===t&&5===r?(e.consume(t),T):!Ve(t)||6!==r&&7!==r?null===t||Ve(t)?(e.exit("htmlFlowData"),E(t)):(e.consume(t),A):(e.exit("htmlFlowData"),e.check(At,B,E)(t))}function E(t){return e.check(Et,I,B)(t)}function I(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),O}function O(t){return null===t||Ve(t)?E(t):(e.enter("htmlFlowData"),A(t))}function z(t){return 45===t?(e.consume(t),j):A(t)}function M(t){return 47===t?(e.consume(t),l="",P):A(t)}function P(t){if(62===t){const n=l.toLowerCase();return $t.includes(n)?(e.consume(t),N):A(t)}return Re(t)&&l.length<8?(e.consume(t),l+=String.fromCharCode(t),P):A(t)}function T(t){return 93===t?(e.consume(t),j):A(t)}function j(t){return 62===t?(e.consume(t),N):45===t&&2===r?(e.consume(t),j):A(t)}function N(t){return null===t||Ve(t)?(e.exit("htmlFlowData"),B(t)):(e.consume(t),N)}function B(n){return e.exit("htmlFlow"),t(n)}}},At={partial:!0,tokenize:function(e,t,n){return function(o){return e.enter("lineEnding"),e.consume(o),e.exit("lineEnding"),e.attempt(Qe,t,n)}}},Et={partial:!0,tokenize:function(e,t,n){const o=this;return function(t){if(Ve(t))return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),r;return n(t)};function r(e){return o.parser.lazy[o.now().line]?n(e):t(e)}}};const It={partial:!0,tokenize:function(e,t,n){const o=this;return function(t){if(null===t)return n(t);return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),r};function r(e){return o.parser.lazy[o.now().line]?n(e):t(e)}}},Ot={concrete:!0,name:"codeFenced",tokenize:function(e,t,n){const o=this,r={partial:!0,tokenize:function(e,t,n){let r=0;return l;function l(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),s}function s(t){return e.enter("codeFencedFence"),Ue(t)?Be(e,c,"linePrefix",o.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):c(t)}function c(t){return t===i?(e.enter("codeFencedFenceSequence"),u(t)):n(t)}function u(t){return t===i?(r++,e.consume(t),u):r>=a?(e.exit("codeFencedFenceSequence"),Ue(t)?Be(e,d,"whitespace")(t):d(t)):n(t)}function d(o){return null===o||Ve(o)?(e.exit("codeFencedFence"),t(o)):n(o)}}};let i,l=0,a=0;return function(t){return function(t){const n=o.events[o.events.length-1];return l=n&&"linePrefix"===n[1].type?n[2].sliceSerialize(n[1],!0).length:0,i=t,e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),s(t)}(t)};function s(t){return t===i?(a++,e.consume(t),s):a<3?n(t):(e.exit("codeFencedFenceSequence"),Ue(t)?Be(e,c,"whitespace")(t):c(t))}function c(n){return null===n||Ve(n)?(e.exit("codeFencedFence"),o.interrupt?t(n):e.check(It,p,b)(n)):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),u(n))}function u(t){return null===t||Ve(t)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),c(t)):Ue(t)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),Be(e,d,"whitespace")(t)):96===t&&t===i?n(t):(e.consume(t),u)}function d(t){return null===t||Ve(t)?c(t):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),f(t))}function f(t){return null===t||Ve(t)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),c(t)):96===t&&t===i?n(t):(e.consume(t),f)}function p(t){return e.attempt(r,b,m)(t)}function m(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),h}function h(t){return l>0&&Ue(t)?Be(e,g,"linePrefix",l+1)(t):g(t)}function g(t){return null===t||Ve(t)?e.check(It,p,b)(t):(e.enter("codeFlowValue"),v(t))}function v(t){return null===t||Ve(t)?(e.exit("codeFlowValue"),g(t)):(e.consume(t),v)}function b(n){return e.exit("codeFenced"),t(n)}}};const zt=document.createElement("i");function Mt(e){const t="&"+e+";";zt.innerHTML=t;const n=zt.textContent;return(59!==n.charCodeAt(n.length-1)||"semi"===e)&&(n!==t&&n)}const Pt={name:"characterReference",tokenize:function(e,t,n){const o=this;let r,i,l=0;return function(t){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(t),e.exit("characterReferenceMarker"),a};function a(t){return 35===t?(e.enter("characterReferenceMarkerNumeric"),e.consume(t),e.exit("characterReferenceMarkerNumeric"),s):(e.enter("characterReferenceValue"),r=31,i=De,c(t))}function s(t){return 88===t||120===t?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(t),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),r=6,i=_e,c):(e.enter("characterReferenceValue"),r=7,i=Fe,c(t))}function c(a){if(59===a&&l){const r=e.exit("characterReferenceValue");return i!==De||Mt(o.sliceSerialize(r))?(e.enter("characterReferenceMarker"),e.consume(a),e.exit("characterReferenceMarker"),e.exit("characterReference"),t):n(a)}return i(a)&&l++<r?(e.consume(a),c):n(a)}}};const Tt={name:"characterEscape",tokenize:function(e,t,n){return function(t){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(t),e.exit("escapeMarker"),o};function o(o){return We(o)?(e.enter("characterEscapeValue"),e.consume(o),e.exit("characterEscapeValue"),e.exit("characterEscape"),t):n(o)}}};const jt={name:"lineEnding",tokenize:function(e,t){return function(n){return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),Be(e,t,"linePrefix")}}};function Nt(e,t,n){const o=[];let r=-1;for(;++r<e.length;){const i=e[r].resolveAll;i&&!o.includes(i)&&(t=i(t,n),o.push(i))}return t}const Bt={name:"labelEnd",resolveAll:function(e){let t=-1;const n=[];for(;++t<e.length;){const o=e[t][1];if(n.push(e[t]),"labelImage"===o.type||"labelLink"===o.type||"labelEnd"===o.type){const e="labelImage"===o.type?4:2;o.type="data",t+=e}}e.length!==n.length&&Se(e,0,e.length,n);return e},resolveTo:function(e,t){let n,o,r,i,l=e.length,a=0;for(;l--;)if(n=e[l][1],o){if("link"===n.type||"labelLink"===n.type&&n._inactive)break;"enter"===e[l][0]&&"labelLink"===n.type&&(n._inactive=!0)}else if(r){if("enter"===e[l][0]&&("labelImage"===n.type||"labelLink"===n.type)&&!n._balanced&&(o=l,"labelLink"!==n.type)){a=2;break}}else"labelEnd"===n.type&&(r=l);const s={type:"labelLink"===e[o][1].type?"link":"image",start:{...e[o][1].start},end:{...e[e.length-1][1].end}},c={type:"label",start:{...e[o][1].start},end:{...e[r][1].end}},u={type:"labelText",start:{...e[o+a+2][1].end},end:{...e[r-2][1].start}};return i=[["enter",s,t],["enter",c,t]],i=Ae(i,e.slice(o+1,o+a+3)),i=Ae(i,[["enter",u,t]]),i=Ae(i,Nt(t.parser.constructs.insideSpan.null,e.slice(o+a+4,r-3),t)),i=Ae(i,[["exit",u,t],e[r-2],e[r-1],["exit",c,t]]),i=Ae(i,e.slice(r+1)),i=Ae(i,[["exit",s,t]]),Se(e,o,e.length,i),e},tokenize:function(e,t,n){const o=this;let r,i,l=o.events.length;for(;l--;)if(("labelImage"===o.events[l][1].type||"labelLink"===o.events[l][1].type)&&!o.events[l][1]._balanced){r=o.events[l][1];break}return function(t){if(!r)return n(t);if(r._inactive)return u(t);return i=o.parser.defined.includes(gt(o.sliceSerialize({start:r.end,end:o.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelEnd"),a};function a(t){return 40===t?e.attempt(Rt,c,i?c:u)(t):91===t?e.attempt(Dt,c,i?s:u)(t):i?c(t):u(t)}function s(t){return e.attempt(Lt,c,u)(t)}function c(e){return t(e)}function u(e){return r._balanced=!0,n(e)}}},Rt={tokenize:function(e,t,n){return function(t){return e.enter("resource"),e.enter("resourceMarker"),e.consume(t),e.exit("resourceMarker"),o};function o(t){return qe(t)?ht(e,r)(t):r(t)}function r(t){return 41===t?c(t):ft(e,i,l,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(t)}function i(t){return qe(t)?ht(e,a)(t):c(t)}function l(e){return n(e)}function a(t){return 34===t||39===t||40===t?mt(e,s,n,"resourceTitle","resourceTitleMarker","resourceTitleString")(t):c(t)}function s(t){return qe(t)?ht(e,c)(t):c(t)}function c(o){return 41===o?(e.enter("resourceMarker"),e.consume(o),e.exit("resourceMarker"),e.exit("resource"),t):n(o)}}},Dt={tokenize:function(e,t,n){const o=this;return function(t){return pt.call(o,e,r,i,"reference","referenceMarker","referenceString")(t)};function r(e){return o.parser.defined.includes(gt(o.sliceSerialize(o.events[o.events.length-1][1]).slice(1,-1)))?t(e):n(e)}function i(e){return n(e)}}},Lt={tokenize:function(e,t,n){return function(t){return e.enter("reference"),e.enter("referenceMarker"),e.consume(t),e.exit("referenceMarker"),o};function o(o){return 93===o?(e.enter("referenceMarker"),e.consume(o),e.exit("referenceMarker"),e.exit("reference"),t):n(o)}}};const Ht={name:"labelStartImage",resolveAll:Bt.resolveAll,tokenize:function(e,t,n){const o=this;return function(t){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(t),e.exit("labelImageMarker"),r};function r(t){return 91===t?(e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelImage"),i):n(t)}function i(e){return 94===e&&"_hiddenFootnoteSupport"in o.parser.constructs?n(e):t(e)}}};Wt(/[A-Za-z]/),Wt(/[\dA-Za-z]/),Wt(/[#-'*+\--9=?A-Z^-~]/);Wt(/\d/),Wt(/[\dA-Fa-f]/),Wt(/[!-/:-@[-`{-~]/);const Ft=Wt(/\p{P}|\p{S}/u),_t=Wt(/\s/);function Wt(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}function Vt(e){return null===e||function(e){return null!==e&&(e<0||32===e)}(e)||_t(e)?1:Ft(e)?2:void 0}const qt={name:"attention",resolveAll:function(e,t){let n,o,r,i,l,a,s,c,u=-1;for(;++u<e.length;)if("enter"===e[u][0]&&"attentionSequence"===e[u][1].type&&e[u][1]._close)for(n=u;n--;)if("exit"===e[n][0]&&"attentionSequence"===e[n][1].type&&e[n][1]._open&&t.sliceSerialize(e[n][1]).charCodeAt(0)===t.sliceSerialize(e[u][1]).charCodeAt(0)){if((e[n][1]._close||e[u][1]._open)&&(e[u][1].end.offset-e[u][1].start.offset)%3&&!((e[n][1].end.offset-e[n][1].start.offset+e[u][1].end.offset-e[u][1].start.offset)%3))continue;a=e[n][1].end.offset-e[n][1].start.offset>1&&e[u][1].end.offset-e[u][1].start.offset>1?2:1;const d={...e[n][1].end},f={...e[u][1].start};Ut(d,-a),Ut(f,a),i={type:a>1?"strongSequence":"emphasisSequence",start:d,end:{...e[n][1].end}},l={type:a>1?"strongSequence":"emphasisSequence",start:{...e[u][1].start},end:f},r={type:a>1?"strongText":"emphasisText",start:{...e[n][1].end},end:{...e[u][1].start}},o={type:a>1?"strong":"emphasis",start:{...i.start},end:{...l.end}},e[n][1].end={...i.start},e[u][1].start={...l.end},s=[],e[n][1].end.offset-e[n][1].start.offset&&(s=Ae(s,[["enter",e[n][1],t],["exit",e[n][1],t]])),s=Ae(s,[["enter",o,t],["enter",i,t],["exit",i,t],["enter",r,t]]),s=Ae(s,Nt(t.parser.constructs.insideSpan.null,e.slice(n+1,u),t)),s=Ae(s,[["exit",r,t],["enter",l,t],["exit",l,t],["exit",o,t]]),e[u][1].end.offset-e[u][1].start.offset?(c=2,s=Ae(s,[["enter",e[u][1],t],["exit",e[u][1],t]])):c=0,Se(e,n-1,u-n+3,s),u=n+s.length-c-2;break}u=-1;for(;++u<e.length;)"attentionSequence"===e[u][1].type&&(e[u][1].type="data");return e},tokenize:function(e,t){const n=this.parser.constructs.attentionMarkers.null,o=this.previous,r=Vt(o);let i;return function(t){return i=t,e.enter("attentionSequence"),l(t)};function l(a){if(a===i)return e.consume(a),l;const s=e.exit("attentionSequence"),c=Vt(a),u=!c||2===c&&r||n.includes(a),d=!r||2===r&&c||n.includes(o);return s._open=Boolean(42===i?u:u&&(r||!d)),s._close=Boolean(42===i?d:d&&(c||!u)),t(a)}}};function Ut(e,t){e.column+=t,e.offset+=t,e._bufferIndex+=t}const Xt={name:"autolink",tokenize:function(e,t,n){let o=0;return function(t){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(t),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),r};function r(t){return Re(t)?(e.consume(t),i):64===t?n(t):s(t)}function i(e){return 43===e||45===e||46===e||De(e)?(o=1,l(e)):s(e)}function l(t){return 58===t?(e.consume(t),o=0,a):(43===t||45===t||46===t||De(t))&&o++<32?(e.consume(t),l):(o=0,s(t))}function a(o){return 62===o?(e.exit("autolinkProtocol"),e.enter("autolinkMarker"),e.consume(o),e.exit("autolinkMarker"),e.exit("autolink"),t):null===o||32===o||60===o||He(o)?n(o):(e.consume(o),a)}function s(t){return 64===t?(e.consume(t),c):Le(t)?(e.consume(t),s):n(t)}function c(e){return De(e)?u(e):n(e)}function u(n){return 46===n?(e.consume(n),o=0,c):62===n?(e.exit("autolinkProtocol").type="autolinkEmail",e.enter("autolinkMarker"),e.consume(n),e.exit("autolinkMarker"),e.exit("autolink"),t):d(n)}function d(t){if((45===t||De(t))&&o++<63){const n=45===t?d:u;return e.consume(t),n}return n(t)}}};const Gt={name:"htmlText",tokenize:function(e,t,n){const o=this;let r,i,l;return function(t){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(t),a};function a(t){return 33===t?(e.consume(t),s):47===t?(e.consume(t),x):63===t?(e.consume(t),b):Re(t)?(e.consume(t),k):n(t)}function s(t){return 45===t?(e.consume(t),c):91===t?(e.consume(t),i=0,p):Re(t)?(e.consume(t),v):n(t)}function c(t){return 45===t?(e.consume(t),f):n(t)}function u(t){return null===t?n(t):45===t?(e.consume(t),d):Ve(t)?(l=u,P(t)):(e.consume(t),u)}function d(t){return 45===t?(e.consume(t),f):u(t)}function f(e){return 62===e?M(e):45===e?d(e):u(e)}function p(t){const o="CDATA[";return t===o.charCodeAt(i++)?(e.consume(t),6===i?m:p):n(t)}function m(t){return null===t?n(t):93===t?(e.consume(t),h):Ve(t)?(l=m,P(t)):(e.consume(t),m)}function h(t){return 93===t?(e.consume(t),g):m(t)}function g(t){return 62===t?M(t):93===t?(e.consume(t),g):m(t)}function v(t){return null===t||62===t?M(t):Ve(t)?(l=v,P(t)):(e.consume(t),v)}function b(t){return null===t?n(t):63===t?(e.consume(t),y):Ve(t)?(l=b,P(t)):(e.consume(t),b)}function y(e){return 62===e?M(e):b(e)}function x(t){return Re(t)?(e.consume(t),w):n(t)}function w(t){return 45===t||De(t)?(e.consume(t),w):C(t)}function C(t){return Ve(t)?(l=C,P(t)):Ue(t)?(e.consume(t),C):M(t)}function k(t){return 45===t||De(t)?(e.consume(t),k):47===t||62===t||qe(t)?$(t):n(t)}function $(t){return 47===t?(e.consume(t),M):58===t||95===t||Re(t)?(e.consume(t),S):Ve(t)?(l=$,P(t)):Ue(t)?(e.consume(t),$):M(t)}function S(t){return 45===t||46===t||58===t||95===t||De(t)?(e.consume(t),S):A(t)}function A(t){return 61===t?(e.consume(t),E):Ve(t)?(l=A,P(t)):Ue(t)?(e.consume(t),A):$(t)}function E(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),r=t,I):Ve(t)?(l=E,P(t)):Ue(t)?(e.consume(t),E):(e.consume(t),O)}function I(t){return t===r?(e.consume(t),r=void 0,z):null===t?n(t):Ve(t)?(l=I,P(t)):(e.consume(t),I)}function O(t){return null===t||34===t||39===t||60===t||61===t||96===t?n(t):47===t||62===t||qe(t)?$(t):(e.consume(t),O)}function z(e){return 47===e||62===e||qe(e)?$(e):n(e)}function M(o){return 62===o?(e.consume(o),e.exit("htmlTextData"),e.exit("htmlText"),t):n(o)}function P(t){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),T}function T(t){return Ue(t)?Be(e,j,"linePrefix",o.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):j(t)}function j(t){return e.enter("htmlTextData"),l(t)}}};const Kt={name:"labelStartLink",resolveAll:Bt.resolveAll,tokenize:function(e,t,n){const o=this;return function(t){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelLink"),r};function r(e){return 94===e&&"_hiddenFootnoteSupport"in o.parser.constructs?n(e):t(e)}}};const Yt={name:"hardBreakEscape",tokenize:function(e,t,n){return function(t){return e.enter("hardBreakEscape"),e.consume(t),o};function o(o){return Ve(o)?(e.exit("hardBreakEscape"),t(o)):n(o)}}};const Qt={name:"codeText",previous:function(e){return 96!==e||"characterEscape"===this.events[this.events.length-1][1].type},resolve:function(e){let t,n,o=e.length-4,r=3;if(!("lineEnding"!==e[r][1].type&&"space"!==e[r][1].type||"lineEnding"!==e[o][1].type&&"space"!==e[o][1].type))for(t=r;++t<o;)if("codeTextData"===e[t][1].type){e[r][1].type="codeTextPadding",e[o][1].type="codeTextPadding",r+=2,o-=2;break}t=r-1,o++;for(;++t<=o;)void 0===n?t!==o&&"lineEnding"!==e[t][1].type&&(n=t):t!==o&&"lineEnding"!==e[t][1].type||(e[n][1].type="codeTextData",t!==n+2&&(e[n][1].end=e[t-1][1].end,e.splice(n+2,t-n-2),o-=t-n-2,t=n+2),n=void 0);return e},tokenize:function(e,t,n){let o,r,i=0;return function(t){return e.enter("codeText"),e.enter("codeTextSequence"),l(t)};function l(t){return 96===t?(e.consume(t),i++,l):(e.exit("codeTextSequence"),a(t))}function a(t){return null===t?n(t):32===t?(e.enter("space"),e.consume(t),e.exit("space"),a):96===t?(r=e.enter("codeTextSequence"),o=0,c(t)):Ve(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),a):(e.enter("codeTextData"),s(t))}function s(t){return null===t||32===t||96===t||Ve(t)?(e.exit("codeTextData"),a(t)):(e.consume(t),s)}function c(n){return 96===n?(e.consume(n),o++,c):o===i?(e.exit("codeTextSequence"),e.exit("codeText"),t(n)):(r.type="codeTextData",s(n))}}};const Zt={42:st,43:st,45:st,48:st,49:st,50:st,51:st,52:st,53:st,54:st,55:st,56:st,57:st,62:dt},Jt={91:vt},en={[-2]:yt,[-1]:yt,32:yt},tn={35:wt,42:at,45:[Ct,at],60:St,61:Ct,95:at,96:Ot,126:Ot},nn={38:Pt,92:Tt},on={[-5]:jt,[-4]:jt,[-3]:jt,33:Ht,38:Pt,42:qt,60:[Xt,Gt],91:Kt,92:[Yt,Tt],93:Bt,95:qt,96:Qt},rn={null:[qt,tt]},ln={null:[42,95]},an={null:[]};function sn(e,t,n){let o={_bufferIndex:-1,_index:0,line:n&&n.line||1,column:n&&n.column||1,offset:n&&n.offset||0};const r={},i=[];let l=[],a=[],s=!0;const c={attempt:b(function(e,t){y(e,t.from)}),check:b(v),consume:function(e){Ve(e)?(o.line++,o.column=1,o.offset+=-3===e?2:1,x()):-1!==e&&(o.column++,o.offset++);o._bufferIndex<0?o._index++:(o._bufferIndex++,o._bufferIndex===l[o._index].length&&(o._bufferIndex=-1,o._index++));u.previous=e,s=!0},enter:function(e,t){const n=t||{};return n.type=e,n.start=m(),u.events.push(["enter",n,u]),a.push(n),n},exit:function(e){const t=a.pop();return t.end=m(),u.events.push(["exit",t,u]),t},interrupt:b(v,{interrupt:!0})},u={code:null,containerState:{},defineSkip:function(e){r[e.line]=e.column,x()},events:[],now:m,parser:e,previous:null,sliceSerialize:function(e,t){return function(e,t){let n=-1;const o=[];let r;for(;++n<e.length;){const i=e[n];let l;if("string"==typeof i)l=i;else switch(i){case-5:l="\r";break;case-4:l="\n";break;case-3:l="\r\n";break;case-2:l=t?" ":"\t";break;case-1:if(!t&&r)continue;l=" ";break;default:l=String.fromCharCode(i)}r=-2===i,o.push(l)}return o.join("")}(p(e),t)},sliceStream:p,write:function(e){if(l=Ae(l,e),h(),null!==l[l.length-1])return[];return y(t,0),u.events=Nt(i,u.events,u),u.events}};let d,f=t.tokenize.call(u,c);return t.resolveAll&&i.push(t),u;function p(e){return function(e,t){const n=t.start._index,o=t.start._bufferIndex,r=t.end._index,i=t.end._bufferIndex;let l;if(n===r)l=[e[n].slice(o,i)];else{if(l=e.slice(n,r),o>-1){const e=l[0];"string"==typeof e?l[0]=e.slice(o):l.shift()}i>0&&l.push(e[r].slice(0,i))}return l}(l,e)}function m(){const{_bufferIndex:e,_index:t,line:n,column:r,offset:i}=o;return{_bufferIndex:e,_index:t,line:n,column:r,offset:i}}function h(){let e;for(;o._index<l.length;){const t=l[o._index];if("string"==typeof t)for(e=o._index,o._bufferIndex<0&&(o._bufferIndex=0);o._index===e&&o._bufferIndex<t.length;)g(t.charCodeAt(o._bufferIndex));else g(t)}}function g(e){s=void 0,d=e,f=f(e)}function v(e,t){t.restore()}function b(e,t){return function(n,r,i){let l,d,f,p;return Array.isArray(n)?h(n):"tokenize"in n?h([n]):function(e){return t;function t(t){const n=null!==t&&e[t],o=null!==t&&e.null;return h([...Array.isArray(n)?n:n?[n]:[],...Array.isArray(o)?o:o?[o]:[]])(t)}}(n);function h(e){return l=e,d=0,0===e.length?i:g(e[d])}function g(e){return function(n){p=function(){const e=m(),t=u.previous,n=u.currentConstruct,r=u.events.length,i=Array.from(a);return{from:r,restore:l};function l(){o=e,u.previous=t,u.currentConstruct=n,u.events.length=r,a=i,x()}}(),f=e,e.partial||(u.currentConstruct=e);if(e.name&&u.parser.constructs.disable.null.includes(e.name))return b(n);return e.tokenize.call(t?Object.assign(Object.create(u),t):u,c,v,b)(n)}}function v(t){return s=!0,e(f,p),r}function b(e){return s=!0,p.restore(),++d<l.length?g(l[d]):i}}}function y(e,t){e.resolveAll&&!i.includes(e)&&i.push(e),e.resolve&&Se(u.events,t,u.events.length-t,e.resolve(u.events.slice(t),u)),e.resolveTo&&(u.events=e.resolveTo(u.events,u))}function x(){o.line in r&&o.column<2&&(o.column=r[o.line],o.offset+=r[o.line]-1)}}function cn(e){const t=function(e){const t={};let n=-1;for(;++n<e.length;)Pe(t,e[n]);return t}([r,...(e||{}).extensions||[]]),n={constructs:t,content:o(Ge),defined:[],document:o(Ke),flow:o(et),lazy:{},string:o(nt),text:o(ot)};return n;function o(e){return function(t){return sn(n,e,t)}}}const un=/[\0\t\n\r]/g;function dn(e,t){const n=Number.parseInt(e,t);return n<9||11===n||n>13&&n<32||n>126&&n<160||n>55295&&n<57344||n>64975&&n<65008||!(65535&~n)||65534==(65535&n)||n>1114111?"�":String.fromCodePoint(n)}const fn=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function pn(e,t,n){if(t)return t;if(35===n.charCodeAt(0)){const e=n.charCodeAt(1),t=120===e||88===e;return dn(n.slice(t?2:1),t?16:10)}return Mt(n)||e}function mn(e){return e&&"object"==typeof e?"position"in e||"type"in e?gn(e.position):"start"in e||"end"in e?gn(e):"line"in e||"column"in e?hn(e):"":""}function hn(e){return vn(e&&e.line)+":"+vn(e&&e.column)}function gn(e){return hn(e&&e.start)+"-"+hn(e&&e.end)}function vn(e){return e&&"number"==typeof e?e:1}const bn={}.hasOwnProperty;function yn(e,t,n){return"string"!=typeof t&&(n=t,t=void 0),function(e){const t={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:i(te),autolinkProtocol:S,autolinkEmail:S,atxHeading:i(Q),blockQuote:i(U),characterEscape:S,characterReference:S,codeFenced:i(X),codeFencedFenceInfo:l,codeFencedFenceMeta:l,codeIndented:i(X,l),codeText:i(G,l),codeTextData:S,data:S,codeFlowValue:S,definition:i(K),definitionDestinationString:l,definitionLabelString:l,definitionTitleString:l,emphasis:i(Y),hardBreakEscape:i(Z),hardBreakTrailing:i(Z),htmlFlow:i(J,l),htmlFlowData:S,htmlText:i(J,l),htmlTextData:S,image:i(ee),label:l,link:i(te),listItem:i(oe),listItemValue:f,listOrdered:i(ne,d),listUnordered:i(ne),paragraph:i(re),reference:L,referenceString:l,resourceDestinationString:l,resourceTitleString:l,setextHeading:i(Q),strong:i(ie),thematicBreak:i(ae)},exit:{atxHeading:s(),atxHeadingSequence:w,autolink:s(),autolinkEmail:q,autolinkProtocol:V,blockQuote:s(),characterEscapeValue:A,characterReferenceMarkerHexadecimal:F,characterReferenceMarkerNumeric:F,characterReferenceValue:_,characterReference:W,codeFenced:s(g),codeFencedFence:h,codeFencedFenceInfo:p,codeFencedFenceMeta:m,codeFlowValue:A,codeIndented:s(v),codeText:s(M),codeTextData:A,data:A,definition:s(),definitionDestinationString:x,definitionLabelString:b,definitionTitleString:y,emphasis:s(),hardBreakEscape:s(I),hardBreakTrailing:s(I),htmlFlow:s(O),htmlFlowData:A,htmlText:s(z),htmlTextData:A,image:s(T),label:N,labelText:j,lineEnding:E,link:s(P),listItem:s(),listOrdered:s(),listUnordered:s(),paragraph:s(),referenceString:H,resourceDestinationString:B,resourceTitleString:R,resource:D,setextHeading:s($),setextHeadingLineSequence:k,setextHeadingText:C,strong:s(),thematicBreak:s()}};wn(t,(e||{}).mdastExtensions||[]);const n={};return o;function o(e){let o={type:"root",children:[]};const i={stack:[o],tokenStack:[],config:t,enter:a,exit:c,buffer:l,resume:u,data:n},s=[];let d=-1;for(;++d<e.length;)if("listOrdered"===e[d][1].type||"listUnordered"===e[d][1].type)if("enter"===e[d][0])s.push(d);else{d=r(e,s.pop(),d)}for(d=-1;++d<e.length;){const n=t[e[d][0]];bn.call(n,e[d][1].type)&&n[e[d][1].type].call(Object.assign({sliceSerialize:e[d][2].sliceSerialize},i),e[d][1])}if(i.tokenStack.length>0){const e=i.tokenStack[i.tokenStack.length-1];(e[1]||kn).call(i,void 0,e[0])}for(o.position={start:xn(e.length>0?e[0][1].start:{line:1,column:1,offset:0}),end:xn(e.length>0?e[e.length-2][1].end:{line:1,column:1,offset:0})},d=-1;++d<t.transforms.length;)o=t.transforms[d](o)||o;return o}function r(e,t,n){let o,r,i,l,a=t-1,s=-1,c=!1;for(;++a<=n;){const t=e[a];switch(t[1].type){case"listUnordered":case"listOrdered":case"blockQuote":"enter"===t[0]?s++:s--,l=void 0;break;case"lineEndingBlank":"enter"===t[0]&&(!o||l||s||i||(i=a),l=void 0);break;case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:l=void 0}if(!s&&"enter"===t[0]&&"listItemPrefix"===t[1].type||-1===s&&"exit"===t[0]&&("listUnordered"===t[1].type||"listOrdered"===t[1].type)){if(o){let l=a;for(r=void 0;l--;){const t=e[l];if("lineEnding"===t[1].type||"lineEndingBlank"===t[1].type){if("exit"===t[0])continue;r&&(e[r][1].type="lineEndingBlank",c=!0),t[1].type="lineEnding",r=l}else if("linePrefix"!==t[1].type&&"blockQuotePrefix"!==t[1].type&&"blockQuotePrefixWhitespace"!==t[1].type&&"blockQuoteMarker"!==t[1].type&&"listItemIndent"!==t[1].type)break}i&&(!r||i<r)&&(o._spread=!0),o.end=Object.assign({},r?e[r][1].start:t[1].end),e.splice(r||a,0,["exit",o,t[2]]),a++,n++}if("listItemPrefix"===t[1].type){const r={type:"listItem",_spread:!1,start:Object.assign({},t[1].start),end:void 0};o=r,e.splice(a,0,["enter",r,t[2]]),a++,n++,i=void 0,l=!0}}}return e[t][1]._spread=c,n}function i(e,t){return n;function n(n){a.call(this,e(n),n),t&&t.call(this,n)}}function l(){this.stack.push({type:"fragment",children:[]})}function a(e,t,n){this.stack[this.stack.length-1].children.push(e),this.stack.push(e),this.tokenStack.push([t,n||void 0]),e.position={start:xn(t.start),end:void 0}}function s(e){return t;function t(t){e&&e.call(this,t),c.call(this,t)}}function c(e,t){const n=this.stack.pop(),o=this.tokenStack.pop();if(!o)throw new Error("Cannot close `"+e.type+"` ("+mn({start:e.start,end:e.end})+"): it’s not open");if(o[0].type!==e.type)if(t)t.call(this,e,o[0]);else{(o[1]||kn).call(this,e,o[0])}n.position.end=xn(e.end)}function u(){return function(e,t){const n=t||Ce;return ke(e,"boolean"!=typeof n.includeImageAlt||n.includeImageAlt,"boolean"!=typeof n.includeHtml||n.includeHtml)}(this.stack.pop())}function d(){this.data.expectingFirstListItemValue=!0}function f(e){if(this.data.expectingFirstListItemValue){this.stack[this.stack.length-2].start=Number.parseInt(this.sliceSerialize(e),10),this.data.expectingFirstListItemValue=void 0}}function p(){const e=this.resume();this.stack[this.stack.length-1].lang=e}function m(){const e=this.resume();this.stack[this.stack.length-1].meta=e}function h(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)}function g(){const e=this.resume();this.stack[this.stack.length-1].value=e.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}function v(){const e=this.resume();this.stack[this.stack.length-1].value=e.replace(/(\r?\n|\r)$/g,"")}function b(e){const t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=gt(this.sliceSerialize(e)).toLowerCase()}function y(){const e=this.resume();this.stack[this.stack.length-1].title=e}function x(){const e=this.resume();this.stack[this.stack.length-1].url=e}function w(e){const t=this.stack[this.stack.length-1];if(!t.depth){const n=this.sliceSerialize(e).length;t.depth=n}}function C(){this.data.setextHeadingSlurpLineEnding=!0}function k(e){this.stack[this.stack.length-1].depth=61===this.sliceSerialize(e).codePointAt(0)?1:2}function $(){this.data.setextHeadingSlurpLineEnding=void 0}function S(e){const t=this.stack[this.stack.length-1].children;let n=t[t.length-1];n&&"text"===n.type||(n=le(),n.position={start:xn(e.start),end:void 0},t.push(n)),this.stack.push(n)}function A(e){const t=this.stack.pop();t.value+=this.sliceSerialize(e),t.position.end=xn(e.end)}function E(e){const n=this.stack[this.stack.length-1];if(this.data.atHardBreak){return n.children[n.children.length-1].position.end=xn(e.end),void(this.data.atHardBreak=void 0)}!this.data.setextHeadingSlurpLineEnding&&t.canContainEols.includes(n.type)&&(S.call(this,e),A.call(this,e))}function I(){this.data.atHardBreak=!0}function O(){const e=this.resume();this.stack[this.stack.length-1].value=e}function z(){const e=this.resume();this.stack[this.stack.length-1].value=e}function M(){const e=this.resume();this.stack[this.stack.length-1].value=e}function P(){const e=this.stack[this.stack.length-1];if(this.data.inReference){const t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}function T(){const e=this.stack[this.stack.length-1];if(this.data.inReference){const t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}function j(e){const t=this.sliceSerialize(e),n=this.stack[this.stack.length-2];n.label=function(e){return e.replace(fn,pn)}(t),n.identifier=gt(t).toLowerCase()}function N(){const e=this.stack[this.stack.length-1],t=this.resume(),n=this.stack[this.stack.length-1];if(this.data.inReference=!0,"link"===n.type){const t=e.children;n.children=t}else n.alt=t}function B(){const e=this.resume();this.stack[this.stack.length-1].url=e}function R(){const e=this.resume();this.stack[this.stack.length-1].title=e}function D(){this.data.inReference=void 0}function L(){this.data.referenceType="collapsed"}function H(e){const t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=gt(this.sliceSerialize(e)).toLowerCase(),this.data.referenceType="full"}function F(e){this.data.characterReferenceType=e.type}function _(e){const t=this.sliceSerialize(e),n=this.data.characterReferenceType;let o;if(n)o=dn(t,"characterReferenceMarkerNumeric"===n?10:16),this.data.characterReferenceType=void 0;else{o=Mt(t)}this.stack[this.stack.length-1].value+=o}function W(e){this.stack.pop().position.end=xn(e.end)}function V(e){A.call(this,e);this.stack[this.stack.length-1].url=this.sliceSerialize(e)}function q(e){A.call(this,e);this.stack[this.stack.length-1].url="mailto:"+this.sliceSerialize(e)}function U(){return{type:"blockquote",children:[]}}function X(){return{type:"code",lang:null,meta:null,value:""}}function G(){return{type:"inlineCode",value:""}}function K(){return{type:"definition",identifier:"",label:null,title:null,url:""}}function Y(){return{type:"emphasis",children:[]}}function Q(){return{type:"heading",depth:0,children:[]}}function Z(){return{type:"break"}}function J(){return{type:"html",value:""}}function ee(){return{type:"image",title:null,url:"",alt:null}}function te(){return{type:"link",title:null,url:"",children:[]}}function ne(e){return{type:"list",ordered:"listOrdered"===e.type,start:null,spread:e._spread,children:[]}}function oe(e){return{type:"listItem",spread:e._spread,checked:null,children:[]}}function re(){return{type:"paragraph",children:[]}}function ie(){return{type:"strong",children:[]}}function le(){return{type:"text",value:""}}function ae(){return{type:"thematicBreak"}}}(n)(function(e){for(;!Oe(e););return e}(cn(n).document().write(function(){let e,t=1,n="",o=!0;return function(r,i,l){const a=[];let s,c,u,d,f;for(r=n+("string"==typeof r?r.toString():new TextDecoder(i||void 0).decode(r)),u=0,n="",o&&(65279===r.charCodeAt(0)&&u++,o=void 0);u<r.length;){if(un.lastIndex=u,s=un.exec(r),d=s&&void 0!==s.index?s.index:r.length,f=r.charCodeAt(d),!s){n=r.slice(u);break}if(10===f&&u===d&&e)a.push(-3),e=void 0;else switch(e&&(a.push(-5),e=void 0),u<d&&(a.push(r.slice(u,d)),t+=d-u),f){case 0:a.push(65533),t++;break;case 9:for(c=4*Math.ceil(t/4),a.push(-2);t++<c;)a.push(-1);break;case 10:a.push(-4),t=1;break;default:e=!0,t=1}u=d+1}return l&&(e&&a.push(-5),n&&a.push(n),a.push(null)),a}}()(e,t,!0))))}function xn(e){return{line:e.line,column:e.column,offset:e.offset}}function wn(e,t){let n=-1;for(;++n<t.length;){const o=t[n];Array.isArray(o)?wn(e,o):Cn(e,o)}}function Cn(e,t){let n;for(n in t)if(bn.call(t,n))switch(n){case"canContainEols":{const o=t[n];o&&e[n].push(...o);break}case"transforms":{const o=t[n];o&&e[n].push(...o);break}case"enter":case"exit":{const o=t[n];o&&Object.assign(e[n],o);break}}}function kn(e,t){throw e?new Error("Cannot close `"+e.type+"` ("+mn({start:e.start,end:e.end})+"): a different token (`"+t.type+"`, "+mn({start:t.start,end:t.end})+") is open"):new Error("Cannot close document, a token (`"+t.type+"`, "+mn({start:t.start,end:t.end})+") is still open")}function $n(e){const t=this;t.parser=function(n){return yn(n,{...t.data("settings"),...e,extensions:t.data("micromarkExtensions")||[],mdastExtensions:t.data("fromMarkdownExtensions")||[]})}}const Sn="object"==typeof self?self:globalThis,An=e=>((e,t)=>{const n=(t,n)=>(e.set(n,t),t),o=r=>{if(e.has(r))return e.get(r);const[i,l]=t[r];switch(i){case 0:case-1:return n(l,r);case 1:{const e=n([],r);for(const t of l)e.push(o(t));return e}case 2:{const e=n({},r);for(const[t,n]of l)e[o(t)]=o(n);return e}case 3:return n(new Date(l),r);case 4:{const{source:e,flags:t}=l;return n(new RegExp(e,t),r)}case 5:{const e=n(new Map,r);for(const[t,n]of l)e.set(o(t),o(n));return e}case 6:{const e=n(new Set,r);for(const t of l)e.add(o(t));return e}case 7:{const{name:e,message:t}=l;return n(new Sn[e](t),r)}case 8:return n(BigInt(l),r);case"BigInt":return n(Object(BigInt(l)),r);case"ArrayBuffer":return n(new Uint8Array(l).buffer,l);case"DataView":{const{buffer:e}=new Uint8Array(l);return n(new DataView(e),l)}}return n(new Sn[i](l),r)};return o})(new Map,e)(0),En="",{toString:In}={},{keys:On}=Object,zn=e=>{const t=typeof e;if("object"!==t||!e)return[0,t];const n=In.call(e).slice(8,-1);switch(n){case"Array":return[1,En];case"Object":return[2,En];case"Date":return[3,En];case"RegExp":return[4,En];case"Map":return[5,En];case"Set":return[6,En];case"DataView":return[1,n]}return n.includes("Array")?[1,n]:n.includes("Error")?[7,n]:[2,n]},Mn=([e,t])=>0===e&&("function"===t||"symbol"===t),Pn=(e,{json:t,lossy:n}={})=>{const o=[];return((e,t,n,o)=>{const r=(e,t)=>{const r=o.push(e)-1;return n.set(t,r),r},i=o=>{if(n.has(o))return n.get(o);let[l,a]=zn(o);switch(l){case 0:{let t=o;switch(a){case"bigint":l=8,t=o.toString();break;case"function":case"symbol":if(e)throw new TypeError("unable to serialize "+a);t=null;break;case"undefined":return r([-1],o)}return r([l,t],o)}case 1:{if(a){let e=o;return"DataView"===a?e=new Uint8Array(o.buffer):"ArrayBuffer"===a&&(e=new Uint8Array(o)),r([a,[...e]],o)}const e=[],t=r([l,e],o);for(const n of o)e.push(i(n));return t}case 2:{if(a)switch(a){case"BigInt":return r([a,o.toString()],o);case"Boolean":case"Number":case"String":return r([a,o.valueOf()],o)}if(t&&"toJSON"in o)return i(o.toJSON());const n=[],s=r([l,n],o);for(const t of On(o))!e&&Mn(zn(o[t]))||n.push([i(t),i(o[t])]);return s}case 3:return r([l,o.toISOString()],o);case 4:{const{source:e,flags:t}=o;return r([l,{source:e,flags:t}],o)}case 5:{const t=[],n=r([l,t],o);for(const[r,l]of o)(e||!Mn(zn(r))&&!Mn(zn(l)))&&t.push([i(r),i(l)]);return n}case 6:{const t=[],n=r([l,t],o);for(const r of o)!e&&Mn(zn(r))||t.push(i(r));return n}}const{message:s}=o;return r([l,{name:a,message:s}],o)};return i})(!(t||n),!!t,new Map,o)(e),o};var Tn="function"==typeof structuredClone?(e,t)=>t&&("json"in t||"lossy"in t)?An(Pn(e,t)):structuredClone(e):(e,t)=>An(Pn(e,t));Nn(/[A-Za-z]/);const jn=Nn(/[\dA-Za-z]/);Nn(/[#-'*+\--9=?A-Z^-~]/);Nn(/\d/),Nn(/[\dA-Fa-f]/),Nn(/[!-/:-@[-`{-~]/);Nn(/\p{P}|\p{S}/u),Nn(/\s/);function Nn(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}function Bn(e){const t=[];let n=-1,o=0,r=0;for(;++n<e.length;){const i=e.charCodeAt(n);let l="";if(37===i&&jn(e.charCodeAt(n+1))&&jn(e.charCodeAt(n+2)))r=2;else if(i<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(i))||(l=String.fromCharCode(i));else if(i>55295&&i<57344){const t=e.charCodeAt(n+1);i<56320&&t>56319&&t<57344?(l=String.fromCharCode(i,t),r=1):l="�"}else l=String.fromCharCode(i);l&&(t.push(e.slice(o,n),encodeURIComponent(l)),o=n+r+1,l=""),r&&(n+=r,r=0)}return t.join("")+e.slice(o)}function Rn(e,t){const n=[{type:"text",value:"↩"}];return t>1&&n.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(t)}]}),n}function Dn(e,t){return"Back to reference "+(e+1)+(t>1?"-"+t:"")}const Ln=function(e){if(null==e)return Fn;if("function"==typeof e)return Hn(e);if("object"==typeof e)return Array.isArray(e)?function(e){const t=[];let n=-1;for(;++n<e.length;)t[n]=Ln(e[n]);return Hn(o);function o(...e){let n=-1;for(;++n<t.length;)if(t[n].apply(this,e))return!0;return!1}}(e):function(e){const t=e;return Hn(n);function n(n){const o=n;let r;for(r in e)if(o[r]!==t[r])return!1;return!0}}(e);if("string"==typeof e)return function(e){return Hn(t);function t(t){return t&&t.type===e}}(e);throw new Error("Expected function, string, or object as test")};function Hn(e){return function(t,n,o){return Boolean(_n(t)&&e.call(this,t,"number"==typeof n?n:void 0,o||void 0))}}function Fn(){return!0}function _n(e){return null!==e&&"object"==typeof e&&"type"in e}const Wn=[],Vn=!0,qn=!1;function Un(e,t,n,o){let r;"function"==typeof t&&"function"!=typeof n?(o=n,n=t):r=t;const i=Ln(r),l=o?-1:1;!function e(r,a,s){const c=r&&"object"==typeof r?r:{};if("string"==typeof c.type){const e="string"==typeof c.tagName?c.tagName:"string"==typeof c.name?c.name:void 0;Object.defineProperty(u,"name",{value:"node ("+r.type+(e?"<"+e+">":"")+")"})}return u;function u(){let c,u,d,f=Wn;if((!t||i(r,a,s[s.length-1]||void 0))&&(f=function(e){if(Array.isArray(e))return e;if("number"==typeof e)return[Vn,e];return null==e?Wn:[e]}(n(r,s)),f[0]===qn))return f;if("children"in r&&r.children){const t=r;if(t.children&&"skip"!==f[0])for(u=(o?t.children.length:-1)+l,d=s.concat(t);u>-1&&u<t.children.length;){const n=t.children[u];if(c=e(n,u,d)(),c[0]===qn)return c;u="number"==typeof c[1]?c[1]:u+l}}return f}}(e,void 0,[])()}const Xn=Kn("end"),Gn=Kn("start");function Kn(e){return function(t){const n=t&&t.position&&t.position[e]||{};if("number"==typeof n.line&&n.line>0&&"number"==typeof n.column&&n.column>0)return{line:n.line,column:n.column,offset:"number"==typeof n.offset&&n.offset>-1?n.offset:void 0}}}function Yn(e,t){const n=t.referenceType;let o="]";if("collapsed"===n?o+="[]":"full"===n&&(o+="["+(t.label||t.identifier)+"]"),"imageReference"===t.type)return[{type:"text",value:"!["+t.alt+o}];const r=e.all(t),i=r[0];i&&"text"===i.type?i.value="["+i.value:r.unshift({type:"text",value:"["});const l=r[r.length-1];return l&&"text"===l.type?l.value+=o:r.push({type:"text",value:o}),r}function Qn(e){const t=e.spread;return null==t?e.children.length>1:t}function Zn(e){const t=String(e),n=/\r?\n|\r/g;let o=n.exec(t),r=0;const i=[];for(;o;)i.push(Jn(t.slice(r,o.index),r>0,!0),o[0]),r=o.index+o[0].length,o=n.exec(t);return i.push(Jn(t.slice(r),r>0,!1)),i.join("")}function Jn(e,t,n){let o=0,r=e.length;if(t){let t=e.codePointAt(o);for(;9===t||32===t;)o++,t=e.codePointAt(o)}if(n){let t=e.codePointAt(r-1);for(;9===t||32===t;)r--,t=e.codePointAt(r-1)}return r>o?e.slice(o,r):""}const eo={blockquote:function(e,t){const n={type:"element",tagName:"blockquote",properties:{},children:e.wrap(e.all(t),!0)};return e.patch(t,n),e.applyData(t,n)},break:function(e,t){const n={type:"element",tagName:"br",properties:{},children:[]};return e.patch(t,n),[e.applyData(t,n),{type:"text",value:"\n"}]},code:function(e,t){const n=t.value?t.value+"\n":"",o={};t.lang&&(o.className=["language-"+t.lang]);let r={type:"element",tagName:"code",properties:o,children:[{type:"text",value:n}]};return t.meta&&(r.data={meta:t.meta}),e.patch(t,r),r=e.applyData(t,r),r={type:"element",tagName:"pre",properties:{},children:[r]},e.patch(t,r),r},delete:function(e,t){const n={type:"element",tagName:"del",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},emphasis:function(e,t){const n={type:"element",tagName:"em",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},footnoteReference:function(e,t){const n="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",o=String(t.identifier).toUpperCase(),r=Bn(o.toLowerCase()),i=e.footnoteOrder.indexOf(o);let l,a=e.footnoteCounts.get(o);void 0===a?(a=0,e.footnoteOrder.push(o),l=e.footnoteOrder.length):l=i+1,a+=1,e.footnoteCounts.set(o,a);const s={type:"element",tagName:"a",properties:{href:"#"+n+"fn-"+r,id:n+"fnref-"+r+(a>1?"-"+a:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(l)}]};e.patch(t,s);const c={type:"element",tagName:"sup",properties:{},children:[s]};return e.patch(t,c),e.applyData(t,c)},heading:function(e,t){const n={type:"element",tagName:"h"+t.depth,properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},html:function(e,t){if(e.options.allowDangerousHtml){const n={type:"raw",value:t.value};return e.patch(t,n),e.applyData(t,n)}},imageReference:function(e,t){const n=String(t.identifier).toUpperCase(),o=e.definitionById.get(n);if(!o)return Yn(e,t);const r={src:Bn(o.url||""),alt:t.alt};null!==o.title&&void 0!==o.title&&(r.title=o.title);const i={type:"element",tagName:"img",properties:r,children:[]};return e.patch(t,i),e.applyData(t,i)},image:function(e,t){const n={src:Bn(t.url)};null!==t.alt&&void 0!==t.alt&&(n.alt=t.alt),null!==t.title&&void 0!==t.title&&(n.title=t.title);const o={type:"element",tagName:"img",properties:n,children:[]};return e.patch(t,o),e.applyData(t,o)},inlineCode:function(e,t){const n={type:"text",value:t.value.replace(/\r?\n|\r/g," ")};e.patch(t,n);const o={type:"element",tagName:"code",properties:{},children:[n]};return e.patch(t,o),e.applyData(t,o)},linkReference:function(e,t){const n=String(t.identifier).toUpperCase(),o=e.definitionById.get(n);if(!o)return Yn(e,t);const r={href:Bn(o.url||"")};null!==o.title&&void 0!==o.title&&(r.title=o.title);const i={type:"element",tagName:"a",properties:r,children:e.all(t)};return e.patch(t,i),e.applyData(t,i)},link:function(e,t){const n={href:Bn(t.url)};null!==t.title&&void 0!==t.title&&(n.title=t.title);const o={type:"element",tagName:"a",properties:n,children:e.all(t)};return e.patch(t,o),e.applyData(t,o)},listItem:function(e,t,n){const o=e.all(t),r=n?function(e){let t=!1;if("list"===e.type){t=e.spread||!1;const n=e.children;let o=-1;for(;!t&&++o<n.length;)t=Qn(n[o])}return t}(n):Qn(t),i={},l=[];if("boolean"==typeof t.checked){const e=o[0];let n;e&&"element"===e.type&&"p"===e.tagName?n=e:(n={type:"element",tagName:"p",properties:{},children:[]},o.unshift(n)),n.children.length>0&&n.children.unshift({type:"text",value:" "}),n.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:t.checked,disabled:!0},children:[]}),i.className=["task-list-item"]}let a=-1;for(;++a<o.length;){const e=o[a];(r||0!==a||"element"!==e.type||"p"!==e.tagName)&&l.push({type:"text",value:"\n"}),"element"!==e.type||"p"!==e.tagName||r?l.push(e):l.push(...e.children)}const s=o[o.length-1];s&&(r||"element"!==s.type||"p"!==s.tagName)&&l.push({type:"text",value:"\n"});const c={type:"element",tagName:"li",properties:i,children:l};return e.patch(t,c),e.applyData(t,c)},list:function(e,t){const n={},o=e.all(t);let r=-1;for("number"==typeof t.start&&1!==t.start&&(n.start=t.start);++r<o.length;){const e=o[r];if("element"===e.type&&"li"===e.tagName&&e.properties&&Array.isArray(e.properties.className)&&e.properties.className.includes("task-list-item")){n.className=["contains-task-list"];break}}const i={type:"element",tagName:t.ordered?"ol":"ul",properties:n,children:e.wrap(o,!0)};return e.patch(t,i),e.applyData(t,i)},paragraph:function(e,t){const n={type:"element",tagName:"p",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},root:function(e,t){const n={type:"root",children:e.wrap(e.all(t))};return e.patch(t,n),e.applyData(t,n)},strong:function(e,t){const n={type:"element",tagName:"strong",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},table:function(e,t){const n=e.all(t),o=n.shift(),r=[];if(o){const n={type:"element",tagName:"thead",properties:{},children:e.wrap([o],!0)};e.patch(t.children[0],n),r.push(n)}if(n.length>0){const o={type:"element",tagName:"tbody",properties:{},children:e.wrap(n,!0)},i=Gn(t.children[1]),l=Xn(t.children[t.children.length-1]);i&&l&&(o.position={start:i,end:l}),r.push(o)}const i={type:"element",tagName:"table",properties:{},children:e.wrap(r,!0)};return e.patch(t,i),e.applyData(t,i)},tableCell:function(e,t){const n={type:"element",tagName:"td",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},tableRow:function(e,t,n){const o=n?n.children:void 0,r=0===(o?o.indexOf(t):1)?"th":"td",i=n&&"table"===n.type?n.align:void 0,l=i?i.length:t.children.length;let a=-1;const s=[];for(;++a<l;){const n=t.children[a],o={},l=i?i[a]:void 0;l&&(o.align=l);let c={type:"element",tagName:r,properties:o,children:[]};n&&(c.children=e.all(n),e.patch(n,c),c=e.applyData(n,c)),s.push(c)}const c={type:"element",tagName:"tr",properties:{},children:e.wrap(s,!0)};return e.patch(t,c),e.applyData(t,c)},text:function(e,t){const n={type:"text",value:Zn(String(t.value))};return e.patch(t,n),e.applyData(t,n)},thematicBreak:function(e,t){const n={type:"element",tagName:"hr",properties:{},children:[]};return e.patch(t,n),e.applyData(t,n)},toml:to,yaml:to,definition:to,footnoteDefinition:to};function to(){}const no={}.hasOwnProperty,oo={};function ro(e,t){const n=t||oo,o=new Map,r=new Map,i=new Map,l={...eo,...n.handlers},a={all:function(e){const t=[];if("children"in e){const n=e.children;let o=-1;for(;++o<n.length;){const r=a.one(n[o],e);if(r){if(o&&"break"===n[o-1].type&&(Array.isArray(r)||"text"!==r.type||(r.value=co(r.value)),!Array.isArray(r)&&"element"===r.type)){const e=r.children[0];e&&"text"===e.type&&(e.value=co(e.value))}Array.isArray(r)?t.push(...r):t.push(r)}}}return t},applyData:lo,definitionById:o,footnoteById:r,footnoteCounts:i,footnoteOrder:[],handlers:l,one:function(e,t){const n=e.type,o=a.handlers[n];if(no.call(a.handlers,n)&&o)return o(a,e,t);if(a.options.passThrough&&a.options.passThrough.includes(n)){if("children"in e){const{children:t,...n}=e,o=Tn(n);return o.children=a.all(e),o}return Tn(e)}return(a.options.unknownHandler||ao)(a,e,t)},options:n,patch:io,wrap:so};return function(e,t,n,o){let r,i,l;"function"==typeof t&&"function"!=typeof n?(i=void 0,l=t,r=n):(i=t,l=n,r=o),Un(e,i,function(e,t){const n=t[t.length-1],o=n?n.children.indexOf(e):void 0;return l(e,o,n)},r)}(e,function(e){if("definition"===e.type||"footnoteDefinition"===e.type){const t="definition"===e.type?o:r,n=String(e.identifier).toUpperCase();t.has(n)||t.set(n,e)}}),a}function io(e,t){e.position&&(t.position=function(e){const t=Gn(e),n=Xn(e);if(t&&n)return{start:t,end:n}}(e))}function lo(e,t){let n=t;if(e&&e.data){const t=e.data.hName,o=e.data.hChildren,r=e.data.hProperties;if("string"==typeof t)if("element"===n.type)n.tagName=t;else{n={type:"element",tagName:t,properties:{},children:"children"in n?n.children:[n]}}"element"===n.type&&r&&Object.assign(n.properties,Tn(r)),"children"in n&&n.children&&null!=o&&(n.children=o)}return n}function ao(e,t){const n=t.data||{},o=!("value"in t)||no.call(n,"hProperties")||no.call(n,"hChildren")?{type:"element",tagName:"div",properties:{},children:e.all(t)}:{type:"text",value:t.value};return e.patch(t,o),e.applyData(t,o)}function so(e,t){const n=[];let o=-1;for(t&&n.push({type:"text",value:"\n"});++o<e.length;)o&&n.push({type:"text",value:"\n"}),n.push(e[o]);return t&&e.length>0&&n.push({type:"text",value:"\n"}),n}function co(e){let t=0,n=e.charCodeAt(t);for(;9===n||32===n;)t++,n=e.charCodeAt(t);return e.slice(t)}function uo(e,t){const n=ro(e,t),o=n.one(e,void 0),r=function(e){const t="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",n=e.options.footnoteBackContent||Rn,o=e.options.footnoteBackLabel||Dn,r=e.options.footnoteLabel||"Footnotes",i=e.options.footnoteLabelTagName||"h2",l=e.options.footnoteLabelProperties||{className:["sr-only"]},a=[];let s=-1;for(;++s<e.footnoteOrder.length;){const r=e.footnoteById.get(e.footnoteOrder[s]);if(!r)continue;const i=e.all(r),l=String(r.identifier).toUpperCase(),c=Bn(l.toLowerCase());let u=0;const d=[],f=e.footnoteCounts.get(l);for(;void 0!==f&&++u<=f;){d.length>0&&d.push({type:"text",value:" "});let e="string"==typeof n?n:n(s,u);"string"==typeof e&&(e={type:"text",value:e}),d.push({type:"element",tagName:"a",properties:{href:"#"+t+"fnref-"+c+(u>1?"-"+u:""),dataFootnoteBackref:"",ariaLabel:"string"==typeof o?o:o(s,u),className:["data-footnote-backref"]},children:Array.isArray(e)?e:[e]})}const p=i[i.length-1];if(p&&"element"===p.type&&"p"===p.tagName){const e=p.children[p.children.length-1];e&&"text"===e.type?e.value+=" ":p.children.push({type:"text",value:" "}),p.children.push(...d)}else i.push(...d);const m={type:"element",tagName:"li",properties:{id:t+"fn-"+c},children:e.wrap(i,!0)};e.patch(r,m),a.push(m)}if(0!==a.length)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:i,properties:{...Tn(l),id:"footnote-label"},children:[{type:"text",value:r}]},{type:"text",value:"\n"},{type:"element",tagName:"ol",properties:{},children:e.wrap(a,!0)},{type:"text",value:"\n"}]}}(n),i=Array.isArray(o)?{type:"root",children:o}:o||{type:"root",children:[]};return r&&i.children.push({type:"text",value:"\n"},r),i}function fo(e,t){return e&&"run"in e?async function(n,o){const r=uo(n,{file:o,...t});await e.run(r,o)}:function(n,o){return uo(n,{file:o,...e||t})}}function po(e){if(e)throw e}var mo=n(2849);function ho(e){if("object"!=typeof e||null===e)return!1;const t=Object.getPrototypeOf(e);return!(null!==t&&t!==Object.prototype&&null!==Object.getPrototypeOf(t)||Symbol.toStringTag in e||Symbol.iterator in e)}function go(){const e=[],t={run:function(...t){let n=-1;const o=t.pop();if("function"!=typeof o)throw new TypeError("Expected function as last argument, not "+o);!function r(i,...l){const a=e[++n];let s=-1;if(i)o(i);else{for(;++s<t.length;)null!==l[s]&&void 0!==l[s]||(l[s]=t[s]);t=l,a?function(e,t){let n;return o;function o(...t){const o=e.length>t.length;let a;o&&t.push(r);try{a=e.apply(this,t)}catch(i){if(o&&n)throw i;return r(i)}o||(a&&a.then&&"function"==typeof a.then?a.then(l,r):a instanceof Error?r(a):l(a))}function r(e,...o){n||(n=!0,t(e,...o))}function l(e){r(null,e)}}(a,r)(...l):o(null,...l)}}(null,...t)},use:function(n){if("function"!=typeof n)throw new TypeError("Expected `middelware` to be a function, not "+n);return e.push(n),t}};return t}function vo(e){return yo(e&&e.line)+":"+yo(e&&e.column)}function bo(e){return vo(e&&e.start)+"-"+vo(e&&e.end)}function yo(e){return e&&"number"==typeof e?e:1}class xo extends Error{constructor(e,t,n){super(),"string"==typeof t&&(n=t,t=void 0);let o="",r={},i=!1;if(t&&(r="line"in t&&"column"in t||"start"in t&&"end"in t?{place:t}:"type"in t?{ancestors:[t],place:t.position}:{...t}),"string"==typeof e?o=e:!r.cause&&e&&(i=!0,o=e.message,r.cause=e),!r.ruleId&&!r.source&&"string"==typeof n){const e=n.indexOf(":");-1===e?r.ruleId=n:(r.source=n.slice(0,e),r.ruleId=n.slice(e+1))}if(!r.place&&r.ancestors&&r.ancestors){const e=r.ancestors[r.ancestors.length-1];e&&(r.place=e.position)}const l=r.place&&"start"in r.place?r.place.start:r.place;var a;this.ancestors=r.ancestors||void 0,this.cause=r.cause||void 0,this.column=l?l.column:void 0,this.fatal=void 0,this.file="",this.message=o,this.line=l?l.line:void 0,this.name=((a=r.place)&&"object"==typeof a?"position"in a||"type"in a?bo(a.position):"start"in a||"end"in a?bo(a):"line"in a||"column"in a?vo(a):"":"")||"1:1",this.place=r.place||void 0,this.reason=this.message,this.ruleId=r.ruleId||void 0,this.source=r.source||void 0,this.stack=i&&r.cause&&"string"==typeof r.cause.stack?r.cause.stack:"",this.actual=void 0,this.expected=void 0,this.note=void 0,this.url=void 0}}xo.prototype.file="",xo.prototype.name="",xo.prototype.reason="",xo.prototype.message="",xo.prototype.stack="",xo.prototype.column=void 0,xo.prototype.line=void 0,xo.prototype.ancestors=void 0,xo.prototype.cause=void 0,xo.prototype.fatal=void 0,xo.prototype.place=void 0,xo.prototype.ruleId=void 0,xo.prototype.source=void 0;const wo={basename:function(e,t){if(void 0!==t&&"string"!=typeof t)throw new TypeError('"ext" argument must be a string');Co(e);let n,o=0,r=-1,i=e.length;if(void 0===t||0===t.length||t.length>e.length){for(;i--;)if(47===e.codePointAt(i)){if(n){o=i+1;break}}else r<0&&(n=!0,r=i+1);return r<0?"":e.slice(o,r)}if(t===e)return"";let l=-1,a=t.length-1;for(;i--;)if(47===e.codePointAt(i)){if(n){o=i+1;break}}else l<0&&(n=!0,l=i+1),a>-1&&(e.codePointAt(i)===t.codePointAt(a--)?a<0&&(r=i):(a=-1,r=l));o===r?r=l:r<0&&(r=e.length);return e.slice(o,r)},dirname:function(e){if(Co(e),0===e.length)return".";let t,n=-1,o=e.length;for(;--o;)if(47===e.codePointAt(o)){if(t){n=o;break}}else t||(t=!0);return n<0?47===e.codePointAt(0)?"/":".":1===n&&47===e.codePointAt(0)?"//":e.slice(0,n)},extname:function(e){Co(e);let t,n=e.length,o=-1,r=0,i=-1,l=0;for(;n--;){const a=e.codePointAt(n);if(47!==a)o<0&&(t=!0,o=n+1),46===a?i<0?i=n:1!==l&&(l=1):i>-1&&(l=-1);else if(t){r=n+1;break}}if(i<0||o<0||0===l||1===l&&i===o-1&&i===r+1)return"";return e.slice(i,o)},join:function(...e){let t,n=-1;for(;++n<e.length;)Co(e[n]),e[n]&&(t=void 0===t?e[n]:t+"/"+e[n]);return void 0===t?".":function(e){Co(e);const t=47===e.codePointAt(0);let n=function(e,t){let n,o,r="",i=0,l=-1,a=0,s=-1;for(;++s<=e.length;){if(s<e.length)n=e.codePointAt(s);else{if(47===n)break;n=47}if(47===n){if(l===s-1||1===a);else if(l!==s-1&&2===a){if(r.length<2||2!==i||46!==r.codePointAt(r.length-1)||46!==r.codePointAt(r.length-2))if(r.length>2){if(o=r.lastIndexOf("/"),o!==r.length-1){o<0?(r="",i=0):(r=r.slice(0,o),i=r.length-1-r.lastIndexOf("/")),l=s,a=0;continue}}else if(r.length>0){r="",i=0,l=s,a=0;continue}t&&(r=r.length>0?r+"/..":"..",i=2)}else r.length>0?r+="/"+e.slice(l+1,s):r=e.slice(l+1,s),i=s-l-1;l=s,a=0}else 46===n&&a>-1?a++:a=-1}return r}(e,!t);0!==n.length||t||(n=".");n.length>0&&47===e.codePointAt(e.length-1)&&(n+="/");return t?"/"+n:n}(t)},sep:"/"};function Co(e){if("string"!=typeof e)throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}const ko={cwd:function(){return"/"}};function $o(e){return Boolean(null!==e&&"object"==typeof e&&"href"in e&&e.href&&"protocol"in e&&e.protocol&&void 0===e.auth)}function So(e){if("string"==typeof e)e=new URL(e);else if(!$o(e)){const t=new TypeError('The "path" argument must be of type string or an instance of URL. Received `'+e+"`");throw t.code="ERR_INVALID_ARG_TYPE",t}if("file:"!==e.protocol){const e=new TypeError("The URL must be of scheme file");throw e.code="ERR_INVALID_URL_SCHEME",e}return function(e){if(""!==e.hostname){const e=new TypeError('File URL host must be "localhost" or empty on darwin');throw e.code="ERR_INVALID_FILE_URL_HOST",e}const t=e.pathname;let n=-1;for(;++n<t.length;)if(37===t.codePointAt(n)&&50===t.codePointAt(n+1)){const e=t.codePointAt(n+2);if(70===e||102===e){const e=new TypeError("File URL path must not include encoded / characters");throw e.code="ERR_INVALID_FILE_URL_PATH",e}}return decodeURIComponent(t)}(e)}const Ao=["history","path","basename","stem","extname","dirname"];class Eo{constructor(e){let t;t=e?$o(e)?{path:e}:"string"==typeof e||function(e){return Boolean(e&&"object"==typeof e&&"byteLength"in e&&"byteOffset"in e)}(e)?{value:e}:e:{},this.cwd="cwd"in t?"":ko.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let n,o=-1;for(;++o<Ao.length;){const e=Ao[o];e in t&&void 0!==t[e]&&null!==t[e]&&(this[e]="history"===e?[...t[e]]:t[e])}for(n in t)Ao.includes(n)||(this[n]=t[n])}get basename(){return"string"==typeof this.path?wo.basename(this.path):void 0}set basename(e){Oo(e,"basename"),Io(e,"basename"),this.path=wo.join(this.dirname||"",e)}get dirname(){return"string"==typeof this.path?wo.dirname(this.path):void 0}set dirname(e){zo(this.basename,"dirname"),this.path=wo.join(e||"",this.basename)}get extname(){return"string"==typeof this.path?wo.extname(this.path):void 0}set extname(e){if(Io(e,"extname"),zo(this.dirname,"extname"),e){if(46!==e.codePointAt(0))throw new Error("`extname` must start with `.`");if(e.includes(".",1))throw new Error("`extname` cannot contain multiple dots")}this.path=wo.join(this.dirname,this.stem+(e||""))}get path(){return this.history[this.history.length-1]}set path(e){$o(e)&&(e=So(e)),Oo(e,"path"),this.path!==e&&this.history.push(e)}get stem(){return"string"==typeof this.path?wo.basename(this.path,this.extname):void 0}set stem(e){Oo(e,"stem"),Io(e,"stem"),this.path=wo.join(this.dirname||"",e+(this.extname||""))}fail(e,t,n){const o=this.message(e,t,n);throw o.fatal=!0,o}info(e,t,n){const o=this.message(e,t,n);return o.fatal=void 0,o}message(e,t,n){const o=new xo(e,t,n);return this.path&&(o.name=this.path+":"+o.name,o.file=this.path),o.fatal=!1,this.messages.push(o),o}toString(e){if(void 0===this.value)return"";if("string"==typeof this.value)return this.value;return new TextDecoder(e||void 0).decode(this.value)}}function Io(e,t){if(e&&e.includes(wo.sep))throw new Error("`"+t+"` cannot be a path: did not expect `"+wo.sep+"`")}function Oo(e,t){if(!e)throw new Error("`"+t+"` cannot be empty")}function zo(e,t){if(!e)throw new Error("Setting `"+t+"` requires `path` to be set too")}const Mo=function(e){const t=this.constructor.prototype,n=t[e],o=function(){return n.apply(o,arguments)};return Object.setPrototypeOf(o,t),o},Po={}.hasOwnProperty;class To extends Mo{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=go()}copy(){const e=new To;let t=-1;for(;++t<this.attachers.length;){const n=this.attachers[t];e.use(...n)}return e.data(mo(!0,{},this.namespace)),e}data(e,t){return"string"==typeof e?2===arguments.length?(Ro("data",this.frozen),this.namespace[e]=t,this):Po.call(this.namespace,e)&&this.namespace[e]||void 0:e?(Ro("data",this.frozen),this.namespace=e,this):this.namespace}freeze(){if(this.frozen)return this;const e=this;for(;++this.freezeIndex<this.attachers.length;){const[t,...n]=this.attachers[this.freezeIndex];if(!1===n[0])continue;!0===n[0]&&(n[0]=void 0);const o=t.call(e,...n);"function"==typeof o&&this.transformers.use(o)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(e){this.freeze();const t=Ho(e),n=this.parser||this.Parser;return No("parse",n),n(String(t),t)}process(e,t){const n=this;return this.freeze(),No("process",this.parser||this.Parser),Bo("process",this.compiler||this.Compiler),t?o(void 0,t):new Promise(o);function o(o,r){const i=Ho(e),l=n.parse(i);function a(e,n){e||!n?r(e):o?o(n):t(void 0,n)}n.run(l,i,function(e,t,o){if(e||!t||!o)return a(e);const r=t,i=n.stringify(r,o);var l;"string"==typeof(l=i)||function(e){return Boolean(e&&"object"==typeof e&&"byteLength"in e&&"byteOffset"in e)}(l)?o.value=i:o.result=i,a(e,o)})}}processSync(e){let t,n=!1;return this.freeze(),No("processSync",this.parser||this.Parser),Bo("processSync",this.compiler||this.Compiler),this.process(e,function(e,o){n=!0,po(e),t=o}),Lo("processSync","process",n),t}run(e,t,n){Do(e),this.freeze();const o=this.transformers;return n||"function"!=typeof t||(n=t,t=void 0),n?r(void 0,n):new Promise(r);function r(r,i){const l=Ho(t);o.run(e,l,function(t,o,l){const a=o||e;t?i(t):r?r(a):n(void 0,a,l)})}}runSync(e,t){let n,o=!1;return this.run(e,t,function(e,t){po(e),n=t,o=!0}),Lo("runSync","run",o),n}stringify(e,t){this.freeze();const n=Ho(t),o=this.compiler||this.Compiler;return Bo("stringify",o),Do(e),o(e,n)}use(e,...t){const n=this.attachers,o=this.namespace;if(Ro("use",this.frozen),null==e);else if("function"==typeof e)a(e,t);else{if("object"!=typeof e)throw new TypeError("Expected usable value, not `"+e+"`");Array.isArray(e)?l(e):i(e)}return this;function r(e){if("function"==typeof e)a(e,[]);else{if("object"!=typeof e)throw new TypeError("Expected usable value, not `"+e+"`");if(Array.isArray(e)){const[t,...n]=e;a(t,n)}else i(e)}}function i(e){if(!("plugins"in e)&&!("settings"in e))throw new Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");l(e.plugins),e.settings&&(o.settings=mo(!0,o.settings,e.settings))}function l(e){let t=-1;if(null==e);else{if(!Array.isArray(e))throw new TypeError("Expected a list of plugins, not `"+e+"`");for(;++t<e.length;){r(e[t])}}}function a(e,t){let o=-1,r=-1;for(;++o<n.length;)if(n[o][0]===e){r=o;break}if(-1===r)n.push([e,...t]);else if(t.length>0){let[o,...i]=t;const l=n[r][1];ho(l)&&ho(o)&&(o=mo(!0,l,o)),n[r]=[e,o,...i]}}}}const jo=(new To).freeze();function No(e,t){if("function"!=typeof t)throw new TypeError("Cannot `"+e+"` without `parser`")}function Bo(e,t){if("function"!=typeof t)throw new TypeError("Cannot `"+e+"` without `compiler`")}function Ro(e,t){if(t)throw new Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function Do(e){if(!ho(e)||"string"!=typeof e.type)throw new TypeError("Expected node, got `"+e+"`")}function Lo(e,t,n){if(!n)throw new Error("`"+e+"` finished async. Use `"+t+"` instead")}function Ho(e){return function(e){return Boolean(e&&"object"==typeof e&&"message"in e&&"messages"in e)}(e)?e:new Eo(e)}const Fo=[],_o={allowDangerousHtml:!0},Wo=/^(https?|ircs?|mailto|xmpp)$/i,Vo=[{from:"astPlugins",id:"remove-buggy-html-in-markdown-parser"},{from:"allowDangerousHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"allowNode",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowElement"},{from:"allowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowedElements"},{from:"disallowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"disallowedElements"},{from:"escapeHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"includeElementIndex",id:"#remove-includeelementindex"},{from:"includeNodeIndex",id:"change-includenodeindex-to-includeelementindex"},{from:"linkTarget",id:"remove-linktarget"},{from:"plugins",id:"change-plugins-to-remarkplugins",to:"remarkPlugins"},{from:"rawSourcePos",id:"#remove-rawsourcepos"},{from:"renderers",id:"change-renderers-to-components",to:"components"},{from:"source",id:"change-source-to-children",to:"children"},{from:"sourcePos",id:"#remove-sourcepos"},{from:"transformImageUri",id:"#add-urltransform",to:"urlTransform"},{from:"transformLinkUri",id:"#add-urltransform",to:"urlTransform"}];function qo(e){const t=Uo(e),n=Xo(e);return Go(t.runSync(t.parse(n),n),e)}function Uo(e){const t=e.rehypePlugins||Fo,n=e.remarkPlugins||Fo,o=e.remarkRehypeOptions?{...e.remarkRehypeOptions,..._o}:_o;return jo().use($n).use(n).use(fo,o).use(t)}function Xo(e){const t=e.children||"",n=new Eo;return"string"==typeof t&&(n.value=t),n}function Go(e,t){const n=t.allowedElements,o=t.allowElement,r=t.components,i=t.disallowedElements,l=t.skipHtml,a=t.unwrapDisallowed,s=t.urlTransform||Ko;for(const c of Vo)Object.hasOwn(t,c.from)&&d((c.from,c.to&&c.to,c.id));return t.className&&(e={type:"element",tagName:"div",properties:{className:t.className},children:"root"===e.type?e.children:[e]}),function(e,t,n,o){let r,i,l;"function"==typeof t&&"function"!=typeof n?(i=void 0,l=t,r=n):(i=t,l=n,r=o),Un(e,i,function(e,t){const n=t[t.length-1],o=n?n.children.indexOf(e):void 0;return l(e,o,n)},r)}(e,function(e,t,r){if("raw"===e.type&&r&&"number"==typeof t)return l?r.children.splice(t,1):r.children[t]={type:"text",value:e.value},t;if("element"===e.type){let t;for(t in xe)if(Object.hasOwn(xe,t)&&Object.hasOwn(e.properties,t)){const n=e.properties[t],o=xe[t];(null===o||o.includes(e.tagName))&&(e.properties[t]=s(String(n||""),t,e))}}if("element"===e.type){let l=n?!n.includes(e.tagName):!!i&&i.includes(e.tagName);if(!l&&o&&"number"==typeof t&&(l=!o(e,t,r)),l&&r&&"number"==typeof t)return a&&e.children?r.children.splice(t,1,...e.children):r.children.splice(t,1),t}}),ue(e,{Fragment:we.Fragment,components:r,ignoreInvalidStyle:!0,jsx:we.jsx,jsxs:we.jsxs,passKeys:!0,passNode:!0})}function Ko(e){const t=e.indexOf(":"),n=e.indexOf("?"),o=e.indexOf("#"),r=e.indexOf("/");return-1===t||-1!==r&&t>r||-1!==n&&t>n||-1!==o&&t>o||Wo.test(e.slice(0,t))?e:""}var Yo=n(367);function Qo(e,t){const n=String(e);if("string"!=typeof t)throw new TypeError("Expected character");let o=0,r=n.indexOf(t);for(;-1!==r;)o++,r=n.indexOf(t,r+t.length);return o}er(/[A-Za-z]/),er(/[\dA-Za-z]/),er(/[#-'*+\--9=?A-Z^-~]/);er(/\d/),er(/[\dA-Fa-f]/),er(/[!-/:-@[-`{-~]/);const Zo=er(/\p{P}|\p{S}/u),Jo=er(/\s/);function er(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}const tr=function(e){if(null==e)return or;if("function"==typeof e)return nr(e);if("object"==typeof e)return Array.isArray(e)?function(e){const t=[];let n=-1;for(;++n<e.length;)t[n]=tr(e[n]);return nr(o);function o(...e){let n=-1;for(;++n<t.length;)if(t[n].apply(this,e))return!0;return!1}}(e):function(e){const t=e;return nr(n);function n(n){const o=n;let r;for(r in e)if(o[r]!==t[r])return!1;return!0}}(e);if("string"==typeof e)return function(e){return nr(t);function t(t){return t&&t.type===e}}(e);throw new Error("Expected function, string, or object as test")};function nr(e){return function(t,n,o){return Boolean(rr(t)&&e.call(this,t,"number"==typeof n?n:void 0,o||void 0))}}function or(){return!0}function rr(e){return null!==e&&"object"==typeof e&&"type"in e}function ir(e,t,n){const o=tr((n||{}).ignore||[]),r=function(e){const t=[];if(!Array.isArray(e))throw new TypeError("Expected find and replace tuple or list of tuples");const n=!e[0]||Array.isArray(e[0])?e:[e];let o=-1;for(;++o<n.length;){const e=n[o];t.push([lr(e[0]),ar(e[1])])}return t}(t);let i=-1;for(;++i<r.length;)Un(e,"text",l);function l(e,t){let n,l=-1;for(;++l<t.length;){const e=t[l],r=n?n.children:void 0;if(o(e,r?r.indexOf(e):void 0,n))return;n=e}if(n)return function(e,t){const n=t[t.length-1],o=r[i][0],l=r[i][1];let a=0;const s=n.children.indexOf(e);let c=!1,u=[];o.lastIndex=0;let d=o.exec(e.value);for(;d;){const n=d.index,r={index:d.index,input:d.input,stack:[...t,e]};let i=l(...d,r);if("string"==typeof i&&(i=i.length>0?{type:"text",value:i}:void 0),!1===i?o.lastIndex=n+1:(a!==n&&u.push({type:"text",value:e.value.slice(a,n)}),Array.isArray(i)?u.push(...i):i&&u.push(i),a=n+d[0].length,c=!0),!o.global)break;d=o.exec(e.value)}c?(a<e.value.length&&u.push({type:"text",value:e.value.slice(a)}),n.children.splice(s,1,...u)):u=[e];return s+u.length}(e,t)}}function lr(e){return"string"==typeof e?new RegExp(function(e){if("string"!=typeof e)throw new TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}(e),"g"):e}function ar(e){return"function"==typeof e?e:function(){return e}}const sr="phrasing",cr=["autolink","link","image","label"];function ur(e){this.enter({type:"link",title:null,url:"",children:[]},e)}function dr(e){this.config.enter.autolinkProtocol.call(this,e)}function fr(e){this.config.exit.autolinkProtocol.call(this,e)}function pr(e){this.config.exit.data.call(this,e);const t=this.stack[this.stack.length-1];t.type,t.url="http://"+this.sliceSerialize(e)}function mr(e){this.config.exit.autolinkEmail.call(this,e)}function hr(e){this.exit(e)}function gr(e){ir(e,[[/(https?:\/\/|www(?=\.))([-.\w]+)([^ \t\r\n]*)/gi,vr],[/(?<=^|\s|\p{P}|\p{S})([-.\w+]+)@([-\w]+(?:\.[-\w]+)+)/gu,br]],{ignore:["link","linkReference"]})}function vr(e,t,n,o,r){let i="";if(!yr(r))return!1;if(/^w/i.test(t)&&(n=t+n,t="",i="http://"),!function(e){const t=e.split(".");if(t.length<2||t[t.length-1]&&(/_/.test(t[t.length-1])||!/[a-zA-Z\d]/.test(t[t.length-1]))||t[t.length-2]&&(/_/.test(t[t.length-2])||!/[a-zA-Z\d]/.test(t[t.length-2])))return!1;return!0}(n))return!1;const l=function(e){const t=/[!"&'),.:;<>?\]}]+$/.exec(e);if(!t)return[e,void 0];e=e.slice(0,t.index);let n=t[0],o=n.indexOf(")");const r=Qo(e,"(");let i=Qo(e,")");for(;-1!==o&&r>i;)e+=n.slice(0,o+1),n=n.slice(o+1),o=n.indexOf(")"),i++;return[e,n]}(n+o);if(!l[0])return!1;const a={type:"link",title:null,url:i+t+l[0],children:[{type:"text",value:t+l[0]}]};return l[1]?[a,{type:"text",value:l[1]}]:a}function br(e,t,n,o){return!(!yr(o,!0)||/[-\d_]$/.test(n))&&{type:"link",title:null,url:"mailto:"+t+"@"+n,children:[{type:"text",value:t+"@"+n}]}}function yr(e,t){const n=e.input.charCodeAt(e.index-1);return(0===e.index||Jo(n)||Zo(n))&&(!t||47!==n)}function xr(){this.buffer()}function wr(e){this.enter({type:"footnoteReference",identifier:"",label:""},e)}function Cr(){this.buffer()}function kr(e){this.enter({type:"footnoteDefinition",identifier:"",label:"",children:[]},e)}function $r(e){const t=this.resume(),n=this.stack[this.stack.length-1];n.type,n.identifier=gt(this.sliceSerialize(e)).toLowerCase(),n.label=t}function Sr(e){this.exit(e)}function Ar(e){const t=this.resume(),n=this.stack[this.stack.length-1];n.type,n.identifier=gt(this.sliceSerialize(e)).toLowerCase(),n.label=t}function Er(e){this.exit(e)}function Ir(e,t,n,o){const r=n.createTracker(o);let i=r.move("[^");const l=n.enter("footnoteReference"),a=n.enter("reference");return i+=r.move(n.safe(n.associationId(e),{after:"]",before:i})),a(),l(),i+=r.move("]"),i}function Or(e){let t=!1;return e&&e.firstLineBlank&&(t=!0),{handlers:{footnoteDefinition:function(e,n,o,r){const i=o.createTracker(r);let l=i.move("[^");const a=o.enter("footnoteDefinition"),s=o.enter("label");l+=i.move(o.safe(o.associationId(e),{before:l,after:"]"})),s(),l+=i.move("]:"),e.children&&e.children.length>0&&(i.shift(4),l+=i.move((t?"\n":" ")+o.indentLines(o.containerFlow(e,i.current()),t?Mr:zr)));return a(),l},footnoteReference:Ir},unsafe:[{character:"[",inConstruct:["label","phrasing","reference"]}]}}function zr(e,t,n){return 0===t?e:Mr(e,t,n)}function Mr(e,t,n){return(n?"":"    ")+e}Ir.peek=function(){return"["};const Pr=["autolink","destinationLiteral","destinationRaw","reference","titleQuote","titleApostrophe"];function Tr(e){this.enter({type:"delete",children:[]},e)}function jr(e){this.exit(e)}function Nr(e,t,n,o){const r=n.createTracker(o),i=n.enter("strikethrough");let l=r.move("~~");return l+=n.containerPhrasing(e,{...r.current(),before:l,after:"~"}),l+=r.move("~~"),i(),l}function Br(e){return e.length}function Rr(e){return null==e?"":String(e)}function Dr(e){const t="string"==typeof e?e.codePointAt(0):0;return 67===t||99===t?99:76===t||108===t?108:82===t||114===t?114:0}function Lr(e,t,n){return">"+(n?"":" ")+e}function Hr(e,t){return Fr(e,t.inConstruct,!0)&&!Fr(e,t.notInConstruct,!1)}function Fr(e,t,n){if("string"==typeof t&&(t=[t]),!t||0===t.length)return n;let o=-1;for(;++o<t.length;)if(e.includes(t[o]))return!0;return!1}function _r(e,t,n,o){let r=-1;for(;++r<n.unsafe.length;)if("\n"===n.unsafe[r].character&&Hr(n.stack,n.unsafe[r]))return/[ \t]/.test(o.before)?"":" ";return"\\\n"}function Wr(e,t,n){return(n?"":"    ")+e}function Vr(e){const t=e.options.quote||'"';if('"'!==t&&"'"!==t)throw new Error("Cannot serialize title with `"+t+"` for `options.quote`, expected `\"`, or `'`");return t}function qr(e){return"&#x"+e.toString(16).toUpperCase()+";"}function Ur(e,t,n){const o=Vt(e),r=Vt(t);return void 0===o?void 0===r?"_"===n?{inside:!0,outside:!0}:{inside:!1,outside:!1}:1===r?{inside:!0,outside:!0}:{inside:!1,outside:!0}:1===o?void 0===r?{inside:!1,outside:!1}:1===r?{inside:!0,outside:!0}:{inside:!1,outside:!1}:void 0===r?{inside:!1,outside:!1}:1===r?{inside:!0,outside:!1}:{inside:!1,outside:!1}}function Xr(e,t,n,o){const r=function(e){const t=e.options.emphasis||"*";if("*"!==t&&"_"!==t)throw new Error("Cannot serialize emphasis with `"+t+"` for `options.emphasis`, expected `*`, or `_`");return t}(n),i=n.enter("emphasis"),l=n.createTracker(o),a=l.move(r);let s=l.move(n.containerPhrasing(e,{after:r,before:a,...l.current()}));const c=s.charCodeAt(0),u=Ur(o.before.charCodeAt(o.before.length-1),c,r);u.inside&&(s=qr(c)+s.slice(1));const d=s.charCodeAt(s.length-1),f=Ur(o.after.charCodeAt(0),d,r);f.inside&&(s=s.slice(0,-1)+qr(d));const p=l.move(r);return i(),n.attentionEncodeSurroundingInfo={after:f.outside,before:u.outside},a+s+p}Nr.peek=function(){return"~"},Xr.peek=function(e,t,n){return n.options.emphasis||"*"};const Gr={};function Kr(e,t){const n=t||Gr;return Yr(e,"boolean"!=typeof n.includeImageAlt||n.includeImageAlt,"boolean"!=typeof n.includeHtml||n.includeHtml)}function Yr(e,t,n){if(function(e){return Boolean(e&&"object"==typeof e)}(e)){if("value"in e)return"html"!==e.type||n?e.value:"";if(t&&"alt"in e&&e.alt)return e.alt;if("children"in e)return Qr(e.children,t,n)}return Array.isArray(e)?Qr(e,t,n):""}function Qr(e,t,n){const o=[];let r=-1;for(;++r<e.length;)o[r]=Yr(e[r],t,n);return o.join("")}function Zr(e,t){let n=!1;return function(e,t,n,o){let r,i,l;"function"==typeof t&&"function"!=typeof n?(i=void 0,l=t,r=n):(i=t,l=n,r=o),Un(e,i,function(e,t){const n=t[t.length-1],o=n?n.children.indexOf(e):void 0;return l(e,o,n)},r)}(e,function(e){if("value"in e&&/\r?\n|\r/.test(e.value)||"break"===e.type)return n=!0,qn}),Boolean((!e.depth||e.depth<3)&&Kr(e)&&(t.options.setext||n))}function Jr(e){return e.value||""}function ei(e,t,n,o){const r=Vr(n),i='"'===r?"Quote":"Apostrophe",l=n.enter("image");let a=n.enter("label");const s=n.createTracker(o);let c=s.move("![");return c+=s.move(n.safe(e.alt,{before:c,after:"]",...s.current()})),c+=s.move("]("),a(),!e.url&&e.title||/[\0- \u007F]/.test(e.url)?(a=n.enter("destinationLiteral"),c+=s.move("<"),c+=s.move(n.safe(e.url,{before:c,after:">",...s.current()})),c+=s.move(">")):(a=n.enter("destinationRaw"),c+=s.move(n.safe(e.url,{before:c,after:e.title?" ":")",...s.current()}))),a(),e.title&&(a=n.enter(`title${i}`),c+=s.move(" "+r),c+=s.move(n.safe(e.title,{before:c,after:r,...s.current()})),c+=s.move(r),a()),c+=s.move(")"),l(),c}function ti(e,t,n,o){const r=e.referenceType,i=n.enter("imageReference");let l=n.enter("label");const a=n.createTracker(o);let s=a.move("![");const c=n.safe(e.alt,{before:s,after:"]",...a.current()});s+=a.move(c+"]["),l();const u=n.stack;n.stack=[],l=n.enter("reference");const d=n.safe(n.associationId(e),{before:s,after:"]",...a.current()});return l(),n.stack=u,i(),"full"!==r&&c&&c===d?"shortcut"===r?s=s.slice(0,-1):s+=a.move("]"):s+=a.move(d+"]"),s}function ni(e,t,n){let o=e.value||"",r="`",i=-1;for(;new RegExp("(^|[^`])"+r+"([^`]|$)").test(o);)r+="`";for(/[^ \r\n]/.test(o)&&(/^[ \r\n]/.test(o)&&/[ \r\n]$/.test(o)||/^`|`$/.test(o))&&(o=" "+o+" ");++i<n.unsafe.length;){const e=n.unsafe[i],t=n.compilePattern(e);let r;if(e.atBreak)for(;r=t.exec(o);){let e=r.index;10===o.charCodeAt(e)&&13===o.charCodeAt(e-1)&&e--,o=o.slice(0,e)+" "+o.slice(r.index+1)}}return r+o+r}function oi(e,t){const n=Kr(e);return Boolean(!t.options.resourceLink&&e.url&&!e.title&&e.children&&1===e.children.length&&"text"===e.children[0].type&&(n===e.url||"mailto:"+n===e.url)&&/^[a-z][a-z+.-]+:/i.test(e.url)&&!/[\0- <>\u007F]/.test(e.url))}function ri(e,t,n,o){const r=Vr(n),i='"'===r?"Quote":"Apostrophe",l=n.createTracker(o);let a,s;if(oi(e,n)){const t=n.stack;n.stack=[],a=n.enter("autolink");let o=l.move("<");return o+=l.move(n.containerPhrasing(e,{before:o,after:">",...l.current()})),o+=l.move(">"),a(),n.stack=t,o}a=n.enter("link"),s=n.enter("label");let c=l.move("[");return c+=l.move(n.containerPhrasing(e,{before:c,after:"](",...l.current()})),c+=l.move("]("),s(),!e.url&&e.title||/[\0- \u007F]/.test(e.url)?(s=n.enter("destinationLiteral"),c+=l.move("<"),c+=l.move(n.safe(e.url,{before:c,after:">",...l.current()})),c+=l.move(">")):(s=n.enter("destinationRaw"),c+=l.move(n.safe(e.url,{before:c,after:e.title?" ":")",...l.current()}))),s(),e.title&&(s=n.enter(`title${i}`),c+=l.move(" "+r),c+=l.move(n.safe(e.title,{before:c,after:r,...l.current()})),c+=l.move(r),s()),c+=l.move(")"),a(),c}function ii(e,t,n,o){const r=e.referenceType,i=n.enter("linkReference");let l=n.enter("label");const a=n.createTracker(o);let s=a.move("[");const c=n.containerPhrasing(e,{before:s,after:"]",...a.current()});s+=a.move(c+"]["),l();const u=n.stack;n.stack=[],l=n.enter("reference");const d=n.safe(n.associationId(e),{before:s,after:"]",...a.current()});return l(),n.stack=u,i(),"full"!==r&&c&&c===d?"shortcut"===r?s=s.slice(0,-1):s+=a.move("]"):s+=a.move(d+"]"),s}function li(e){const t=e.options.bullet||"*";if("*"!==t&&"+"!==t&&"-"!==t)throw new Error("Cannot serialize items with `"+t+"` for `options.bullet`, expected `*`, `+`, or `-`");return t}function ai(e){const t=e.options.rule||"*";if("*"!==t&&"-"!==t&&"_"!==t)throw new Error("Cannot serialize rules with `"+t+"` for `options.rule`, expected `*`, `-`, or `_`");return t}Jr.peek=function(){return"<"},ei.peek=function(){return"!"},ti.peek=function(){return"!"},ni.peek=function(){return"`"},ri.peek=function(e,t,n){return oi(e,n)?"<":"["},ii.peek=function(){return"["};const si=function(e){if(null==e)return ui;if("function"==typeof e)return ci(e);if("object"==typeof e)return Array.isArray(e)?function(e){const t=[];let n=-1;for(;++n<e.length;)t[n]=si(e[n]);return ci(o);function o(...e){let n=-1;for(;++n<t.length;)if(t[n].apply(this,e))return!0;return!1}}(e):function(e){const t=e;return ci(n);function n(n){const o=n;let r;for(r in e)if(o[r]!==t[r])return!1;return!0}}(e);if("string"==typeof e)return function(e){return ci(t);function t(t){return t&&t.type===e}}(e);throw new Error("Expected function, string, or object as test")};function ci(e){return function(t,n,o){return Boolean(di(t)&&e.call(this,t,"number"==typeof n?n:void 0,o||void 0))}}function ui(){return!0}function di(e){return null!==e&&"object"==typeof e&&"type"in e}const fi=si(["break","delete","emphasis","footnote","footnoteReference","image","imageReference","inlineCode","inlineMath","link","linkReference","mdxJsxTextElement","mdxTextExpression","strong","text","textDirective"]);function pi(e,t,n,o){const r=function(e){const t=e.options.strong||"*";if("*"!==t&&"_"!==t)throw new Error("Cannot serialize strong with `"+t+"` for `options.strong`, expected `*`, or `_`");return t}(n),i=n.enter("strong"),l=n.createTracker(o),a=l.move(r+r);let s=l.move(n.containerPhrasing(e,{after:r,before:a,...l.current()}));const c=s.charCodeAt(0),u=Ur(o.before.charCodeAt(o.before.length-1),c,r);u.inside&&(s=qr(c)+s.slice(1));const d=s.charCodeAt(s.length-1),f=Ur(o.after.charCodeAt(0),d,r);f.inside&&(s=s.slice(0,-1)+qr(d));const p=l.move(r+r);return i(),n.attentionEncodeSurroundingInfo={after:f.outside,before:u.outside},a+s+p}pi.peek=function(e,t,n){return n.options.strong||"*"};const mi={blockquote:function(e,t,n,o){const r=n.enter("blockquote"),i=n.createTracker(o);i.move("> "),i.shift(2);const l=n.indentLines(n.containerFlow(e,i.current()),Lr);return r(),l},break:_r,code:function(e,t,n,o){const r=function(e){const t=e.options.fence||"`";if("`"!==t&&"~"!==t)throw new Error("Cannot serialize code with `"+t+"` for `options.fence`, expected `` ` `` or `~`");return t}(n),i=e.value||"",l="`"===r?"GraveAccent":"Tilde";if(function(e,t){return Boolean(!1===t.options.fences&&e.value&&!e.lang&&/[^ \r\n]/.test(e.value)&&!/^[\t ]*(?:[\r\n]|$)|(?:^|[\r\n])[\t ]*$/.test(e.value))}(e,n)){const e=n.enter("codeIndented"),t=n.indentLines(i,Wr);return e(),t}const a=n.createTracker(o),s=r.repeat(Math.max(function(e,t){const n=String(e);let o=n.indexOf(t),r=o,i=0,l=0;if("string"!=typeof t)throw new TypeError("Expected substring");for(;-1!==o;)o===r?++i>l&&(l=i):i=1,r=o+t.length,o=n.indexOf(t,r);return l}(i,r)+1,3)),c=n.enter("codeFenced");let u=a.move(s);if(e.lang){const t=n.enter(`codeFencedLang${l}`);u+=a.move(n.safe(e.lang,{before:u,after:" ",encode:["`"],...a.current()})),t()}if(e.lang&&e.meta){const t=n.enter(`codeFencedMeta${l}`);u+=a.move(" "),u+=a.move(n.safe(e.meta,{before:u,after:"\n",encode:["`"],...a.current()})),t()}return u+=a.move("\n"),i&&(u+=a.move(i+"\n")),u+=a.move(s),c(),u},definition:function(e,t,n,o){const r=Vr(n),i='"'===r?"Quote":"Apostrophe",l=n.enter("definition");let a=n.enter("label");const s=n.createTracker(o);let c=s.move("[");return c+=s.move(n.safe(n.associationId(e),{before:c,after:"]",...s.current()})),c+=s.move("]: "),a(),!e.url||/[\0- \u007F]/.test(e.url)?(a=n.enter("destinationLiteral"),c+=s.move("<"),c+=s.move(n.safe(e.url,{before:c,after:">",...s.current()})),c+=s.move(">")):(a=n.enter("destinationRaw"),c+=s.move(n.safe(e.url,{before:c,after:e.title?" ":"\n",...s.current()}))),a(),e.title&&(a=n.enter(`title${i}`),c+=s.move(" "+r),c+=s.move(n.safe(e.title,{before:c,after:r,...s.current()})),c+=s.move(r),a()),l(),c},emphasis:Xr,hardBreak:_r,heading:function(e,t,n,o){const r=Math.max(Math.min(6,e.depth||1),1),i=n.createTracker(o);if(Zr(e,n)){const t=n.enter("headingSetext"),o=n.enter("phrasing"),l=n.containerPhrasing(e,{...i.current(),before:"\n",after:"\n"});return o(),t(),l+"\n"+(1===r?"=":"-").repeat(l.length-(Math.max(l.lastIndexOf("\r"),l.lastIndexOf("\n"))+1))}const l="#".repeat(r),a=n.enter("headingAtx"),s=n.enter("phrasing");i.move(l+" ");let c=n.containerPhrasing(e,{before:"# ",after:"\n",...i.current()});return/^[\t ]/.test(c)&&(c=qr(c.charCodeAt(0))+c.slice(1)),c=c?l+" "+c:l,n.options.closeAtx&&(c+=" "+l),s(),a(),c},html:Jr,image:ei,imageReference:ti,inlineCode:ni,link:ri,linkReference:ii,list:function(e,t,n,o){const r=n.enter("list"),i=n.bulletCurrent;let l=e.ordered?function(e){const t=e.options.bulletOrdered||".";if("."!==t&&")"!==t)throw new Error("Cannot serialize items with `"+t+"` for `options.bulletOrdered`, expected `.` or `)`");return t}(n):li(n);const a=e.ordered?"."===l?")":".":function(e){const t=li(e),n=e.options.bulletOther;if(!n)return"*"===t?"-":"*";if("*"!==n&&"+"!==n&&"-"!==n)throw new Error("Cannot serialize items with `"+n+"` for `options.bulletOther`, expected `*`, `+`, or `-`");if(n===t)throw new Error("Expected `bullet` (`"+t+"`) and `bulletOther` (`"+n+"`) to be different");return n}(n);let s=!(!t||!n.bulletLastUsed)&&l===n.bulletLastUsed;if(!e.ordered){const t=e.children?e.children[0]:void 0;if("*"!==l&&"-"!==l||!t||t.children&&t.children[0]||"list"!==n.stack[n.stack.length-1]||"listItem"!==n.stack[n.stack.length-2]||"list"!==n.stack[n.stack.length-3]||"listItem"!==n.stack[n.stack.length-4]||0!==n.indexStack[n.indexStack.length-1]||0!==n.indexStack[n.indexStack.length-2]||0!==n.indexStack[n.indexStack.length-3]||(s=!0),ai(n)===l&&t){let t=-1;for(;++t<e.children.length;){const n=e.children[t];if(n&&"listItem"===n.type&&n.children&&n.children[0]&&"thematicBreak"===n.children[0].type){s=!0;break}}}}s&&(l=a),n.bulletCurrent=l;const c=n.containerFlow(e,o);return n.bulletLastUsed=l,n.bulletCurrent=i,r(),c},listItem:function(e,t,n,o){const r=function(e){const t=e.options.listItemIndent||"one";if("tab"!==t&&"one"!==t&&"mixed"!==t)throw new Error("Cannot serialize items with `"+t+"` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`");return t}(n);let i=n.bulletCurrent||li(n);t&&"list"===t.type&&t.ordered&&(i=("number"==typeof t.start&&t.start>-1?t.start:1)+(!1===n.options.incrementListMarker?0:t.children.indexOf(e))+i);let l=i.length+1;("tab"===r||"mixed"===r&&(t&&"list"===t.type&&t.spread||e.spread))&&(l=4*Math.ceil(l/4));const a=n.createTracker(o);a.move(i+" ".repeat(l-i.length)),a.shift(l);const s=n.enter("listItem"),c=n.indentLines(n.containerFlow(e,a.current()),function(e,t,n){if(t)return(n?"":" ".repeat(l))+e;return(n?i:i+" ".repeat(l-i.length))+e});return s(),c},paragraph:function(e,t,n,o){const r=n.enter("paragraph"),i=n.enter("phrasing"),l=n.containerPhrasing(e,o);return i(),r(),l},root:function(e,t,n,o){return(e.children.some(function(e){return fi(e)})?n.containerPhrasing:n.containerFlow).call(n,e,o)},strong:pi,text:function(e,t,n,o){return n.safe(e.value,o)},thematicBreak:function(e,t,n){const o=(ai(n)+(n.options.ruleSpaces?" ":"")).repeat(function(e){const t=e.options.ruleRepetition||3;if(t<3)throw new Error("Cannot serialize rules with repetition `"+t+"` for `options.ruleRepetition`, expected `3` or more");return t}(n));return n.options.ruleSpaces?o.slice(0,-1):o}};function hi(e){const t=e._align;this.enter({type:"table",align:t.map(function(e){return"none"===e?null:e}),children:[]},e),this.data.inTable=!0}function gi(e){this.exit(e),this.data.inTable=void 0}function vi(e){this.enter({type:"tableRow",children:[]},e)}function bi(e){this.exit(e)}function yi(e){this.enter({type:"tableCell",children:[]},e)}function xi(e){let t=this.resume();this.data.inTable&&(t=t.replace(/\\([\\|])/g,wi));const n=this.stack[this.stack.length-1];n.type,n.value=t,this.exit(e)}function wi(e,t){return"|"===t?t:e}function Ci(e){const t=e||{},n=t.tableCellPadding,o=t.tablePipeAlign,r=t.stringLength,i=n?" ":"|";return{unsafe:[{character:"\r",inConstruct:"tableCell"},{character:"\n",inConstruct:"tableCell"},{atBreak:!0,character:"|",after:"[\t :-]"},{character:"|",inConstruct:"tableCell"},{atBreak:!0,character:":",after:"-"},{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{inlineCode:function(e,t,n){let o=mi.inlineCode(e,t,n);n.stack.includes("tableCell")&&(o=o.replace(/\|/g,"\\$&"));return o},table:function(e,t,n,o){return a(function(e,t,n){const o=e.children;let r=-1;const i=[],l=t.enter("table");for(;++r<o.length;)i[r]=s(o[r],t,n);return l(),i}(e,n,o),e.align)},tableCell:l,tableRow:function(e,t,n,o){const r=s(e,n,o),i=a([r]);return i.slice(0,i.indexOf("\n"))}}};function l(e,t,n,o){const r=n.enter("tableCell"),l=n.enter("phrasing"),a=n.containerPhrasing(e,{...o,before:i,after:i});return l(),r(),a}function a(e,t){return function(e,t){const n=t||{},o=(n.align||[]).concat(),r=n.stringLength||Br,i=[],l=[],a=[],s=[];let c=0,u=-1;for(;++u<e.length;){const t=[],o=[];let i=-1;for(e[u].length>c&&(c=e[u].length);++i<e[u].length;){const l=Rr(e[u][i]);if(!1!==n.alignDelimiters){const e=r(l);o[i]=e,(void 0===s[i]||e>s[i])&&(s[i]=e)}t.push(l)}l[u]=t,a[u]=o}let d=-1;if("object"==typeof o&&"length"in o)for(;++d<c;)i[d]=Dr(o[d]);else{const e=Dr(o);for(;++d<c;)i[d]=e}d=-1;const f=[],p=[];for(;++d<c;){const e=i[d];let t="",o="";99===e?(t=":",o=":"):108===e?t=":":114===e&&(o=":");let r=!1===n.alignDelimiters?1:Math.max(1,s[d]-t.length-o.length);const l=t+"-".repeat(r)+o;!1!==n.alignDelimiters&&(r=t.length+r+o.length,r>s[d]&&(s[d]=r),p[d]=r),f[d]=l}l.splice(1,0,f),a.splice(1,0,p),u=-1;const m=[];for(;++u<l.length;){const e=l[u],t=a[u];d=-1;const o=[];for(;++d<c;){const r=e[d]||"";let l="",a="";if(!1!==n.alignDelimiters){const e=s[d]-(t[d]||0),n=i[d];114===n?l=" ".repeat(e):99===n?e%2?(l=" ".repeat(e/2+.5),a=" ".repeat(e/2-.5)):(l=" ".repeat(e/2),a=l):a=" ".repeat(e)}!1===n.delimiterStart||d||o.push("|"),!1===n.padding||!1===n.alignDelimiters&&""===r||!1===n.delimiterStart&&!d||o.push(" "),!1!==n.alignDelimiters&&o.push(l),o.push(r),!1!==n.alignDelimiters&&o.push(a),!1!==n.padding&&o.push(" "),!1===n.delimiterEnd&&d===c-1||o.push("|")}m.push(!1===n.delimiterEnd?o.join("").replace(/ +$/,""):o.join(""))}return m.join("\n")}(e,{align:t,alignDelimiters:o,padding:n,stringLength:r})}function s(e,t,n){const o=e.children;let r=-1;const i=[],a=t.enter("tableRow");for(;++r<o.length;)i[r]=l(o[r],0,t,n);return a(),i}}function ki(e){const t=this.stack[this.stack.length-2];t.type,t.checked="taskListCheckValueChecked"===e.type}function $i(e){const t=this.stack[this.stack.length-2];if(t&&"listItem"===t.type&&"boolean"==typeof t.checked){const e=this.stack[this.stack.length-1];e.type;const n=e.children[0];if(n&&"text"===n.type){const o=t.children;let r,i=-1;for(;++i<o.length;){const e=o[i];if("paragraph"===e.type){r=e;break}}r===e&&(n.value=n.value.slice(1),0===n.value.length?e.children.shift():e.position&&n.position&&"number"==typeof n.position.start.offset&&(n.position.start.column++,n.position.start.offset++,e.position.start=Object.assign({},n.position.start)))}}this.exit(e)}function Si(e,t,n,o){const r=e.children[0],i="boolean"==typeof e.checked&&r&&"paragraph"===r.type,l="["+(e.checked?"x":" ")+"] ",a=n.createTracker(o);i&&a.move(l);let s=mi.listItem(e,t,n,{...o,...a.current()});return i&&(s=s.replace(/^(?:[*+-]|\d+\.)([\r\n]| {1,3})/,function(e){return e+l})),s}const Ai={}.hasOwnProperty;function Ei(e,t){let n;for(n in t){const o=(Ai.call(e,n)?e[n]:void 0)||(e[n]={}),r=t[n];let i;if(r)for(i in r){Ai.call(o,i)||(o[i]=[]);const e=r[i];Ii(o[i],Array.isArray(e)?e:e?[e]:[])}}}function Ii(e,t){let n=-1;const o=[];for(;++n<t.length;)("after"===t[n].add?e:o).push(t[n]);Se(e,0,0,o)}const Oi=ji(/[A-Za-z]/),zi=ji(/[\dA-Za-z]/);ji(/[#-'*+\--9=?A-Z^-~]/);ji(/\d/),ji(/[\dA-Fa-f]/),ji(/[!-/:-@[-`{-~]/);function Mi(e){return null!==e&&(e<0||32===e)}const Pi=ji(/\p{P}|\p{S}/u),Ti=ji(/\s/);function ji(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}const Ni={tokenize:function(e,t,n){let o=0;return function t(i){if((87===i||119===i)&&o<3)return o++,e.consume(i),t;if(46===i&&3===o)return e.consume(i),r;return n(i)};function r(e){return null===e?n(e):t(e)}},partial:!0},Bi={tokenize:function(e,t,n){let o,r,i;return l;function l(t){return 46===t||95===t?e.check(Di,s,a)(t):null===t||Mi(t)||Ti(t)||45!==t&&Pi(t)?s(t):(i=!0,e.consume(t),l)}function a(t){return 95===t?o=!0:(r=o,o=void 0),e.consume(t),l}function s(e){return r||o||!i?n(e):t(e)}},partial:!0},Ri={tokenize:function(e,t){let n=0,o=0;return r;function r(l){return 40===l?(n++,e.consume(l),r):41===l&&o<n?i(l):33===l||34===l||38===l||39===l||41===l||42===l||44===l||46===l||58===l||59===l||60===l||63===l||93===l||95===l||126===l?e.check(Di,t,i)(l):null===l||Mi(l)||Ti(l)?t(l):(e.consume(l),r)}function i(t){return 41===t&&o++,e.consume(t),r}},partial:!0},Di={tokenize:function(e,t,n){return o;function o(l){return 33===l||34===l||39===l||41===l||42===l||44===l||46===l||58===l||59===l||63===l||95===l||126===l?(e.consume(l),o):38===l?(e.consume(l),i):93===l?(e.consume(l),r):60===l||null===l||Mi(l)||Ti(l)?t(l):n(l)}function r(e){return null===e||40===e||91===e||Mi(e)||Ti(e)?t(e):o(e)}function i(e){return Oi(e)?l(e):n(e)}function l(t){return 59===t?(e.consume(t),o):Oi(t)?(e.consume(t),l):n(t)}},partial:!0},Li={tokenize:function(e,t,n){return function(t){return e.consume(t),o};function o(e){return zi(e)?n(e):t(e)}},partial:!0},Hi={name:"wwwAutolink",tokenize:function(e,t,n){const o=this;return function(t){if(87!==t&&119!==t||!qi.call(o,o.previous)||Ki(o.events))return n(t);return e.enter("literalAutolink"),e.enter("literalAutolinkWww"),e.check(Ni,e.attempt(Bi,e.attempt(Ri,r),n),n)(t)};function r(n){return e.exit("literalAutolinkWww"),e.exit("literalAutolink"),t(n)}},previous:qi},Fi={name:"protocolAutolink",tokenize:function(e,t,n){const o=this;let r="",i=!1;return function(t){if((72===t||104===t)&&Ui.call(o,o.previous)&&!Ki(o.events))return e.enter("literalAutolink"),e.enter("literalAutolinkHttp"),r+=String.fromCodePoint(t),e.consume(t),l;return n(t)};function l(t){if(Oi(t)&&r.length<5)return r+=String.fromCodePoint(t),e.consume(t),l;if(58===t){const n=r.toLowerCase();if("http"===n||"https"===n)return e.consume(t),a}return n(t)}function a(t){return 47===t?(e.consume(t),i?s:(i=!0,a)):n(t)}function s(t){return null===t||function(e){return null!==e&&(e<32||127===e)}(t)||Mi(t)||Ti(t)||Pi(t)?n(t):e.attempt(Bi,e.attempt(Ri,c),n)(t)}function c(n){return e.exit("literalAutolinkHttp"),e.exit("literalAutolink"),t(n)}},previous:Ui},_i={name:"emailAutolink",tokenize:function(e,t,n){const o=this;let r,i;return function(t){if(!Gi(t)||!Xi.call(o,o.previous)||Ki(o.events))return n(t);return e.enter("literalAutolink"),e.enter("literalAutolinkEmail"),l(t)};function l(t){return Gi(t)?(e.consume(t),l):64===t?(e.consume(t),a):n(t)}function a(t){return 46===t?e.check(Li,c,s)(t):45===t||95===t||zi(t)?(i=!0,e.consume(t),a):c(t)}function s(t){return e.consume(t),r=!0,a}function c(l){return i&&r&&Oi(o.previous)?(e.exit("literalAutolinkEmail"),e.exit("literalAutolink"),t(l)):n(l)}},previous:Xi},Wi={};let Vi=48;for(;Vi<123;)Wi[Vi]=_i,Vi++,58===Vi?Vi=65:91===Vi&&(Vi=97);function qi(e){return null===e||40===e||42===e||95===e||91===e||93===e||126===e||Mi(e)}function Ui(e){return!Oi(e)}function Xi(e){return!(47===e||Gi(e))}function Gi(e){return 43===e||45===e||46===e||95===e||zi(e)}function Ki(e){let t=e.length,n=!1;for(;t--;){const o=e[t][1];if(("labelLink"===o.type||"labelImage"===o.type)&&!o._balanced){n=!0;break}if(o._gfmAutolinkLiteralWalkedInto){n=!1;break}}return e.length>0&&!n&&(e[e.length-1][1]._gfmAutolinkLiteralWalkedInto=!0),n}Wi[43]=_i,Wi[45]=_i,Wi[46]=_i,Wi[95]=_i,Wi[72]=[_i,Fi],Wi[104]=[_i,Fi],Wi[87]=[_i,Hi],Wi[119]=[_i,Hi];Qi(/[A-Za-z]/),Qi(/[\dA-Za-z]/),Qi(/[#-'*+\--9=?A-Z^-~]/);Qi(/\d/),Qi(/[\dA-Fa-f]/),Qi(/[!-/:-@[-`{-~]/);function Yi(e){return null!==e&&(e<0||32===e)}Qi(/\p{P}|\p{S}/u),Qi(/\s/);function Qi(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}const Zi={partial:!0,tokenize:function(e,t,n){return function(t){return function(e){return-2===e||-1===e||32===e}(t)?Be(e,o,"linePrefix")(t):o(t)};function o(e){return null===e||function(e){return null!==e&&e<-2}(e)?t(e):n(e)}}};const Ji={tokenize:function(e,t,n){const o=this;return Be(e,function(e){const r=o.events[o.events.length-1];return r&&"gfmFootnoteDefinitionIndent"===r[1].type&&4===r[2].sliceSerialize(r[1],!0).length?t(e):n(e)},"gfmFootnoteDefinitionIndent",5)},partial:!0};function el(e,t,n){const o=this;let r=o.events.length;const i=o.parser.gfmFootnotes||(o.parser.gfmFootnotes=[]);let l;for(;r--;){const e=o.events[r][1];if("labelImage"===e.type){l=e;break}if("gfmFootnoteCall"===e.type||"labelLink"===e.type||"label"===e.type||"image"===e.type||"link"===e.type)break}return function(r){if(!l||!l._balanced)return n(r);const a=gt(o.sliceSerialize({start:l.end,end:o.now()}));if(94!==a.codePointAt(0)||!i.includes(a.slice(1)))return n(r);return e.enter("gfmFootnoteCallLabelMarker"),e.consume(r),e.exit("gfmFootnoteCallLabelMarker"),t(r)}}function tl(e,t){let n,o=e.length;for(;o--;)if("labelImage"===e[o][1].type&&"enter"===e[o][0]){n=e[o][1];break}e[o+1][1].type="data",e[o+3][1].type="gfmFootnoteCallLabelMarker";const r={type:"gfmFootnoteCall",start:Object.assign({},e[o+3][1].start),end:Object.assign({},e[e.length-1][1].end)},i={type:"gfmFootnoteCallMarker",start:Object.assign({},e[o+3][1].end),end:Object.assign({},e[o+3][1].end)};i.end.column++,i.end.offset++,i.end._bufferIndex++;const l={type:"gfmFootnoteCallString",start:Object.assign({},i.end),end:Object.assign({},e[e.length-1][1].start)},a={type:"chunkString",contentType:"string",start:Object.assign({},l.start),end:Object.assign({},l.end)},s=[e[o+1],e[o+2],["enter",r,t],e[o+3],e[o+4],["enter",i,t],["exit",i,t],["enter",l,t],["enter",a,t],["exit",a,t],["exit",l,t],e[e.length-2],e[e.length-1],["exit",r,t]];return e.splice(o,e.length-o+1,...s),e}function nl(e,t,n){const o=this,r=o.parser.gfmFootnotes||(o.parser.gfmFootnotes=[]);let i,l=0;return function(t){return e.enter("gfmFootnoteCall"),e.enter("gfmFootnoteCallLabelMarker"),e.consume(t),e.exit("gfmFootnoteCallLabelMarker"),a};function a(t){return 94!==t?n(t):(e.enter("gfmFootnoteCallMarker"),e.consume(t),e.exit("gfmFootnoteCallMarker"),e.enter("gfmFootnoteCallString"),e.enter("chunkString").contentType="string",s)}function s(a){if(l>999||93===a&&!i||null===a||91===a||Yi(a))return n(a);if(93===a){e.exit("chunkString");const i=e.exit("gfmFootnoteCallString");return r.includes(gt(o.sliceSerialize(i)))?(e.enter("gfmFootnoteCallLabelMarker"),e.consume(a),e.exit("gfmFootnoteCallLabelMarker"),e.exit("gfmFootnoteCall"),t):n(a)}return Yi(a)||(i=!0),l++,e.consume(a),92===a?c:s}function c(t){return 91===t||92===t||93===t?(e.consume(t),l++,s):s(t)}}function ol(e,t,n){const o=this,r=o.parser.gfmFootnotes||(o.parser.gfmFootnotes=[]);let i,l,a=0;return function(t){return e.enter("gfmFootnoteDefinition")._container=!0,e.enter("gfmFootnoteDefinitionLabel"),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionLabelMarker"),s};function s(t){return 94===t?(e.enter("gfmFootnoteDefinitionMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionMarker"),e.enter("gfmFootnoteDefinitionLabelString"),e.enter("chunkString").contentType="string",c):n(t)}function c(t){if(a>999||93===t&&!l||null===t||91===t||Yi(t))return n(t);if(93===t){e.exit("chunkString");const n=e.exit("gfmFootnoteDefinitionLabelString");return i=gt(o.sliceSerialize(n)),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionLabelMarker"),e.exit("gfmFootnoteDefinitionLabel"),d}return Yi(t)||(l=!0),a++,e.consume(t),92===t?u:c}function u(t){return 91===t||92===t||93===t?(e.consume(t),a++,c):c(t)}function d(t){return 58===t?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),r.includes(i)||r.push(i),Be(e,f,"gfmFootnoteDefinitionWhitespace")):n(t)}function f(e){return t(e)}}function rl(e,t,n){return e.check(Zi,t,e.attempt(Ji,t,n))}function il(e){e.exit("gfmFootnoteDefinition")}function ll(e){let t=(e||{}).singleTilde;const n={name:"strikethrough",tokenize:function(e,n,o){const r=this.previous,i=this.events;let l=0;return function(t){if(126===r&&"characterEscape"!==i[i.length-1][1].type)return o(t);return e.enter("strikethroughSequenceTemporary"),a(t)};function a(i){const s=Vt(r);if(126===i)return l>1?o(i):(e.consume(i),l++,a);if(l<2&&!t)return o(i);const c=e.exit("strikethroughSequenceTemporary"),u=Vt(i);return c._open=!u||2===u&&Boolean(s),c._close=!s||2===s&&Boolean(u),n(i)}},resolveAll:function(e,t){let n=-1;for(;++n<e.length;)if("enter"===e[n][0]&&"strikethroughSequenceTemporary"===e[n][1].type&&e[n][1]._close){let o=n;for(;o--;)if("exit"===e[o][0]&&"strikethroughSequenceTemporary"===e[o][1].type&&e[o][1]._open&&e[n][1].end.offset-e[n][1].start.offset===e[o][1].end.offset-e[o][1].start.offset){e[n][1].type="strikethroughSequence",e[o][1].type="strikethroughSequence";const r={type:"strikethrough",start:Object.assign({},e[o][1].start),end:Object.assign({},e[n][1].end)},i={type:"strikethroughText",start:Object.assign({},e[o][1].end),end:Object.assign({},e[n][1].start)},l=[["enter",r,t],["enter",e[o][1],t],["exit",e[o][1],t],["enter",i,t]],a=t.parser.constructs.insideSpan.null;a&&Se(l,l.length,0,Nt(a,e.slice(o+1,n),t)),Se(l,l.length,0,[["exit",i,t],["enter",e[n][1],t],["exit",e[n][1],t],["exit",r,t]]),Se(e,o-1,n-o+3,l),n=o+l.length-2;break}}n=-1;for(;++n<e.length;)"strikethroughSequenceTemporary"===e[n][1].type&&(e[n][1].type="data");return e}};return null==t&&(t=!0),{text:{126:n},insideSpan:{null:[n]},attentionMarkers:{null:[126]}}}ul(/[A-Za-z]/),ul(/[\dA-Za-z]/),ul(/[#-'*+\--9=?A-Z^-~]/);ul(/\d/),ul(/[\dA-Fa-f]/),ul(/[!-/:-@[-`{-~]/);function al(e){return null!==e&&e<-2}function sl(e){return null!==e&&(e<0||32===e)}function cl(e){return-2===e||-1===e||32===e}ul(/\p{P}|\p{S}/u),ul(/\s/);function ul(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}class dl{constructor(){this.map=[]}add(e,t,n){!function(e,t,n,o){let r=0;if(0===n&&0===o.length)return;for(;r<e.map.length;){if(e.map[r][0]===t)return e.map[r][1]+=n,void e.map[r][2].push(...o);r+=1}e.map.push([t,n,o])}(this,e,t,n)}consume(e){if(this.map.sort(function(e,t){return e[0]-t[0]}),0===this.map.length)return;let t=this.map.length;const n=[];for(;t>0;)t-=1,n.push(e.slice(this.map[t][0]+this.map[t][1]),this.map[t][2]),e.length=this.map[t][0];n.push(e.slice()),e.length=0;let o=n.pop();for(;o;){for(const t of o)e.push(t);o=n.pop()}this.map.length=0}}function fl(e,t){let n=!1;const o=[];for(;t<e.length;){const r=e[t];if(n){if("enter"===r[0])"tableContent"===r[1].type&&o.push("tableDelimiterMarker"===e[t+1][1].type?"left":"none");else if("tableContent"===r[1].type){if("tableDelimiterMarker"===e[t-1][1].type){const e=o.length-1;o[e]="left"===o[e]?"center":"right"}}else if("tableDelimiterRow"===r[1].type)break}else"enter"===r[0]&&"tableDelimiterRow"===r[1].type&&(n=!0);t+=1}return o}function pl(e,t,n){const o=this;let r,i=0,l=0;return function(e){let t=o.events.length-1;for(;t>-1;){const e=o.events[t][1].type;if("lineEnding"!==e&&"linePrefix"!==e)break;t--}const r=t>-1?o.events[t][1].type:null,i="tableHead"===r||"tableRow"===r?x:a;if(i===x&&o.parser.lazy[o.now().line])return n(e);return i(e)};function a(t){return e.enter("tableHead"),e.enter("tableRow"),function(e){if(124===e)return s(e);return r=!0,l+=1,s(e)}(t)}function s(t){return null===t?n(t):al(t)?l>1?(l=0,o.interrupt=!0,e.exit("tableRow"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),d):n(t):cl(t)?Be(e,s,"whitespace")(t):(l+=1,r&&(r=!1,i+=1),124===t?(e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),r=!0,s):(e.enter("data"),c(t)))}function c(t){return null===t||124===t||sl(t)?(e.exit("data"),s(t)):(e.consume(t),92===t?u:c)}function u(t){return 92===t||124===t?(e.consume(t),c):c(t)}function d(t){return o.interrupt=!1,o.parser.lazy[o.now().line]?n(t):(e.enter("tableDelimiterRow"),r=!1,cl(t)?Be(e,f,"linePrefix",o.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):f(t))}function f(t){return 45===t||58===t?m(t):124===t?(r=!0,e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),p):y(t)}function p(t){return cl(t)?Be(e,m,"whitespace")(t):m(t)}function m(t){return 58===t?(l+=1,r=!0,e.enter("tableDelimiterMarker"),e.consume(t),e.exit("tableDelimiterMarker"),h):45===t?(l+=1,h(t)):null===t||al(t)?b(t):y(t)}function h(t){return 45===t?(e.enter("tableDelimiterFiller"),g(t)):y(t)}function g(t){return 45===t?(e.consume(t),g):58===t?(r=!0,e.exit("tableDelimiterFiller"),e.enter("tableDelimiterMarker"),e.consume(t),e.exit("tableDelimiterMarker"),v):(e.exit("tableDelimiterFiller"),v(t))}function v(t){return cl(t)?Be(e,b,"whitespace")(t):b(t)}function b(n){return 124===n?f(n):(null===n||al(n))&&r&&i===l?(e.exit("tableDelimiterRow"),e.exit("tableHead"),t(n)):y(n)}function y(e){return n(e)}function x(t){return e.enter("tableRow"),w(t)}function w(n){return 124===n?(e.enter("tableCellDivider"),e.consume(n),e.exit("tableCellDivider"),w):null===n||al(n)?(e.exit("tableRow"),t(n)):cl(n)?Be(e,w,"whitespace")(n):(e.enter("data"),C(n))}function C(t){return null===t||124===t||sl(t)?(e.exit("data"),w(t)):(e.consume(t),92===t?k:C)}function k(t){return 92===t||124===t?(e.consume(t),C):C(t)}}function ml(e,t){let n,o,r,i=-1,l=!0,a=0,s=[0,0,0,0],c=[0,0,0,0],u=!1,d=0;const f=new dl;for(;++i<e.length;){const p=e[i],m=p[1];"enter"===p[0]?"tableHead"===m.type?(u=!1,0!==d&&(gl(f,t,d,n,o),o=void 0,d=0),n={type:"table",start:Object.assign({},m.start),end:Object.assign({},m.end)},f.add(i,0,[["enter",n,t]])):"tableRow"===m.type||"tableDelimiterRow"===m.type?(l=!0,r=void 0,s=[0,0,0,0],c=[0,i+1,0,0],u&&(u=!1,o={type:"tableBody",start:Object.assign({},m.start),end:Object.assign({},m.end)},f.add(i,0,[["enter",o,t]])),a="tableDelimiterRow"===m.type?2:o?3:1):!a||"data"!==m.type&&"tableDelimiterMarker"!==m.type&&"tableDelimiterFiller"!==m.type?"tableCellDivider"===m.type&&(l?l=!1:(0!==s[1]&&(c[0]=c[1],r=hl(f,t,s,a,void 0,r)),s=c,c=[s[1],i,0,0])):(l=!1,0===c[2]&&(0!==s[1]&&(c[0]=c[1],r=hl(f,t,s,a,void 0,r),s=[0,0,0,0]),c[2]=i)):"tableHead"===m.type?(u=!0,d=i):"tableRow"===m.type||"tableDelimiterRow"===m.type?(d=i,0!==s[1]?(c[0]=c[1],r=hl(f,t,s,a,i,r)):0!==c[1]&&(r=hl(f,t,c,a,i,r)),a=0):!a||"data"!==m.type&&"tableDelimiterMarker"!==m.type&&"tableDelimiterFiller"!==m.type||(c[3]=i)}for(0!==d&&gl(f,t,d,n,o),f.consume(t.events),i=-1;++i<t.events.length;){const e=t.events[i];"enter"===e[0]&&"table"===e[1].type&&(e[1]._align=fl(t.events,i))}return e}function hl(e,t,n,o,r,i){const l=1===o?"tableHeader":2===o?"tableDelimiter":"tableData";0!==n[0]&&(i.end=Object.assign({},vl(t.events,n[0])),e.add(n[0],0,[["exit",i,t]]));const a=vl(t.events,n[1]);if(i={type:l,start:Object.assign({},a),end:Object.assign({},a)},e.add(n[1],0,[["enter",i,t]]),0!==n[2]){const r=vl(t.events,n[2]),i=vl(t.events,n[3]),l={type:"tableContent",start:Object.assign({},r),end:Object.assign({},i)};if(e.add(n[2],0,[["enter",l,t]]),2!==o){const o=t.events[n[2]],r=t.events[n[3]];if(o[1].end=Object.assign({},r[1].end),o[1].type="chunkText",o[1].contentType="text",n[3]>n[2]+1){const t=n[2]+1,o=n[3]-n[2]-1;e.add(t,o,[])}}e.add(n[3]+1,0,[["exit",l,t]])}return void 0!==r&&(i.end=Object.assign({},vl(t.events,r)),e.add(r,0,[["exit",i,t]]),i=void 0),i}function gl(e,t,n,o,r){const i=[],l=vl(t.events,n);r&&(r.end=Object.assign({},l),i.push(["exit",r,t])),o.end=Object.assign({},l),i.push(["exit",o,t]),e.add(n+1,0,i)}function vl(e,t){const n=e[t],o="enter"===n[0]?"start":"end";return n[1][o]}bl(/[A-Za-z]/),bl(/[\dA-Za-z]/),bl(/[#-'*+\--9=?A-Z^-~]/);bl(/\d/),bl(/[\dA-Fa-f]/),bl(/[!-/:-@[-`{-~]/);bl(/\p{P}|\p{S}/u),bl(/\s/);function bl(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}const yl={name:"tasklistCheck",tokenize:function(e,t,n){const o=this;return function(t){if(null!==o.previous||!o._gfmTasklistFirstContentOfListItem)return n(t);return e.enter("taskListCheck"),e.enter("taskListCheckMarker"),e.consume(t),e.exit("taskListCheckMarker"),r};function r(t){return function(e){return null!==e&&(e<0||32===e)}(t)?(e.enter("taskListCheckValueUnchecked"),e.consume(t),e.exit("taskListCheckValueUnchecked"),i):88===t||120===t?(e.enter("taskListCheckValueChecked"),e.consume(t),e.exit("taskListCheckValueChecked"),i):n(t)}function i(t){return 93===t?(e.enter("taskListCheckMarker"),e.consume(t),e.exit("taskListCheckMarker"),e.exit("taskListCheck"),l):n(t)}function l(o){return function(e){return null!==e&&e<-2}(o)?t(o):function(e){return-2===e||-1===e||32===e}(o)?e.check({tokenize:xl},t,n)(o):n(o)}}};function xl(e,t,n){return Be(e,function(e){return null===e?n(e):t(e)},"whitespace")}function wl(e){return function(e){const t={};let n=-1;for(;++n<e.length;)Ei(t,e[n]);return t}([{text:Wi},{document:{91:{name:"gfmFootnoteDefinition",tokenize:ol,continuation:{tokenize:rl},exit:il}},text:{91:{name:"gfmFootnoteCall",tokenize:nl},93:{name:"gfmPotentialFootnoteCall",add:"after",tokenize:el,resolveTo:tl}}},ll(e),{flow:{null:{name:"table",tokenize:pl,resolveAll:ml}}},{text:{91:yl}}])}const Cl={};function kl(e){const t=e||Cl,n=this.data(),o=n.micromarkExtensions||(n.micromarkExtensions=[]),r=n.fromMarkdownExtensions||(n.fromMarkdownExtensions=[]),i=n.toMarkdownExtensions||(n.toMarkdownExtensions=[]);o.push(wl(t)),r.push([{transforms:[gr],enter:{literalAutolink:ur,literalAutolinkEmail:dr,literalAutolinkHttp:dr,literalAutolinkWww:dr},exit:{literalAutolink:hr,literalAutolinkEmail:mr,literalAutolinkHttp:fr,literalAutolinkWww:pr}},{enter:{gfmFootnoteCallString:xr,gfmFootnoteCall:wr,gfmFootnoteDefinitionLabelString:Cr,gfmFootnoteDefinition:kr},exit:{gfmFootnoteCallString:$r,gfmFootnoteCall:Sr,gfmFootnoteDefinitionLabelString:Ar,gfmFootnoteDefinition:Er}},{canContainEols:["delete"],enter:{strikethrough:Tr},exit:{strikethrough:jr}},{enter:{table:hi,tableData:yi,tableHeader:yi,tableRow:vi},exit:{codeText:xi,table:gi,tableData:bi,tableHeader:bi,tableRow:bi}},{exit:{taskListCheckValueChecked:ki,taskListCheckValueUnchecked:ki,paragraph:$i}}]),i.push(function(e){return{extensions:[{unsafe:[{character:"@",before:"[+\\-.\\w]",after:"[\\-.\\w]",inConstruct:sr,notInConstruct:cr},{character:".",before:"[Ww]",after:"[\\-.\\w]",inConstruct:sr,notInConstruct:cr},{character:":",before:"[ps]",after:"\\/",inConstruct:sr,notInConstruct:cr}]},Or(e),{unsafe:[{character:"~",inConstruct:"phrasing",notInConstruct:Pr}],handlers:{delete:Nr}},Ci(e),{unsafe:[{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{listItem:Si}}]}}(t))}const $l=e=>{let{size:t=8}=e;return i.createElement("span",{className:"inline-flex items-center gap-2"},i.createElement("span",{className:"bg-accent rounded-full animate-bounce",style:{width:`${t}px`,height:`${t}px`,animationDuration:"0.6s"}}),i.createElement("span",{className:"bg-accent rounded-full animate-bounce",style:{width:`${t}px`,height:`${t}px`,animationDuration:"0.6s",animationDelay:"0.2s"}}),i.createElement("span",{className:"bg-accent rounded-full animate-bounce",style:{width:`${t}px`,height:`${t}px`,animationDuration:"0.6s",animationDelay:"0.4s"}}))};const Sl=(0,i.memo)(e=>{let{content:t="",isJson:n=!1,className:o="",jsonThreshold:r=1e3,textThreshold:u=500,showFullscreen:d=!0}=e;const{0:f,1:p}=(0,i.useState)(!1),{0:m,1:h}=(0,i.useState)(!1),g=n?r:u;var v;t=("object"==typeof(v=t)&&null!==v?JSON.stringify(v):v)+"";const b=t.length>g,y=b&&!f?t.slice(0,g)+"...":t,x=" dark:prose-invert prose-table:border-hidden prose-td:border-t prose-th:border-b prose-ul:list-disc prose-sm prose-ol:list-decimal ";return i.createElement("div",{className:"relative"},i.createElement("div",{className:`\n            transition-[max-height,opacity] overflow-auto scroll  duration-500 ease-in-out\n            ${b&&!f?"max-h-[300px]":"max-h-[10000px]"}\n            ${o}\n          `},i.createElement(qo,{className:f?`mt-4 text-sm text-primary ${x}`:"",remarkPlugins:[kl]},y),b&&!f&&i.createElement("div",{className:"absolute bottom-0 left-0 right-0 h-24 bg-gradient-to-t from-secondary to-transparent opacity-20"})),b&&i.createElement("div",{className:"mt-2 flex items-center justify-end gap-2"},i.createElement("button",{title:f?"Show less":"Show more",type:"button",onClick:e=>{p(!f),e.stopPropagation()},className:"inline-flex items-center justify-center p-2 rounded bg-secondary text-primary hover:text-accent hover:scale-105 transition-all duration-300 z-10","aria-label":f?"Show less":"Show more"},f?i.createElement(l.A,{size:18}):i.createElement(a.A,{size:18})),d&&i.createElement(Yo.A,{title:"Fullscreen"},i.createElement("button",{type:"button",onClick:()=>h(!0),className:"inline-flex items-center justify-center p-2 rounded bg-secondary text-primary hover:text-accent hover:scale-105 transition-all duration-300 z-10","aria-label":"Toggle fullscreen"},i.createElement(s.A,{size:18})))),m&&i.createElement("div",{className:"fixed inset-0 dark:bg-black/80 bg-black/10 z-50 flex items-center justify-center",onClick:()=>h(!1)},i.createElement("div",{className:"relative bg-primary scroll w-full h-full md:w-4/5 md:h-4/5 md:rounded-lg p-8 overflow-auto",style:{opacity:.95},onClick:e=>e.stopPropagation()},i.createElement(Yo.A,{title:"Close"},i.createElement("button",{onClick:()=>h(!1),className:"absolute top-4 right-4 p-2 rounded-full bg-black/50 hover:bg-black/70 text-primary transition-colors","aria-label":"Close fullscreen view"},i.createElement(c.A,{size:24}))),i.createElement("div",{className:`mt-8 text-base text-primary ${x}`},n?i.createElement("pre",{className:"whitespace-pre-wrap"},t):i.createElement(qo,{className:"text-primary",remarkPlugins:[kl]},t)))))}),Al=e=>{let{src:t,alt:n,onClose:o}=e;return i.createElement("div",{className:"fixed inset-0 bg-black/80 z-50 flex items-center justify-center",onClick:o},i.createElement("button",{onClick:o,className:"absolute top-4 right-4 p-2 rounded-full bg-black/50 hover:bg-black/70 text-white transition-colors","aria-label":"Close fullscreen image"},i.createElement(c.A,{size:24})),i.createElement("img",{src:t,alt:n,className:"max-h-[90vh] max-w-[90vw] object-contain rounded-lg",onClick:e=>e.stopPropagation()}))},El=e=>{let{src:t,alt:n,className:o=""}=e;const{0:r,1:l}=(0,i.useState)(!1);return i.createElement(i.Fragment,null,i.createElement("img",{src:t,alt:n,className:`${o} cursor-pointer rounded hover:opacity-90 transition-opacity`,onClick:()=>l(!0)}),r&&i.createElement(Al,{src:t,alt:n,onClose:()=>l(!1)}))};function Il(e){const t=new Date,n=new Date(e),o=t.getTime()-n.getTime(),r=Math.floor(o/1e3),i=Math.floor(r/60),l=Math.floor(i/60),a=Math.floor(l/24),s=Math.floor(a/30),c=Math.floor(a/365);return r<60?"just now":i<60?`${i} ${1===i?"minute":"minutes"} ago`:l<24?`${l} ${1===l?"hour":"hours"} ago`:a<30?`${a} ${1===a?"day":"days"} ago`:s<12?`${s} ${1===s?"month":"months"} ago`:`${c} ${1===c?"year":"years"} ago`}},7308:function(e,t,n){"use strict";n.d(t,{A:function(){return x}});var o=n(6540),r=n(6942),i=n.n(r),l=n(9155),a=n(2616),s=n(1320);var c=()=>{const[,e]=(0,s.Ay)(),[t]=(0,l.A)("Empty"),n=new a.Y(e.colorBgBase).toHsl().l<.5?{opacity:.65}:{};return o.createElement("svg",{style:n,width:"184",height:"152",viewBox:"0 0 184 152",xmlns:"http://www.w3.org/2000/svg"},o.createElement("title",null,(null==t?void 0:t.description)||"Empty"),o.createElement("g",{fill:"none",fillRule:"evenodd"},o.createElement("g",{transform:"translate(24 31.67)"},o.createElement("ellipse",{fillOpacity:".8",fill:"#F5F5F7",cx:"67.797",cy:"106.89",rx:"67.797",ry:"12.668"}),o.createElement("path",{d:"M122.034 69.674L98.109 40.229c-1.148-1.386-2.826-2.225-4.593-2.225h-51.44c-1.766 0-3.444.839-4.592 2.225L13.56 69.674v15.383h108.475V69.674z",fill:"#AEB8C2"}),o.createElement("path",{d:"M101.537 86.214L80.63 61.102c-1.001-1.207-2.507-1.867-4.048-1.867H31.724c-1.54 0-3.047.66-4.048 1.867L6.769 86.214v13.792h94.768V86.214z",fill:"url(#linearGradient-1)",transform:"translate(13.56)"}),o.createElement("path",{d:"M33.83 0h67.933a4 4 0 0 1 4 4v93.344a4 4 0 0 1-4 4H33.83a4 4 0 0 1-4-4V4a4 4 0 0 1 4-4z",fill:"#F5F5F7"}),o.createElement("path",{d:"M42.678 9.953h50.237a2 2 0 0 1 2 2V36.91a2 2 0 0 1-2 2H42.678a2 2 0 0 1-2-2V11.953a2 2 0 0 1 2-2zM42.94 49.767h49.713a2.262 2.262 0 1 1 0 4.524H42.94a2.262 2.262 0 0 1 0-4.524zM42.94 61.53h49.713a2.262 2.262 0 1 1 0 4.525H42.94a2.262 2.262 0 0 1 0-4.525zM121.813 105.032c-.775 3.071-3.497 5.36-6.735 5.36H20.515c-3.238 0-5.96-2.29-6.734-5.36a7.309 7.309 0 0 1-.222-1.79V69.675h26.318c2.907 0 5.25 2.448 5.25 5.42v.04c0 2.971 2.37 5.37 5.277 5.37h34.785c2.907 0 5.277-2.421 5.277-5.393V75.1c0-2.972 2.343-5.426 5.25-5.426h26.318v33.569c0 .617-.077 1.216-.221 1.789z",fill:"#DCE0E6"})),o.createElement("path",{d:"M149.121 33.292l-6.83 2.65a1 1 0 0 1-1.317-1.23l1.937-6.207c-2.589-2.944-4.109-6.534-4.109-10.408C138.802 8.102 148.92 0 161.402 0 173.881 0 184 8.102 184 18.097c0 9.995-10.118 18.097-22.599 18.097-4.528 0-8.744-1.066-12.28-2.902z",fill:"#DCE0E6"}),o.createElement("g",{transform:"translate(149.65 15.383)",fill:"#FFF"},o.createElement("ellipse",{cx:"20.654",cy:"3.167",rx:"2.849",ry:"2.815"}),o.createElement("path",{d:"M5.698 5.63H0L2.898.704zM9.259.704h4.985V5.63H9.259z"}))))};var u=()=>{const[,e]=(0,s.Ay)(),[t]=(0,l.A)("Empty"),{colorFill:n,colorFillTertiary:r,colorFillQuaternary:i,colorBgContainer:c}=e,{borderColor:u,shadowColor:d,contentColor:f}=(0,o.useMemo)(()=>({borderColor:new a.Y(n).onBackground(c).toHexString(),shadowColor:new a.Y(r).onBackground(c).toHexString(),contentColor:new a.Y(i).onBackground(c).toHexString()}),[n,r,i,c]);return o.createElement("svg",{width:"64",height:"41",viewBox:"0 0 64 41",xmlns:"http://www.w3.org/2000/svg"},o.createElement("title",null,(null==t?void 0:t.description)||"Empty"),o.createElement("g",{transform:"translate(0 1)",fill:"none",fillRule:"evenodd"},o.createElement("ellipse",{fill:d,cx:"32",cy:"33",rx:"32",ry:"7"}),o.createElement("g",{fillRule:"nonzero",stroke:u},o.createElement("path",{d:"M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"}),o.createElement("path",{d:"M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z",fill:f}))))},d=n(7358),f=n(4277);const p=e=>{const{componentCls:t,margin:n,marginXS:o,marginXL:r,fontSize:i,lineHeight:l}=e;return{[t]:{marginInline:o,fontSize:i,lineHeight:l,textAlign:"center",[`${t}-image`]:{height:e.emptyImgHeight,marginBottom:o,opacity:e.opacityImage,img:{height:"100%"},svg:{maxWidth:"100%",height:"100%",margin:"auto"}},[`${t}-description`]:{color:e.colorTextDescription},[`${t}-footer`]:{marginTop:n},"&-normal":{marginBlock:r,color:e.colorTextDescription,[`${t}-description`]:{color:e.colorTextDescription},[`${t}-image`]:{height:e.emptyImgHeightMD}},"&-small":{marginBlock:o,color:e.colorTextDescription,[`${t}-image`]:{height:e.emptyImgHeightSM}}}}};var m=(0,d.OF)("Empty",e=>{const{componentCls:t,controlHeightLG:n,calc:o}=e,r=(0,f.oX)(e,{emptyImgCls:`${t}-img`,emptyImgHeight:o(n).mul(2.5).equal(),emptyImgHeightMD:n,emptyImgHeightSM:o(n).mul(.875).equal()});return[p(r)]}),h=n(2279),g=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const v=o.createElement(c,null),b=o.createElement(u,null),y=e=>{const{className:t,rootClassName:n,prefixCls:r,image:a=v,description:s,children:c,imageStyle:u,style:d,classNames:f,styles:p}=e,y=g(e,["className","rootClassName","prefixCls","image","description","children","imageStyle","style","classNames","styles"]),{getPrefixCls:x,direction:w,className:C,style:k,classNames:$,styles:S}=(0,h.TP)("empty"),A=x("empty",r),[E,I,O]=m(A),[z]=(0,l.A)("Empty"),M=void 0!==s?s:null==z?void 0:z.description,P="string"==typeof M?M:"empty";let T=null;return T="string"==typeof a?o.createElement("img",{alt:P,src:a}):a,E(o.createElement("div",Object.assign({className:i()(I,O,A,C,{[`${A}-normal`]:a===b,[`${A}-rtl`]:"rtl"===w},t,n,$.root,null==f?void 0:f.root),style:Object.assign(Object.assign(Object.assign(Object.assign({},S.root),k),null==p?void 0:p.root),d)},y),o.createElement("div",{className:i()(`${A}-image`,$.image,null==f?void 0:f.image),style:Object.assign(Object.assign(Object.assign({},u),S.image),null==p?void 0:p.image)},T),M&&o.createElement("div",{className:i()(`${A}-description`,$.description,null==f?void 0:f.description),style:Object.assign(Object.assign({},S.description),null==p?void 0:p.description)},M),c&&o.createElement("div",{className:i()(`${A}-footer`,$.footer,null==f?void 0:f.footer),style:Object.assign(Object.assign({},S.footer),null==p?void 0:p.footer)},c)))};y.PRESENTED_IMAGE_DEFAULT=v,y.PRESENTED_IMAGE_SIMPLE=b;var x=y},7447:function(e,t,n){"use strict";n.d(t,{A:function(){return r}});var o=n(6540);function r(){const[,e]=o.useReducer(e=>e+1,0);return e}},7504:function(e,t,n){"use strict";n.d(t,{A:function(){return o}});const o=(0,n(1788).A)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},8e3:function(e,t,n){"use strict";n.d(t,{A:function(){return s}});var o=n(8168),r=n(6540),i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"}}]},name:"right",theme:"outlined"},l=n(7064),a=function(e,t){return r.createElement(l.A,(0,o.A)({},e,{ref:t,icon:i}))};var s=r.forwardRef(a)},8071:function(e,t,n){"use strict";n.d(t,{Dk:function(){return f},FY:function(){return g},cH:function(){return v}});var o=n(436),r=n(2187),i=n(5006),l=n(5905),a=n(8680),s=n(9077),c=n(4277),u=n(7358);function d(e){return{position:e,inset:0}}const f=e=>{const{componentCls:t,antCls:n}=e;return[{[`${t}-root`]:{[`${t}${n}-zoom-enter, ${t}${n}-zoom-appear`]:{transform:"none",opacity:0,animationDuration:e.motionDurationSlow,userSelect:"none"},[`${t}${n}-zoom-leave ${t}-content`]:{pointerEvents:"none"},[`${t}-mask`]:Object.assign(Object.assign({},d("fixed")),{zIndex:e.zIndexPopupBase,height:"100%",backgroundColor:e.colorBgMask,pointerEvents:"none",[`${t}-hidden`]:{display:"none"}}),[`${t}-wrap`]:Object.assign(Object.assign({},d("fixed")),{zIndex:e.zIndexPopupBase,overflow:"auto",outline:0,WebkitOverflowScrolling:"touch"})}},{[`${t}-root`]:(0,a.p9)(e)}]},p=e=>{const{componentCls:t}=e;return[{[`${t}-root`]:{[`${t}-wrap-rtl`]:{direction:"rtl"},[`${t}-centered`]:{textAlign:"center","&::before":{display:"inline-block",width:0,height:"100%",verticalAlign:"middle",content:'""'},[t]:{top:0,display:"inline-block",paddingBottom:0,textAlign:"start",verticalAlign:"middle"}},[`@media (max-width: ${e.screenSMMax}px)`]:{[t]:{maxWidth:"calc(100vw - 16px)",margin:`${(0,r.zA)(e.marginXS)} auto`},[`${t}-centered`]:{[t]:{flex:1}}}}},{[t]:Object.assign(Object.assign({},(0,l.dF)(e)),{pointerEvents:"none",position:"relative",top:100,width:"auto",maxWidth:`calc(100vw - ${(0,r.zA)(e.calc(e.margin).mul(2).equal())})`,margin:"0 auto",paddingBottom:e.paddingLG,[`${t}-title`]:{margin:0,color:e.titleColor,fontWeight:e.fontWeightStrong,fontSize:e.titleFontSize,lineHeight:e.titleLineHeight,wordWrap:"break-word"},[`${t}-content`]:{position:"relative",backgroundColor:e.contentBg,backgroundClip:"padding-box",border:0,borderRadius:e.borderRadiusLG,boxShadow:e.boxShadow,pointerEvents:"auto",padding:e.contentPadding},[`${t}-close`]:Object.assign({position:"absolute",top:e.calc(e.modalHeaderHeight).sub(e.modalCloseBtnSize).div(2).equal(),insetInlineEnd:e.calc(e.modalHeaderHeight).sub(e.modalCloseBtnSize).div(2).equal(),zIndex:e.calc(e.zIndexPopupBase).add(10).equal(),padding:0,color:e.modalCloseIconColor,fontWeight:e.fontWeightStrong,lineHeight:1,textDecoration:"none",background:"transparent",borderRadius:e.borderRadiusSM,width:e.modalCloseBtnSize,height:e.modalCloseBtnSize,border:0,outline:0,cursor:"pointer",transition:`color ${e.motionDurationMid}, background-color ${e.motionDurationMid}`,"&-x":{display:"flex",fontSize:e.fontSizeLG,fontStyle:"normal",lineHeight:(0,r.zA)(e.modalCloseBtnSize),justifyContent:"center",textTransform:"none",textRendering:"auto"},"&:disabled":{pointerEvents:"none"},"&:hover":{color:e.modalCloseIconHoverColor,backgroundColor:e.colorBgTextHover,textDecoration:"none"},"&:active":{backgroundColor:e.colorBgTextActive}},(0,l.K8)(e)),[`${t}-header`]:{color:e.colorText,background:e.headerBg,borderRadius:`${(0,r.zA)(e.borderRadiusLG)} ${(0,r.zA)(e.borderRadiusLG)} 0 0`,marginBottom:e.headerMarginBottom,padding:e.headerPadding,borderBottom:e.headerBorderBottom},[`${t}-body`]:{fontSize:e.fontSize,lineHeight:e.lineHeight,wordWrap:"break-word",padding:e.bodyPadding,[`${t}-body-skeleton`]:{width:"100%",height:"100%",display:"flex",justifyContent:"center",alignItems:"center",margin:`${(0,r.zA)(e.margin)} auto`}},[`${t}-footer`]:{textAlign:"end",background:e.footerBg,marginTop:e.footerMarginTop,padding:e.footerPadding,borderTop:e.footerBorderTop,borderRadius:e.footerBorderRadius,[`> ${e.antCls}-btn + ${e.antCls}-btn`]:{marginInlineStart:e.marginXS}},[`${t}-open`]:{overflow:"hidden"}})},{[`${t}-pure-panel`]:{top:"auto",padding:0,display:"flex",flexDirection:"column",[`${t}-content,\n          ${t}-body,\n          ${t}-confirm-body-wrapper`]:{display:"flex",flexDirection:"column",flex:"auto"},[`${t}-confirm-body`]:{marginBottom:"auto"}}}]},m=e=>{const{componentCls:t}=e;return{[`${t}-root`]:{[`${t}-wrap-rtl`]:{direction:"rtl",[`${t}-confirm-body`]:{direction:"rtl"}}}}},h=e=>{const{componentCls:t}=e,n=(0,i.i4)(e),l=Object.assign({},n);delete l.xs;const a=`--${t.replace(".","")}-`,s=Object.keys(l).map(e=>({[`@media (min-width: ${(0,r.zA)(l[e])})`]:{width:`var(${a}${e}-width)`}}));return{[`${t}-root`]:{[t]:[].concat((0,o.A)(Object.keys(n).map((e,t)=>{const o=Object.keys(n)[t-1];return o?{[`${a}${e}-width`]:`var(${a}${o}-width)`}:null})),[{width:`var(${a}xs-width)`}],(0,o.A)(s))}}},g=e=>{const t=e.padding,n=e.fontSizeHeading5,o=e.lineHeightHeading5;return(0,c.oX)(e,{modalHeaderHeight:e.calc(e.calc(o).mul(n).equal()).add(e.calc(t).mul(2).equal()).equal(),modalFooterBorderColorSplit:e.colorSplit,modalFooterBorderStyle:e.lineType,modalFooterBorderWidth:e.lineWidth,modalCloseIconColor:e.colorIcon,modalCloseIconHoverColor:e.colorIconHover,modalCloseBtnSize:e.controlHeight,modalConfirmIconSize:e.fontHeight,modalTitleHeight:e.calc(e.titleFontSize).mul(e.titleLineHeight).equal()})},v=e=>({footerBg:"transparent",headerBg:e.colorBgElevated,titleLineHeight:e.lineHeightHeading5,titleFontSize:e.fontSizeHeading5,contentBg:e.colorBgElevated,titleColor:e.colorTextHeading,contentPadding:e.wireframe?0:`${(0,r.zA)(e.paddingMD)} ${(0,r.zA)(e.paddingContentHorizontalLG)}`,headerPadding:e.wireframe?`${(0,r.zA)(e.padding)} ${(0,r.zA)(e.paddingLG)}`:0,headerBorderBottom:e.wireframe?`${(0,r.zA)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`:"none",headerMarginBottom:e.wireframe?0:e.marginXS,bodyPadding:e.wireframe?e.paddingLG:0,footerPadding:e.wireframe?`${(0,r.zA)(e.paddingXS)} ${(0,r.zA)(e.padding)}`:0,footerBorderTop:e.wireframe?`${(0,r.zA)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`:"none",footerBorderRadius:e.wireframe?`0 0 ${(0,r.zA)(e.borderRadiusLG)} ${(0,r.zA)(e.borderRadiusLG)}`:0,footerMarginTop:e.wireframe?0:e.marginSM,confirmBodyPadding:e.wireframe?`${(0,r.zA)(2*e.padding)} ${(0,r.zA)(2*e.padding)} ${(0,r.zA)(e.paddingLG)}`:0,confirmIconMarginInlineEnd:e.wireframe?e.margin:e.marginSM,confirmBtnsMarginTop:e.wireframe?e.marginLG:e.marginSM});t.Ay=(0,u.OF)("Modal",e=>{const t=g(e);return[p(t),m(t),f(t),(0,s.aB)(t,"zoom"),h(t)]},v,{unitless:{titleLineHeight:!0}})},8414:function(e,t,n){"use strict";n.d(t,{P:function(){return $},A:function(){return E}});var o=n(6540),r=n(8168),i={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 192H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM104 228a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"bars",theme:"outlined"},l=n(7064),a=function(e,t){return o.createElement(l.A,(0,r.A)({},e,{ref:t,icon:i}))};var s=o.forwardRef(a),c=n(329),u=n(8e3),d=n(6942),f=n.n(d),p=n(9853),m=n(4849),h=n(2279),g=n(4129),v=n(2187),b=n(4440),y=n(7358);const x=e=>{const{componentCls:t,siderBg:n,motionDurationMid:o,motionDurationSlow:r,antCls:i,triggerHeight:l,triggerColor:a,triggerBg:s,headerHeight:c,zeroTriggerWidth:u,zeroTriggerHeight:d,borderRadiusLG:f,lightSiderBg:p,lightTriggerColor:m,lightTriggerBg:h,bodyBg:g}=e;return{[t]:{position:"relative",minWidth:0,background:n,transition:`all ${o}, background 0s`,"&-has-trigger":{paddingBottom:l},"&-right":{order:1},[`${t}-children`]:{height:"100%",marginTop:-.1,paddingTop:.1,[`${i}-menu${i}-menu-inline-collapsed`]:{width:"auto"}},[`&-zero-width ${t}-children`]:{overflow:"hidden"},[`${t}-trigger`]:{position:"fixed",bottom:0,zIndex:1,height:l,color:a,lineHeight:(0,v.zA)(l),textAlign:"center",background:s,cursor:"pointer",transition:`all ${o}`},[`${t}-zero-width-trigger`]:{position:"absolute",top:c,insetInlineEnd:e.calc(u).mul(-1).equal(),zIndex:1,width:u,height:d,color:a,fontSize:e.fontSizeXL,display:"flex",alignItems:"center",justifyContent:"center",background:n,borderRadius:`0 ${(0,v.zA)(f)} ${(0,v.zA)(f)} 0`,cursor:"pointer",transition:`background ${r} ease`,"&::after":{position:"absolute",inset:0,background:"transparent",transition:`all ${r}`,content:'""'},"&:hover::after":{background:"rgba(255, 255, 255, 0.2)"},"&-right":{insetInlineStart:e.calc(u).mul(-1).equal(),borderRadius:`${(0,v.zA)(f)} 0 0 ${(0,v.zA)(f)}`}},"&-light":{background:p,[`${t}-trigger`]:{color:m,background:h},[`${t}-zero-width-trigger`]:{color:m,background:h,border:`1px solid ${g}`,borderInlineStart:0}}}}};var w=(0,y.OF)(["Layout","Sider"],e=>[x(e)],b.cH,{deprecatedTokens:b.lB}),C=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const k={xs:"479.98px",sm:"575.98px",md:"767.98px",lg:"991.98px",xl:"1199.98px",xxl:"1599.98px"},$=o.createContext({}),S=(()=>{let e=0;return(t="")=>(e+=1,`${t}${e}`)})(),A=o.forwardRef((e,t)=>{const{prefixCls:n,className:r,trigger:i,children:l,defaultCollapsed:a=!1,theme:d="dark",style:v={},collapsible:b=!1,reverseArrow:y=!1,width:x=200,collapsedWidth:A=80,zeroWidthTriggerStyle:E,breakpoint:I,onCollapse:O,onBreakpoint:z}=e,M=C(e,["prefixCls","className","trigger","children","defaultCollapsed","theme","style","collapsible","reverseArrow","width","collapsedWidth","zeroWidthTriggerStyle","breakpoint","onCollapse","onBreakpoint"]),{siderHook:P}=(0,o.useContext)(g.M),[T,j]=(0,o.useState)("collapsed"in e?e.collapsed:a),[N,B]=(0,o.useState)(!1);(0,o.useEffect)(()=>{"collapsed"in e&&j(e.collapsed)},[e.collapsed]);const R=(t,n)=>{"collapsed"in e||j(t),null==O||O(t,n)},{getPrefixCls:D,direction:L}=(0,o.useContext)(h.QO),H=D("layout-sider",n),[F,_,W]=w(H),V=(0,o.useRef)(null);V.current=e=>{B(e.matches),null==z||z(e.matches),T!==e.matches&&R(e.matches,"responsive")},(0,o.useEffect)(()=>{function e(e){var t;return null===(t=V.current)||void 0===t?void 0:t.call(V,e)}let t;return void 0!==(null===window||void 0===window?void 0:window.matchMedia)&&I&&I in k&&(t=window.matchMedia(`screen and (max-width: ${k[I]})`),(0,m.e)(t,e),e(t)),()=>{(0,m.p)(t,e)}},[I]),(0,o.useEffect)(()=>{const e=S("ant-sider-");return P.addSider(e),()=>P.removeSider(e)},[]);const q=()=>{R(!T,"clickTrigger")},U=(0,p.A)(M,["collapsed"]),X=T?A:x,G=(K=X,!Number.isNaN(Number.parseFloat(K))&&isFinite(K)?`${X}px`:String(X));var K;const Y=0===parseFloat(String(A||0))?o.createElement("span",{onClick:q,className:f()(`${H}-zero-width-trigger`,`${H}-zero-width-trigger-${y?"right":"left"}`),style:E},i||o.createElement(s,null)):null,Q="rtl"===L==!y,Z={expanded:Q?o.createElement(u.A,null):o.createElement(c.A,null),collapsed:Q?o.createElement(c.A,null):o.createElement(u.A,null)}[T?"collapsed":"expanded"],J=null!==i?Y||o.createElement("div",{className:`${H}-trigger`,onClick:q,style:{width:G}},i||Z):null,ee=Object.assign(Object.assign({},v),{flex:`0 0 ${G}`,maxWidth:G,minWidth:G,width:G}),te=f()(H,`${H}-${d}`,{[`${H}-collapsed`]:!!T,[`${H}-has-trigger`]:b&&null!==i&&!Y,[`${H}-below`]:!!N,[`${H}-zero-width`]:0===parseFloat(G)},r,_,W),ne=o.useMemo(()=>({siderCollapsed:T}),[T]);return F(o.createElement($.Provider,{value:ne},o.createElement("aside",Object.assign({className:te},U,{style:ee,ref:t}),o.createElement("div",{className:`${H}-children`},l),b||N&&Y?J:null)))});var E=A},8557:function(e,t,n){"use strict";n.d(t,{f:function(){return a}});var o=n(6540),r=n(6956);function i(){}const l=o.createContext({add:i,remove:i});function a(e){const t=o.useContext(l),n=o.useRef(null);return(0,r.A)(o=>{if(o){const r=e?o.querySelector(e):o;t.add(r),n.current=r}else t.remove(n.current)})}},8603:function(e,t,n){"use strict";n.d(t,{A:function(){return g}});var o=n(9106),r=n(6540),i=n(2318),l=n(6942),a=n.n(l),s=n(2941),c=n(2279),u=n(2702),d=n(6327),f=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const p=e=>{const{getPopupContainer:t,getPrefixCls:n,direction:l}=r.useContext(c.QO),{prefixCls:p,type:m="default",danger:h,disabled:g,loading:v,onClick:b,htmlType:y,children:x,className:w,menu:C,arrow:k,autoFocus:$,overlay:S,trigger:A,align:E,open:I,onOpenChange:O,placement:z,getPopupContainer:M,href:P,icon:T=r.createElement(i.A,null),title:j,buttonsRender:N=e=>e,mouseEnterDelay:B,mouseLeaveDelay:R,overlayClassName:D,overlayStyle:L,destroyOnHidden:H,destroyPopupOnHide:F,dropdownRender:_,popupRender:W}=e,V=f(e,["prefixCls","type","danger","disabled","loading","onClick","htmlType","children","className","menu","arrow","autoFocus","overlay","trigger","align","open","onOpenChange","placement","getPopupContainer","href","icon","title","buttonsRender","mouseEnterDelay","mouseLeaveDelay","overlayClassName","overlayStyle","destroyOnHidden","destroyPopupOnHide","dropdownRender","popupRender"]),q=n("dropdown",p),U=`${q}-button`,X={menu:C,arrow:k,autoFocus:$,align:E,disabled:g,trigger:g?[]:A,onOpenChange:O,getPopupContainer:M||t,mouseEnterDelay:B,mouseLeaveDelay:R,overlayClassName:D,overlayStyle:L,destroyOnHidden:H,popupRender:W||_},{compactSize:G,compactItemClassnames:K}=(0,d.RQ)(q,l),Y=a()(U,K,w);"destroyPopupOnHide"in e&&(X.destroyPopupOnHide=F),"overlay"in e&&(X.overlay=S),"open"in e&&(X.open=I),X.placement="placement"in e?z:"rtl"===l?"bottomLeft":"bottomRight";const Q=r.createElement(s.Ay,{type:m,danger:h,disabled:g,loading:v,onClick:b,htmlType:y,href:P,title:j},x),Z=r.createElement(s.Ay,{type:m,danger:h,icon:T}),[J,ee]=N([Q,Z]);return r.createElement(u.A.Compact,Object.assign({className:Y,size:G,block:!0},V),J,r.createElement(o.A,Object.assign({},X),ee))};p.__ANT_BUTTON=!0;var m=p;const h=o.A;h.Button=m;var g=h},8680:function(e,t,n){"use strict";n.d(t,{p9:function(){return a}});var o=n(2187),r=n(4980);const i=new o.Mo("antFadeIn",{"0%":{opacity:0},"100%":{opacity:1}}),l=new o.Mo("antFadeOut",{"0%":{opacity:1},"100%":{opacity:0}}),a=(e,t=!1)=>{const{antCls:n}=e,o=`${n}-fade`,a=t?"&":"";return[(0,r.b)(o,i,l,e.motionDurationMid,t),{[`\n        ${a}${o}-enter,\n        ${a}${o}-appear\n      `]:{opacity:0,animationTimingFunction:"linear"},[`${a}${o}-leave`]:{animationTimingFunction:"linear"}}]}},8917:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.camelCase=void 0;var n=/^--[a-zA-Z0-9_-]+$/,o=/-([a-z])/g,r=/^[^-]+$/,i=/^-(webkit|moz|ms|o|khtml)-/,l=/^-(ms)-/,a=function(e,t){return t.toUpperCase()},s=function(e,t){return"".concat(t,"-")};t.camelCase=function(e,t){return void 0===t&&(t={}),function(e){return!e||r.test(e)||n.test(e)}(e)?e:(e=e.toLowerCase(),(e=t.reactCompat?e.replace(l,s):e.replace(i,s)).replace(o,a))}},9106:function(e,t,n){"use strict";n.d(t,{A:function(){return ye}});var o=n(6540),r=n(329),i=n(8e3),l=n(6942),a=n.n(l),s=n(3497),c=n(6956),u=n(2533),d=n(9853),f=n(275);var p=e=>"object"!=typeof e&&"function"!=typeof e||null===e,m=n(3257),h=n(3425),g=n(682),v=n(8877),b=n(235),y=n(2279),x=n(934),w=n(8810),C=n(8414),k=n(2318),$=n(3723);var S=(0,o.createContext)({prefixCls:"",firstLevel:!0,inlineCollapsed:!1}),A=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};var E=e=>{const{prefixCls:t,className:n,dashed:r}=e,i=A(e,["prefixCls","className","dashed"]),{getPrefixCls:l}=o.useContext(y.QO),s=l("menu",t),c=a()({[`${s}-item-divider-dashed`]:!!r},n);return o.createElement(w.cG,Object.assign({className:c},i))},I=n(2546),O=n(367);var z=e=>{var t;const{className:n,children:r,icon:i,title:l,danger:s,extra:c}=e,{prefixCls:u,firstLevel:f,direction:p,disableMenuItemTitleTooltip:m,inlineCollapsed:h}=o.useContext(S),{siderCollapsed:v}=o.useContext(C.P);let b=l;void 0===l?b=f?r:"":!1===l&&(b="");const y={title:b};v||h||(y.title=null,y.open=!1);const x=(0,I.A)(r).length;let k=o.createElement(w.q7,Object.assign({},(0,d.A)(e,["title","icon","danger"]),{className:a()({[`${u}-item-danger`]:s,[`${u}-item-only-child`]:1===(i?x+1:x)},n),title:"string"==typeof l?l:void 0}),(0,g.Ob)(i,{className:a()(o.isValidElement(i)?null===(t=i.props)||void 0===t?void 0:t.className:void 0,`${u}-item-icon`)}),(e=>{const t=null==r?void 0:r[0],n=o.createElement("span",{className:a()(`${u}-title-content`,{[`${u}-title-content-with-extra`]:!!c||0===c})},r);return(!i||o.isValidElement(r)&&"span"===r.type)&&r&&e&&f&&"string"==typeof t?o.createElement("div",{className:`${u}-inline-collapsed-noicon`},t.charAt(0)):n})(h));return m||(k=o.createElement(O.A,Object.assign({},y,{placement:"rtl"===p?"left":"right",classNames:{root:`${u}-inline-collapsed-tooltip`}}),k)),k},M=n(8719),P=n(2897),T=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const j=o.createContext(null),N=o.forwardRef((e,t)=>{const{children:n}=e,r=T(e,["children"]),i=o.useContext(j),l=o.useMemo(()=>Object.assign(Object.assign({},i),r),[i,r.prefixCls,r.mode,r.selectable,r.rootClassName]),a=(0,M.H3)(n),s=(0,M.xK)(t,a?(0,M.A9)(n):null);return o.createElement(j.Provider,{value:l},o.createElement(P.A,{space:!0},a?o.cloneElement(n,{ref:s}):n))});var B=j,R=n(2187),D=n(2616),L=n(5905),H=n(977),F=n(3561),_=n(9077),W=n(7358),V=n(4277);var q=e=>{const{componentCls:t,motionDurationSlow:n,horizontalLineHeight:o,colorSplit:r,lineWidth:i,lineType:l,itemPaddingInline:a}=e;return{[`${t}-horizontal`]:{lineHeight:o,border:0,borderBottom:`${(0,R.zA)(i)} ${l} ${r}`,boxShadow:"none","&::after":{display:"block",clear:"both",height:0,content:'"\\20"'},[`${t}-item, ${t}-submenu`]:{position:"relative",display:"inline-block",verticalAlign:"bottom",paddingInline:a},[`> ${t}-item:hover,\n        > ${t}-item-active,\n        > ${t}-submenu ${t}-submenu-title:hover`]:{backgroundColor:"transparent"},[`${t}-item, ${t}-submenu-title`]:{transition:[`border-color ${n}`,`background ${n}`].join(",")},[`${t}-submenu-arrow`]:{display:"none"}}}};var U=({componentCls:e,menuArrowOffset:t,calc:n})=>({[`${e}-rtl`]:{direction:"rtl"},[`${e}-submenu-rtl`]:{transformOrigin:"100% 0"},[`${e}-rtl${e}-vertical,\n    ${e}-submenu-rtl ${e}-vertical`]:{[`${e}-submenu-arrow`]:{"&::before":{transform:`rotate(-45deg) translateY(${(0,R.zA)(n(t).mul(-1).equal())})`},"&::after":{transform:`rotate(45deg) translateY(${(0,R.zA)(t)})`}}}});const X=e=>Object.assign({},(0,L.jk)(e));var G=(e,t)=>{const{componentCls:n,itemColor:o,itemSelectedColor:r,subMenuItemSelectedColor:i,groupTitleColor:l,itemBg:a,subMenuItemBg:s,itemSelectedBg:c,activeBarHeight:u,activeBarWidth:d,activeBarBorderWidth:f,motionDurationSlow:p,motionEaseInOut:m,motionEaseOut:h,itemPaddingInline:g,motionDurationMid:v,itemHoverColor:b,lineType:y,colorSplit:x,itemDisabledColor:w,dangerItemColor:C,dangerItemHoverColor:k,dangerItemSelectedColor:$,dangerItemActiveBg:S,dangerItemSelectedBg:A,popupBg:E,itemHoverBg:I,itemActiveBg:O,menuSubMenuBg:z,horizontalItemSelectedColor:M,horizontalItemSelectedBg:P,horizontalItemBorderRadius:T,horizontalItemHoverBg:j}=e;return{[`${n}-${t}, ${n}-${t} > ${n}`]:{color:o,background:a,[`&${n}-root:focus-visible`]:Object.assign({},X(e)),[`${n}-item`]:{"&-group-title, &-extra":{color:l}},[`${n}-submenu-selected > ${n}-submenu-title`]:{color:i},[`${n}-item, ${n}-submenu-title`]:{color:o,[`&:not(${n}-item-disabled):focus-visible`]:Object.assign({},X(e))},[`${n}-item-disabled, ${n}-submenu-disabled`]:{color:`${w} !important`},[`${n}-item:not(${n}-item-selected):not(${n}-submenu-selected)`]:{[`&:hover, > ${n}-submenu-title:hover`]:{color:b}},[`&:not(${n}-horizontal)`]:{[`${n}-item:not(${n}-item-selected)`]:{"&:hover":{backgroundColor:I},"&:active":{backgroundColor:O}},[`${n}-submenu-title`]:{"&:hover":{backgroundColor:I},"&:active":{backgroundColor:O}}},[`${n}-item-danger`]:{color:C,[`&${n}-item:hover`]:{[`&:not(${n}-item-selected):not(${n}-submenu-selected)`]:{color:k}},[`&${n}-item:active`]:{background:S}},[`${n}-item a`]:{"&, &:hover":{color:"inherit"}},[`${n}-item-selected`]:{color:r,[`&${n}-item-danger`]:{color:$},"a, a:hover":{color:"inherit"}},[`& ${n}-item-selected`]:{backgroundColor:c,[`&${n}-item-danger`]:{backgroundColor:A}},[`&${n}-submenu > ${n}`]:{backgroundColor:z},[`&${n}-popup > ${n}`]:{backgroundColor:E},[`&${n}-submenu-popup > ${n}`]:{backgroundColor:E},[`&${n}-horizontal`]:Object.assign(Object.assign({},"dark"===t?{borderBottom:0}:{}),{[`> ${n}-item, > ${n}-submenu`]:{top:f,marginTop:e.calc(f).mul(-1).equal(),marginBottom:0,borderRadius:T,"&::after":{position:"absolute",insetInline:g,bottom:0,borderBottom:`${(0,R.zA)(u)} solid transparent`,transition:`border-color ${p} ${m}`,content:'""'},"&:hover, &-active, &-open":{background:j,"&::after":{borderBottomWidth:u,borderBottomColor:M}},"&-selected":{color:M,backgroundColor:P,"&:hover":{backgroundColor:P},"&::after":{borderBottomWidth:u,borderBottomColor:M}}}}),[`&${n}-root`]:{[`&${n}-inline, &${n}-vertical`]:{borderInlineEnd:`${(0,R.zA)(f)} ${y} ${x}`}},[`&${n}-inline`]:{[`${n}-sub${n}-inline`]:{background:s},[`${n}-item`]:{position:"relative","&::after":{position:"absolute",insetBlock:0,insetInlineEnd:0,borderInlineEnd:`${(0,R.zA)(d)} solid ${r}`,transform:"scaleY(0.0001)",opacity:0,transition:[`transform ${v} ${h}`,`opacity ${v} ${h}`].join(","),content:'""'},[`&${n}-item-danger`]:{"&::after":{borderInlineEndColor:$}}},[`${n}-selected, ${n}-item-selected`]:{"&::after":{transform:"scaleY(1)",opacity:1,transition:[`transform ${v} ${m}`,`opacity ${v} ${m}`].join(",")}}}}}};const K=e=>{const{componentCls:t,itemHeight:n,itemMarginInline:o,padding:r,menuArrowSize:i,marginXS:l,itemMarginBlock:a,itemWidth:s,itemPaddingInline:c}=e,u=e.calc(i).add(r).add(l).equal();return{[`${t}-item`]:{position:"relative",overflow:"hidden"},[`${t}-item, ${t}-submenu-title`]:{height:n,lineHeight:(0,R.zA)(n),paddingInline:c,overflow:"hidden",textOverflow:"ellipsis",marginInline:o,marginBlock:a,width:s},[`> ${t}-item,\n            > ${t}-submenu > ${t}-submenu-title`]:{height:n,lineHeight:(0,R.zA)(n)},[`${t}-item-group-list ${t}-submenu-title,\n            ${t}-submenu-title`]:{paddingInlineEnd:u}}};var Y=e=>{const{componentCls:t,iconCls:n,itemHeight:o,colorTextLightSolid:r,dropdownWidth:i,controlHeightLG:l,motionEaseOut:a,paddingXL:s,itemMarginInline:c,fontSizeLG:u,motionDurationFast:d,motionDurationSlow:f,paddingXS:p,boxShadowSecondary:m,collapsedWidth:h,collapsedIconSize:g}=e,v={height:o,lineHeight:(0,R.zA)(o),listStylePosition:"inside",listStyleType:"disc"};return[{[t]:{"&-inline, &-vertical":Object.assign({[`&${t}-root`]:{boxShadow:"none"}},K(e))},[`${t}-submenu-popup`]:{[`${t}-vertical`]:Object.assign(Object.assign({},K(e)),{boxShadow:m})}},{[`${t}-submenu-popup ${t}-vertical${t}-sub`]:{minWidth:i,maxHeight:`calc(100vh - ${(0,R.zA)(e.calc(l).mul(2.5).equal())})`,padding:"0",overflow:"hidden",borderInlineEnd:0,"&:not([class*='-active'])":{overflowX:"hidden",overflowY:"auto"}}},{[`${t}-inline`]:{width:"100%",[`&${t}-root`]:{[`${t}-item, ${t}-submenu-title`]:{display:"flex",alignItems:"center",transition:[`border-color ${f}`,`background ${f}`,`padding ${d} ${a}`].join(","),[`> ${t}-title-content`]:{flex:"auto",minWidth:0,overflow:"hidden",textOverflow:"ellipsis"},"> *":{flex:"none"}}},[`${t}-sub${t}-inline`]:{padding:0,border:0,borderRadius:0,boxShadow:"none",[`& > ${t}-submenu > ${t}-submenu-title`]:v,[`& ${t}-item-group-title`]:{paddingInlineStart:s}},[`${t}-item`]:v}},{[`${t}-inline-collapsed`]:{width:h,[`&${t}-root`]:{[`${t}-item, ${t}-submenu ${t}-submenu-title`]:{[`> ${t}-inline-collapsed-noicon`]:{fontSize:u,textAlign:"center"}}},[`> ${t}-item,\n          > ${t}-item-group > ${t}-item-group-list > ${t}-item,\n          > ${t}-item-group > ${t}-item-group-list > ${t}-submenu > ${t}-submenu-title,\n          > ${t}-submenu > ${t}-submenu-title`]:{insetInlineStart:0,paddingInline:`calc(50% - ${(0,R.zA)(e.calc(g).div(2).equal())} - ${(0,R.zA)(c)})`,textOverflow:"clip",[`\n            ${t}-submenu-arrow,\n            ${t}-submenu-expand-icon\n          `]:{opacity:0},[`${t}-item-icon, ${n}`]:{margin:0,fontSize:g,lineHeight:(0,R.zA)(o),"+ span":{display:"inline-block",opacity:0}}},[`${t}-item-icon, ${n}`]:{display:"inline-block"},"&-tooltip":{pointerEvents:"none",[`${t}-item-icon, ${n}`]:{display:"none"},"a, a:hover":{color:r}},[`${t}-item-group-title`]:Object.assign(Object.assign({},L.L9),{paddingInline:p})}}]};const Q=e=>{const{componentCls:t,motionDurationSlow:n,motionDurationMid:o,motionEaseInOut:r,motionEaseOut:i,iconCls:l,iconSize:a,iconMarginInlineEnd:s}=e;return{[`${t}-item, ${t}-submenu-title`]:{position:"relative",display:"block",margin:0,whiteSpace:"nowrap",cursor:"pointer",transition:[`border-color ${n}`,`background ${n}`,`padding calc(${n} + 0.1s) ${r}`].join(","),[`${t}-item-icon, ${l}`]:{minWidth:a,fontSize:a,transition:[`font-size ${o} ${i}`,`margin ${n} ${r}`,`color ${n}`].join(","),"+ span":{marginInlineStart:s,opacity:1,transition:[`opacity ${n} ${r}`,`margin ${n}`,`color ${n}`].join(",")}},[`${t}-item-icon`]:Object.assign({},(0,L.Nk)()),[`&${t}-item-only-child`]:{[`> ${l}, > ${t}-item-icon`]:{marginInlineEnd:0}}},[`${t}-item-disabled, ${t}-submenu-disabled`]:{background:"none !important",cursor:"not-allowed","&::after":{borderColor:"transparent !important"},a:{color:"inherit !important",cursor:"not-allowed",pointerEvents:"none"},[`> ${t}-submenu-title`]:{color:"inherit !important",cursor:"not-allowed"}}}},Z=e=>{const{componentCls:t,motionDurationSlow:n,motionEaseInOut:o,borderRadius:r,menuArrowSize:i,menuArrowOffset:l}=e;return{[`${t}-submenu`]:{"&-expand-icon, &-arrow":{position:"absolute",top:"50%",insetInlineEnd:e.margin,width:i,color:"currentcolor",transform:"translateY(-50%)",transition:`transform ${n} ${o}, opacity ${n}`},"&-arrow":{"&::before, &::after":{position:"absolute",width:e.calc(i).mul(.6).equal(),height:e.calc(i).mul(.15).equal(),backgroundColor:"currentcolor",borderRadius:r,transition:[`background ${n} ${o}`,`transform ${n} ${o}`,`top ${n} ${o}`,`color ${n} ${o}`].join(","),content:'""'},"&::before":{transform:`rotate(45deg) translateY(${(0,R.zA)(e.calc(l).mul(-1).equal())})`},"&::after":{transform:`rotate(-45deg) translateY(${(0,R.zA)(l)})`}}}}},J=e=>{const{antCls:t,componentCls:n,fontSize:o,motionDurationSlow:r,motionDurationMid:i,motionEaseInOut:l,paddingXS:a,padding:s,colorSplit:c,lineWidth:u,zIndexPopup:d,borderRadiusLG:f,subMenuItemBorderRadius:p,menuArrowSize:m,menuArrowOffset:h,lineType:g,groupTitleLineHeight:v,groupTitleFontSize:b}=e;return[{"":{[n]:Object.assign(Object.assign({},(0,L.t6)()),{"&-hidden":{display:"none"}})},[`${n}-submenu-hidden`]:{display:"none"}},{[n]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},(0,L.dF)(e)),(0,L.t6)()),{marginBottom:0,paddingInlineStart:0,fontSize:o,lineHeight:0,listStyle:"none",outline:"none",transition:`width ${r} cubic-bezier(0.2, 0, 0, 1) 0s`,"ul, ol":{margin:0,padding:0,listStyle:"none"},"&-overflow":{display:"flex",[`${n}-item`]:{flex:"none"}},[`${n}-item, ${n}-submenu, ${n}-submenu-title`]:{borderRadius:e.itemBorderRadius},[`${n}-item-group-title`]:{padding:`${(0,R.zA)(a)} ${(0,R.zA)(s)}`,fontSize:b,lineHeight:v,transition:`all ${r}`},[`&-horizontal ${n}-submenu`]:{transition:[`border-color ${r} ${l}`,`background ${r} ${l}`].join(",")},[`${n}-submenu, ${n}-submenu-inline`]:{transition:[`border-color ${r} ${l}`,`background ${r} ${l}`,`padding ${i} ${l}`].join(",")},[`${n}-submenu ${n}-sub`]:{cursor:"initial",transition:[`background ${r} ${l}`,`padding ${r} ${l}`].join(",")},[`${n}-title-content`]:{transition:`color ${r}`,"&-with-extra":{display:"inline-flex",alignItems:"center",width:"100%"},[`> ${t}-typography-ellipsis-single-line`]:{display:"inline",verticalAlign:"unset"},[`${n}-item-extra`]:{marginInlineStart:"auto",paddingInlineStart:e.padding}},[`${n}-item a`]:{"&::before":{position:"absolute",inset:0,backgroundColor:"transparent",content:'""'}},[`${n}-item-divider`]:{overflow:"hidden",lineHeight:0,borderColor:c,borderStyle:g,borderWidth:0,borderTopWidth:u,marginBlock:u,padding:0,"&-dashed":{borderStyle:"dashed"}}}),Q(e)),{[`${n}-item-group`]:{[`${n}-item-group-list`]:{margin:0,padding:0,[`${n}-item, ${n}-submenu-title`]:{paddingInline:`${(0,R.zA)(e.calc(o).mul(2).equal())} ${(0,R.zA)(s)}`}}},"&-submenu":{"&-popup":{position:"absolute",zIndex:d,borderRadius:f,boxShadow:"none",transformOrigin:"0 0",[`&${n}-submenu`]:{background:"transparent"},"&::before":{position:"absolute",inset:0,zIndex:-1,width:"100%",height:"100%",opacity:0,content:'""'},[`> ${n}`]:Object.assign(Object.assign(Object.assign({borderRadius:f},Q(e)),Z(e)),{[`${n}-item, ${n}-submenu > ${n}-submenu-title`]:{borderRadius:p},[`${n}-submenu-title::after`]:{transition:`transform ${r} ${l}`}})},"\n          &-placement-leftTop,\n          &-placement-bottomRight,\n          ":{transformOrigin:"100% 0"},"\n          &-placement-leftBottom,\n          &-placement-topRight,\n          ":{transformOrigin:"100% 100%"},"\n          &-placement-rightBottom,\n          &-placement-topLeft,\n          ":{transformOrigin:"0 100%"},"\n          &-placement-bottomLeft,\n          &-placement-rightTop,\n          ":{transformOrigin:"0 0"},"\n          &-placement-leftTop,\n          &-placement-leftBottom\n          ":{paddingInlineEnd:e.paddingXS},"\n          &-placement-rightTop,\n          &-placement-rightBottom\n          ":{paddingInlineStart:e.paddingXS},"\n          &-placement-topRight,\n          &-placement-topLeft\n          ":{paddingBottom:e.paddingXS},"\n          &-placement-bottomRight,\n          &-placement-bottomLeft\n          ":{paddingTop:e.paddingXS}}}),Z(e)),{[`&-inline-collapsed ${n}-submenu-arrow,\n        &-inline ${n}-submenu-arrow`]:{"&::before":{transform:`rotate(-45deg) translateX(${(0,R.zA)(h)})`},"&::after":{transform:`rotate(45deg) translateX(${(0,R.zA)(e.calc(h).mul(-1).equal())})`}},[`${n}-submenu-open${n}-submenu-inline > ${n}-submenu-title > ${n}-submenu-arrow`]:{transform:`translateY(${(0,R.zA)(e.calc(m).mul(.2).mul(-1).equal())})`,"&::after":{transform:`rotate(-45deg) translateX(${(0,R.zA)(e.calc(h).mul(-1).equal())})`},"&::before":{transform:`rotate(45deg) translateX(${(0,R.zA)(h)})`}}})},{[`${t}-layout-header`]:{[n]:{lineHeight:"inherit"}}}]},ee=e=>{var t,n,o;const{colorPrimary:r,colorError:i,colorTextDisabled:l,colorErrorBg:a,colorText:s,colorTextDescription:c,colorBgContainer:u,colorFillAlter:d,colorFillContent:f,lineWidth:p,lineWidthBold:m,controlItemBgActive:h,colorBgTextHover:g,controlHeightLG:v,lineHeight:b,colorBgElevated:y,marginXXS:x,padding:w,fontSize:C,controlHeightSM:k,fontSizeLG:$,colorTextLightSolid:S,colorErrorHover:A}=e,E=null!==(t=e.activeBarWidth)&&void 0!==t?t:0,I=null!==(n=e.activeBarBorderWidth)&&void 0!==n?n:p,O=null!==(o=e.itemMarginInline)&&void 0!==o?o:e.marginXXS,z=new D.Y(S).setA(.65).toRgbString();return{dropdownWidth:160,zIndexPopup:e.zIndexPopupBase+50,radiusItem:e.borderRadiusLG,itemBorderRadius:e.borderRadiusLG,radiusSubMenuItem:e.borderRadiusSM,subMenuItemBorderRadius:e.borderRadiusSM,colorItemText:s,itemColor:s,colorItemTextHover:s,itemHoverColor:s,colorItemTextHoverHorizontal:r,horizontalItemHoverColor:r,colorGroupTitle:c,groupTitleColor:c,colorItemTextSelected:r,itemSelectedColor:r,subMenuItemSelectedColor:r,colorItemTextSelectedHorizontal:r,horizontalItemSelectedColor:r,colorItemBg:u,itemBg:u,colorItemBgHover:g,itemHoverBg:g,colorItemBgActive:f,itemActiveBg:h,colorSubItemBg:d,subMenuItemBg:d,colorItemBgSelected:h,itemSelectedBg:h,colorItemBgSelectedHorizontal:"transparent",horizontalItemSelectedBg:"transparent",colorActiveBarWidth:0,activeBarWidth:E,colorActiveBarHeight:m,activeBarHeight:m,colorActiveBarBorderSize:p,activeBarBorderWidth:I,colorItemTextDisabled:l,itemDisabledColor:l,colorDangerItemText:i,dangerItemColor:i,colorDangerItemTextHover:i,dangerItemHoverColor:i,colorDangerItemTextSelected:i,dangerItemSelectedColor:i,colorDangerItemBgActive:a,dangerItemActiveBg:a,colorDangerItemBgSelected:a,dangerItemSelectedBg:a,itemMarginInline:O,horizontalItemBorderRadius:0,horizontalItemHoverBg:"transparent",itemHeight:v,groupTitleLineHeight:b,collapsedWidth:2*v,popupBg:y,itemMarginBlock:x,itemPaddingInline:w,horizontalLineHeight:1.15*v+"px",iconSize:C,iconMarginInlineEnd:k-C,collapsedIconSize:$,groupTitleFontSize:C,darkItemDisabledColor:new D.Y(S).setA(.25).toRgbString(),darkItemColor:z,darkDangerItemColor:i,darkItemBg:"#001529",darkPopupBg:"#001529",darkSubMenuItemBg:"#000c17",darkItemSelectedColor:S,darkItemSelectedBg:r,darkDangerItemSelectedBg:i,darkItemHoverBg:"transparent",darkGroupTitleColor:z,darkItemHoverColor:S,darkDangerItemHoverColor:A,darkDangerItemSelectedColor:S,darkDangerItemActiveBg:i,itemWidth:E?`calc(100% + ${I}px)`:`calc(100% - ${2*O}px)`}};var te=(e,t=e,n=!0)=>(0,W.OF)("Menu",e=>{const{colorBgElevated:t,controlHeightLG:n,fontSize:o,darkItemColor:r,darkDangerItemColor:i,darkItemBg:l,darkSubMenuItemBg:a,darkItemSelectedColor:s,darkItemSelectedBg:c,darkDangerItemSelectedBg:u,darkItemHoverBg:d,darkGroupTitleColor:f,darkItemHoverColor:p,darkItemDisabledColor:m,darkDangerItemHoverColor:h,darkDangerItemSelectedColor:g,darkDangerItemActiveBg:v,popupBg:b,darkPopupBg:y}=e,x=e.calc(o).div(7).mul(5).equal(),w=(0,V.oX)(e,{menuArrowSize:x,menuHorizontalHeight:e.calc(n).mul(1.15).equal(),menuArrowOffset:e.calc(x).mul(.25).equal(),menuSubMenuBg:t,calc:e.calc,popupBg:b}),C=(0,V.oX)(w,{itemColor:r,itemHoverColor:p,groupTitleColor:f,itemSelectedColor:s,subMenuItemSelectedColor:s,itemBg:l,popupBg:y,subMenuItemBg:a,itemActiveBg:"transparent",itemSelectedBg:c,activeBarHeight:0,activeBarBorderWidth:0,itemHoverBg:d,itemDisabledColor:m,dangerItemColor:i,dangerItemHoverColor:h,dangerItemSelectedColor:g,dangerItemActiveBg:v,dangerItemSelectedBg:u,menuSubMenuBg:a,horizontalItemSelectedColor:s,horizontalItemSelectedBg:c});return[J(w),q(w),Y(w),G(w,"light"),G(C,"dark"),U(w),(0,H.A)(w),(0,F._j)(w,"slide-up"),(0,F._j)(w,"slide-down"),(0,_.aB)(w,"zoom-big")]},ee,{deprecatedTokens:[["colorGroupTitle","groupTitleColor"],["radiusItem","itemBorderRadius"],["radiusSubMenuItem","subMenuItemBorderRadius"],["colorItemText","itemColor"],["colorItemTextHover","itemHoverColor"],["colorItemTextHoverHorizontal","horizontalItemHoverColor"],["colorItemTextSelected","itemSelectedColor"],["colorItemTextSelectedHorizontal","horizontalItemSelectedColor"],["colorItemTextDisabled","itemDisabledColor"],["colorDangerItemText","dangerItemColor"],["colorDangerItemTextHover","dangerItemHoverColor"],["colorDangerItemTextSelected","dangerItemSelectedColor"],["colorDangerItemBgActive","dangerItemActiveBg"],["colorDangerItemBgSelected","dangerItemSelectedBg"],["colorItemBg","itemBg"],["colorItemBgHover","itemHoverBg"],["colorSubItemBg","subMenuItemBg"],["colorItemBgActive","itemActiveBg"],["colorItemBgSelectedHorizontal","horizontalItemSelectedBg"],["colorActiveBarWidth","activeBarWidth"],["colorActiveBarHeight","activeBarHeight"],["colorActiveBarBorderSize","activeBarBorderWidth"],["colorItemBgSelected","itemSelectedBg"]],injectStyle:n,unitless:{groupTitleLineHeight:!0}})(e,t);var ne=e=>{var t;const{popupClassName:n,icon:r,title:i,theme:l}=e,s=o.useContext(S),{prefixCls:c,inlineCollapsed:u,theme:p}=s,m=(0,w.Wj)();let h;if(r){const e=o.isValidElement(i)&&"span"===i.type;h=o.createElement(o.Fragment,null,(0,g.Ob)(r,{className:a()(o.isValidElement(r)?null===(t=r.props)||void 0===t?void 0:t.className:void 0,`${c}-item-icon`)}),e?i:o.createElement("span",{className:`${c}-title-content`},i))}else h=u&&!m.length&&i&&"string"==typeof i?o.createElement("div",{className:`${c}-inline-collapsed-noicon`},i.charAt(0)):o.createElement("span",{className:`${c}-title-content`},i);const v=o.useMemo(()=>Object.assign(Object.assign({},s),{firstLevel:!1}),[s]),[b]=(0,f.YK)("Menu");return o.createElement(S.Provider,{value:v},o.createElement(w.g8,Object.assign({},(0,d.A)(e,["icon"]),{title:h,popupClassName:a()(c,n,`${c}-${l||p}`),popupStyle:Object.assign({zIndex:b},e.popupStyle)})))},oe=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};function re(e){return null===e||!1===e}const ie={item:z,submenu:ne,divider:E},le=(0,o.forwardRef)((e,t)=>{var n;const r=o.useContext(B),i=r||{},{getPrefixCls:l,getPopupContainer:s,direction:u,menu:f}=o.useContext(y.QO),p=l(),{prefixCls:m,className:h,style:v,theme:b="light",expandIcon:C,_internalDisableMenuItemTitleTooltip:A,inlineCollapsed:E,siderCollapsed:I,rootClassName:O,mode:z,selectable:M,onClick:P,overflowedIndicatorPopupClassName:T}=e,j=oe(e,["prefixCls","className","style","theme","expandIcon","_internalDisableMenuItemTitleTooltip","inlineCollapsed","siderCollapsed","rootClassName","mode","selectable","onClick","overflowedIndicatorPopupClassName"]),N=(0,d.A)(j,["collapsedWidth"]);null===(n=i.validator)||void 0===n||n.call(i,{mode:z});const R=(0,c.A)((...e)=>{var t;null==P||P.apply(void 0,e),null===(t=i.onClick)||void 0===t||t.call(i)}),D=i.mode||z,L=null!=M?M:i.selectable,H=null!=E?E:I,F={horizontal:{motionName:`${p}-slide-up`},inline:(0,$.A)(p),other:{motionName:`${p}-zoom-big`}},_=l("menu",m||i.prefixCls),W=(0,x.A)(_),[V,q,U]=te(_,W,!r),X=a()(`${_}-${b}`,null==f?void 0:f.className,h),G=o.useMemo(()=>{var e,t;if("function"==typeof C||re(C))return C||null;if("function"==typeof i.expandIcon||re(i.expandIcon))return i.expandIcon||null;if("function"==typeof(null==f?void 0:f.expandIcon)||re(null==f?void 0:f.expandIcon))return(null==f?void 0:f.expandIcon)||null;const n=null!==(e=null!=C?C:null==i?void 0:i.expandIcon)&&void 0!==e?e:null==f?void 0:f.expandIcon;return(0,g.Ob)(n,{className:a()(`${_}-submenu-expand-icon`,o.isValidElement(n)?null===(t=n.props)||void 0===t?void 0:t.className:void 0)})},[C,null==i?void 0:i.expandIcon,null==f?void 0:f.expandIcon,_]),K=o.useMemo(()=>({prefixCls:_,inlineCollapsed:H||!1,direction:u,firstLevel:!0,theme:b,mode:D,disableMenuItemTitleTooltip:A}),[_,H,u,A,b]);return V(o.createElement(B.Provider,{value:null},o.createElement(S.Provider,{value:K},o.createElement(w.Ay,Object.assign({getPopupContainer:s,overflowedIndicator:o.createElement(k.A,null),overflowedIndicatorPopupClassName:a()(_,`${_}-${b}`,T),mode:D,selectable:L,onClick:R},N,{inlineCollapsed:H,style:Object.assign(Object.assign({},null==f?void 0:f.style),v),className:X,prefixCls:_,direction:u,defaultMotions:F,expandIcon:G,ref:t,rootClassName:a()(O,q,i.rootClassName,U,W),_internalComponents:ie})))))});var ae=le;const se=(0,o.forwardRef)((e,t)=>{const n=(0,o.useRef)(null),r=o.useContext(C.P);return(0,o.useImperativeHandle)(t,()=>({menu:n.current,focus:e=>{var t;null===(t=n.current)||void 0===t||t.focus(e)}})),o.createElement(ae,Object.assign({ref:n},e,r))});se.Item=z,se.SubMenu=ne,se.Divider=E,se.ItemGroup=w.te;var ce=se,ue=n(1320),de=n(4211),fe=n(5201),pe=n(791);var me=e=>{const{componentCls:t,menuCls:n,colorError:o,colorTextLightSolid:r}=e,i=`${n}-item`;return{[`${t}, ${t}-menu-submenu`]:{[`${n} ${i}`]:{[`&${i}-danger:not(${i}-disabled)`]:{color:o,"&:hover":{color:r,backgroundColor:o}}}}}};const he=e=>{const{componentCls:t,menuCls:n,zIndexPopup:o,dropdownArrowDistance:r,sizePopupArrow:i,antCls:l,iconCls:a,motionDurationMid:s,paddingBlock:c,fontSize:u,dropdownEdgeChildPadding:d,colorTextDisabled:f,fontSizeIcon:p,controlPaddingHorizontal:m,colorBgElevated:h}=e;return[{[t]:{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:o,display:"block","&::before":{position:"absolute",insetBlock:e.calc(i).div(2).sub(r).equal(),zIndex:-9999,opacity:1e-4,content:'""'},"&-menu-vertical":{maxHeight:"100vh",overflowY:"auto"},[`&-trigger${l}-btn`]:{[`& > ${a}-down, & > ${l}-btn-icon > ${a}-down`]:{fontSize:p}},[`${t}-wrap`]:{position:"relative",[`${l}-btn > ${a}-down`]:{fontSize:p},[`${a}-down::before`]:{transition:`transform ${s}`}},[`${t}-wrap-open`]:{[`${a}-down::before`]:{transform:"rotate(180deg)"}},"\n        &-hidden,\n        &-menu-hidden,\n        &-menu-submenu-hidden\n      ":{display:"none"},[`&${l}-slide-down-enter${l}-slide-down-enter-active${t}-placement-bottomLeft,\n          &${l}-slide-down-appear${l}-slide-down-appear-active${t}-placement-bottomLeft,\n          &${l}-slide-down-enter${l}-slide-down-enter-active${t}-placement-bottom,\n          &${l}-slide-down-appear${l}-slide-down-appear-active${t}-placement-bottom,\n          &${l}-slide-down-enter${l}-slide-down-enter-active${t}-placement-bottomRight,\n          &${l}-slide-down-appear${l}-slide-down-appear-active${t}-placement-bottomRight`]:{animationName:F.ox},[`&${l}-slide-up-enter${l}-slide-up-enter-active${t}-placement-topLeft,\n          &${l}-slide-up-appear${l}-slide-up-appear-active${t}-placement-topLeft,\n          &${l}-slide-up-enter${l}-slide-up-enter-active${t}-placement-top,\n          &${l}-slide-up-appear${l}-slide-up-appear-active${t}-placement-top,\n          &${l}-slide-up-enter${l}-slide-up-enter-active${t}-placement-topRight,\n          &${l}-slide-up-appear${l}-slide-up-appear-active${t}-placement-topRight`]:{animationName:F.nP},[`&${l}-slide-down-leave${l}-slide-down-leave-active${t}-placement-bottomLeft,\n          &${l}-slide-down-leave${l}-slide-down-leave-active${t}-placement-bottom,\n          &${l}-slide-down-leave${l}-slide-down-leave-active${t}-placement-bottomRight`]:{animationName:F.vR},[`&${l}-slide-up-leave${l}-slide-up-leave-active${t}-placement-topLeft,\n          &${l}-slide-up-leave${l}-slide-up-leave-active${t}-placement-top,\n          &${l}-slide-up-leave${l}-slide-up-leave-active${t}-placement-topRight`]:{animationName:F.YU}}},(0,fe.Ay)(e,h,{arrowPlacement:{top:!0,bottom:!0}}),{[`${t} ${n}`]:{position:"relative",margin:0},[`${n}-submenu-popup`]:{position:"absolute",zIndex:o,background:"transparent",boxShadow:"none",transformOrigin:"0 0","ul, li":{listStyle:"none",margin:0}},[`${t}, ${t}-menu-submenu`]:Object.assign(Object.assign({},(0,L.dF)(e)),{[n]:Object.assign(Object.assign({padding:d,listStyleType:"none",backgroundColor:h,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary},(0,L.K8)(e)),{"&:empty":{padding:0,boxShadow:"none"},[`${n}-item-group-title`]:{padding:`${(0,R.zA)(c)} ${(0,R.zA)(m)}`,color:e.colorTextDescription,transition:`all ${s}`},[`${n}-item`]:{position:"relative",display:"flex",alignItems:"center"},[`${n}-item-icon`]:{minWidth:u,marginInlineEnd:e.marginXS,fontSize:e.fontSizeSM},[`${n}-title-content`]:{flex:"auto","&-with-extra":{display:"inline-flex",alignItems:"center",width:"100%"},"> a":{color:"inherit",transition:`all ${s}`,"&:hover":{color:"inherit"},"&::after":{position:"absolute",inset:0,content:'""'}},[`${n}-item-extra`]:{paddingInlineStart:e.padding,marginInlineStart:"auto",fontSize:e.fontSizeSM,color:e.colorTextDescription}},[`${n}-item, ${n}-submenu-title`]:Object.assign(Object.assign({display:"flex",margin:0,padding:`${(0,R.zA)(c)} ${(0,R.zA)(m)}`,color:e.colorText,fontWeight:"normal",fontSize:u,lineHeight:e.lineHeight,cursor:"pointer",transition:`all ${s}`,borderRadius:e.borderRadiusSM,"&:hover, &-active":{backgroundColor:e.controlItemBgHover}},(0,L.K8)(e)),{"&-selected":{color:e.colorPrimary,backgroundColor:e.controlItemBgActive,"&:hover, &-active":{backgroundColor:e.controlItemBgActiveHover}},"&-disabled":{color:f,cursor:"not-allowed","&:hover":{color:f,backgroundColor:h,cursor:"not-allowed"},a:{pointerEvents:"none"}},"&-divider":{height:1,margin:`${(0,R.zA)(e.marginXXS)} 0`,overflow:"hidden",lineHeight:0,backgroundColor:e.colorSplit},[`${t}-menu-submenu-expand-icon`]:{position:"absolute",insetInlineEnd:e.paddingXS,[`${t}-menu-submenu-arrow-icon`]:{marginInlineEnd:"0 !important",color:e.colorIcon,fontSize:p,fontStyle:"normal"}}}),[`${n}-item-group-list`]:{margin:`0 ${(0,R.zA)(e.marginXS)}`,padding:0,listStyle:"none"},[`${n}-submenu-title`]:{paddingInlineEnd:e.calc(m).add(e.fontSizeSM).equal()},[`${n}-submenu-vertical`]:{position:"relative"},[`${n}-submenu${n}-submenu-disabled ${t}-menu-submenu-title`]:{[`&, ${t}-menu-submenu-arrow-icon`]:{color:f,backgroundColor:h,cursor:"not-allowed"}},[`${n}-submenu-selected ${t}-menu-submenu-title`]:{color:e.colorPrimary}})})},[(0,F._j)(e,"slide-up"),(0,F._j)(e,"slide-down"),(0,de.Mh)(e,"move-up"),(0,de.Mh)(e,"move-down"),(0,_.aB)(e,"zoom-big")]]};var ge=(0,W.OF)("Dropdown",e=>{const{marginXXS:t,sizePopupArrow:n,paddingXXS:o,componentCls:r}=e,i=(0,V.oX)(e,{menuCls:`${r}-menu`,dropdownArrowDistance:e.calc(n).div(2).add(t).equal(),dropdownEdgeChildPadding:o});return[he(i),me(i)]},e=>Object.assign(Object.assign({zIndexPopup:e.zIndexPopupBase+50,paddingBlock:(e.controlHeight-e.fontSize*e.lineHeight)/2},(0,fe.Ke)({contentRadius:e.borderRadiusLG,limitVerticalRadius:!0})),(0,pe.n)(e)),{resetStyle:!1});const ve=e=>{var t;const{menu:n,arrow:l,prefixCls:h,children:w,trigger:C,disabled:k,dropdownRender:$,popupRender:S,getPopupContainer:A,overlayClassName:E,rootClassName:I,overlayStyle:O,open:z,onOpenChange:M,visible:P,onVisibleChange:T,mouseEnterDelay:j=.15,mouseLeaveDelay:B=.1,autoAdjustOverflow:R=!0,placement:D="",overlay:L,transitionName:H,destroyOnHidden:F,destroyPopupOnHide:_}=e,{getPopupContainer:W,getPrefixCls:V,direction:q,dropdown:U}=o.useContext(y.QO),X=S||$;(0,v.rJ)("Dropdown");const G=o.useMemo(()=>{const e=V();return void 0!==H?H:D.includes("top")?`${e}-slide-down`:`${e}-slide-up`},[V,D,H]),K=o.useMemo(()=>D?D.includes("Center")?D.slice(0,D.indexOf("Center")):D:"rtl"===q?"bottomRight":"bottomLeft",[D,q]),Y=V("dropdown",h),Q=(0,x.A)(Y),[Z,J,ee]=ge(Y,Q),[,te]=(0,ue.Ay)(),ne=o.Children.only(p(w)?o.createElement("span",null,w):w),oe=(0,g.Ob)(ne,{className:a()(`${Y}-trigger`,{[`${Y}-rtl`]:"rtl"===q},ne.props.className),disabled:null!==(t=ne.props.disabled)&&void 0!==t?t:k}),re=k?[]:C,ie=!!(null==re?void 0:re.includes("contextMenu")),[le,ae]=(0,u.A)(!1,{value:null!=z?z:P}),se=(0,c.A)(e=>{null==M||M(e,{source:"trigger"}),null==T||T(e),ae(e)}),de=a()(E,I,J,ee,Q,null==U?void 0:U.className,{[`${Y}-rtl`]:"rtl"===q}),fe=(0,m.A)({arrowPointAtCenter:"object"==typeof l&&l.pointAtCenter,autoAdjustOverflow:R,offset:te.marginXXS,arrowWidth:l?te.sizePopupArrow:0,borderRadius:te.borderRadius}),pe=o.useCallback(()=>{(null==n?void 0:n.selectable)&&(null==n?void 0:n.multiple)||(null==M||M(!1,{source:"menu"}),ae(!1))},[null==n?void 0:n.selectable,null==n?void 0:n.multiple]),[me,he]=(0,f.YK)("Dropdown",null==O?void 0:O.zIndex);let ve=o.createElement(s.A,Object.assign({alignPoint:ie},(0,d.A)(e,["rootClassName"]),{mouseEnterDelay:j,mouseLeaveDelay:B,visible:le,builtinPlacements:fe,arrow:!!l,overlayClassName:de,prefixCls:Y,getPopupContainer:A||W,transitionName:G,trigger:re,overlay:()=>{let e;return e=(null==n?void 0:n.items)?o.createElement(ce,Object.assign({},n)):"function"==typeof L?L():L,X&&(e=X(e)),e=o.Children.only("string"==typeof e?o.createElement("span",null,e):e),o.createElement(N,{prefixCls:`${Y}-menu`,rootClassName:a()(ee,Q),expandIcon:o.createElement("span",{className:`${Y}-menu-submenu-arrow`},"rtl"===q?o.createElement(r.A,{className:`${Y}-menu-submenu-arrow-icon`}):o.createElement(i.A,{className:`${Y}-menu-submenu-arrow-icon`})),mode:"vertical",selectable:!1,onClick:pe,validator:({mode:e})=>{}},e)},placement:K,onVisibleChange:se,overlayStyle:Object.assign(Object.assign(Object.assign({},null==U?void 0:U.style),O),{zIndex:me}),autoDestroy:null!=F?F:_}),oe);return me&&(ve=o.createElement(b.A.Provider,{value:he},ve)),Z(ve)},be=(0,h.A)(ve,"align",void 0,"dropdown",e=>e);ve._InternalPanelDoNotUseOrYouWillBeFired=e=>o.createElement(be,Object.assign({},e),o.createElement("span",null));var ye=ve},9314:function(e,t,n){"use strict";n.d(t,{A:function(){return nn}});var o=n(6540),r=n(6942),i=n.n(r),l=n(8168),a=n(436),s=n(4467),c=n(9379),u=n(5544),d=n(3986),f=n(2284),p=n(2533),m=n(8210),h=n(981),g=n(8430),v=n(8719),b=function(e){var t=e.className,n=e.customizeIcon,r=e.customizeIconProps,l=e.children,a=e.onMouseDown,s=e.onClick,c="function"==typeof n?n(r):n;return o.createElement("span",{className:t,onMouseDown:function(e){e.preventDefault(),null==a||a(e)},style:{userSelect:"none",WebkitUserSelect:"none"},unselectable:"on",onClick:s,"aria-hidden":!0},void 0!==c?c:o.createElement("span",{className:i()(t.split(/\s+/).map(function(e){return"".concat(e,"-icon")}))},l))},y=o.createContext(null);function x(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:250,t=o.useRef(null),n=o.useRef(null);return o.useEffect(function(){return function(){window.clearTimeout(n.current)}},[]),[function(){return t.current},function(o){(o||null===t.current)&&(t.current=o),window.clearTimeout(n.current),n.current=window.setTimeout(function(){t.current=null},e)}]}var w=n(6928);var C=n(2065),k=n(9591);var $=function(e,t,n){var o=(0,c.A)((0,c.A)({},e),n?t:{});return Object.keys(t).forEach(function(n){var r=t[n];"function"==typeof r&&(o[n]=function(){for(var t,o=arguments.length,i=new Array(o),l=0;l<o;l++)i[l]=arguments[l];return r.apply(void 0,i),null===(t=e[n])||void 0===t?void 0:t.call.apply(t,[e].concat(i))})}),o},S=["prefixCls","id","inputElement","autoFocus","autoComplete","editable","activeDescendantId","value","open","attrs"],A=function(e,t){var n=e.prefixCls,r=e.id,l=e.inputElement,a=e.autoFocus,s=e.autoComplete,u=e.editable,f=e.activeDescendantId,p=e.value,h=e.open,g=e.attrs,b=(0,d.A)(e,S),y=l||o.createElement("input",null),x=y,w=x.ref,C=x.props;return(0,m.$e)(!("maxLength"in y.props),"Passing 'maxLength' to input element directly may not work because input in BaseSelect is controlled."),y=o.cloneElement(y,(0,c.A)((0,c.A)((0,c.A)({type:"search"},$(b,C,!0)),{},{id:r,ref:(0,v.K4)(t,w),autoComplete:s||"off",autoFocus:a,className:i()("".concat(n,"-selection-search-input"),null==C?void 0:C.className),role:"combobox","aria-expanded":h||!1,"aria-haspopup":"listbox","aria-owns":"".concat(r,"_list"),"aria-autocomplete":"list","aria-controls":"".concat(r,"_list"),"aria-activedescendant":h?f:void 0},g),{},{value:u?p:"",readOnly:!u,unselectable:u?null:"on",style:(0,c.A)((0,c.A)({},C.style),{},{opacity:u?null:0})}))};var E=o.forwardRef(A);function I(e){return Array.isArray(e)?e:void 0!==e?[e]:[]}var O="undefined"!=typeof window&&window.document&&window.document.documentElement;function z(e){return["string","number"].includes((0,f.A)(e))}function M(e){var t=void 0;return e&&(z(e.title)?t=e.title.toString():z(e.label)&&(t=e.label.toString())),t}function P(e){var t;return null!==(t=e.key)&&void 0!==t?t:e.value}var T=function(e){e.preventDefault(),e.stopPropagation()},j=function(e){var t,n,r=e.id,l=e.prefixCls,a=e.values,c=e.open,d=e.searchValue,f=e.autoClearSearchValue,p=e.inputRef,m=e.placeholder,h=e.disabled,g=e.mode,v=e.showSearch,y=e.autoFocus,x=e.autoComplete,w=e.activeDescendantId,$=e.tabIndex,S=e.removeIcon,A=e.maxTagCount,I=e.maxTagTextLength,z=e.maxTagPlaceholder,j=void 0===z?function(e){return"+ ".concat(e.length," ...")}:z,N=e.tagRender,B=e.onToggleOpen,R=e.onRemove,D=e.onInputChange,L=e.onInputPaste,H=e.onInputKeyDown,F=e.onInputMouseDown,_=e.onInputCompositionStart,W=e.onInputCompositionEnd,V=e.onInputBlur,q=o.useRef(null),U=(0,o.useState)(0),X=(0,u.A)(U,2),G=X[0],K=X[1],Y=(0,o.useState)(!1),Q=(0,u.A)(Y,2),Z=Q[0],J=Q[1],ee="".concat(l,"-selection"),te=c||"multiple"===g&&!1===f||"tags"===g?d:"",ne="tags"===g||"multiple"===g&&!1===f||v&&(c||Z);t=function(){K(q.current.scrollWidth)},n=[te],O?o.useLayoutEffect(t,n):o.useEffect(t,n);var oe=function(e,t,n,r,l){return o.createElement("span",{title:M(e),className:i()("".concat(ee,"-item"),(0,s.A)({},"".concat(ee,"-item-disabled"),n))},o.createElement("span",{className:"".concat(ee,"-item-content")},t),r&&o.createElement(b,{className:"".concat(ee,"-item-remove"),onMouseDown:T,onClick:l,customizeIcon:S},"×"))},re=function(e,t,n,r,i,l){return o.createElement("span",{onMouseDown:function(e){T(e),B(!c)}},N({label:t,value:e,disabled:n,closable:r,onClose:i,isMaxTag:!!l}))},ie=o.createElement("div",{className:"".concat(ee,"-search"),style:{width:G},onFocus:function(){J(!0)},onBlur:function(){J(!1)}},o.createElement(E,{ref:p,open:c,prefixCls:l,id:r,inputElement:null,disabled:h,autoFocus:y,autoComplete:x,editable:ne,activeDescendantId:w,value:te,onKeyDown:H,onMouseDown:F,onChange:D,onPaste:L,onCompositionStart:_,onCompositionEnd:W,onBlur:V,tabIndex:$,attrs:(0,C.A)(e,!0)}),o.createElement("span",{ref:q,className:"".concat(ee,"-search-mirror"),"aria-hidden":!0},te," ")),le=o.createElement(k.A,{prefixCls:"".concat(ee,"-overflow"),data:a,renderItem:function(e){var t=e.disabled,n=e.label,o=e.value,r=!h&&!t,i=n;if("number"==typeof I&&("string"==typeof n||"number"==typeof n)){var l=String(i);l.length>I&&(i="".concat(l.slice(0,I),"..."))}var a=function(t){t&&t.stopPropagation(),R(e)};return"function"==typeof N?re(o,i,t,r,a):oe(e,i,t,r,a)},renderRest:function(e){if(!a.length)return null;var t="function"==typeof j?j(e):j;return"function"==typeof N?re(void 0,t,!1,!1,void 0,!0):oe({title:t},t,!1)},suffix:ie,itemKey:P,maxCount:A});return o.createElement("span",{className:"".concat(ee,"-wrap")},le,!a.length&&!te&&o.createElement("span",{className:"".concat(ee,"-placeholder")},m))},N=function(e){var t=e.inputElement,n=e.prefixCls,r=e.id,i=e.inputRef,l=e.disabled,a=e.autoFocus,s=e.autoComplete,c=e.activeDescendantId,d=e.mode,f=e.open,p=e.values,m=e.placeholder,h=e.tabIndex,g=e.showSearch,v=e.searchValue,b=e.activeValue,y=e.maxLength,x=e.onInputKeyDown,w=e.onInputMouseDown,k=e.onInputChange,$=e.onInputPaste,S=e.onInputCompositionStart,A=e.onInputCompositionEnd,I=e.onInputBlur,O=e.title,z=o.useState(!1),P=(0,u.A)(z,2),T=P[0],j=P[1],N="combobox"===d,B=N||g,R=p[0],D=v||"";N&&b&&!T&&(D=b),o.useEffect(function(){N&&j(!1)},[N,b]);var L=!("combobox"!==d&&!f&&!g)&&!!D,H=void 0===O?M(R):O,F=o.useMemo(function(){return R?null:o.createElement("span",{className:"".concat(n,"-selection-placeholder"),style:L?{visibility:"hidden"}:void 0},m)},[R,L,m,n]);return o.createElement("span",{className:"".concat(n,"-selection-wrap")},o.createElement("span",{className:"".concat(n,"-selection-search")},o.createElement(E,{ref:i,prefixCls:n,id:r,open:f,inputElement:t,disabled:l,autoFocus:a,autoComplete:s,editable:B,activeDescendantId:c,value:D,onKeyDown:x,onMouseDown:w,onChange:function(e){j(!0),k(e)},onPaste:$,onCompositionStart:S,onCompositionEnd:A,onBlur:I,tabIndex:h,attrs:(0,C.A)(e,!0),maxLength:N?y:void 0})),!N&&R?o.createElement("span",{className:"".concat(n,"-selection-item"),title:H,style:L?{visibility:"hidden"}:void 0},R.label):null,F)},B=function(e,t){var n=(0,o.useRef)(null),r=(0,o.useRef)(!1),i=e.prefixCls,a=e.open,s=e.mode,c=e.showSearch,d=e.tokenWithEnter,f=e.disabled,p=e.prefix,m=e.autoClearSearchValue,h=e.onSearch,g=e.onSearchSubmit,v=e.onToggleOpen,b=e.onInputKeyDown,y=e.onInputBlur,C=e.domRef;o.useImperativeHandle(t,function(){return{focus:function(e){n.current.focus(e)},blur:function(){n.current.blur()}}});var k=x(0),$=(0,u.A)(k,2),S=$[0],A=$[1],E=(0,o.useRef)(null),I=function(e){!1!==h(e,!0,r.current)&&v(!0)},O={inputRef:n,onInputKeyDown:function(e){var t,o=e.which,i=n.current instanceof HTMLTextAreaElement;(i||!a||o!==w.A.UP&&o!==w.A.DOWN||e.preventDefault(),b&&b(e),o!==w.A.ENTER||"tags"!==s||r.current||a||null==g||g(e.target.value),i&&!a&&~[w.A.UP,w.A.DOWN,w.A.LEFT,w.A.RIGHT].indexOf(o))||(t=o)&&![w.A.ESC,w.A.SHIFT,w.A.BACKSPACE,w.A.TAB,w.A.WIN_KEY,w.A.ALT,w.A.META,w.A.WIN_KEY_RIGHT,w.A.CTRL,w.A.SEMICOLON,w.A.EQUALS,w.A.CAPS_LOCK,w.A.CONTEXT_MENU,w.A.F1,w.A.F2,w.A.F3,w.A.F4,w.A.F5,w.A.F6,w.A.F7,w.A.F8,w.A.F9,w.A.F10,w.A.F11,w.A.F12].includes(t)&&v(!0)},onInputMouseDown:function(){A(!0)},onInputChange:function(e){var t=e.target.value;if(d&&E.current&&/[\r\n]/.test(E.current)){var n=E.current.replace(/[\r\n]+$/,"").replace(/\r\n/g," ").replace(/[\r\n]/g," ");t=t.replace(n,E.current)}E.current=null,I(t)},onInputPaste:function(e){var t=e.clipboardData,n=null==t?void 0:t.getData("text");E.current=n||""},onInputCompositionStart:function(){r.current=!0},onInputCompositionEnd:function(e){r.current=!1,"combobox"!==s&&I(e.target.value)},onInputBlur:y},z="multiple"===s||"tags"===s?o.createElement(j,(0,l.A)({},e,O)):o.createElement(N,(0,l.A)({},e,O));return o.createElement("div",{ref:C,className:"".concat(i,"-selector"),onClick:function(e){e.target!==n.current&&(void 0!==document.body.style.msTouchAction?setTimeout(function(){n.current.focus()}):n.current.focus())},onMouseDown:function(e){var t=S();e.target===n.current||t||"combobox"===s&&f||e.preventDefault(),("combobox"===s||c&&t)&&a||(a&&!1!==m&&h("",!0,!1),v())}},p&&o.createElement("div",{className:"".concat(i,"-prefix")},p),z)};var R=o.forwardRef(B),D=n(2427),L=["prefixCls","disabled","visible","children","popupElement","animation","transitionName","dropdownStyle","dropdownClassName","direction","placement","builtinPlacements","dropdownMatchSelectWidth","dropdownRender","dropdownAlign","getPopupContainer","empty","getTriggerDOMNode","onPopupVisibleChange","onPopupMouseEnter"],H=function(e,t){var n=e.prefixCls,r=(e.disabled,e.visible),a=e.children,u=e.popupElement,f=e.animation,p=e.transitionName,m=e.dropdownStyle,h=e.dropdownClassName,g=e.direction,v=void 0===g?"ltr":g,b=e.placement,y=e.builtinPlacements,x=e.dropdownMatchSelectWidth,w=e.dropdownRender,C=e.dropdownAlign,k=e.getPopupContainer,$=e.empty,S=e.getTriggerDOMNode,A=e.onPopupVisibleChange,E=e.onPopupMouseEnter,I=(0,d.A)(e,L),O="".concat(n,"-dropdown"),z=u;w&&(z=w(u));var M=o.useMemo(function(){return y||function(e){var t=!0===e?0:1;return{bottomLeft:{points:["tl","bl"],offset:[0,4],overflow:{adjustX:t,adjustY:1},htmlRegion:"scroll"},bottomRight:{points:["tr","br"],offset:[0,4],overflow:{adjustX:t,adjustY:1},htmlRegion:"scroll"},topLeft:{points:["bl","tl"],offset:[0,-4],overflow:{adjustX:t,adjustY:1},htmlRegion:"scroll"},topRight:{points:["br","tr"],offset:[0,-4],overflow:{adjustX:t,adjustY:1},htmlRegion:"scroll"}}}(x)},[y,x]),P=f?"".concat(O,"-").concat(f):p,T="number"==typeof x,j=o.useMemo(function(){return T?null:!1===x?"minWidth":"width"},[x,T]),N=m;T&&(N=(0,c.A)((0,c.A)({},N),{},{width:x}));var B=o.useRef(null);return o.useImperativeHandle(t,function(){return{getPopupElement:function(){var e;return null===(e=B.current)||void 0===e?void 0:e.popupElement}}}),o.createElement(D.A,(0,l.A)({},I,{showAction:A?["click"]:[],hideAction:A?["click"]:[],popupPlacement:b||("rtl"===v?"bottomRight":"bottomLeft"),builtinPlacements:M,prefixCls:O,popupTransitionName:P,popup:o.createElement("div",{onMouseEnter:E},z),ref:B,stretch:j,popupAlign:C,popupVisible:r,getPopupContainer:k,popupClassName:i()(h,(0,s.A)({},"".concat(O,"-empty"),$)),popupStyle:N,getTriggerDOMNode:S,onPopupVisibleChange:A}),a)};var F=o.forwardRef(H),_=n(7695);function W(e,t){var n,o=e.key;return"value"in e&&(n=e.value),null!=o?o:void 0!==n?n:"rc-index-key-".concat(t)}function V(e){return void 0!==e&&!Number.isNaN(e)}function q(e,t){var n=e||{},o=n.label||(t?"children":"label");return{label:o,value:n.value||"value",options:n.options||"options",groupLabel:n.groupLabel||o}}function U(e){var t=(0,c.A)({},e);return"props"in t||Object.defineProperty(t,"props",{get:function(){return(0,m.Ay)(!1,"Return type is option instead of Option instance. Please read value directly instead of reading from `props`."),t}}),t}var X=o.createContext(null);function G(e){var t=e.visible,n=e.values;if(!t)return null;return o.createElement("span",{"aria-live":"polite",style:{width:0,height:0,position:"absolute",overflow:"hidden",opacity:0}},"".concat(n.slice(0,50).map(function(e){var t=e.label,n=e.value;return["number","string"].includes((0,f.A)(t))?t:n}).join(", ")),n.length>50?", ...":null)}var K=["id","prefixCls","className","showSearch","tagRender","direction","omitDomProps","displayValues","onDisplayValuesChange","emptyOptions","notFoundContent","onClear","mode","disabled","loading","getInputElement","getRawInputElement","open","defaultOpen","onDropdownVisibleChange","activeValue","onActiveValueChange","activeDescendantId","searchValue","autoClearSearchValue","onSearch","onSearchSplit","tokenSeparators","allowClear","prefix","suffixIcon","clearIcon","OptionList","animation","transitionName","dropdownStyle","dropdownClassName","dropdownMatchSelectWidth","dropdownRender","dropdownAlign","placement","builtinPlacements","getPopupContainer","showAction","onFocus","onBlur","onKeyUp","onKeyDown","onMouseDown"],Y=["value","onChange","removeIcon","placeholder","autoFocus","maxTagCount","maxTagTextLength","maxTagPlaceholder","choiceTransitionName","onInputKeyDown","onPopupScroll","tabIndex"],Q=function(e){return"tags"===e||"multiple"===e},Z=o.forwardRef(function(e,t){var n,r=e.id,m=e.prefixCls,w=e.className,C=e.showSearch,k=e.tagRender,$=e.direction,S=e.omitDomProps,A=e.displayValues,E=e.onDisplayValuesChange,I=e.emptyOptions,O=e.notFoundContent,z=void 0===O?"Not Found":O,M=e.onClear,P=e.mode,T=e.disabled,j=e.loading,N=e.getInputElement,B=e.getRawInputElement,D=e.open,L=e.defaultOpen,H=e.onDropdownVisibleChange,W=e.activeValue,q=e.onActiveValueChange,U=e.activeDescendantId,Z=e.searchValue,J=e.autoClearSearchValue,ee=e.onSearch,te=e.onSearchSplit,ne=e.tokenSeparators,oe=e.allowClear,re=e.prefix,ie=e.suffixIcon,le=e.clearIcon,ae=e.OptionList,se=e.animation,ce=e.transitionName,ue=e.dropdownStyle,de=e.dropdownClassName,fe=e.dropdownMatchSelectWidth,pe=e.dropdownRender,me=e.dropdownAlign,he=e.placement,ge=e.builtinPlacements,ve=e.getPopupContainer,be=e.showAction,ye=void 0===be?[]:be,xe=e.onFocus,we=e.onBlur,Ce=e.onKeyUp,ke=e.onKeyDown,$e=e.onMouseDown,Se=(0,d.A)(e,K),Ae=Q(P),Ee=(void 0!==C?C:Ae)||"combobox"===P,Ie=(0,c.A)({},Se);Y.forEach(function(e){delete Ie[e]}),null==S||S.forEach(function(e){delete Ie[e]});var Oe=o.useState(!1),ze=(0,u.A)(Oe,2),Me=ze[0],Pe=ze[1];o.useEffect(function(){Pe((0,g.A)())},[]);var Te=o.useRef(null),je=o.useRef(null),Ne=o.useRef(null),Be=o.useRef(null),Re=o.useRef(null),De=o.useRef(!1),Le=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10,t=o.useState(!1),n=(0,u.A)(t,2),r=n[0],i=n[1],l=o.useRef(null),a=function(){window.clearTimeout(l.current)};return o.useEffect(function(){return a},[]),[r,function(t,n){a(),l.current=window.setTimeout(function(){i(t),n&&n()},e)},a]}(),He=(0,u.A)(Le,3),Fe=He[0],_e=He[1],We=He[2];o.useImperativeHandle(t,function(){var e,t;return{focus:null===(e=Be.current)||void 0===e?void 0:e.focus,blur:null===(t=Be.current)||void 0===t?void 0:t.blur,scrollTo:function(e){var t;return null===(t=Re.current)||void 0===t?void 0:t.scrollTo(e)},nativeElement:Te.current||je.current}});var Ve=o.useMemo(function(){var e;if("combobox"!==P)return Z;var t=null===(e=A[0])||void 0===e?void 0:e.value;return"string"==typeof t||"number"==typeof t?String(t):""},[Z,P,A]),qe="combobox"===P&&"function"==typeof N&&N()||null,Ue="function"==typeof B&&B(),Xe=(0,v.xK)(je,null==Ue||null===(n=Ue.props)||void 0===n?void 0:n.ref),Ge=o.useState(!1),Ke=(0,u.A)(Ge,2),Ye=Ke[0],Qe=Ke[1];(0,h.A)(function(){Qe(!0)},[]);var Ze=(0,p.A)(!1,{defaultValue:L,value:D}),Je=(0,u.A)(Ze,2),et=Je[0],tt=Je[1],nt=!!Ye&&et,ot=!z&&I;(T||ot&&nt&&"combobox"===P)&&(nt=!1);var rt=!ot&&nt,it=o.useCallback(function(e){var t=void 0!==e?e:!nt;T||(tt(t),nt!==t&&(null==H||H(t)))},[T,nt,tt,H]),lt=o.useMemo(function(){return(ne||[]).some(function(e){return["\n","\r\n"].includes(e)})},[ne]),at=o.useContext(X)||{},st=at.maxCount,ct=at.rawValues,ut=function(e,t,n){if(!(Ae&&V(st)&&(null==ct?void 0:ct.size)>=st)){var o=!0,r=e;null==q||q(null);var i=function(e,t,n){if(!t||!t.length)return null;var o=!1,r=function e(t,n){var r=(0,_.A)(n),i=r[0],l=r.slice(1);if(!i)return[t];var s=t.split(i);return o=o||s.length>1,s.reduce(function(t,n){return[].concat((0,a.A)(t),(0,a.A)(e(n,l)))},[]).filter(Boolean)}(e,t);return o?void 0!==n?r.slice(0,n):r:null}(e,ne,V(st)?st-ct.size:void 0),l=n?null:i;return"combobox"!==P&&l&&(r="",null==te||te(l),it(!1),o=!1),ee&&Ve!==r&&ee(r,{source:t?"typing":"effect"}),o}};o.useEffect(function(){nt||Ae||"combobox"===P||ut("",!1,!1)},[nt]),o.useEffect(function(){et&&T&&tt(!1),T&&!De.current&&_e(!1)},[T]);var dt=x(),ft=(0,u.A)(dt,2),pt=ft[0],mt=ft[1],ht=o.useRef(!1),gt=o.useRef(!1),vt=[];o.useEffect(function(){return function(){vt.forEach(function(e){return clearTimeout(e)}),vt.splice(0,vt.length)}},[]);var bt,yt=o.useState({}),xt=(0,u.A)(yt,2)[1];Ue&&(bt=function(e){it(e)}),function(e,t,n,r){var i=o.useRef(null);i.current={open:t,triggerOpen:n,customizedTrigger:r},o.useEffect(function(){function t(t){var n;if(null===(n=i.current)||void 0===n||!n.customizedTrigger){var o=t.target;o.shadowRoot&&t.composed&&(o=t.composedPath()[0]||o),i.current.open&&e().filter(function(e){return e}).every(function(e){return!e.contains(o)&&e!==o})&&i.current.triggerOpen(!1)}}return window.addEventListener("mousedown",t),function(){return window.removeEventListener("mousedown",t)}},[])}(function(){var e;return[Te.current,null===(e=Ne.current)||void 0===e?void 0:e.getPopupElement()]},rt,it,!!Ue);var wt,Ct=o.useMemo(function(){return(0,c.A)((0,c.A)({},e),{},{notFoundContent:z,open:nt,triggerOpen:rt,id:r,showSearch:Ee,multiple:Ae,toggleOpen:it})},[e,z,rt,nt,r,Ee,Ae,it]),kt=!!ie||j;kt&&(wt=o.createElement(b,{className:i()("".concat(m,"-arrow"),(0,s.A)({},"".concat(m,"-arrow-loading"),j)),customizeIcon:ie,customizeIconProps:{loading:j,searchValue:Ve,open:nt,focused:Fe,showSearch:Ee}}));var $t,St=function(e,t,n,r,i){var l=arguments.length>5&&void 0!==arguments[5]&&arguments[5],a=arguments.length>6?arguments[6]:void 0,s=arguments.length>7?arguments[7]:void 0,c=o.useMemo(function(){return"object"===(0,f.A)(r)?r.clearIcon:i||void 0},[r,i]);return{allowClear:o.useMemo(function(){return!(l||!r||!n.length&&!a||"combobox"===s&&""===a)},[r,l,n.length,a,s]),clearIcon:o.createElement(b,{className:"".concat(e,"-clear"),onMouseDown:t,customizeIcon:c},"×")}}(m,function(){var e;null==M||M(),null===(e=Be.current)||void 0===e||e.focus(),E([],{type:"clear",values:A}),ut("",!1,!1)},A,oe,le,T,Ve,P),At=St.allowClear,Et=St.clearIcon,It=o.createElement(ae,{ref:Re}),Ot=i()(m,w,(0,s.A)((0,s.A)((0,s.A)((0,s.A)((0,s.A)((0,s.A)((0,s.A)((0,s.A)((0,s.A)((0,s.A)({},"".concat(m,"-focused"),Fe),"".concat(m,"-multiple"),Ae),"".concat(m,"-single"),!Ae),"".concat(m,"-allow-clear"),oe),"".concat(m,"-show-arrow"),kt),"".concat(m,"-disabled"),T),"".concat(m,"-loading"),j),"".concat(m,"-open"),nt),"".concat(m,"-customize-input"),qe),"".concat(m,"-show-search"),Ee)),zt=o.createElement(F,{ref:Ne,disabled:T,prefixCls:m,visible:rt,popupElement:It,animation:se,transitionName:ce,dropdownStyle:ue,dropdownClassName:de,direction:$,dropdownMatchSelectWidth:fe,dropdownRender:pe,dropdownAlign:me,placement:he,builtinPlacements:ge,getPopupContainer:ve,empty:I,getTriggerDOMNode:function(e){return je.current||e},onPopupVisibleChange:bt,onPopupMouseEnter:function(){xt({})}},Ue?o.cloneElement(Ue,{ref:Xe}):o.createElement(R,(0,l.A)({},e,{domRef:je,prefixCls:m,inputElement:qe,ref:Be,id:r,prefix:re,showSearch:Ee,autoClearSearchValue:J,mode:P,activeDescendantId:U,tagRender:k,values:A,open:nt,onToggleOpen:it,activeValue:W,searchValue:Ve,onSearch:ut,onSearchSubmit:function(e){e&&e.trim()&&ee(e,{source:"submit"})},onRemove:function(e){var t=A.filter(function(t){return t!==e});E(t,{type:"remove",values:[e]})},tokenWithEnter:lt,onInputBlur:function(){ht.current=!1}})));return $t=Ue?zt:o.createElement("div",(0,l.A)({className:Ot},Ie,{ref:Te,onMouseDown:function(e){var t,n=e.target,o=null===(t=Ne.current)||void 0===t?void 0:t.getPopupElement();if(o&&o.contains(n)){var r=setTimeout(function(){var e,t=vt.indexOf(r);-1!==t&&vt.splice(t,1),We(),Me||o.contains(document.activeElement)||null===(e=Be.current)||void 0===e||e.focus()});vt.push(r)}for(var i=arguments.length,l=new Array(i>1?i-1:0),a=1;a<i;a++)l[a-1]=arguments[a];null==$e||$e.apply(void 0,[e].concat(l))},onKeyDown:function(e){var t,n=pt(),o=e.key,r="Enter"===o;if(r&&("combobox"!==P&&e.preventDefault(),nt||it(!0)),mt(!!Ve),"Backspace"===o&&!n&&Ae&&!Ve&&A.length){for(var i=(0,a.A)(A),l=null,s=i.length-1;s>=0;s-=1){var c=i[s];if(!c.disabled){i.splice(s,1),l=c;break}}l&&E(i,{type:"remove",values:[l]})}for(var u=arguments.length,d=new Array(u>1?u-1:0),f=1;f<u;f++)d[f-1]=arguments[f];!nt||r&&ht.current||(r&&(ht.current=!0),null===(t=Re.current)||void 0===t||t.onKeyDown.apply(t,[e].concat(d))),null==ke||ke.apply(void 0,[e].concat(d))},onKeyUp:function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),o=1;o<t;o++)n[o-1]=arguments[o];var r;nt&&(null===(r=Re.current)||void 0===r||r.onKeyUp.apply(r,[e].concat(n))),"Enter"===e.key&&(ht.current=!1),null==Ce||Ce.apply(void 0,[e].concat(n))},onFocus:function(){_e(!0),T||(xe&&!gt.current&&xe.apply(void 0,arguments),ye.includes("focus")&&it(!0)),gt.current=!0},onBlur:function(){De.current=!0,_e(!1,function(){gt.current=!1,De.current=!1,it(!1)}),T||(Ve&&("tags"===P?ee(Ve,{source:"submit"}):"multiple"===P&&ee("",{source:"blur"})),we&&we.apply(void 0,arguments))}}),o.createElement(G,{visible:Fe&&!nt,values:A}),zt,wt,At&&Et),o.createElement(y.Provider,{value:Ct},$t)});var J=Z,ee=function(){return null};ee.isSelectOptGroup=!0;var te=ee,ne=function(){return null};ne.isSelectOption=!0;var oe=ne,re=n(8104),ie=n(9853),le=n(8462),ae=n(1470),se=n(961),ce=o.forwardRef(function(e,t){var n=e.height,r=e.offsetY,a=e.offsetX,u=e.children,d=e.prefixCls,f=e.onInnerResize,p=e.innerProps,m=e.rtl,h=e.extra,g={},v={display:"flex",flexDirection:"column"};return void 0!==r&&(g={height:n,position:"relative",overflow:"hidden"},v=(0,c.A)((0,c.A)({},v),{},(0,s.A)((0,s.A)((0,s.A)((0,s.A)((0,s.A)({transform:"translateY(".concat(r,"px)")},m?"marginRight":"marginLeft",-a),"position","absolute"),"left",0),"right",0),"top",0))),o.createElement("div",{style:g},o.createElement(le.A,{onResize:function(e){e.offsetHeight&&f&&f()}},o.createElement("div",(0,l.A)({style:v,className:i()((0,s.A)({},"".concat(d,"-holder-inner"),d)),ref:t},p),u,h)))});ce.displayName="Filler";var ue=ce;function de(e){var t=e.children,n=e.setRef,r=o.useCallback(function(e){n(e)},[]);return o.cloneElement(t,{ref:r})}function fe(e,t,n){var r=o.useState(e),i=(0,u.A)(r,2),l=i[0],a=i[1],s=o.useState(null),c=(0,u.A)(s,2),d=c[0],f=c[1];return o.useEffect(function(){var o=function(e,t,n){var o,r,i=e.length,l=t.length;if(0===i&&0===l)return null;i<l?(o=e,r=t):(o=t,r=e);var a={__EMPTY_ITEM__:!0};function s(e){return void 0!==e?n(e):a}for(var c=null,u=1!==Math.abs(i-l),d=0;d<r.length;d+=1){var f=s(o[d]);if(f!==s(r[d])){c=d,u=u||f!==s(r[d+1]);break}}return null===c?null:{index:c,multiple:u}}(l||[],e||[],t);void 0!==(null==o?void 0:o.index)&&(null==n||n(o.index),f(e[o.index])),a(e)},[e]),[d]}var pe=n(5371),me="object"===("undefined"==typeof navigator?"undefined":(0,f.A)(navigator))&&/Firefox/i.test(navigator.userAgent),he=function(e,t,n,r){var i=(0,o.useRef)(!1),l=(0,o.useRef)(null);var a=(0,o.useRef)({top:e,bottom:t,left:n,right:r});return a.current.top=e,a.current.bottom=t,a.current.left=n,a.current.right=r,function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=e?t<0&&a.current.left||t>0&&a.current.right:t<0&&a.current.top||t>0&&a.current.bottom;return n&&o?(clearTimeout(l.current),i.current=!1):o&&!i.current||(clearTimeout(l.current),i.current=!0,l.current=setTimeout(function(){i.current=!1},50)),!i.current&&o}};function ge(e,t,n,r,i,l,a){var s=(0,o.useRef)(0),c=(0,o.useRef)(null),u=(0,o.useRef)(null),d=(0,o.useRef)(!1),f=he(t,n,r,i);var p=(0,o.useRef)(null),m=(0,o.useRef)(null);return[function(t){if(e){pe.A.cancel(m.current),m.current=(0,pe.A)(function(){p.current=null},2);var n=t.deltaX,o=t.deltaY,r=t.shiftKey,i=n,h=o;("sx"===p.current||!p.current&&r&&o&&!n)&&(i=o,h=0,p.current="sx");var g=Math.abs(i),v=Math.abs(h);null===p.current&&(p.current=l&&g>v?"x":"y"),"y"===p.current?function(e,t){if(pe.A.cancel(c.current),!f(!1,t)){var n=e;n._virtualHandled||(n._virtualHandled=!0,s.current+=t,u.current=t,me||n.preventDefault(),c.current=(0,pe.A)(function(){var e=d.current?10:1;a(s.current*e,!1),s.current=0}))}}(t,h):function(e,t){a(t,!0),me||e.preventDefault()}(t,i)}},function(t){e&&(d.current=t.detail===u.current)}]}var ve=n(3029),be=n(2901),ye=function(){function e(){(0,ve.A)(this,e),(0,s.A)(this,"maps",void 0),(0,s.A)(this,"id",0),(0,s.A)(this,"diffRecords",new Map),this.maps=Object.create(null)}return(0,be.A)(e,[{key:"set",value:function(e,t){this.diffRecords.set(e,this.maps[e]),this.maps[e]=t,this.id+=1}},{key:"get",value:function(e){return this.maps[e]}},{key:"resetRecord",value:function(){this.diffRecords.clear()}},{key:"getRecord",value:function(){return this.diffRecords}}]),e}();function xe(e){var t=parseFloat(e);return isNaN(t)?0:t}var we=14/15;function Ce(e){return Math.floor(Math.pow(e,.5))}function ke(e,t){return("touches"in e?e.touches[0]:e)[t?"pageX":"pageY"]-window[t?"scrollX":"scrollY"]}var $e=o.forwardRef(function(e,t){var n=e.prefixCls,r=e.rtl,l=e.scrollOffset,a=e.scrollRange,d=e.onStartMove,f=e.onStopMove,p=e.onScroll,m=e.horizontal,h=e.spinSize,g=e.containerSize,v=e.style,b=e.thumbStyle,y=e.showScrollBar,x=o.useState(!1),w=(0,u.A)(x,2),C=w[0],k=w[1],$=o.useState(null),S=(0,u.A)($,2),A=S[0],E=S[1],I=o.useState(null),O=(0,u.A)(I,2),z=O[0],M=O[1],P=!r,T=o.useRef(),j=o.useRef(),N=o.useState(y),B=(0,u.A)(N,2),R=B[0],D=B[1],L=o.useRef(),H=function(){!0!==y&&!1!==y&&(clearTimeout(L.current),D(!0),L.current=setTimeout(function(){D(!1)},3e3))},F=a-g||0,_=g-h||0,W=o.useMemo(function(){return 0===l||0===F?0:l/F*_},[l,F,_]),V=o.useRef({top:W,dragging:C,pageY:A,startTop:z});V.current={top:W,dragging:C,pageY:A,startTop:z};var q=function(e){k(!0),E(ke(e,m)),M(V.current.top),d(),e.stopPropagation(),e.preventDefault()};o.useEffect(function(){var e=function(e){e.preventDefault()},t=T.current,n=j.current;return t.addEventListener("touchstart",e,{passive:!1}),n.addEventListener("touchstart",q,{passive:!1}),function(){t.removeEventListener("touchstart",e),n.removeEventListener("touchstart",q)}},[]);var U=o.useRef();U.current=F;var X=o.useRef();X.current=_,o.useEffect(function(){if(C){var e,t=function(t){var n=V.current,o=n.dragging,r=n.pageY,i=n.startTop;pe.A.cancel(e);var l=T.current.getBoundingClientRect(),a=g/(m?l.width:l.height);if(o){var s=(ke(t,m)-r)*a,c=i;!P&&m?c-=s:c+=s;var u=U.current,d=X.current,f=d?c/d:0,h=Math.ceil(f*u);h=Math.max(h,0),h=Math.min(h,u),e=(0,pe.A)(function(){p(h,m)})}},n=function(){k(!1),f()};return window.addEventListener("mousemove",t,{passive:!0}),window.addEventListener("touchmove",t,{passive:!0}),window.addEventListener("mouseup",n,{passive:!0}),window.addEventListener("touchend",n,{passive:!0}),function(){window.removeEventListener("mousemove",t),window.removeEventListener("touchmove",t),window.removeEventListener("mouseup",n),window.removeEventListener("touchend",n),pe.A.cancel(e)}}},[C]),o.useEffect(function(){return H(),function(){clearTimeout(L.current)}},[l]),o.useImperativeHandle(t,function(){return{delayHidden:H}});var G="".concat(n,"-scrollbar"),K={position:"absolute",visibility:R?null:"hidden"},Y={position:"absolute",borderRadius:99,background:"var(--rc-virtual-list-scrollbar-bg, rgba(0, 0, 0, 0.5))",cursor:"pointer",userSelect:"none"};return m?(Object.assign(K,{height:8,left:0,right:0,bottom:0}),Object.assign(Y,(0,s.A)({height:"100%",width:h},P?"left":"right",W))):(Object.assign(K,(0,s.A)({width:8,top:0,bottom:0},P?"right":"left",0)),Object.assign(Y,{width:"100%",height:h,top:W})),o.createElement("div",{ref:T,className:i()(G,(0,s.A)((0,s.A)((0,s.A)({},"".concat(G,"-horizontal"),m),"".concat(G,"-vertical"),!m),"".concat(G,"-visible"),R)),style:(0,c.A)((0,c.A)({},K),v),onMouseDown:function(e){e.stopPropagation(),e.preventDefault()},onMouseMove:H},o.createElement("div",{ref:j,className:i()("".concat(G,"-thumb"),(0,s.A)({},"".concat(G,"-thumb-moving"),C)),style:(0,c.A)((0,c.A)({},Y),b),onMouseDown:q}))});var Se=$e;function Ae(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=e/(arguments.length>1&&void 0!==arguments[1]?arguments[1]:0)*e;return isNaN(t)&&(t=0),t=Math.max(t,20),Math.floor(t)}var Ee=["prefixCls","className","height","itemHeight","fullHeight","style","data","children","itemKey","virtual","direction","scrollWidth","component","onScroll","onVirtualScroll","onVisibleChange","innerProps","extraRender","styles","showScrollBar"],Ie=[],Oe={overflowY:"auto",overflowAnchor:"none"};function ze(e,t){var n=e.prefixCls,r=void 0===n?"rc-virtual-list":n,a=e.className,p=e.height,m=e.itemHeight,g=e.fullHeight,v=void 0===g||g,b=e.style,y=e.data,x=e.children,w=e.itemKey,C=e.virtual,k=e.direction,$=e.scrollWidth,S=e.component,A=void 0===S?"div":S,E=e.onScroll,I=e.onVirtualScroll,O=e.onVisibleChange,z=e.innerProps,M=e.extraRender,P=e.styles,T=e.showScrollBar,j=void 0===T?"optional":T,N=(0,d.A)(e,Ee),B=o.useCallback(function(e){return"function"==typeof w?w(e):null==e?void 0:e[w]},[w]),R=function(e,t,n){var r=o.useState(0),i=(0,u.A)(r,2),l=i[0],a=i[1],s=(0,o.useRef)(new Map),c=(0,o.useRef)(new ye),d=(0,o.useRef)(0);function f(){d.current+=1}function p(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];f();var t=function(){var e=!1;s.current.forEach(function(t,n){if(t&&t.offsetParent){var o=t.offsetHeight,r=getComputedStyle(t),i=r.marginTop,l=r.marginBottom,a=o+xe(i)+xe(l);c.current.get(n)!==a&&(c.current.set(n,a),e=!0)}}),e&&a(function(e){return e+1})};if(e)t();else{d.current+=1;var n=d.current;Promise.resolve().then(function(){n===d.current&&t()})}}return(0,o.useEffect)(function(){return f},[]),[function(o,r){var i=e(o),l=s.current.get(i);r?(s.current.set(i,r),p()):s.current.delete(i),!l!=!r&&(r?null==t||t(o):null==n||n(o))},p,c.current,l]}(B,null,null),D=(0,u.A)(R,4),L=D[0],H=D[1],F=D[2],_=D[3],W=!(!1===C||!p||!m),V=o.useMemo(function(){return Object.values(F.maps).reduce(function(e,t){return e+t},0)},[F.id,F.maps]),q=W&&y&&(Math.max(m*y.length,V)>p||!!$),U="rtl"===k,X=i()(r,(0,s.A)({},"".concat(r,"-rtl"),U),a),G=y||Ie,K=(0,o.useRef)(),Y=(0,o.useRef)(),Q=(0,o.useRef)(),Z=(0,o.useState)(0),J=(0,u.A)(Z,2),ee=J[0],te=J[1],ne=(0,o.useState)(0),oe=(0,u.A)(ne,2),re=oe[0],ie=oe[1],ce=(0,o.useState)(!1),me=(0,u.A)(ce,2),ve=me[0],be=me[1],$e=function(){be(!0)},ze=function(){be(!1)},Me={getKey:B};function Pe(e){te(function(t){var n=function(e){var t=e;Number.isNaN(Qe.current)||(t=Math.min(t,Qe.current));return t=Math.max(t,0),t}("function"==typeof e?e(t):e);return K.current.scrollTop=n,n})}var Te=(0,o.useRef)({start:0,end:G.length}),je=(0,o.useRef)(),Ne=fe(G,B),Be=(0,u.A)(Ne,1)[0];je.current=Be;var Re=o.useMemo(function(){if(!W)return{scrollHeight:void 0,start:0,end:G.length-1,offset:void 0};var e;if(!q)return{scrollHeight:(null===(e=Y.current)||void 0===e?void 0:e.offsetHeight)||0,start:0,end:G.length-1,offset:void 0};for(var t,n,o,r=0,i=G.length,l=0;l<i;l+=1){var a=G[l],s=B(a),c=F.get(s),u=r+(void 0===c?m:c);u>=ee&&void 0===t&&(t=l,n=r),u>ee+p&&void 0===o&&(o=l),r=u}return void 0===t&&(t=0,n=0,o=Math.ceil(p/m)),void 0===o&&(o=G.length-1),{scrollHeight:r,start:t,end:o=Math.min(o+1,G.length-1),offset:n}},[q,W,ee,G,_,p]),De=Re.scrollHeight,Le=Re.start,He=Re.end,Fe=Re.offset;Te.current.start=Le,Te.current.end=He,o.useLayoutEffect(function(){var e=F.getRecord();if(1===e.size){var t=Array.from(e.keys())[0],n=e.get(t),o=G[Le];if(o&&void 0===n)if(B(o)===t){var r=F.get(t)-m;Pe(function(e){return e+r})}}F.resetRecord()},[De]);var _e=o.useState({width:0,height:p}),We=(0,u.A)(_e,2),Ve=We[0],qe=We[1],Ue=(0,o.useRef)(),Xe=(0,o.useRef)(),Ge=o.useMemo(function(){return Ae(Ve.width,$)},[Ve.width,$]),Ke=o.useMemo(function(){return Ae(Ve.height,De)},[Ve.height,De]),Ye=De-p,Qe=(0,o.useRef)(Ye);Qe.current=Ye;var Ze=ee<=0,Je=ee>=Ye,et=re<=0,tt=re>=$,nt=he(Ze,Je,et,tt),ot=function(){return{x:U?-re:re,y:ee}},rt=(0,o.useRef)(ot()),it=(0,ae._q)(function(e){if(I){var t=(0,c.A)((0,c.A)({},ot()),e);rt.current.x===t.x&&rt.current.y===t.y||(I(t),rt.current=t)}});function lt(e,t){var n=e;t?((0,se.flushSync)(function(){ie(n)}),it()):Pe(n)}var at=function(e){var t=e,n=$?$-Ve.width:0;return t=Math.max(t,0),t=Math.min(t,n)},st=(0,ae._q)(function(e,t){t?((0,se.flushSync)(function(){ie(function(t){return at(t+(U?-e:e))})}),it()):Pe(function(t){return t+e})}),ct=ge(W,Ze,Je,et,tt,!!$,st),ut=(0,u.A)(ct,2),dt=ut[0],ft=ut[1];!function(e,t,n){var r,i=(0,o.useRef)(!1),l=(0,o.useRef)(0),a=(0,o.useRef)(0),s=(0,o.useRef)(null),c=(0,o.useRef)(null),u=function(e){if(i.current){var t=Math.ceil(e.touches[0].pageX),o=Math.ceil(e.touches[0].pageY),r=l.current-t,s=a.current-o,u=Math.abs(r)>Math.abs(s);u?l.current=t:a.current=o;var d=n(u,u?r:s,!1,e);d&&e.preventDefault(),clearInterval(c.current),d&&(c.current=setInterval(function(){u?r*=we:s*=we;var e=Math.floor(u?r:s);(!n(u,e,!0)||Math.abs(e)<=.1)&&clearInterval(c.current)},16))}},d=function(){i.current=!1,r()},f=function(e){r(),1!==e.touches.length||i.current||(i.current=!0,l.current=Math.ceil(e.touches[0].pageX),a.current=Math.ceil(e.touches[0].pageY),s.current=e.target,s.current.addEventListener("touchmove",u,{passive:!1}),s.current.addEventListener("touchend",d,{passive:!0}))};r=function(){s.current&&(s.current.removeEventListener("touchmove",u),s.current.removeEventListener("touchend",d))},(0,h.A)(function(){return e&&t.current.addEventListener("touchstart",f,{passive:!0}),function(){var e;null===(e=t.current)||void 0===e||e.removeEventListener("touchstart",f),r(),clearInterval(c.current)}},[e])}(W,K,function(e,t,n,o){var r=o;return!nt(e,t,n)&&((!r||!r._virtualHandled)&&(r&&(r._virtualHandled=!0),dt({preventDefault:function(){},deltaX:e?t:0,deltaY:e?0:t}),!0))}),function(e,t,n){o.useEffect(function(){var o=t.current;if(e&&o){var r,i,l=!1,a=function(){pe.A.cancel(r)},s=function e(){a(),r=(0,pe.A)(function(){n(i),e()})},c=function(e){if(!e.target.draggable&&0===e.button){var t=e;t._virtualHandled||(t._virtualHandled=!0,l=!0)}},u=function(){l=!1,a()},d=function(e){if(l){var t=ke(e,!1),n=o.getBoundingClientRect(),r=n.top,c=n.bottom;t<=r?(i=-Ce(r-t),s()):t>=c?(i=Ce(t-c),s()):a()}};return o.addEventListener("mousedown",c),o.ownerDocument.addEventListener("mouseup",u),o.ownerDocument.addEventListener("mousemove",d),function(){o.removeEventListener("mousedown",c),o.ownerDocument.removeEventListener("mouseup",u),o.ownerDocument.removeEventListener("mousemove",d),a()}}},[e])}(q,K,function(e){Pe(function(t){return t+e})}),(0,h.A)(function(){function e(e){var t=Ze&&e.detail<0,n=Je&&e.detail>0;!W||t||n||e.preventDefault()}var t=K.current;return t.addEventListener("wheel",dt,{passive:!1}),t.addEventListener("DOMMouseScroll",ft,{passive:!0}),t.addEventListener("MozMousePixelScroll",e,{passive:!1}),function(){t.removeEventListener("wheel",dt),t.removeEventListener("DOMMouseScroll",ft),t.removeEventListener("MozMousePixelScroll",e)}},[W,Ze,Je]),(0,h.A)(function(){if($){var e=at(re);ie(e),it({x:e})}},[Ve.width,$]);var pt=function(){var e,t;null===(e=Ue.current)||void 0===e||e.delayHidden(),null===(t=Xe.current)||void 0===t||t.delayHidden()},mt=function(e,t,n,r,i,l,a,s){var d=o.useRef(),p=o.useState(null),m=(0,u.A)(p,2),g=m[0],v=m[1];return(0,h.A)(function(){if(g&&g.times<10){if(!e.current)return void v(function(e){return(0,c.A)({},e)});l();var o=g.targetAlign,s=g.originAlign,u=g.index,d=g.offset,f=e.current.clientHeight,p=!1,m=o,h=null;if(f){for(var b=o||s,y=0,x=0,w=0,C=Math.min(t.length-1,u),k=0;k<=C;k+=1){var $=i(t[k]);x=y;var S=n.get($);y=w=x+(void 0===S?r:S)}for(var A="top"===b?d:f-d,E=C;E>=0;E-=1){var I=i(t[E]),O=n.get(I);if(void 0===O){p=!0;break}if((A-=O)<=0)break}switch(b){case"top":h=x-d;break;case"bottom":h=w-f+d;break;default:var z=e.current.scrollTop;x<z?m="top":w>z+f&&(m="bottom")}null!==h&&a(h),h!==g.lastTop&&(p=!0)}p&&v((0,c.A)((0,c.A)({},g),{},{times:g.times+1,targetAlign:m,lastTop:h}))}},[g,e.current]),function(e){if(null!=e){if(pe.A.cancel(d.current),"number"==typeof e)a(e);else if(e&&"object"===(0,f.A)(e)){var n,o=e.align;n="index"in e?e.index:t.findIndex(function(t){return i(t)===e.key});var r=e.offset;v({times:0,index:n,offset:void 0===r?0:r,originAlign:o})}}else s()}}(K,G,F,m,B,function(){return H(!0)},Pe,pt);o.useImperativeHandle(t,function(){return{nativeElement:Q.current,getScrollInfo:ot,scrollTo:function(e){var t;(t=e)&&"object"===(0,f.A)(t)&&("left"in t||"top"in t)?(void 0!==e.left&&ie(at(e.left)),mt(e.top)):mt(e)}}}),(0,h.A)(function(){if(O){var e=G.slice(Le,He+1);O(e,G)}},[Le,He,G]);var ht=function(e,t,n,r){var i=o.useMemo(function(){return[new Map,[]]},[e,n.id,r]),l=(0,u.A)(i,2),a=l[0],s=l[1];return function(o){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:o,l=a.get(o),c=a.get(i);if(void 0===l||void 0===c)for(var u=e.length,d=s.length;d<u;d+=1){var f,p=e[d],m=t(p);a.set(m,d);var h=null!==(f=n.get(m))&&void 0!==f?f:r;if(s[d]=(s[d-1]||0)+h,m===o&&(l=d),m===i&&(c=d),void 0!==l&&void 0!==c)break}return{top:s[l-1]||0,bottom:s[c]}}}(G,B,F,m),gt=null==M?void 0:M({start:Le,end:He,virtual:q,offsetX:re,offsetY:Fe,rtl:U,getSize:ht}),vt=function(e,t,n,r,i,l,a,s){var c=s.getKey;return e.slice(t,n+1).map(function(e,n){var s=a(e,t+n,{style:{width:r},offsetX:i}),u=c(e);return o.createElement(de,{key:u,setRef:function(t){return l(e,t)}},s)})}(G,Le,He,$,re,L,x,Me),bt=null;p&&(bt=(0,c.A)((0,s.A)({},v?"height":"maxHeight",p),Oe),W&&(bt.overflowY="hidden",$&&(bt.overflowX="hidden"),ve&&(bt.pointerEvents="none")));var yt={};return U&&(yt.dir="rtl"),o.createElement("div",(0,l.A)({ref:Q,style:(0,c.A)((0,c.A)({},b),{},{position:"relative"}),className:X},yt,N),o.createElement(le.A,{onResize:function(e){qe({width:e.offsetWidth,height:e.offsetHeight})}},o.createElement(A,{className:"".concat(r,"-holder"),style:bt,ref:K,onScroll:function(e){var t=e.currentTarget.scrollTop;t!==ee&&Pe(t),null==E||E(e),it()},onMouseEnter:pt},o.createElement(ue,{prefixCls:r,height:De,offsetX:re,offsetY:Fe,scrollWidth:$,onInnerResize:H,ref:Y,innerProps:z,rtl:U,extra:gt},vt))),q&&De>p&&o.createElement(Se,{ref:Ue,prefixCls:r,scrollOffset:ee,scrollRange:De,rtl:U,onScroll:lt,onStartMove:$e,onStopMove:ze,spinSize:Ke,containerSize:Ve.height,style:null==P?void 0:P.verticalScrollBar,thumbStyle:null==P?void 0:P.verticalScrollBarThumb,showScrollBar:j}),q&&$>Ve.width&&o.createElement(Se,{ref:Xe,prefixCls:r,scrollOffset:re,scrollRange:$,rtl:U,onScroll:lt,onStartMove:$e,onStopMove:ze,spinSize:Ge,containerSize:Ve.width,horizontal:!0,style:null==P?void 0:P.horizontalScrollBar,thumbStyle:null==P?void 0:P.horizontalScrollBarThumb,showScrollBar:j}))}var Me=o.forwardRef(ze);Me.displayName="List";var Pe=Me;var Te=["disabled","title","children","style","className"];function je(e){return"string"==typeof e||"number"==typeof e}var Ne=function(e,t){var n=o.useContext(y),r=n.prefixCls,c=n.id,f=n.open,p=n.multiple,m=n.mode,h=n.searchValue,g=n.toggleOpen,v=n.notFoundContent,x=n.onPopupScroll,k=o.useContext(X),$=k.maxCount,S=k.flattenOptions,A=k.onActiveValue,E=k.defaultActiveFirstOption,I=k.onSelect,O=k.menuItemSelectedIcon,z=k.rawValues,M=k.fieldNames,P=k.virtual,T=k.direction,j=k.listHeight,N=k.listItemHeight,B=k.optionRender,R="".concat(r,"-item"),D=(0,re.A)(function(){return S},[f,S],function(e,t){return t[0]&&e[1]!==t[1]}),L=o.useRef(null),H=o.useMemo(function(){return p&&V($)&&(null==z?void 0:z.size)>=$},[p,$,null==z?void 0:z.size]),F=function(e){e.preventDefault()},_=function(e){var t;null===(t=L.current)||void 0===t||t.scrollTo("number"==typeof e?{index:e}:e)},W=o.useCallback(function(e){return"combobox"!==m&&z.has(e)},[m,(0,a.A)(z).toString(),z.size]),q=function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,n=D.length,o=0;o<n;o+=1){var r=(e+o*t+n)%n,i=D[r]||{},l=i.group,a=i.data;if(!l&&(null==a||!a.disabled)&&(W(a.value)||!H))return r}return-1},U=o.useState(function(){return q(0)}),G=(0,u.A)(U,2),K=G[0],Y=G[1],Q=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];Y(e);var n={source:t?"keyboard":"mouse"},o=D[e];o?A(o.value,e,n):A(null,-1,n)};(0,o.useEffect)(function(){Q(!1!==E?q(0):-1)},[D.length,h]);var Z=o.useCallback(function(e){return"combobox"===m?String(e).toLowerCase()===h.toLowerCase():z.has(e)},[m,h,(0,a.A)(z).toString(),z.size]);(0,o.useEffect)(function(){var e,t=setTimeout(function(){if(!p&&f&&1===z.size){var e=Array.from(z)[0],t=D.findIndex(function(t){var n=t.data;return h?String(n.value).startsWith(h):n.value===e});-1!==t&&(Q(t),_(t))}});f&&(null===(e=L.current)||void 0===e||e.scrollTo(void 0));return function(){return clearTimeout(t)}},[f,h]);var J=function(e){void 0!==e&&I(e,{selected:!z.has(e)}),p||g(!1)};if(o.useImperativeHandle(t,function(){return{onKeyDown:function(e){var t=e.which,n=e.ctrlKey;switch(t){case w.A.N:case w.A.P:case w.A.UP:case w.A.DOWN:var o=0;if(t===w.A.UP?o=-1:t===w.A.DOWN?o=1:/(mac\sos|macintosh)/i.test(navigator.appVersion)&&n&&(t===w.A.N?o=1:t===w.A.P&&(o=-1)),0!==o){var r=q(K+o,o);_(r),Q(r,!0)}break;case w.A.TAB:case w.A.ENTER:var i,l=D[K];!l||null!=l&&null!==(i=l.data)&&void 0!==i&&i.disabled||H?J(void 0):J(l.value),f&&e.preventDefault();break;case w.A.ESC:g(!1),f&&e.stopPropagation()}},onKeyUp:function(){},scrollTo:function(e){_(e)}}}),0===D.length)return o.createElement("div",{role:"listbox",id:"".concat(c,"_list"),className:"".concat(R,"-empty"),onMouseDown:F},v);var ee=Object.keys(M).map(function(e){return M[e]}),te=function(e){return e.label};function ne(e,t){return{role:e.group?"presentation":"option",id:"".concat(c,"_list_").concat(t)}}var oe=function(e){var t=D[e];if(!t)return null;var n=t.data||{},r=n.value,i=t.group,a=(0,C.A)(n,!0),s=te(t);return t?o.createElement("div",(0,l.A)({"aria-label":"string"!=typeof s||i?null:s},a,{key:e},ne(t,e),{"aria-selected":Z(r)}),r):null},le={role:"listbox",id:"".concat(c,"_list")};return o.createElement(o.Fragment,null,P&&o.createElement("div",(0,l.A)({},le,{style:{height:0,width:0,overflow:"hidden"}}),oe(K-1),oe(K),oe(K+1)),o.createElement(Pe,{itemKey:"key",ref:L,data:D,height:j,itemHeight:N,fullHeight:!1,onMouseDown:F,onScroll:x,virtual:P,direction:T,innerProps:P?null:le},function(e,t){var n=e.group,r=e.groupOption,a=e.data,c=e.label,u=e.value,f=a.key;if(n){var p,m=null!==(p=a.title)&&void 0!==p?p:je(c)?c.toString():void 0;return o.createElement("div",{className:i()(R,"".concat(R,"-group"),a.className),title:m},void 0!==c?c:f)}var h=a.disabled,g=a.title,v=(a.children,a.style),y=a.className,x=(0,d.A)(a,Te),w=(0,ie.A)(x,ee),k=W(u),$=h||!k&&H,S="".concat(R,"-option"),A=i()(R,S,y,(0,s.A)((0,s.A)((0,s.A)((0,s.A)({},"".concat(S,"-grouped"),r),"".concat(S,"-active"),K===t&&!$),"".concat(S,"-disabled"),$),"".concat(S,"-selected"),k)),E=te(e),I=!O||"function"==typeof O||k,z="number"==typeof E?E:E||u,M=je(z)?z.toString():void 0;return void 0!==g&&(M=g),o.createElement("div",(0,l.A)({},(0,C.A)(w),P?{}:ne(e,t),{"aria-selected":Z(u),className:A,title:M,onMouseMove:function(){K===t||$||Q(t)},onClick:function(){$||J(u)},style:v}),o.createElement("div",{className:"".concat(S,"-content")},"function"==typeof B?B(e,{index:t}):z),o.isValidElement(O)||k,I&&o.createElement(b,{className:"".concat(R,"-option-state"),customizeIcon:O,customizeIconProps:{value:u,disabled:$,isSelected:k}},k?"✓":null))}))};var Be=o.forwardRef(Ne);function Re(e,t){return I(e).join("").toUpperCase().includes(t)}var De=n(998),Le=0,He=(0,De.A)();function Fe(e){var t=o.useState(),n=(0,u.A)(t,2),r=n[0],i=n[1];return o.useEffect(function(){var e;i("rc_select_".concat((He?(e=Le,Le+=1):e="TEST_OR_SSR",e)))},[]),e||r}var _e=n(2546),We=["children","value"],Ve=["children"];function qe(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return(0,_e.A)(e).map(function(e,n){if(!o.isValidElement(e)||!e.type)return null;var r=e,i=r.type.isSelectOptGroup,l=r.key,a=r.props,s=a.children,u=(0,d.A)(a,Ve);return t||!i?function(e){var t=e,n=t.key,o=t.props,r=o.children,i=o.value,l=(0,d.A)(o,We);return(0,c.A)({key:n,value:void 0!==i?i:n,children:r},l)}(e):(0,c.A)((0,c.A)({key:"__RC_SELECT_GRP__".concat(null===l?n:l,"__"),label:l},u),{},{options:qe(s)})}).filter(function(e){return e})}var Ue=function(e,t,n,r,i){return o.useMemo(function(){var o=e;!e&&(o=qe(t));var l=new Map,a=new Map,s=function(e,t,n){n&&"string"==typeof n&&e.set(t[n],t)};return function e(t){for(var o=arguments.length>1&&void 0!==arguments[1]&&arguments[1],c=0;c<t.length;c+=1){var u=t[c];!u[n.options]||o?(l.set(u[n.value],u),s(a,u,n.label),s(a,u,r),s(a,u,i)):e(u[n.options],!0)}}(o),{options:o,valueOptions:l,labelOptions:a}},[e,t,n,r,i])};function Xe(e){var t=o.useRef();t.current=e;var n=o.useCallback(function(){return t.current.apply(t,arguments)},[]);return n}var Ge=["id","mode","prefixCls","backfill","fieldNames","inputValue","searchValue","onSearch","autoClearSearchValue","onSelect","onDeselect","dropdownMatchSelectWidth","filterOption","filterSort","optionFilterProp","optionLabelProp","options","optionRender","children","defaultActiveFirstOption","menuItemSelectedIcon","virtual","direction","listHeight","listItemHeight","labelRender","value","defaultValue","labelInValue","onChange","maxCount"],Ke=["inputValue"];var Ye=o.forwardRef(function(e,t){var n=e.id,r=e.mode,i=e.prefixCls,m=void 0===i?"rc-select":i,h=e.backfill,g=e.fieldNames,v=e.inputValue,b=e.searchValue,y=e.onSearch,x=e.autoClearSearchValue,w=void 0===x||x,C=e.onSelect,k=e.onDeselect,$=e.dropdownMatchSelectWidth,S=void 0===$||$,A=e.filterOption,E=e.filterSort,O=e.optionFilterProp,z=e.optionLabelProp,M=e.options,P=e.optionRender,T=e.children,j=e.defaultActiveFirstOption,N=e.menuItemSelectedIcon,B=e.virtual,R=e.direction,D=e.listHeight,L=void 0===D?200:D,H=e.listItemHeight,F=void 0===H?20:H,_=e.labelRender,V=e.value,G=e.defaultValue,K=e.labelInValue,Y=e.onChange,Z=e.maxCount,ee=(0,d.A)(e,Ge),te=Fe(n),ne=Q(r),oe=!(M||!T),re=o.useMemo(function(){return(void 0!==A||"combobox"!==r)&&A},[A,r]),ie=o.useMemo(function(){return q(g,oe)},[JSON.stringify(g),oe]),le=(0,p.A)("",{value:void 0!==b?b:v,postState:function(e){return e||""}}),ae=(0,u.A)(le,2),se=ae[0],ce=ae[1],ue=Ue(M,T,ie,O,z),de=ue.valueOptions,fe=ue.labelOptions,pe=ue.options,me=o.useCallback(function(e){return I(e).map(function(e){var t,n,o,r,i,l;(function(e){return!e||"object"!==(0,f.A)(e)})(e)?t=e:(o=e.key,n=e.label,t=null!==(l=e.value)&&void 0!==l?l:o);var a,s=de.get(t);s&&(void 0===n&&(n=null==s?void 0:s[z||ie.label]),void 0===o&&(o=null!==(a=null==s?void 0:s.key)&&void 0!==a?a:t),r=null==s?void 0:s.disabled,i=null==s?void 0:s.title);return{label:n,value:t,key:o,disabled:r,title:i}})},[ie,z,de]),he=(0,p.A)(G,{value:V}),ge=(0,u.A)(he,2),ve=ge[0],be=ge[1],ye=o.useMemo(function(){var e,t=me(ne&&null===ve?[]:ve);return"combobox"===r&&function(e){return!e&&0!==e}(null===(e=t[0])||void 0===e?void 0:e.value)?[]:t},[ve,me,r,ne]),xe=function(e,t){var n=o.useRef({values:new Map,options:new Map});return[o.useMemo(function(){var o=n.current,r=o.values,i=o.options,l=e.map(function(e){var t;return void 0===e.label?(0,c.A)((0,c.A)({},e),{},{label:null===(t=r.get(e.value))||void 0===t?void 0:t.label}):e}),a=new Map,s=new Map;return l.forEach(function(e){a.set(e.value,e),s.set(e.value,t.get(e.value)||i.get(e.value))}),n.current.values=a,n.current.options=s,l},[e,t]),o.useCallback(function(e){return t.get(e)||n.current.options.get(e)},[t])]}(ye,de),we=(0,u.A)(xe,2),Ce=we[0],ke=we[1],$e=o.useMemo(function(){if(!r&&1===Ce.length){var e=Ce[0];if(null===e.value&&(null===e.label||void 0===e.label))return[]}return Ce.map(function(e){var t;return(0,c.A)((0,c.A)({},e),{},{label:null!==(t="function"==typeof _?_(e):e.label)&&void 0!==t?t:e.value})})},[r,Ce,_]),Se=o.useMemo(function(){return new Set(Ce.map(function(e){return e.value}))},[Ce]);o.useEffect(function(){if("combobox"===r){var e,t=null===(e=Ce[0])||void 0===e?void 0:e.value;ce(function(e){return null!=e}(t)?String(t):"")}},[Ce]);var Ae=Xe(function(e,t){var n=null!=t?t:e;return(0,s.A)((0,s.A)({},ie.value,e),ie.label,n)}),Ee=function(e,t,n,r,i){return o.useMemo(function(){if(!n||!1===r)return e;var o=t.options,l=t.label,a=t.value,u=[],d="function"==typeof r,f=n.toUpperCase(),p=d?r:function(e,t){return i?Re(t[i],f):t[o]?Re(t["children"!==l?l:"label"],f):Re(t[a],f)},m=d?function(e){return U(e)}:function(e){return e};return e.forEach(function(e){if(e[o])if(p(n,m(e)))u.push(e);else{var t=e[o].filter(function(e){return p(n,m(e))});t.length&&u.push((0,c.A)((0,c.A)({},e),{},(0,s.A)({},o,t)))}else p(n,m(e))&&u.push(e)}),u},[e,r,i,n,t])}(o.useMemo(function(){if("tags"!==r)return pe;var e=(0,a.A)(pe);return(0,a.A)(Ce).sort(function(e,t){return e.value<t.value?-1:1}).forEach(function(t){var n=t.value;(function(e){return de.has(e)})(n)||e.push(Ae(n,t.label))}),e},[Ae,pe,de,Ce,r]),ie,se,re,O),Ie=o.useMemo(function(){return"tags"!==r||!se||Ee.some(function(e){return e[O||"value"]===se})||Ee.some(function(e){return e[ie.value]===se})?Ee:[Ae(se)].concat((0,a.A)(Ee))},[Ae,O,r,Ee,se,ie]),Oe=function e(t){return(0,a.A)(t).sort(function(e,t){return E(e,t,{searchValue:se})}).map(function(t){return Array.isArray(t.options)?(0,c.A)((0,c.A)({},t),{},{options:t.options.length>0?e(t.options):t.options}):t})},ze=o.useMemo(function(){return E?Oe(Ie):Ie},[Ie,E,se]),Me=o.useMemo(function(){return function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.fieldNames,o=t.childrenAsData,r=[],i=q(n,!1),l=i.label,a=i.value,s=i.options,c=i.groupLabel;return function e(t,n){Array.isArray(t)&&t.forEach(function(t){if(n||!(s in t)){var i=t[a];r.push({key:W(t,r.length),groupOption:n,data:t,label:t[l],value:i})}else{var u=t[c];void 0===u&&o&&(u=t.label),r.push({key:W(t,r.length),group:!0,data:t,label:u}),e(t[s],!0)}})}(e,!1),r}(ze,{fieldNames:ie,childrenAsData:oe})},[ze,ie,oe]),Pe=function(e){var t=me(e);if(be(t),Y&&(t.length!==Ce.length||t.some(function(e,t){var n;return(null===(n=Ce[t])||void 0===n?void 0:n.value)!==(null==e?void 0:e.value)}))){var n=K?t:t.map(function(e){return e.value}),o=t.map(function(e){return U(ke(e.value))});Y(ne?n:n[0],ne?o:o[0])}},Te=o.useState(null),je=(0,u.A)(Te,2),Ne=je[0],De=je[1],Le=o.useState(0),He=(0,u.A)(Le,2),_e=He[0],We=He[1],Ve=void 0!==j?j:"combobox"!==r,qe=o.useCallback(function(e,t){var n=(arguments.length>2&&void 0!==arguments[2]?arguments[2]:{}).source,o=void 0===n?"keyboard":n;We(t),h&&"combobox"===r&&null!==e&&"keyboard"===o&&De(String(e))},[h,r]),Ye=function(e,t,n){var o=function(){var t,n=ke(e);return[K?{label:null==n?void 0:n[ie.label],value:e,key:null!==(t=null==n?void 0:n.key)&&void 0!==t?t:e}:e,U(n)]};if(t&&C){var r=o(),i=(0,u.A)(r,2),l=i[0],a=i[1];C(l,a)}else if(!t&&k&&"clear"!==n){var s=o(),c=(0,u.A)(s,2),d=c[0],f=c[1];k(d,f)}},Qe=Xe(function(e,t){var n,o=!ne||t.selected;n=o?ne?[].concat((0,a.A)(Ce),[e]):[e]:Ce.filter(function(t){return t.value!==e}),Pe(n),Ye(e,o),"combobox"===r?De(""):Q&&!w||(ce(""),De(""))}),Ze=o.useMemo(function(){var e=!1!==B&&!1!==S;return(0,c.A)((0,c.A)({},ue),{},{flattenOptions:Me,onActiveValue:qe,defaultActiveFirstOption:Ve,onSelect:Qe,menuItemSelectedIcon:N,rawValues:Se,fieldNames:ie,virtual:e,direction:R,listHeight:L,listItemHeight:F,childrenAsData:oe,maxCount:Z,optionRender:P})},[Z,ue,Me,qe,Ve,Qe,N,Se,ie,B,S,R,L,F,oe,P]);return o.createElement(X.Provider,{value:Ze},o.createElement(J,(0,l.A)({},ee,{id:te,prefixCls:m,ref:t,omitDomProps:Ke,mode:r,displayValues:$e,onDisplayValuesChange:function(e,t){Pe(e);var n=t.type,o=t.values;"remove"!==n&&"clear"!==n||o.forEach(function(e){Ye(e.value,!1,n)})},direction:R,searchValue:se,onSearch:function(e,t){if(ce(e),De(null),"submit"!==t.source)"blur"!==t.source&&("combobox"===r&&Pe(e),null==y||y(e));else{var n=(e||"").trim();if(n){var o=Array.from(new Set([].concat((0,a.A)(Se),[n])));Pe(o),Ye(n,!0),ce("")}}},autoClearSearchValue:w,onSearchSplit:function(e){var t=e;"tags"!==r&&(t=e.map(function(e){var t=fe.get(e);return null==t?void 0:t.value}).filter(function(e){return void 0!==e}));var n=Array.from(new Set([].concat((0,a.A)(Se),(0,a.A)(t))));Pe(n),n.forEach(function(e){Ye(e,!0)})},dropdownMatchSelectWidth:S,OptionList:Be,emptyOptions:!Me.length,activeValue:Ne,activeDescendantId:"".concat(te,"_list_").concat(_e)})))});var Qe=Ye;Qe.Option=oe,Qe.OptGroup=te;var Ze=Qe,Je=n(275),et=n(3723),tt=n(3425),nt=n(8182),ot=n(2279),rt=n(7308);var it=e=>{const{componentName:t}=e,{getPrefixCls:n}=(0,o.useContext)(ot.QO),r=n("empty");switch(t){case"Table":case"List":return o.createElement(rt.A,{image:rt.A.PRESENTED_IMAGE_SIMPLE});case"Select":case"TreeSelect":case"Cascader":case"Transfer":case"Mentions":return o.createElement(rt.A,{image:rt.A.PRESENTED_IMAGE_SIMPLE,className:`${r}-small`});case"Table.filter":return null;default:return o.createElement(rt.A,null)}},lt=n(8119),at=n(934),st=n(829),ct=n(4241),ut=n(124),dt=n(6327),ft=n(1320);var pt=function(e,t){return e||(e=>{const t={overflow:{adjustX:!0,adjustY:!0,shiftY:!0},htmlRegion:"scroll"===e?"scroll":"visible",dynamicInset:!0};return{bottomLeft:Object.assign(Object.assign({},t),{points:["tl","bl"],offset:[0,4]}),bottomRight:Object.assign(Object.assign({},t),{points:["tr","br"],offset:[0,4]}),topLeft:Object.assign(Object.assign({},t),{points:["bl","tl"],offset:[0,-4]}),topRight:Object.assign(Object.assign({},t),{points:["br","tr"],offset:[0,-4]})}})(t)},mt=n(5905),ht=n(5974),gt=n(7358),vt=n(4277),bt=n(3561),yt=n(4211);const xt=e=>{const{optionHeight:t,optionFontSize:n,optionLineHeight:o,optionPadding:r}=e;return{position:"relative",display:"block",minHeight:t,padding:r,color:e.colorText,fontWeight:"normal",fontSize:n,lineHeight:o,boxSizing:"border-box"}};var wt=e=>{const{antCls:t,componentCls:n}=e,o=`${n}-item`,r=`&${t}-slide-up-enter${t}-slide-up-enter-active`,i=`&${t}-slide-up-appear${t}-slide-up-appear-active`,l=`&${t}-slide-up-leave${t}-slide-up-leave-active`,a=`${n}-dropdown-placement-`,s=`${o}-option-selected`;return[{[`${n}-dropdown`]:Object.assign(Object.assign({},(0,mt.dF)(e)),{position:"absolute",top:-9999,zIndex:e.zIndexPopup,boxSizing:"border-box",padding:e.paddingXXS,overflow:"hidden",fontSize:e.fontSize,fontVariant:"initial",backgroundColor:e.colorBgElevated,borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary,[`\n          ${r}${a}bottomLeft,\n          ${i}${a}bottomLeft\n        `]:{animationName:bt.ox},[`\n          ${r}${a}topLeft,\n          ${i}${a}topLeft,\n          ${r}${a}topRight,\n          ${i}${a}topRight\n        `]:{animationName:bt.nP},[`${l}${a}bottomLeft`]:{animationName:bt.vR},[`\n          ${l}${a}topLeft,\n          ${l}${a}topRight\n        `]:{animationName:bt.YU},"&-hidden":{display:"none"},[o]:Object.assign(Object.assign({},xt(e)),{cursor:"pointer",transition:`background ${e.motionDurationSlow} ease`,borderRadius:e.borderRadiusSM,"&-group":{color:e.colorTextDescription,fontSize:e.fontSizeSM,cursor:"default"},"&-option":{display:"flex","&-content":Object.assign({flex:"auto"},mt.L9),"&-state":{flex:"none",display:"flex",alignItems:"center"},[`&-active:not(${o}-option-disabled)`]:{backgroundColor:e.optionActiveBg},[`&-selected:not(${o}-option-disabled)`]:{color:e.optionSelectedColor,fontWeight:e.optionSelectedFontWeight,backgroundColor:e.optionSelectedBg,[`${o}-option-state`]:{color:e.colorPrimary}},"&-disabled":{[`&${o}-option-selected`]:{backgroundColor:e.colorBgContainerDisabled},color:e.colorTextDisabled,cursor:"not-allowed"},"&-grouped":{paddingInlineStart:e.calc(e.controlPaddingHorizontal).mul(2).equal()}},"&-empty":Object.assign(Object.assign({},xt(e)),{color:e.colorTextDisabled})}),[`${s}:has(+ ${s})`]:{borderEndStartRadius:0,borderEndEndRadius:0,[`& + ${s}`]:{borderStartStartRadius:0,borderStartEndRadius:0}},"&-rtl":{direction:"rtl"}})},(0,bt._j)(e,"slide-up"),(0,bt._j)(e,"slide-down"),(0,yt.Mh)(e,"move-up"),(0,yt.Mh)(e,"move-down")]},Ct=n(2187);const kt=e=>{const{componentCls:t,iconCls:n,borderRadiusSM:o,motionDurationSlow:r,paddingXS:i,multipleItemColorDisabled:l,multipleItemBorderColorDisabled:a,colorIcon:s,colorIconHover:c,INTERNAL_FIXED_ITEM_MARGIN:u}=e,d=`${t}-selection-overflow`;return{[d]:{position:"relative",display:"flex",flex:"auto",flexWrap:"wrap",maxWidth:"100%","&-item":{flex:"none",alignSelf:"center",maxWidth:"calc(100% - 4px)",display:"inline-flex"},[`${t}-selection-item`]:{display:"flex",alignSelf:"center",flex:"none",boxSizing:"border-box",maxWidth:"100%",marginBlock:u,borderRadius:o,cursor:"default",transition:`font-size ${r}, line-height ${r}, height ${r}`,marginInlineEnd:e.calc(u).mul(2).equal(),paddingInlineStart:i,paddingInlineEnd:e.calc(i).div(2).equal(),[`${t}-disabled&`]:{color:l,borderColor:a,cursor:"not-allowed"},"&-content":{display:"inline-block",marginInlineEnd:e.calc(i).div(2).equal(),overflow:"hidden",whiteSpace:"pre",textOverflow:"ellipsis"},"&-remove":Object.assign(Object.assign({},(0,mt.Nk)()),{display:"inline-flex",alignItems:"center",color:s,fontWeight:"bold",fontSize:10,lineHeight:"inherit",cursor:"pointer",[`> ${n}`]:{verticalAlign:"-0.2em"},"&:hover":{color:c}})}}}},$t=(e,t)=>{const{componentCls:n,INTERNAL_FIXED_ITEM_MARGIN:o}=e,r=`${n}-selection-overflow`,i=e.multipleSelectItemHeight,l=(e=>{const{multipleSelectItemHeight:t,selectHeight:n,lineWidth:o}=e;return e.calc(n).sub(t).div(2).sub(o).equal()})(e),a=t?`${n}-${t}`:"",s=(e=>{const{multipleSelectItemHeight:t,paddingXXS:n,lineWidth:o,INTERNAL_FIXED_ITEM_MARGIN:r}=e,i=e.max(e.calc(n).sub(o).equal(),0);return{basePadding:i,containerPadding:e.max(e.calc(i).sub(r).equal(),0),itemHeight:(0,Ct.zA)(t),itemLineHeight:(0,Ct.zA)(e.calc(t).sub(e.calc(e.lineWidth).mul(2)).equal())}})(e);return{[`${n}-multiple${a}`]:Object.assign(Object.assign({},kt(e)),{[`${n}-selector`]:{display:"flex",alignItems:"center",width:"100%",height:"100%",paddingInline:s.basePadding,paddingBlock:s.containerPadding,borderRadius:e.borderRadius,[`${n}-disabled&`]:{background:e.multipleSelectorBgDisabled,cursor:"not-allowed"},"&:after":{display:"inline-block",width:0,margin:`${(0,Ct.zA)(o)} 0`,lineHeight:(0,Ct.zA)(i),visibility:"hidden",content:'"\\a0"'}},[`${n}-selection-item`]:{height:s.itemHeight,lineHeight:(0,Ct.zA)(s.itemLineHeight)},[`${n}-selection-wrap`]:{alignSelf:"flex-start","&:after":{lineHeight:(0,Ct.zA)(i),marginBlock:o}},[`${n}-prefix`]:{marginInlineStart:e.calc(e.inputPaddingHorizontalBase).sub(s.basePadding).equal()},[`${r}-item + ${r}-item,\n        ${n}-prefix + ${n}-selection-wrap\n      `]:{[`${n}-selection-search`]:{marginInlineStart:0},[`${n}-selection-placeholder`]:{insetInlineStart:0}},[`${r}-item-suffix`]:{minHeight:s.itemHeight,marginBlock:o},[`${n}-selection-search`]:{display:"inline-flex",position:"relative",maxWidth:"100%",marginInlineStart:e.calc(e.inputPaddingHorizontalBase).sub(l).equal(),"\n          &-input,\n          &-mirror\n        ":{height:i,fontFamily:e.fontFamily,lineHeight:(0,Ct.zA)(i),transition:`all ${e.motionDurationSlow}`},"&-input":{width:"100%",minWidth:4.1},"&-mirror":{position:"absolute",top:0,insetInlineStart:0,insetInlineEnd:"auto",zIndex:999,whiteSpace:"pre",visibility:"hidden"}},[`${n}-selection-placeholder`]:{position:"absolute",top:"50%",insetInlineStart:e.calc(e.inputPaddingHorizontalBase).sub(s.basePadding).equal(),insetInlineEnd:e.inputPaddingHorizontalBase,transform:"translateY(-50%)",transition:`all ${e.motionDurationSlow}`}})}};function St(e,t){const{componentCls:n}=e,o=t?`${n}-${t}`:"",r={[`${n}-multiple${o}`]:{fontSize:e.fontSize,[`${n}-selector`]:{[`${n}-show-search&`]:{cursor:"text"}},[`\n        &${n}-show-arrow ${n}-selector,\n        &${n}-allow-clear ${n}-selector\n      `]:{paddingInlineEnd:e.calc(e.fontSizeIcon).add(e.controlPaddingHorizontal).equal()}}};return[$t(e,t),r]}var At=e=>{const{componentCls:t}=e,n=(0,vt.oX)(e,{selectHeight:e.controlHeightSM,multipleSelectItemHeight:e.multipleItemHeightSM,borderRadius:e.borderRadiusSM,borderRadiusSM:e.borderRadiusXS}),o=(0,vt.oX)(e,{fontSize:e.fontSizeLG,selectHeight:e.controlHeightLG,multipleSelectItemHeight:e.multipleItemHeightLG,borderRadius:e.borderRadiusLG,borderRadiusSM:e.borderRadius});return[St(e),St(n,"sm"),{[`${t}-multiple${t}-sm`]:{[`${t}-selection-placeholder`]:{insetInline:e.calc(e.controlPaddingHorizontalSM).sub(e.lineWidth).equal()},[`${t}-selection-search`]:{marginInlineStart:2}}},St(o,"lg")]};function Et(e,t){const{componentCls:n,inputPaddingHorizontalBase:o,borderRadius:r}=e,i=e.calc(e.controlHeight).sub(e.calc(e.lineWidth).mul(2)).equal(),l=t?`${n}-${t}`:"";return{[`${n}-single${l}`]:{fontSize:e.fontSize,height:e.controlHeight,[`${n}-selector`]:Object.assign(Object.assign({},(0,mt.dF)(e,!0)),{display:"flex",borderRadius:r,flex:"1 1 auto",[`${n}-selection-wrap:after`]:{lineHeight:(0,Ct.zA)(i)},[`${n}-selection-search`]:{position:"absolute",inset:0,width:"100%","&-input":{width:"100%",WebkitAppearance:"textfield"}},[`\n          ${n}-selection-item,\n          ${n}-selection-placeholder\n        `]:{display:"block",padding:0,lineHeight:(0,Ct.zA)(i),transition:`all ${e.motionDurationSlow}, visibility 0s`,alignSelf:"center"},[`${n}-selection-placeholder`]:{transition:"none",pointerEvents:"none"},[["&:after",`${n}-selection-item:empty:after`,`${n}-selection-placeholder:empty:after`].join(",")]:{display:"inline-block",width:0,visibility:"hidden",content:'"\\a0"'}}),[`\n        &${n}-show-arrow ${n}-selection-item,\n        &${n}-show-arrow ${n}-selection-search,\n        &${n}-show-arrow ${n}-selection-placeholder\n      `]:{paddingInlineEnd:e.showArrowPaddingInlineEnd},[`&${n}-open ${n}-selection-item`]:{color:e.colorTextPlaceholder},[`&:not(${n}-customize-input)`]:{[`${n}-selector`]:{width:"100%",height:"100%",alignItems:"center",padding:`0 ${(0,Ct.zA)(o)}`,[`${n}-selection-search-input`]:{height:i,fontSize:e.fontSize},"&:after":{lineHeight:(0,Ct.zA)(i)}}},[`&${n}-customize-input`]:{[`${n}-selector`]:{"&:after":{display:"none"},[`${n}-selection-search`]:{position:"static",width:"100%"},[`${n}-selection-placeholder`]:{position:"absolute",insetInlineStart:0,insetInlineEnd:0,padding:`0 ${(0,Ct.zA)(o)}`,"&:after":{display:"none"}}}}}}}function It(e){const{componentCls:t}=e,n=e.calc(e.controlPaddingHorizontalSM).sub(e.lineWidth).equal();return[Et(e),Et((0,vt.oX)(e,{controlHeight:e.controlHeightSM,borderRadius:e.borderRadiusSM}),"sm"),{[`${t}-single${t}-sm`]:{[`&:not(${t}-customize-input)`]:{[`${t}-selector`]:{padding:`0 ${(0,Ct.zA)(n)}`},[`&${t}-show-arrow ${t}-selection-search`]:{insetInlineEnd:e.calc(n).add(e.calc(e.fontSize).mul(1.5)).equal()},[`\n            &${t}-show-arrow ${t}-selection-item,\n            &${t}-show-arrow ${t}-selection-placeholder\n          `]:{paddingInlineEnd:e.calc(e.fontSize).mul(1.5).equal()}}}},Et((0,vt.oX)(e,{controlHeight:e.singleItemHeightLG,fontSize:e.fontSizeLG,borderRadius:e.borderRadiusLG}),"lg")]}const Ot=(e,t)=>{const{componentCls:n,antCls:o,controlOutlineWidth:r}=e;return{[`&:not(${n}-customize-input) ${n}-selector`]:{border:`${(0,Ct.zA)(e.lineWidth)} ${e.lineType} ${t.borderColor}`,background:e.selectorBg},[`&:not(${n}-disabled):not(${n}-customize-input):not(${o}-pagination-size-changer)`]:{[`&:hover ${n}-selector`]:{borderColor:t.hoverBorderHover},[`${n}-focused& ${n}-selector`]:{borderColor:t.activeBorderColor,boxShadow:`0 0 0 ${(0,Ct.zA)(r)} ${t.activeOutlineColor}`,outline:0},[`${n}-prefix`]:{color:t.color}}}},zt=(e,t)=>({[`&${e.componentCls}-status-${t.status}`]:Object.assign({},Ot(e,t))}),Mt=e=>({"&-outlined":Object.assign(Object.assign(Object.assign(Object.assign({},Ot(e,{borderColor:e.colorBorder,hoverBorderHover:e.hoverBorderColor,activeBorderColor:e.activeBorderColor,activeOutlineColor:e.activeOutlineColor,color:e.colorText})),zt(e,{status:"error",borderColor:e.colorError,hoverBorderHover:e.colorErrorHover,activeBorderColor:e.colorError,activeOutlineColor:e.colorErrorOutline,color:e.colorError})),zt(e,{status:"warning",borderColor:e.colorWarning,hoverBorderHover:e.colorWarningHover,activeBorderColor:e.colorWarning,activeOutlineColor:e.colorWarningOutline,color:e.colorWarning})),{[`&${e.componentCls}-disabled`]:{[`&:not(${e.componentCls}-customize-input) ${e.componentCls}-selector`]:{background:e.colorBgContainerDisabled,color:e.colorTextDisabled}},[`&${e.componentCls}-multiple ${e.componentCls}-selection-item`]:{background:e.multipleItemBg,border:`${(0,Ct.zA)(e.lineWidth)} ${e.lineType} ${e.multipleItemBorderColor}`}})}),Pt=(e,t)=>{const{componentCls:n,antCls:o}=e;return{[`&:not(${n}-customize-input) ${n}-selector`]:{background:t.bg,border:`${(0,Ct.zA)(e.lineWidth)} ${e.lineType} transparent`,color:t.color},[`&:not(${n}-disabled):not(${n}-customize-input):not(${o}-pagination-size-changer)`]:{[`&:hover ${n}-selector`]:{background:t.hoverBg},[`${n}-focused& ${n}-selector`]:{background:e.selectorBg,borderColor:t.activeBorderColor,outline:0}}}},Tt=(e,t)=>({[`&${e.componentCls}-status-${t.status}`]:Object.assign({},Pt(e,t))}),jt=e=>({"&-filled":Object.assign(Object.assign(Object.assign(Object.assign({},Pt(e,{bg:e.colorFillTertiary,hoverBg:e.colorFillSecondary,activeBorderColor:e.activeBorderColor,color:e.colorText})),Tt(e,{status:"error",bg:e.colorErrorBg,hoverBg:e.colorErrorBgHover,activeBorderColor:e.colorError,color:e.colorError})),Tt(e,{status:"warning",bg:e.colorWarningBg,hoverBg:e.colorWarningBgHover,activeBorderColor:e.colorWarning,color:e.colorWarning})),{[`&${e.componentCls}-disabled`]:{[`&:not(${e.componentCls}-customize-input) ${e.componentCls}-selector`]:{borderColor:e.colorBorder,background:e.colorBgContainerDisabled,color:e.colorTextDisabled}},[`&${e.componentCls}-multiple ${e.componentCls}-selection-item`]:{background:e.colorBgContainer,border:`${(0,Ct.zA)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`}})}),Nt=e=>({"&-borderless":{[`${e.componentCls}-selector`]:{background:"transparent",border:`${(0,Ct.zA)(e.lineWidth)} ${e.lineType} transparent`},[`&${e.componentCls}-disabled`]:{[`&:not(${e.componentCls}-customize-input) ${e.componentCls}-selector`]:{color:e.colorTextDisabled}},[`&${e.componentCls}-multiple ${e.componentCls}-selection-item`]:{background:e.multipleItemBg,border:`${(0,Ct.zA)(e.lineWidth)} ${e.lineType} ${e.multipleItemBorderColor}`},[`&${e.componentCls}-status-error`]:{[`${e.componentCls}-prefix, ${e.componentCls}-selection-item`]:{color:e.colorError}},[`&${e.componentCls}-status-warning`]:{[`${e.componentCls}-prefix, ${e.componentCls}-selection-item`]:{color:e.colorWarning}}}}),Bt=(e,t)=>{const{componentCls:n,antCls:o}=e;return{[`&:not(${n}-customize-input) ${n}-selector`]:{borderWidth:`0 0 ${(0,Ct.zA)(e.lineWidth)} 0`,borderStyle:`none none ${e.lineType} none`,borderColor:t.borderColor,background:e.selectorBg,borderRadius:0},[`&:not(${n}-disabled):not(${n}-customize-input):not(${o}-pagination-size-changer)`]:{[`&:hover ${n}-selector`]:{borderColor:t.hoverBorderHover},[`${n}-focused& ${n}-selector`]:{borderColor:t.activeBorderColor,outline:0},[`${n}-prefix`]:{color:t.color}}}},Rt=(e,t)=>({[`&${e.componentCls}-status-${t.status}`]:Object.assign({},Bt(e,t))}),Dt=e=>({"&-underlined":Object.assign(Object.assign(Object.assign(Object.assign({},Bt(e,{borderColor:e.colorBorder,hoverBorderHover:e.hoverBorderColor,activeBorderColor:e.activeBorderColor,activeOutlineColor:e.activeOutlineColor,color:e.colorText})),Rt(e,{status:"error",borderColor:e.colorError,hoverBorderHover:e.colorErrorHover,activeBorderColor:e.colorError,activeOutlineColor:e.colorErrorOutline,color:e.colorError})),Rt(e,{status:"warning",borderColor:e.colorWarning,hoverBorderHover:e.colorWarningHover,activeBorderColor:e.colorWarning,activeOutlineColor:e.colorWarningOutline,color:e.colorWarning})),{[`&${e.componentCls}-disabled`]:{[`&:not(${e.componentCls}-customize-input) ${e.componentCls}-selector`]:{color:e.colorTextDisabled}},[`&${e.componentCls}-multiple ${e.componentCls}-selection-item`]:{background:e.multipleItemBg,border:`${(0,Ct.zA)(e.lineWidth)} ${e.lineType} ${e.multipleItemBorderColor}`}})});var Lt=e=>({[e.componentCls]:Object.assign(Object.assign(Object.assign(Object.assign({},Mt(e)),jt(e)),Nt(e)),Dt(e))});const Ht=e=>{const{componentCls:t}=e;return{position:"relative",transition:`all ${e.motionDurationMid} ${e.motionEaseInOut}`,input:{cursor:"pointer"},[`${t}-show-search&`]:{cursor:"text",input:{cursor:"auto",color:"inherit",height:"100%"}},[`${t}-disabled&`]:{cursor:"not-allowed",input:{cursor:"not-allowed"}}}},Ft=e=>{const{componentCls:t}=e;return{[`${t}-selection-search-input`]:{margin:0,padding:0,background:"transparent",border:"none",outline:"none",appearance:"none",fontFamily:"inherit","&::-webkit-search-cancel-button":{display:"none",appearance:"none"}}}},_t=e=>{const{antCls:t,componentCls:n,inputPaddingHorizontalBase:o,iconCls:r}=e,i={[`${n}-clear`]:{opacity:1,background:e.colorBgBase,borderRadius:"50%"}};return{[n]:Object.assign(Object.assign({},(0,mt.dF)(e)),{position:"relative",display:"inline-flex",cursor:"pointer",[`&:not(${n}-customize-input) ${n}-selector`]:Object.assign(Object.assign({},Ht(e)),Ft(e)),[`${n}-selection-item`]:Object.assign(Object.assign({flex:1,fontWeight:"normal",position:"relative",userSelect:"none"},mt.L9),{[`> ${t}-typography`]:{display:"inline"}}),[`${n}-selection-placeholder`]:Object.assign(Object.assign({},mt.L9),{flex:1,color:e.colorTextPlaceholder,pointerEvents:"none"}),[`${n}-arrow`]:Object.assign(Object.assign({},(0,mt.Nk)()),{position:"absolute",top:"50%",insetInlineStart:"auto",insetInlineEnd:o,height:e.fontSizeIcon,marginTop:e.calc(e.fontSizeIcon).mul(-1).div(2).equal(),color:e.colorTextQuaternary,fontSize:e.fontSizeIcon,lineHeight:1,textAlign:"center",pointerEvents:"none",display:"flex",alignItems:"center",transition:`opacity ${e.motionDurationSlow} ease`,[r]:{verticalAlign:"top",transition:`transform ${e.motionDurationSlow}`,"> svg":{verticalAlign:"top"},[`&:not(${n}-suffix)`]:{pointerEvents:"auto"}},[`${n}-disabled &`]:{cursor:"not-allowed"},"> *:not(:last-child)":{marginInlineEnd:8}}),[`${n}-selection-wrap`]:{display:"flex",width:"100%",position:"relative",minWidth:0,"&:after":{content:'"\\a0"',width:0,overflow:"hidden"}},[`${n}-prefix`]:{flex:"none",marginInlineEnd:e.selectAffixPadding},[`${n}-clear`]:{position:"absolute",top:"50%",insetInlineStart:"auto",insetInlineEnd:o,zIndex:1,display:"inline-block",width:e.fontSizeIcon,height:e.fontSizeIcon,marginTop:e.calc(e.fontSizeIcon).mul(-1).div(2).equal(),color:e.colorTextQuaternary,fontSize:e.fontSizeIcon,fontStyle:"normal",lineHeight:1,textAlign:"center",textTransform:"none",cursor:"pointer",opacity:0,transition:`color ${e.motionDurationMid} ease, opacity ${e.motionDurationSlow} ease`,textRendering:"auto",transform:"translateZ(0)","&:before":{display:"block"},"&:hover":{color:e.colorIcon}},"@media(hover:none)":i,"&:hover":i}),[`${n}-status`]:{"&-error, &-warning, &-success, &-validating":{[`&${n}-has-feedback`]:{[`${n}-clear`]:{insetInlineEnd:e.calc(o).add(e.fontSize).add(e.paddingXS).equal()}}}}}},Wt=e=>{const{componentCls:t}=e;return[{[t]:{[`&${t}-in-form-item`]:{width:"100%"}}},_t(e),It(e),At(e),wt(e),{[`${t}-rtl`]:{direction:"rtl"}},(0,ht.G)(e,{borderElCls:`${t}-selector`,focusElCls:`${t}-focused`})]};var Vt=(0,gt.OF)("Select",(e,{rootPrefixCls:t})=>{const n=(0,vt.oX)(e,{rootPrefixCls:t,inputPaddingHorizontalBase:e.calc(e.paddingSM).sub(1).equal(),multipleSelectItemHeight:e.multipleItemHeight,selectHeight:e.controlHeight});return[Wt(n),Lt(n)]},e=>{const{fontSize:t,lineHeight:n,lineWidth:o,controlHeight:r,controlHeightSM:i,controlHeightLG:l,paddingXXS:a,controlPaddingHorizontal:s,zIndexPopupBase:c,colorText:u,fontWeightStrong:d,controlItemBgActive:f,controlItemBgHover:p,colorBgContainer:m,colorFillSecondary:h,colorBgContainerDisabled:g,colorTextDisabled:v,colorPrimaryHover:b,colorPrimary:y,controlOutline:x}=e,w=2*a,C=2*o,k=Math.min(r-w,r-C),$=Math.min(i-w,i-C),S=Math.min(l-w,l-C);return{INTERNAL_FIXED_ITEM_MARGIN:Math.floor(a/2),zIndexPopup:c+50,optionSelectedColor:u,optionSelectedFontWeight:d,optionSelectedBg:f,optionActiveBg:p,optionPadding:`${(r-t*n)/2}px ${s}px`,optionFontSize:t,optionLineHeight:n,optionHeight:r,selectorBg:m,clearBg:m,singleItemHeightLG:l,multipleItemBg:h,multipleItemBorderColor:"transparent",multipleItemHeight:k,multipleItemHeightSM:$,multipleItemHeightLG:S,multipleSelectorBgDisabled:g,multipleItemColorDisabled:v,multipleItemBorderColorDisabled:"transparent",showArrowPaddingInlineEnd:Math.ceil(1.25*e.fontSize),hoverBorderColor:b,activeBorderColor:y,activeOutlineColor:x,selectAffixPadding:a}},{unitless:{optionLineHeight:!0,optionSelectedFontWeight:!0}}),qt=n(6067),Ut=n(6029),Xt=n(7852),Gt=n(4103),Kt=n(3567),Yt=n(2877);var Qt=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const Zt="SECRET_COMBOBOX_MODE_DO_NOT_USE",Jt=(e,t)=>{var n,r,l,a,s;const{prefixCls:c,bordered:u,className:d,rootClassName:f,getPopupContainer:p,popupClassName:m,dropdownClassName:h,listHeight:g=256,placement:v,listItemHeight:b,size:y,disabled:x,notFoundContent:w,status:C,builtinPlacements:k,dropdownMatchSelectWidth:$,popupMatchSelectWidth:S,direction:A,style:E,allowClear:I,variant:O,dropdownStyle:z,transitionName:M,tagRender:P,maxCount:T,prefix:j,dropdownRender:N,popupRender:B,onDropdownVisibleChange:R,onOpenChange:D,styles:L,classNames:H}=e,F=Qt(e,["prefixCls","bordered","className","rootClassName","getPopupContainer","popupClassName","dropdownClassName","listHeight","placement","listItemHeight","size","disabled","notFoundContent","status","builtinPlacements","dropdownMatchSelectWidth","popupMatchSelectWidth","direction","style","allowClear","variant","dropdownStyle","transitionName","tagRender","maxCount","prefix","dropdownRender","popupRender","onDropdownVisibleChange","onOpenChange","styles","classNames"]),{getPopupContainer:_,getPrefixCls:W,renderEmpty:V,direction:q,virtual:U,popupMatchSelectWidth:X,popupOverflow:G}=o.useContext(ot.QO),{showSearch:K,style:Y,styles:Q,className:Z,classNames:J}=(0,ot.TP)("select"),[,ee]=(0,ft.Ay)(),te=null!=b?b:null==ee?void 0:ee.controlHeight,ne=W("select",c),oe=W(),re=null!=A?A:q,{compactSize:le,compactItemClassnames:ae}=(0,dt.RQ)(ne,re),[se,ce]=(0,ut.A)("select",O,u),ue=(0,at.A)(ne),[de,fe,pe]=Vt(ne,ue),me=o.useMemo(()=>{const{mode:t}=e;if("combobox"!==t)return t===Zt?"combobox":t},[e.mode]),he="multiple"===me||"tags"===me,ge=function(e,t){return void 0!==t?t:null!==e}(e.suffixIcon,e.showArrow),ve=null!==(n=null!=S?S:$)&&void 0!==n?n:X,be=(null===(r=null==L?void 0:L.popup)||void 0===r?void 0:r.root)||(null===(l=Q.popup)||void 0===l?void 0:l.root)||z,ye=B||N,xe=D||R,{status:we,hasFeedback:Ce,isFormItemInput:ke,feedbackIcon:$e}=o.useContext(ct.$W),Se=(0,nt.v)(we,C);let Ae;Ae=void 0!==w?w:"combobox"===me?null:(null==V?void 0:V("Select"))||o.createElement(it,{componentName:"Select"});const{suffixIcon:Ee,itemIcon:Ie,removeIcon:Oe,clearIcon:ze}=function({suffixIcon:e,clearIcon:t,menuItemSelectedIcon:n,removeIcon:r,loading:i,multiple:l,hasFeedback:a,prefixCls:s,showSuffixIcon:c,feedbackIcon:u,showArrow:d,componentName:f}){const p=null!=t?t:o.createElement(Ut.A,null),m=t=>null!==e||a||d?o.createElement(o.Fragment,null,!1!==c&&t,a&&u):null;let h=null;if(void 0!==e)h=m(e);else if(i)h=m(o.createElement(Kt.A,{spin:!0}));else{const e=`${s}-suffix`;h=({open:t,showSearch:n})=>m(t&&n?o.createElement(Yt.A,{className:e}):o.createElement(Gt.A,{className:e}))}let g=null;g=void 0!==n?n:l?o.createElement(qt.A,null):null;let v=null;return v=void 0!==r?r:o.createElement(Xt.A,null),{clearIcon:p,suffixIcon:h,itemIcon:g,removeIcon:v}}(Object.assign(Object.assign({},F),{multiple:he,hasFeedback:Ce,feedbackIcon:$e,showSuffixIcon:ge,prefixCls:ne,componentName:"Select"})),Me=!0===I?{clearIcon:ze}:I,Pe=(0,ie.A)(F,["suffixIcon","itemIcon"]),Te=i()((null===(a=null==H?void 0:H.popup)||void 0===a?void 0:a.root)||(null===(s=null==J?void 0:J.popup)||void 0===s?void 0:s.root)||m||h,{[`${ne}-dropdown-${re}`]:"rtl"===re},f,J.root,null==H?void 0:H.root,pe,ue,fe),je=(0,st.A)(e=>{var t;return null!==(t=null!=y?y:le)&&void 0!==t?t:e}),Ne=o.useContext(lt.A),Be=null!=x?x:Ne,Re=i()({[`${ne}-lg`]:"large"===je,[`${ne}-sm`]:"small"===je,[`${ne}-rtl`]:"rtl"===re,[`${ne}-${se}`]:ce,[`${ne}-in-form-item`]:ke},(0,nt.L)(ne,Se,Ce),ae,Z,d,J.root,null==H?void 0:H.root,f,pe,ue,fe),De=o.useMemo(()=>void 0!==v?v:"rtl"===re?"bottomRight":"bottomLeft",[v,re]);const[Le]=(0,Je.YK)("SelectLike",null==be?void 0:be.zIndex);return de(o.createElement(Ze,Object.assign({ref:t,virtual:U,showSearch:K},Pe,{style:Object.assign(Object.assign(Object.assign(Object.assign({},Q.root),null==L?void 0:L.root),Y),E),dropdownMatchSelectWidth:ve,transitionName:(0,et.b)(oe,"slide-up",M),builtinPlacements:pt(k,G),listHeight:g,listItemHeight:te,mode:me,prefixCls:ne,placement:De,direction:re,prefix:j,suffixIcon:Ee,menuItemSelectedIcon:Ie,removeIcon:Oe,allowClear:Me,notFoundContent:Ae,className:Re,getPopupContainer:p||_,dropdownClassName:Te,disabled:Be,dropdownStyle:Object.assign(Object.assign({},be),{zIndex:Le}),maxCount:he?T:void 0,tagRender:he?P:void 0,dropdownRender:ye,onDropdownVisibleChange:xe})))};const en=o.forwardRef(Jt),tn=(0,tt.A)(en,"dropdownAlign");en.SECRET_COMBOBOX_MODE_DO_NOT_USE=Zt,en.Option=oe,en.OptGroup=te,en._InternalPanelDoNotUseOrYouWillBeFired=tn;var nn=en},9492:function(e,t,n){"use strict";n.d(t,{A:function(){return o}});const o=(0,n(1788).A)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},9612:function(e,t,n){"use strict";n.d(t,{A:function(){return o}});const o=(0,n(1788).A)("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])}}]);
//# sourceMappingURL=50b6c9a48db3f2c12a9112e6cf56a481ffb5324c-212575c12d7d62d8d99e.js.map