{"version": 3, "file": "f359c09962c4e52a9d4cb2c90fcf1a14e88c481c-398322329f3c1682e759.js", "mappings": ";sTAGA,MAAMA,EAAoB,CAACC,EAASC,EAAaC,EAAWC,EAAOC,KAAa,CAC9EC,WAAYL,EACZM,OAAQ,IAAG,QAAKH,EAAMI,cAAcJ,EAAMK,YAAYP,IACtD,CAAC,GAAGG,UAAkB,CACpBK,MAAOP,KAGEQ,EAAeP,IAC1B,MAAM,aACJQ,EACAC,mBAAoBC,EAAQ,SAC5BC,EAAQ,SACRC,EAAQ,SACRC,EAAQ,WACRC,EAAU,WACVC,EACAC,eAAgBC,EAAY,oBAC5BC,EAAmB,wBACnBC,EAAuB,UACvBC,EAAS,iBACTC,EAAgB,uBAChBC,EAAsB,eACtBC,GACEvB,EACJ,MAAO,CACL,CAACQ,GAAegB,OAAOC,OAAOD,OAAOC,OAAO,CAAC,GAAG,QAAezB,IAAS,CACtE0B,SAAU,WACVC,QAAS,OACTC,WAAY,SACZC,QAASN,EACTO,SAAU,aACVb,eACA,CAAC,IAAIT,SAAqB,CACxBuB,UAAW,OAEb,CAAC,GAAGvB,aAAyB,CAC3BwB,KAAM,EACNC,SAAU,GAEZ,CAAC,GAAGzB,UAAsB,CACxB0B,gBAAiBvB,EACjBI,WAAY,GAEd,gBAAiB,CACfY,QAAS,OACTd,WACAE,cAEF,YAAa,CACXT,MAAOe,GAET,CAAC,IAAIb,kBAA8B,CACjC2B,SAAU,SACVC,QAAS,EACTC,WAAY,cAAc3B,KAAYQ,cAAgCR,KAAYQ,2BACpER,KAAYQ,qBAAuCR,KAAYQ,6BAC7DR,KAAYQ,KAE9B,CAAC,IAAIV,yBAAqC,CACxC8B,UAAW,EACXC,aAAc,eACdC,WAAY,EACZC,cAAe,EACfL,QAAS,KAGb,CAAC,GAAG5B,sBAAkC,CACpCoB,WAAY,aACZC,QAASP,EACT,CAAC,GAAGd,UAAsB,CACxB0B,gBAAiBtB,EACjBC,SAAUM,EACVJ,WAAY,GAEd,CAAC,GAAGP,aAAyB,CAC3BmB,QAAS,QACTY,aAAc5B,EACdL,MAAOe,EACPR,SAAUC,GAEZ,CAAC,GAAGN,iBAA6B,CAC/BmB,QAAS,QACTrB,MAAOc,IAGX,CAAC,GAAGZ,YAAwB,CAC1B+B,aAAc,EACdpC,OAAQ,eACRc,aAAc,KAIPyB,EAAe1C,IAC1B,MAAM,aACJQ,EAAY,aACZmC,EAAY,mBACZC,EAAkB,eAClBC,EAAc,aACdC,EAAY,mBACZC,EAAkB,eAClBC,EAAc,WACdC,EAAU,iBACVC,EAAgB,aAChBC,EAAY,UACZC,EAAS,gBACTC,EAAe,YACfC,GACEtD,EACJ,MAAO,CACL,CAACQ,GAAe,CACd,YAAaZ,EAAkBiD,EAAgBD,EAAoBD,EAAc3C,EAAOQ,GACxF,SAAUZ,EAAkB0D,EAAaD,EAAiBD,EAAWpD,EAAOQ,GAC5E,YAAaZ,EAAkBoD,EAAgBD,EAAoBD,EAAc9C,EAAOQ,GACxF,UAAWgB,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAG7B,EAAkBuD,EAAcD,EAAkBD,EAAYjD,EAAOQ,IAAgB,CAC9H,CAAC,GAAGA,uBAAmC,CACrC+C,OAAQ,EACR1B,QAAS,QAMN2B,EAAiBxD,IAC5B,MAAM,aACJQ,EAAY,QACZiD,EAAO,kBACPC,EAAiB,SACjB/C,EAAQ,aACRgD,EAAY,UACZC,EAAS,eACTC,GACE7D,EACJ,MAAO,CACL,CAACQ,GAAe,CACd,WAAY,CACVsD,kBAAmBnD,GAErB,CAAC,GAAGH,gBAA4B,CAC9BsD,kBAAmBnD,EACnBkB,QAAS,EACTM,SAAU,SACVtB,SAAU8C,EACV5C,YAAY,QAAK4C,GACjBI,gBAAiB,cACjB5D,OAAQ,OACR6D,QAAS,OACTC,OAAQ,UACR,CAAC,GAAGR,WAAkB,CACpBnD,MAAOsD,EACPvB,WAAY,SAASqB,IACrB,UAAW,CACTpD,MAAOuD,KAIb,eAAgB,CACdvD,MAAOsD,EACPvB,WAAY,SAASqB,IACrB,UAAW,CACTpD,MAAOuD,OAcjB,OAAe,QAAc,QAAS7D,GAAS,CAACO,EAAaP,GAAQ0C,EAAa1C,GAAQwD,EAAexD,IARpEA,IAE5B,CACLmB,wBAAyBnB,EAAMkE,iBAC/B3C,eAAgB,GAAGvB,EAAMmE,kCACzB7C,uBAAwB,GAAGtB,EAAMoE,eAAepE,EAAMqE,kCC3KtDC,EAAgC,SAAUC,EAAGC,GAC/C,IAAIC,EAAI,CAAC,EACT,IAAK,IAAIC,KAAKH,EAAO/C,OAAOmD,UAAUC,eAAeC,KAAKN,EAAGG,IAAMF,EAAEM,QAAQJ,GAAK,IAAGD,EAAEC,GAAKH,EAAEG,IAC9F,GAAS,MAALH,GAAqD,mBAAjC/C,OAAOuD,sBAA2C,KAAIC,EAAI,EAAb,IAAgBN,EAAIlD,OAAOuD,sBAAsBR,GAAIS,EAAIN,EAAEO,OAAQD,IAClIR,EAAEM,QAAQJ,EAAEM,IAAM,GAAKxD,OAAOmD,UAAUO,qBAAqBL,KAAKN,EAAGG,EAAEM,MAAKP,EAAEC,EAAEM,IAAMT,EAAEG,EAAEM,IADuB,CAGvH,OAAOP,CACT,EAeA,MAAMU,EAAgB,CACpBC,QAASC,EAAA,EACTC,KAAMC,EAAA,EACNC,MAAOC,EAAA,EACPC,QAASC,EAAA,GAELC,EAAWC,IACf,MAAM,KACJC,EAAI,UACJC,EAAS,KACTC,GACEH,EACEI,EAAWd,EAAca,IAAS,KACxC,OAAIF,GACK,QAAeA,EAAmB,gBAAoB,OAAQ,CACnEI,UAAW,GAAGH,UACbD,GAAO,KAAM,CACdI,UAAW,IAAW,GAAGH,SAAkBD,EAAKD,MAAMK,cAGtC,gBAAoBD,EAAU,CAChDC,UAAW,GAAGH,YAGZI,EAAgBN,IACpB,MAAM,WACJO,EAAU,UACVL,EAAS,UACTM,EAAS,YACTC,EAAW,UACXC,GACEV,EACEW,GAAgC,IAAdH,QAAoCI,IAAdJ,EAAuC,gBAAoBK,EAAA,EAAe,MAAQL,EAChI,OAAOD,EAA2B,gBAAoB,SAAU5E,OAAOC,OAAO,CAC5EuE,KAAM,SACNW,QAASL,EACTJ,UAAW,GAAGH,eACda,SAAU,GACTL,GAAYC,GAAoB,MAE/BK,EAAqB,aAAiB,CAAChB,EAAOiB,KAClD,MAAM,YACFC,EACAhB,UAAWiB,EAAkB,QAC7BC,EAAO,OACPC,EAAM,UACNhB,EAAS,cACTiB,EAAa,MACbC,EAAK,aACLC,EAAY,aACZC,EAAY,QACZX,EAAO,WACPY,EAAU,SACVC,EAAQ,SACRC,EAAQ,UACRC,EAAS,UACTrB,EAAS,OACTsB,EAAM,GACNC,GACE/B,EACJgC,EAAavD,EAAOuB,EAAO,CAAC,cAAe,YAAa,UAAW,SAAU,YAAa,gBAAiB,QAAS,eAAgB,eAAgB,UAAW,aAAc,WAAY,WAAY,YAAa,YAAa,SAAU,QACpOiC,EAAQC,GAAa,YAAe,GAK3C,MAAMC,EAAc,SAAa,MACjC,sBAA0BlB,EAAK,KAAM,CACnCmB,cAAeD,EAAYE,WAE7B,MAAM,aACJC,EAAY,UACZpG,EACA0F,SAAUW,EACV/B,UAAWgC,EACXnC,UAAWoC,EACXlB,MAAOmB,IACL,QAAmB,SACjBxC,EAAYoC,EAAa,QAASnB,IACjCwB,EAAYC,EAAQC,GAAa,EAAS3C,GAC3CO,EAAc9B,IAClB,IAAImE,EACJZ,GAAU,GACe,QAAxBY,EAAK9C,EAAM+C,eAA4B,IAAPD,GAAyBA,EAAG9D,KAAKgB,EAAOrB,IAErEwB,EAAO,UAAc,SACNS,IAAfZ,EAAMG,KACDH,EAAMG,KAGRkB,EAAS,UAAY,OAC3B,CAACrB,EAAMG,KAAMkB,IAEVd,EAAa,UAAc,MACP,iBAAbqB,IAAyBA,EAASpB,eACzCqB,IAGoB,kBAAbD,EACFA,GAGS,IAAdpB,SAAuBA,KAGlB+B,IACR,CAACV,EAAWrB,EAAWoB,EAAUW,IAE9BS,KAAa3B,QAAuBT,IAAbe,IAAgCA,EACvDvH,EAAW,IAAW8F,EAAW,GAAGA,KAAaC,IAAQ,CAC7D,CAAC,GAAGD,wBAAiCgB,EACrC,CAAC,GAAGhB,cAAuB8C,EAC3B,CAAC,GAAG9C,cAAuBmB,EAC3B,CAAC,GAAGnB,SAAgC,QAAdhE,GACrBuG,EAAkBpC,EAAWiB,EAAeuB,EAAWD,GACpDK,GAAY,EAAAC,EAAA,GAAUlB,EAAY,CACtCmB,MAAM,EACNC,MAAM,IAEFzC,EAAkB,UAAc,IACZ,iBAAbiB,GAAyBA,EAASpB,UACpCoB,EAASpB,UAEdqB,SAGcjB,IAAdJ,EACKA,EAEsB,iBAApB+B,GAAgCA,EAAgB/B,UAClD+B,EAAgB/B,UAElBgC,GACN,CAAChC,EAAWoB,EAAUC,EAAWW,IAC9Ba,EAAkB,UAAc,KACpC,MAAMC,EAAS1B,QAA2CA,EAAWW,EACrE,GAAsB,iBAAXe,EAAqB,CAC9B,MACI9C,UAAW+C,GACTD,EAEN,OADc7E,EAAO6E,EAAQ,CAAC,aAEhC,CACA,MAAO,CAAC,GACP,CAAC1B,EAAUW,IACd,OAAOI,EAAwB,gBAAoB,KAAW,CAC5Da,SAAUvB,EACVwB,WAAY,GAAGvD,WACfwD,cAAc,EACdC,aAAa,EACbC,aAAcC,IAAQ,CACpBpH,UAAWoH,EAAKC,eAElBC,WAAYrC,GACX,EACDrB,UAAW2D,EACXzC,MAAO0C,GACNC,IAAyB,gBAAoB,MAAOvI,OAAOC,OAAO,CACnEmG,GAAIA,EACJd,KAAK,QAAWkB,EAAa+B,GAC7B,aAAcjC,EACd5B,UAAW,IAAWjG,EAAU4J,GAChCzC,MAAO5F,OAAOC,OAAOD,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAG8G,GAAenB,GAAQ0C,GAC5EzC,aAAcA,EACdC,aAAcA,EACdX,QAASA,EACTqD,KAAM,SACLlB,GAAYD,EAA2B,gBAAoBjD,EAAU,CACtEmB,YAAaA,EACbjB,KAAMD,EAAMC,KACZC,UAAWA,EACXC,KAAMA,IACF,KAAmB,gBAAoB,MAAO,CAClDE,UAAW,GAAGH,aACbkB,EAAuB,gBAAoB,MAAO,CACnDf,UAAW,GAAGH,aACbkB,GAAW,KAAMF,EAA2B,gBAAoB,MAAO,CACxEb,UAAW,GAAGH,iBACbgB,GAAe,MAAOY,EAAsB,gBAAoB,MAAO,CACxEzB,UAAW,GAAGH,YACb4B,GAAU,KAAmB,gBAAoBxB,EAAe,CACjEC,WAAYA,EACZL,UAAWA,EACXM,UAAWG,EACXF,YAAaA,EACbC,UAAW2C,SAMf,Q,gEC/MA,IAAIe,EAA6B,SAAUC,GACzC,SAASD,IACP,IAAIE,ECPY1F,EAAG2F,EAAG5F,EDgBtB,OARA,OAAgB6F,KAAMJ,GCRNxF,EDSG4F,KCTAD,EDSMH,ECTHzF,EDSkB8F,UCRnCF,GAAI,EAAAG,EAAA,GAAeH,IDQxBD,GCR4B,EAAAK,EAAA,GAA0B/F,GAAG,EAAAgG,EAAA,KAA6BC,QAAQC,UAAUP,EAAG5F,GAAK,IAAI,EAAA+F,EAAA,GAAe9F,GAAGmG,aAAeR,EAAES,MAAMpG,EAAGD,KDS1JsG,MAAQ,CACZtF,WAAOiB,EACPnB,KAAM,CACJyF,eAAgB,KAGbZ,CACT,CAEA,OADA,OAAUF,EAAeC,IAClB,OAAaD,EAAe,CAAC,CAClCe,IAAK,oBACLC,MAAO,SAA2BzF,EAAOF,GACvC+E,KAAKa,SAAS,CACZ1F,QACAF,QAEJ,GACC,CACD0F,IAAK,SACLC,MAAO,WACL,MAAM,QACJhE,EAAO,YACPF,EAAW,GACXa,EAAE,SACFuD,GACEd,KAAKxE,OACH,MACJL,EAAK,KACLF,GACE+E,KAAKS,MACHC,GAAkBzF,aAAmC,EAASA,EAAKyF,iBAAmB,KACtFK,OAAkC,IAAZnE,GAA2BzB,GAAS,IAAI6F,WAAapE,EAC3EqE,OAA0C,IAAhBvE,EAA8BgE,EAAiBhE,EAC/E,OAAIvB,EACkB,gBAAoB,EAAO,CAC7CoC,GAAIA,EACJ5B,KAAM,QACNiB,QAASmE,EACTrE,YAA0B,gBAAoB,MAAO,CACnDK,MAAO,CACLvG,SAAU,QACV0K,UAAW,SAEZD,KAGAH,CACT,IAEJ,CAtDiC,CAsD/B,aACF,QE3DA,MAAM,EAAQ,EACd,EAAMlB,cAAgB,EACtB,O,uDCGA,MAAMuB,GAAW,E,QAAA,GAAiB,WAAY,CAC5C,CAAC,OAAQ,CAAEC,EAAG,4CAA6CT,IAAK,WAChE,CAAC,WAAY,CAAEU,OAAQ,mBAAoBV,IAAK,WAChD,CAAC,OAAQ,CAAEW,GAAI,KAAMC,GAAI,KAAMC,GAAI,KAAMC,GAAI,IAAKd,IAAK,Y,sECZzD,SAASe,EAAgBC,EAAKhB,EAAKC,GAYjC,OAXID,KAAOgB,EACTxK,OAAOyK,eAAeD,EAAKhB,EAAK,CAC9BC,MAAOA,EACPiB,YAAY,EACZC,cAAc,EACdC,UAAU,IAGZJ,EAAIhB,GAAOC,EAGNe,CACT,CAEA,SAASK,EAAQC,EAAQC,GACvB,IAAIC,EAAOhL,OAAOgL,KAAKF,GAEvB,GAAI9K,OAAOuD,sBAAuB,CAChC,IAAI0H,EAAUjL,OAAOuD,sBAAsBuH,GACvCC,IAAgBE,EAAUA,EAAQC,OAAO,SAAUC,GACrD,OAAOnL,OAAOoL,yBAAyBN,EAAQK,GAAKT,UACtD,IACAM,EAAKK,KAAKhC,MAAM2B,EAAMC,EACxB,CAEA,OAAOD,CACT,CAEA,SAASM,EAAeC,GACtB,IAAK,IAAI/H,EAAI,EAAGA,EAAIsF,UAAUrF,OAAQD,IAAK,CACzC,IAAIgI,EAAyB,MAAhB1C,UAAUtF,GAAasF,UAAUtF,GAAK,CAAC,EAEhDA,EAAI,EACNqH,EAAQ7K,OAAOwL,IAAS,GAAMC,QAAQ,SAAUjC,GAC9Ce,EAAgBgB,EAAQ/B,EAAKgC,EAAOhC,GACtC,GACSxJ,OAAO0L,0BAChB1L,OAAO2L,iBAAiBJ,EAAQvL,OAAO0L,0BAA0BF,IAEjEX,EAAQ7K,OAAOwL,IAASC,QAAQ,SAAUjC,GACxCxJ,OAAOyK,eAAec,EAAQ/B,EAAKxJ,OAAOoL,yBAAyBI,EAAQhC,GAC7E,EAEJ,CAEA,OAAO+B,CACT,CAiBA,SAASK,EAAyBJ,EAAQK,GACxC,GAAc,MAAVL,EAAgB,MAAO,CAAC,EAE5B,IAEIhC,EAAKhG,EAFL+H,EAlBN,SAAuCC,EAAQK,GAC7C,GAAc,MAAVL,EAAgB,MAAO,CAAC,EAC5B,IAEIhC,EAAKhG,EAFL+H,EAAS,CAAC,EACVO,EAAa9L,OAAOgL,KAAKQ,GAG7B,IAAKhI,EAAI,EAAGA,EAAIsI,EAAWrI,OAAQD,IACjCgG,EAAMsC,EAAWtI,GACbqI,EAASvI,QAAQkG,IAAQ,IAC7B+B,EAAO/B,GAAOgC,EAAOhC,IAGvB,OAAO+B,CACT,CAKeQ,CAA8BP,EAAQK,GAInD,GAAI7L,OAAOuD,sBAAuB,CAChC,IAAIyI,EAAmBhM,OAAOuD,sBAAsBiI,GAEpD,IAAKhI,EAAI,EAAGA,EAAIwI,EAAiBvI,OAAQD,IACvCgG,EAAMwC,EAAiBxI,GACnBqI,EAASvI,QAAQkG,IAAQ,GACxBxJ,OAAOmD,UAAUO,qBAAqBL,KAAKmI,EAAQhC,KACxD+B,EAAO/B,GAAOgC,EAAOhC,GAEzB,CAEA,OAAO+B,CACT,CA8CA,SAASU,EAAkBC,EAAKC,IACnB,MAAPA,GAAeA,EAAMD,EAAIzI,UAAQ0I,EAAMD,EAAIzI,QAE/C,IAAK,IAAID,EAAI,EAAG4I,EAAO,IAAIC,MAAMF,GAAM3I,EAAI2I,EAAK3I,IAAK4I,EAAK5I,GAAK0I,EAAI1I,GAEnE,OAAO4I,CACT,CCvIA,SAAS,EAAgB5B,EAAKhB,EAAKC,GAYjC,OAXID,KAAOgB,EACTxK,OAAOyK,eAAeD,EAAKhB,EAAK,CAC9BC,MAAOA,EACPiB,YAAY,EACZC,cAAc,EACdC,UAAU,IAGZJ,EAAIhB,GAAOC,EAGNe,CACT,CAEA,SAAS,EAAQM,EAAQC,GACvB,IAAIC,EAAOhL,OAAOgL,KAAKF,GAEvB,GAAI9K,OAAOuD,sBAAuB,CAChC,IAAI0H,EAAUjL,OAAOuD,sBAAsBuH,GACvCC,IAAgBE,EAAUA,EAAQC,OAAO,SAAUC,GACrD,OAAOnL,OAAOoL,yBAAyBN,EAAQK,GAAKT,UACtD,IACAM,EAAKK,KAAKhC,MAAM2B,EAAMC,EACxB,CAEA,OAAOD,CACT,CAEA,SAAS,EAAeO,GACtB,IAAK,IAAI/H,EAAI,EAAGA,EAAIsF,UAAUrF,OAAQD,IAAK,CACzC,IAAIgI,EAAyB,MAAhB1C,UAAUtF,GAAasF,UAAUtF,GAAK,CAAC,EAEhDA,EAAI,EACN,EAAQxD,OAAOwL,IAAS,GAAMC,QAAQ,SAAUjC,GAC9C,EAAgB+B,EAAQ/B,EAAKgC,EAAOhC,GACtC,GACSxJ,OAAO0L,0BAChB1L,OAAO2L,iBAAiBJ,EAAQvL,OAAO0L,0BAA0BF,IAEjE,EAAQxL,OAAOwL,IAASC,QAAQ,SAAUjC,GACxCxJ,OAAOyK,eAAec,EAAQ/B,EAAKxJ,OAAOoL,yBAAyBI,EAAQhC,GAC7E,EAEJ,CAEA,OAAO+B,CACT,CAcA,SAASe,EAAMC,GACb,OAAO,SAASC,IAGd,IAFA,IAAI7D,EAAQE,KAEH4D,EAAQ3D,UAAUrF,OAAQiJ,EAAO,IAAIL,MAAMI,GAAQE,EAAQ,EAAGA,EAAQF,EAAOE,IACpFD,EAAKC,GAAS7D,UAAU6D,GAG1B,OAAOD,EAAKjJ,QAAU8I,EAAG9I,OAAS8I,EAAGlD,MAAMR,KAAM6D,GAAQ,WACvD,IAAK,IAAIE,EAAQ9D,UAAUrF,OAAQoJ,EAAW,IAAIR,MAAMO,GAAQE,EAAQ,EAAGA,EAAQF,EAAOE,IACxFD,EAASC,GAAShE,UAAUgE,GAG9B,OAAON,EAAQnD,MAAMV,EAAO,GAAGoE,OAAOL,EAAMG,GAC9C,CACF,CACF,CAEA,SAASG,EAASvD,GAChB,MAAO,CAAC,EAAEI,SAASxG,KAAKoG,GAAOwD,SAAS,SAC1C,CAMA,SAASC,EAAWzD,GAClB,MAAwB,mBAAVA,CAChB,CAmCA,IAWI0D,EAAeb,EAfnB,SAAoBc,EAAe5I,GACjC,MAAM,IAAI6I,MAAMD,EAAc5I,IAAS4I,EAAuB,QAChE,EAamBd,CAXC,CAClBgB,kBAAmB,4BACnBC,YAAa,oCACbC,eAAgB,6CAChBC,YAAa,4CACbC,aAAc,qCACdC,aAAc,gCACdC,WAAY,gDACZC,YAAa,iGACb,QAAW,sDAGTC,EAAa,CACfC,QA1CF,SAAyBC,EAASD,GAKhC,OAJKf,EAASe,IAAUZ,EAAa,cACjCnN,OAAOgL,KAAK+C,GAASE,KAAK,SAAUC,GACtC,OAPoBpD,EAOGkD,EAPKG,EAOID,GAN3BlO,OAAOmD,UAAUC,eAAeC,KAAKyH,EAAQqD,GADtD,IAAwBrD,EAAQqD,CAQ9B,IAAIhB,EAAa,eACVY,CACT,EAqCEK,SAnCF,SAA0BA,GACnBlB,EAAWkB,IAAWjB,EAAa,eAC1C,EAkCEkB,QAhCF,SAAyBA,GACjBnB,EAAWmB,IAAYrB,EAASqB,IAAWlB,EAAa,eAC1DH,EAASqB,IAAYrO,OAAOsO,OAAOD,GAASJ,KAAK,SAAUM,GAC7D,OAAQrB,EAAWqB,EACrB,IAAIpB,EAAa,eACnB,EA4BEa,QA1BF,SAAyBA,GA/BzB,IAAiBxD,EAgCVwD,GAASb,EAAa,qBACtBH,EAASgB,IAAUb,EAAa,eAjCtB3C,EAkCHwD,EAjCJhO,OAAOgL,KAAKR,GAAK/G,QAiCH0J,EAAa,iBACrC,GAoDA,SAASqB,EAAelF,EAAOmF,GAC7B,OAAOvB,EAAWuB,GAAiBA,EAAcnF,EAAM5C,SAAW+H,CACpE,CAEA,SAASC,EAAYpF,EAAOyE,GAE1B,OADAzE,EAAM5C,QAAU,EAAe,EAAe,CAAC,EAAG4C,EAAM5C,SAAUqH,GAC3DA,CACT,CAEA,SAASY,EAAerF,EAAO+E,EAASN,GAMtC,OALAb,EAAWmB,GAAWA,EAAQ/E,EAAM5C,SAAW1G,OAAOgL,KAAK+C,GAAStC,QAAQ,SAAUyC,GACpF,IAAIU,EAEJ,OAA6C,QAArCA,EAAiBP,EAAQH,UAAuC,IAAnBU,OAA4B,EAASA,EAAevL,KAAKgL,EAAS/E,EAAM5C,QAAQwH,GACvI,GACOH,CACT,CAEA,IAAIc,EAAQ,CACVC,OA9CF,SAAgBd,GACd,IAAIK,EAAUvF,UAAUrF,OAAS,QAAsBwB,IAAjB6D,UAAU,GAAmBA,UAAU,GAAK,CAAC,EACnFgF,EAAWE,QAAQA,GACnBF,EAAWO,QAAQA,GACnB,IAAI/E,EAAQ,CACV5C,QAASsH,GAEPe,EAAYzC,EAAMqC,EAANrC,CAAsBhD,EAAO+E,GACzCW,EAAS1C,EAAMoC,EAANpC,CAAmBhD,GAC5B2F,EAAW3C,EAAMwB,EAAWC,QAAjBzB,CAA0B0B,GACrCkB,EAAa5C,EAAMkC,EAANlC,CAAsBhD,GAcvC,MAAO,CAZP,WACE,IAAI8E,EAAWtF,UAAUrF,OAAS,QAAsBwB,IAAjB6D,UAAU,GAAmBA,UAAU,GAAK,SAAUQ,GAC3F,OAAOA,CACT,EAEA,OADAwE,EAAWM,SAASA,GACbA,EAAS9E,EAAM5C,QACxB,EAEA,SAAkB+H,IAlHpB,WACE,IAAK,IAAIU,EAAOrG,UAAUrF,OAAQ2L,EAAM,IAAI/C,MAAM8C,GAAOE,EAAO,EAAGA,EAAOF,EAAME,IAC9ED,EAAIC,GAAQvG,UAAUuG,GAGxB,OAAO,SAAUC,GACf,OAAOF,EAAIG,YAAY,SAAUC,EAAGC,GAClC,OAAOA,EAAED,EACX,EAAGF,EACL,CACF,CAyGII,CAAQX,EAAWC,EAAQC,EAAUC,EAArCQ,CAAiDjB,EACnD,EAGF,GAwBA,IC1LA,EANa,CACXkB,MAAO,CACLC,GAAI,6DCgBR,MAlBA,SAAerD,GACb,OAAO,SAASC,IAGd,IAFA,IAAI7D,EAAQE,KAEHsG,EAAOrG,UAAUrF,OAAQiJ,EAAO,IAAIL,MAAM8C,GAAOE,EAAO,EAAGA,EAAOF,EAAME,IAC/E3C,EAAK2C,GAAQvG,UAAUuG,GAGzB,OAAO3C,EAAKjJ,QAAU8I,EAAG9I,OAAS8I,EAAGlD,MAAMR,KAAM6D,GAAQ,WACvD,IAAK,IAAID,EAAQ3D,UAAUrF,OAAQoJ,EAAW,IAAIR,MAAMI,GAAQE,EAAQ,EAAGA,EAAQF,EAAOE,IACxFE,EAASF,GAAS7D,UAAU6D,GAG9B,OAAOH,EAAQnD,MAAMV,EAAO,GAAGoE,OAAOL,EAAMG,GAC9C,CACF,CACF,ECZA,MAJA,SAAkBpD,GAChB,MAAO,CAAC,EAAEI,SAASxG,KAAKoG,GAAOwD,SAAS,SAC1C,ECmCA,IAAI,EAAgB,CAClB4C,iBAAkB,uCAClBC,WAAY,+CACZ,QAAW,8DACXC,YAAa,iTAEX,EAAe,EAVnB,SAAoB3C,EAAe5I,GACjC,MAAM,IAAI6I,MAAMD,EAAc5I,IAAS4I,EAAuB,QAChE,EAQmB,CAAkB,GACjC,EAAa,CACf4C,OApCF,SAAwBA,GAItB,OAHKA,GAAQ,EAAa,oBACrB,EAASA,IAAS,EAAa,cAEhCA,EAAOC,MAiBXC,QAAQC,KAAK,EAAcJ,aAflB,CACLJ,MAAO,CACLC,GAAII,EAAOC,KAAKG,cAKfJ,CACT,GAyBA,ICpCA,EAZc,WACZ,IAAK,IAAIb,EAAOrG,UAAUrF,OAAQ2L,EAAM,IAAI/C,MAAM8C,GAAOE,EAAO,EAAGA,EAAOF,EAAME,IAC9ED,EAAIC,GAAQvG,UAAUuG,GAGxB,OAAO,SAAUC,GACf,OAAOF,EAAIG,YAAY,SAAUC,EAAGC,GAClC,OAAOA,EAAED,EACX,EAAGF,EACL,CACF,ECGA,MAXA,SAASe,EAAM9E,EAAQC,GAQrB,OAPAxL,OAAOgL,KAAKQ,GAAQC,QAAQ,SAAUjC,GAChCgC,EAAOhC,aAAgBxJ,QACrBuL,EAAO/B,IACTxJ,OAAOC,OAAOuL,EAAOhC,GAAM6G,EAAM9E,EAAO/B,GAAMgC,EAAOhC,IAG3D,GACO8B,EAAeA,EAAe,CAAC,EAAGC,GAASC,EACpD,ECVI8E,EAAsB,CACxB9L,KAAM,cACN+L,IAAK,kCAgBP,IRkEwBrE,EAAK1I,EQlE7B,EAbA,SAAwBgN,GACtB,IAAIC,GAAe,EACfC,EAAiB,IAAIC,QAAQ,SAAUC,EAASC,GAClDL,EAAQM,KAAK,SAAUC,GACrB,OAAON,EAAeI,EAAOP,GAAuBM,EAAQG,EAC9D,GACAP,EAAe,MAAEK,EACnB,GACA,OAAOH,EAAeM,OAAS,WAC7B,OAAOP,GAAe,CACxB,EAAGC,CACL,ECPIO,EAAgB,EAAMnC,OAAO,CAC/BkB,OAAQ,EACRkB,eAAe,EACfN,QAAS,KACTC,OAAQ,KACRM,OAAQ,OAENC,GToEyB5N,ESpEsB,ETwEnD,SAAyB0I,GACvB,GAAIG,MAAMgF,QAAQnF,GAAM,OAAOA,CACjC,CALSoF,CADepF,ESpEY+E,IT4EpC,SAA+B/E,EAAK1I,GAClC,GAAsB,oBAAX+N,QAA4BA,OAAOC,YAAYxR,OAAOkM,GAAjE,CACA,IAAIuF,EAAO,GACPC,GAAK,EACLC,GAAK,EACLC,OAAK3M,EAET,IACE,IAAK,IAAiC4M,EAA7BC,EAAK5F,EAAIqF,OAAOC,cAAmBE,GAAMG,EAAKC,EAAGC,QAAQC,QAChEP,EAAKpG,KAAKwG,EAAGpI,QAETjG,GAAKiO,EAAKhO,SAAWD,GAH8CkO,GAAK,GAKhF,CAAE,MAAOO,GACPN,GAAK,EACLC,EAAKK,CACP,CAAE,QACA,IACOP,GAAsB,MAAhBI,EAAW,QAAWA,EAAW,QAC9C,CAAE,QACA,GAAIH,EAAI,MAAMC,CAChB,CACF,CAEA,OAAOH,CAvBuE,CAwBhF,CAhCiCS,CAAsBhG,EAAK1I,IAkC5D,SAAqCoF,EAAGuJ,GACtC,GAAKvJ,EAAL,CACA,GAAiB,iBAANA,EAAgB,OAAOqD,EAAkBrD,EAAGuJ,GACvD,IAAIC,EAAIpS,OAAOmD,UAAU0G,SAASxG,KAAKuF,GAAGyJ,MAAM,GAAI,GAEpD,MADU,WAAND,GAAkBxJ,EAAEQ,cAAagJ,EAAIxJ,EAAEQ,YAAYkJ,MAC7C,QAANF,GAAqB,QAANA,EAAoB/F,MAAMkG,KAAK3J,GACxC,cAANwJ,GAAqB,2CAA2CI,KAAKJ,GAAWnG,EAAkBrD,EAAGuJ,QAAzG,CALc,CAMhB,CAzCkEM,CAA4BvG,EAAK1I,IAmDnG,WACE,MAAM,IAAIkP,UAAU,4IACtB,CArDyGC,ISpErGC,EAAWxB,EAAe,GAC1B1H,EAAW0H,EAAe,GAiE9B,SAASyB,EAAcC,GACrB,OAAOC,SAASC,KAAKC,YAAYH,EACnC,CAkBA,SAASI,EAAsBC,GAC7B,IAXoBC,EAChBN,EAUAxJ,EAAQsJ,EAAS,SAAUS,GAG7B,MAAO,CACLrD,OAHWqD,EAAMrD,OAIjBa,OAHWwC,EAAMxC,OAKrB,GACIyC,GAnBgBF,EAmBY,GAAGrG,OAAOzD,EAAM0G,OAAOL,MAAMC,GAAI,cAlB7DkD,EAASC,SAASQ,cAAc,UAC7BH,IAAQN,EAAOM,IAAMA,GAAMN,GAwBlC,OALAQ,EAAaE,OAAS,WACpB,OAAOL,GACT,EAEAG,EAAaG,QAAUnK,EAAMuH,OACtByC,CACT,CAMA,SAASH,IACP,IAAI7J,EAAQsJ,EAAS,SAAUc,GAI7B,MAAO,CACL1D,OAJW0D,EAAM1D,OAKjBY,QAJY8C,EAAM9C,QAKlBC,OAJW6C,EAAM7C,OAMrB,GACI8C,EAAUC,OAAOD,QAErBA,EAAQ3D,OAAO1G,EAAM0G,QAErB2D,EAAQ,CAAC,yBAA0B,SAAUxC,GAC3C0C,EAAoB1C,GACpB7H,EAAMsH,QAAQO,EAChB,EAAG,SAAUnN,GACXsF,EAAMuH,OAAO7M,EACf,EACF,CAMA,SAAS6P,EAAoB1C,GACtByB,IAAWzB,QACdzH,EAAS,CACPyH,OAAQA,GAGd,CAeA,IAAI2C,EAAiB,IAAInD,QAAQ,SAAUC,EAASC,GAClD,OAAOnH,EAAS,CACdkH,QAASA,EACTC,OAAQA,GAEZ,GACIkD,EAAS,CACX/D,OA5JF,SAAgBgE,GACd,IAAIC,EAAqB,EAAWjE,OAAOgE,GACvC7C,EAAS8C,EAAmB9C,OAC5BnB,EAASpE,EAAyBqI,EAAoB,CAAC,WAE3DvK,EAAS,SAAUJ,GACjB,MAAO,CACL0G,OAAQ,EAAM1G,EAAM0G,OAAQA,GAC5BmB,OAAQA,EAEZ,EACF,EAkJE+C,KA3IF,WACE,IAAI5K,EAAQsJ,EAAS,SAAUuB,GAI7B,MAAO,CACLhD,OAJWgD,EAAKhD,OAKhBD,cAJkBiD,EAAKjD,cAKvBN,QAJYuD,EAAKvD,QAMrB,GAEA,IAAKtH,EAAM4H,cAAe,CAKxB,GAJAxH,EAAS,CACPwH,eAAe,IAGb5H,EAAM6H,OAER,OADA7H,EAAMsH,QAAQtH,EAAM6H,QACb,EAAe2C,GAGxB,GAAIF,OAAOzC,QAAUyC,OAAOzC,OAAOiD,OAGjC,OAFAP,EAAoBD,OAAOzC,QAC3B7H,EAAMsH,QAAQgD,OAAOzC,QACd,EAAe2C,GAGxB,EAAQjB,EAAeK,EAAvB,CAA8CC,EAChD,CAEA,OAAO,EAAeW,EACxB,EA4GEO,oBAhBF,WACE,OAAOzB,EAAS,SAAU0B,GAExB,OADaA,EAAMnD,MAErB,EACF,GAcA,IC3L0WoD,EAAlH,CAACC,QAAQ,CAACrU,QAAQ,OAAOD,SAAS,WAAWuU,UAAU,WAAWC,UAAU,CAACC,MAAM,QAAQC,KAAK,CAACzU,QAAQ,SAA+I0U,EAApG,CAACC,UAAU,CAAC3U,QAAQ,OAAO4U,OAAO,OAAOJ,MAAM,OAAOK,eAAe,SAAS5U,WAAW,WAA+F,IAAa6U,EAA5F,UAAatL,SAAS3G,IAAI,OAAO,gBAAiB,MAAM,CAAC4C,MAAMiP,EAAEC,WAAW9R,EAAE,EAA2S,IAAIkS,EAA7R,UAAaP,MAAM3R,EAAE+R,OAAOI,EAAEC,cAAchD,EAAEiD,QAAQpS,EAAEkR,KAAKmB,EAAE5Q,UAAU6Q,EAAEC,aAAaC,IAAI,OAAO,gBAAgB,UAAU,CAAC7P,MAAM,IAAI2O,EAAEC,QAAQG,MAAM3R,EAAE+R,OAAOI,MAAMM,IAAIrD,GAAG,gBAAgB6C,EAAE,KAAKhS,GAAG,gBAAgB,MAAM,CAACqC,IAAIgQ,EAAE1P,MAAM,IAAI2O,EAAEG,cAActC,GAAGmC,EAAEK,MAAMlQ,UAAU6Q,IAAI,EAAeG,GAAE,UAAGR,GAA+D,IAAIS,EAA5B,SAAY3S,IAAG,eAAGA,EAAE,GAAG,EAAsI,IAAI4S,EAAhF,SAAY5S,EAAEmS,EAAE/C,GAAE,GAAI,IAAInP,GAAE,aAAG,IAAI,eAAGA,EAAEyD,UAAU0L,EAAE,KAAKnP,EAAEyD,SAAQ,GAAI1D,EAAEmS,EAAE,EAAU,SAASU,IAAI,CAAC,SAASC,GAAE9S,EAAEmS,EAAE/C,EAAEnP,GAAG,OAA4B,SAAYD,EAAEmS,GAAG,OAAOnS,EAAEoR,OAAO2B,SAASC,GAAGhT,EAAEmS,GAAG,CAAvEc,CAAGjT,EAAEC,IAAmE,SAAYD,EAAEmS,EAAE/C,EAAEnP,GAAG,OAAOD,EAAEoR,OAAO8B,YAAYf,EAAE/C,EAAEnP,EAAE+S,GAAGhT,EAAEC,QAAG,EAAO,CAArIkT,CAAGnT,EAAEmS,EAAE/C,EAAEnP,EAAE,CAA2H,SAAS+S,GAAGhT,EAAEmS,GAAG,OAAOnS,EAAEoT,IAAIC,MAAMlB,EAAE,CAAqlE,IAAImB,GAAxlE,UAAaC,SAASvT,EAAEwT,SAASrB,EAAEsB,SAASrE,EAAEsE,iBAAiBzT,EAAE0T,iBAAiBrB,EAAEsB,kBAAkBrB,EAAEsB,kBAAkBpB,EAAEqB,yBAAyBC,GAAE,EAAGC,yBAAyBC,GAAE,EAAGC,MAAM5H,EAAE,QAAQ+F,QAAQ8B,EAAE,aAAaC,QAAQ5H,EAAE,CAAC,EAAEuF,OAAOsC,EAAE,OAAO1C,MAAM2C,EAAE,OAAO5S,UAAU6S,EAAE/B,aAAagC,EAAE,CAAC,EAAEC,YAAYC,EAAE7B,EAAE8B,QAAQC,EAAE/B,IAAI,IAAIgC,EAAEC,IAAG,eAAG,IAAKC,EAAEhV,IAAG,eAAG,GAAIiV,GAAE,YAAE,MAAMC,GAAE,YAAE,MAAMC,GAAE,YAAE,MAAMjO,GAAE,YAAE2N,GAAGhP,GAAE,YAAE8O,GAAGS,GAAE,aAAE,GAAIxC,EAAE,KAAK,IAAInS,EAAE,EAAG0Q,OAAO,OAAO1Q,EAAEsN,KAAKrB,IAAIwI,EAAEvR,QAAQ+I,IAAI1M,GAAE,IAAKqV,MAAM3I,GAAa,gBAAVA,GAAGjL,MAAsB0L,QAAQlM,MAAM,gCAAgCyL,IAAI,IAAIuI,EAAEtR,QAAw0C,WAAa,IAAIlD,EAAEwU,EAAEtR,SAASqP,WAAWgB,GAAGvT,GAAG+S,UAAU8B,UAAUpB,GAAGzT,GAAGgT,UAAU6B,UAAUL,EAAEtR,SAAS2R,SAAS,CAAj7CC,GAAI9U,EAAEwN,WAAW4E,EAAE,KAAK,GAAGoC,EAAEtR,SAASuR,EAAEvR,QAAQ,CAAC,IAAIlD,EAAEwU,EAAEtR,QAAQ6R,oBAAoB9I,EAAEqG,GAAEmC,EAAEvR,QAAQ1D,GAAG,GAAGC,GAAGmP,GAAG,OAAOmD,GAAG,IAAI9F,IAAIjM,EAAEuS,YAAYvS,EAAEgV,SAAS/I,EAAE,GAAG,CAAC8F,GAAGsC,GAAGjC,EAAE,KAAK,GAAGoC,EAAEtR,SAASuR,EAAEvR,QAAQ,CAAC,IAAIlD,EAAEwU,EAAEtR,QAAQ+R,oBAAoBhJ,EAAEqG,GAAEmC,EAAEvR,QAAQyO,GAAG,GAAGG,GAAGlD,GAAG,OAAOqD,GAAG,IAAIhG,IAAIjM,EAAEuS,YAAYvS,EAAEgV,SAAS/I,EAAE,GAAG,CAACgG,GAAGoC,GAAGjC,EAAE,KAAK,IAAIpS,EAAEwU,EAAEtR,QAAQ+R,oBAAoBjV,EAAEkV,UAAUT,EAAEvR,QAAQ0N,OAAOuE,aAAaC,UAAUpV,EAAEqV,SAAS1D,GAAG,IAAIA,IAAI3R,EAAEsV,aAAatV,EAAEuV,aAAa,GAAG,CAAC,CAACC,MAAMxV,EAAEuS,WAAWkD,oBAAoBC,KAAK/D,GAAG,GAAGgE,kBAAiB,KAAM3V,EAAE4V,iBAAiB,CAACjE,GAAG0C,GAAGjC,EAAE,KAAKoC,EAAEtR,SAASqP,YAAYQ,SAASsC,SAAS7V,GAAG,KAAK,CAACA,GAAG6U,GAAGjC,EAAE,KAAK,IAAIW,SAAS/S,EAAEgT,SAAS/G,GAAGuI,EAAEtR,QAAQqP,WAAWkC,EAAEvR,QAAQ0N,OAAOiF,iBAAiB7V,EAAEP,GAAGmP,GAAG,QAAQ6F,EAAEvR,QAAQ0N,OAAOiF,iBAAiB5J,EAAE6F,GAAGlD,GAAG,SAAS,CAACA,EAAEnP,EAAEqS,GAAGuC,GAAGjC,EAAE,KAAKqC,EAAEvR,SAAS0N,OAAOkF,SAAShK,IAAI,CAACA,GAAGuI,GAAGjC,EAAE,KAAKoC,EAAEtR,SAAS6S,cAAc/J,IAAI,CAACA,GAAGqI,GAAG,IAAI2B,GAAE,iBAAG,KAAK,IAAIvB,EAAEvR,QAAQ,OAAOkC,EAAElC,QAAQuR,EAAEvR,SAAS,IAAIlD,EAAEsS,GAAEmC,EAAEvR,QAAQ1D,GAAG,GAAGC,GAAGmP,GAAG,OAAOmD,GAAG,IAAI9F,EAAEqG,GAAEmC,EAAEvR,QAAQyO,GAAG,GAAGG,GAAGlD,GAAG,OAAOqD,GAAG,IAAIuC,EAAEtR,SAAS8R,SAAS,CAACjC,SAAS/S,EAAEgT,SAAS/G,KAAK,CAAC2C,EAAE+C,EAAEG,EAAEtS,EAAEC,EAAEsS,EAAEE,IAAIgE,GAAE,iBAAG,MAAMtB,EAAEzR,SAASwR,EAAExR,UAAUsR,EAAEtR,QAAQuR,EAAEvR,QAAQ0N,OAAOsF,iBAAiBxB,EAAExR,QAAQ,CAACiT,iBAAgB,KAAMnK,IAAIgK,IAAIvB,EAAEvR,SAAS0N,OAAOkF,SAAShK,GAAGwI,GAAE,GAAIK,EAAEzR,SAAQ,IAAK,CAAC8I,EAAEF,EAAEkK,IAAoM,OAAhM,eAAG,KAAK3B,GAAG5N,EAAEvD,QAAQsR,EAAEtR,QAAQuR,EAAEvR,UAAU,CAACmR,KAAI,eAAG,MAAME,IAAIF,GAAG4B,KAAK,CAAC1B,EAAEF,EAAE4B,IAA6H,gBAAiB/D,EAAE,CAACf,MAAM2C,EAAEvC,OAAOsC,EAAEjC,cAAcyC,EAAExC,QAAQ8B,EAAEhD,KAAK+D,EAAExT,UAAU6S,EAAE/B,aAAagC,GAAG,GAAkB,UAAGlB,IAAwe,IAAIsD,GAAzE,SAAY5W,GAAG,IAAImS,GAAE,cAAK,OAAO,eAAG,KAAKA,EAAEzO,QAAQ1D,GAAG,CAACA,IAAImS,EAAEzO,OAAO,EAAekB,GAAE,IAAIiS,IAAy1E,IAAIC,GAAz1E,UAAaC,aAAa/W,EAAEgX,gBAAgB7E,EAAE8E,YAAY7H,EAAE3I,MAAMxG,EAAEwT,SAASnB,EAAE4E,KAAK3E,EAAE2B,MAAMzB,EAAE,QAAQ0E,KAAKpD,EAAE1B,QAAQ4B,EAAE,aAAaG,QAAQ9H,EAAE,CAAC,EAAE8K,iBAAiBjD,EAAE,CAAC,EAAEkD,cAAc7K,GAAE,EAAG8K,iBAAiBjD,GAAE,EAAG1C,MAAM2C,EAAE,OAAOvC,OAAOwC,EAAE,OAAO7S,UAAU8S,EAAEhC,aAAakC,EAAE,CAAC,EAAED,YAAYG,EAAE/B,EAAE8B,QAAQE,EAAEhC,EAAE0E,SAASzC,EAAE0C,WAAWzC,EAAElC,IAAI,IAAI9S,EAAEiV,IAAG,eAAG,IAAKC,EAAEC,IAAG,eAAG,GAAIjO,GAAE,YAAE,MAAMrB,GAAE,YAAE,MAAMuP,GAAE,YAAE,MAAMqB,GAAE,YAAE3B,GAAG4B,GAAE,YAAE7B,GAAGU,GAAE,cAAI9U,GAAE,YAAEP,GAAGwM,EAAEmK,GAAGrE,GAAGkF,GAAE,aAAE,GAAIC,GAAE,aAAE,GAAI/E,EAAE,KAAK,IAAIzS,EAAE,EAAGgR,OAAO,OAAOhR,EAAE4N,KAAK6J,IAAI1Q,EAAEvD,QAAQiU,IAAIzC,GAAE,IAAKE,MAAMuC,GAAa,gBAAVA,GAAGnW,MAAsB0L,QAAQlM,MAAM,gCAAgC2W,IAAI,IAAI/R,EAAElC,SAA+iD4R,EAAE5R,SAAS2R,UAAUhB,EAAE7H,GAAG5H,GAAEgT,IAAIrF,EAAE3M,EAAElC,QAAQ2T,iBAAiBzR,EAAElC,QAAQqP,YAAYsC,eAAUzP,EAAElC,QAAQ2R,WAAzoDnV,EAAE8N,WAAW4E,EAAE,KAAK,IAAI1S,EAAE4S,GAAE7L,EAAEvD,QAAQ1D,GAAGC,GAAG,GAAGkS,GAAGG,GAAG,GAAGC,GAAGnD,GAAG,IAAIlP,IAAI0F,EAAElC,SAASqP,aAAavG,GAAG5H,GAAEgT,IAAInL,EAAE7G,EAAElC,SAAS2T,iBAAiBzR,EAAElC,SAAS8R,SAAStV,GAAGsM,GAAG5G,EAAElC,SAASmU,iBAAiBjT,GAAEkT,IAAIvF,MAAM,CAACA,GAAGxS,GAAG6S,EAAE,KAAKhN,EAAElC,SAAS6S,cAAcjK,IAAI,CAACA,GAAGvM,GAAG6S,EAAE,MAAMhN,EAAElC,cAAa,IAAJzD,IAAa2F,EAAElC,QAAQgS,UAAUzO,EAAEvD,QAAQ0N,OAAOuE,aAAaC,UAAUhQ,EAAElC,QAAQmS,SAAS5V,GAAGA,IAAI2F,EAAElC,QAAQoS,aAAa4B,EAAEhU,SAAQ,EAAGkC,EAAElC,QAAQqS,aAAa,GAAG,CAAC,CAACC,MAAMpQ,EAAElC,QAAQqP,WAAWkD,oBAAoBC,KAAKjW,EAAEkW,kBAAiB,KAAMvQ,EAAElC,QAAQ0S,eAAesB,EAAEhU,SAAQ,KAAM,CAACzD,GAAGF,GAAG6S,EAAE,KAAK,IAAI1S,EAAE0F,EAAElC,SAASqP,WAAW7S,GAAGoS,GAAGrL,EAAEvD,SAAS0N,OAAOiF,iBAAiBnW,EAAEoS,IAAI,CAACA,GAAGvS,GAAG6S,EAAE,UAAS,IAAJmB,GAAYnO,EAAElC,SAASqU,WAAWhE,IAAI,CAACA,GAAGhU,GAAG6S,EAAE,KAAK3L,EAAEvD,SAAS0N,OAAOkF,SAAS7D,IAAI,CAACA,GAAG1S,GAAG,IAAIiY,GAAE,iBAAG,KAAK,GAAM7C,EAAEzR,SAAUuD,EAAEvD,UAAW+T,EAAE/T,QAAQ,CAAC+S,EAAE/S,QAAQuD,EAAEvD,SAAS,IAAIxD,EAAEqS,GAAGnD,EAAEuI,EAAE7E,GAAE7L,EAAEvD,QAAQzD,GAAGD,GAAG,GAAGmS,GAAGG,GAAG,GAAGpS,GAAG,IAAI0F,EAAElC,QAAQuD,EAAEvD,SAAS0N,OAAOtF,OAAOqJ,EAAEzR,QAAQ,CAACuU,MAAMN,EAAEhB,iBAAgB,KAAMrK,GAAG6H,GAAG3H,GAAG5G,EAAElC,QAAQmU,iBAAiBjT,GAAEkT,IAAI5X,IAAI+G,EAAEvD,QAAQ0N,OAAOkF,SAAS7D,QAAO,IAAJsB,GAAYnO,EAAElC,QAAQqU,WAAWhE,GAAGiB,GAAE,GAAIyC,EAAE/T,SAAQ,CAAE,GAAG,CAAC1D,EAAEmS,EAAE/C,EAAEnP,EAAEqS,EAAEC,EAAEjG,EAAE6H,EAAE3H,EAAEiG,EAAEsB,IAAykB,OAArkB,eAAE,KAAKhU,GAAGyW,EAAE9S,QAAQkC,EAAElC,QAAQuD,EAAEvD,UAAU,CAAC3D,KAAI,eAAE,MAAMkV,IAAIlV,GAAGiY,KAAK,CAAC/C,EAAElV,EAAEiY,IAAIxX,EAAEkD,QAAQzD,GAAE,eAAE,KAAKF,GAAG+U,IAAIQ,EAAE5R,SAAS2R,UAAUC,EAAE5R,QAAQkC,EAAElC,SAASwU,wBAAwBhY,IAAIwX,EAAEhU,SAASoR,EAAElP,EAAElC,QAAQoS,WAAW5V,OAAO,CAACH,EAAE+U,KAAI,eAAE,KAAK,GAAG/U,EAAE,CAAC,IAAIG,EAAE+G,EAAEvD,QAAQ0N,OAAO+G,mBAAmBR,IAAI,IAAIS,EAAExS,EAAElC,QAAQqP,YAAYsF,IAAI,GAAGD,GAAGT,EAAEW,KAAKC,GAAGA,EAAErB,OAAOkB,EAAElB,MAAM,CAAC,IAAIqB,EAAEtR,EAAEvD,QAAQ0N,OAAOoH,gBAAgB,CAACC,SAASL,IAAIrD,IAAIwD,EAAE,IAAI,MAAM,KAAKrY,GAAGmV,UAAU,CAAC,MAAM,QAAQ,CAACtV,EAAEgV,IAA0I,gBAAiBrC,EAAE,CAACf,MAAM2C,EAAEvC,OAAOwC,EAAEnC,cAAcrS,EAAEsS,QAAQ4B,EAAE9C,KAAKgE,EAAEzT,UAAU8S,EAAEhC,aAAakC,GAAG,EAA6BgE,IAAX,UAAG5B,ICK9vM/F,EAAO/D,OAAO,CACZL,MAAO,CACLC,GAAI,uBAID,MAAM+L,GAAexH,IAcrB,IAdsB,MAC3B1K,EAAK,UACLmS,EAAS,SACTnF,EAAQ,SACR8D,EAAQ,QACRsB,GAAU,EAAI,UACdnX,GAQDyP,EACC,MAAM,EAACiB,EAAc,EAAC0G,IAAoBC,EAAAA,EAAAA,WAAS,GAKnD,OACEC,EAAAA,cAAA,OAAK5V,GAAG,gBAAgB1B,UAAW,kBAAkBA,KACnDsX,EAAAA,cAACC,GAAM,CACLlH,OAAO,OACPrQ,UAAU,iBACVsV,gBAAiBvD,EACjBsD,aAActQ,EACdA,MAAOA,EACP8Q,SAAW9Q,IACL8Q,GAAY9Q,GACd8Q,EAAS9Q,IAGbkO,QAjBmBuE,CAAC9H,EAAajD,KACrCyK,EAAUlV,QAAU0N,EACpB0H,GAAiB,IAgBb5E,MAAM,UACNE,QAAS,CACP9W,SAAU,KACV6b,eAAgB,SAChBC,iBAAkB,WAClBP,QAAS,CACPQ,QAASR,O", "sources": ["webpack://autogentstudio/./node_modules/antd/es/alert/style/index.js", "webpack://autogentstudio/./node_modules/antd/es/alert/Alert.js", "webpack://autogentstudio/./node_modules/antd/es/alert/ErrorBoundary.js", "webpack://autogentstudio/./node_modules/@babel/runtime/helpers/esm/callSuper.js", "webpack://autogentstudio/./node_modules/antd/es/alert/index.js", "webpack://autogentstudio/./node_modules/lucide-react/dist/esm/icons/download.js", "webpack://autogentstudio/./node_modules/@monaco-editor/loader/lib/es/_virtual/_rollupPluginBabelHelpers.js", "webpack://autogentstudio/./node_modules/state-local/lib/es/state-local.js", "webpack://autogentstudio/./node_modules/@monaco-editor/loader/lib/es/config/index.js", "webpack://autogentstudio/./node_modules/@monaco-editor/loader/lib/es/utils/curry.js", "webpack://autogentstudio/./node_modules/@monaco-editor/loader/lib/es/utils/isObject.js", "webpack://autogentstudio/./node_modules/@monaco-editor/loader/lib/es/validators/index.js", "webpack://autogentstudio/./node_modules/@monaco-editor/loader/lib/es/utils/compose.js", "webpack://autogentstudio/./node_modules/@monaco-editor/loader/lib/es/utils/deepMerge.js", "webpack://autogentstudio/./node_modules/@monaco-editor/loader/lib/es/utils/makeCancelable.js", "webpack://autogentstudio/./node_modules/@monaco-editor/loader/lib/es/loader/index.js", "webpack://autogentstudio/./node_modules/@monaco-editor/react/dist/index.mjs", "webpack://autogentstudio/./src/components/views/monaco.tsx"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nimport { resetComponent } from '../../style';\nimport { genStyleHooks } from '../../theme/internal';\nconst genAlertTypeStyle = (bgColor, borderColor, iconColor, token, alertCls) => ({\n  background: bgColor,\n  border: `${unit(token.lineWidth)} ${token.lineType} ${borderColor}`,\n  [`${alertCls}-icon`]: {\n    color: iconColor\n  }\n});\nexport const genBaseStyle = token => {\n  const {\n    componentCls,\n    motionDurationSlow: duration,\n    marginXS,\n    marginSM,\n    fontSize,\n    fontSizeLG,\n    lineHeight,\n    borderRadiusLG: borderRadius,\n    motionEaseInOutCirc,\n    withDescriptionIconSize,\n    colorText,\n    colorTextHeading,\n    withDescriptionPadding,\n    defaultPadding\n  } = token;\n  return {\n    [componentCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      position: 'relative',\n      display: 'flex',\n      alignItems: 'center',\n      padding: defaultPadding,\n      wordWrap: 'break-word',\n      borderRadius,\n      [`&${componentCls}-rtl`]: {\n        direction: 'rtl'\n      },\n      [`${componentCls}-content`]: {\n        flex: 1,\n        minWidth: 0\n      },\n      [`${componentCls}-icon`]: {\n        marginInlineEnd: marginXS,\n        lineHeight: 0\n      },\n      '&-description': {\n        display: 'none',\n        fontSize,\n        lineHeight\n      },\n      '&-message': {\n        color: colorTextHeading\n      },\n      [`&${componentCls}-motion-leave`]: {\n        overflow: 'hidden',\n        opacity: 1,\n        transition: `max-height ${duration} ${motionEaseInOutCirc}, opacity ${duration} ${motionEaseInOutCirc},\n        padding-top ${duration} ${motionEaseInOutCirc}, padding-bottom ${duration} ${motionEaseInOutCirc},\n        margin-bottom ${duration} ${motionEaseInOutCirc}`\n      },\n      [`&${componentCls}-motion-leave-active`]: {\n        maxHeight: 0,\n        marginBottom: '0 !important',\n        paddingTop: 0,\n        paddingBottom: 0,\n        opacity: 0\n      }\n    }),\n    [`${componentCls}-with-description`]: {\n      alignItems: 'flex-start',\n      padding: withDescriptionPadding,\n      [`${componentCls}-icon`]: {\n        marginInlineEnd: marginSM,\n        fontSize: withDescriptionIconSize,\n        lineHeight: 0\n      },\n      [`${componentCls}-message`]: {\n        display: 'block',\n        marginBottom: marginXS,\n        color: colorTextHeading,\n        fontSize: fontSizeLG\n      },\n      [`${componentCls}-description`]: {\n        display: 'block',\n        color: colorText\n      }\n    },\n    [`${componentCls}-banner`]: {\n      marginBottom: 0,\n      border: '0 !important',\n      borderRadius: 0\n    }\n  };\n};\nexport const genTypeStyle = token => {\n  const {\n    componentCls,\n    colorSuccess,\n    colorSuccessBorder,\n    colorSuccessBg,\n    colorWarning,\n    colorWarningBorder,\n    colorWarningBg,\n    colorError,\n    colorErrorBorder,\n    colorErrorBg,\n    colorInfo,\n    colorInfoBorder,\n    colorInfoBg\n  } = token;\n  return {\n    [componentCls]: {\n      '&-success': genAlertTypeStyle(colorSuccessBg, colorSuccessBorder, colorSuccess, token, componentCls),\n      '&-info': genAlertTypeStyle(colorInfoBg, colorInfoBorder, colorInfo, token, componentCls),\n      '&-warning': genAlertTypeStyle(colorWarningBg, colorWarningBorder, colorWarning, token, componentCls),\n      '&-error': Object.assign(Object.assign({}, genAlertTypeStyle(colorErrorBg, colorErrorBorder, colorError, token, componentCls)), {\n        [`${componentCls}-description > pre`]: {\n          margin: 0,\n          padding: 0\n        }\n      })\n    }\n  };\n};\nexport const genActionStyle = token => {\n  const {\n    componentCls,\n    iconCls,\n    motionDurationMid,\n    marginXS,\n    fontSizeIcon,\n    colorIcon,\n    colorIconHover\n  } = token;\n  return {\n    [componentCls]: {\n      '&-action': {\n        marginInlineStart: marginXS\n      },\n      [`${componentCls}-close-icon`]: {\n        marginInlineStart: marginXS,\n        padding: 0,\n        overflow: 'hidden',\n        fontSize: fontSizeIcon,\n        lineHeight: unit(fontSizeIcon),\n        backgroundColor: 'transparent',\n        border: 'none',\n        outline: 'none',\n        cursor: 'pointer',\n        [`${iconCls}-close`]: {\n          color: colorIcon,\n          transition: `color ${motionDurationMid}`,\n          '&:hover': {\n            color: colorIconHover\n          }\n        }\n      },\n      '&-close-text': {\n        color: colorIcon,\n        transition: `color ${motionDurationMid}`,\n        '&:hover': {\n          color: colorIconHover\n        }\n      }\n    }\n  };\n};\nexport const prepareComponentToken = token => {\n  const paddingHorizontal = 12; // Fixed value here.\n  return {\n    withDescriptionIconSize: token.fontSizeHeading3,\n    defaultPadding: `${token.paddingContentVerticalSM}px ${paddingHorizontal}px`,\n    withDescriptionPadding: `${token.paddingMD}px ${token.paddingContentHorizontalLG}px`\n  };\n};\nexport default genStyleHooks('Alert', token => [genBaseStyle(token), genTypeStyle(token), genActionStyle(token)], prepareComponentToken);", "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport CheckCircleFilled from \"@ant-design/icons/es/icons/CheckCircleFilled\";\nimport CloseCircleFilled from \"@ant-design/icons/es/icons/CloseCircleFilled\";\nimport CloseOutlined from \"@ant-design/icons/es/icons/CloseOutlined\";\nimport ExclamationCircleFilled from \"@ant-design/icons/es/icons/ExclamationCircleFilled\";\nimport InfoCircleFilled from \"@ant-design/icons/es/icons/InfoCircleFilled\";\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport { replaceElement } from '../_util/reactNode';\nimport { devUseWarning } from '../_util/warning';\nimport { useComponentConfig } from '../config-provider/context';\nimport useStyle from './style';\nconst iconMapFilled = {\n  success: CheckCircleFilled,\n  info: InfoCircleFilled,\n  error: CloseCircleFilled,\n  warning: ExclamationCircleFilled\n};\nconst IconNode = props => {\n  const {\n    icon,\n    prefixCls,\n    type\n  } = props;\n  const iconType = iconMapFilled[type] || null;\n  if (icon) {\n    return replaceElement(icon, /*#__PURE__*/React.createElement(\"span\", {\n      className: `${prefixCls}-icon`\n    }, icon), () => ({\n      className: classNames(`${prefixCls}-icon`, icon.props.className)\n    }));\n  }\n  return /*#__PURE__*/React.createElement(iconType, {\n    className: `${prefixCls}-icon`\n  });\n};\nconst CloseIconNode = props => {\n  const {\n    isClosable,\n    prefixCls,\n    closeIcon,\n    handleClose,\n    ariaProps\n  } = props;\n  const mergedCloseIcon = closeIcon === true || closeIcon === undefined ? /*#__PURE__*/React.createElement(CloseOutlined, null) : closeIcon;\n  return isClosable ? (/*#__PURE__*/React.createElement(\"button\", Object.assign({\n    type: \"button\",\n    onClick: handleClose,\n    className: `${prefixCls}-close-icon`,\n    tabIndex: 0\n  }, ariaProps), mergedCloseIcon)) : null;\n};\nconst Alert = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      description,\n      prefixCls: customizePrefixCls,\n      message,\n      banner,\n      className,\n      rootClassName,\n      style,\n      onMouseEnter,\n      onMouseLeave,\n      onClick,\n      afterClose,\n      showIcon,\n      closable,\n      closeText,\n      closeIcon,\n      action,\n      id\n    } = props,\n    otherProps = __rest(props, [\"description\", \"prefixCls\", \"message\", \"banner\", \"className\", \"rootClassName\", \"style\", \"onMouseEnter\", \"onMouseLeave\", \"onClick\", \"afterClose\", \"showIcon\", \"closable\", \"closeText\", \"closeIcon\", \"action\", \"id\"]);\n  const [closed, setClosed] = React.useState(false);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Alert');\n    warning.deprecated(!closeText, 'closeText', 'closable.closeIcon');\n  }\n  const internalRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => ({\n    nativeElement: internalRef.current\n  }));\n  const {\n    getPrefixCls,\n    direction,\n    closable: contextClosable,\n    closeIcon: contextCloseIcon,\n    className: contextClassName,\n    style: contextStyle\n  } = useComponentConfig('alert');\n  const prefixCls = getPrefixCls('alert', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const handleClose = e => {\n    var _a;\n    setClosed(true);\n    (_a = props.onClose) === null || _a === void 0 ? void 0 : _a.call(props, e);\n  };\n  const type = React.useMemo(() => {\n    if (props.type !== undefined) {\n      return props.type;\n    }\n    // banner mode defaults to 'warning'\n    return banner ? 'warning' : 'info';\n  }, [props.type, banner]);\n  // closeable when closeText or closeIcon is assigned\n  const isClosable = React.useMemo(() => {\n    if (typeof closable === 'object' && closable.closeIcon) return true;\n    if (closeText) {\n      return true;\n    }\n    if (typeof closable === 'boolean') {\n      return closable;\n    }\n    // should be true when closeIcon is 0 or ''\n    if (closeIcon !== false && closeIcon !== null && closeIcon !== undefined) {\n      return true;\n    }\n    return !!contextClosable;\n  }, [closeText, closeIcon, closable, contextClosable]);\n  // banner mode defaults to Icon\n  const isShowIcon = banner && showIcon === undefined ? true : showIcon;\n  const alertCls = classNames(prefixCls, `${prefixCls}-${type}`, {\n    [`${prefixCls}-with-description`]: !!description,\n    [`${prefixCls}-no-icon`]: !isShowIcon,\n    [`${prefixCls}-banner`]: !!banner,\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, contextClassName, className, rootClassName, cssVarCls, hashId);\n  const restProps = pickAttrs(otherProps, {\n    aria: true,\n    data: true\n  });\n  const mergedCloseIcon = React.useMemo(() => {\n    if (typeof closable === 'object' && closable.closeIcon) {\n      return closable.closeIcon;\n    }\n    if (closeText) {\n      return closeText;\n    }\n    if (closeIcon !== undefined) {\n      return closeIcon;\n    }\n    if (typeof contextClosable === 'object' && contextClosable.closeIcon) {\n      return contextClosable.closeIcon;\n    }\n    return contextCloseIcon;\n  }, [closeIcon, closable, closeText, contextCloseIcon]);\n  const mergedAriaProps = React.useMemo(() => {\n    const merged = closable !== null && closable !== void 0 ? closable : contextClosable;\n    if (typeof merged === 'object') {\n      const {\n          closeIcon: _\n        } = merged,\n        ariaProps = __rest(merged, [\"closeIcon\"]);\n      return ariaProps;\n    }\n    return {};\n  }, [closable, contextClosable]);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(CSSMotion, {\n    visible: !closed,\n    motionName: `${prefixCls}-motion`,\n    motionAppear: false,\n    motionEnter: false,\n    onLeaveStart: node => ({\n      maxHeight: node.offsetHeight\n    }),\n    onLeaveEnd: afterClose\n  }, ({\n    className: motionClassName,\n    style: motionStyle\n  }, setRef) => (/*#__PURE__*/React.createElement(\"div\", Object.assign({\n    id: id,\n    ref: composeRef(internalRef, setRef),\n    \"data-show\": !closed,\n    className: classNames(alertCls, motionClassName),\n    style: Object.assign(Object.assign(Object.assign({}, contextStyle), style), motionStyle),\n    onMouseEnter: onMouseEnter,\n    onMouseLeave: onMouseLeave,\n    onClick: onClick,\n    role: \"alert\"\n  }, restProps), isShowIcon ? (/*#__PURE__*/React.createElement(IconNode, {\n    description: description,\n    icon: props.icon,\n    prefixCls: prefixCls,\n    type: type\n  })) : null, /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-content`\n  }, message ? /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-message`\n  }, message) : null, description ? /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-description`\n  }, description) : null), action ? /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-action`\n  }, action) : null, /*#__PURE__*/React.createElement(CloseIconNode, {\n    isClosable: isClosable,\n    prefixCls: prefixCls,\n    closeIcon: mergedCloseIcon,\n    handleClose: handleClose,\n    ariaProps: mergedAriaProps\n  })))));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Alert.displayName = 'Alert';\n}\nexport default Alert;", "\"use client\";\n\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _callSuper from \"@babel/runtime/helpers/esm/callSuper\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport * as React from 'react';\nimport Alert from './Alert';\nlet ErrorBoundary = /*#__PURE__*/function (_React$Component) {\n  function ErrorBoundary() {\n    var _this;\n    _classCallCheck(this, ErrorBoundary);\n    _this = _callSuper(this, ErrorBoundary, arguments);\n    _this.state = {\n      error: undefined,\n      info: {\n        componentStack: ''\n      }\n    };\n    return _this;\n  }\n  _inherits(ErrorBoundary, _React$Component);\n  return _createClass(ErrorBoundary, [{\n    key: \"componentDidCatch\",\n    value: function componentDidCatch(error, info) {\n      this.setState({\n        error,\n        info\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      const {\n        message,\n        description,\n        id,\n        children\n      } = this.props;\n      const {\n        error,\n        info\n      } = this.state;\n      const componentStack = (info === null || info === void 0 ? void 0 : info.componentStack) || null;\n      const errorMessage = typeof message === 'undefined' ? (error || '').toString() : message;\n      const errorDescription = typeof description === 'undefined' ? componentStack : description;\n      if (error) {\n        return /*#__PURE__*/React.createElement(Alert, {\n          id: id,\n          type: \"error\",\n          message: errorMessage,\n          description: /*#__PURE__*/React.createElement(\"pre\", {\n            style: {\n              fontSize: '0.9em',\n              overflowX: 'auto'\n            }\n          }, errorDescription)\n        });\n      }\n      return children;\n    }\n  }]);\n}(React.Component);\nexport default ErrorBoundary;", "import getPrototypeOf from \"./getPrototypeOf.js\";\nimport isNativeReflectConstruct from \"./isNativeReflectConstruct.js\";\nimport possibleConstructorReturn from \"./possibleConstructorReturn.js\";\nfunction _callSuper(t, o, e) {\n  return o = getPrototypeOf(o), possibleConstructorReturn(t, isNativeReflectConstruct() ? Reflect.construct(o, e || [], getPrototypeOf(t).constructor) : o.apply(t, e));\n}\nexport { _callSuper as default };", "\"use client\";\n\nimport InternalAlert from './Alert';\nimport ErrorBoundary from './ErrorBoundary';\nconst Alert = InternalAlert;\nAlert.ErrorBoundary = ErrorBoundary;\nexport default Alert;", "/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst Download = createLucideIcon(\"Download\", [\n  [\"path\", { d: \"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\", key: \"ih7n3h\" }],\n  [\"polyline\", { points: \"7 10 12 15 17 10\", key: \"2ggqvy\" }],\n  [\"line\", { x1: \"12\", x2: \"12\", y1: \"15\", y2: \"3\", key: \"1vk2je\" }]\n]);\n\nexport { Download as default };\n//# sourceMappingURL=download.js.map\n", "function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    });\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n\n  var key, i;\n\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n\n  return target;\n}\n\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\n\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\n\nfunction _iterableToArrayLimit(arr, i) {\n  if (typeof Symbol === \"undefined\" || !(Symbol.iterator in Object(arr))) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n  var _e = undefined;\n\n  try {\n    for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n\n  return _arr;\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nexport { _arrayLikeToArray as arrayLikeToArray, _arrayWithHoles as arrayWithHoles, _defineProperty as defineProperty, _iterableToArrayLimit as iterableToArrayLimit, _nonIterableRest as nonIterableRest, _objectSpread2 as objectSpread2, _objectWithoutProperties as objectWithoutProperties, _objectWithoutPropertiesLoose as objectWithoutPropertiesLoose, _slicedToArray as slicedToArray, _unsupportedIterableToArray as unsupportedIterableToArray };\n", "function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    });\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nfunction compose() {\n  for (var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++) {\n    fns[_key] = arguments[_key];\n  }\n\n  return function (x) {\n    return fns.reduceRight(function (y, f) {\n      return f(y);\n    }, x);\n  };\n}\n\nfunction curry(fn) {\n  return function curried() {\n    var _this = this;\n\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n\n    return args.length >= fn.length ? fn.apply(this, args) : function () {\n      for (var _len3 = arguments.length, nextArgs = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        nextArgs[_key3] = arguments[_key3];\n      }\n\n      return curried.apply(_this, [].concat(args, nextArgs));\n    };\n  };\n}\n\nfunction isObject(value) {\n  return {}.toString.call(value).includes('Object');\n}\n\nfunction isEmpty(obj) {\n  return !Object.keys(obj).length;\n}\n\nfunction isFunction(value) {\n  return typeof value === 'function';\n}\n\nfunction hasOwnProperty(object, property) {\n  return Object.prototype.hasOwnProperty.call(object, property);\n}\n\nfunction validateChanges(initial, changes) {\n  if (!isObject(changes)) errorHandler('changeType');\n  if (Object.keys(changes).some(function (field) {\n    return !hasOwnProperty(initial, field);\n  })) errorHandler('changeField');\n  return changes;\n}\n\nfunction validateSelector(selector) {\n  if (!isFunction(selector)) errorHandler('selectorType');\n}\n\nfunction validateHandler(handler) {\n  if (!(isFunction(handler) || isObject(handler))) errorHandler('handlerType');\n  if (isObject(handler) && Object.values(handler).some(function (_handler) {\n    return !isFunction(_handler);\n  })) errorHandler('handlersType');\n}\n\nfunction validateInitial(initial) {\n  if (!initial) errorHandler('initialIsRequired');\n  if (!isObject(initial)) errorHandler('initialType');\n  if (isEmpty(initial)) errorHandler('initialContent');\n}\n\nfunction throwError(errorMessages, type) {\n  throw new Error(errorMessages[type] || errorMessages[\"default\"]);\n}\n\nvar errorMessages = {\n  initialIsRequired: 'initial state is required',\n  initialType: 'initial state should be an object',\n  initialContent: 'initial state shouldn\\'t be an empty object',\n  handlerType: 'handler should be an object or a function',\n  handlersType: 'all handlers should be a functions',\n  selectorType: 'selector should be a function',\n  changeType: 'provided value of changes should be an object',\n  changeField: 'it seams you want to change a field in the state which is not specified in the \"initial\" state',\n  \"default\": 'an unknown error accured in `state-local` package'\n};\nvar errorHandler = curry(throwError)(errorMessages);\nvar validators = {\n  changes: validateChanges,\n  selector: validateSelector,\n  handler: validateHandler,\n  initial: validateInitial\n};\n\nfunction create(initial) {\n  var handler = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  validators.initial(initial);\n  validators.handler(handler);\n  var state = {\n    current: initial\n  };\n  var didUpdate = curry(didStateUpdate)(state, handler);\n  var update = curry(updateState)(state);\n  var validate = curry(validators.changes)(initial);\n  var getChanges = curry(extractChanges)(state);\n\n  function getState() {\n    var selector = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : function (state) {\n      return state;\n    };\n    validators.selector(selector);\n    return selector(state.current);\n  }\n\n  function setState(causedChanges) {\n    compose(didUpdate, update, validate, getChanges)(causedChanges);\n  }\n\n  return [getState, setState];\n}\n\nfunction extractChanges(state, causedChanges) {\n  return isFunction(causedChanges) ? causedChanges(state.current) : causedChanges;\n}\n\nfunction updateState(state, changes) {\n  state.current = _objectSpread2(_objectSpread2({}, state.current), changes);\n  return changes;\n}\n\nfunction didStateUpdate(state, handler, changes) {\n  isFunction(handler) ? handler(state.current) : Object.keys(changes).forEach(function (field) {\n    var _handler$field;\n\n    return (_handler$field = handler[field]) === null || _handler$field === void 0 ? void 0 : _handler$field.call(handler, state.current[field]);\n  });\n  return changes;\n}\n\nvar index = {\n  create: create\n};\n\nexport default index;\n", "var config = {\n  paths: {\n    vs: 'https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs'\n  }\n};\n\nexport default config;\n", "function curry(fn) {\n  return function curried() {\n    var _this = this;\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    return args.length >= fn.length ? fn.apply(this, args) : function () {\n      for (var _len2 = arguments.length, nextArgs = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        nextArgs[_key2] = arguments[_key2];\n      }\n\n      return curried.apply(_this, [].concat(args, nextArgs));\n    };\n  };\n}\n\nexport default curry;\n", "function isObject(value) {\n  return {}.toString.call(value).includes('Object');\n}\n\nexport default isObject;\n", "import curry from '../utils/curry.js';\nimport isObject from '../utils/isObject.js';\n\n/**\n * validates the configuration object and informs about deprecation\n * @param {Object} config - the configuration object \n * @return {Object} config - the validated configuration object\n */\n\nfunction validateConfig(config) {\n  if (!config) errorHandler('configIsRequired');\n  if (!isObject(config)) errorHandler('configType');\n\n  if (config.urls) {\n    informAboutDeprecation();\n    return {\n      paths: {\n        vs: config.urls.monacoBase\n      }\n    };\n  }\n\n  return config;\n}\n/**\n * logs deprecation message\n */\n\n\nfunction informAboutDeprecation() {\n  console.warn(errorMessages.deprecation);\n}\n\nfunction throwError(errorMessages, type) {\n  throw new Error(errorMessages[type] || errorMessages[\"default\"]);\n}\n\nvar errorMessages = {\n  configIsRequired: 'the configuration object is required',\n  configType: 'the configuration object should be an object',\n  \"default\": 'an unknown error accured in `@monaco-editor/loader` package',\n  deprecation: \"Deprecation warning!\\n    You are using deprecated way of configuration.\\n\\n    Instead of using\\n      monaco.config({ urls: { monacoBase: '...' } })\\n    use\\n      monaco.config({ paths: { vs: '...' } })\\n\\n    For more please check the link https://github.com/suren-atoyan/monaco-loader#config\\n  \"\n};\nvar errorHandler = curry(throwError)(errorMessages);\nvar validators = {\n  config: validateConfig\n};\n\nexport default validators;\nexport { errorHandler, errorMessages };\n", "var compose = function compose() {\n  for (var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++) {\n    fns[_key] = arguments[_key];\n  }\n\n  return function (x) {\n    return fns.reduceRight(function (y, f) {\n      return f(y);\n    }, x);\n  };\n};\n\nexport default compose;\n", "import { objectSpread2 as _objectSpread2 } from '../_virtual/_rollupPluginBabelHelpers.js';\n\nfunction merge(target, source) {\n  Object.keys(source).forEach(function (key) {\n    if (source[key] instanceof Object) {\n      if (target[key]) {\n        Object.assign(source[key], merge(target[key], source[key]));\n      }\n    }\n  });\n  return _objectSpread2(_objectSpread2({}, target), source);\n}\n\nexport default merge;\n", "// The source (has been changed) is https://github.com/facebook/react/issues/5465#issuecomment-157888325\nvar CANCELATION_MESSAGE = {\n  type: 'cancelation',\n  msg: 'operation is manually canceled'\n};\n\nfunction makeCancelable(promise) {\n  var hasCanceled_ = false;\n  var wrappedPromise = new Promise(function (resolve, reject) {\n    promise.then(function (val) {\n      return hasCanceled_ ? reject(CANCELATION_MESSAGE) : resolve(val);\n    });\n    promise[\"catch\"](reject);\n  });\n  return wrappedPromise.cancel = function () {\n    return hasCanceled_ = true;\n  }, wrappedPromise;\n}\n\nexport default makeCancelable;\nexport { CANCELATION_MESSAGE };\n", "import { slicedToArray as _slicedToArray, objectWithoutProperties as _objectWithoutProperties } from '../_virtual/_rollupPluginBabelHelpers.js';\nimport state from 'state-local';\nimport config$1 from '../config/index.js';\nimport validators from '../validators/index.js';\nimport compose from '../utils/compose.js';\nimport merge from '../utils/deepMerge.js';\nimport makeCancelable from '../utils/makeCancelable.js';\n\n/** the local state of the module */\n\nvar _state$create = state.create({\n  config: config$1,\n  isInitialized: false,\n  resolve: null,\n  reject: null,\n  monaco: null\n}),\n    _state$create2 = _slicedToArray(_state$create, 2),\n    getState = _state$create2[0],\n    setState = _state$create2[1];\n/**\n * set the loader configuration\n * @param {Object} config - the configuration object\n */\n\n\nfunction config(globalConfig) {\n  var _validators$config = validators.config(globalConfig),\n      monaco = _validators$config.monaco,\n      config = _objectWithoutProperties(_validators$config, [\"monaco\"]);\n\n  setState(function (state) {\n    return {\n      config: merge(state.config, config),\n      monaco: monaco\n    };\n  });\n}\n/**\n * handles the initialization of the monaco-editor\n * @return {Promise} - returns an instance of monaco (with a cancelable promise)\n */\n\n\nfunction init() {\n  var state = getState(function (_ref) {\n    var monaco = _ref.monaco,\n        isInitialized = _ref.isInitialized,\n        resolve = _ref.resolve;\n    return {\n      monaco: monaco,\n      isInitialized: isInitialized,\n      resolve: resolve\n    };\n  });\n\n  if (!state.isInitialized) {\n    setState({\n      isInitialized: true\n    });\n\n    if (state.monaco) {\n      state.resolve(state.monaco);\n      return makeCancelable(wrapperPromise);\n    }\n\n    if (window.monaco && window.monaco.editor) {\n      storeMonacoInstance(window.monaco);\n      state.resolve(window.monaco);\n      return makeCancelable(wrapperPromise);\n    }\n\n    compose(injectScripts, getMonacoLoaderScript)(configureLoader);\n  }\n\n  return makeCancelable(wrapperPromise);\n}\n/**\n * injects provided scripts into the document.body\n * @param {Object} script - an HTML script element\n * @return {Object} - the injected HTML script element\n */\n\n\nfunction injectScripts(script) {\n  return document.body.appendChild(script);\n}\n/**\n * creates an HTML script element with/without provided src\n * @param {string} [src] - the source path of the script\n * @return {Object} - the created HTML script element\n */\n\n\nfunction createScript(src) {\n  var script = document.createElement('script');\n  return src && (script.src = src), script;\n}\n/**\n * creates an HTML script element with the monaco loader src\n * @return {Object} - the created HTML script element\n */\n\n\nfunction getMonacoLoaderScript(configureLoader) {\n  var state = getState(function (_ref2) {\n    var config = _ref2.config,\n        reject = _ref2.reject;\n    return {\n      config: config,\n      reject: reject\n    };\n  });\n  var loaderScript = createScript(\"\".concat(state.config.paths.vs, \"/loader.js\"));\n\n  loaderScript.onload = function () {\n    return configureLoader();\n  };\n\n  loaderScript.onerror = state.reject;\n  return loaderScript;\n}\n/**\n * configures the monaco loader\n */\n\n\nfunction configureLoader() {\n  var state = getState(function (_ref3) {\n    var config = _ref3.config,\n        resolve = _ref3.resolve,\n        reject = _ref3.reject;\n    return {\n      config: config,\n      resolve: resolve,\n      reject: reject\n    };\n  });\n  var require = window.require;\n\n  require.config(state.config);\n\n  require(['vs/editor/editor.main'], function (monaco) {\n    storeMonacoInstance(monaco);\n    state.resolve(monaco);\n  }, function (error) {\n    state.reject(error);\n  });\n}\n/**\n * store monaco instance in local state\n */\n\n\nfunction storeMonacoInstance(monaco) {\n  if (!getState().monaco) {\n    setState({\n      monaco: monaco\n    });\n  }\n}\n/**\n * internal helper function\n * extracts stored monaco instance\n * @return {Object|null} - the monaco instance\n */\n\n\nfunction __getMonacoInstance() {\n  return getState(function (_ref4) {\n    var monaco = _ref4.monaco;\n    return monaco;\n  });\n}\n\nvar wrapperPromise = new Promise(function (resolve, reject) {\n  return setState({\n    resolve: resolve,\n    reject: reject\n  });\n});\nvar loader = {\n  config: config,\n  init: init,\n  __getMonacoInstance: __getMonacoInstance\n};\n\nexport default loader;\n", "import _t from\"@monaco-editor/loader\";import{memo as Te}from\"react\";import ke,{useState as re,useRef as S,useCallback as oe,useEffect as ne}from\"react\";import Se from\"@monaco-editor/loader\";import{memo as ye}from\"react\";import K from\"react\";var le={wrapper:{display:\"flex\",position:\"relative\",textAlign:\"initial\"},fullWidth:{width:\"100%\"},hide:{display:\"none\"}},v=le;import me from\"react\";var ae={container:{display:\"flex\",height:\"100%\",width:\"100%\",justifyContent:\"center\",alignItems:\"center\"}},Y=ae;function Me({children:e}){return me.createElement(\"div\",{style:Y.container},e)}var Z=Me;var $=Z;function Ee({width:e,height:r,isEditorReady:n,loading:t,_ref:a,className:m,wrapperProps:E}){return K.createElement(\"section\",{style:{...v.wrapper,width:e,height:r},...E},!n&&K.createElement($,null,t),K.createElement(\"div\",{ref:a,style:{...v.fullWidth,...!n&&v.hide},className:m}))}var ee=Ee;var H=ye(ee);import{useEffect as xe}from\"react\";function Ce(e){xe(e,[])}var k=Ce;import{useEffect as ge,useRef as Re}from\"react\";function he(e,r,n=!0){let t=Re(!0);ge(t.current||!n?()=>{t.current=!1}:e,r)}var l=he;function D(){}function h(e,r,n,t){return De(e,t)||be(e,r,n,t)}function De(e,r){return e.editor.getModel(te(e,r))}function be(e,r,n,t){return e.editor.createModel(r,n,t?te(e,t):void 0)}function te(e,r){return e.Uri.parse(r)}function Oe({original:e,modified:r,language:n,originalLanguage:t,modifiedLanguage:a,originalModelPath:m,modifiedModelPath:E,keepCurrentOriginalModel:g=!1,keepCurrentModifiedModel:N=!1,theme:x=\"light\",loading:P=\"Loading...\",options:y={},height:V=\"100%\",width:z=\"100%\",className:F,wrapperProps:j={},beforeMount:A=D,onMount:q=D}){let[M,O]=re(!1),[T,s]=re(!0),u=S(null),c=S(null),w=S(null),d=S(q),o=S(A),b=S(!1);k(()=>{let i=Se.init();return i.then(f=>(c.current=f)&&s(!1)).catch(f=>f?.type!==\"cancelation\"&&console.error(\"Monaco initialization: error:\",f)),()=>u.current?I():i.cancel()}),l(()=>{if(u.current&&c.current){let i=u.current.getOriginalEditor(),f=h(c.current,e||\"\",t||n||\"text\",m||\"\");f!==i.getModel()&&i.setModel(f)}},[m],M),l(()=>{if(u.current&&c.current){let i=u.current.getModifiedEditor(),f=h(c.current,r||\"\",a||n||\"text\",E||\"\");f!==i.getModel()&&i.setModel(f)}},[E],M),l(()=>{let i=u.current.getModifiedEditor();i.getOption(c.current.editor.EditorOption.readOnly)?i.setValue(r||\"\"):r!==i.getValue()&&(i.executeEdits(\"\",[{range:i.getModel().getFullModelRange(),text:r||\"\",forceMoveMarkers:!0}]),i.pushUndoStop())},[r],M),l(()=>{u.current?.getModel()?.original.setValue(e||\"\")},[e],M),l(()=>{let{original:i,modified:f}=u.current.getModel();c.current.editor.setModelLanguage(i,t||n||\"text\"),c.current.editor.setModelLanguage(f,a||n||\"text\")},[n,t,a],M),l(()=>{c.current?.editor.setTheme(x)},[x],M),l(()=>{u.current?.updateOptions(y)},[y],M);let L=oe(()=>{if(!c.current)return;o.current(c.current);let i=h(c.current,e||\"\",t||n||\"text\",m||\"\"),f=h(c.current,r||\"\",a||n||\"text\",E||\"\");u.current?.setModel({original:i,modified:f})},[n,r,a,e,t,m,E]),U=oe(()=>{!b.current&&w.current&&(u.current=c.current.editor.createDiffEditor(w.current,{automaticLayout:!0,...y}),L(),c.current?.editor.setTheme(x),O(!0),b.current=!0)},[y,x,L]);ne(()=>{M&&d.current(u.current,c.current)},[M]),ne(()=>{!T&&!M&&U()},[T,M,U]);function I(){let i=u.current?.getModel();g||i?.original?.dispose(),N||i?.modified?.dispose(),u.current?.dispose()}return ke.createElement(H,{width:z,height:V,isEditorReady:M,loading:P,_ref:w,className:F,wrapperProps:j})}var ie=Oe;var we=Te(ie);import{useState as Ie}from\"react\";import ce from\"@monaco-editor/loader\";function Pe(){let[e,r]=Ie(ce.__getMonacoInstance());return k(()=>{let n;return e||(n=ce.init(),n.then(t=>{r(t)})),()=>n?.cancel()}),e}var Le=Pe;import{memo as ze}from\"react\";import We,{useState as ue,useEffect as W,useRef as C,useCallback as _e}from\"react\";import Ne from\"@monaco-editor/loader\";import{useEffect as Ue,useRef as ve}from\"react\";function He(e){let r=ve();return Ue(()=>{r.current=e},[e]),r.current}var se=He;var _=new Map;function Ve({defaultValue:e,defaultLanguage:r,defaultPath:n,value:t,language:a,path:m,theme:E=\"light\",line:g,loading:N=\"Loading...\",options:x={},overrideServices:P={},saveViewState:y=!0,keepCurrentModel:V=!1,width:z=\"100%\",height:F=\"100%\",className:j,wrapperProps:A={},beforeMount:q=D,onMount:M=D,onChange:O,onValidate:T=D}){let[s,u]=ue(!1),[c,w]=ue(!0),d=C(null),o=C(null),b=C(null),L=C(M),U=C(q),I=C(),i=C(t),f=se(m),Q=C(!1),B=C(!1);k(()=>{let p=Ne.init();return p.then(R=>(d.current=R)&&w(!1)).catch(R=>R?.type!==\"cancelation\"&&console.error(\"Monaco initialization: error:\",R)),()=>o.current?pe():p.cancel()}),l(()=>{let p=h(d.current,e||t||\"\",r||a||\"\",m||n||\"\");p!==o.current?.getModel()&&(y&&_.set(f,o.current?.saveViewState()),o.current?.setModel(p),y&&o.current?.restoreViewState(_.get(m)))},[m],s),l(()=>{o.current?.updateOptions(x)},[x],s),l(()=>{!o.current||t===void 0||(o.current.getOption(d.current.editor.EditorOption.readOnly)?o.current.setValue(t):t!==o.current.getValue()&&(B.current=!0,o.current.executeEdits(\"\",[{range:o.current.getModel().getFullModelRange(),text:t,forceMoveMarkers:!0}]),o.current.pushUndoStop(),B.current=!1))},[t],s),l(()=>{let p=o.current?.getModel();p&&a&&d.current?.editor.setModelLanguage(p,a)},[a],s),l(()=>{g!==void 0&&o.current?.revealLine(g)},[g],s),l(()=>{d.current?.editor.setTheme(E)},[E],s);let X=_e(()=>{if(!(!b.current||!d.current)&&!Q.current){U.current(d.current);let p=m||n,R=h(d.current,t||e||\"\",r||a||\"\",p||\"\");o.current=d.current?.editor.create(b.current,{model:R,automaticLayout:!0,...x},P),y&&o.current.restoreViewState(_.get(p)),d.current.editor.setTheme(E),g!==void 0&&o.current.revealLine(g),u(!0),Q.current=!0}},[e,r,n,t,a,m,x,P,y,E,g]);W(()=>{s&&L.current(o.current,d.current)},[s]),W(()=>{!c&&!s&&X()},[c,s,X]),i.current=t,W(()=>{s&&O&&(I.current?.dispose(),I.current=o.current?.onDidChangeModelContent(p=>{B.current||O(o.current.getValue(),p)}))},[s,O]),W(()=>{if(s){let p=d.current.editor.onDidChangeMarkers(R=>{let G=o.current.getModel()?.uri;if(G&&R.find(J=>J.path===G.path)){let J=d.current.editor.getModelMarkers({resource:G});T?.(J)}});return()=>{p?.dispose()}}return()=>{}},[s,T]);function pe(){I.current?.dispose(),V?y&&_.set(m,o.current.saveViewState()):o.current.getModel()?.dispose(),o.current.dispose()}return We.createElement(H,{width:z,height:F,isEditorReady:s,loading:N,_ref:b,className:j,wrapperProps:A})}var fe=Ve;var de=ze(fe);var Ft=de;export{we as DiffEditor,de as Editor,Ft as default,_t as loader,Le as useMonaco};\n//# sourceMappingURL=index.mjs.map", "import React, { useState } from \"react\";\r\nimport Editor from \"@monaco-editor/react\";\r\nimport { loader } from \"@monaco-editor/react\";\r\n\r\n// Configure Monaco Editor to use local files\r\nloader.config({\r\n  paths: {\r\n    vs: '/monaco-editor/vs'\r\n  }\r\n});\r\n\r\nexport const MonacoEditor = ({\r\n  value,\r\n  editorRef,\r\n  language,\r\n  onChange,\r\n  minimap = true,\r\n  className,\r\n}: {\r\n  value: string;\r\n  onChange?: (value: string) => void;\r\n  editorRef: any;\r\n  language: string;\r\n  minimap?: boolean;\r\n  className?: string;\r\n}) => {\r\n  const [isEditorReady, setIsEditorReady] = useState(false);\r\n  const onEditorDidMount = (editor: any, monaco: any) => {\r\n    editorRef.current = editor;\r\n    setIsEditorReady(true);\r\n  };\r\n  return (\r\n    <div id=\"monaco-editor\" className={`h-full rounded ${className}`}>\r\n      <Editor\r\n        height=\"100%\"\r\n        className=\"h-full rounded\"\r\n        defaultLanguage={language}\r\n        defaultValue={value}\r\n        value={value}\r\n        onChange={(value: string | undefined) => {\r\n          if (onChange && value) {\r\n            onChange(value);\r\n          }\r\n        }}\r\n        onMount={onEditorDidMount}\r\n        theme=\"vs-dark\"\r\n        options={{\r\n          wordWrap: \"on\",\r\n          wrappingIndent: \"indent\",\r\n          wrappingStrategy: \"advanced\",\r\n          minimap: {\r\n            enabled: minimap,\r\n          },\r\n        }}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n"], "names": ["genAlertTypeStyle", "bgColor", "borderColor", "iconColor", "token", "alertCls", "background", "border", "lineWidth", "lineType", "color", "genBaseStyle", "componentCls", "motionDurationSlow", "duration", "marginXS", "marginSM", "fontSize", "fontSizeLG", "lineHeight", "borderRadiusLG", "borderRadius", "motionEaseInOutCirc", "withDescriptionIconSize", "colorText", "colorTextHeading", "withDescriptionPadding", "defaultPadding", "Object", "assign", "position", "display", "alignItems", "padding", "wordWrap", "direction", "flex", "min<PERSON><PERSON><PERSON>", "marginInlineEnd", "overflow", "opacity", "transition", "maxHeight", "marginBottom", "paddingTop", "paddingBottom", "genTypeStyle", "colorSuccess", "colorSuccessBorder", "colorSuccessBg", "colorWarning", "colorWarningBorder", "colorWarningBg", "colorError", "colorErrorBorder", "colorErrorBg", "colorInfo", "colorInfoBorder", "colorInfoBg", "margin", "genActionStyle", "iconCls", "motionDurationMid", "fontSizeIcon", "colorIcon", "colorIconHover", "marginInlineStart", "backgroundColor", "outline", "cursor", "fontSizeHeading3", "paddingContentVerticalSM", "paddingMD", "paddingContentHorizontalLG", "__rest", "s", "e", "t", "p", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "iconMapFilled", "success", "CheckCircleFilled", "info", "InfoCircleFilled", "error", "CloseCircleFilled", "warning", "ExclamationCircleFilled", "IconNode", "props", "icon", "prefixCls", "type", "iconType", "className", "CloseIconNode", "isClosable", "closeIcon", "handleClose", "ariaProps", "mergedCloseIcon", "undefined", "CloseOutlined", "onClick", "tabIndex", "<PERSON><PERSON>", "ref", "description", "customizePrefixCls", "message", "banner", "rootClassName", "style", "onMouseEnter", "onMouseLeave", "afterClose", "showIcon", "closable", "closeText", "action", "id", "otherProps", "closed", "setClosed", "internalRef", "nativeElement", "current", "getPrefixCls", "contextClosable", "contextCloseIcon", "contextClassName", "contextStyle", "wrapCSSVar", "hashId", "cssVarCls", "_a", "onClose", "isShowIcon", "restProps", "pickAttrs", "aria", "data", "mergedAriaProps", "merged", "_", "visible", "motionName", "motionAppear", "motionEnter", "onLeaveStart", "node", "offsetHeight", "onLeaveEnd", "motionClassName", "motionStyle", "setRef", "role", "Error<PERSON>ou<PERSON><PERSON>", "_React$Component", "_this", "o", "this", "arguments", "getPrototypeOf", "possibleConstructorReturn", "isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "state", "componentStack", "key", "value", "setState", "children", "errorMessage", "toString", "errorDescription", "overflowX", "Download", "d", "points", "x1", "x2", "y1", "y2", "_defineProperty", "obj", "defineProperty", "enumerable", "configurable", "writable", "ownKeys", "object", "enumerableOnly", "keys", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "push", "_objectSpread2", "target", "source", "for<PERSON>ach", "getOwnPropertyDescriptors", "defineProperties", "_objectWithoutProperties", "excluded", "sourceKeys", "_objectWithoutPropertiesLoose", "sourceSymbolKeys", "_arrayLikeToArray", "arr", "len", "arr2", "Array", "curry", "fn", "curried", "_len2", "args", "_key2", "_len3", "nextArgs", "_key3", "concat", "isObject", "includes", "isFunction", "<PERSON><PERSON><PERSON><PERSON>", "errorMessages", "Error", "initialIsRequired", "initialType", "initialContent", "handlerType", "handlersType", "selectorType", "changeType", "changeField", "validators", "changes", "initial", "some", "field", "property", "selector", "handler", "values", "_handler", "extractChanges", "<PERSON><PERSON><PERSON><PERSON>", "updateState", "didStateUpdate", "_handler$field", "index", "create", "didUpdate", "update", "validate", "getChanges", "_len", "fns", "_key", "x", "reduceRight", "y", "f", "compose", "paths", "vs", "configIsRequired", "configType", "deprecation", "config", "urls", "console", "warn", "monacoBase", "merge", "CANCELATION_MESSAGE", "msg", "promise", "hasCanceled_", "wrappedPromise", "Promise", "resolve", "reject", "then", "val", "cancel", "_state$create", "isInitialized", "monaco", "_state$create2", "isArray", "_arrayWithHoles", "Symbol", "iterator", "_arr", "_n", "_d", "_e", "_s", "_i", "next", "done", "err", "_iterableToArrayLimit", "minLen", "n", "slice", "name", "from", "test", "_unsupportedIterableToArray", "TypeError", "_nonIterableRest", "getState", "injectScripts", "script", "document", "body", "append<PERSON><PERSON><PERSON>", "getMonacoLoaderScript", "configure<PERSON><PERSON><PERSON>", "src", "_ref2", "loaderScript", "createElement", "onload", "onerror", "_ref3", "require", "window", "storeMonacoInstance", "wrapperPromise", "loader", "globalConfig", "_validators$config", "init", "_ref", "editor", "__getMonacoInstance", "_ref4", "v", "wrapper", "textAlign", "fullWidth", "width", "hide", "Y", "container", "height", "justifyContent", "$", "ee", "r", "isEditorReady", "loading", "a", "m", "wrapperProps", "E", "H", "k", "l", "D", "h", "getModel", "te", "De", "createModel", "be", "<PERSON><PERSON>", "parse", "ie", "original", "modified", "language", "originalLanguage", "modifiedLanguage", "originalModelPath", "modifiedModelPath", "keepCurrentOriginalModel", "g", "keepCurrentModifiedModel", "N", "theme", "P", "options", "V", "z", "F", "j", "beforeMount", "A", "onMount", "q", "M", "O", "T", "u", "c", "w", "b", "catch", "dispose", "I", "getOriginalEditor", "setModel", "getModifiedEditor", "getOption", "EditorOption", "readOnly", "setValue", "getValue", "executeEdits", "range", "getFullModelRange", "text", "forceMoveMarkers", "pushUndoStop", "setModelLanguage", "setTheme", "updateOptions", "L", "U", "createDiffEditor", "automaticLayout", "se", "Map", "fe", "defaultValue", "defaultLanguage", "defaultPath", "path", "line", "overrideServices", "saveViewState", "keepCurrentModel", "onChange", "onValidate", "Q", "B", "R", "set", "restoreViewState", "get", "revealLine", "X", "model", "onDidChangeModelContent", "onDidChangeMarkers", "G", "uri", "find", "J", "getModelMarkers", "resource", "Ft", "MonacoEditor", "editor<PERSON><PERSON>", "minimap", "setIsEditorReady", "useState", "React", "Editor", "onEditorDidMount", "wrappingIndent", "wrappingStrategy", "enabled"], "sourceRoot": ""}