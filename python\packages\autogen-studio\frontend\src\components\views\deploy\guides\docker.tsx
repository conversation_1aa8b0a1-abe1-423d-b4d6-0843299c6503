import React from "react";
import { Alert } from "antd";
import { CodeSection, copyToClipboard } from "./guides";

const DockerGuide: React.FC = () => {
  return (
    <div className="max-w-4xl">
      <h1 className="tdext-2xl font-bold mb-6">Docker 容器设置</h1>

      <Alert
        className="mb-6"
        message="前置条件"
        description={
          <ul className="list-disc pl-4 mt-2 space-y-1">
            <li>系统上已安装 Docker</li>
          </ul>
        }
        type="info"
      />
      <CodeSection
        title="1. Dockerfile"
        description=<div>
          多智能体工作室 提供了一个
          <a
            href="https://github.com/microsoft/autogen/blob/main/python/packages/autogen-studio/Dockerfile"
            target="_blank"
            rel="noreferrer"
            className="text-accent underline px-1"
          >
            Dockerfile
          </a>
          ，您可以使用它来构建您的 Docker 容器。{" "}
        </div>
        code={`FROM python:3.10-slim

WORKDIR /code

RUN pip install -U gunicorn autogenstudio

RUN useradd -m -u 1000 user
USER user
ENV HOME=/home/<USER>
    PATH=/home/<USER>/.local/bin:$PATH 
    AUTOGENSTUDIO_APPDIR=/home/<USER>/app

WORKDIR $HOME/app

COPY --chown=user . $HOME/app

CMD gunicorn -w $((2 * $(getconf _NPROCESSORS_ONLN) + 1)) --timeout 12600 -k uvicorn.workers.UvicornWorker autogenstudio.web.app:app --bind "0.0.0.0:8081"`}
        onCopy={copyToClipboard}
      />

      {/* Build and Run */}
      <CodeSection
        title="2. 构建和运行"
        description="构建并运行您的 Docker 容器："
        code={`docker build -t autogenstudio .
docker run -p 8081:8081 autogenstudio`}
        onCopy={copyToClipboard}
      />
    </div>
  );
};

export default DockerGuide;
