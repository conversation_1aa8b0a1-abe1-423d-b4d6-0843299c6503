const fs = require('fs-extra');
const path = require('path');

async function copyBuild() {
  const sourceDir = path.join(__dirname, '..', 'public');
  const targetDir = path.join(__dirname, '..', '..', 'autogenstudio', 'web', 'ui');
  
  try {
    // 确保目标目录存在
    await fs.ensureDir(targetDir);
    
    // 清空目标目录
    await fs.emptyDir(targetDir);
    
    // 复制文件
    await fs.copy(sourceDir, targetDir);
    
    console.log('Build files copied successfully!');
  } catch (err) {
    console.error('Error copying build files:', err);
    process.exit(1);
  }
}

copyBuild();
