/*! For license information please see component---src-pages-labs-tsx-5cfcfda7c438b5493b69.js.LICENSE.txt */
"use strict";(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[439],{4060:function(e,t,o){o.d(t,{A:function(){return n}});const n=(0,o(1788).A)("RefreshCcw",[["path",{d:"M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"14sxne"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16",key:"1hlbsb"}],["path",{d:"M16 16h5v5",key:"ccwih5"}]])},6713:function(e,t,o){o.r(t),o.d(t,{default:function(){return P}});var n=o(6540),r=o(1155),a=o(7677),l=o(418),s=o(367),c=o(9910),i=o(9644),d=o(4060),m=o(7213);const u=e=>{let{isOpen:t,labs:o,currentLab:r,onToggle:a,onSelectLab:l,isLoading:u=!1}=e;return t?n.createElement("div",{className:"h-full border-r border-secondary"},n.createElement("div",{className:"flex items-center justify-between pt-0 p-4 pl-2 pr-2 border-b border-secondary"},n.createElement("div",{className:"flex items-center gap-2"},n.createElement("span",{className:"text-primary font-medium"},"Labs")),n.createElement(s.A,{title:"Close Sidebar"},n.createElement("button",{onClick:a,className:"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50"},n.createElement(i.A,{strokeWidth:1.5,className:"h-6 w-6"})))),u&&n.createElement("div",{className:"p-4"},n.createElement(d.A,{className:"w-4 h-4 inline-block animate-spin"})),!u&&0===o.length&&n.createElement("div",{className:"p-2 mt-2 mr-2 text-center text-secondary text-sm border border-dashed rounded"},n.createElement(m.A,{className:"w-4 h-4 inline-block mr-1.5 -mt-0.5"}),"No labs available. Please check back later."),n.createElement("div",{className:"overflow-y-auto h-[calc(100%-64px)] mt-4"},o.map(e=>n.createElement("div",{key:e.id,className:"relative"},n.createElement("div",{className:"absolute top-1 left-0.5 z-50 h-[calc(100%-8px)]\n               w-1 bg-opacity-80 rounded "+((null==r?void 0:r.id)===e.id?"bg-accent":"bg-tertiary")}),n.createElement("div",{className:"group ml-1 flex flex-col p-2 rounded-l cursor-pointer hover:bg-secondary "+((null==r?void 0:r.id)===e.id?"border-accent bg-secondary":"border-transparent"),onClick:()=>l(e)},n.createElement("div",{className:"flex items-center justify-between"},n.createElement("span",{className:"text-sm truncate"},e.title))))))):n.createElement("div",{className:"h-full border-r border-secondary"},n.createElement("div",{className:"p-2 -ml-2"},n.createElement(s.A,{title:"Documentation"},n.createElement("button",{onClick:a,className:"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50"},n.createElement(c.A,{strokeWidth:1.5,className:"h-6 w-6"})))))},p=[{id:"tool-maker",title:"Tool Maker",type:"python"}];var g=o(7260);var f=()=>n.createElement("div",{className:""},n.createElement("h1",{className:"tdext-2xl font-bold mb-6"},"Using 多智能体工作室 Teams in Python Code and REST API"),n.createElement(g.A,{className:"mb-6",message:"Prerequisites",description:n.createElement("ul",{className:"list-disc pl-4 mt-2 space-y-1"},n.createElement("li",null,"多智能体工作室 installed")),type:"info"})),b=o(436),h=o(6914),v=o(9957),y=o(2941),E=o(4716),x=o(8168),C={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M931.4 498.9L94.9 79.5c-3.4-1.7-7.3-2.1-11-1.2a15.99 15.99 0 00-11.7 19.3l86.2 352.2c1.3 5.3 5.2 9.6 10.4 11.3l147.7 50.7-147.6 50.7c-5.2 1.8-9.1 6-10.3 11.3L72.2 926.5c-.9 3.7-.5 7.6 1.2 10.9 3.9 7.9 13.5 11.1 21.5 7.2l836.5-417c3.1-1.5 5.6-4.1 7.2-7.1 3.9-8 .7-17.6-7.2-21.6zM170.8 826.3l50.3-205.6 295.2-101.3c2.3-.8 4.2-2.6 5-5 1.4-4.2-.8-8.7-5-10.2L221.1 403 171 198.2l628 314.9-628.2 313.2z"}}]},name:"send",theme:"outlined"},w=o(7064),N=function(e,t){return n.createElement(w.A,(0,x.A)({},e,{ref:t,icon:C}))};var $=n.forwardRef(N),S=o(7387);let k=function(e){function t(){for(var t,o=arguments.length,n=new Array(o),r=0;r<o;r++)n[r]=arguments[r];return(t=e.call.apply(e,[this].concat(n))||this).ws=null,t}(0,S.A)(t,e);var o=t.prototype;return o.getWebSocketBaseUrl=function(e){try{let t=e.replace(/(^\w+:|^)\/\//,"");return t=t.startsWith("localhost")?t.replace("/api",""):"/api"===t?window.location.host:t.replace("/api","").replace(/\/$/,""),t}catch(t){throw new Error("Invalid server URL configuration")}},o.connect=function(e,t,o){const n=this.getBaseUrl(),r=this.getWebSocketBaseUrl(n),a=`${"https:"===window.location.protocol?"wss:":"ws:"}//${r}/api/maker/tool`;this.ws=new window.WebSocket(a),this.ws.onmessage=o=>{try{const t=JSON.parse(o.data);e(t)}catch(n){t&&t(n)}},this.ws.onerror=e=>{t&&t(e)},this.ws.onclose=()=>{o&&o()}},o.sendDescription=function(e){this.ws&&1===this.ws.readyState&&this.ws.send(JSON.stringify({description:e}))},o.close=function(){this.ws&&this.ws.close()},t}(o(3838).y);const A=new k;var O=()=>{const{0:e,1:t}=(0,n.useState)(""),{0:o,1:r}=(0,n.useState)([]),{0:a,1:l}=(0,n.useState)(null),{0:s,1:c}=(0,n.useState)(!1),{0:i,1:d}=(0,n.useState)(null),m=(0,n.useRef)(null),u=()=>{r([]),l(null),d(null),c(!0),m.current=A,m.current.connect(e=>{if("event"in e)r(t=>[].concat((0,b.A)(t),[e.event]));else if("component"in e){var t;l(e.component),c(!1),null===(t=m.current)||void 0===t||t.close()}else if("error"in e){var o;d(e.error),c(!1),null===(o=m.current)||void 0===o||o.close()}},e=>{d("WebSocket error: "+e),c(!1)},()=>{c(!1)}),setTimeout(()=>{var t;null===(t=m.current)||void 0===t||t.sendDescription(e)},200)};return n.createElement("div",{className:""},n.createElement("h1",{className:"text-2xl font-bold mb-6"},"Tool Maker (Experimental)"),n.createElement("p",{className:"mb-4 text-secondary"},"This lab allows you to create and test new tools using natural language descriptions."),n.createElement("div",{className:"mb-4"},n.createElement("p",{className:"text-sm font-medium mb-2 text-secondary"},"Try these examples:"),n.createElement("div",{className:"flex flex-wrap gap-2"},["A tool that fetches the content of a web page","A calculator tool that performs mathematical operations","A tool that generates QR codes from text","A tool that converts text to speech","A tool that extracts text from images (OCR)","A tool that validates email addresses","A tool that generates secure random passwords","A tool that converts currencies using live exchange rates"].map((e,o)=>n.createElement(h.A,{key:o,className:"cursor-pointer hover:bg-primary/10 transition-colors",onClick:()=>(e=>{t(e)})(e)},e)))),n.createElement("div",{className:"flex gap-2 mb-4"},n.createElement(v.A,{value:e,onChange:e=>t(e.target.value),placeholder:"Describe your tool (e.g. 'A tool that fetches the content of a web page')",onPressEnter:u,disabled:s}),n.createElement(y.Ay,{type:"primary",icon:n.createElement($,null),onClick:u,loading:s,disabled:!e.trim()||s},"Create")),i&&n.createElement(g.A,{type:"error",message:i,className:"mb-4"}),s&&n.createElement(E.A,{className:"mb-4"}),n.createElement("div",{className:"mb-4"},o.map((e,t)=>n.createElement(g.A,{key:t,type:"info",message:e.status,description:e.content,className:"mb-2"}))),a&&n.createElement("div",{className:"border rounded p-4 bg-secondary/10"},n.createElement("h2",{className:"font-semibold mb-2"},"Generated Tool"),n.createElement("pre",{className:"bg-secondary/20 p-2 rounded text-xs overflow-x-auto mb-2"},JSON.stringify(a,null,2))))};const I=e=>{let{lab:t}=e;switch(t.id){case"python-setup":return n.createElement(f,null);case"tool-maker":return n.createElement(O,null);default:return n.createElement("div",{className:"text-secondary"},"A Lab with the title ",n.createElement("strong",null,t.title)," is work in progress!")}};var j=()=>{const{0:e,1:t}=(0,n.useState)(!1),{0:o,1:r}=(0,n.useState)([]),{0:s,1:c}=(0,n.useState)(null),{0:i,1:d}=(0,n.useState)(()=>{if("undefined"!=typeof window){const e=localStorage.getItem("labsSidebar");return null===e||JSON.parse(e)}return!0});return(0,n.useEffect)(()=>{"undefined"!=typeof window&&localStorage.setItem("labsSidebar",JSON.stringify(i))},[i]),(0,n.useEffect)(()=>{!s&&o.length>0&&c(o[0])},[o,s]),(0,n.useEffect)(()=>{r(p)},[]),n.createElement("div",{className:"relative    flex h-full w-full"},n.createElement("div",{className:"absolute  left-0 top-0 h-full transition-all duration-200 ease-in-out "+(i?"w-64":"w-12")},n.createElement(u,{isOpen:i,labs:o,currentLab:s,onToggle:()=>d(!i),onSelectLab:c,isLoading:e})),n.createElement("div",{className:"flex-1 transition-all max-w-5xl  -mr-6 duration-200 "+(i?"ml-64":"ml-12")},n.createElement("div",{className:"p-4 pt-2"},n.createElement("div",{className:"flex items-center gap-2 mb-4 text-sm"},n.createElement("span",{className:"text-primary font-medium"},"Labs"),s&&n.createElement(n.Fragment,null,n.createElement(a.A,{className:"w-4 h-4 text-secondary"}),n.createElement("span",{className:"text-secondary"},s.title))),n.createElement("div",{className:"rounded border border-secondary border-dashed p-2 text-sm mb-4"},n.createElement(l.A,{className:"w-4 h-4 inline-block mr-2 -mt-1 text-secondary "})," ","Labs is designed to host experimental features for building and debugging multiagent applications."),s?n.createElement(I,{lab:s}):n.createElement("div",{className:"flex items-center justify-center h-[calc(100vh-190px)] text-secondary"},"Select a lab from the sidebar to get started"))))};var P=e=>{let{data:t}=e;return n.createElement(r.A,{meta:t.site.siteMetadata,title:"实验室",link:"/labs"},n.createElement("main",{style:{height:"100%"},className:" h-full "},n.createElement(j,null)))}},6914:function(e,t,o){o.d(t,{A:function(){return I}});var n=o(6540),r=o(6942),a=o.n(r),l=o(9853),s=o(4121),c=o(8055),i=o(682),d=o(57),m=o(2279),u=o(2187),p=o(2616),g=o(5905),f=o(4277),b=o(7358);const h=e=>{const{lineWidth:t,fontSizeIcon:o,calc:n}=e,r=e.fontSizeSM;return(0,f.oX)(e,{tagFontSize:r,tagLineHeight:(0,u.zA)(n(e.lineHeightSM).mul(r).equal()),tagIconSize:n(o).sub(n(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},v=e=>({defaultBg:new p.Y(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var y=(0,b.OF)("Tag",e=>(e=>{const{paddingXXS:t,lineWidth:o,tagPaddingHorizontal:n,componentCls:r,calc:a}=e,l=a(n).sub(o).equal(),s=a(t).sub(o).equal();return{[r]:Object.assign(Object.assign({},(0,g.dF)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:l,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,u.zA)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${r}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${r}-close-icon`]:{marginInlineStart:s,fontSize:e.tagIconSize,color:e.colorIcon,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${r}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${r}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:l}}),[`${r}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}})(h(e)),v),E=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(o[n[r]]=e[n[r]])}return o};const x=n.forwardRef((e,t)=>{const{prefixCls:o,style:r,className:l,checked:s,onChange:c,onClick:i}=e,d=E(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:u,tag:p}=n.useContext(m.QO),g=u("tag",o),[f,b,h]=y(g),v=a()(g,`${g}-checkable`,{[`${g}-checkable-checked`]:s},null==p?void 0:p.className,l,b,h);return f(n.createElement("span",Object.assign({},d,{ref:t,style:Object.assign(Object.assign({},r),null==p?void 0:p.style),className:v,onClick:e=>{null==c||c(!s),null==i||i(e)}})))});var C=x,w=o(1108);var N=(0,b.bf)(["Tag","preset"],e=>(e=>(0,w.A)(e,(t,{textColor:o,lightBorderColor:n,lightColor:r,darkColor:a})=>({[`${e.componentCls}${e.componentCls}-${t}`]:{color:o,background:r,borderColor:n,"&-inverse":{color:e.colorTextLightSolid,background:a,borderColor:a},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}})))(h(e)),v);const $=(e,t,o)=>{const n="string"!=typeof(r=o)?r:r.charAt(0).toUpperCase()+r.slice(1);var r;return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:e[`color${o}`],background:e[`color${n}Bg`],borderColor:e[`color${n}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}};var S=(0,b.bf)(["Tag","status"],e=>{const t=h(e);return[$(t,"success","Success"),$(t,"processing","Info"),$(t,"error","Error"),$(t,"warning","Warning")]},v),k=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(o[n[r]]=e[n[r]])}return o};const A=n.forwardRef((e,t)=>{const{prefixCls:o,className:r,rootClassName:u,style:p,children:g,icon:f,color:b,onClose:h,bordered:v=!0,visible:E}=e,x=k(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:C,direction:w,tag:$}=n.useContext(m.QO),[A,O]=n.useState(!0),I=(0,l.A)(x,["closeIcon","closable"]);n.useEffect(()=>{void 0!==E&&O(E)},[E]);const j=(0,s.nP)(b),P=(0,s.ZZ)(b),B=j||P,z=Object.assign(Object.assign({backgroundColor:b&&!B?b:void 0},null==$?void 0:$.style),p),L=C("tag",o),[M,T,H]=y(L),R=a()(L,null==$?void 0:$.className,{[`${L}-${b}`]:B,[`${L}-has-color`]:b&&!B,[`${L}-hidden`]:!A,[`${L}-rtl`]:"rtl"===w,[`${L}-borderless`]:!v},r,u,T,H),W=e=>{e.stopPropagation(),null==h||h(e),e.defaultPrevented||O(!1)},[,D]=(0,c.A)((0,c.d)(e),(0,c.d)($),{closable:!1,closeIconRender:e=>{const t=n.createElement("span",{className:`${L}-close-icon`,onClick:W},e);return(0,i.fx)(e,t,e=>({onClick:t=>{var o;null===(o=null==e?void 0:e.onClick)||void 0===o||o.call(e,t),W(t)},className:a()(null==e?void 0:e.className,`${L}-close-icon`)}))}}),F="function"==typeof x.onClick||g&&"a"===g.type,X=f||null,q=X?n.createElement(n.Fragment,null,X,g&&n.createElement("span",null,g)):g,U=n.createElement("span",Object.assign({},I,{ref:t,className:R,style:z}),q,D,j&&n.createElement(N,{key:"preset",prefixCls:L}),P&&n.createElement(S,{key:"status",prefixCls:L}));return M(F?n.createElement(d.A,{component:"Tag"},U):U)}),O=A;O.CheckableTag=C;var I=O},7260:function(e,t,o){o.d(t,{A:function(){return R}});var n=o(6540),r=o(8811),a=o(6029),l=o(7852),s=o(7541),c=o(7850),i=o(6942),d=o.n(i),m=o(754),u=o(2065),p=o(8719),g=o(682),f=o(2279),b=o(2187),h=o(5905),v=o(7358);const y=(e,t,o,n,r)=>({background:e,border:`${(0,b.zA)(n.lineWidth)} ${n.lineType} ${t}`,[`${r}-icon`]:{color:o}}),E=e=>{const{componentCls:t,motionDurationSlow:o,marginXS:n,marginSM:r,fontSize:a,fontSizeLG:l,lineHeight:s,borderRadiusLG:c,motionEaseInOutCirc:i,withDescriptionIconSize:d,colorText:m,colorTextHeading:u,withDescriptionPadding:p,defaultPadding:g}=e;return{[t]:Object.assign(Object.assign({},(0,h.dF)(e)),{position:"relative",display:"flex",alignItems:"center",padding:g,wordWrap:"break-word",borderRadius:c,[`&${t}-rtl`]:{direction:"rtl"},[`${t}-content`]:{flex:1,minWidth:0},[`${t}-icon`]:{marginInlineEnd:n,lineHeight:0},"&-description":{display:"none",fontSize:a,lineHeight:s},"&-message":{color:u},[`&${t}-motion-leave`]:{overflow:"hidden",opacity:1,transition:`max-height ${o} ${i}, opacity ${o} ${i},\n        padding-top ${o} ${i}, padding-bottom ${o} ${i},\n        margin-bottom ${o} ${i}`},[`&${t}-motion-leave-active`]:{maxHeight:0,marginBottom:"0 !important",paddingTop:0,paddingBottom:0,opacity:0}}),[`${t}-with-description`]:{alignItems:"flex-start",padding:p,[`${t}-icon`]:{marginInlineEnd:r,fontSize:d,lineHeight:0},[`${t}-message`]:{display:"block",marginBottom:n,color:u,fontSize:l},[`${t}-description`]:{display:"block",color:m}},[`${t}-banner`]:{marginBottom:0,border:"0 !important",borderRadius:0}}},x=e=>{const{componentCls:t,colorSuccess:o,colorSuccessBorder:n,colorSuccessBg:r,colorWarning:a,colorWarningBorder:l,colorWarningBg:s,colorError:c,colorErrorBorder:i,colorErrorBg:d,colorInfo:m,colorInfoBorder:u,colorInfoBg:p}=e;return{[t]:{"&-success":y(r,n,o,e,t),"&-info":y(p,u,m,e,t),"&-warning":y(s,l,a,e,t),"&-error":Object.assign(Object.assign({},y(d,i,c,e,t)),{[`${t}-description > pre`]:{margin:0,padding:0}})}}},C=e=>{const{componentCls:t,iconCls:o,motionDurationMid:n,marginXS:r,fontSizeIcon:a,colorIcon:l,colorIconHover:s}=e;return{[t]:{"&-action":{marginInlineStart:r},[`${t}-close-icon`]:{marginInlineStart:r,padding:0,overflow:"hidden",fontSize:a,lineHeight:(0,b.zA)(a),backgroundColor:"transparent",border:"none",outline:"none",cursor:"pointer",[`${o}-close`]:{color:l,transition:`color ${n}`,"&:hover":{color:s}}},"&-close-text":{color:l,transition:`color ${n}`,"&:hover":{color:s}}}}};var w=(0,v.OF)("Alert",e=>[E(e),x(e),C(e)],e=>({withDescriptionIconSize:e.fontSizeHeading3,defaultPadding:`${e.paddingContentVerticalSM}px 12px`,withDescriptionPadding:`${e.paddingMD}px ${e.paddingContentHorizontalLG}px`})),N=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(o[n[r]]=e[n[r]])}return o};const $={success:r.A,info:c.A,error:a.A,warning:s.A},S=e=>{const{icon:t,prefixCls:o,type:r}=e,a=$[r]||null;return t?(0,g.fx)(t,n.createElement("span",{className:`${o}-icon`},t),()=>({className:d()(`${o}-icon`,t.props.className)})):n.createElement(a,{className:`${o}-icon`})},k=e=>{const{isClosable:t,prefixCls:o,closeIcon:r,handleClose:a,ariaProps:s}=e,c=!0===r||void 0===r?n.createElement(l.A,null):r;return t?n.createElement("button",Object.assign({type:"button",onClick:a,className:`${o}-close-icon`,tabIndex:0},s),c):null},A=n.forwardRef((e,t)=>{const{description:o,prefixCls:r,message:a,banner:l,className:s,rootClassName:c,style:i,onMouseEnter:g,onMouseLeave:b,onClick:h,afterClose:v,showIcon:y,closable:E,closeText:x,closeIcon:C,action:$,id:A}=e,O=N(e,["description","prefixCls","message","banner","className","rootClassName","style","onMouseEnter","onMouseLeave","onClick","afterClose","showIcon","closable","closeText","closeIcon","action","id"]),[I,j]=n.useState(!1);const P=n.useRef(null);n.useImperativeHandle(t,()=>({nativeElement:P.current}));const{getPrefixCls:B,direction:z,closable:L,closeIcon:M,className:T,style:H}=(0,f.TP)("alert"),R=B("alert",r),[W,D,F]=w(R),X=t=>{var o;j(!0),null===(o=e.onClose)||void 0===o||o.call(e,t)},q=n.useMemo(()=>void 0!==e.type?e.type:l?"warning":"info",[e.type,l]),U=n.useMemo(()=>!("object"!=typeof E||!E.closeIcon)||(!!x||("boolean"==typeof E?E:!1!==C&&null!=C||!!L)),[x,C,E,L]),J=!(!l||void 0!==y)||y,G=d()(R,`${R}-${q}`,{[`${R}-with-description`]:!!o,[`${R}-no-icon`]:!J,[`${R}-banner`]:!!l,[`${R}-rtl`]:"rtl"===z},T,s,c,F,D),Q=(0,u.A)(O,{aria:!0,data:!0}),Z=n.useMemo(()=>"object"==typeof E&&E.closeIcon?E.closeIcon:x||(void 0!==C?C:"object"==typeof L&&L.closeIcon?L.closeIcon:M),[C,E,x,M]),K=n.useMemo(()=>{const e=null!=E?E:L;if("object"==typeof e){const{closeIcon:t}=e;return N(e,["closeIcon"])}return{}},[E,L]);return W(n.createElement(m.Ay,{visible:!I,motionName:`${R}-motion`,motionAppear:!1,motionEnter:!1,onLeaveStart:e=>({maxHeight:e.offsetHeight}),onLeaveEnd:v},({className:t,style:r},l)=>n.createElement("div",Object.assign({id:A,ref:(0,p.K4)(P,l),"data-show":!I,className:d()(G,t),style:Object.assign(Object.assign(Object.assign({},H),i),r),onMouseEnter:g,onMouseLeave:b,onClick:h,role:"alert"},Q),J?n.createElement(S,{description:o,icon:e.icon,prefixCls:R,type:q}):null,n.createElement("div",{className:`${R}-content`},a?n.createElement("div",{className:`${R}-message`},a):null,o?n.createElement("div",{className:`${R}-description`},o):null),$?n.createElement("div",{className:`${R}-action`},$):null,n.createElement(k,{isClosable:U,prefixCls:R,closeIcon:Z,handleClose:X,ariaProps:K}))))});var O=A,I=o(3029),j=o(2901),P=o(3954),B=o(2176),z=o(6822);var L=o(5501);let M=function(e){function t(){var e,o,n,r;return(0,I.A)(this,t),o=this,n=t,r=arguments,n=(0,P.A)(n),(e=(0,z.A)(o,(0,B.A)()?Reflect.construct(n,r||[],(0,P.A)(o).constructor):n.apply(o,r))).state={error:void 0,info:{componentStack:""}},e}return(0,L.A)(t,e),(0,j.A)(t,[{key:"componentDidCatch",value:function(e,t){this.setState({error:e,info:t})}},{key:"render",value:function(){const{message:e,description:t,id:o,children:r}=this.props,{error:a,info:l}=this.state,s=(null==l?void 0:l.componentStack)||null,c=void 0===e?(a||"").toString():e,i=void 0===t?s:t;return a?n.createElement(O,{id:o,type:"error",message:c,description:n.createElement("pre",{style:{fontSize:"0.9em",overflowX:"auto"}},i)}):r}}])}(n.Component);var T=M;const H=O;H.ErrorBoundary=T;var R=H}}]);
//# sourceMappingURL=component---src-pages-labs-tsx-5cfcfda7c438b5493b69.js.map