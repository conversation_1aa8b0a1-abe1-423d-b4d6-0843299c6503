/*! For license information please see component---src-pages-build-tsx-8fffd6b35bd63e5515c0.js.LICENSE.txt */
(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[477],{2:function(e,t,n){var r=n(2199),o=n(4664),a=n(5950);e.exports=function(e){return r(e,a,o)}},79:function(e,t,n){var r=n(3702),o=n(80),a=n(4739),i=n(8655),c=n(1175);function s(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}s.prototype.clear=r,s.prototype.delete=o,s.prototype.get=a,s.prototype.has=i,s.prototype.set=c,e.exports=s},80:function(e,t,n){var r=n(6025),o=Array.prototype.splice;e.exports=function(e){var t=this.__data__,n=r(t,e);return!(n<0)&&(n==t.length-1?t.pop():o.call(t,n,1),--this.size,!0)}},270:function(e,t,n){var r=n(7068),o=n(346);e.exports=function e(t,n,a,i,c){return t===n||(null==t||null==n||!o(t)&&!o(n)?t!=t&&n!=n:r(t,n,a,i,e,c))}},289:function(e,t,n){var r=n(2651);e.exports=function(e){return r(this,e).get(e)}},294:function(e){e.exports=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}},317:function(e){e.exports=function(e){var t=-1,n=Array(e.size);return e.forEach(function(e,r){n[++t]=[r,e]}),n}},346:function(e){e.exports=function(e){return null!=e&&"object"==typeof e}},361:function(e){var t=/^(?:0|[1-9]\d*)$/;e.exports=function(e,n){var r=typeof e;return!!(n=null==n?9007199254740991:n)&&("number"==r||"symbol"!=r&&t.test(e))&&e>-1&&e%1==0&&e<n}},392:function(e){e.exports=function(e,t){return null==e?void 0:e[t]}},659:function(e,t,n){var r=n(1873),o=Object.prototype,a=o.hasOwnProperty,i=o.toString,c=r?r.toStringTag:void 0;e.exports=function(e){var t=a.call(e,c),n=e[c];try{e[c]=void 0;var r=!0}catch(s){}var o=i.call(e);return r&&(t?e[c]=n:delete e[c]),o}},689:function(e,t,n){var r=n(2),o=Object.prototype.hasOwnProperty;e.exports=function(e,t,n,a,i,c){var s=1&n,l=r(e),d=l.length;if(d!=r(t).length&&!s)return!1;for(var u=d;u--;){var m=l[u];if(!(s?m in t:o.call(t,m)))return!1}var p=c.get(e),f=c.get(t);if(p&&f)return p==t&&f==e;var g=!0;c.set(e,t),c.set(t,e);for(var v=s;++u<d;){var h=e[m=l[u]],y=t[m];if(a)var b=s?a(y,h,m,t,e,c):a(h,y,m,e,t,c);if(!(void 0===b?h===y||i(h,y,n,a,c):b)){g=!1;break}v||(v="constructor"==m)}if(g&&!v){var x=e.constructor,E=t.constructor;x==E||!("constructor"in e)||!("constructor"in t)||"function"==typeof x&&x instanceof x&&"function"==typeof E&&E instanceof E||(g=!1)}return c.delete(e),c.delete(t),g}},695:function(e,t,n){var r=n(8096),o=n(2428),a=n(6449),i=n(3656),c=n(361),s=n(7167),l=Object.prototype.hasOwnProperty;e.exports=function(e,t){var n=a(e),d=!n&&o(e),u=!n&&!d&&i(e),m=!n&&!d&&!u&&s(e),p=n||d||u||m,f=p?r(e.length,String):[],g=f.length;for(var v in e)!t&&!l.call(e,v)||p&&("length"==v||u&&("offset"==v||"parent"==v)||m&&("buffer"==v||"byteLength"==v||"byteOffset"==v)||c(v,g))||f.push(v);return f}},938:function(e){e.exports=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n}},945:function(e,t,n){var r=n(79),o=n(8223),a=n(3661);e.exports=function(e,t){var n=this.__data__;if(n instanceof r){var i=n.__data__;if(!o||i.length<199)return i.push([e,t]),this.size=++n.size,this;n=this.__data__=new a(i)}return n.set(e,t),this.size=n.size,this}},1042:function(e,t,n){var r=n(6110)(Object,"create");e.exports=r},1175:function(e,t,n){var r=n(6025);e.exports=function(e,t){var n=this.__data__,o=r(n,e);return o<0?(++this.size,n.push([e,t])):n[o][1]=t,this}},1380:function(e){e.exports=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this}},1420:function(e,t,n){var r=n(79);e.exports=function(){this.__data__=new r,this.size=0}},1459:function(e){e.exports=function(e){return this.__data__.has(e)}},1549:function(e,t,n){var r=n(2032),o=n(3862),a=n(6721),i=n(2749),c=n(5749);function s(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}s.prototype.clear=r,s.prototype.delete=o,s.prototype.get=a,s.prototype.has=i,s.prototype.set=c,e.exports=s},1873:function(e,t,n){var r=n(9325).Symbol;e.exports=r},1882:function(e,t,n){var r=n(2552),o=n(3805);e.exports=function(e){if(!o(e))return!1;var t=r(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}},1986:function(e,t,n){var r=n(1873),o=n(7828),a=n(5288),i=n(5911),c=n(317),s=n(4247),l=r?r.prototype:void 0,d=l?l.valueOf:void 0;e.exports=function(e,t,n,r,l,u,m){switch(n){case"[object DataView]":if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case"[object ArrayBuffer]":return!(e.byteLength!=t.byteLength||!u(new o(e),new o(t)));case"[object Boolean]":case"[object Date]":case"[object Number]":return a(+e,+t);case"[object Error]":return e.name==t.name&&e.message==t.message;case"[object RegExp]":case"[object String]":return e==t+"";case"[object Map]":var p=c;case"[object Set]":var f=1&r;if(p||(p=s),e.size!=t.size&&!f)return!1;var g=m.get(e);if(g)return g==t;r|=2,m.set(e,t);var v=i(p(e),p(t),r,l,u,m);return m.delete(e),v;case"[object Symbol]":if(d)return d.call(e)==d.call(t)}return!1}},2032:function(e,t,n){var r=n(1042);e.exports=function(){this.__data__=r?r(null):{},this.size=0}},2199:function(e,t,n){var r=n(4528),o=n(6449);e.exports=function(e,t,n){var a=t(e);return o(e)?a:r(a,n(e))}},2404:function(e,t,n){var r=n(270);e.exports=function(e,t){return r(e,t)}},2428:function(e,t,n){var r=n(7534),o=n(346),a=Object.prototype,i=a.hasOwnProperty,c=a.propertyIsEnumerable,s=r(function(){return arguments}())?r:function(e){return o(e)&&i.call(e,"callee")&&!c.call(e,"callee")};e.exports=s},2552:function(e,t,n){var r=n(1873),o=n(659),a=n(9350),i=r?r.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":i&&i in Object(e)?o(e):a(e)}},2651:function(e,t,n){var r=n(4218);e.exports=function(e,t){var n=e.__data__;return r(t)?n["string"==typeof t?"string":"hash"]:n.map}},2749:function(e,t,n){var r=n(1042),o=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;return r?void 0!==t[e]:o.call(t,e)}},2804:function(e,t,n){var r=n(6110)(n(9325),"Promise");e.exports=r},2949:function(e,t,n){var r=n(2651);e.exports=function(e,t){var n=r(this,e),o=n.size;return n.set(e,t),this.size+=n.size==o?0:1,this}},3040:function(e,t,n){var r=n(1549),o=n(79),a=n(8223);e.exports=function(){this.size=0,this.__data__={hash:new r,map:new(a||o),string:new r}}},3345:function(e){e.exports=function(){return[]}},3605:function(e){e.exports=function(e){return this.__data__.get(e)}},3650:function(e,t,n){var r=n(4335)(Object.keys,Object);e.exports=r},3656:function(e,t,n){e=n.nmd(e);var r=n(9325),o=n(9935),a=t&&!t.nodeType&&t,i=a&&e&&!e.nodeType&&e,c=i&&i.exports===a?r.Buffer:void 0,s=(c?c.isBuffer:void 0)||o;e.exports=s},3661:function(e,t,n){var r=n(3040),o=n(7670),a=n(289),i=n(4509),c=n(2949);function s(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}s.prototype.clear=r,s.prototype.delete=o,s.prototype.get=a,s.prototype.has=i,s.prototype.set=c,e.exports=s},3702:function(e){e.exports=function(){this.__data__=[],this.size=0}},3769:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return Xr}});var r=n(6540),o=n(1155),a=n(436),i=n(9036),c=n(6108),s=n(7677),l=n(2744),d=n(2197),u=n(367),m=n(2941),p=n(9314),f=n(9910),g=n(697),v=n(9644),h=n(85),y=n(4060),b=n(6148),x=n(7213),E=n(2708),w=n(2640),N=n(5404),C=n(7163),k=n(2571),A=n(4810),S=n(180);const _=e=>{var t,n,o;let{isOpen:a,teams:c,currentTeam:s,onToggle:d,onSelectTeam:_,onCreateTeam:O,onEditTeam:T,onDeleteTeam:D,isLoading:I=!1,selectedGallery:R,setSelectedGallery:j}=e;const{0:M,1:H}=(0,r.useState)("recent"),[z,L]=i.Ay.useMessage(),{0:P,1:$}=(0,r.useState)(!1),{0:F,1:U}=(0,r.useState)([]),{user:B}=(0,r.useContext)(l.v);r.useEffect(()=>{(async()=>{if(null!=B&&B.id){$(!0);try{const e=new k.Z,t=await e.listGalleries(B.id);U(t);const n=(0,S.Lg)(`selectedGalleryId_${B.id}`);if(n&&t.length>0){const e=t.find(e=>e.id===n);e?j(e):!R&&t.length>0&&j(t[0])}else!R&&t.length>0&&j(t[0])}catch(e){console.error("Error fetching galleries:",e)}finally{$(!1)}}})()},[null==B?void 0:B.id]);const V=()=>{var e,t;if(null==R||null===(e=R.config.components)||void 0===e||null===(t=e.teams)||void 0===t||!t.length)return;const n=Object.assign({},{component:R.config.components.teams[0]});n.component.label="default_team"+(new Date).getTime().toString().slice(0,2),O(n),H("recent"),z.success(`"${n.component.label}" added to Recents`)};return a?r.createElement("div",{className:"h-full border-r border-secondary"},L,r.createElement("div",{className:"flex items-center justify-between pt-0 p-4 pl-2 pr-2 border-b border-secondary"},r.createElement("div",{className:"flex items-center gap-2"},r.createElement("span",{className:"text-primary font-medium"},"Teams"),r.createElement("span",{className:"px-2 py-0.5 text-xs bg-accent/10 text-accent rounded"},c.length)),r.createElement(u.A,{title:"Close Sidebar"},r.createElement("button",{onClick:d,className:"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50"},r.createElement(v.A,{strokeWidth:1.5,className:"h-6 w-6"})))),r.createElement("div",{className:"my-4 flex text-sm"},r.createElement("div",{className:"mr-2 w-full"},r.createElement(u.A,{title:"创建一个新团队"},r.createElement(m.Ay,{type:"primary",className:"w-full",icon:r.createElement(g.A,{className:"w-4 h-4"}),onClick:V,disabled:!(null!=R&&null!==(t=R.config.components)&&void 0!==t&&null!==(n=t.teams)&&void 0!==n&&n.length)},"新的团队")))),r.createElement("div",{className:"flex border-b border-secondary"},r.createElement("button",{style:{width:"110px"},className:"flex items-center  px-2 py-1 text-sm font-medium "+("recent"===M?"text-accent border-b-2 border-accent":"text-secondary hover:text-primary"),onClick:()=>H("recent")},!I&&r.createElement(r.Fragment,null," ",r.createElement(h.A,{className:"w-4 h-4 mr-1.5"})," 最近"," ",r.createElement("span",{className:"ml-1 text-xs"},"(",c.length,")")),I&&"recent"===M&&r.createElement(r.Fragment,null,"加载中 ",r.createElement(y.A,{className:"w-4 h-4 ml-2 animate-spin"}))),r.createElement("button",{className:"flex items-center px-4 py-2 text-sm font-medium "+("gallery"===M?"text-accent border-b-2 border-accent":"text-secondary hover:text-primary"),onClick:()=>H("gallery")},r.createElement(b.A,{className:"w-4 h-4 mr-1.5"}),"从库中选择",P&&"gallery"===M&&r.createElement(y.A,{className:"w-4 h-4 ml-2 animate-spin"}))),r.createElement("div",{className:"scroll overflow-y-auto h-[calc(100%-200px)]"},"recent"===M&&r.createElement("div",{className:"pt-2"},!I&&0===c.length&&r.createElement("div",{className:"p-2 mr-2 text-center text-secondary text-sm border border-dashed rounded"},r.createElement(x.A,{className:"w-4 h-4 inline-block mr-1.5 -mt-0.5"}),"未找到最近的团队"),c.length>0&&r.createElement("div",{className:I?"pointer-events-none":""},c.map(e=>{var t,n,o,a,i;return r.createElement("div",{key:e.id,className:"relative border-secondary"},r.createElement("div",{className:"absolute top-1 left-0.5 z-50 h-[calc(100%-8px)]\n                        w-1 bg-opacity-80 rounded "+((null==s?void 0:s.id)===e.id?"bg-accent":"bg-tertiary")}),r.createElement("div",{className:"group ml-1 flex flex-col p-3 rounded-l cursor-pointer hover:bg-secondary "+((null==s?void 0:s.id)===e.id?"border-accent bg-secondary":"border-transparent"),onClick:()=>_(e)},r.createElement("div",{className:"flex items-center justify-between"},r.createElement("span",{className:"font-medium truncate text-sm"},null===(t=e.component)||void 0===t?void 0:t.label),r.createElement("div",{className:"flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity"},r.createElement(u.A,{title:"Delete team"},r.createElement(m.Ay,{type:"text",size:"small",className:"p-0 min-w-[24px] h-6",danger:!0,icon:r.createElement(E.A,{className:"w-4 h-4 text-red-500"}),onClick:t=>{t.stopPropagation(),e.id&&D(e.id)}})))),r.createElement("div",{className:"mt-1 flex items-center gap-2 text-xs text-secondary"},r.createElement("span",{className:"bg-secondary/20 truncate rounded"},e.component.component_type),r.createElement("div",{className:"flex items-center gap-1"},r.createElement(w.A,{className:"w-3 h-3"}),r.createElement("span",null,(null===(n=e.component.config)||void 0===n||null===(o=n.participants)||void 0===o?void 0:o.length)||0," ",1===((null===(a=e.component.config)||void 0===a||null===(i=a.participants)||void 0===i?void 0:i.length)||0)?"agent":"agents"))),e.updated_at&&r.createElement("div",{className:"mt-1 flex items-center gap-1 text-xs text-secondary"},r.createElement("span",null,(0,C.vq)(e.updated_at)))))}))),"gallery"===M&&r.createElement("div",{className:"p-2"},r.createElement("div",{className:"my-2 mb-3 text-xs"}," ","Select a"," ",r.createElement(A.Link,{to:"/gallery",className:"text-accent"},r.createElement("span",{className:"font-medium"},"gallery"))," ","to view its components as templates"),r.createElement(p.A,{className:"w-full mb-4",placeholder:"Select gallery",value:null==R?void 0:R.id,onChange:e=>{const t=F.find(t=>t.id===e);t&&(j(t),null!=B&&B.id&&(0,S.ZB)(`selectedGalleryId_${B.id}`,e))},options:F.map(e=>({value:e.id,label:e.config.name})),loading:P}),null==R||null===(o=R.config.components)||void 0===o?void 0:o.teams.map(e=>{var t,n,o,a;return r.createElement("div",{key:e.label+e.component_type,className:"relative border-secondary -ml-2 group"},r.createElement("div",{className:"absolute top-1 left-0.5 z-50 h-[calc(100%-8px)]\n                  w-1 bg-opacity-80 rounded bg-tertiary group-hover:bg-accent"}),r.createElement("div",{className:"group ml-1 flex flex-col p-3 rounded-l cursor-pointer hover:bg-secondary"},r.createElement("div",{className:"flex items-center justify-between"},r.createElement("span",{className:"font-medium truncate text-sm"},e.label),r.createElement("div",{className:"flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity"},r.createElement(u.A,{title:"Use as template"},r.createElement(m.Ay,{type:"text",size:"small",className:"p-0 min-w-[24px] h-6",icon:r.createElement(N.A,{className:"w-4 h-4"}),onClick:t=>{t.stopPropagation();const n={component:{...e,label:`${e.label}_${((new Date).getTime()+"").substring(0,5)}`}};O(n),H("recent"),i.Ay.success(`"${n.component.label}" added to Recents`)}})))),r.createElement("div",{className:"mt-1 flex items-center gap-2 text-xs text-secondary"},r.createElement("span",{className:"bg-secondary/20 truncate rounded"},e.component_type),r.createElement("div",{className:"flex items-center gap-1"},r.createElement(w.A,{className:"w-3 h-3"}),r.createElement("span",null,(null===(t=e.config)||void 0===t||null===(n=t.participants)||void 0===n?void 0:n.length)||0," ",1===((null===(o=e.config)||void 0===o||null===(a=o.participants)||void 0===a?void 0:a.length)||0)?"agent":"agents")))))}),!R&&r.createElement("div",{className:"p-2 mr-2 text-center text-secondary text-sm border border-dashed rounded"},r.createElement(x.A,{className:"w-4 h-4 inline-block mr-1.5 -mt-0.5"}),"Select a gallery to view templates")))):r.createElement("div",{className:"h-full border-r border-secondary"},r.createElement("div",{className:"p-2 -ml-2"},r.createElement(u.A,{title:`Teams (${c.length})`},r.createElement("button",{onClick:d,className:"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50"},r.createElement(f.A,{strokeWidth:1.5,className:"h-6 w-6"})))),r.createElement("div",{className:"mt-4 px-2 -ml-1"},r.createElement(u.A,{title:"Create new team"},r.createElement(m.Ay,{type:"text",className:"w-full p-2 flex justify-center",onClick:()=>V(),icon:r.createElement(g.A,{className:"w-4 h-4"})}))))};var O=n(961);const T="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;function D(e){const t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function I(e){return"nodeType"in e}function R(e){var t,n;return e?D(e)?e:I(e)&&null!=(t=null==(n=e.ownerDocument)?void 0:n.defaultView)?t:window:window}function j(e){const{Document:t}=R(e);return e instanceof t}function M(e){return!D(e)&&e instanceof R(e).HTMLElement}function H(e){return e instanceof R(e).SVGElement}function z(e){return e?D(e)?e.document:I(e)?j(e)?e:M(e)||H(e)?e.ownerDocument:document:document:document}const L=T?r.useLayoutEffect:r.useEffect;function P(e){const t=(0,r.useRef)(e);return L(()=>{t.current=e}),(0,r.useCallback)(function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)},[])}function $(e,t){void 0===t&&(t=[e]);const n=(0,r.useRef)(e);return L(()=>{n.current!==e&&(n.current=e)},t),n}function F(e,t){const n=(0,r.useRef)();return(0,r.useMemo)(()=>{const t=e(n.current);return n.current=t,t},[...t])}function U(e){const t=P(e),n=(0,r.useRef)(null),o=(0,r.useCallback)(e=>{e!==n.current&&(null==t||t(e,n.current)),n.current=e},[]);return[n,o]}function B(e){const t=(0,r.useRef)();return(0,r.useEffect)(()=>{t.current=e},[e]),t.current}let V={};function G(e,t){return(0,r.useMemo)(()=>{if(t)return t;const n=null==V[e]?0:V[e]+1;return V[e]=n,e+"-"+n},[e,t])}function X(e){return function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return r.reduce((t,n)=>{const r=Object.entries(n);for(const[o,a]of r){const n=t[o];null!=n&&(t[o]=n+e*a)}return t},{...t})}}const W=X(1),Y=X(-1);function J(e){if(!e)return!1;const{KeyboardEvent:t}=R(e.target);return t&&e instanceof t}function Z(e){if(function(e){if(!e)return!1;const{TouchEvent:t}=R(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){const{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}if(e.changedTouches&&e.changedTouches.length){const{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return function(e){return"clientX"in e&&"clientY"in e}(e)?{x:e.clientX,y:e.clientY}:null}const K=Object.freeze({Translate:{toString(e){if(!e)return;const{x:t,y:n}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(n?Math.round(n):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;const{scaleX:t,scaleY:n}=e;return"scaleX("+t+") scaleY("+n+")"}},Transform:{toString(e){if(e)return[K.Translate.toString(e),K.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:n,easing:r}=e;return t+" "+n+"ms "+r}}}),q="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]";function Q(e){return e.matches(q)?e:e.querySelector(q)}const ee={display:"none"};function te(e){let{id:t,value:n}=e;return r.createElement("div",{id:t,style:ee},n)}function ne(e){let{id:t,announcement:n,ariaLiveType:o="assertive"}=e;return r.createElement("div",{id:t,style:{position:"fixed",top:0,left:0,width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"},role:"status","aria-live":o,"aria-atomic":!0},n)}const re=(0,r.createContext)(null);const oe={draggable:"\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  "},ae={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was moved over droppable area "+n.id+".":"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was dropped over droppable area "+n.id:"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function ie(e){let{announcements:t=ae,container:n,hiddenTextDescribedById:o,screenReaderInstructions:a=oe}=e;const{announce:i,announcement:c}=function(){const[e,t]=(0,r.useState)("");return{announce:(0,r.useCallback)(e=>{null!=e&&t(e)},[]),announcement:e}}(),s=G("DndLiveRegion"),[l,d]=(0,r.useState)(!1);if((0,r.useEffect)(()=>{d(!0)},[]),function(e){const t=(0,r.useContext)(re);(0,r.useEffect)(()=>{if(!t)throw new Error("useDndMonitor must be used within a children of <DndContext>");return t(e)},[e,t])}((0,r.useMemo)(()=>({onDragStart(e){let{active:n}=e;i(t.onDragStart({active:n}))},onDragMove(e){let{active:n,over:r}=e;t.onDragMove&&i(t.onDragMove({active:n,over:r}))},onDragOver(e){let{active:n,over:r}=e;i(t.onDragOver({active:n,over:r}))},onDragEnd(e){let{active:n,over:r}=e;i(t.onDragEnd({active:n,over:r}))},onDragCancel(e){let{active:n,over:r}=e;i(t.onDragCancel({active:n,over:r}))}}),[i,t])),!l)return null;const u=r.createElement(r.Fragment,null,r.createElement(te,{id:o,value:a.draggable}),r.createElement(ne,{id:s,announcement:c}));return n?(0,O.createPortal)(u,n):u}var ce;function se(){}!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(ce||(ce={}));const le=Object.freeze({x:0,y:0});function de(e,t){const n=Z(e);if(!n)return"0 0";return(n.x-t.left)/t.width*100+"% "+(n.y-t.top)/t.height*100+"%"}function ue(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return r-n}function me(e,t){const n=Math.max(t.top,e.top),r=Math.max(t.left,e.left),o=Math.min(t.left+t.width,e.left+e.width),a=Math.min(t.top+t.height,e.top+e.height),i=o-r,c=a-n;if(r<o&&n<a){const n=t.width*t.height,r=e.width*e.height,o=i*c;return Number((o/(n+r-o)).toFixed(4))}return 0}const pe=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e;const o=[];for(const a of r){const{id:e}=a,r=n.get(e);if(r){const n=me(r,t);n>0&&o.push({id:e,data:{droppableContainer:a,value:n}})}}return o.sort(ue)};function fe(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:le}function ge(e){return function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return r.reduce((t,n)=>({...t,top:t.top+e*n.y,bottom:t.bottom+e*n.y,left:t.left+e*n.x,right:t.right+e*n.x}),{...t})}}const ve=ge(1);function he(e){if(e.startsWith("matrix3d(")){const t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}if(e.startsWith("matrix(")){const t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}const ye={ignoreTransform:!1};function be(e,t){void 0===t&&(t=ye);let n=e.getBoundingClientRect();if(t.ignoreTransform){const{transform:t,transformOrigin:r}=R(e).getComputedStyle(e);t&&(n=function(e,t,n){const r=he(t);if(!r)return e;const{scaleX:o,scaleY:a,x:i,y:c}=r,s=e.left-i-(1-o)*parseFloat(n),l=e.top-c-(1-a)*parseFloat(n.slice(n.indexOf(" ")+1)),d=o?e.width/o:e.width,u=a?e.height/a:e.height;return{width:d,height:u,top:l,right:s+d,bottom:l+u,left:s}}(n,t,r))}const{top:r,left:o,width:a,height:i,bottom:c,right:s}=n;return{top:r,left:o,width:a,height:i,bottom:c,right:s}}function xe(e){return be(e,{ignoreTransform:!0})}function Ee(e,t){const n=[];return e?function r(o){if(null!=t&&n.length>=t)return n;if(!o)return n;if(j(o)&&null!=o.scrollingElement&&!n.includes(o.scrollingElement))return n.push(o.scrollingElement),n;if(!M(o)||H(o))return n;if(n.includes(o))return n;const a=R(e).getComputedStyle(o);return o!==e&&function(e,t){void 0===t&&(t=R(e).getComputedStyle(e));const n=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some(e=>{const r=t[e];return"string"==typeof r&&n.test(r)})}(o,a)&&n.push(o),function(e,t){return void 0===t&&(t=R(e).getComputedStyle(e)),"fixed"===t.position}(o,a)?n:r(o.parentNode)}(e):n}function we(e){const[t]=Ee(e,1);return null!=t?t:null}function Ne(e){return T&&e?D(e)?e:I(e)?j(e)||e===z(e).scrollingElement?window:M(e)?e:null:null:null}function Ce(e){return D(e)?e.scrollX:e.scrollLeft}function ke(e){return D(e)?e.scrollY:e.scrollTop}function Ae(e){return{x:Ce(e),y:ke(e)}}var Se;function _e(e){return!(!T||!e)&&e===document.scrollingElement}function Oe(e){const t={x:0,y:0},n=_e(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth},r={x:e.scrollWidth-n.width,y:e.scrollHeight-n.height};return{isTop:e.scrollTop<=t.y,isLeft:e.scrollLeft<=t.x,isBottom:e.scrollTop>=r.y,isRight:e.scrollLeft>=r.x,maxScroll:r,minScroll:t}}!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(Se||(Se={}));const Te={x:.2,y:.2};function De(e,t,n,r,o){let{top:a,left:i,right:c,bottom:s}=n;void 0===r&&(r=10),void 0===o&&(o=Te);const{isTop:l,isBottom:d,isLeft:u,isRight:m}=Oe(e),p={x:0,y:0},f={x:0,y:0},g=t.height*o.y,v=t.width*o.x;return!l&&a<=t.top+g?(p.y=Se.Backward,f.y=r*Math.abs((t.top+g-a)/g)):!d&&s>=t.bottom-g&&(p.y=Se.Forward,f.y=r*Math.abs((t.bottom-g-s)/g)),!m&&c>=t.right-v?(p.x=Se.Forward,f.x=r*Math.abs((t.right-v-c)/v)):!u&&i<=t.left+v&&(p.x=Se.Backward,f.x=r*Math.abs((t.left+v-i)/v)),{direction:p,speed:f}}function Ie(e){if(e===document.scrollingElement){const{innerWidth:e,innerHeight:t}=window;return{top:0,left:0,right:e,bottom:t,width:e,height:t}}const{top:t,left:n,right:r,bottom:o}=e.getBoundingClientRect();return{top:t,left:n,right:r,bottom:o,width:e.clientWidth,height:e.clientHeight}}function Re(e){return e.reduce((e,t)=>W(e,Ae(t)),le)}function je(e,t){if(void 0===t&&(t=be),!e)return;const{top:n,left:r,bottom:o,right:a}=t(e);we(e)&&(o<=0||a<=0||n>=window.innerHeight||r>=window.innerWidth)&&e.scrollIntoView({block:"center",inline:"center"})}const Me=[["x",["left","right"],function(e){return e.reduce((e,t)=>e+Ce(t),0)}],["y",["top","bottom"],function(e){return e.reduce((e,t)=>e+ke(t),0)}]];class He{constructor(e,t){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;const n=Ee(t),r=Re(n);this.rect={...e},this.width=e.width,this.height=e.height;for(const[o,a,i]of Me)for(const e of a)Object.defineProperty(this,e,{get:()=>{const t=i(n),a=r[o]-t;return this.rect[e]+a},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class ze{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach(e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)})},this.target=e}add(e,t,n){var r;null==(r=this.target)||r.addEventListener(e,t,n),this.listeners.push([e,t,n])}}function Le(e,t){const n=Math.abs(e.x),r=Math.abs(e.y);return"number"==typeof t?Math.sqrt(n**2+r**2)>t:"x"in t&&"y"in t?n>t.x&&r>t.y:"x"in t?n>t.x:"y"in t&&r>t.y}var Pe,$e;function Fe(e){e.preventDefault()}function Ue(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(Pe||(Pe={})),function(e){e.Space="Space",e.Down="ArrowDown",e.Right="ArrowRight",e.Left="ArrowLeft",e.Up="ArrowUp",e.Esc="Escape",e.Enter="Enter",e.Tab="Tab"}($e||($e={}));const Be={start:[$e.Space,$e.Enter],cancel:[$e.Esc],end:[$e.Space,$e.Enter,$e.Tab]},Ve=(e,t)=>{let{currentCoordinates:n}=t;switch(e.code){case $e.Right:return{...n,x:n.x+25};case $e.Left:return{...n,x:n.x-25};case $e.Down:return{...n,y:n.y+25};case $e.Up:return{...n,y:n.y-25}}};class Ge{constructor(e){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=e;const{event:{target:t}}=e;this.props=e,this.listeners=new ze(z(t)),this.windowListeners=new ze(R(t)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(Pe.Resize,this.handleCancel),this.windowListeners.add(Pe.VisibilityChange,this.handleCancel),setTimeout(()=>this.listeners.add(Pe.Keydown,this.handleKeyDown))}handleStart(){const{activeNode:e,onStart:t}=this.props,n=e.node.current;n&&je(n),t(le)}handleKeyDown(e){if(J(e)){const{active:t,context:n,options:r}=this.props,{keyboardCodes:o=Be,coordinateGetter:a=Ve,scrollBehavior:i="smooth"}=r,{code:c}=e;if(o.end.includes(c))return void this.handleEnd(e);if(o.cancel.includes(c))return void this.handleCancel(e);const{collisionRect:s}=n.current,l=s?{x:s.left,y:s.top}:le;this.referenceCoordinates||(this.referenceCoordinates=l);const d=a(e,{active:t,context:n.current,currentCoordinates:l});if(d){const t=Y(d,l),r={x:0,y:0},{scrollableAncestors:o}=n.current;for(const n of o){const o=e.code,{isTop:a,isRight:c,isLeft:s,isBottom:l,maxScroll:u,minScroll:m}=Oe(n),p=Ie(n),f={x:Math.min(o===$e.Right?p.right-p.width/2:p.right,Math.max(o===$e.Right?p.left:p.left+p.width/2,d.x)),y:Math.min(o===$e.Down?p.bottom-p.height/2:p.bottom,Math.max(o===$e.Down?p.top:p.top+p.height/2,d.y))},g=o===$e.Right&&!c||o===$e.Left&&!s,v=o===$e.Down&&!l||o===$e.Up&&!a;if(g&&f.x!==d.x){const e=n.scrollLeft+t.x,a=o===$e.Right&&e<=u.x||o===$e.Left&&e>=m.x;if(a&&!t.y)return void n.scrollTo({left:e,behavior:i});r.x=a?n.scrollLeft-e:o===$e.Right?n.scrollLeft-u.x:n.scrollLeft-m.x,r.x&&n.scrollBy({left:-r.x,behavior:i});break}if(v&&f.y!==d.y){const e=n.scrollTop+t.y,a=o===$e.Down&&e<=u.y||o===$e.Up&&e>=m.y;if(a&&!t.x)return void n.scrollTo({top:e,behavior:i});r.y=a?n.scrollTop-e:o===$e.Down?n.scrollTop-u.y:n.scrollTop-m.y,r.y&&n.scrollBy({top:-r.y,behavior:i});break}}this.handleMove(e,W(Y(d,this.referenceCoordinates),r))}}}handleMove(e,t){const{onMove:n}=this.props;e.preventDefault(),n(t)}handleEnd(e){const{onEnd:t}=this.props;e.preventDefault(),this.detach(),t()}handleCancel(e){const{onCancel:t}=this.props;e.preventDefault(),this.detach(),t()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}function Xe(e){return Boolean(e&&"distance"in e)}function We(e){return Boolean(e&&"delay"in e)}Ge.activators=[{eventName:"onKeyDown",handler:(e,t,n)=>{let{keyboardCodes:r=Be,onActivation:o}=t,{active:a}=n;const{code:i}=e.nativeEvent;if(r.start.includes(i)){const t=a.activatorNode.current;return(!t||e.target===t)&&(e.preventDefault(),null==o||o({event:e.nativeEvent}),!0)}return!1}}];class Ye{constructor(e,t,n){var r;void 0===n&&(n=function(e){const{EventTarget:t}=R(e);return e instanceof t?e:z(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;const{event:o}=e,{target:a}=o;this.props=e,this.events=t,this.document=z(a),this.documentListeners=new ze(this.document),this.listeners=new ze(n),this.windowListeners=new ze(R(a)),this.initialCoordinates=null!=(r=Z(o))?r:le,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){const{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),e.cancel&&this.listeners.add(e.cancel.name,this.handleCancel),this.windowListeners.add(Pe.Resize,this.handleCancel),this.windowListeners.add(Pe.DragStart,Fe),this.windowListeners.add(Pe.VisibilityChange,this.handleCancel),this.windowListeners.add(Pe.ContextMenu,Fe),this.documentListeners.add(Pe.Keydown,this.handleKeydown),t){if(null!=n&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(We(t))return this.timeoutId=setTimeout(this.handleStart,t.delay),void this.handlePending(t);if(Xe(t))return void this.handlePending(t)}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handlePending(e,t){const{active:n,onPending:r}=this.props;r(n,e,this.initialCoordinates,t)}handleStart(){const{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(Pe.Click,Ue,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(Pe.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;const{activated:n,initialCoordinates:r,props:o}=this,{onMove:a,options:{activationConstraint:i}}=o;if(!r)return;const c=null!=(t=Z(e))?t:le,s=Y(r,c);if(!n&&i){if(Xe(i)){if(null!=i.tolerance&&Le(s,i.tolerance))return this.handleCancel();if(Le(s,i.distance))return this.handleStart()}return We(i)&&Le(s,i.tolerance)?this.handleCancel():void this.handlePending(i,s)}e.cancelable&&e.preventDefault(),a(c)}handleEnd(){const{onAbort:e,onEnd:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleCancel(){const{onAbort:e,onCancel:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleKeydown(e){e.code===$e.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}const Je={cancel:{name:"pointercancel"},move:{name:"pointermove"},end:{name:"pointerup"}};class Ze extends Ye{constructor(e){const{event:t}=e,n=z(t.target);super(e,Je,n)}}Ze.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return!(!n.isPrimary||0!==n.button)&&(null==r||r({event:n}),!0)}}];const Ke={move:{name:"mousemove"},end:{name:"mouseup"}};var qe;!function(e){e[e.RightClick=2]="RightClick"}(qe||(qe={}));(class extends Ye{constructor(e){super(e,Ke,z(e.event.target))}}).activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return n.button!==qe.RightClick&&(null==r||r({event:n}),!0)}}];const Qe={cancel:{name:"touchcancel"},move:{name:"touchmove"},end:{name:"touchend"}};var et,tt;function nt(e){let{acceleration:t,activator:n=et.Pointer,canScroll:o,draggingRect:a,enabled:i,interval:c=5,order:s=tt.TreeOrder,pointerCoordinates:l,scrollableAncestors:d,scrollableAncestorRects:u,delta:m,threshold:p}=e;const f=function(e){let{delta:t,disabled:n}=e;const r=B(t);return F(e=>{if(n||!r||!e)return rt;const o={x:Math.sign(t.x-r.x),y:Math.sign(t.y-r.y)};return{x:{[Se.Backward]:e.x[Se.Backward]||-1===o.x,[Se.Forward]:e.x[Se.Forward]||1===o.x},y:{[Se.Backward]:e.y[Se.Backward]||-1===o.y,[Se.Forward]:e.y[Se.Forward]||1===o.y}}},[n,t,r])}({delta:m,disabled:!i}),[g,v]=function(){const e=(0,r.useRef)(null);return[(0,r.useCallback)((t,n)=>{e.current=setInterval(t,n)},[]),(0,r.useCallback)(()=>{null!==e.current&&(clearInterval(e.current),e.current=null)},[])]}(),h=(0,r.useRef)({x:0,y:0}),y=(0,r.useRef)({x:0,y:0}),b=(0,r.useMemo)(()=>{switch(n){case et.Pointer:return l?{top:l.y,bottom:l.y,left:l.x,right:l.x}:null;case et.DraggableRect:return a}},[n,a,l]),x=(0,r.useRef)(null),E=(0,r.useCallback)(()=>{const e=x.current;if(!e)return;const t=h.current.x*y.current.x,n=h.current.y*y.current.y;e.scrollBy(t,n)},[]),w=(0,r.useMemo)(()=>s===tt.TreeOrder?[...d].reverse():d,[s,d]);(0,r.useEffect)(()=>{if(i&&d.length&&b){for(const e of w){if(!1===(null==o?void 0:o(e)))continue;const n=d.indexOf(e),r=u[n];if(!r)continue;const{direction:a,speed:i}=De(e,r,b,t,p);for(const e of["x","y"])f[e][a[e]]||(i[e]=0,a[e]=0);if(i.x>0||i.y>0)return v(),x.current=e,g(E,c),h.current=i,void(y.current=a)}h.current={x:0,y:0},y.current={x:0,y:0},v()}else v()},[t,E,o,v,i,c,JSON.stringify(b),JSON.stringify(f),g,d,w,u,JSON.stringify(p)])}(class extends Ye{constructor(e){super(e,Qe)}static setup(){return window.addEventListener(Qe.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(Qe.move.name,e)};function e(){}}}).activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;const{touches:o}=n;return!(o.length>1)&&(null==r||r({event:n}),!0)}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(et||(et={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(tt||(tt={}));const rt={x:{[Se.Backward]:!1,[Se.Forward]:!1},y:{[Se.Backward]:!1,[Se.Forward]:!1}};var ot,at;!function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(ot||(ot={})),function(e){e.Optimized="optimized"}(at||(at={}));const it=new Map;function ct(e,t){return F(n=>e?n||("function"==typeof t?t(e):e):null,[t,e])}function st(e){let{callback:t,disabled:n}=e;const o=P(t),a=(0,r.useMemo)(()=>{if(n||"undefined"==typeof window||void 0===window.ResizeObserver)return;const{ResizeObserver:e}=window;return new e(o)},[n]);return(0,r.useEffect)(()=>()=>null==a?void 0:a.disconnect(),[a]),a}function lt(e){return new He(be(e),e)}function dt(e,t,n){void 0===t&&(t=lt);const[o,a]=(0,r.useState)(null);function i(){a(r=>{if(!e)return null;var o;if(!1===e.isConnected)return null!=(o=null!=r?r:n)?o:null;const a=t(e);return JSON.stringify(r)===JSON.stringify(a)?r:a})}const c=function(e){let{callback:t,disabled:n}=e;const o=P(t),a=(0,r.useMemo)(()=>{if(n||"undefined"==typeof window||void 0===window.MutationObserver)return;const{MutationObserver:e}=window;return new e(o)},[o,n]);return(0,r.useEffect)(()=>()=>null==a?void 0:a.disconnect(),[a]),a}({callback(t){if(e)for(const n of t){const{type:t,target:r}=n;if("childList"===t&&r instanceof HTMLElement&&r.contains(e)){i();break}}}}),s=st({callback:i});return L(()=>{i(),e?(null==s||s.observe(e),null==c||c.observe(document.body,{childList:!0,subtree:!0})):(null==s||s.disconnect(),null==c||c.disconnect())},[e]),o}const ut=[];function mt(e,t){void 0===t&&(t=[]);const n=(0,r.useRef)(null);return(0,r.useEffect)(()=>{n.current=null},t),(0,r.useEffect)(()=>{const t=e!==le;t&&!n.current&&(n.current=e),!t&&n.current&&(n.current=null)},[e]),n.current?Y(e,n.current):le}function pt(e){return(0,r.useMemo)(()=>e?function(e){const t=e.innerWidth,n=e.innerHeight;return{top:0,left:0,right:t,bottom:n,width:t,height:n}}(e):null,[e])}const ft=[];function gt(e){if(!e)return null;if(e.children.length>1)return e;const t=e.children[0];return M(t)?t:e}const vt=[{sensor:Ze,options:{}},{sensor:Ge,options:{}}],ht={current:{}},yt={draggable:{measure:xe},droppable:{measure:xe,strategy:ot.WhileDragging,frequency:at.Optimized},dragOverlay:{measure:be}};class bt extends Map{get(e){var t;return null!=e&&null!=(t=super.get(e))?t:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter(e=>{let{disabled:t}=e;return!t})}getNodeFor(e){var t,n;return null!=(t=null==(n=this.get(e))?void 0:n.node.current)?t:void 0}}const xt={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new bt,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:se},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:yt,measureDroppableContainers:se,windowRect:null,measuringScheduled:!1},Et={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:se,draggableNodes:new Map,over:null,measureDroppableContainers:se},wt=(0,r.createContext)(Et),Nt=(0,r.createContext)(xt);function Ct(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new bt}}}function kt(e,t){switch(t.type){case ce.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case ce.DragMove:return null==e.draggable.active?e:{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}};case ce.DragEnd:case ce.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case ce.RegisterDroppable:{const{element:n}=t,{id:r}=n,o=new bt(e.droppable.containers);return o.set(r,n),{...e,droppable:{...e.droppable,containers:o}}}case ce.SetDroppableDisabled:{const{id:n,key:r,disabled:o}=t,a=e.droppable.containers.get(n);if(!a||r!==a.key)return e;const i=new bt(e.droppable.containers);return i.set(n,{...a,disabled:o}),{...e,droppable:{...e.droppable,containers:i}}}case ce.UnregisterDroppable:{const{id:n,key:r}=t,o=e.droppable.containers.get(n);if(!o||r!==o.key)return e;const a=new bt(e.droppable.containers);return a.delete(n),{...e,droppable:{...e.droppable,containers:a}}}default:return e}}function At(e){let{disabled:t}=e;const{active:n,activatorEvent:o,draggableNodes:a}=(0,r.useContext)(wt),i=B(o),c=B(null==n?void 0:n.id);return(0,r.useEffect)(()=>{if(!t&&!o&&i&&null!=c){if(!J(i))return;if(document.activeElement===i.target)return;const e=a.get(c);if(!e)return;const{activatorNode:t,node:n}=e;if(!t.current&&!n.current)return;requestAnimationFrame(()=>{for(const e of[t.current,n.current]){if(!e)continue;const t=Q(e);if(t){t.focus();break}}})}},[o,t,a,c,i]),null}function St(e,t){let{transform:n,...r}=t;return null!=e&&e.length?e.reduce((e,t)=>t({transform:e,...r}),n):n}const _t=(0,r.createContext)({...le,scaleX:1,scaleY:1});var Ot;!function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(Ot||(Ot={}));const Tt=(0,r.memo)(function(e){var t,n,o,a;let{id:i,accessibility:c,autoScroll:s=!0,children:l,sensors:d=vt,collisionDetection:u=pe,measuring:m,modifiers:p,...f}=e;const g=(0,r.useReducer)(kt,void 0,Ct),[v,h]=g,[y,b]=function(){const[e]=(0,r.useState)(()=>new Set),t=(0,r.useCallback)(t=>(e.add(t),()=>e.delete(t)),[e]);return[(0,r.useCallback)(t=>{let{type:n,event:r}=t;e.forEach(e=>{var t;return null==(t=e[n])?void 0:t.call(e,r)})},[e]),t]}(),[x,E]=(0,r.useState)(Ot.Uninitialized),w=x===Ot.Initialized,{draggable:{active:N,nodes:C,translate:k},droppable:{containers:A}}=v,S=null!=N?C.get(N):null,_=(0,r.useRef)({initial:null,translated:null}),D=(0,r.useMemo)(()=>{var e;return null!=N?{id:N,data:null!=(e=null==S?void 0:S.data)?e:ht,rect:_}:null},[N,S]),I=(0,r.useRef)(null),[j,H]=(0,r.useState)(null),[z,P]=(0,r.useState)(null),B=$(f,Object.values(f)),V=G("DndDescribedBy",i),X=(0,r.useMemo)(()=>A.getEnabled(),[A]),Y=(J=m,(0,r.useMemo)(()=>({draggable:{...yt.draggable,...null==J?void 0:J.draggable},droppable:{...yt.droppable,...null==J?void 0:J.droppable},dragOverlay:{...yt.dragOverlay,...null==J?void 0:J.dragOverlay}}),[null==J?void 0:J.draggable,null==J?void 0:J.droppable,null==J?void 0:J.dragOverlay]));var J;const{droppableRects:K,measureDroppableContainers:q,measuringScheduled:Q}=function(e,t){let{dragging:n,dependencies:o,config:a}=t;const[i,c]=(0,r.useState)(null),{frequency:s,measure:l,strategy:d}=a,u=(0,r.useRef)(e),m=function(){switch(d){case ot.Always:return!1;case ot.BeforeDragging:return n;default:return!n}}(),p=$(m),f=(0,r.useCallback)(function(e){void 0===e&&(e=[]),p.current||c(t=>null===t?e:t.concat(e.filter(e=>!t.includes(e))))},[p]),g=(0,r.useRef)(null),v=F(t=>{if(m&&!n)return it;if(!t||t===it||u.current!==e||null!=i){const t=new Map;for(let n of e){if(!n)continue;if(i&&i.length>0&&!i.includes(n.id)&&n.rect.current){t.set(n.id,n.rect.current);continue}const e=n.node.current,r=e?new He(l(e),e):null;n.rect.current=r,r&&t.set(n.id,r)}return t}return t},[e,i,n,m,l]);return(0,r.useEffect)(()=>{u.current=e},[e]),(0,r.useEffect)(()=>{m||f()},[n,m]),(0,r.useEffect)(()=>{i&&i.length>0&&c(null)},[JSON.stringify(i)]),(0,r.useEffect)(()=>{m||"number"!=typeof s||null!==g.current||(g.current=setTimeout(()=>{f(),g.current=null},s))},[s,m,f,...o]),{droppableRects:v,measureDroppableContainers:f,measuringScheduled:null!=i}}(X,{dragging:w,dependencies:[k.x,k.y],config:Y.droppable}),ee=function(e,t){const n=null!=t?e.get(t):void 0,r=n?n.node.current:null;return F(e=>{var n;return null==t?null:null!=(n=null!=r?r:e)?n:null},[r,t])}(C,N),te=(0,r.useMemo)(()=>z?Z(z):null,[z]),ne=function(){const e=!1===(null==j?void 0:j.autoScrollEnabled),t="object"==typeof s?!1===s.enabled:!1===s,n=w&&!e&&!t;if("object"==typeof s)return{...s,enabled:n};return{enabled:n}}(),oe=function(e,t){return ct(e,t)}(ee,Y.draggable.measure);!function(e){let{activeNode:t,measure:n,initialRect:o,config:a=!0}=e;const i=(0,r.useRef)(!1),{x:c,y:s}="boolean"==typeof a?{x:a,y:a}:a;L(()=>{if(!c&&!s||!t)return void(i.current=!1);if(i.current||!o)return;const e=null==t?void 0:t.node.current;if(!e||!1===e.isConnected)return;const r=fe(n(e),o);if(c||(r.x=0),s||(r.y=0),i.current=!0,Math.abs(r.x)>0||Math.abs(r.y)>0){const t=we(e);t&&t.scrollBy({top:r.y,left:r.x})}},[t,c,s,o,n])}({activeNode:null!=N?C.get(N):null,config:ne.layoutShiftCompensation,initialRect:oe,measure:Y.draggable.measure});const ae=dt(ee,Y.draggable.measure,oe),se=dt(ee?ee.parentElement:null),de=(0,r.useRef)({activatorEvent:null,active:null,activeNode:ee,collisionRect:null,collisions:null,droppableRects:K,draggableNodes:C,draggingNode:null,draggingNodeRect:null,droppableContainers:A,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),ue=A.getNodeFor(null==(t=de.current.over)?void 0:t.id),me=function(e){let{measure:t}=e;const[n,o]=(0,r.useState)(null),a=st({callback:(0,r.useCallback)(e=>{for(const{target:n}of e)if(M(n)){o(e=>{const r=t(n);return e?{...e,width:r.width,height:r.height}:r});break}},[t])}),i=(0,r.useCallback)(e=>{const n=gt(e);null==a||a.disconnect(),n&&(null==a||a.observe(n)),o(n?t(n):null)},[t,a]),[c,s]=U(i);return(0,r.useMemo)(()=>({nodeRef:c,rect:n,setRef:s}),[n,c,s])}({measure:Y.dragOverlay.measure}),ge=null!=(n=me.nodeRef.current)?n:ee,he=w?null!=(o=me.rect)?o:ae:null,ye=Boolean(me.nodeRef.current&&me.rect),xe=fe(Ce=ye?null:ae,ct(Ce));var Ce;const ke=pt(ge?R(ge):null),Se=function(e){const t=(0,r.useRef)(e),n=F(n=>e?n&&n!==ut&&e&&t.current&&e.parentNode===t.current.parentNode?n:Ee(e):ut,[e]);return(0,r.useEffect)(()=>{t.current=e},[e]),n}(w?null!=ue?ue:ee:null),Oe=function(e,t){void 0===t&&(t=be);const[n]=e,o=pt(n?R(n):null),[a,i]=(0,r.useState)(ft);function c(){i(()=>e.length?e.map(e=>_e(e)?o:new He(t(e),e)):ft)}const s=st({callback:c});return L(()=>{null==s||s.disconnect(),c(),e.forEach(e=>null==s?void 0:s.observe(e))},[e]),a}(Se),Te=St(p,{transform:{x:k.x-xe.x,y:k.y-xe.y,scaleX:1,scaleY:1},activatorEvent:z,active:D,activeNodeRect:ae,containerNodeRect:se,draggingNodeRect:he,over:de.current.over,overlayNodeRect:me.rect,scrollableAncestors:Se,scrollableAncestorRects:Oe,windowRect:ke}),De=te?W(te,k):null,Ie=function(e){const[t,n]=(0,r.useState)(null),o=(0,r.useRef)(e),a=(0,r.useCallback)(e=>{const t=Ne(e.target);t&&n(e=>e?(e.set(t,Ae(t)),new Map(e)):null)},[]);return(0,r.useEffect)(()=>{const t=o.current;if(e!==t){r(t);const i=e.map(e=>{const t=Ne(e);return t?(t.addEventListener("scroll",a,{passive:!0}),[t,Ae(t)]):null}).filter(e=>null!=e);n(i.length?new Map(i):null),o.current=e}return()=>{r(e),r(t)};function r(e){e.forEach(e=>{const t=Ne(e);null==t||t.removeEventListener("scroll",a)})}},[a,e]),(0,r.useMemo)(()=>e.length?t?Array.from(t.values()).reduce((e,t)=>W(e,t),le):Re(e):le,[e,t])}(Se),je=mt(Ie),Me=mt(Ie,[ae]),ze=W(Te,je),Le=he?ve(he,Te):null,Pe=D&&Le?u({active:D,collisionRect:Le,droppableRects:K,droppableContainers:X,pointerCoordinates:De}):null,$e=function(e,t){if(!e||0===e.length)return null;const[n]=e;return t?n[t]:n}(Pe,"id"),[Fe,Ue]=(0,r.useState)(null),Be=function(e,t,n){return{...e,scaleX:t&&n?t.width/n.width:1,scaleY:t&&n?t.height/n.height:1}}(ye?Te:W(Te,Me),null!=(a=null==Fe?void 0:Fe.rect)?a:null,ae),Ve=(0,r.useRef)(null),Ge=(0,r.useCallback)((e,t)=>{let{sensor:n,options:r}=t;if(null==I.current)return;const o=C.get(I.current);if(!o)return;const a=e.nativeEvent,i=new n({active:I.current,activeNode:o,event:a,options:r,context:de,onAbort(e){if(!C.get(e))return;const{onDragAbort:t}=B.current,n={id:e};null==t||t(n),y({type:"onDragAbort",event:n})},onPending(e,t,n,r){if(!C.get(e))return;const{onDragPending:o}=B.current,a={id:e,constraint:t,initialCoordinates:n,offset:r};null==o||o(a),y({type:"onDragPending",event:a})},onStart(e){const t=I.current;if(null==t)return;const n=C.get(t);if(!n)return;const{onDragStart:r}=B.current,o={activatorEvent:a,active:{id:t,data:n.data,rect:_}};(0,O.unstable_batchedUpdates)(()=>{null==r||r(o),E(Ot.Initializing),h({type:ce.DragStart,initialCoordinates:e,active:t}),y({type:"onDragStart",event:o}),H(Ve.current),P(a)})},onMove(e){h({type:ce.DragMove,coordinates:e})},onEnd:c(ce.DragEnd),onCancel:c(ce.DragCancel)});function c(e){return async function(){const{active:t,collisions:n,over:r,scrollAdjustedTranslate:o}=de.current;let i=null;if(t&&o){const{cancelDrop:c}=B.current;if(i={activatorEvent:a,active:t,collisions:n,delta:o,over:r},e===ce.DragEnd&&"function"==typeof c){await Promise.resolve(c(i))&&(e=ce.DragCancel)}}I.current=null,(0,O.unstable_batchedUpdates)(()=>{h({type:e}),E(Ot.Uninitialized),Ue(null),H(null),P(null),Ve.current=null;const t=e===ce.DragEnd?"onDragEnd":"onDragCancel";if(i){const e=B.current[t];null==e||e(i),y({type:t,event:i})}})}}Ve.current=i},[C]),Xe=(0,r.useCallback)((e,t)=>(n,r)=>{const o=n.nativeEvent,a=C.get(r);if(null!==I.current||!a||o.dndKit||o.defaultPrevented)return;const i={active:a};!0===e(n,t.options,i)&&(o.dndKit={capturedBy:t.sensor},I.current=r,Ge(n,t))},[C,Ge]),We=function(e,t){return(0,r.useMemo)(()=>e.reduce((e,n)=>{const{sensor:r}=n;return[...e,...r.activators.map(e=>({eventName:e.eventName,handler:t(e.handler,n)}))]},[]),[e,t])}(d,Xe);!function(e){(0,r.useEffect)(()=>{if(!T)return;const t=e.map(e=>{let{sensor:t}=e;return null==t.setup?void 0:t.setup()});return()=>{for(const e of t)null==e||e()}},e.map(e=>{let{sensor:t}=e;return t}))}(d),L(()=>{ae&&x===Ot.Initializing&&E(Ot.Initialized)},[ae,x]),(0,r.useEffect)(()=>{const{onDragMove:e}=B.current,{active:t,activatorEvent:n,collisions:r,over:o}=de.current;if(!t||!n)return;const a={active:t,activatorEvent:n,collisions:r,delta:{x:ze.x,y:ze.y},over:o};(0,O.unstable_batchedUpdates)(()=>{null==e||e(a),y({type:"onDragMove",event:a})})},[ze.x,ze.y]),(0,r.useEffect)(()=>{const{active:e,activatorEvent:t,collisions:n,droppableContainers:r,scrollAdjustedTranslate:o}=de.current;if(!e||null==I.current||!t||!o)return;const{onDragOver:a}=B.current,i=r.get($e),c=i&&i.rect.current?{id:i.id,rect:i.rect.current,data:i.data,disabled:i.disabled}:null,s={active:e,activatorEvent:t,collisions:n,delta:{x:o.x,y:o.y},over:c};(0,O.unstable_batchedUpdates)(()=>{Ue(c),null==a||a(s),y({type:"onDragOver",event:s})})},[$e]),L(()=>{de.current={activatorEvent:z,active:D,activeNode:ee,collisionRect:Le,collisions:Pe,droppableRects:K,draggableNodes:C,draggingNode:ge,draggingNodeRect:he,droppableContainers:A,over:Fe,scrollableAncestors:Se,scrollAdjustedTranslate:ze},_.current={initial:he,translated:Le}},[D,ee,Pe,Le,C,ge,he,K,A,Fe,Se,ze]),nt({...ne,delta:k,draggingRect:Le,pointerCoordinates:De,scrollableAncestors:Se,scrollableAncestorRects:Oe});const Ye=(0,r.useMemo)(()=>({active:D,activeNode:ee,activeNodeRect:ae,activatorEvent:z,collisions:Pe,containerNodeRect:se,dragOverlay:me,draggableNodes:C,droppableContainers:A,droppableRects:K,over:Fe,measureDroppableContainers:q,scrollableAncestors:Se,scrollableAncestorRects:Oe,measuringConfiguration:Y,measuringScheduled:Q,windowRect:ke}),[D,ee,ae,z,Pe,se,me,C,A,K,Fe,q,Se,Oe,Y,Q,ke]),Je=(0,r.useMemo)(()=>({activatorEvent:z,activators:We,active:D,activeNodeRect:ae,ariaDescribedById:{draggable:V},dispatch:h,draggableNodes:C,over:Fe,measureDroppableContainers:q}),[z,We,D,ae,h,V,C,Fe,q]);return r.createElement(re.Provider,{value:b},r.createElement(wt.Provider,{value:Je},r.createElement(Nt.Provider,{value:Ye},r.createElement(_t.Provider,{value:Be},l)),r.createElement(At,{disabled:!1===(null==c?void 0:c.restoreFocus)})),r.createElement(ie,{...c,hiddenTextDescribedById:V}))}),Dt=(0,r.createContext)(null),It="button";function Rt(e){let{id:t,data:n,disabled:o=!1,attributes:a}=e;const i=G("Draggable"),{activators:c,activatorEvent:s,active:l,activeNodeRect:d,ariaDescribedById:u,draggableNodes:m,over:p}=(0,r.useContext)(wt),{role:f=It,roleDescription:g="draggable",tabIndex:v=0}=null!=a?a:{},h=(null==l?void 0:l.id)===t,y=(0,r.useContext)(h?_t:Dt),[b,x]=U(),[E,w]=U(),N=function(e,t){return(0,r.useMemo)(()=>e.reduce((e,n)=>{let{eventName:r,handler:o}=n;return e[r]=e=>{o(e,t)},e},{}),[e,t])}(c,t),C=$(n);L(()=>(m.set(t,{id:t,key:i,node:b,activatorNode:E,data:C}),()=>{const e=m.get(t);e&&e.key===i&&m.delete(t)}),[m,t]);return{active:l,activatorEvent:s,activeNodeRect:d,attributes:(0,r.useMemo)(()=>({role:f,tabIndex:v,"aria-disabled":o,"aria-pressed":!(!h||f!==It)||void 0,"aria-roledescription":g,"aria-describedby":u.draggable}),[o,f,v,h,g,u.draggable]),isDragging:h,listeners:o?void 0:N,node:b,over:p,setNodeRef:x,setActivatorNodeRef:w,transform:y}}const jt={timeout:25};function Mt(e){let{animation:t,children:n}=e;const[o,a]=(0,r.useState)(null),[i,c]=(0,r.useState)(null),s=B(n);return n||o||!s||a(s),L(()=>{if(!i)return;const e=null==o?void 0:o.key,n=null==o?void 0:o.props.id;null!=e&&null!=n?Promise.resolve(t(n,i)).then(()=>{a(null)}):a(null)},[t,o,i]),r.createElement(r.Fragment,null,n,o?(0,r.cloneElement)(o,{ref:c}):null)}const Ht={x:0,y:0,scaleX:1,scaleY:1};function zt(e){let{children:t}=e;return r.createElement(wt.Provider,{value:Et},r.createElement(_t.Provider,{value:Ht},t))}const Lt={position:"fixed",touchAction:"none"},Pt=e=>J(e)?"transform 250ms ease":void 0,$t=(0,r.forwardRef)((e,t)=>{let{as:n,activatorEvent:o,adjustScale:a,children:i,className:c,rect:s,style:l,transform:d,transition:u=Pt}=e;if(!s)return null;const m=a?d:{...d,scaleX:1,scaleY:1},p={...Lt,width:s.width,height:s.height,top:s.top,left:s.left,transform:K.Transform.toString(m),transformOrigin:a&&o?de(o,s):void 0,transition:"function"==typeof u?u(o):u,...l};return r.createElement(n,{className:c,style:p,ref:t},i)}),Ft=e=>t=>{let{active:n,dragOverlay:r}=t;const o={},{styles:a,className:i}=e;if(null!=a&&a.active)for(const[e,c]of Object.entries(a.active))void 0!==c&&(o[e]=n.node.style.getPropertyValue(e),n.node.style.setProperty(e,c));if(null!=a&&a.dragOverlay)for(const[e,c]of Object.entries(a.dragOverlay))void 0!==c&&r.node.style.setProperty(e,c);return null!=i&&i.active&&n.node.classList.add(i.active),null!=i&&i.dragOverlay&&r.node.classList.add(i.dragOverlay),function(){for(const[e,t]of Object.entries(o))n.node.style.setProperty(e,t);null!=i&&i.active&&n.node.classList.remove(i.active)}},Ut={duration:250,easing:"ease",keyframes:e=>{let{transform:{initial:t,final:n}}=e;return[{transform:K.Transform.toString(t)},{transform:K.Transform.toString(n)}]},sideEffects:Ft({styles:{active:{opacity:"0"}}})};function Bt(e){let{config:t,draggableNodes:n,droppableContainers:r,measuringConfiguration:o}=e;return P((e,a)=>{if(null===t)return;const i=n.get(e);if(!i)return;const c=i.node.current;if(!c)return;const s=gt(a);if(!s)return;const{transform:l}=R(a).getComputedStyle(a),d=he(l);if(!d)return;const u="function"==typeof t?t:function(e){const{duration:t,easing:n,sideEffects:r,keyframes:o}={...Ut,...e};return e=>{let{active:a,dragOverlay:i,transform:c,...s}=e;if(!t)return;const l={x:i.rect.left-a.rect.left,y:i.rect.top-a.rect.top},d={scaleX:1!==c.scaleX?a.rect.width*c.scaleX/i.rect.width:1,scaleY:1!==c.scaleY?a.rect.height*c.scaleY/i.rect.height:1},u={x:c.x-l.x,y:c.y-l.y,...d},m=o({...s,active:a,dragOverlay:i,transform:{initial:c,final:u}}),[p]=m,f=m[m.length-1];if(JSON.stringify(p)===JSON.stringify(f))return;const g=null==r?void 0:r({active:a,dragOverlay:i,...s}),v=i.node.animate(m,{duration:t,easing:n,fill:"forwards"});return new Promise(e=>{v.onfinish=()=>{null==g||g(),e()}})}}(t);return je(c,o.draggable.measure),u({active:{id:e,data:i.data,node:c,rect:o.draggable.measure(c)},draggableNodes:n,dragOverlay:{node:a,rect:o.dragOverlay.measure(s)},droppableContainers:r,measuringConfiguration:o,transform:d})})}let Vt=0;function Gt(e){return(0,r.useMemo)(()=>{if(null!=e)return Vt++,Vt},[e])}const Xt=r.memo(e=>{let{adjustScale:t=!1,children:n,dropAnimation:o,style:a,transition:i,modifiers:c,wrapperElement:s="div",className:l,zIndex:d=999}=e;const{activatorEvent:u,active:m,activeNodeRect:p,containerNodeRect:f,draggableNodes:g,droppableContainers:v,dragOverlay:h,over:y,measuringConfiguration:b,scrollableAncestors:x,scrollableAncestorRects:E,windowRect:w}=(0,r.useContext)(Nt),N=(0,r.useContext)(_t),C=Gt(null==m?void 0:m.id),k=St(c,{activatorEvent:u,active:m,activeNodeRect:p,containerNodeRect:f,draggingNodeRect:h.rect,over:y,overlayNodeRect:h.rect,scrollableAncestors:x,scrollableAncestorRects:E,transform:N,windowRect:w}),A=ct(p),S=Bt({config:o,draggableNodes:g,droppableContainers:v,measuringConfiguration:b}),_=A?h.setRef:void 0;return r.createElement(zt,null,r.createElement(Mt,{animation:S},m&&C?r.createElement($t,{key:C,id:m.id,ref:_,as:s,activatorEvent:u,adjustScale:t,className:l,transition:i,rect:A,style:{zIndex:d,...a},transform:k},n):null))});var Wt=n(1133),Yt=n(5154),Jt=(n(2154),n(6942)),Zt=n.n(Jt),Kt=n(9853),qt=n(2279),Qt=n(4129),en=n(2546),tn=n(8414);var nn=n(4440),rn=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};function on({suffixCls:e,tagName:t,displayName:n}){return n=>r.forwardRef((o,a)=>r.createElement(n,Object.assign({ref:a,suffixCls:e,tagName:t},o)))}const an=r.forwardRef((e,t)=>{const{prefixCls:n,suffixCls:o,className:a,tagName:i}=e,c=rn(e,["prefixCls","suffixCls","className","tagName"]),{getPrefixCls:s}=r.useContext(qt.QO),l=s("layout",n),[d,u,m]=(0,nn.Ay)(l),p=o?`${l}-${o}`:l;return d(r.createElement(i,Object.assign({className:Zt()(n||p,a,u,m),ref:t},c)))}),cn=r.forwardRef((e,t)=>{const{direction:n}=r.useContext(qt.QO),[o,i]=r.useState([]),{prefixCls:c,className:s,rootClassName:l,children:d,hasSider:u,tagName:m,style:p}=e,f=rn(e,["prefixCls","className","rootClassName","children","hasSider","tagName","style"]),g=(0,Kt.A)(f,["suffixCls"]),{getPrefixCls:v,className:h,style:y}=(0,qt.TP)("layout"),b=v("layout",c),x=function(e,t,n){return"boolean"==typeof n?n:!!e.length||(0,en.A)(t).some(e=>e.type===tn.A)}(o,d,u),[E,w,N]=(0,nn.Ay)(b),C=Zt()(b,{[`${b}-has-sider`]:x,[`${b}-rtl`]:"rtl"===n},h,s,l,w,N),k=r.useMemo(()=>({siderHook:{addSider:e=>{i(t=>[].concat((0,a.A)(t),[e]))},removeSider:e=>{i(t=>t.filter(t=>t!==e))}}}),[]);return E(r.createElement(Qt.M.Provider,{value:k},r.createElement(m,Object.assign({ref:t,className:C,style:Object.assign(Object.assign({},y),p)},g),d)))}),sn=on({tagName:"div",displayName:"Layout"})(cn),ln=on({suffixCls:"header",tagName:"header",displayName:"Header"})(an),dn=on({suffixCls:"footer",tagName:"footer",displayName:"Footer"})(an),un=on({suffixCls:"content",tagName:"main",displayName:"Content"})(an);const mn=sn;mn.Header=ln,mn.Footer=dn,mn.Content=un,mn.Sider=tn.A,mn._InternalSiderContext=tn.P;var pn=mn,fn=n(2609),gn=n(6143),vn=n(1788);const hn=(0,vn.A)("Cable",[["path",{d:"M17 21v-2a1 1 0 0 1-1-1v-1a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v1a1 1 0 0 1-1 1",key:"10bnsj"}],["path",{d:"M19 15V6.5a1 1 0 0 0-7 0v11a1 1 0 0 1-7 0V9",key:"1eqmu1"}],["path",{d:"M21 21v-2h-4",key:"14zm7j"}],["path",{d:"M3 5h4V3",key:"z442eg"}],["path",{d:"M7 5a1 1 0 0 1 1 1v1a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a1 1 0 0 1 1-1V3",key:"ebdjd7"}]]),yn=(0,vn.A)("CodeXml",[["path",{d:"m18 16 4-4-4-4",key:"1inbqp"}],["path",{d:"m6 8-4 4 4 4",key:"15zrgr"}],["path",{d:"m14.5 4-5 16",key:"e7oirm"}]]);var bn=n(8309),xn=n(8852),En=n(4471),wn=n(7799);const Nn=(0,vn.A)("ListCheck",[["path",{d:"M11 18H3",key:"n3j2dh"}],["path",{d:"m15 18 2 2 4-4",key:"1szwhi"}],["path",{d:"M16 12H3",key:"1a2rj7"}],["path",{d:"M16 6H3",key:"1wxfjs"}]]);var Cn=n(6808),kn=n(2404),An=n.n(kn),Sn=n(1511);let _n=(e=21)=>crypto.getRandomValues(new Uint8Array(e)).reduce((e,t)=>e+=(t&=63)<36?t.toString(36):t<62?(t-26).toString(36).toUpperCase():t>62?"-":"_","");var On=n(4279);const Tn="teambuilder_layout",Dn=e=>{const t=(e=>{const t=JSON.stringify(e);let n=0;for(let r=0;r<t.length;r++)n=(n<<5)-n+t.charCodeAt(r),n&=n;return n.toString(36)})(e.config),n=e.label||e.component_type;return`${e.component_type}_${n}_${t}`},In=()=>{try{const e=localStorage.getItem(Tn);if(!e)return{};const t=JSON.parse(e);return 1!==t.version?(localStorage.removeItem(Tn),{}):t.data||{}}catch(e){return console.warn("Failed to parse layout storage:",e),localStorage.removeItem(Tn),{}}},Rn=e=>{try{const t={version:1,data:e};localStorage.setItem(Tn,JSON.stringify(t))}catch(t){console.warn("Failed to save layout storage:",t)}},jn=function(e,t,n,r){void 0===r&&(r=!0);const o=Dn(t);let a=In();a=(e=>{const t=Date.now(),n={};for(const[r,o]of Object.entries(e)){const e={};for(const[n,r]of Object.entries(o))t-r.timestamp<2592e6&&(e[n]=r);Object.keys(e).length>0&&(n[r]=e)}return n})(a),a[e]||(a[e]={}),a[e][o]={position:n,isUserPositioned:r,timestamp:Date.now()},Rn(a)},Mn=(e,t)=>{var n;const r=Dn(t);return(null===(n=In()[e])||void 0===n?void 0:n[r])||null},Hn=(e,t)=>{const n=Mn(e,t);return(null==n?void 0:n.isUserPositioned)||!1},zn={X_POSITION:100,MIN_Y_POSITION:200},Ln={START_X:600,START_Y:200,X_STAGGER:0,MIN_Y_STAGGER:50},Pn={WIDTH:272,MIN_HEIGHT:100,PADDING:20},$n={BASE:80,DESCRIPTION:60,MODEL_SECTION:100,TOOL_SECTION:80,TOOL_ITEM:40,AGENT_SECTION:80,AGENT_ITEM:40,TERMINATION_SECTION:80},Fn=e=>{var t;let n=$n.BASE;switch(e.description&&(n+=$n.DESCRIPTION),e.component_type){case"team":const o=e;null!==(t=o.config.participants)&&void 0!==t&&t.length&&(n+=$n.AGENT_SECTION,n+=o.config.participants.length*$n.AGENT_ITEM),o.config.termination_condition&&(n+=$n.TERMINATION_SECTION);break;case"agent":if((0,On.O6)(e)){n+=200;const t=e.config.workbench;if(t){const e=Array.isArray(t)?t:[t];e.length>0&&(n+=$n.TOOL_SECTION,e.forEach(e=>{if(e)if((0,On.wb)(e)){var t;const r=(null===(t=e.config.tools)||void 0===t?void 0:t.length)||0;r>0&&(n+=r*$n.TOOL_ITEM)}else(0,On.Mf)(e)&&(n+=$n.TOOL_ITEM)}))}}(0,On.Zj)(e)&&(n+=100),(0,On.fk)(e)&&(n+=-100);break;case"workbench":if((0,On.wb)(e)){var r;const t=(null===(r=e.config.tools)||void 0===r?void 0:r.length)||0;n+=$n.TOOL_SECTION,t>0&&(n+=t*$n.TOOL_ITEM)}else(0,On.Mf)(e)&&(n+=$n.TOOL_SECTION,n+=$n.TOOL_ITEM)}return Math.max(n,Pn.MIN_HEIGHT)},Un=(e,t)=>{const n=t.map(e=>Fn(e.data.component)+50).reduce((e,t)=>e+t+Ln.MIN_Y_STAGGER,0);return{x:Ln.START_X+e*Ln.X_STAGGER,y:Ln.START_Y+n}},Bn=e=>{if(0===e.length)return{x:zn.X_POSITION,y:zn.MIN_Y_POSITION};const t=e.reduce((e,t)=>e+t.position.y,0)/e.length,n=Math.max(zn.MIN_Y_POSITION,t);return{x:zn.X_POSITION,y:n}},Vn=(e,t,n)=>({id:_n(),position:e,type:t.component_type,data:{label:n||t.label||t.component_type,component:t,type:t.component_type,dimensions:{width:Pn.WIDTH,height:Fn(t)}}}),Gn=(e,t,n)=>{const r=Mn(e,t);return(null==r?void 0:r.position)||n},Xn=function(e,t,n,r){void 0===r&&(r=!0);const o=e.find(e=>"team"===e.data.type);if(!o)return{nodes:e,edges:t};const i=e.filter(e=>"team"!==e.data.type),c=i.map((e,t)=>{const o=Un(t,i.slice(0,t)),a=r&&n&&Hn(n,e.data.component)?e.position:n?Gn(n,e.data.component,o):o;return{...e,position:a,data:{...e.data,dimensions:{width:Pn.WIDTH,height:Fn(e.data.component)}}}}),s=Bn(c),l=r&&n&&Hn(n,o.data.component)?o.position:n?Gn(n,o.data.component,s):s;return{nodes:[{...o,position:l,data:{...o.data,dimensions:{width:Pn.WIDTH,height:Fn(o.data.component)}}}].concat((0,a.A)(c)),edges:t}},Wn=(e,t)=>{let n=e.replace(/[^a-zA-Z0-9_$]/g,"_").replace(/^([^a-zA-Z_$])/,"_$1");if(!t.includes(n))return n;let r=1;for(;t.includes(`${n}_${r}`);)r++;return`${n}_${r}`},Yn=e=>e?Array.isArray(e)?e:[e]:[],Jn=(0,Sn.v)((e,t)=>({nodes:[],edges:[],selectedNodeId:null,history:[],currentHistoryIndex:-1,originalComponent:null,teamId:null,setNodeUserPositioned:(e,n)=>{const r=t();if(r.teamId){const t=r.nodes.find(t=>t.id===e);t&&((e,t,n)=>{jn(e,t.data.component,n,!0)})(r.teamId,t,n)}},addNode:(t,n,r)=>{e(e=>{const o=JSON.parse(JSON.stringify(n));let i=(0,a.A)(e.nodes),c=(0,a.A)(e.edges);if(r){const t=e.nodes.find(e=>e.id===r);if(!t)return e;if((0,On.nL)(o)){if((0,On.VZ)(t.data.component)&&(0,On.HX)(t.data.component))return t.data.component.config.model_client=o,{nodes:i,edges:c,history:[].concat((0,a.A)(e.history.slice(0,e.currentHistoryIndex+1)),[{nodes:i,edges:c}]).slice(-50),currentHistoryIndex:e.currentHistoryIndex+1};if((0,On.fF)(t.data.component)&&((0,On.O6)(t.data.component)||(0,On.Zj)(t.data.component)))return t.data.component.config.model_client=o,{nodes:i,edges:c,history:[].concat((0,a.A)(e.history.slice(0,e.currentHistoryIndex+1)),[{nodes:i,edges:c}]).slice(-50),currentHistoryIndex:e.currentHistoryIndex+1}}else if((0,On.gG)(o)){if((0,On.fF)(t.data.component)&&(0,On.O6)(t.data.component)){t.data.component.config.workbench||(t.data.component.config.workbench=[]);let n=Yn(t.data.component.config.workbench),r=n.findIndex(e=>(0,On.wb)(e));if(-1===r){const e={provider:"autogen_core.tools.StaticWorkbench",component_type:"workbench",version:1,component_version:1,config:{tools:[]},label:"Static Workbench",description:"A static workbench for managing custom tools"};n=[].concat((0,a.A)(n),[e]),t.data.component.config.workbench=n,r=n.length-1}const s=n[r];if((0,On.wb)(s)){const e=s.config;e.tools||(e.tools=[]);const t=Wn(o.config.name||o.label||"tool",e.tools.map(e=>e.config.name||e.label||"tool"));o.config.name=t,e.tools.push(o)}return{nodes:i,edges:c,history:[].concat((0,a.A)(e.history.slice(0,e.currentHistoryIndex+1)),[{nodes:i,edges:c}]).slice(-50),currentHistoryIndex:e.currentHistoryIndex+1}}}else if((0,On.U9)(o)){if(console.log("Termination component added",o),(0,On.VZ)(t.data.component))return i=e.nodes.map(e=>e.id===r?{...e,data:{...e.data,component:{...e.data.component,config:{...e.data.component.config,termination_condition:o}}}}:e),{nodes:i,edges:c,history:[].concat((0,a.A)(e.history.slice(0,e.currentHistoryIndex+1)),[{nodes:i,edges:c}]).slice(-50),currentHistoryIndex:e.currentHistoryIndex+1}}else if((0,On.ls)(o)&&(0,On.fF)(t.data.component)&&(0,On.O6)(t.data.component)){t.data.component.config.workbench||(t.data.component.config.workbench=[]);let n=Yn(t.data.component.config.workbench);return n.push(o),t.data.component.config.workbench=n,{nodes:i,edges:c,history:[].concat((0,a.A)(e.history.slice(0,e.currentHistoryIndex+1)),[{nodes:i,edges:c}]).slice(-50),currentHistoryIndex:e.currentHistoryIndex+1}}}if((0,On.VZ)(o)){const e={id:_n(),position:t,type:o.component_type,data:{label:o.label||"Team",component:o,type:o.component_type}};i.push(e)}else if((0,On.fF)(o)){const e=i.find(e=>(0,On.VZ)(e.data.component));if(e){if((0,On.O6)(o)&&(0,On.VZ)(e.data.component)){const t=(e.data.component.config.participants||[]).map(e=>e.config.name);o.config.name=Wn(o.config.name,t)}const n={id:_n(),position:t,type:o.component_type,data:{label:o.label||o.config.name,component:o,type:o.component_type}};i.push(n),c.push({id:_n(),source:e.id,target:n.id,sourceHandle:`${e.id}-agent-output-handle`,targetHandle:`${n.id}-agent-input-handle`,type:"agent-connection"}),(0,On.VZ)(e.data.component)&&(e.data.component.config.participants||(e.data.component.config.participants=[]),e.data.component.config.participants.push(n.data.component))}}else if((0,On.ls)(o)){const e={id:_n(),position:t,type:o.component_type,data:{label:o.label||"Workbench",component:o,type:o.component_type}};i.push(e)}if(r){const{nodes:t,edges:n}=(s=c,{nodes:i.map(e=>({...e,data:{...e.data,dimensions:{width:Pn.WIDTH,height:Fn(e.data.component)}}})),edges:s});return{nodes:t,edges:n,history:[].concat((0,a.A)(e.history.slice(0,e.currentHistoryIndex+1)),[{nodes:t,edges:n}]).slice(-50),currentHistoryIndex:e.currentHistoryIndex+1}}var s;const{nodes:l,edges:d}=Xn(i,c,e.teamId||void 0);return{nodes:l,edges:d,history:[].concat((0,a.A)(e.history.slice(0,e.currentHistoryIndex+1)),[{nodes:l,edges:d}]).slice(-50),currentHistoryIndex:e.currentHistoryIndex+1}})},updateNode:(t,n)=>{e(e=>{const r=e.nodes.map(r=>{if(r.id!==t){return(0,On.VZ)(r.data.component)&&e.edges.some(e=>"agent-connection"===e.type&&e.target===t&&e.source===r.id)&&(0,On.VZ)(r.data.component)?{...r,data:{...r.data,component:{...r.data.component,config:{...r.data.component.config,participants:r.data.component.config.participants.map(r=>{var o;return r===(null===(o=e.nodes.find(e=>e.id===t))||void 0===o?void 0:o.data.component)?n.component:r})}}}}:r}const o=n.component||r.data.component;return{...r,data:{...r.data,...n,component:o}}});return{nodes:r,history:[].concat((0,a.A)(e.history.slice(0,e.currentHistoryIndex+1)),[{nodes:r,edges:e.edges}]).slice(-50),currentHistoryIndex:e.currentHistoryIndex+1}})},removeNode:t=>{e(e=>{const n=new Set,r=new Map,o=t=>{const a=e.nodes.find(e=>e.id===t);if(!a)return;n.add(t);const i=e.edges.filter(e=>e.source===t||e.target===t);if((0,On.VZ)(a.data.component))i.filter(e=>"agent-connection"===e.type).forEach(e=>o(e.target));else if((0,On.fF)(a.data.component)){const t=i.find(e=>"agent-connection"===e.type);if(t){const n=e.nodes.find(e=>e.id===t.source);if(n&&(0,On.VZ)(n.data.component)){const e={...n,data:{...n.data,component:{...n.data.component,config:{...n.data.component.config,participants:n.data.component.config.participants.filter(e=>!An()(e,a.data.component))}}}};r.set(n.id,e)}}}};o(t);const i=e.nodes.filter(e=>!n.has(e.id)).map(e=>r.get(e.id)||e),c=e.edges.filter(e=>!n.has(e.source)&&!n.has(e.target));return{nodes:i,edges:c,history:[].concat((0,a.A)(e.history.slice(0,e.currentHistoryIndex+1)),[{nodes:i,edges:c}]).slice(-50),currentHistoryIndex:e.currentHistoryIndex+1}})},addEdge:t=>{e(e=>({edges:[].concat((0,a.A)(e.edges),[t]),history:[].concat((0,a.A)(e.history.slice(0,e.currentHistoryIndex+1)),[{nodes:e.nodes,edges:[].concat((0,a.A)(e.edges),[t])}]).slice(-50),currentHistoryIndex:e.currentHistoryIndex+1}))},removeEdge:t=>{e(e=>({edges:e.edges.filter(e=>e.id!==t),history:[].concat((0,a.A)(e.history.slice(0,e.currentHistoryIndex+1)),[{nodes:e.nodes,edges:e.edges.filter(e=>e.id!==t)}]).slice(-50),currentHistoryIndex:e.currentHistoryIndex+1}))},setSelectedNode:t=>{e({selectedNodeId:t})},undo:()=>{e(e=>{if(e.currentHistoryIndex<=0)return e;const t=e.history[e.currentHistoryIndex-1];return{...e,nodes:t.nodes,edges:t.edges,currentHistoryIndex:e.currentHistoryIndex-1}})},redo:()=>{e(e=>{if(e.currentHistoryIndex>=e.history.length-1)return e;const t=e.history[e.currentHistoryIndex+1];return{...e,nodes:t.nodes,edges:t.edges,currentHistoryIndex:e.currentHistoryIndex+1}})},syncToJson:()=>{const e=t(),n=e.nodes.filter(e=>"team"===e.data.component.component_type);if(0===n.length)return null;return((e,t,n)=>{if(!(0,On.VZ)(e.data.component))return null;const r={...e.data.component},o=n.filter(t=>t.source===e.id&&"agent-connection"===t.type);return r.config.participants=o.map(e=>{const n=t.find(t=>t.id===e.target);return n&&(0,On.fF)(n.data.component)?n.data.component:null}).filter(e=>null!==e),r})(n[0],e.nodes,e.edges)},layoutNodes:()=>{const n=t();n.teamId&&(e=>{const t=In(),n=t[e];if(n){for(const e in n)n[e].isUserPositioned=!1;Rn(t)}})(n.teamId);const{nodes:r,edges:o}=Xn(n.nodes,n.edges,n.teamId||void 0,!1);e({nodes:r,edges:o,history:[].concat((0,a.A)(n.history.slice(0,n.currentHistoryIndex+1)),[{nodes:r,edges:o}]).slice(-50),currentHistoryIndex:n.currentHistoryIndex+1})},loadFromJson:function(n,r,o){void 0===r&&(r=!0);const{nodes:i,edges:c}=((e,t)=>{const n=[],r=[],o=[];e.config.participants.forEach((e,n)=>{const r=Un(n,o),a=t?Gn(t,e,r):r,i=Vn(a,e);o.push(i)});const a=Bn(o),i=t?Gn(t,e,a):a,c=Vn(i,e);return n.push.apply(n,[c].concat(o)),o.forEach(e=>{var t,n;r.push({id:`e${t=c.id}-${n=e.id}`,source:t,target:n,sourceHandle:`${t}-agent-output-handle`,targetHandle:`${n}-agent-input-handle`,type:"agent-connection"})}),{nodes:n,edges:r}})(n,o),{nodes:s,edges:l}=Xn(i,c,o);if(r)e({nodes:s,edges:l,originalComponent:n,teamId:o||null,history:[{nodes:s,edges:l}],currentHistoryIndex:0,selectedNodeId:null});else{const n=t();An()(s,n.nodes)&&An()(l,n.edges)||e(e=>({nodes:s,edges:l,teamId:o||e.teamId,history:[].concat((0,a.A)(e.history.slice(0,e.currentHistoryIndex+1)),[{nodes:s,edges:l}]).slice(-50),currentHistoryIndex:e.currentHistoryIndex+1}))}return{nodes:s,edges:l}},resetHistory:()=>{e(e=>({history:[{nodes:e.nodes,edges:e.edges}],currentHistoryIndex:0}))},addToHistory:()=>{e(e=>({history:[].concat((0,a.A)(e.history.slice(0,e.currentHistoryIndex+1)),[{nodes:e.nodes,edges:e.edges}]).slice(-50),currentHistoryIndex:e.currentHistoryIndex+1}))}}));var Zn=n(9957),Kn=n(696);const qn=(0,vn.A)("GripVertical",[["circle",{cx:"9",cy:"12",r:"1",key:"1vctgf"}],["circle",{cx:"9",cy:"5",r:"1",key:"hp0tcf"}],["circle",{cx:"9",cy:"19",r:"1",key:"fkjjf6"}],["circle",{cx:"15",cy:"12",r:"1",key:"1tmaij"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["circle",{cx:"15",cy:"19",r:"1",key:"f4zoj3"}]]);var Qn=n(7073),er=n(7133),tr=n(6816),nr=n(5680),rr=n(3324),or=n(942),ar=n(5107);const ir=e=>{let{id:t,type:n,config:o,label:a,icon:i}=e;const{attributes:c,listeners:s,setNodeRef:l,transform:d,isDragging:u}=Rt({id:t,data:{current:{type:n,config:o,label:a}}}),m={transform:K.Transform.toString(d),opacity:u?.8:void 0};return r.createElement("div",Object.assign({ref:l,style:m},c,s,{className:"p-2 text-primary mb-2 border  rounded cursor-move  bg-secondary transition-colors"}),r.createElement("div",{className:"flex items-center gap-2"},r.createElement(qn,{className:"w-4 h-4 inline-block"}),i,r.createElement("span",{className:" text-sm"},a)))},cr=e=>{let{defaultGallery:t}=e;const[n,o]=r.useState(""),[a,i]=r.useState(!1),c=r.useMemo(()=>{var e;return[{title:"智能体",type:"agent",items:t.config.components.agents.map(e=>({label:e.label,config:e})),icon:r.createElement(w.A,{className:"w-4 h-4"})},{title:"模型",type:"model",items:t.config.components.models.map(e=>({label:`${e.label||e.config.model}`,config:e})),icon:r.createElement(Qn.A,{className:"w-4 h-4"})},{title:"工作台",type:"workbench",items:(null===(e=t.config.components.workbenches)||void 0===e?void 0:e.map(e=>({label:e.label||e.provider.split(".").pop(),config:e})))||[],icon:r.createElement(er.A,{className:"w-4 h-4"})},{title:"工具 (已弃用)",type:"tool",items:t.config.components.tools.map(e=>{var t;return{label:(null===(t=e.config)||void 0===t?void 0:t.name)||e.label,config:e}}),icon:r.createElement(tr.A,{className:"w-4 h-4"})},{title:"终止条件",type:"termination",items:t.config.components.terminations.map(e=>({label:`${e.label}`,config:e})),icon:r.createElement(nr.A,{className:"w-4 h-4"})}]},[t]).map(e=>{const t=e.items.filter(e=>{var t;return null===(t=e.label)||void 0===t?void 0:t.toLowerCase().includes(n.toLowerCase())});return{key:e.title,label:r.createElement("div",{className:"flex items-center gap-2 font-medium"},e.icon,r.createElement("span",null,e.title),r.createElement("span",{className:"text-xs text-gray-500"},"(",t.length,")")),children:r.createElement("div",{className:"space-y-2"},t.map((t,n)=>r.createElement(ir,{key:n,id:`${e.title.toLowerCase()}-${n}`,type:e.type,config:t.config,label:t.label||"",icon:e.icon})))}});return a?r.createElement("div",{onClick:()=>i(!1),className:"absolute group top-4 left-4 bg-primary shadow-md rounded px-4 pr-2 py-2 cursor-pointer transition-all duration-300 z-50 flex items-center gap-2"},r.createElement("span",null,"显示组件库"),r.createElement("button",{onClick:()=>i(!1),className:"p-1 group-hover:bg-tertiary rounded transition-colors",title:"最大化组件库"},r.createElement(rr.A,{className:"w-4 h-4"}))):r.createElement(tn.A,{width:300,className:"bg-primary border z-10 mr-2 border-r border-secondary"},r.createElement("div",{className:"rounded p-2 pt-2"},r.createElement("div",{className:"flex justify-between items-center mb-2"},r.createElement("div",{className:"text-normal"},"组件库"),r.createElement("button",{onClick:()=>i(!0),className:"p-1 hover:bg-tertiary rounded transition-colors",title:"最小化组件库"},r.createElement(or.A,{className:"w-4 h-4"}))),r.createElement("div",{className:"mb-4 text-secondary"},"拖拽组件以添加到团队中"),r.createElement("div",{className:"flex items-center gap-2 mb-4"},r.createElement(Zn.A,{placeholder:"搜索组件...",onChange:e=>o(e.target.value),className:"flex-1 p-2"})),r.createElement(Kn.A,{accordion:!0,items:c,defaultActiveKey:["Agents"],bordered:!1,expandIcon:e=>{let{isActive:t}=e;return r.createElement(ar.A,{strokeWidth:1,className:(t?"transform rotate-180":"")+" w-4 h-4"})}})))};var sr=n(6274),lr=n(8188);const dr={team:sr.A,agent:w.A,tool:tr.A,model:Qn.A,termination:nr.A,workbench:er.A},ur=(0,r.memo)(e=>{var t,n,o;let{accepts:a,children:i,className:c,id:s}=e;const{isOver:l,setNodeRef:d,active:u}=function(e){let{data:t,disabled:n=!1,id:o,resizeObserverConfig:a}=e;const i=G("Droppable"),{active:c,dispatch:s,over:l,measureDroppableContainers:d}=(0,r.useContext)(wt),u=(0,r.useRef)({disabled:n}),m=(0,r.useRef)(!1),p=(0,r.useRef)(null),f=(0,r.useRef)(null),{disabled:g,updateMeasurementsFor:v,timeout:h}={...jt,...a},y=$(null!=v?v:o),b=st({callback:(0,r.useCallback)(()=>{m.current?(null!=f.current&&clearTimeout(f.current),f.current=setTimeout(()=>{d(Array.isArray(y.current)?y.current:[y.current]),f.current=null},h)):m.current=!0},[h]),disabled:g||!c}),x=(0,r.useCallback)((e,t)=>{b&&(t&&(b.unobserve(t),m.current=!1),e&&b.observe(e))},[b]),[E,w]=U(x),N=$(t);return(0,r.useEffect)(()=>{b&&E.current&&(b.disconnect(),m.current=!1,b.observe(E.current))},[E,b]),(0,r.useEffect)(()=>(s({type:ce.RegisterDroppable,element:{id:o,key:i,disabled:n,node:E,rect:p,data:N}}),()=>s({type:ce.UnregisterDroppable,key:i,id:o})),[o]),(0,r.useEffect)(()=>{n!==u.current.disabled&&(s({type:ce.SetDroppableDisabled,id:o,key:i,disabled:n}),u.current.disabled=n)},[o,i,n,s]),{active:c,rect:p,isOver:(null==l?void 0:l.id)===o,node:E,over:l,setNodeRef:w}}({id:s,data:{accepts:a}}),m=l&&(null==u||null===(t=u.data)||void 0===t||null===(n=t.current)||void 0===n||null===(o=n.current)||void 0===o?void 0:o.type)&&a.includes(u.data.current.current.type);return r.createElement("div",{ref:d,className:`droppable-zone p-2 ${m?"can-drop":""} ${c||""}`},i)});ur.displayName="DroppableZone";const mr=(0,r.memo)(e=>{let{id:t,data:n,selected:o,dragHandle:a,icon:i,children:c,headerContent:s,descriptionContent:l,className:d,onEditClick:u}=e;const m=Jn(e=>e.removeNode),p=Jn(e=>e.setSelectedNode),f="team"!==n.type;return r.createElement("div",{ref:a,className:`\n        bg-white text-primary relative rounded-lg shadow-lg w-72 \n        ${o?"ring-2 ring-accent":""}\n        ${d||""} \n        transition-all duration-200\n      `},r.createElement("div",{className:"border-b p-3 bg-gray-50 rounded-t-lg"},r.createElement("div",{className:"flex items-center justify-between min-w-0"},r.createElement("div",{className:"flex items-center gap-2 min-w-0 flex-1"},r.createElement(i,{className:"flex-shrink-0 w-5 h-5 text-gray-600"}),r.createElement("span",{className:"font-medium text-gray-800 truncate"},n.component.label)),r.createElement("div",{className:"flex items-center gap-2 flex-shrink-0"},r.createElement("span",{className:"text-xs px-2 py-1 bg-gray-200 rounded text-gray-700"},n.component.component_type),r.createElement("button",{onClick:e=>{e.stopPropagation(),p(t)},className:"p-1 hover:bg-secondary rounded"},r.createElement(lr.A,{className:"w-4 h-4 text-accent"})),f&&r.createElement(r.Fragment,null,r.createElement("button",{onClick:e=>{console.log("remove node",t),e.stopPropagation(),t&&m(t)},className:"p-1 hover:bg-red-100 rounded"},r.createElement(E.A,{className:"w-4 h-4 text-red-500"}))))),s),r.createElement("div",{className:"px-3 py-2 border-b text-sm text-gray-600"},l),r.createElement("div",{className:"p-3 space-y-2"},c))});mr.displayName="BaseNode";const pr=e=>{let{title:t,children:n}=e;return r.createElement("div",{className:"space-y-1 relative"},r.createElement("h4",{className:"text-xs font-medium text-gray-500 uppercase"},t),r.createElement("div",{className:"bg-gray-50 rounded p-2"},n))},fr=e=>{let{connected:t,label:n}=e;return r.createElement("span",{className:`\n      text-xs px-2 py-1 rounded-full\n      ${t?"bg-green-100 text-green-700":"bg-gray-100 text-gray-600"}\n    `},n)},gr=(0,r.memo)(e=>{var t,n;const o=e.data.component,a=(0,On.HX)(o)&&!!o.config.model_client,i=(null===(t=o.config.participants)||void 0===t?void 0:t.length)||0,c=(0,On.gU)(o)?"Swarm":(0,On.HX)(o)?"Selector":"RoundRobin";return r.createElement(mr,Object.assign({},e,{icon:dr.team,headerContent:r.createElement("div",{className:"flex gap-2 mt-2"},r.createElement(fr,{connected:!0,label:c}),(0,On.HX)(o)&&r.createElement(fr,{connected:a,label:"Model"}),r.createElement(fr,{connected:i>0,label:`${i} Agent${i>1?"s":""}`})),descriptionContent:r.createElement("div",null,r.createElement("div",null,r.createElement(C.PA,{content:o.description||o.label||"",textThreshold:150,showFullscreen:!1})),(0,On.HX)(o)&&o.config.selector_prompt&&r.createElement("div",{className:"mt-1 text-xs"},"Selector:"," ",r.createElement(C.PA,{content:o.config.selector_prompt,textThreshold:150,showFullscreen:!1})),(0,On.gU)(o)&&r.createElement("div",{className:"mt-1 text-xs text-gray-600"},"Handoff-based agent coordination"))}),(0,On.HX)(o)&&r.createElement(pr,{title:"Model"},r.createElement("div",{className:"relative"},a&&r.createElement("div",{className:"text-sm"},o.config.model_client.config.model),r.createElement(ur,{id:`${e.id}@@@model-zone`,accepts:["model"]},r.createElement("div",{className:"text-secondary text-xs my-1 text-center"},"Drop model here")))),r.createElement(pr,{title:r.createElement("div",null,"Agents"," ",r.createElement("span",{className:"text-xs text-accent"},"(",i,")"))},r.createElement(Wt.h7,{type:"source",position:Yt.yX.Right,id:`${e.id}-agent-output-handle`,className:"my-right-handle"}),r.createElement("div",{className:"space-y-1"},null===(n=o.config.participants)||void 0===n?void 0:n.map((e,t)=>r.createElement("div",{key:t,className:"relative text-sm py-1 px-2 bg-white rounded flex items-center gap-2"},r.createElement(Qn.A,{className:"w-4 h-4 text-gray-500"}),r.createElement("span",null,e.config.name))),r.createElement(ur,{id:`${e.id}@@@agent-zone`,accepts:["agent"]},r.createElement("div",{className:"text-secondary text-xs my-1 text-center"},"Drop agents here")))),r.createElement(pr,{title:"Terminations"},r.createElement("div",{className:"space-y-1"},o.config.termination_condition&&r.createElement("div",{className:"text-sm py-1 px-2 bg-white rounded flex items-center gap-2"},r.createElement(nr.A,{className:"w-4 h-4 text-gray-500"}),r.createElement("span",null,o.config.termination_condition.label||o.config.termination_condition.component_type)),r.createElement(ur,{id:`${e.id}@@@termination-zone`,accepts:["termination"]},r.createElement("div",{className:"text-secondary text-xs my-1 text-center"},"Drop termination here")))))});gr.displayName="TeamNode";const vr=(0,r.memo)(e=>{var t,n,o;const a=e.data.component,i=(0,On.O6)(a)&&!!a.config.model_client,c=(()=>{if(!(0,On.O6)(a))return[];const e=a.config.workbench;if(!e)return[];return(Array.isArray(e)?e:[e]).map(e=>{if(!e)return{hasWorkbench:!1,toolCount:0,workbenchType:"unknown",serverType:null,workbench:e};var t;if((0,On.gn)(e))return{hasWorkbench:!0,toolCount:(null===(t=e.config.tools)||void 0===t?void 0:t.length)||0,workbenchType:"static",serverType:null,workbench:e};if((0,On.Mf)(e)){var n;return{hasWorkbench:!0,toolCount:0,workbenchType:"mcp",serverType:(null===(n=e.config.server_params)||void 0===n?void 0:n.type)||"unknown",workbench:e}}return{hasWorkbench:!0,toolCount:0,workbenchType:"unknown",serverType:null,workbench:e}})})(),s=c.reduce((e,t)=>e+("static"===t.workbenchType?t.toolCount:0),0);return r.createElement(mr,Object.assign({},e,{icon:dr.agent,headerContent:r.createElement("div",{className:"flex gap-2 mt-2"},(0,On.O6)(a)&&r.createElement(r.Fragment,null,r.createElement(fr,{connected:i,label:"Model"}),r.createElement(fr,{connected:c.length>0,label:`${c.length} Workbench${1!==c.length?"es":""} (${s} Tool${1!==s?"s":""})`}))),descriptionContent:r.createElement("div",null,r.createElement("div",{className:"break-words truncate mb-1"}," ",a.config.name),r.createElement("div",{className:"break-words"}," ",a.description))}),r.createElement(Wt.h7,{type:"target",position:Yt.yX.Left,id:`${e.id}-agent-input-handle`,className:"my-left-handle z-100"}),((0,On.O6)(a)||(0,On.Zj)(a))&&r.createElement(r.Fragment,null,r.createElement(pr,{title:"Model"},r.createElement("div",{className:"relative"},(null===(t=a.config)||void 0===t?void 0:t.model_client)&&r.createElement("div",{className:"text-sm"},null===(n=a.config)||void 0===n||null===(o=n.model_client.config)||void 0===o?void 0:o.model),r.createElement(ur,{id:`${e.id}@@@model-zone`,accepts:["model"]},r.createElement("div",{className:"text-secondary text-xs my-1 text-center"},"Drop model here")))),(0,On.O6)(a)&&r.createElement(pr,{title:`工作台 (${c.length})`},r.createElement(Wt.h7,{type:"target",position:Yt.yX.Left,id:`${e.id}-workbench-input-handle`,className:"my-left-handle"}),r.createElement("div",{className:"space-y-3"},c.length>0?c.map((e,t)=>{var n;return r.createElement("div",{key:t,className:"space-y-1"},r.createElement("div",{className:"text-sm py-1 px-2 bg-white rounded flex items-center gap-2"},r.createElement(er.A,{className:"w-4 h-4 text-gray-500"}),r.createElement("span",null,"static"===e.workbenchType?`静态工作台 (${e.toolCount} 个工具${e.toolCount,""})`:"mcp"===e.workbenchType?`MCP 工作台 (${e.serverType})`:`工作台 (${(null===(n=e.workbench)||void 0===n?void 0:n.provider)||"未知"})`)),"static"===e.workbenchType&&e.toolCount>0&&r.createElement("div",{className:"ml-2"},e.workbench.config.tools.map((e,t)=>r.createElement("div",{key:t,className:"text-sm py-1 px-2 bg-white rounded flex items-center gap-2 mb-1"},r.createElement(tr.A,{className:"w-4 h-4 text-gray-500"}),r.createElement("span",{className:"truncate text-xs"},e.config.name||e.label||"Unnamed Tool")))))}):r.createElement("div",{className:"text-xs text-gray-500 text-center p-2"},"No workbenches connected"),r.createElement(ur,{id:`${e.id}@@@workbench-zone`,accepts:["workbench"]},r.createElement("div",{className:"text-secondary text-xs my-1 text-center"},"Drop workbench here"))))))});vr.displayName="AgentNode";const hr=(0,r.memo)(e=>{const t=e.data.component,n=(()=>{if((0,On.gn)(t)){var e;const n=(null===(e=t.config.tools)||void 0===e?void 0:e.length)||0;return{type:"static",toolCount:n,subtitle:`${n} 个静态工具`,hasContent:n>0}}if((0,On.Mf)(t)){var n;return{type:"mcp",toolCount:0,subtitle:`MCP 服务器 (${(null===(n=t.config.server_params)||void 0===n?void 0:n.type)||"unknown"})`,hasContent:!0}}return{type:"unknown",toolCount:0,subtitle:"未知工作台类型",hasContent:!1}})();return r.createElement(mr,Object.assign({},e,{icon:dr.workbench,headerContent:r.createElement("div",{className:"flex gap-2 mt-2"},r.createElement(fr,{connected:n.hasContent,label:n.subtitle})),descriptionContent:r.createElement("div",null,r.createElement("div",{className:"break-words truncate mb-1"},t.description||"用于管理工具的工作台"))}),r.createElement(Wt.h7,{type:"source",position:Yt.yX.Right,id:`${e.id}-workbench-output-handle`,className:"my-right-handle"}),"static"===n.type&&r.createElement(pr,{title:`Tools (${n.toolCount})`},r.createElement("div",{className:"space-y-1"},n.toolCount>0?t.config.tools.map((e,t)=>r.createElement("div",{key:t,className:"text-sm py-1 px-2 bg-white rounded flex items-center gap-2"},r.createElement(tr.A,{className:"w-4 h-4 text-gray-500"}),r.createElement("span",{className:"truncate text-xs"},e.config.name||e.label||"Unnamed Tool"))):r.createElement("div",{className:"text-xs text-gray-500 text-center p-2"},"No tools configured"),r.createElement(ur,{id:`${e.id}@@@tool-zone`,accepts:["tool"]},r.createElement("div",{className:"text-secondary text-xs my-1 text-center"},"Drop tool here")))),"mcp"===n.type&&r.createElement(pr,{title:"MCP Configuration"},r.createElement("div",{className:"space-y-1"},r.createElement("div",{className:"text-sm py-1 px-2 bg-white rounded flex items-center gap-2"},r.createElement(er.A,{className:"w-4 h-4 text-gray-500"}),r.createElement("span",null,"Dynamic Tools")),r.createElement("div",{className:"text-xs text-gray-600 p-2"},"Tools provided by"," ",t.config.server_params.type," ","server"))))});hr.displayName="WorkbenchNode";const yr={team:gr,agent:vr,workbench:hr},br={"model-connection":{stroke:"rgb(220,220,220)"},"tool-connection":{stroke:"rgb(220,220,220)"},"workbench-connection":{stroke:"rgb(34, 197, 94)"},"agent-connection":{stroke:"rgb(220,220,220)"},"termination-connection":{stroke:"rgb(220,220,220)"}},xr=e=>{let{type:t,data:n,deletable:o,...a}=e;const[i]=(0,Yt.Fp)(a),c=t||"model-connection",{style:s,...l}=a,{sourceX:d,sourceY:u,sourcePosition:m,targetPosition:p,sourceHandleId:f,targetHandleId:g,pathOptions:v,selectable:h,...y}=l;return r.createElement(Wt.tE,Object.assign({path:i,style:{...br[c],strokeWidth:2}},y))},Er={"model-connection":xr,"tool-connection":xr,"workbench-connection":xr,"agent-connection":xr,"termination-connection":xr};var wr=n(8603);const Nr=(0,vn.A)("LayoutGrid",[["rect",{width:"7",height:"7",x:"3",y:"3",rx:"1",key:"1g98yp"}],["rect",{width:"7",height:"7",x:"14",y:"3",rx:"1",key:"6d4xhi"}],["rect",{width:"7",height:"7",x:"14",y:"14",rx:"1",key:"nxv5o0"}],["rect",{width:"7",height:"7",x:"3",y:"14",rx:"1",key:"1bb6yr"}]]);var Cr=n(468),kr=n(6043);const Ar=(0,vn.A)("Undo2",[["path",{d:"M9 14 4 9l5-5",key:"102s5s"}],["path",{d:"M4 9h10.5a5.5 5.5 0 0 1 5.5 5.5a5.5 5.5 0 0 1-5.5 5.5H11",key:"f3b9sd"}]]),Sr=(0,vn.A)("Redo2",[["path",{d:"m15 14 5-5-5-5",key:"12vg1m"}],["path",{d:"M20 9H9.5A5.5 5.5 0 0 0 4 14.5A5.5 5.5 0 0 0 9.5 20H13",key:"6uklza"}]]);var _r=n(4718);var Or=e=>{let{isJsonMode:t,isFullscreen:n,showGrid:o,canUndo:a,canRedo:i,isDirty:c,onToggleView:s,onUndo:l,onRedo:d,onSave:p,onToggleGrid:f,onToggleFullscreen:g,onAutoLayout:v,onToggleMiniMap:h}=e;const y=[{key:"autoLayout",label:"Auto Layout",icon:r.createElement(Nr,{size:16}),onClick:v},{key:"grid",label:"Show Grid",icon:r.createElement(Cr.A,{size:16}),onClick:f},{key:"minimap",label:"Show Mini Map",icon:r.createElement(kr.A,{size:16}),onClick:h}];return r.createElement("div",{className:(n?"fixed top-6 right-6":"absolute top-2 right-2")+" bg-secondary hover:bg-secondary rounded shadow-sm min-w-[200px] z-[60]"},r.createElement("div",{className:"p-1 flex items-center gap-1"},!t&&r.createElement(r.Fragment,null,r.createElement(u.A,{title:"Undo"},r.createElement(m.Ay,{type:"text",icon:r.createElement(Ar,{size:18}),className:"p-1.5 hover:bg-primary/10 rounded-md text-primary/75 hover:text-primary disabled:opacity-50 disabled:cursor-not-allowed",onClick:l,disabled:!a})),r.createElement(u.A,{title:"Redo"},r.createElement(m.Ay,{type:"text",icon:r.createElement(Sr,{size:18}),className:"p-1.5 hover:bg-primary/10 rounded-md text-primary/75 hover:text-primary disabled:opacity-50 disabled:cursor-not-allowed",onClick:d,disabled:!i})),r.createElement(u.A,{title:n?"Exit Fullscreen":"Enter Fullscreen"},r.createElement(m.Ay,{type:"text",icon:n?r.createElement(or.A,{size:18}):r.createElement(rr.A,{size:18}),className:"p-1.5 hover:bg-primary/10 rounded-md text-primary/75 hover:text-primary",onClick:g}))),r.createElement(u.A,{title:"Save Changes"},r.createElement(m.Ay,{type:"text",icon:r.createElement("div",{className:"relative"},r.createElement(xn.A,{size:18}),c&&r.createElement("div",{className:"absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full"})),className:"p-1.5 hover:bg-primary/10 rounded-md text-primary/75 hover:text-primary disabled:opacity-50 disabled:cursor-not-allowed",onClick:p})),r.createElement(u.A,{title:t?"Switch to Visual":"Switch to JSON"},r.createElement(m.Ay,{type:"text",icon:t?r.createElement(hn,{size:18}):r.createElement(yn,{size:18}),className:"p-1.5 hover:bg-primary/10 rounded-md text-primary/75 hover:text-primary",onClick:s})),!t&&r.createElement(wr.A,{menu:{items:y},trigger:["click"],overlayStyle:{zIndex:1001},placement:"bottomRight"},r.createElement(m.Ay,{type:"text",icon:r.createElement(_r.A,{size:18}),className:"p-1.5 hover:bg-primary/10 rounded-md text-primary/75 hover:text-primary",title:"More Options"}))))},Tr=n(9872),Dr=n(181),Ir=n.n(Dr),Rr=n(2697),jr=n(6427),Mr=n(9850);var Hr=e=>{let{isVisble:t,onClose:n,team:o}=e;const{0:a,1:c}=(0,r.useState)(null),{user:s}=(0,r.useContext)(l.v),{0:d,1:u}=(0,r.useState)(!1),{0:m,1:p}=(0,r.useState)(!0),[f,g]=i.Ay.useMessage();(0,r.useEffect)(()=>{t&&null!=o&&o.id&&!a&&(u(!0),(async(e,t)=>{if(null!=s&&s.id)try{const n=`Test Session ${t.substring(0,20)} - ${(new Date).toLocaleString()} `,r=await Mr.j.createSession({name:n,team_id:e},s.id);c(r)}catch(n){f.error("Error creating session")}})(o.id,o.component.label||o.component.component_type).finally(()=>{u(!1)}))},[t,null==o?void 0:o.id]);return r.createElement("div",null,g,r.createElement(gn.A,{title:r.createElement("span",null,"Test Team: ",o.component.label),size:"large",placement:"right",onClose:async()=>{null!=a&&a.id&&m&&await(async e=>{if(null!=s&&s.id)try{await Mr.j.deleteSession(e,s.id),c(null)}catch(t){f.error("Error deleting session")}})(a.id),n()},open:t,extra:r.createElement(Rr.A,{checked:m,onChange:e=>p(e.target.checked)},"Delete session on close")},d&&r.createElement("p",null,"Creating a test session..."),a&&r.createElement(jr.A,{session:a,showCompareButton:!1})))},zr=n(8697),Lr=n(418);const Pr=e=>{let{validation:t,onClose:n}=e;return r.createElement("div",{style:{zIndex:1e3},className:"fixed inset-0 bg-black/80  flex items-center justify-center transition-opacity duration-300",onClick:n},r.createElement("div",{className:"relative bg-primary w-full h-full md:w-4/5 md:h-4/5 md:rounded-lg p-8 overflow-auto",style:{opacity:.95},onClick:e=>e.stopPropagation()},r.createElement(u.A,{title:"Close"},r.createElement("button",{onClick:n,className:"absolute top-4 right-4 p-2 rounded-full bg-tertiary  hover:bg-secondary text-primary transition-colors"},r.createElement(zr.A,{size:24}))),r.createElement("div",{className:"space-y-4"},r.createElement("div",{className:"flex items-center gap-2 mb-4"},r.createElement(wn.A,{size:20,className:"text-red-500"}),r.createElement("h3",{className:"text-lg font-medium"},"Validation Issues"),r.createElement("h4",{className:"text-sm text-secondary"},t.errors.length," errors • ",t.warnings.length," ","warnings")),t.errors.length>0&&r.createElement("div",{className:"space-y-2"},r.createElement("h4",{className:"text-sm font-medium"},"Errors"),t.errors.map((e,t)=>r.createElement("div",{key:t,className:"p-4 bg-tertiary rounded-lg"},r.createElement("div",{className:"flex gap-3"},r.createElement(wn.A,{className:"h-4 w-4 text-red-500 shrink-0 mt-1"}),r.createElement("div",null,r.createElement("div",{className:"text-xs font-medium uppercase text-secondary mb-1"},e.field),r.createElement("div",{className:"text-sm"},e.error),e.suggestion&&r.createElement("div",{className:"text-sm mt-2 text-secondary"},"Suggestion: ",e.suggestion)))))),t.warnings.length>0&&r.createElement("div",{className:"space-y-2 mt-6"},r.createElement("h4",{className:"text-sm font-medium"},"Warnings"),t.warnings.map((e,t)=>r.createElement("div",{key:t,className:"p-4 bg-tertiary rounded-lg"},r.createElement("div",{className:"flex gap-3"},r.createElement(Lr.A,{className:"h-4 w-4 text-yellow-500 shrink-0 mt-1"}),r.createElement("div",null,r.createElement("div",{className:"text-xs font-medium uppercase text-secondary mb-1"},e.field),r.createElement("div",{className:"text-sm"},e.error),e.suggestion&&r.createElement("div",{className:"text-sm mt-2 text-secondary"},"Suggestion: ",e.suggestion)))))))))},$r=e=>{let{validation:t}=e;const[n,o]=r.useState(!1);return r.createElement(r.Fragment,null,r.createElement("div",{className:"flex items-center gap-2 py-2   px-3 bg-secondary rounded  text-sm text-secondary hover:text-primary transition-colors group cursor-pointer",onClick:()=>o(!0)},r.createElement(wn.A,{size:14,className:"text-red-500"}),r.createElement("span",{className:"flex-1"},t.errors.length," errors • ",t.warnings.length," ","warnings"),r.createElement(Lr.A,{size:14,className:"group-hover:text-accent"})),n&&r.createElement(Pr,{validation:t,onClose:()=>o(!1)}))};var Fr=n(6812);const{Sider:Ur,Content:Br}=pn,Vr=e=>{var t;let{team:n,onChange:o,onDirtyStateChange:a,selectedGallery:c}=e;const[s,l,p]=(0,Wt.ck)([]),[f,g,v]=(0,Wt.fM)([]),{0:h,1:y}=(0,r.useState)(!1),{0:b,1:x}=(0,r.useState)(!1),{0:E,1:w}=(0,r.useState)(!0),{0:N,1:C}=(0,r.useState)(!0),k=(0,r.useRef)(null),[A,S]=i.Ay.useMessage(),{0:_,1:O}=(0,r.useState)(null),{0:T,1:D}=(0,r.useState)(null),{0:I,1:R}=(0,r.useState)(!1),{0:j,1:M}=(0,r.useState)(!1),{undo:H,redo:z,loadFromJson:L,syncToJson:P,addNode:$,layoutNodes:F,resetHistory:U,history:B,updateNode:V,selectedNodeId:G,setSelectedNode:X,setNodeUserPositioned:W}=Jn(),Y=Jn(e=>e.currentHistoryIndex),J=Y>0,Z=Y>0,K=Y<B.length-1,q=(0,r.useCallback)(e=>g(t=>(0,Yt.rN)(e,t)),[g]),Q=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,r.useMemo)(()=>[...t].filter(e=>null!=e),[...t])}((ee=Ze,te={activationConstraint:{distance:8}},(0,r.useMemo)(()=>({sensor:ee,options:null!=te?te:{}}),[ee,te])));var ee,te;r.useEffect(()=>{null==a||a(J)},[J,a]),r.useEffect(()=>{if(J){const e=e=>{e.preventDefault(),e.returnValue=""};return window.addEventListener("beforeunload",e),()=>window.removeEventListener("beforeunload",e)}},[J]),r.useEffect(()=>{if(null!=n&&n.component){var e;const{nodes:t,edges:r}=L(n.component,!0,null===(e=n.id)||void 0===e?void 0:e.toString());l(t),g(r)}return re(),()=>{D(null)}},[n,l,g]);const ne=(0,r.useCallback)(Ir()(e=>{try{var t;const r=JSON.parse(e);L(r,!1,null==n||null===(t=n.id)||void 0===t?void 0:t.toString()),Jn.getState().addToHistory()}catch(r){console.error("Invalid JSON:",r)}},1e3),[L,null==n?void 0:n.id]);(0,r.useEffect)(()=>()=>{ne.cancel(),D(null)},[ne]);const re=(0,r.useCallback)(async()=>{const e=P();if(!e)throw new Error("Unable to generate valid configuration");try{R(!0);const t=await d.gw.validateComponent(e);D(t)}catch(t){console.error("Validation error:",t),A.error("Validation failed")}finally{R(!1)}},[P]),oe=(0,r.useCallback)(async()=>{try{const e=P();if(!e)throw new Error("Unable to generate valid configuration");if(o){const t=n?{...n,component:e,created_at:void 0,updated_at:void 0}:{component:e};await o(t),U()}}catch(e){A.error(e instanceof Error?e.message:"Failed to save team configuration")}},[P,o,U]),ae=(0,r.useCallback)(()=>{x(e=>!e)},[]);r.useEffect(()=>{if(!b)return;const e=e=>{"Escape"===e.key&&x(!1)};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[b]),r.useEffect(()=>Jn.subscribe(e=>{l(e.nodes),g(e.edges)}),[l,g]);const ie=(e,t)=>{var n;return(null===(n={model:["team","agent"],tool:["agent"],agent:["team"],team:[],termination:["team"],workbench:["agent"]}[e])||void 0===n?void 0:n.includes(t))||!1},ce=T&&T.is_valid;return r.createElement("div",null,S,r.createElement("div",{className:"flex gap-2 text-xs rounded border-dashed border p-2 mb-2 items-center"},r.createElement("div",{className:"flex-1"},r.createElement(fn.A,{onChange:()=>{y(!h)},className:"mr-2",defaultChecked:!h,checkedChildren:r.createElement("div",{className:" text-xs"},r.createElement(hn,{className:"w-3 h-3 inline-block mt-1 mr-1"})),unCheckedChildren:r.createElement("div",{className:" text-xs"},r.createElement(yn,{className:"w-3 h-3 mt-1 inline-block mr-1"}))}),h?"View JSON":r.createElement(r.Fragment,null,"Visual Builder")," "),r.createElement("div",{className:"flex items-center"},T&&!T.is_valid&&r.createElement("div",{className:"inline-block mr-2"}," ",r.createElement($r,{validation:T})),r.createElement(u.A,{title:"Download Team"},r.createElement(m.Ay,{type:"text",icon:r.createElement(bn.A,{size:18}),className:"p-1.5 hover:bg-primary/10 rounded-md text-primary/75 hover:text-primary",onClick:()=>{const e=JSON.stringify(P(),null,2),t=new Blob([e],{type:"application/json"}),n=URL.createObjectURL(t),r=document.createElement("a");r.href=n,r.download="team-config.json",r.click(),URL.revokeObjectURL(n)}})),r.createElement(u.A,{title:"Save Changes"},r.createElement(m.Ay,{type:"text",icon:r.createElement("div",{className:"relative"},r.createElement(xn.A,{size:18}),J&&r.createElement("div",{className:"absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full"})),className:"p-1.5 hover:bg-primary/10 rounded-md text-primary/75 hover:text-primary disabled:opacity-50 disabled:cursor-not-allowed",onClick:oe})),r.createElement(u.A,{title:r.createElement("div",null,"Validate Team",T&&r.createElement("div",{className:"text-xs text-center my-1"},ce?r.createElement("span",null,r.createElement(En.A,{className:"w-3 h-3 text-green-500 inline-block mr-1"}),"success"):r.createElement("div",{className:""},r.createElement(wn.A,{className:"w-3 h-3 text-red-500 inline-block mr-1"}),"errors")))},r.createElement(m.Ay,{type:"text",loading:I,icon:r.createElement("div",{className:"relative"},r.createElement(Nn,{size:18}),T&&r.createElement("div",{className:` ${ce?"bg-green-500":"bg-red-500"} absolute top-0 right-0 w-2 h-2  rounded-full`})),className:"p-1.5 hover:bg-primary/10 rounded-md text-primary/75 hover:text-primary disabled:opacity-50 disabled:cursor-not-allowed",onClick:re})),r.createElement(u.A,{title:"Run Team"},r.createElement(m.Ay,{type:"primary",icon:r.createElement(Cn.A,{size:18}),className:"p-1.5 ml-2 px-2.5 hover:bg-primary/10 rounded-md text-primary/75 hover:text-primary",onClick:()=>{M(!0)}},"Run")))),r.createElement(Tt,{sensors:Q,onDragEnd:e=>{var t,n;const{active:r,over:o}=e;if(!o||null===(t=r.data)||void 0===t||null===(n=t.current)||void 0===n||!n.current)return;const a=r.data.current.current,i=o.id,[c]=i.split("@@@"),l=s.find(e=>e.id===c);if(!l)return;if(!ie(a.type,l.data.component.component_type))return;const d={x:e.delta.x,y:e.delta.y};$(d,a.config,c),O(null)},onDragOver:e=>{const{active:t,over:n}=e;if(null==n||!n.id||!t.data.current)return;const r=t.data.current.type,o=s.find(e=>e.id===n.id);if(!o)return;const a=ie(r,o.data.component.component_type);o.className=a?"drop-target-valid":"drop-target-invalid"},onDragStart:e=>{const{active:t}=e;t.data.current&&O(t.data.current)}},r.createElement(pn,{className:" relative bg-primary  h-[calc(100vh-239px)] rounded"},!h&&c&&r.createElement(cr,{defaultGallery:c}),r.createElement(pn,{className:"bg-primary rounded"},r.createElement(Br,{className:"relative rounded bg-tertiary  "},r.createElement("div",{className:"w-full h-full transition-all duration-200 "+(b?"fixed inset-4 z-50 shadow bg-tertiary  backdrop-blur-sm":"")},h?r.createElement(Tr.T,{value:JSON.stringify(P(),null,2),onChange:ne,editorRef:k,language:"json",minimap:!1}):r.createElement(Wt.Gc,{nodes:s,edges:f,onNodesChange:p,onEdgesChange:v,onConnect:q,onNodeDragStop:(e,t)=>{W(t.id,t.position),console.log("Node dragged:",t.id)},nodeTypes:yr,edgeTypes:Er,onDrop:e=>e.preventDefault(),onDragOver:e=>e.preventDefault(),className:"rounded",fitView:!0,fitViewOptions:{padding:10}},E&&r.createElement(Wt.VS,null),N&&r.createElement(Wt.of,null))),b&&r.createElement("div",{className:"fixed inset-0 -z-10 bg-background bg-opacity-80 backdrop-blur-sm",onClick:ae}),r.createElement(Or,{isJsonMode:h,isFullscreen:b,showGrid:E,onToggleMiniMap:()=>C(!N),canUndo:Z,canRedo:K,isDirty:J,onToggleView:()=>y(!h),onUndo:H,onRedo:z,onSave:oe,onToggleGrid:()=>w(!E),onToggleFullscreen:ae,onAutoLayout:F}))),G&&r.createElement(gn.A,{title:"Edit Component",placement:"right",size:"large",onClose:()=>X(null),open:!!G,className:"component-editor-drawer"},(null===(t=s.find(e=>e.id===G))||void 0===t?void 0:t.data.component)&&r.createElement(Fr.A,{component:s.find(e=>e.id===G).data.component,onChange:e=>{G&&(V(G,{component:e}),oe())},onClose:()=>X(null),navigationDepth:!0}))),r.createElement(Xt,{dropAnimation:{duration:250,easing:"cubic-bezier(0.18, 0.67, 0.6, 1.22)"}},_?r.createElement("div",{className:"p-2 text-primary h-full     rounded    "},r.createElement("div",{className:"flex items-center gap-2"},_.icon,r.createElement("span",{className:"text-sm"},_.label))):null)),j&&r.createElement(Hr,{isVisble:j,team:n,onClose:()=>{M(!1)}}))};var Gr=()=>{var e;const{0:t,1:n}=(0,r.useState)(!1),{0:o,1:u}=(0,r.useState)([]),{0:m,1:p}=(0,r.useState)(null),{0:f,1:g}=(0,r.useState)(()=>{if("undefined"!=typeof window){const e=localStorage.getItem("teamSidebar");return null===e||JSON.parse(e)}}),{0:v,1:h}=(0,r.useState)(null),{user:y}=(0,r.useContext)(l.v),[b,x]=i.Ay.useMessage(),{0:E,1:w}=(0,r.useState)(!1);(0,r.useEffect)(()=>{"undefined"!=typeof window&&localStorage.setItem("teamSidebar",JSON.stringify(f))},[f]);const N=(0,r.useCallback)(async()=>{if(null!=y&&y.id)try{n(!0);const e=await d.CG.listTeams(y.id);u(e),!m&&e.length>0&&p(e[0])}catch(e){console.error("Error fetching teams:",e)}finally{n(!1)}},[null==y?void 0:y.id,m]);(0,r.useEffect)(()=>{N()},[N]),(0,r.useEffect)(()=>{const e=new URLSearchParams(window.location.search).get("teamId");e&&!m&&C({id:parseInt(e)})},[]);const C=async e=>{null!=y&&y.id&&e.id&&(E?c.A.confirm({title:"Unsaved Changes",content:"You have unsaved changes. Do you want to discard them?",okText:"Discard",cancelText:"Go Back",onOk:()=>{k(e.id)}}):await k(e.id))},k=async e=>{if(e&&null!=y&&y.id){n(!0);try{const t=await d.CG.getTeam(e,y.id);p(t),window.history.pushState({},"",`?teamId=${e}`)}catch(t){console.error("Error loading team:",t),b.error("Failed to load team")}finally{n(!1)}}},A=async e=>{if(null!=y&&y.id)try{const t={...e,created_at:void 0,updated_at:void 0},n=await d.CG.createTeam(t,y.id);b.success(`Team ${e.id?"updated":"created"} successfully`),e.id?(u(o.map(e=>e.id===n.id?n:e)),(null==m?void 0:m.id)===n.id&&p(n)):(u([n].concat((0,a.A)(o))),p(n))}catch(t){throw t}};return r.createElement("div",{className:"relative flex h-full w-full"},x,r.createElement("div",{className:"absolute left-0 top-0 h-full transition-all duration-200 ease-in-out "+(f?"w-64":"w-12")},r.createElement(_,{isOpen:f,teams:o,currentTeam:m,onToggle:()=>g(!f),onSelectTeam:C,onCreateTeam:e=>{p(e),A(e)},onEditTeam:p,onDeleteTeam:async e=>{if(null!=y&&y.id)try{await d.CG.deleteTeam(e,y.id),(e=>{const t=In();delete t[e],Rn(t)})(e.toString()),u(o.filter(t=>t.id!==e)),(null==m?void 0:m.id)===e&&p(null),b.success("Team deleted")}catch(t){console.error("Error deleting team:",t),b.error("Error deleting team")}},isLoading:t,setSelectedGallery:h,selectedGallery:v})),r.createElement("div",{className:"flex-1 transition-all -mr-6 duration-200 "+(f?"ml-64":"ml-12")},r.createElement("div",{className:"p-4 pt-2"},r.createElement("div",{className:"flex items-center gap-2 mb-4 text-sm"},r.createElement("span",{className:"text-primary font-medium"},"Teams"),m&&r.createElement(r.Fragment,null,r.createElement(s.A,{className:"w-4 h-4 text-secondary"}),r.createElement("span",{className:"text-secondary"},null===(e=m.component)||void 0===e?void 0:e.label,m.id?"":r.createElement("span",{className:"text-xs text-orange-500"}," (New)")))),m?r.createElement(Vr,{team:m,onChange:A,onDirtyStateChange:w,selectedGallery:v}):r.createElement("div",{className:"flex items-center justify-center h-[calc(100vh-190px)] text-secondary"},"Select a team from the sidebar or create a new one"))))};var Xr=e=>{let{data:t}=e;return r.createElement(o.A,{meta:t.site.siteMetadata,title:"构建",link:"/build"},r.createElement("main",{style:{height:"100%"},className:" h-full "},r.createElement(Gr,null)))}},3805:function(e){e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},3862:function(e){e.exports=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}},4218:function(e){e.exports=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}},4247:function(e){e.exports=function(e){var t=-1,n=Array(e.size);return e.forEach(function(e){n[++t]=e}),n}},4248:function(e){e.exports=function(e,t){for(var n=-1,r=null==e?0:e.length;++n<r;)if(t(e[n],n,e))return!0;return!1}},4335:function(e){e.exports=function(e,t){return function(n){return e(t(n))}}},4509:function(e,t,n){var r=n(2651);e.exports=function(e){return r(this,e).has(e)}},4528:function(e){e.exports=function(e,t){for(var n=-1,r=t.length,o=e.length;++n<r;)e[o+n]=t[n];return e}},4664:function(e,t,n){var r=n(9770),o=n(3345),a=Object.prototype.propertyIsEnumerable,i=Object.getOwnPropertySymbols,c=i?function(e){return null==e?[]:(e=Object(e),r(i(e),function(t){return a.call(e,t)}))}:o;e.exports=c},4739:function(e,t,n){var r=n(6025);e.exports=function(e){var t=this.__data__,n=r(t,e);return n<0?void 0:t[n][1]}},4840:function(e,t,n){var r="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g;e.exports=r},4894:function(e,t,n){var r=n(1882),o=n(294);e.exports=function(e){return null!=e&&o(e.length)&&!r(e)}},4901:function(e,t,n){var r=n(2552),o=n(294),a=n(346),i={};i["[object Float32Array]"]=i["[object Float64Array]"]=i["[object Int8Array]"]=i["[object Int16Array]"]=i["[object Int32Array]"]=i["[object Uint8Array]"]=i["[object Uint8ClampedArray]"]=i["[object Uint16Array]"]=i["[object Uint32Array]"]=!0,i["[object Arguments]"]=i["[object Array]"]=i["[object ArrayBuffer]"]=i["[object Boolean]"]=i["[object DataView]"]=i["[object Date]"]=i["[object Error]"]=i["[object Function]"]=i["[object Map]"]=i["[object Number]"]=i["[object Object]"]=i["[object RegExp]"]=i["[object Set]"]=i["[object String]"]=i["[object WeakMap]"]=!1,e.exports=function(e){return a(e)&&o(e.length)&&!!i[r(e)]}},5083:function(e,t,n){var r=n(1882),o=n(7296),a=n(3805),i=n(7473),c=/^\[object .+?Constructor\]$/,s=Function.prototype,l=Object.prototype,d=s.toString,u=l.hasOwnProperty,m=RegExp("^"+d.call(u).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");e.exports=function(e){return!(!a(e)||o(e))&&(r(e)?m:c).test(i(e))}},5288:function(e){e.exports=function(e,t){return e===t||e!=e&&t!=t}},5481:function(e,t,n){var r=n(9325)["__core-js_shared__"];e.exports=r},5527:function(e){var t=Object.prototype;e.exports=function(e){var n=e&&e.constructor;return e===("function"==typeof n&&n.prototype||t)}},5580:function(e,t,n){var r=n(6110)(n(9325),"DataView");e.exports=r},5749:function(e,t,n){var r=n(1042);e.exports=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=r&&void 0===t?"__lodash_hash_undefined__":t,this}},5861:function(e,t,n){var r=n(5580),o=n(8223),a=n(2804),i=n(6545),c=n(8303),s=n(2552),l=n(7473),d="[object Map]",u="[object Promise]",m="[object Set]",p="[object WeakMap]",f="[object DataView]",g=l(r),v=l(o),h=l(a),y=l(i),b=l(c),x=s;(r&&x(new r(new ArrayBuffer(1)))!=f||o&&x(new o)!=d||a&&x(a.resolve())!=u||i&&x(new i)!=m||c&&x(new c)!=p)&&(x=function(e){var t=s(e),n="[object Object]"==t?e.constructor:void 0,r=n?l(n):"";if(r)switch(r){case g:return f;case v:return d;case h:return u;case y:return m;case b:return p}return t}),e.exports=x},5911:function(e,t,n){var r=n(8859),o=n(4248),a=n(9219);e.exports=function(e,t,n,i,c,s){var l=1&n,d=e.length,u=t.length;if(d!=u&&!(l&&u>d))return!1;var m=s.get(e),p=s.get(t);if(m&&p)return m==t&&p==e;var f=-1,g=!0,v=2&n?new r:void 0;for(s.set(e,t),s.set(t,e);++f<d;){var h=e[f],y=t[f];if(i)var b=l?i(y,h,f,t,e,s):i(h,y,f,e,t,s);if(void 0!==b){if(b)continue;g=!1;break}if(v){if(!o(t,function(e,t){if(!a(v,t)&&(h===e||c(h,e,n,i,s)))return v.push(t)})){g=!1;break}}else if(h!==y&&!c(h,y,n,i,s)){g=!1;break}}return s.delete(e),s.delete(t),g}},5950:function(e,t,n){var r=n(695),o=n(8984),a=n(4894);e.exports=function(e){return a(e)?r(e):o(e)}},6009:function(e,t,n){e=n.nmd(e);var r=n(4840),o=t&&!t.nodeType&&t,a=o&&e&&!e.nodeType&&e,i=a&&a.exports===o&&r.process,c=function(){try{var e=a&&a.require&&a.require("util").types;return e||i&&i.binding&&i.binding("util")}catch(t){}}();e.exports=c},6025:function(e,t,n){var r=n(5288);e.exports=function(e,t){for(var n=e.length;n--;)if(r(e[n][0],t))return n;return-1}},6110:function(e,t,n){var r=n(5083),o=n(392);e.exports=function(e,t){var n=o(e,t);return r(n)?n:void 0}},6449:function(e){var t=Array.isArray;e.exports=t},6545:function(e,t,n){var r=n(6110)(n(9325),"Set");e.exports=r},6721:function(e,t,n){var r=n(1042),o=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;if(r){var n=t[e];return"__lodash_hash_undefined__"===n?void 0:n}return o.call(t,e)?t[e]:void 0}},7068:function(e,t,n){var r=n(7217),o=n(5911),a=n(1986),i=n(689),c=n(5861),s=n(6449),l=n(3656),d=n(7167),u="[object Arguments]",m="[object Array]",p="[object Object]",f=Object.prototype.hasOwnProperty;e.exports=function(e,t,n,g,v,h){var y=s(e),b=s(t),x=y?m:c(e),E=b?m:c(t),w=(x=x==u?p:x)==p,N=(E=E==u?p:E)==p,C=x==E;if(C&&l(e)){if(!l(t))return!1;y=!0,w=!1}if(C&&!w)return h||(h=new r),y||d(e)?o(e,t,n,g,v,h):a(e,t,x,n,g,v,h);if(!(1&n)){var k=w&&f.call(e,"__wrapped__"),A=N&&f.call(t,"__wrapped__");if(k||A){var S=k?e.value():e,_=A?t.value():t;return h||(h=new r),v(S,_,n,g,h)}}return!!C&&(h||(h=new r),i(e,t,n,g,v,h))}},7167:function(e,t,n){var r=n(4901),o=n(7301),a=n(6009),i=a&&a.isTypedArray,c=i?o(i):r;e.exports=c},7217:function(e,t,n){var r=n(79),o=n(1420),a=n(938),i=n(3605),c=n(9817),s=n(945);function l(e){var t=this.__data__=new r(e);this.size=t.size}l.prototype.clear=o,l.prototype.delete=a,l.prototype.get=i,l.prototype.has=c,l.prototype.set=s,e.exports=l},7296:function(e,t,n){var r,o=n(5481),a=(r=/[^.]+$/.exec(o&&o.keys&&o.keys.IE_PROTO||""))?"Symbol(src)_1."+r:"";e.exports=function(e){return!!a&&a in e}},7301:function(e){e.exports=function(e){return function(t){return e(t)}}},7473:function(e){var t=Function.prototype.toString;e.exports=function(e){if(null!=e){try{return t.call(e)}catch(n){}try{return e+""}catch(n){}}return""}},7534:function(e,t,n){var r=n(2552),o=n(346);e.exports=function(e){return o(e)&&"[object Arguments]"==r(e)}},7670:function(e,t,n){var r=n(2651);e.exports=function(e){var t=r(this,e).delete(e);return this.size-=t?1:0,t}},7828:function(e,t,n){var r=n(9325).Uint8Array;e.exports=r},8096:function(e){e.exports=function(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r}},8223:function(e,t,n){var r=n(6110)(n(9325),"Map");e.exports=r},8303:function(e,t,n){var r=n(6110)(n(9325),"WeakMap");e.exports=r},8655:function(e,t,n){var r=n(6025);e.exports=function(e){return r(this.__data__,e)>-1}},8852:function(e,t,n){"use strict";n.d(t,{A:function(){return r}});const r=(0,n(1788).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},8859:function(e,t,n){var r=n(3661),o=n(1380),a=n(1459);function i(e){var t=-1,n=null==e?0:e.length;for(this.__data__=new r;++t<n;)this.add(e[t])}i.prototype.add=i.prototype.push=o,i.prototype.has=a,e.exports=i},8984:function(e,t,n){var r=n(5527),o=n(3650),a=Object.prototype.hasOwnProperty;e.exports=function(e){if(!r(e))return o(e);var t=[];for(var n in Object(e))a.call(e,n)&&"constructor"!=n&&t.push(n);return t}},9219:function(e){e.exports=function(e,t){return e.has(t)}},9325:function(e,t,n){var r=n(4840),o="object"==typeof self&&self&&self.Object===Object&&self,a=r||o||Function("return this")();e.exports=a},9350:function(e){var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}},9770:function(e){e.exports=function(e,t){for(var n=-1,r=null==e?0:e.length,o=0,a=[];++n<r;){var i=e[n];t(i,n,e)&&(a[o++]=i)}return a}},9817:function(e){e.exports=function(e){return this.__data__.has(e)}},9935:function(e){e.exports=function(){return!1}}}]);
//# sourceMappingURL=component---src-pages-build-tsx-8fffd6b35bd63e5515c0.js.map