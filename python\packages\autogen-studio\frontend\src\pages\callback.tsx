import React, { useEffect, useState } from "react";
import { useAuth } from "../auth/context";
import { Spin, Typography, Alert } from "antd";
import Layout from "../components/layout";
import { graphql } from "gatsby";

const { Title } = Typography;

const CallbackPage = ({ data, location }: any) => {
  const { handleAuthCallback } = useAuth();
  const [error, setError] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(true);

  useEffect(() => {
    const processAuth = async () => {
      try {
        // Get the authorization code and state from URL search params
        const params = new URLSearchParams(location.search);
        const code = params.get("code");
        const state = params.get("state");
        const authError = params.get("error");

        if (authError) {
          setError(`认证错误: ${authError}`);
          setIsProcessing(false);
          return;
        }

        if (!code) {
          setError("在URL中未找到授权码");
          setIsProcessing(false);
          return;
        }

        // Handle the authorization code - for popup window
        // The actual token handling is done by the backend HTML response
        await handleAuthCallback(code, state || undefined);
        setIsProcessing(false);
      } catch (err) {
        console.error("Error during auth callback:", err);
        setError("完成认证失败");
        setIsProcessing(false);
      }
    };

    processAuth();
  }, [location.search, handleAuthCallback]);

  return (
    <Layout
      meta={data.site.siteMetadata}
      title="认证中"
      link="/callback"
      showHeader={false}
    >
      <div className="flex flex-col items-center justify-center h-screen">
        {isProcessing ? (
          <>
            <Spin size="large" />
            <Title level={4} className="mt-4">
              正在完成认证...
            </Title>
          </>
        ) : error ? (
          <Alert
            message="认证错误"
            description={error}
            type="error"
            showIcon
            className="max-w-md"
          />
        ) : (
          <Alert
            message="认证成功"
            description="您已成功认证。现在可以关闭此窗口。"
            type="success"
            showIcon
            className="max-w-md"
          />
        )}
      </div>
    </Layout>
  );
};

export const query = graphql`
  query CallbackPageQuery {
    site {
      siteMetadata {
        description
        title
      }
    }
  }
`;

export default CallbackPage;
