"use strict";(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[123],{7495:function(e,t,n){n.r(t),n.d(t,{Head:function(){return c}});var l=n(6540),a=n(588),u=n(1155);t.default=e=>{let{data:t}=e;return l.createElement(u.j,null,l.createElement("main",{style:{height:"100%"},className:"h-full"},l.createElement(a.C,null)))};const c=()=>l.createElement(l.Fragment,null,l.createElement("title",null,"多智能体工作室 - 轻量模式"),l.createElement("meta",{name:"description",content:"多智能体工作室 轻量模式 - 简化的聊天界面"}))}}]);
//# sourceMappingURL=component---src-pages-lite-tsx-d9897c7a3997539cb630.js.map