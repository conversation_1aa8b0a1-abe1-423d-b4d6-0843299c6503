{"version": 3, "file": "eedffc80701a03b1a2d2fdb1388f6aab282c4bcd-cffacb83cd3b987fb3e4.js", "mappings": "0MA+BA,MA3BgBA,IACd,MAAM,UACJC,EAAS,UACTC,EAAS,MACTC,EAAK,KACLC,EAAI,MACJC,GACEL,EACEM,EAAU,IAAW,CACzB,CAAC,GAAGL,QAA0B,UAATG,EACrB,CAAC,GAAGH,QAA0B,UAATG,IAEjBG,EAAW,IAAW,CAC1B,CAAC,GAAGN,YAA+B,WAAVI,EACzB,CAAC,GAAGJ,YAA+B,WAAVI,EACzB,CAAC,GAAGJ,WAA8B,UAAVI,IAEpBG,EAAY,UAAc,IAAsB,iBAATJ,EAAoB,CAC/DK,MAAOL,EACPM,OAAQN,EACRO,WAAY,GAAGP,OACb,CAAC,EAAG,CAACA,IACT,OAAoB,gBAAoB,OAAQ,CAC9CF,UAAW,IAAWD,EAAWK,EAASC,EAAUL,GACpDC,MAAOS,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAGL,GAAYL,M,8BC1BvD,MAAMW,EAAqB,IAAI,KAAU,uBAAwB,CAC/D,KAAM,CACJC,mBAAoB,YAEtB,OAAQ,CACNA,mBAAoB,WAGlBC,EAA+BZ,IAAQ,CAC3CM,OAAQN,EACRO,YAAY,QAAKP,KAEba,EAA+Bb,GAAQQ,OAAOC,OAAO,CACzDJ,MAAOL,GACNY,EAA6BZ,IAC1Bc,EAAmBC,IAAS,CAChCC,WAAYD,EAAME,0BAClBC,eAAgB,YAChBC,cAAeT,EACfU,kBAAmBL,EAAMM,8BACzBC,wBAAyB,OACzBC,wBAAyB,aAErBC,EAA8B,CAACxB,EAAMyB,IAASjB,OAAOC,OAAO,CAChEJ,MAAOoB,EAAKzB,GAAM0B,IAAI,GAAGC,QACzBC,SAAUH,EAAKzB,GAAM0B,IAAI,GAAGC,SAC3Bf,EAA6BZ,IAC1B6B,EAA2Bd,IAC/B,MAAM,kBACJe,EAAiB,kBACjBC,EAAiB,cACjBC,EAAa,gBACbC,EAAe,gBACfC,GACEnB,EACJ,MAAO,CACL,CAACe,GAAoBtB,OAAOC,OAAO,CACjC0B,QAAS,eACTC,cAAe,MACfpB,WAAYe,GACXlB,EAA6BmB,IAChC,CAAC,GAAGF,IAAoBA,YAA6B,CACnDO,aAAc,OAEhB,CAAC,GAAGP,IAAoBA,QAAyBtB,OAAOC,OAAO,CAAC,EAAGI,EAA6BoB,IAChG,CAAC,GAAGH,IAAoBA,QAAyBtB,OAAOC,OAAO,CAAC,EAAGI,EAA6BqB,MAG9FI,EAA0BvB,IAC9B,MAAM,cACJiB,EAAa,eACbO,EAAc,iBACdC,EAAgB,gBAChBP,EAAe,gBACfC,EAAe,kBACfH,EAAiB,KACjBN,GACEV,EACJ,MAAO,CACL,CAACyB,GAAmBhC,OAAOC,OAAO,CAChC0B,QAAS,eACTC,cAAe,MACfpB,WAAYe,EACZM,aAAcE,GACbf,EAA4BQ,EAAeP,IAC9C,CAAC,GAAGe,QAAwBhC,OAAOC,OAAO,CAAC,EAAGe,EAA4BS,EAAiBR,IAC3F,CAAC,GAAGe,QAAwBhC,OAAOC,OAAO,CAAC,EAAGe,EAA4BU,EAAiBT,MAGzFgB,EAA8BzC,GAAQQ,OAAOC,OAAO,CACxDJ,MAAOL,GACNY,EAA6BZ,IAC1B0C,EAA0B3B,IAC9B,MAAM,iBACJ4B,EAAgB,cAChBC,EAAa,kBACbb,EAAiB,eACjBQ,EAAc,KACdd,GACEV,EACJ,MAAO,CACL,CAAC4B,GAAmBnC,OAAOC,OAAOD,OAAOC,OAAO,CAC9C0B,QAAS,cACTU,WAAY,SACZC,eAAgB,SAChBV,cAAe,SACfpB,WAAYe,EACZM,aAAcE,GACbE,EAA4BhB,EAAKmB,GAAelB,IAAI,GAAGC,UAAW,CACnE,CAAC,GAAGgB,UAA0B,CAC5BI,KAAM,WAER,CAAC,GAAGJ,SAAyBnC,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAGgC,EAA4BG,IAAiB,CACxGI,SAAUvB,EAAKmB,GAAelB,IAAI,GAAGC,QACrCsB,UAAWxB,EAAKmB,GAAelB,IAAI,GAAGC,UAExC,CAAC,GAAGgB,QAAuBA,gBAAgC,CACzDN,aAAc,SAGlB,CAAC,GAAGM,IAAmBA,YAA4B,CACjDN,aAAc,SAIda,EAAgC,CAACnC,EAAOf,EAAMmD,KAClD,MAAM,kBACJC,GACErC,EACJ,MAAO,CACL,CAAC,GAAGoC,IAAYC,YAA6B,CAC3C/C,MAAOL,EACP4B,SAAU5B,EACVqC,aAAc,OAEhB,CAAC,GAAGc,IAAYC,WAA4B,CAC1Cf,aAAcrC,KAIdqD,EAA+B,CAACrD,EAAMyB,IAASjB,OAAOC,OAAO,CACjEJ,MAAOoB,EAAKzB,GAAM0B,IAAI,GAAGC,QACzBC,SAAUH,EAAKzB,GAAM0B,IAAI,GAAGC,SAC3Bf,EAA6BZ,IAC1BsD,EAA2BvC,IAC/B,MAAM,eACJwB,EAAc,kBACda,EAAiB,cACjBpB,EAAa,gBACbC,EAAe,gBACfC,EAAe,kBACfH,EAAiB,KACjBN,GACEV,EACJ,OAAOP,OAAOC,OAAOD,OAAOC,OAAOD,OAAOC,OAAOD,OAAOC,OAAOD,OAAOC,OAAO,CAC3E,CAAC2C,GAAoB5C,OAAOC,OAAO,CACjC0B,QAAS,eACTC,cAAe,MACfpB,WAAYe,EACZM,aAAcE,EACdlC,MAAOoB,EAAKO,GAAeN,IAAI,GAAGC,QAClCC,SAAUH,EAAKO,GAAeN,IAAI,GAAGC,SACpC0B,EAA6BrB,EAAeP,KAC9CyB,EAA8BnC,EAAOiB,EAAeoB,IAAqB,CAC1E,CAAC,GAAGA,QAAyB5C,OAAOC,OAAO,CAAC,EAAG4C,EAA6BpB,EAAiBR,MAC3FyB,EAA8BnC,EAAOkB,EAAiB,GAAGmB,SAA0B,CACrF,CAAC,GAAGA,QAAyB5C,OAAOC,OAAO,CAAC,EAAG4C,EAA6BnB,EAAiBT,MAC3FyB,EAA8BnC,EAAOmB,EAAiB,GAAGkB,UAGzDG,EAAexC,IACnB,MAAM,aACJyC,EAAY,kBACZ1B,EAAiB,iBACjB2B,EAAgB,qBAChBC,EAAoB,kBACpBN,EAAiB,iBACjBZ,EAAgB,iBAChBG,EAAgB,cAChBX,EAAa,gBACbC,EAAe,gBACfC,EAAe,kBACfH,EAAiB,QACjB4B,EAAO,SACPC,EAAQ,aACRvB,EAAY,YACZwB,EAAW,YACXC,EAAW,kBACXC,EAAiB,gBACjBC,EAAe,mBACfC,GACElD,EACJ,MAAO,CACL,CAACyC,GAAe,CACdrB,QAAS,QACT9B,MAAO,OACP,CAAC,GAAGmD,YAAwB,CAC1BrB,QAAS,aACT+B,iBAAkBP,EAClBvB,cAAe,MAEf,CAACN,GAAoBtB,OAAOC,OAAO,CACjC0B,QAAS,eACTC,cAAe,MACfpB,WAAYe,GACXlB,EAA6BmB,IAChC,CAAC,GAAGF,YAA6B,CAC/BO,aAAc,OAEhB,CAAC,GAAGP,QAAyBtB,OAAOC,OAAO,CAAC,EAAGI,EAA6BoB,IAC5E,CAAC,GAAGH,QAAyBtB,OAAOC,OAAO,CAAC,EAAGI,EAA6BqB,KAE9E,CAAC,GAAGsB,aAAyB,CAC3BrB,QAAS,aACT9B,MAAO,OACP+B,cAAe,MAEf,CAACqB,GAAmB,CAClBpD,MAAO,OACPC,OAAQuD,EACR7C,WAAYe,EACZM,aAAcyB,EACd,CAAC,KAAKJ,KAAyB,CAC7BS,iBAAkBjC,IAItB,CAACwB,GAAuB,CACtBC,QAAS,EACT,OAAQ,CACNtD,MAAO,OACPC,OAAQyD,EACRK,UAAW,OACXpD,WAAYe,EACZM,aAAcyB,EACd,OAAQ,CACNK,iBAAkBH,KAIxB,CAAC,GAAGN,yDAA6E,CAC/ErD,MAAO,QAGX,CAAC,WAAWmD,aAAyB,CACnC,CAAC,GAAGC,MAAqBC,UAA8B,CACrDrB,kBAIN,CAAC,GAAGmB,iBAA4BA,aAAyB,CAEvD,CAACC,GAAmB,CAClBU,iBAAkBP,EAClB,CAAC,KAAKF,KAAyB,CAC7BS,iBAAkBF,KAKxB,CAAC,GAAGT,IAAeA,aAAyBhD,OAAOC,OAAOD,OAAOC,OAAOD,OAAOC,OAAOD,OAAOC,OAAO,CAClG0B,QAAS,eACT9B,MAAO,QACNiD,EAAyBvC,IAASc,EAAyBd,IAASuB,EAAwBvB,IAAS2B,EAAwB3B,IAEhI,CAAC,GAAGyC,IAAeA,WAAuB,CACxCnD,MAAO,OACP,CAAC+C,GAAoB,CACnB/C,MAAO,QAET,CAACmC,GAAmB,CAClBnC,MAAO,SAIX,CAAC,GAAGmD,IAAeA,YAAwB,CACzC,CAAC,aACGC,eACAC,oBACA5B,eACAsB,eACAZ,eACAG,aACAnC,OAAOC,OAAO,CAAC,EAAGK,EAAiBC,OAuB7C,OAAe,QAAc,WAAYA,IACvC,MAAM,aACJyC,EAAY,KACZ/B,GACEV,EACEsD,GAAgB,QAAWtD,EAAO,CACtCe,kBAAmB,GAAG0B,WACtBC,iBAAkB,GAAGD,UACrBE,qBAAsB,GAAGF,cACzBJ,kBAAmB,GAAGI,WACtBhB,iBAAkB,GAAGgB,UACrBb,iBAAkB,GAAGa,UACrBZ,cAAenB,EAAKV,EAAMiB,eAAeN,IAAI,KAAKC,QAClDU,aAAc,IAEdpB,0BAA2B,0BAA0BF,EAAMgB,0BAA0BhB,EAAMuD,wBAAwBvD,EAAMgB,yBACzHV,8BAA+B,SAEjC,MAAO,CAACkC,EAAac,KApCctD,IACnC,MAAM,iBACJwD,EAAgB,UAChBC,GACEzD,EAGJ,MAAO,CACL0D,MAHwBF,EAIxBG,iBAHsBF,EAItBzC,kBALwBwC,EAMxBD,gBALsBE,EAMtBX,YAAa9C,EAAMiB,cAAgB,EACnC8B,YAAa/C,EAAMwB,eACnB0B,mBAAoBlD,EAAM4D,SAAW5D,EAAM6D,UAC3Cb,kBAAmBhD,EAAMiB,cAAgB,IAsBnB,CACxB6C,iBAAkB,CAAC,CAAC,QAAS,qBAAsB,CAAC,mBAAoB,sBClR1E,MA1BuBjF,IACrB,MACEC,UAAWiF,EAAkB,UAC7BhF,EAAS,cACTiF,EAAa,OACbC,EAAM,MACN/E,EAAQ,SAAQ,KAChBD,EAAO,WACLJ,GACE,aACJqF,GACE,aAAiB,MACfpF,EAAYoF,EAAa,WAAYH,IACpCI,EAAYC,EAAQC,GAAa,EAASvF,GAC3CwF,GAAa,EAAAC,EAAA,GAAK1F,EAAO,CAAC,YAAa,cACvC2F,EAAM,IAAW1F,EAAW,GAAGA,YAAqB,CACxD,CAAC,GAAGA,YAAqBmF,GACxBlF,EAAWiF,EAAeI,EAAQC,GACrC,OAAOF,EAAwB,gBAAoB,MAAO,CACxDpF,UAAWyF,GACG,gBAAoB,EAAS/E,OAAOC,OAAO,CACzDZ,UAAW,GAAGA,WACdI,MAAOA,EACPD,KAAMA,GACLqF,OCKL,MA9BsBzF,IACpB,MACEC,UAAWiF,EAAkB,UAC7BhF,EAAS,cACTiF,EAAa,MACbhF,EAAK,OACLiF,GACEpF,GACE,aACJqF,GACE,aAAiB,MACfpF,EAAYoF,EAAa,WAAYH,IACpCI,EAAYC,EAAQC,GAAa,EAASvF,GAC3C0F,EAAM,IAAW1F,EAAW,GAAGA,YAAqB,CACxD,CAAC,GAAGA,YAAqBmF,GACxBlF,EAAWiF,EAAeI,EAAQC,GACrC,OAAOF,EAAwB,gBAAoB,MAAO,CACxDpF,UAAWyF,GACG,gBAAoB,MAAO,CACzCzF,UAAW,IAAW,GAAGD,UAAmBC,GAC5CC,MAAOA,GACO,gBAAoB,MAAO,CACzCyF,QAAS,gBACTC,MAAO,6BACP3F,UAAW,GAAGD,eACA,gBAAoB,QAAS,KAAM,qBAAmC,gBAAoB,OAAQ,CAChH6F,EA3BS,k3BA4BT5F,UAAW,GAAGD,sBCAlB,MA1BsBD,IACpB,MACEC,UAAWiF,EAAkB,UAC7BhF,EAAS,cACTiF,EAAa,OACbC,EAAM,MACNW,EAAK,KACL3F,EAAO,WACLJ,GACE,aACJqF,GACE,aAAiB,MACfpF,EAAYoF,EAAa,WAAYH,IACpCI,EAAYC,EAAQC,GAAa,EAASvF,GAC3CwF,GAAa,EAAAC,EAAA,GAAK1F,EAAO,CAAC,cAC1B2F,EAAM,IAAW1F,EAAW,GAAGA,YAAqB,CACxD,CAAC,GAAGA,YAAqBmF,EACzB,CAAC,GAAGnF,WAAoB8F,GACvB7F,EAAWiF,EAAeI,EAAQC,GACrC,OAAOF,EAAwB,gBAAoB,MAAO,CACxDpF,UAAWyF,GACG,gBAAoB,EAAS/E,OAAOC,OAAO,CACzDZ,UAAW,GAAGA,UACdG,KAAMA,GACLqF,OCFL,MAxBqBzF,IACnB,MACEC,UAAWiF,EAAkB,UAC7BhF,EAAS,cACTiF,EAAa,MACbhF,EAAK,OACLiF,EAAM,SACNY,GACEhG,GACE,aACJqF,GACE,aAAiB,MACfpF,EAAYoF,EAAa,WAAYH,IACpCI,EAAYC,EAAQC,GAAa,EAASvF,GAC3C0F,EAAM,IAAW1F,EAAW,GAAGA,YAAqB,CACxD,CAAC,GAAGA,YAAqBmF,GACxBG,EAAQrF,EAAWiF,EAAeK,GACrC,OAAOF,EAAwB,gBAAoB,MAAO,CACxDpF,UAAWyF,GACG,gBAAoB,MAAO,CACzCzF,UAAW,IAAW,GAAGD,UAAmBC,GAC5CC,MAAOA,GACN6F,MCxBL,MAAMC,EAAW,CAACC,EAAOlG,KACvB,MAAM,MACJS,EAAK,KACL0F,EAAO,GACLnG,EACJ,OAAIoG,MAAMC,QAAQ5F,GACTA,EAAMyF,GAGXC,EAAO,IAAMD,EACRzF,OADT,GA4BF,MAvBkBT,IAChB,MAAM,UACJC,EAAS,UACTC,EAAS,MACTC,EAAK,KACLgG,EAAO,GACLnG,EACEsG,EAAUF,MAAMG,KAAK,CACzBC,OAAQL,IACPM,IAAI,CAACC,EAAGR,IAGX,gBAAoB,KAAM,CACxBS,IAAKT,EACL/F,MAAO,CACLM,MAAOwF,EAASC,EAAOlG,OAG3B,OAAoB,gBAAoB,KAAM,CAC5CE,UAAW,IAAWD,EAAWC,GACjCC,MAAOA,GACNmG,ICpBL,MAdc,EACZrG,YACAC,YACAO,QACAN,WAIF,gBAAoB,KAAM,CACxBD,UAAW,IAAWD,EAAWC,GACjCC,MAAOS,OAAOC,OAAO,CACnBJ,SACCN,KCHL,SAASyG,EAAkBC,GACzB,OAAIA,GAAwB,iBAATA,EACVA,EAEF,CAAC,CACV,CAyCA,MAAMC,EAAW9G,IACf,MACEC,UAAWiF,EAAkB,QAC7B6B,EAAO,UACP7G,EAAS,cACTiF,EAAa,MACbhF,EAAK,SACL6F,EAAQ,OACRgB,GAAS,EAAK,MACdC,GAAQ,EAAI,UACZC,GAAY,EAAI,OAChB9B,EAAM,MACN+B,GACEnH,GACE,aACJqF,EAAY,UACZ+B,EACAlH,UAAWmH,EACXlH,MAAOmH,IACL,QAAmB,YACjBrH,EAAYoF,EAAa,WAAYH,IACpCI,EAAYC,EAAQC,GAAa,EAASvF,GACjD,GAAI8G,KAAa,YAAa/G,GAAQ,CACpC,MAAMuH,IAAcP,EACdQ,IAAaP,EACbQ,IAAiBP,EAEvB,IAAIQ,EAUAC,EATJ,GAAIJ,EAAW,CACb,MAAMK,EAAchH,OAAOC,OAAOD,OAAOC,OAAO,CAC9CZ,UAAW,GAAGA,YAtEtB,SAA6BuH,EAAUC,GACrC,OAAID,IAAaC,EAER,CACLrH,KAAM,QACNC,MAAO,UAGJ,CACLD,KAAM,QACNC,MAAO,SAEX,CA2DSwH,CAAoBL,EAAUC,IAAgBb,EAAkBI,IAEnEU,EAA0B,gBAAoB,MAAO,CACnDxH,UAAW,GAAGD,YACA,gBAAoB,EAASW,OAAOC,OAAO,CAAC,EAAG+G,IACjE,CAEA,GAAIJ,GAAYC,EAAc,CAE5B,IAAIK,EAQAC,EAPJ,GAAIP,EAAU,CACZ,MAAMQ,EAAapH,OAAOC,OAAOD,OAAOC,OAAO,CAC7CZ,UAAW,GAAGA,WAtExB,SAA4BsH,EAAWE,GACrC,OAAKF,GAAaE,EACT,CACLhH,MAAO,OAGP8G,GAAaE,EACR,CACLhH,MAAO,OAGJ,CAAC,CACV,CA2DWwH,CAAmBV,EAAWE,IAAgBb,EAAkBK,IACnEa,EAAsB,gBAAoB,EAAOlH,OAAOC,OAAO,CAAC,EAAGmH,GACrE,CAGA,GAAIP,EAAc,CAChB,MAAMS,EAAiBtH,OAAOC,OAAOD,OAAOC,OAAO,CACjDZ,UAAW,GAAGA,eAjExB,SAAgCsH,EAAWC,GACzC,MAAMW,EAAa,CAAC,EAWpB,OATKZ,GAAcC,IACjBW,EAAW1H,MAAQ,OAInB0H,EAAWhC,MADRoB,GAAaC,EACE,EAEA,EAEbW,CACT,CAqDWC,CAAuBb,EAAWC,IAAYZ,EAAkBM,IACnEa,EAA6B,gBAAoB,EAAWnH,OAAOC,OAAO,CAAC,EAAGqH,GAChF,CACAP,EAA2B,gBAAoB,MAAO,CACpDzH,UAAW,GAAGD,aACb6H,EAAQC,EACb,CACA,MAAMpC,EAAM,IAAW1F,EAAW,CAChC,CAAC,GAAGA,iBAA0BsH,EAC9B,CAAC,GAAGtH,YAAqBmF,EACzB,CAAC,GAAGnF,SAAgC,QAAdmH,EACtB,CAAC,GAAGnH,WAAoBkH,GACvBE,EAAkBnH,EAAWiF,EAAeI,EAAQC,GACvD,OAAOF,EAAwB,gBAAoB,MAAO,CACxDpF,UAAWyF,EACXxF,MAAOS,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAGyG,GAAenH,IACrDuH,EAAYC,GACjB,CACA,OAAO3B,QAA2CA,EAAW,MAE/Dc,EAASuB,OC5HcrI,IACrB,MACEC,UAAWiF,EAAkB,UAC7BhF,EAAS,cACTiF,EAAa,OACbC,EAAM,MACNW,GAAQ,EAAK,KACb3F,EAAO,WACLJ,GACE,aACJqF,GACE,aAAiB,MACfpF,EAAYoF,EAAa,WAAYH,IACpCI,EAAYC,EAAQC,GAAa,EAASvF,GAC3CwF,GAAa,EAAAC,EAAA,GAAK1F,EAAO,CAAC,cAC1B2F,EAAM,IAAW1F,EAAW,GAAGA,YAAqB,CACxD,CAAC,GAAGA,YAAqBmF,EACzB,CAAC,GAAGnF,WAAoB8F,GACvB7F,EAAWiF,EAAeI,EAAQC,GACrC,OAAOF,EAAwB,gBAAoB,MAAO,CACxDpF,UAAWyF,GACG,gBAAoB,EAAS/E,OAAOC,OAAO,CACzDZ,UAAW,GAAGA,WACdG,KAAMA,GACLqF,ODqGLqB,EAASwB,OAAS,EAClBxB,EAASyB,MAAQ,EACjBzB,EAAS0B,MAAQ,EACjB1B,EAAS2B,KAAO,EAIhB,IEzIA,EFyIA,C", "sources": ["webpack://autogentstudio/./node_modules/antd/es/skeleton/Element.js", "webpack://autogentstudio/./node_modules/antd/es/skeleton/style/index.js", "webpack://autogentstudio/./node_modules/antd/es/skeleton/Avatar.js", "webpack://autogentstudio/./node_modules/antd/es/skeleton/Image.js", "webpack://autogentstudio/./node_modules/antd/es/skeleton/Input.js", "webpack://autogentstudio/./node_modules/antd/es/skeleton/Node.js", "webpack://autogentstudio/./node_modules/antd/es/skeleton/Paragraph.js", "webpack://autogentstudio/./node_modules/antd/es/skeleton/Title.js", "webpack://autogentstudio/./node_modules/antd/es/skeleton/Skeleton.js", "webpack://autogentstudio/./node_modules/antd/es/skeleton/Button.js", "webpack://autogentstudio/./node_modules/antd/es/skeleton/index.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nconst Element = props => {\n  const {\n    prefixCls,\n    className,\n    style,\n    size,\n    shape\n  } = props;\n  const sizeCls = classNames({\n    [`${prefixCls}-lg`]: size === 'large',\n    [`${prefixCls}-sm`]: size === 'small'\n  });\n  const shapeCls = classNames({\n    [`${prefixCls}-circle`]: shape === 'circle',\n    [`${prefixCls}-square`]: shape === 'square',\n    [`${prefixCls}-round`]: shape === 'round'\n  });\n  const sizeStyle = React.useMemo(() => typeof size === 'number' ? {\n    width: size,\n    height: size,\n    lineHeight: `${size}px`\n  } : {}, [size]);\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(prefixCls, sizeCls, shapeCls, className),\n    style: Object.assign(Object.assign({}, sizeStyle), style)\n  });\n};\nexport default Element;", "import { Keyframes, unit } from '@ant-design/cssinjs';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nconst skeletonClsLoading = new Keyframes(`ant-skeleton-loading`, {\n  '0%': {\n    backgroundPosition: '100% 50%'\n  },\n  '100%': {\n    backgroundPosition: '0 50%'\n  }\n});\nconst genSkeletonElementCommonSize = size => ({\n  height: size,\n  lineHeight: unit(size)\n});\nconst genSkeletonElementAvatarSize = size => Object.assign({\n  width: size\n}, genSkeletonElementCommonSize(size));\nconst genSkeletonColor = token => ({\n  background: token.skeletonLoadingBackground,\n  backgroundSize: '400% 100%',\n  animationName: skeletonClsLoading,\n  animationDuration: token.skeletonLoadingMotionDuration,\n  animationTimingFunction: 'ease',\n  animationIterationCount: 'infinite'\n});\nconst genSkeletonElementInputSize = (size, calc) => Object.assign({\n  width: calc(size).mul(5).equal(),\n  minWidth: calc(size).mul(5).equal()\n}, genSkeletonElementCommonSize(size));\nconst genSkeletonElementAvatar = token => {\n  const {\n    skeletonAvatarCls,\n    gradientFromColor,\n    controlHeight,\n    controlHeightLG,\n    controlHeightSM\n  } = token;\n  return {\n    [skeletonAvatarCls]: Object.assign({\n      display: 'inline-block',\n      verticalAlign: 'top',\n      background: gradientFromColor\n    }, genSkeletonElementAvatarSize(controlHeight)),\n    [`${skeletonAvatarCls}${skeletonAvatarCls}-circle`]: {\n      borderRadius: '50%'\n    },\n    [`${skeletonAvatarCls}${skeletonAvatarCls}-lg`]: Object.assign({}, genSkeletonElementAvatarSize(controlHeightLG)),\n    [`${skeletonAvatarCls}${skeletonAvatarCls}-sm`]: Object.assign({}, genSkeletonElementAvatarSize(controlHeightSM))\n  };\n};\nconst genSkeletonElementInput = token => {\n  const {\n    controlHeight,\n    borderRadiusSM,\n    skeletonInputCls,\n    controlHeightLG,\n    controlHeightSM,\n    gradientFromColor,\n    calc\n  } = token;\n  return {\n    [skeletonInputCls]: Object.assign({\n      display: 'inline-block',\n      verticalAlign: 'top',\n      background: gradientFromColor,\n      borderRadius: borderRadiusSM\n    }, genSkeletonElementInputSize(controlHeight, calc)),\n    [`${skeletonInputCls}-lg`]: Object.assign({}, genSkeletonElementInputSize(controlHeightLG, calc)),\n    [`${skeletonInputCls}-sm`]: Object.assign({}, genSkeletonElementInputSize(controlHeightSM, calc))\n  };\n};\nconst genSkeletonElementImageSize = size => Object.assign({\n  width: size\n}, genSkeletonElementCommonSize(size));\nconst genSkeletonElementImage = token => {\n  const {\n    skeletonImageCls,\n    imageSizeBase,\n    gradientFromColor,\n    borderRadiusSM,\n    calc\n  } = token;\n  return {\n    [skeletonImageCls]: Object.assign(Object.assign({\n      display: 'inline-flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      verticalAlign: 'middle',\n      background: gradientFromColor,\n      borderRadius: borderRadiusSM\n    }, genSkeletonElementImageSize(calc(imageSizeBase).mul(2).equal())), {\n      [`${skeletonImageCls}-path`]: {\n        fill: '#bfbfbf'\n      },\n      [`${skeletonImageCls}-svg`]: Object.assign(Object.assign({}, genSkeletonElementImageSize(imageSizeBase)), {\n        maxWidth: calc(imageSizeBase).mul(4).equal(),\n        maxHeight: calc(imageSizeBase).mul(4).equal()\n      }),\n      [`${skeletonImageCls}-svg${skeletonImageCls}-svg-circle`]: {\n        borderRadius: '50%'\n      }\n    }),\n    [`${skeletonImageCls}${skeletonImageCls}-circle`]: {\n      borderRadius: '50%'\n    }\n  };\n};\nconst genSkeletonElementButtonShape = (token, size, buttonCls) => {\n  const {\n    skeletonButtonCls\n  } = token;\n  return {\n    [`${buttonCls}${skeletonButtonCls}-circle`]: {\n      width: size,\n      minWidth: size,\n      borderRadius: '50%'\n    },\n    [`${buttonCls}${skeletonButtonCls}-round`]: {\n      borderRadius: size\n    }\n  };\n};\nconst genSkeletonElementButtonSize = (size, calc) => Object.assign({\n  width: calc(size).mul(2).equal(),\n  minWidth: calc(size).mul(2).equal()\n}, genSkeletonElementCommonSize(size));\nconst genSkeletonElementButton = token => {\n  const {\n    borderRadiusSM,\n    skeletonButtonCls,\n    controlHeight,\n    controlHeightLG,\n    controlHeightSM,\n    gradientFromColor,\n    calc\n  } = token;\n  return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({\n    [skeletonButtonCls]: Object.assign({\n      display: 'inline-block',\n      verticalAlign: 'top',\n      background: gradientFromColor,\n      borderRadius: borderRadiusSM,\n      width: calc(controlHeight).mul(2).equal(),\n      minWidth: calc(controlHeight).mul(2).equal()\n    }, genSkeletonElementButtonSize(controlHeight, calc))\n  }, genSkeletonElementButtonShape(token, controlHeight, skeletonButtonCls)), {\n    [`${skeletonButtonCls}-lg`]: Object.assign({}, genSkeletonElementButtonSize(controlHeightLG, calc))\n  }), genSkeletonElementButtonShape(token, controlHeightLG, `${skeletonButtonCls}-lg`)), {\n    [`${skeletonButtonCls}-sm`]: Object.assign({}, genSkeletonElementButtonSize(controlHeightSM, calc))\n  }), genSkeletonElementButtonShape(token, controlHeightSM, `${skeletonButtonCls}-sm`));\n};\n// =============================== Base ===============================\nconst genBaseStyle = token => {\n  const {\n    componentCls,\n    skeletonAvatarCls,\n    skeletonTitleCls,\n    skeletonParagraphCls,\n    skeletonButtonCls,\n    skeletonInputCls,\n    skeletonImageCls,\n    controlHeight,\n    controlHeightLG,\n    controlHeightSM,\n    gradientFromColor,\n    padding,\n    marginSM,\n    borderRadius,\n    titleHeight,\n    blockRadius,\n    paragraphLiHeight,\n    controlHeightXS,\n    paragraphMarginTop\n  } = token;\n  return {\n    [componentCls]: {\n      display: 'table',\n      width: '100%',\n      [`${componentCls}-header`]: {\n        display: 'table-cell',\n        paddingInlineEnd: padding,\n        verticalAlign: 'top',\n        // Avatar\n        [skeletonAvatarCls]: Object.assign({\n          display: 'inline-block',\n          verticalAlign: 'top',\n          background: gradientFromColor\n        }, genSkeletonElementAvatarSize(controlHeight)),\n        [`${skeletonAvatarCls}-circle`]: {\n          borderRadius: '50%'\n        },\n        [`${skeletonAvatarCls}-lg`]: Object.assign({}, genSkeletonElementAvatarSize(controlHeightLG)),\n        [`${skeletonAvatarCls}-sm`]: Object.assign({}, genSkeletonElementAvatarSize(controlHeightSM))\n      },\n      [`${componentCls}-content`]: {\n        display: 'table-cell',\n        width: '100%',\n        verticalAlign: 'top',\n        // Title\n        [skeletonTitleCls]: {\n          width: '100%',\n          height: titleHeight,\n          background: gradientFromColor,\n          borderRadius: blockRadius,\n          [`+ ${skeletonParagraphCls}`]: {\n            marginBlockStart: controlHeightSM\n          }\n        },\n        // paragraph\n        [skeletonParagraphCls]: {\n          padding: 0,\n          '> li': {\n            width: '100%',\n            height: paragraphLiHeight,\n            listStyle: 'none',\n            background: gradientFromColor,\n            borderRadius: blockRadius,\n            '+ li': {\n              marginBlockStart: controlHeightXS\n            }\n          }\n        },\n        [`${skeletonParagraphCls}> li:last-child:not(:first-child):not(:nth-child(2))`]: {\n          width: '61%'\n        }\n      },\n      [`&-round ${componentCls}-content`]: {\n        [`${skeletonTitleCls}, ${skeletonParagraphCls} > li`]: {\n          borderRadius\n        }\n      }\n    },\n    [`${componentCls}-with-avatar ${componentCls}-content`]: {\n      // Title\n      [skeletonTitleCls]: {\n        marginBlockStart: marginSM,\n        [`+ ${skeletonParagraphCls}`]: {\n          marginBlockStart: paragraphMarginTop\n        }\n      }\n    },\n    // Skeleton element\n    [`${componentCls}${componentCls}-element`]: Object.assign(Object.assign(Object.assign(Object.assign({\n      display: 'inline-block',\n      width: 'auto'\n    }, genSkeletonElementButton(token)), genSkeletonElementAvatar(token)), genSkeletonElementInput(token)), genSkeletonElementImage(token)),\n    // Skeleton Block Button, Input\n    [`${componentCls}${componentCls}-block`]: {\n      width: '100%',\n      [skeletonButtonCls]: {\n        width: '100%'\n      },\n      [skeletonInputCls]: {\n        width: '100%'\n      }\n    },\n    // With active animation\n    [`${componentCls}${componentCls}-active`]: {\n      [`\n        ${skeletonTitleCls},\n        ${skeletonParagraphCls} > li,\n        ${skeletonAvatarCls},\n        ${skeletonButtonCls},\n        ${skeletonInputCls},\n        ${skeletonImageCls}\n      `]: Object.assign({}, genSkeletonColor(token))\n    }\n  };\n};\n// ============================== Export ==============================\nexport const prepareComponentToken = token => {\n  const {\n    colorFillContent,\n    colorFill\n  } = token;\n  const gradientFromColor = colorFillContent;\n  const gradientToColor = colorFill;\n  return {\n    color: gradientFromColor,\n    colorGradientEnd: gradientToColor,\n    gradientFromColor,\n    gradientToColor,\n    titleHeight: token.controlHeight / 2,\n    blockRadius: token.borderRadiusSM,\n    paragraphMarginTop: token.marginLG + token.marginXXS,\n    paragraphLiHeight: token.controlHeight / 2\n  };\n};\nexport default genStyleHooks('Skeleton', token => {\n  const {\n    componentCls,\n    calc\n  } = token;\n  const skeletonToken = mergeToken(token, {\n    skeletonAvatarCls: `${componentCls}-avatar`,\n    skeletonTitleCls: `${componentCls}-title`,\n    skeletonParagraphCls: `${componentCls}-paragraph`,\n    skeletonButtonCls: `${componentCls}-button`,\n    skeletonInputCls: `${componentCls}-input`,\n    skeletonImageCls: `${componentCls}-image`,\n    imageSizeBase: calc(token.controlHeight).mul(1.5).equal(),\n    borderRadius: 100,\n    // Large number to make capsule shape\n    skeletonLoadingBackground: `linear-gradient(90deg, ${token.gradientFromColor} 25%, ${token.gradientToColor} 37%, ${token.gradientFromColor} 63%)`,\n    skeletonLoadingMotionDuration: '1.4s'\n  });\n  return [genBaseStyle(skeletonToken)];\n}, prepareComponentToken, {\n  deprecatedTokens: [['color', 'gradientFromColor'], ['colorGradientEnd', 'gradientToColor']]\n});", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport { ConfigContext } from '../config-provider';\nimport Element from './Element';\nimport useStyle from './style';\nconst SkeletonAvatar = props => {\n  const {\n    prefixCls: customizePrefixCls,\n    className,\n    rootClassName,\n    active,\n    shape = 'circle',\n    size = 'default'\n  } = props;\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('skeleton', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const otherProps = omit(props, ['prefixCls', 'className']);\n  const cls = classNames(prefixCls, `${prefixCls}-element`, {\n    [`${prefixCls}-active`]: active\n  }, className, rootClassName, hashId, cssVarCls);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", {\n    className: cls\n  }, /*#__PURE__*/React.createElement(Element, Object.assign({\n    prefixCls: `${prefixCls}-avatar`,\n    shape: shape,\n    size: size\n  }, otherProps))));\n};\nexport default SkeletonAvatar;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { ConfigContext } from '../config-provider';\nimport useStyle from './style';\nconst path = 'M365.714286 329.142857q0 45.714286-32.036571 77.677714t-77.677714 32.036571-77.677714-32.036571-32.036571-77.677714 32.036571-77.677714 77.677714-32.036571 77.677714 32.036571 32.036571 77.677714zM950.857143 548.571429l0 256-804.571429 0 0-109.714286 182.857143-182.857143 91.428571 91.428571 292.571429-292.571429zM1005.714286 146.285714l-914.285714 0q-7.460571 0-12.873143 5.412571t-5.412571 12.873143l0 694.857143q0 7.460571 5.412571 12.873143t12.873143 5.412571l914.285714 0q7.460571 0 12.873143-5.412571t5.412571-12.873143l0-694.857143q0-7.460571-5.412571-12.873143t-12.873143-5.412571zM1097.142857 164.571429l0 694.857143q0 37.741714-26.843429 64.585143t-64.585143 26.843429l-914.285714 0q-37.741714 0-64.585143-26.843429t-26.843429-64.585143l0-694.857143q0-37.741714 26.843429-64.585143t64.585143-26.843429l914.285714 0q37.741714 0 64.585143 26.843429t26.843429 64.585143z';\nconst SkeletonImage = props => {\n  const {\n    prefixCls: customizePrefixCls,\n    className,\n    rootClassName,\n    style,\n    active\n  } = props;\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('skeleton', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const cls = classNames(prefixCls, `${prefixCls}-element`, {\n    [`${prefixCls}-active`]: active\n  }, className, rootClassName, hashId, cssVarCls);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", {\n    className: cls\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(`${prefixCls}-image`, className),\n    style: style\n  }, /*#__PURE__*/React.createElement(\"svg\", {\n    viewBox: \"0 0 1098 1024\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    className: `${prefixCls}-image-svg`\n  }, /*#__PURE__*/React.createElement(\"title\", null, \"Image placeholder\"), /*#__PURE__*/React.createElement(\"path\", {\n    d: path,\n    className: `${prefixCls}-image-path`\n  })))));\n};\nexport default SkeletonImage;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport { ConfigContext } from '../config-provider';\nimport Element from './Element';\nimport useStyle from './style';\nconst SkeletonInput = props => {\n  const {\n    prefixCls: customizePrefixCls,\n    className,\n    rootClassName,\n    active,\n    block,\n    size = 'default'\n  } = props;\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('skeleton', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const otherProps = omit(props, ['prefixCls']);\n  const cls = classNames(prefixCls, `${prefixCls}-element`, {\n    [`${prefixCls}-active`]: active,\n    [`${prefixCls}-block`]: block\n  }, className, rootClassName, hashId, cssVarCls);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", {\n    className: cls\n  }, /*#__PURE__*/React.createElement(Element, Object.assign({\n    prefixCls: `${prefixCls}-input`,\n    size: size\n  }, otherProps))));\n};\nexport default SkeletonInput;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { ConfigContext } from '../config-provider';\nimport useStyle from './style';\nconst SkeletonNode = props => {\n  const {\n    prefixCls: customizePrefixCls,\n    className,\n    rootClassName,\n    style,\n    active,\n    children\n  } = props;\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('skeleton', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const cls = classNames(prefixCls, `${prefixCls}-element`, {\n    [`${prefixCls}-active`]: active\n  }, hashId, className, rootClassName, cssVarCls);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", {\n    className: cls\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(`${prefixCls}-image`, className),\n    style: style\n  }, children)));\n};\nexport default SkeletonNode;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nconst getWidth = (index, props) => {\n  const {\n    width,\n    rows = 2\n  } = props;\n  if (Array.isArray(width)) {\n    return width[index];\n  }\n  // last paragraph\n  if (rows - 1 === index) {\n    return width;\n  }\n  return undefined;\n};\nconst Paragraph = props => {\n  const {\n    prefixCls,\n    className,\n    style,\n    rows = 0\n  } = props;\n  const rowList = Array.from({\n    length: rows\n  }).map((_, index) => (\n  /*#__PURE__*/\n  // eslint-disable-next-line react/no-array-index-key\n  React.createElement(\"li\", {\n    key: index,\n    style: {\n      width: getWidth(index, props)\n    }\n  })));\n  return /*#__PURE__*/React.createElement(\"ul\", {\n    className: classNames(prefixCls, className),\n    style: style\n  }, rowList);\n};\nexport default Paragraph;", "\"use client\";\n\n/* eslint-disable jsx-a11y/heading-has-content */\nimport * as React from 'react';\nimport classNames from 'classnames';\nconst Title = ({\n  prefixCls,\n  className,\n  width,\n  style\n}) => (\n/*#__PURE__*/\n// biome-ignore lint/a11y/useHeadingContent: HOC here\nReact.createElement(\"h3\", {\n  className: classNames(prefixCls, className),\n  style: Object.assign({\n    width\n  }, style)\n}));\nexport default Title;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useComponentConfig } from '../config-provider/context';\nimport SkeletonAvatar from './Avatar';\nimport SkeletonButton from './Button';\nimport Element from './Element';\nimport SkeletonImage from './Image';\nimport SkeletonInput from './Input';\nimport SkeletonNode from './Node';\nimport Paragraph from './Paragraph';\nimport useStyle from './style';\nimport Title from './Title';\nfunction getComponentProps(prop) {\n  if (prop && typeof prop === 'object') {\n    return prop;\n  }\n  return {};\n}\nfunction getAvatarBasicProps(hasTitle, hasParagraph) {\n  if (hasTitle && !hasParagraph) {\n    // Square avatar\n    return {\n      size: 'large',\n      shape: 'square'\n    };\n  }\n  return {\n    size: 'large',\n    shape: 'circle'\n  };\n}\nfunction getTitleBasicProps(hasAvatar, hasParagraph) {\n  if (!hasAvatar && hasParagraph) {\n    return {\n      width: '38%'\n    };\n  }\n  if (hasAvatar && hasParagraph) {\n    return {\n      width: '50%'\n    };\n  }\n  return {};\n}\nfunction getParagraphBasicProps(hasAvatar, hasTitle) {\n  const basicProps = {};\n  // Width\n  if (!hasAvatar || !hasTitle) {\n    basicProps.width = '61%';\n  }\n  // Rows\n  if (!hasAvatar && hasTitle) {\n    basicProps.rows = 3;\n  } else {\n    basicProps.rows = 2;\n  }\n  return basicProps;\n}\nconst Skeleton = props => {\n  const {\n    prefixCls: customizePrefixCls,\n    loading,\n    className,\n    rootClassName,\n    style,\n    children,\n    avatar = false,\n    title = true,\n    paragraph = true,\n    active,\n    round\n  } = props;\n  const {\n    getPrefixCls,\n    direction,\n    className: contextClassName,\n    style: contextStyle\n  } = useComponentConfig('skeleton');\n  const prefixCls = getPrefixCls('skeleton', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  if (loading || !('loading' in props)) {\n    const hasAvatar = !!avatar;\n    const hasTitle = !!title;\n    const hasParagraph = !!paragraph;\n    // Avatar\n    let avatarNode;\n    if (hasAvatar) {\n      const avatarProps = Object.assign(Object.assign({\n        prefixCls: `${prefixCls}-avatar`\n      }, getAvatarBasicProps(hasTitle, hasParagraph)), getComponentProps(avatar));\n      // We direct use SkeletonElement as avatar in skeleton internal.\n      avatarNode = /*#__PURE__*/React.createElement(\"div\", {\n        className: `${prefixCls}-header`\n      }, /*#__PURE__*/React.createElement(Element, Object.assign({}, avatarProps)));\n    }\n    let contentNode;\n    if (hasTitle || hasParagraph) {\n      // Title\n      let $title;\n      if (hasTitle) {\n        const titleProps = Object.assign(Object.assign({\n          prefixCls: `${prefixCls}-title`\n        }, getTitleBasicProps(hasAvatar, hasParagraph)), getComponentProps(title));\n        $title = /*#__PURE__*/React.createElement(Title, Object.assign({}, titleProps));\n      }\n      // Paragraph\n      let paragraphNode;\n      if (hasParagraph) {\n        const paragraphProps = Object.assign(Object.assign({\n          prefixCls: `${prefixCls}-paragraph`\n        }, getParagraphBasicProps(hasAvatar, hasTitle)), getComponentProps(paragraph));\n        paragraphNode = /*#__PURE__*/React.createElement(Paragraph, Object.assign({}, paragraphProps));\n      }\n      contentNode = /*#__PURE__*/React.createElement(\"div\", {\n        className: `${prefixCls}-content`\n      }, $title, paragraphNode);\n    }\n    const cls = classNames(prefixCls, {\n      [`${prefixCls}-with-avatar`]: hasAvatar,\n      [`${prefixCls}-active`]: active,\n      [`${prefixCls}-rtl`]: direction === 'rtl',\n      [`${prefixCls}-round`]: round\n    }, contextClassName, className, rootClassName, hashId, cssVarCls);\n    return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", {\n      className: cls,\n      style: Object.assign(Object.assign({}, contextStyle), style)\n    }, avatarNode, contentNode));\n  }\n  return children !== null && children !== void 0 ? children : null;\n};\nSkeleton.Button = SkeletonButton;\nSkeleton.Avatar = SkeletonAvatar;\nSkeleton.Input = SkeletonInput;\nSkeleton.Image = SkeletonImage;\nSkeleton.Node = SkeletonNode;\nif (process.env.NODE_ENV !== 'production') {\n  Skeleton.displayName = 'Skeleton';\n}\nexport default Skeleton;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport { ConfigContext } from '../config-provider';\nimport Element from './Element';\nimport useStyle from './style';\nconst SkeletonButton = props => {\n  const {\n    prefixCls: customizePrefixCls,\n    className,\n    rootClassName,\n    active,\n    block = false,\n    size = 'default'\n  } = props;\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('skeleton', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const otherProps = omit(props, ['prefixCls']);\n  const cls = classNames(prefixCls, `${prefixCls}-element`, {\n    [`${prefixCls}-active`]: active,\n    [`${prefixCls}-block`]: block\n  }, className, rootClassName, hashId, cssVarCls);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", {\n    className: cls\n  }, /*#__PURE__*/React.createElement(Element, Object.assign({\n    prefixCls: `${prefixCls}-button`,\n    size: size\n  }, otherProps))));\n};\nexport default SkeletonButton;", "\"use client\";\n\nimport Skeleton from './Skeleton';\nexport default Skeleton;"], "names": ["props", "prefixCls", "className", "style", "size", "shape", "sizeCls", "shapeCls", "sizeStyle", "width", "height", "lineHeight", "Object", "assign", "skeletonClsLoading", "backgroundPosition", "genSkeletonElementCommonSize", "genSkeletonElementAvatarSize", "genSkeletonColor", "token", "background", "skeletonLoadingBackground", "backgroundSize", "animationName", "animationDuration", "skeletonLoadingMotionDuration", "animationTimingFunction", "animationIterationCount", "genSkeletonElementInputSize", "calc", "mul", "equal", "min<PERSON><PERSON><PERSON>", "genSkeletonElementAvatar", "skeletonAvatarCls", "gradientFromColor", "controlHeight", "controlHeightLG", "controlHeightSM", "display", "verticalAlign", "borderRadius", "genSkeletonElementInput", "borderRadiusSM", "skeletonInputCls", "genSkeletonElementImageSize", "genSkeletonElementImage", "skeletonImageCls", "imageSizeBase", "alignItems", "justifyContent", "fill", "max<PERSON><PERSON><PERSON>", "maxHeight", "genSkeletonElementButtonShape", "buttonCls", "skeletonButtonCls", "genSkeletonElementButtonSize", "genSkeletonElementButton", "genBaseStyle", "componentCls", "skeletonTitleCls", "skeletonParagraphCls", "padding", "marginSM", "titleHeight", "blockRadius", "paragraphLiHeight", "controlHeightXS", "paragraphMarginTop", "paddingInlineEnd", "marginBlockStart", "listStyle", "skeletonToken", "gradientToColor", "colorFillContent", "colorFill", "color", "colorGradientEnd", "marginLG", "marginXXS", "deprecatedTokens", "customizePrefixCls", "rootClassName", "active", "getPrefixCls", "wrapCSSVar", "hashId", "cssVarCls", "otherProps", "omit", "cls", "viewBox", "xmlns", "d", "block", "children", "getWidth", "index", "rows", "Array", "isArray", "rowList", "from", "length", "map", "_", "key", "getComponentProps", "prop", "Skeleton", "loading", "avatar", "title", "paragraph", "round", "direction", "contextClassName", "contextStyle", "has<PERSON><PERSON><PERSON>", "hasTitle", "hasParagraph", "avatarNode", "contentNode", "avatarProps", "getAvatarBasicProps", "$title", "paragraphNode", "titleProps", "getTitleBasicProps", "paragraphProps", "basicProps", "getParagraphBasicProps", "<PERSON><PERSON>", "Avatar", "Input", "Image", "Node"], "sourceRoot": ""}