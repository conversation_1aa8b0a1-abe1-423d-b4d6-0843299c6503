import * as React from "react";
import { useEffect, useState } from "react";
import Layout from "../components/layout";
import { graphql, navigate } from "gatsby";
import ChatView from "../components/views/playground/chat/chat";
import { SessionManager } from "../components/views/playground/manager";
import { useAuth } from "../auth/context";
import { Spin, message } from "antd";

// markup
const IndexPage = ({ data, location }: any) => {
  const { loginWithToken, isAuthenticated, isLoading } = useAuth();
  const [isProcessingToken, setIsProcessingToken] = useState(false);

  useEffect(() => {
    const processTokenLogin = async () => {
      // Check if there's a token parameter in the URL
      const params = new URLSearchParams(location.search);
      const token = params.get("token");

      if (token && !isAuthenticated && !isLoading) {
        try {
          setIsProcessingToken(true);
          await loginWithToken(token);

          // Remove token from URL after successful login
          const newUrl = window.location.pathname;
          window.history.replaceState({}, document.title, newUrl);
        } catch (error) {
          console.error("Token login failed:", error);
          message.error("Token login failed. Please try again.");
        } finally {
          setIsProcessingToken(false);
        }
      }
    };

    processTokenLogin();
  }, [location.search, isAuthenticated, isLoading, loginWithToken]);

  // Show loading spinner while processing token login
  if (isProcessingToken) {
    return (
      <Layout meta={data.site.siteMetadata} title="首页" link={"/"}>
        <div className="flex items-center justify-center h-screen">
          <Spin size="large" tip="正在登录..." />
        </div>
      </Layout>
    );
  }

  return (
    <Layout meta={data.site.siteMetadata} title="首页" link={"/"}>
      <main style={{ height: "100%" }} className=" h-full ">
        <SessionManager />
      </main>
    </Layout>
  );
};

export const query = graphql`
  query HomePageQuery {
    site {
      siteMetadata {
        description
        title
      }
    }
  }
`;

export default IndexPage;
