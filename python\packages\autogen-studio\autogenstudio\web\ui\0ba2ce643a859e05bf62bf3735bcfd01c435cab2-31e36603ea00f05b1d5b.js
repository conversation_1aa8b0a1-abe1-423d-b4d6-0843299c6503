"use strict";(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[617],{2318:function(e,n,t){t.d(n,{A:function(){return u}});var r=t(8168),o=t(6540),i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"ellipsis",theme:"outlined"},a=t(7064),l=function(e,n){return o.createElement(a.A,(0,r.A)({},e,{ref:n,icon:i}))};var u=o.forwardRef(l)},3497:function(e,n,t){t.d(n,{A:function(){return E}});var r=t(8168),o=t(4467),i=t(5544),a=t(3986),l=t(2427),u=t(6942),c=t.n(u),s=t(8719),f=t(6540),d=t(6928),m=t(5371),v=d.A.ESC,p=d.A.TAB;var y=(0,f.forwardRef)(function(e,n){var t=e.overlay,r=e.arrow,o=e.prefixCls,i=(0,f.useMemo)(function(){return"function"==typeof t?t():t},[t]),a=(0,s.K4)(n,(0,s.A9)(i));return f.createElement(f.Fragment,null,r&&f.createElement("div",{className:"".concat(o,"-arrow")}),f.cloneElement(i,{ref:(0,s.f3)(i)?a:void 0}))}),A={adjustX:1,adjustY:1},g=[0,0],h={topLeft:{points:["bl","tl"],overflow:A,offset:[0,-4],targetOffset:g},top:{points:["bc","tc"],overflow:A,offset:[0,-4],targetOffset:g},topRight:{points:["br","tr"],overflow:A,offset:[0,-4],targetOffset:g},bottomLeft:{points:["tl","bl"],overflow:A,offset:[0,4],targetOffset:g},bottom:{points:["tc","bc"],overflow:A,offset:[0,4],targetOffset:g},bottomRight:{points:["tr","br"],overflow:A,offset:[0,4],targetOffset:g}},b=["arrow","prefixCls","transitionName","animation","align","placement","placements","getPopupContainer","showAction","hideAction","overlayClassName","overlayStyle","visible","trigger","autoFocus","overlay","children","onVisibleChange"];function C(e,n){var t,u=e.arrow,d=void 0!==u&&u,A=e.prefixCls,g=void 0===A?"rc-dropdown":A,C=e.transitionName,E=e.animation,w=e.align,M=e.placement,R=void 0===M?"bottomLeft":M,N=e.placements,S=void 0===N?h:N,x=e.getPopupContainer,I=e.showAction,k=e.hideAction,K=e.overlayClassName,P=e.overlayStyle,O=e.visible,D=e.trigger,T=void 0===D?["hover"]:D,L=e.autoFocus,_=e.overlay,z=e.children,V=e.onVisibleChange,F=(0,a.A)(e,b),X=f.useState(),Y=(0,i.A)(X,2),j=Y[0],W=Y[1],B="visible"in e?O:j,U=f.useRef(null),G=f.useRef(null),H=f.useRef(null);f.useImperativeHandle(n,function(){return U.current});var $=function(e){W(e),null==V||V(e)};!function(e){var n=e.visible,t=e.triggerRef,r=e.onVisibleChange,o=e.autoFocus,i=e.overlayRef,a=f.useRef(!1),l=function(){var e,o;n&&(null===(e=t.current)||void 0===e||null===(o=e.focus)||void 0===o||o.call(e),null==r||r(!1))},u=function(){var e;return!(null===(e=i.current)||void 0===e||!e.focus||(i.current.focus(),a.current=!0,0))},c=function(e){switch(e.keyCode){case v:l();break;case p:var n=!1;a.current||(n=u()),n?e.preventDefault():l()}};f.useEffect(function(){return n?(window.addEventListener("keydown",c),o&&(0,m.A)(u,3),function(){window.removeEventListener("keydown",c),a.current=!1}):function(){a.current=!1}},[n])}({visible:B,triggerRef:H,onVisibleChange:$,autoFocus:L,overlayRef:G});var q,Q,J,Z=function(){return f.createElement(y,{ref:G,overlay:_,prefixCls:g,arrow:d})},ee=f.cloneElement(z,{className:c()(null===(t=z.props)||void 0===t?void 0:t.className,B&&(q=e.openClassName,void 0!==q?q:"".concat(g,"-open"))),ref:(0,s.f3)(z)?(0,s.K4)(H,(0,s.A9)(z)):void 0}),ne=k;return ne||-1===T.indexOf("contextMenu")||(ne=["click"]),f.createElement(l.A,(0,r.A)({builtinPlacements:S},F,{prefixCls:g,ref:U,popupClassName:c()(K,(0,o.A)({},"".concat(g,"-show-arrow"),d)),popupStyle:P,action:T,showAction:I,hideAction:ne,popupPlacement:R,popupAlign:w,popupTransitionName:C,popupAnimation:E,popupVisible:B,stretch:(Q=e.minOverlayWidthMatchTrigger,J=e.alignPoint,("minOverlayWidthMatchTrigger"in e?Q:!J)?"minWidth":""),popup:"function"==typeof _?Z:Z(),onPopupVisibleChange:$,onPopupClick:function(n){var t=e.onOverlayClick;W(!1),t&&t(n)},getPopupContainer:x}),ee)}var E=f.forwardRef(C)},3561:function(e,n,t){t.d(n,{YU:function(){return u},_j:function(){return v},nP:function(){return l},ox:function(){return i},vR:function(){return a}});var r=t(2187),o=t(4980);const i=new r.Mo("antSlideUpIn",{"0%":{transform:"scaleY(0.8)",transformOrigin:"0% 0%",opacity:0},"100%":{transform:"scaleY(1)",transformOrigin:"0% 0%",opacity:1}}),a=new r.Mo("antSlideUpOut",{"0%":{transform:"scaleY(1)",transformOrigin:"0% 0%",opacity:1},"100%":{transform:"scaleY(0.8)",transformOrigin:"0% 0%",opacity:0}}),l=new r.Mo("antSlideDownIn",{"0%":{transform:"scaleY(0.8)",transformOrigin:"100% 100%",opacity:0},"100%":{transform:"scaleY(1)",transformOrigin:"100% 100%",opacity:1}}),u=new r.Mo("antSlideDownOut",{"0%":{transform:"scaleY(1)",transformOrigin:"100% 100%",opacity:1},"100%":{transform:"scaleY(0.8)",transformOrigin:"100% 100%",opacity:0}}),c=new r.Mo("antSlideLeftIn",{"0%":{transform:"scaleX(0.8)",transformOrigin:"0% 0%",opacity:0},"100%":{transform:"scaleX(1)",transformOrigin:"0% 0%",opacity:1}}),s=new r.Mo("antSlideLeftOut",{"0%":{transform:"scaleX(1)",transformOrigin:"0% 0%",opacity:1},"100%":{transform:"scaleX(0.8)",transformOrigin:"0% 0%",opacity:0}}),f=new r.Mo("antSlideRightIn",{"0%":{transform:"scaleX(0.8)",transformOrigin:"100% 0%",opacity:0},"100%":{transform:"scaleX(1)",transformOrigin:"100% 0%",opacity:1}}),d=new r.Mo("antSlideRightOut",{"0%":{transform:"scaleX(1)",transformOrigin:"100% 0%",opacity:1},"100%":{transform:"scaleX(0.8)",transformOrigin:"100% 0%",opacity:0}}),m={"slide-up":{inKeyframes:i,outKeyframes:a},"slide-down":{inKeyframes:l,outKeyframes:u},"slide-left":{inKeyframes:c,outKeyframes:s},"slide-right":{inKeyframes:f,outKeyframes:d}},v=(e,n)=>{const{antCls:t}=e,r=`${t}-${n}`,{inKeyframes:i,outKeyframes:a}=m[n];return[(0,o.b)(r,i,a,e.motionDurationMid),{[`\n      ${r}-enter,\n      ${r}-appear\n    `]:{transform:"scale(0)",transformOrigin:"0% 0%",opacity:0,animationTimingFunction:e.motionEaseOutQuint,"&-prepare":{transform:"scale(1)"}},[`${r}-leave`]:{animationTimingFunction:e.motionEaseInQuint}}]}},8810:function(e,n,t){t.d(n,{cG:function(){return Fe},q7:function(){return he},te:function(){return je},Dr:function(){return he},g8:function(){return ze},Ay:function(){return Qe},Wj:function(){return x}});var r=t(8168),o=t(4467),i=t(9379),a=t(436),l=t(5544),u=t(3986),c=t(6942),s=t.n(c),f=t(9591),d=t(2533),m=t(3210),v=t(8210),p=t(6540),y=t(961),A=p.createContext(null);function g(e,n){return void 0===e?null:"".concat(e,"-").concat(n)}function h(e){return g(p.useContext(A),e)}var b=t(8104),C=["children","locked"],E=p.createContext(null);function w(e){var n=e.children,t=e.locked,r=(0,u.A)(e,C),o=p.useContext(E),a=(0,b.A)(function(){return e=o,n=r,t=(0,i.A)({},e),Object.keys(n).forEach(function(e){var r=n[e];void 0!==r&&(t[e]=r)}),t;var e,n,t},[o,r],function(e,n){return!(t||e[0]===n[0]&&(0,m.A)(e[1],n[1],!0))});return p.createElement(E.Provider,{value:a},n)}var M=[],R=p.createContext(null);function N(){return p.useContext(R)}var S=p.createContext(M);function x(e){var n=p.useContext(S);return p.useMemo(function(){return void 0!==e?[].concat((0,a.A)(n),[e]):n},[n,e])}var I=p.createContext(null),k=p.createContext({}),K=t(2467);function P(e){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if((0,K.A)(e)){var t=e.nodeName.toLowerCase(),r=["input","select","textarea","button"].includes(t)||e.isContentEditable||"a"===t&&!!e.getAttribute("href"),o=e.getAttribute("tabindex"),i=Number(o),a=null;return o&&!Number.isNaN(i)?a=i:r&&null===a&&(a=0),r&&e.disabled&&(a=null),null!==a&&(a>=0||n&&a<0)}return!1}function O(e){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],t=(0,a.A)(e.querySelectorAll("*")).filter(function(e){return P(e,n)});return P(e,n)&&t.unshift(e),t}var D=t(6928),T=t(5371),L=D.A.LEFT,_=D.A.RIGHT,z=D.A.UP,V=D.A.DOWN,F=D.A.ENTER,X=D.A.ESC,Y=D.A.HOME,j=D.A.END,W=[z,V,L,_];function B(e,n){return O(e,!0).filter(function(e){return n.has(e)})}function U(e,n,t){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1;if(!e)return null;var o=B(e,n),i=o.length,a=o.findIndex(function(e){return t===e});return r<0?-1===a?a=i-1:a-=1:r>0&&(a+=1),o[a=(a+i)%i]}var G=function(e,n){var t=new Set,r=new Map,o=new Map;return e.forEach(function(e){var i=document.querySelector("[data-menu-id='".concat(g(n,e),"']"));i&&(t.add(i),o.set(i,e),r.set(e,i))}),{elements:t,key2element:r,element2key:o}};function H(e,n,t,r,i,a,l,u,c,s){var f=p.useRef(),d=p.useRef();d.current=n;var m=function(){T.A.cancel(f.current)};return p.useEffect(function(){return function(){m()}},[]),function(v){var p=v.which;if([].concat(W,[F,X,Y,j]).includes(p)){var y=a(),A=G(y,r),g=A,h=g.elements,b=g.key2element,C=g.element2key,E=function(e,n){for(var t=e||document.activeElement;t;){if(n.has(t))return t;t=t.parentElement}return null}(b.get(n),h),w=C.get(E),M=function(e,n,t,r){var i,a="prev",l="next",u="children",c="parent";if("inline"===e&&r===F)return{inlineTrigger:!0};var s=(0,o.A)((0,o.A)({},z,a),V,l),f=(0,o.A)((0,o.A)((0,o.A)((0,o.A)({},L,t?l:a),_,t?a:l),V,u),F,u),d=(0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)({},z,a),V,l),F,u),X,c),L,t?u:c),_,t?c:u);switch(null===(i={inline:s,horizontal:f,vertical:d,inlineSub:s,horizontalSub:d,verticalSub:d}["".concat(e).concat(n?"":"Sub")])||void 0===i?void 0:i[r]){case a:return{offset:-1,sibling:!0};case l:return{offset:1,sibling:!0};case c:return{offset:-1,sibling:!1};case u:return{offset:1,sibling:!1};default:return null}}(e,1===l(w,!0).length,t,p);if(!M&&p!==Y&&p!==j)return;(W.includes(p)||[Y,j].includes(p))&&v.preventDefault();var R=function(e){if(e){var n=e,t=e.querySelector("a");null!=t&&t.getAttribute("href")&&(n=t);var r=C.get(e);u(r),m(),f.current=(0,T.A)(function(){d.current===r&&n.focus()})}};if([Y,j].includes(p)||M.sibling||!E){var N,S,x=B(N=E&&"inline"!==e?function(e){for(var n=e;n;){if(n.getAttribute("data-menu-list"))return n;n=n.parentElement}return null}(E):i.current,h);S=p===Y?x[0]:p===j?x[x.length-1]:U(N,h,E,M.offset),R(S)}else if(M.inlineTrigger)c(w);else if(M.offset>0)c(w,!0),m(),f.current=(0,T.A)(function(){A=G(y,r);var e=E.getAttribute("aria-controls"),n=U(document.getElementById(e),A.elements);R(n)},5);else if(M.offset<0){var I=l(w,!0),k=I[I.length-2],K=b.get(k);c(k,!1),R(K)}}null==s||s(v)}}var $="__RC_UTIL_PATH_SPLIT__",q=function(e){return e.join($)},Q="rc-menu-more";function J(){var e=p.useState({}),n=(0,l.A)(e,2)[1],t=(0,p.useRef)(new Map),r=(0,p.useRef)(new Map),o=p.useState([]),i=(0,l.A)(o,2),u=i[0],c=i[1],s=(0,p.useRef)(0),f=(0,p.useRef)(!1),d=(0,p.useCallback)(function(e,o){var i=q(o);r.current.set(i,e),t.current.set(e,i),s.current+=1;var a,l=s.current;a=function(){l===s.current&&(f.current||n({}))},Promise.resolve().then(a)},[]),m=(0,p.useCallback)(function(e,n){var o=q(n);r.current.delete(o),t.current.delete(e)},[]),v=(0,p.useCallback)(function(e){c(e)},[]),y=(0,p.useCallback)(function(e,n){var r=t.current.get(e)||"",o=r.split($);return n&&u.includes(o[0])&&o.unshift(Q),o},[u]),A=(0,p.useCallback)(function(e,n){return e.filter(function(e){return void 0!==e}).some(function(e){return y(e,!0).includes(n)})},[y]),g=(0,p.useCallback)(function(e){var n="".concat(t.current.get(e)).concat($),o=new Set;return(0,a.A)(r.current.keys()).forEach(function(e){e.startsWith(n)&&o.add(r.current.get(e))}),o},[]);return p.useEffect(function(){return function(){f.current=!0}},[]),{registerPath:d,unregisterPath:m,refreshOverflowKeys:v,isSubPathKey:A,getKeyPath:y,getKeys:function(){var e=(0,a.A)(t.current.keys());return u.length&&e.push(Q),e},getSubPathKeys:g}}function Z(e){var n=p.useRef(e);n.current=e;var t=p.useCallback(function(){for(var e,t=arguments.length,r=new Array(t),o=0;o<t;o++)r[o]=arguments[o];return null===(e=n.current)||void 0===e?void 0:e.call.apply(e,[n].concat(r))},[]);return e?t:void 0}var ee=Math.random().toFixed(5).toString().slice(2),ne=0;var te=t(3029),re=t(2901),oe=t(5501),ie=t(9426),ae=t(9853),le=t(8719);function ue(e,n,t,r){var o=p.useContext(E),i=o.activeKey,a=o.onActive,l=o.onInactive,u={active:i===e};return n||(u.onMouseEnter=function(n){null==t||t({key:e,domEvent:n}),a(e)},u.onMouseLeave=function(n){null==r||r({key:e,domEvent:n}),l(e)}),u}function ce(e){var n=p.useContext(E),t=n.mode,r=n.rtl,o=n.inlineIndent;if("inline"!==t)return null;return r?{paddingRight:e*o}:{paddingLeft:e*o}}function se(e){var n,t=e.icon,r=e.props,o=e.children;return null===t||!1===t?null:("function"==typeof t?n=p.createElement(t,(0,i.A)({},r)):"boolean"!=typeof t&&(n=t),n||o||null)}var fe=["item"];function de(e){var n=e.item,t=(0,u.A)(e,fe);return Object.defineProperty(t,"item",{get:function(){return(0,v.Ay)(!1,"`info.item` is deprecated since we will move to function component that not provides React Node instance in future."),n}}),t}var me=["title","attribute","elementRef"],ve=["style","className","eventKey","warnKey","disabled","itemIcon","children","role","onMouseEnter","onMouseLeave","onClick","onKeyDown","onFocus"],pe=["active"],ye=function(e){(0,oe.A)(t,e);var n=(0,ie.A)(t);function t(){return(0,te.A)(this,t),n.apply(this,arguments)}return(0,re.A)(t,[{key:"render",value:function(){var e=this.props,n=e.title,t=e.attribute,o=e.elementRef,i=(0,u.A)(e,me),a=(0,ae.A)(i,["eventKey","popupClassName","popupOffset","onTitleClick"]);return(0,v.Ay)(!t,"`attribute` of Menu.Item is deprecated. Please pass attribute directly."),p.createElement(f.A.Item,(0,r.A)({},t,{title:"string"==typeof n?n:void 0},a,{ref:o}))}}]),t}(p.Component),Ae=p.forwardRef(function(e,n){var t=e.style,l=e.className,c=e.eventKey,f=(e.warnKey,e.disabled),d=e.itemIcon,m=e.children,v=e.role,y=e.onMouseEnter,A=e.onMouseLeave,g=e.onClick,b=e.onKeyDown,C=e.onFocus,w=(0,u.A)(e,ve),M=h(c),R=p.useContext(E),N=R.prefixCls,S=R.onItemClick,I=R.disabled,K=R.overflowDisabled,P=R.itemIcon,O=R.selectedKeys,T=R.onActive,L=p.useContext(k)._internalRenderMenuItem,_="".concat(N,"-item"),z=p.useRef(),V=p.useRef(),F=I||f,X=(0,le.xK)(n,V),Y=x(c);var j=function(e){return{key:c,keyPath:(0,a.A)(Y).reverse(),item:z.current,domEvent:e}},W=d||P,B=ue(c,F,y,A),U=B.active,G=(0,u.A)(B,pe),H=O.includes(c),$=ce(Y.length),q={};"option"===e.role&&(q["aria-selected"]=H);var Q=p.createElement(ye,(0,r.A)({ref:z,elementRef:X,role:null===v?"none":v||"menuitem",tabIndex:f?null:-1,"data-menu-id":K&&M?null:M},(0,ae.A)(w,["extra"]),G,q,{component:"li","aria-disabled":f,style:(0,i.A)((0,i.A)({},$),t),className:s()(_,(0,o.A)((0,o.A)((0,o.A)({},"".concat(_,"-active"),U),"".concat(_,"-selected"),H),"".concat(_,"-disabled"),F),l),onClick:function(e){if(!F){var n=j(e);null==g||g(de(n)),S(n)}},onKeyDown:function(e){if(null==b||b(e),e.which===D.A.ENTER){var n=j(e);null==g||g(de(n)),S(n)}},onFocus:function(e){T(c),null==C||C(e)}}),m,p.createElement(se,{props:(0,i.A)((0,i.A)({},e),{},{isSelected:H}),icon:W}));return L&&(Q=L(Q,e,{selected:H})),Q});function ge(e,n){var t=e.eventKey,o=N(),i=x(t);return p.useEffect(function(){if(o)return o.registerPath(t,i),function(){o.unregisterPath(t,i)}},[i]),o?null:p.createElement(Ae,(0,r.A)({},e,{ref:n}))}var he=p.forwardRef(ge),be=["className","children"],Ce=function(e,n){var t=e.className,o=e.children,i=(0,u.A)(e,be),a=p.useContext(E),l=a.prefixCls,c=a.mode,f=a.rtl;return p.createElement("ul",(0,r.A)({className:s()(l,f&&"".concat(l,"-rtl"),"".concat(l,"-sub"),"".concat(l,"-").concat("inline"===c?"inline":"vertical"),t),role:"menu"},i,{"data-menu-list":!0,ref:n}),o)},Ee=p.forwardRef(Ce);Ee.displayName="SubMenuList";var we=Ee,Me=t(2546);function Re(e,n){return(0,Me.A)(e).map(function(e,t){if(p.isValidElement(e)){var r,o,i=e.key,l=null!==(r=null===(o=e.props)||void 0===o?void 0:o.eventKey)&&void 0!==r?r:i;null==l&&(l="tmp_key-".concat([].concat((0,a.A)(n),[t]).join("-")));var u={key:l,eventKey:l};return p.cloneElement(e,u)}return e})}var Ne=t(2427),Se={adjustX:1,adjustY:1},xe={topLeft:{points:["bl","tl"],overflow:Se},topRight:{points:["br","tr"],overflow:Se},bottomLeft:{points:["tl","bl"],overflow:Se},bottomRight:{points:["tr","br"],overflow:Se},leftTop:{points:["tr","tl"],overflow:Se},leftBottom:{points:["br","bl"],overflow:Se},rightTop:{points:["tl","tr"],overflow:Se},rightBottom:{points:["bl","br"],overflow:Se}},Ie={topLeft:{points:["bl","tl"],overflow:Se},topRight:{points:["br","tr"],overflow:Se},bottomLeft:{points:["tl","bl"],overflow:Se},bottomRight:{points:["tr","br"],overflow:Se},rightTop:{points:["tr","tl"],overflow:Se},rightBottom:{points:["br","bl"],overflow:Se},leftTop:{points:["tl","tr"],overflow:Se},leftBottom:{points:["bl","br"],overflow:Se}};function ke(e,n,t){return n||(t?t[e]||t.other:void 0)}var Ke={horizontal:"bottomLeft",vertical:"rightTop","vertical-left":"rightTop","vertical-right":"leftTop"};function Pe(e){var n=e.prefixCls,t=e.visible,r=e.children,a=e.popup,u=e.popupStyle,c=e.popupClassName,f=e.popupOffset,d=e.disabled,m=e.mode,v=e.onVisibleChange,y=p.useContext(E),A=y.getPopupContainer,g=y.rtl,h=y.subMenuOpenDelay,b=y.subMenuCloseDelay,C=y.builtinPlacements,w=y.triggerSubMenuAction,M=y.forceSubMenuRender,R=y.rootClassName,N=y.motion,S=y.defaultMotions,x=p.useState(!1),I=(0,l.A)(x,2),k=I[0],K=I[1],P=g?(0,i.A)((0,i.A)({},Ie),C):(0,i.A)((0,i.A)({},xe),C),O=Ke[m],D=ke(m,N,S),L=p.useRef(D);"inline"!==m&&(L.current=D);var _=(0,i.A)((0,i.A)({},L.current),{},{leavedClassName:"".concat(n,"-hidden"),removeOnLeave:!1,motionAppear:!0}),z=p.useRef();return p.useEffect(function(){return z.current=(0,T.A)(function(){K(t)}),function(){T.A.cancel(z.current)}},[t]),p.createElement(Ne.A,{prefixCls:n,popupClassName:s()("".concat(n,"-popup"),(0,o.A)({},"".concat(n,"-rtl"),g),c,R),stretch:"horizontal"===m?"minWidth":null,getPopupContainer:A,builtinPlacements:P,popupPlacement:O,popupVisible:k,popup:a,popupStyle:u,popupAlign:f&&{offset:f},action:d?[]:[w],mouseEnterDelay:h,mouseLeaveDelay:b,onPopupVisibleChange:v,forceRender:M,popupMotion:_,fresh:!0},r)}var Oe=t(754);function De(e){var n=e.id,t=e.open,o=e.keyPath,a=e.children,u="inline",c=p.useContext(E),s=c.prefixCls,f=c.forceSubMenuRender,d=c.motion,m=c.defaultMotions,v=c.mode,y=p.useRef(!1);y.current=v===u;var A=p.useState(!y.current),g=(0,l.A)(A,2),h=g[0],b=g[1],C=!!y.current&&t;p.useEffect(function(){y.current&&b(!1)},[v]);var M=(0,i.A)({},ke(u,d,m));o.length>1&&(M.motionAppear=!1);var R=M.onVisibleChanged;return M.onVisibleChanged=function(e){return y.current||e||b(!0),null==R?void 0:R(e)},h?null:p.createElement(w,{mode:u,locked:!y.current},p.createElement(Oe.Ay,(0,r.A)({visible:C},M,{forceRender:f,removeOnLeave:!1,leavedClassName:"".concat(s,"-hidden")}),function(e){var t=e.className,r=e.style;return p.createElement(we,{id:n,className:t,style:r},a)}))}var Te=["style","className","title","eventKey","warnKey","disabled","internalPopupClose","children","itemIcon","expandIcon","popupClassName","popupOffset","popupStyle","onClick","onMouseEnter","onMouseLeave","onTitleClick","onTitleMouseEnter","onTitleMouseLeave"],Le=["active"],_e=p.forwardRef(function(e,n){var t=e.style,a=e.className,c=e.title,d=e.eventKey,m=(e.warnKey,e.disabled),v=e.internalPopupClose,y=e.children,A=e.itemIcon,g=e.expandIcon,b=e.popupClassName,C=e.popupOffset,M=e.popupStyle,R=e.onClick,N=e.onMouseEnter,S=e.onMouseLeave,K=e.onTitleClick,P=e.onTitleMouseEnter,O=e.onTitleMouseLeave,D=(0,u.A)(e,Te),T=h(d),L=p.useContext(E),_=L.prefixCls,z=L.mode,V=L.openKeys,F=L.disabled,X=L.overflowDisabled,Y=L.activeKey,j=L.selectedKeys,W=L.itemIcon,B=L.expandIcon,U=L.onItemClick,G=L.onOpenChange,H=L.onActive,$=p.useContext(k)._internalRenderSubMenuItem,q=p.useContext(I).isSubPathKey,Q=x(),J="".concat(_,"-submenu"),ee=F||m,ne=p.useRef(),te=p.useRef();var re=null!=A?A:W,oe=null!=g?g:B,ie=V.includes(d),ae=!X&&ie,le=q(j,d),fe=ue(d,ee,P,O),me=fe.active,ve=(0,u.A)(fe,Le),pe=p.useState(!1),ye=(0,l.A)(pe,2),Ae=ye[0],ge=ye[1],he=function(e){ee||ge(e)},be=p.useMemo(function(){return me||"inline"!==z&&(Ae||q([Y],d))},[z,me,Y,Ae,d,q]),Ce=ce(Q.length),Ee=Z(function(e){null==R||R(de(e)),U(e)}),Me=T&&"".concat(T,"-popup"),Re=p.useMemo(function(){return p.createElement(se,{icon:"horizontal"!==z?oe:void 0,props:(0,i.A)((0,i.A)({},e),{},{isOpen:ae,isSubMenu:!0})},p.createElement("i",{className:"".concat(J,"-arrow")}))},[z,oe,e,ae,J]),Ne=p.createElement("div",(0,r.A)({role:"menuitem",style:Ce,className:"".concat(J,"-title"),tabIndex:ee?null:-1,ref:ne,title:"string"==typeof c?c:null,"data-menu-id":X&&T?null:T,"aria-expanded":ae,"aria-haspopup":!0,"aria-controls":Me,"aria-disabled":ee,onClick:function(e){ee||(null==K||K({key:d,domEvent:e}),"inline"===z&&G(d,!ie))},onFocus:function(){H(d)}},ve),c,Re),Se=p.useRef(z);if("inline"!==z&&Q.length>1?Se.current="vertical":Se.current=z,!X){var xe=Se.current;Ne=p.createElement(Pe,{mode:xe,prefixCls:J,visible:!v&&ae&&"inline"!==z,popupClassName:b,popupOffset:C,popupStyle:M,popup:p.createElement(w,{mode:"horizontal"===xe?"vertical":xe},p.createElement(we,{id:Me,ref:te},y)),disabled:ee,onVisibleChange:function(e){"inline"!==z&&G(d,e)}},Ne)}var Ie=p.createElement(f.A.Item,(0,r.A)({ref:n,role:"none"},D,{component:"li",style:t,className:s()(J,"".concat(J,"-").concat(z),a,(0,o.A)((0,o.A)((0,o.A)((0,o.A)({},"".concat(J,"-open"),ae),"".concat(J,"-active"),be),"".concat(J,"-selected"),le),"".concat(J,"-disabled"),ee)),onMouseEnter:function(e){he(!0),null==N||N({key:d,domEvent:e})},onMouseLeave:function(e){he(!1),null==S||S({key:d,domEvent:e})}}),Ne,!X&&p.createElement(De,{id:Me,open:ae,keyPath:Q},y));return $&&(Ie=$(Ie,e,{selected:le,active:be,open:ae,disabled:ee})),p.createElement(w,{onItemClick:Ee,mode:"horizontal"===z?"vertical":z,itemIcon:re,expandIcon:oe},Ie)});var ze=p.forwardRef(function(e,n){var t,o=e.eventKey,i=e.children,a=x(o),l=Re(i,a),u=N();return p.useEffect(function(){if(u)return u.registerPath(o,a),function(){u.unregisterPath(o,a)}},[a]),t=u?l:p.createElement(_e,(0,r.A)({ref:n},e),l),p.createElement(S.Provider,{value:a},t)}),Ve=t(2284);function Fe(e){var n=e.className,t=e.style,r=p.useContext(E).prefixCls;return N()?null:p.createElement("li",{role:"separator",className:s()("".concat(r,"-item-divider"),n),style:t})}var Xe=["className","title","eventKey","children"],Ye=p.forwardRef(function(e,n){var t=e.className,o=e.title,i=(e.eventKey,e.children),a=(0,u.A)(e,Xe),l=p.useContext(E).prefixCls,c="".concat(l,"-item-group");return p.createElement("li",(0,r.A)({ref:n,role:"presentation"},a,{onClick:function(e){return e.stopPropagation()},className:s()(c,t)}),p.createElement("div",{role:"presentation",className:"".concat(c,"-title"),title:"string"==typeof o?o:void 0},o),p.createElement("ul",{role:"group",className:"".concat(c,"-list")},i))});var je=p.forwardRef(function(e,n){var t=e.eventKey,o=Re(e.children,x(t));return N()?o:p.createElement(Ye,(0,r.A)({ref:n},(0,ae.A)(e,["warnKey"])),o)}),We=["label","children","key","type","extra"];function Be(e,n,t){var o=n.item,i=n.group,a=n.submenu,l=n.divider;return(e||[]).map(function(e,c){if(e&&"object"===(0,Ve.A)(e)){var s=e,f=s.label,d=s.children,m=s.key,v=s.type,y=s.extra,A=(0,u.A)(s,We),g=null!=m?m:"tmp-".concat(c);return d||"group"===v?"group"===v?p.createElement(i,(0,r.A)({key:g},A,{title:f}),Be(d,n,t)):p.createElement(a,(0,r.A)({key:g},A,{title:f}),Be(d,n,t)):"divider"===v?p.createElement(l,(0,r.A)({key:g},A)):p.createElement(o,(0,r.A)({key:g},A,{extra:y}),f,(!!y||0===y)&&p.createElement("span",{className:"".concat(t,"-item-extra")},y))}return null}).filter(function(e){return e})}function Ue(e,n,t,r,o){var a=e,l=(0,i.A)({divider:Fe,item:he,group:je,submenu:ze},r);return n&&(a=Be(n,l,o)),Re(a,t)}var Ge=["prefixCls","rootClassName","style","className","tabIndex","items","children","direction","id","mode","inlineCollapsed","disabled","disabledOverflow","subMenuOpenDelay","subMenuCloseDelay","forceSubMenuRender","defaultOpenKeys","openKeys","activeKey","defaultActiveFirst","selectable","multiple","defaultSelectedKeys","selectedKeys","onSelect","onDeselect","inlineIndent","motion","defaultMotions","triggerSubMenuAction","builtinPlacements","itemIcon","expandIcon","overflowedIndicator","overflowedIndicatorPopupClassName","getPopupContainer","onClick","onOpenChange","onKeyDown","openAnimation","openTransitionName","_internalRenderMenuItem","_internalRenderSubMenuItem","_internalComponents"],He=[],$e=p.forwardRef(function(e,n){var t,c=e,v=c.prefixCls,g=void 0===v?"rc-menu":v,h=c.rootClassName,b=c.style,C=c.className,E=c.tabIndex,M=void 0===E?0:E,N=c.items,S=c.children,x=c.direction,K=c.id,P=c.mode,O=void 0===P?"vertical":P,D=c.inlineCollapsed,T=c.disabled,L=c.disabledOverflow,_=c.subMenuOpenDelay,z=void 0===_?.1:_,V=c.subMenuCloseDelay,F=void 0===V?.1:V,X=c.forceSubMenuRender,Y=c.defaultOpenKeys,j=c.openKeys,W=c.activeKey,U=c.defaultActiveFirst,$=c.selectable,q=void 0===$||$,te=c.multiple,re=void 0!==te&&te,oe=c.defaultSelectedKeys,ie=c.selectedKeys,ae=c.onSelect,le=c.onDeselect,ue=c.inlineIndent,ce=void 0===ue?24:ue,se=c.motion,fe=c.defaultMotions,me=c.triggerSubMenuAction,ve=void 0===me?"hover":me,pe=c.builtinPlacements,ye=c.itemIcon,Ae=c.expandIcon,ge=c.overflowedIndicator,be=void 0===ge?"...":ge,Ce=c.overflowedIndicatorPopupClassName,Ee=c.getPopupContainer,we=c.onClick,Me=c.onOpenChange,Re=c.onKeyDown,Ne=(c.openAnimation,c.openTransitionName,c._internalRenderMenuItem),Se=c._internalRenderSubMenuItem,xe=c._internalComponents,Ie=(0,u.A)(c,Ge),ke=p.useMemo(function(){return[Ue(S,N,He,xe,g),Ue(S,N,He,{},g)]},[S,N,xe]),Ke=(0,l.A)(ke,2),Pe=Ke[0],Oe=Ke[1],De=p.useState(!1),Te=(0,l.A)(De,2),Le=Te[0],_e=Te[1],Ve=p.useRef(),Fe=function(e){var n=(0,d.A)(e,{value:e}),t=(0,l.A)(n,2),r=t[0],o=t[1];return p.useEffect(function(){ne+=1;var e="".concat(ee,"-").concat(ne);o("rc-menu-uuid-".concat(e))},[]),r}(K),Xe="rtl"===x;var Ye=(0,d.A)(Y,{value:j,postState:function(e){return e||He}}),je=(0,l.A)(Ye,2),We=je[0],Be=je[1],$e=function(e){function n(){Be(e),null==Me||Me(e)}arguments.length>1&&void 0!==arguments[1]&&arguments[1]?(0,y.flushSync)(n):n()},qe=p.useState(We),Qe=(0,l.A)(qe,2),Je=Qe[0],Ze=Qe[1],en=p.useRef(!1),nn=p.useMemo(function(){return"inline"!==O&&"vertical"!==O||!D?[O,!1]:["vertical",D]},[O,D]),tn=(0,l.A)(nn,2),rn=tn[0],on=tn[1],an="inline"===rn,ln=p.useState(rn),un=(0,l.A)(ln,2),cn=un[0],sn=un[1],fn=p.useState(on),dn=(0,l.A)(fn,2),mn=dn[0],vn=dn[1];p.useEffect(function(){sn(rn),vn(on),en.current&&(an?Be(Je):$e(He))},[rn,on]);var pn=p.useState(0),yn=(0,l.A)(pn,2),An=yn[0],gn=yn[1],hn=An>=Pe.length-1||"horizontal"!==cn||L;p.useEffect(function(){an&&Ze(We)},[We]),p.useEffect(function(){return en.current=!0,function(){en.current=!1}},[]);var bn=J(),Cn=bn.registerPath,En=bn.unregisterPath,wn=bn.refreshOverflowKeys,Mn=bn.isSubPathKey,Rn=bn.getKeyPath,Nn=bn.getKeys,Sn=bn.getSubPathKeys,xn=p.useMemo(function(){return{registerPath:Cn,unregisterPath:En}},[Cn,En]),In=p.useMemo(function(){return{isSubPathKey:Mn}},[Mn]);p.useEffect(function(){wn(hn?He:Pe.slice(An+1).map(function(e){return e.key}))},[An,hn]);var kn=(0,d.A)(W||U&&(null===(t=Pe[0])||void 0===t?void 0:t.key),{value:W}),Kn=(0,l.A)(kn,2),Pn=Kn[0],On=Kn[1],Dn=Z(function(e){On(e)}),Tn=Z(function(){On(void 0)});(0,p.useImperativeHandle)(n,function(){return{list:Ve.current,focus:function(e){var n,t,r=Nn(),o=G(r,Fe),i=o.elements,a=o.key2element,l=o.element2key,u=B(Ve.current,i),c=null!=Pn?Pn:u[0]?l.get(u[0]):null===(n=Pe.find(function(e){return!e.props.disabled}))||void 0===n?void 0:n.key,s=a.get(c);c&&s&&(null==s||null===(t=s.focus)||void 0===t||t.call(s,e))}}});var Ln=(0,d.A)(oe||[],{value:ie,postState:function(e){return Array.isArray(e)?e:null==e?He:[e]}}),_n=(0,l.A)(Ln,2),zn=_n[0],Vn=_n[1],Fn=Z(function(e){null==we||we(de(e)),function(e){if(q){var n,t=e.key,r=zn.includes(t);n=re?r?zn.filter(function(e){return e!==t}):[].concat((0,a.A)(zn),[t]):[t],Vn(n);var o=(0,i.A)((0,i.A)({},e),{},{selectedKeys:n});r?null==le||le(o):null==ae||ae(o)}!re&&We.length&&"inline"!==cn&&$e(He)}(e)}),Xn=Z(function(e,n){var t=We.filter(function(n){return n!==e});if(n)t.push(e);else if("inline"!==cn){var r=Sn(e);t=t.filter(function(e){return!r.has(e)})}(0,m.A)(We,t,!0)||$e(t,!0)}),Yn=H(cn,Pn,Xe,Fe,Ve,Nn,Rn,On,function(e,n){var t=null!=n?n:!We.includes(e);Xn(e,t)},Re);p.useEffect(function(){_e(!0)},[]);var jn=p.useMemo(function(){return{_internalRenderMenuItem:Ne,_internalRenderSubMenuItem:Se}},[Ne,Se]),Wn="horizontal"!==cn||L?Pe:Pe.map(function(e,n){return p.createElement(w,{key:e.key,overflowDisabled:n>An},e)}),Bn=p.createElement(f.A,(0,r.A)({id:K,ref:Ve,prefixCls:"".concat(g,"-overflow"),component:"ul",itemComponent:he,className:s()(g,"".concat(g,"-root"),"".concat(g,"-").concat(cn),C,(0,o.A)((0,o.A)({},"".concat(g,"-inline-collapsed"),mn),"".concat(g,"-rtl"),Xe),h),dir:x,style:b,role:"menu",tabIndex:M,data:Wn,renderRawItem:function(e){return e},renderRawRest:function(e){var n=e.length,t=n?Pe.slice(-n):null;return p.createElement(ze,{eventKey:Q,title:be,disabled:hn,internalPopupClose:0===n,popupClassName:Ce},t)},maxCount:"horizontal"!==cn||L?f.A.INVALIDATE:f.A.RESPONSIVE,ssr:"full","data-menu-list":!0,onVisibleChange:function(e){gn(e)},onKeyDown:Yn},Ie));return p.createElement(k.Provider,{value:jn},p.createElement(A.Provider,{value:Fe},p.createElement(w,{prefixCls:g,rootClassName:h,mode:cn,openKeys:We,rtl:Xe,disabled:T,motion:Le?se:null,defaultMotions:Le?fe:null,activeKey:Pn,onActive:Dn,onInactive:Tn,selectedKeys:zn,inlineIndent:ce,subMenuOpenDelay:z,subMenuCloseDelay:F,forceSubMenuRender:X,builtinPlacements:pe,triggerSubMenuAction:ve,getPopupContainer:Ee,itemIcon:ye,expandIcon:Ae,onItemClick:Fn,onOpenChange:Xn},p.createElement(I.Provider,{value:In},Bn),p.createElement("div",{style:{display:"none"},"aria-hidden":!0},p.createElement(R.Provider,{value:xn},Oe)))))}),qe=$e;qe.Item=he,qe.SubMenu=ze,qe.ItemGroup=je,qe.Divider=Fe;var Qe=qe},9591:function(e,n,t){t.d(n,{A:function(){return T}});var r=t(8168),o=t(9379),i=t(5544),a=t(3986),l=t(6540),u=t(6942),c=t.n(u),s=t(8462),f=t(981),d=["prefixCls","invalidate","item","renderItem","responsive","responsiveDisabled","registerSize","itemKey","className","style","children","display","order","component"],m=void 0;function v(e,n){var t=e.prefixCls,i=e.invalidate,u=e.item,f=e.renderItem,v=e.responsive,p=e.responsiveDisabled,y=e.registerSize,A=e.itemKey,g=e.className,h=e.style,b=e.children,C=e.display,E=e.order,w=e.component,M=void 0===w?"div":w,R=(0,a.A)(e,d),N=v&&!C;function S(e){y(A,e)}l.useEffect(function(){return function(){S(null)}},[]);var x,I=f&&u!==m?f(u,{index:E}):b;i||(x={opacity:N?0:1,height:N?0:m,overflowY:N?"hidden":m,order:v?E:m,pointerEvents:N?"none":m,position:N?"absolute":m});var k={};N&&(k["aria-hidden"]=!0);var K=l.createElement(M,(0,r.A)({className:c()(!i&&t,g),style:(0,o.A)((0,o.A)({},x),h)},k,R,{ref:n}),I);return v&&(K=l.createElement(s.A,{onResize:function(e){S(e.offsetWidth)},disabled:p},K)),K}var p=l.forwardRef(v);p.displayName="Item";var y=p,A=t(6956),g=t(961),h=t(5371);function b(){var e=l.useRef(null);return function(n){e.current||(e.current=[],function(e){if("undefined"==typeof MessageChannel)(0,h.A)(e);else{var n=new MessageChannel;n.port1.onmessage=function(){return e()},n.port2.postMessage(void 0)}}(function(){(0,g.unstable_batchedUpdates)(function(){e.current.forEach(function(e){e()}),e.current=null})})),e.current.push(n)}}function C(e,n){var t=l.useState(n),r=(0,i.A)(t,2),o=r[0],a=r[1];return[o,(0,A.A)(function(n){e(function(){a(n)})})]}var E=l.createContext(null),w=["component"],M=["className"],R=["className"],N=function(e,n){var t=l.useContext(E);if(!t){var o=e.component,i=void 0===o?"div":o,u=(0,a.A)(e,w);return l.createElement(i,(0,r.A)({},u,{ref:n}))}var s=t.className,f=(0,a.A)(t,M),d=e.className,m=(0,a.A)(e,R);return l.createElement(E.Provider,{value:null},l.createElement(y,(0,r.A)({ref:n,className:c()(s,d)},f,m)))},S=l.forwardRef(N);S.displayName="RawItem";var x=S,I=["prefixCls","data","renderItem","renderRawItem","itemKey","itemWidth","ssr","style","className","maxCount","renderRest","renderRawRest","suffix","component","itemComponent","onVisibleChange"],k="responsive",K="invalidate";function P(e){return"+ ".concat(e.length," ...")}function O(e,n){var t=e.prefixCls,u=void 0===t?"rc-overflow":t,d=e.data,m=void 0===d?[]:d,v=e.renderItem,p=e.renderRawItem,A=e.itemKey,g=e.itemWidth,h=void 0===g?10:g,w=e.ssr,M=e.style,R=e.className,N=e.maxCount,S=e.renderRest,x=e.renderRawRest,O=e.suffix,D=e.component,T=void 0===D?"div":D,L=e.itemComponent,_=e.onVisibleChange,z=(0,a.A)(e,I),V="full"===w,F=b(),X=C(F,null),Y=(0,i.A)(X,2),j=Y[0],W=Y[1],B=j||0,U=C(F,new Map),G=(0,i.A)(U,2),H=G[0],$=G[1],q=C(F,0),Q=(0,i.A)(q,2),J=Q[0],Z=Q[1],ee=C(F,0),ne=(0,i.A)(ee,2),te=ne[0],re=ne[1],oe=C(F,0),ie=(0,i.A)(oe,2),ae=ie[0],le=ie[1],ue=(0,l.useState)(null),ce=(0,i.A)(ue,2),se=ce[0],fe=ce[1],de=(0,l.useState)(null),me=(0,i.A)(de,2),ve=me[0],pe=me[1],ye=l.useMemo(function(){return null===ve&&V?Number.MAX_SAFE_INTEGER:ve||0},[ve,j]),Ae=(0,l.useState)(!1),ge=(0,i.A)(Ae,2),he=ge[0],be=ge[1],Ce="".concat(u,"-item"),Ee=Math.max(J,te),we=N===k,Me=m.length&&we,Re=N===K,Ne=Me||"number"==typeof N&&m.length>N,Se=(0,l.useMemo)(function(){var e=m;return Me?e=null===j&&V?m:m.slice(0,Math.min(m.length,B/h)):"number"==typeof N&&(e=m.slice(0,N)),e},[m,h,j,N,Me]),xe=(0,l.useMemo)(function(){return Me?m.slice(ye+1):m.slice(Se.length)},[m,Se,Me,ye]),Ie=(0,l.useCallback)(function(e,n){var t;return"function"==typeof A?A(e):null!==(t=A&&(null==e?void 0:e[A]))&&void 0!==t?t:n},[A]),ke=(0,l.useCallback)(v||function(e){return e},[v]);function Ke(e,n,t){(ve!==e||void 0!==n&&n!==se)&&(pe(e),t||(be(e<m.length-1),null==_||_(e)),void 0!==n&&fe(n))}function Pe(e,n){$(function(t){var r=new Map(t);return null===n?r.delete(e):r.set(e,n),r})}function Oe(e){return H.get(Ie(Se[e],e))}(0,f.A)(function(){if(B&&"number"==typeof Ee&&Se){var e=ae,n=Se.length,t=n-1;if(!n)return void Ke(0,null);for(var r=0;r<n;r+=1){var o=Oe(r);if(V&&(o=o||0),void 0===o){Ke(r-1,void 0,!0);break}if(e+=o,0===t&&e<=B||r===t-1&&e+Oe(t)<=B){Ke(t,null);break}if(e+Ee>B){Ke(r-1,e-o-ae+te);break}}O&&Oe(0)+ae>B&&fe(null)}},[B,H,te,ae,Ie,Se]);var De=he&&!!xe.length,Te={};null!==se&&Me&&(Te={position:"absolute",left:se,top:0});var Le={prefixCls:Ce,responsive:Me,component:L,invalidate:Re},_e=p?function(e,n){var t=Ie(e,n);return l.createElement(E.Provider,{key:t,value:(0,o.A)((0,o.A)({},Le),{},{order:n,item:e,itemKey:t,registerSize:Pe,display:n<=ye})},p(e,n))}:function(e,n){var t=Ie(e,n);return l.createElement(y,(0,r.A)({},Le,{order:n,key:t,item:e,renderItem:ke,itemKey:t,registerSize:Pe,display:n<=ye}))},ze={order:De?ye:Number.MAX_SAFE_INTEGER,className:"".concat(Ce,"-rest"),registerSize:function(e,n){re(n),Z(te)},display:De},Ve=S||P,Fe=x?l.createElement(E.Provider,{value:(0,o.A)((0,o.A)({},Le),ze)},x(xe)):l.createElement(y,(0,r.A)({},Le,ze),"function"==typeof Ve?Ve(xe):Ve),Xe=l.createElement(T,(0,r.A)({className:c()(!Re&&u,R),style:M,ref:n},z),Se.map(_e),Ne?Fe:null,O&&l.createElement(y,(0,r.A)({},Le,{responsive:we,responsiveDisabled:!Me,order:ye,className:"".concat(Ce,"-suffix"),registerSize:function(e,n){le(n)},display:!0,style:Te}),O));return we?l.createElement(s.A,{onResize:function(e,n){W(n.clientWidth)},disabled:!Me},Xe):Xe}var D=l.forwardRef(O);D.displayName="Overflow",D.Item=x,D.RESPONSIVE=k,D.INVALIDATE=K;var T=D}}]);
//# sourceMappingURL=0ba2ce643a859e05bf62bf3735bcfd01c435cab2-31e36603ea00f05b1d5b.js.map