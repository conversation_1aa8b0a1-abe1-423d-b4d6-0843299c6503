import os
from typing import Any, Dict, List, Literal, Optional, Union

from pydantic import BaseModel, Field, field_validator


class AuthConfig(BaseModel):
    """Authentication configuration model for the application."""

    type: Literal["none", "jwt"] = "none"
    jwt_secret: Optional[str] = None
    token_expiry_minutes: int = 60
    exclude_paths: List[str] = [
        "/",  # root for serving frontend
        "/api/health",
        "/api/version",
        "/api/auth/type",
    ]

    @field_validator("jwt_secret")
    @classmethod
    def validate_jwt_secret(cls, v, info):
        """Validate JWT secret is present for JWT auth type."""
        values = info.data
        if values.get("type") == "jwt" and not v:
            raise ValueError("JWT secret is required for JWT authentication")
        return v


class User(BaseModel):
    """User model for authenticated users."""

    id: str
    name: str
    email: Optional[str] = None
    avatar_url: Optional[str] = None
    provider: Optional[str] = None
    roles: List[str] = ["user"]
    metadata: Optional[Dict[str, Any]] = None
