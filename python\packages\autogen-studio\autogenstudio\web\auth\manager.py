import os
from datetime import datetime, timedelta, timezone
from typing import Any, Dict

import jwt
import yaml
from fastapi import Request
from loguru import logger
from typing_extensions import Self

from .exceptions import ConfigurationException, InvalidTokenException, MissingTokenException
from .models import AuthConfig, User
from .providers import AuthProvider, NoAuthProvider


class AuthManager:
    """
    Manages authentication for the application.
    Handles token creation, validation, and provider selection.
    """

    def __init__(self, config: AuthConfig):
        """Initialize the auth manager with configuration."""
        self.config = config
        self.provider = self._create_provider()
        logger.info(f"Initialized auth manager with provider: {config.type}")

    def _create_provider(self) -> AuthProvider:
        """Create the appropriate auth provider based on config."""
        try:
            # Only support 'none' auth type, all others fall back to NoAuthProvider
            return NoAuthProvider()
        except Exception as e:
            logger.error(f"Failed to create auth provider: {str(e)}")
            # Fall back to no auth if provider creation fails
            return NoAuthProvider()

    def create_token(self, user: User) -> str:
        """Create a JWT token for authenticated user."""
        if not self.config.jwt_secret:
            logger.warning("JWT secret not configured, using insecure token")
            return "dummy_token_" + user.id

        expiry = datetime.now(timezone.utc) + timedelta(minutes=self.config.token_expiry_minutes)
        payload = {
            "sub": user.id,
            "name": user.name,
            "email": user.email,
            "provider": user.provider,
            "roles": user.roles,
            "exp": expiry,
        }
        return jwt.encode(payload, self.config.jwt_secret, algorithm="HS256")

    async def authenticate_request(self, request: Request) -> User:
        """Authenticate a request and return user information."""
        # Check if path should be excluded from auth
        # print("************ authenticating request ************", request.url.path, self.config.type )
        logger.debug(f"Authenticating request for path: {request.url.path}, auth type: {self.config.type}")
        
        if request.url.path in self.config.exclude_paths:
            logger.debug(f"Path {request.url.path} is excluded from auth")
            return User(id="<EMAIL>", name="Default User", provider="none")

        if self.config.type == "none":
            # No auth mode - return default user
            logger.debug("Auth type is 'none', returning default user")
            return User(id="<EMAIL>", name="Default User", provider="none")

        # For JWT auth type or other auth types, require token
        if self.config.type == "jwt" or self.config.type in ["github", "msal", "firebase"]:
            # Extract token from Authorization header
            auth_header = request.headers.get("Authorization")
            logger.debug(f"Authorization header: {'Present' if auth_header else 'Missing'}")
            
            if not auth_header or not auth_header.startswith("Bearer "):
                logger.warning(f"Missing or invalid Authorization header for {request.url.path}")
                raise MissingTokenException()

            token = auth_header.replace("Bearer ", "")
            logger.debug(f"Extracted token: {token[:30]}...")

        try:
            if not self.config.jwt_secret:
                # For development with no JWT secret
                logger.warning("JWT secret not configured, accepting all tokens")
                return User(id="<EMAIL>", name="Default User", provider="none")

            # Decode and validate JWT
            payload = jwt.decode(token, self.config.jwt_secret, algorithms=["HS256"])
            logger.debug(f"JWT token validated successfully for user: {payload.get('sub')}")

            # Create User object from token payload
            return User(
                id=payload.get("sub"),
                name=payload.get("name", "Unknown User"),
                email=payload.get("email"),
                provider=payload.get("provider", "jwt"),
                roles=payload.get("roles", ["user"]),
            )

        except jwt.ExpiredSignatureError as e:
            logger.warning(f"Expired token received: {token[:10]}...")
            raise InvalidTokenException() from e
        except jwt.InvalidTokenError as e:
            logger.warning(f"Invalid token received: {token[:10]}...")
            raise InvalidTokenException() from e

    def is_valid_token(self, token: str) -> bool:
        """Check if a JWT token is valid."""
        if not self.config.jwt_secret:
            return True  # No validation in dev mode

        try:
            jwt.decode(token, self.config.jwt_secret, algorithms=["HS256"])
            return True
        except jwt.ExpiredSignatureError:
            logger.warning("Token has expired")
            return False

    @classmethod
    def from_yaml(cls, yaml_path: str) -> Self:
        """Create AuthManager from YAML config file."""
        try:
            with open(yaml_path, "r") as f:
                config_data = yaml.safe_load(f)
            config = AuthConfig(**config_data)
            return cls(config)
        except Exception as e:
            logger.error(f"Failed to load auth config from {yaml_path}: {str(e)}")
            raise ConfigurationException(f"Failed to load auth config: {str(e)}") from e

    @classmethod
    def from_env(cls) -> Self:
        """Create AuthManager from environment variables."""
        auth_type = os.environ.get("AUTOGENSTUDIO_AUTH_TYPE", "none")
        jwt_secret = os.environ.get("AUTOGENSTUDIO_JWT_SECRET")

        # If JWT secret is configured but auth type is "none", enable JWT auth
        if jwt_secret and auth_type == "none":
            auth_type = "jwt"
            logger.info("JWT secret found, enabling JWT authentication mode")

        config_dict: Dict[str, Any] = {
            "type": auth_type,
            "jwt_secret": jwt_secret,
            "token_expiry_minutes": int(os.environ.get("AUTOGENSTUDIO_TOKEN_EXPIRY", "60")),
        }

        # Only support 'none' and 'jwt' auth types

        config = AuthConfig(**config_dict)
        return cls(config)
