/*! For license information please see component---src-pages-mcp-tsx-7f442b7a1612826fd54d.js.LICENSE.txt */
"use strict";(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[621],{684:function(e,t,a){a.d(t,{A:function(){return n}});const n=(0,a(1788).A)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},5977:function(e,t,a){a.d(t,{A:function(){return n}});const n=(0,a(1788).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},6156:function(e,t,a){var n=a(6540);function r({title:e,titleId:t,...a},r){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},a),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))}const l=n.forwardRef(r);t.A=l},8226:function(e,t,a){a.r(t),a.d(t,{default:function(){return P}});var n=a(6540),r=a(1155),l=a(9036),c=a(7677),s=a(2744),o=a(9314),i=a(367),d=a(2941),m=a(9910),u=a(9644),f=a(5977),p=a(7133),h=a(684),v=a(7213),g=a(9709),y=a(7163),x=a(6647);const{Option:E}=o.A;var w=e=>{let{isOpen:t,onToggle:a,onSelectWorkbench:r,isLoading:l=!1,currentWorkbench:c,onGalleryUpdate:w}=e;const{0:b,1:N}=(0,n.useState)([]),{0:k,1:S}=(0,n.useState)(null),{0:C,1:P}=(0,n.useState)([]),{0:M,1:A}=(0,n.useState)(-1),{0:I,1:L}=(0,n.useState)(!1),{user:W}=(0,n.useContext)(s.v),j=(0,n.useCallback)(e=>{S(e),e.id&&localStorage.setItem("mcp-view-gallery",e.id.toString());const t=g.B0.extractMcpWorkbenches(e);P(t),A(-1)},[]),U=(0,n.useCallback)(async()=>{if(null!=W&&W.id)try{L(!0);const e=await g.B0.listGalleries(W.id);N(e);const t=new URLSearchParams(window.location.search).get("galleryId"),a=localStorage.getItem("mcp-view-gallery");let n=e[0];if(t){const a=e.find(e=>{var a;return(null===(a=e.id)||void 0===a?void 0:a.toString())===t});a&&(n=a)}else if(a){const t=e.find(e=>{var t;return(null===(t=e.id)||void 0===t?void 0:t.toString())===a});t&&(n=t)}if(n){S(n),n.id&&localStorage.setItem("mcp-view-gallery",n.id.toString());const e=g.B0.extractMcpWorkbenches(n);P(e)}}catch(e){console.error("Failed to load galleries:",e)}finally{L(!1)}},[null==W?void 0:W.id]);return(0,n.useEffect)(()=>{U()},[U]),(0,n.useEffect)(()=>{if(C.length>0&&k&&!I){var e;const t=new URLSearchParams(window.location.search),a=t.get("galleryId"),n=t.get("workbenchIndex");let l=0;if(a&&n&&(null===(e=k.id)||void 0===e?void 0:e.toString())===a){const e=parseInt(n);e>=0&&e<C.length&&(l=e)}M!==l&&(A(l),r(C[l],k.id,l))}else 0!==C.length||-1===M||I||(A(-1),r(null))},[C,k,r,I]),t?n.createElement("div",{className:"h-full border-r border-secondary bg-primary"},n.createElement("div",{className:"flex items-center justify-between pt-0 p-4 pl-2 pr-2 border-b border-secondary"},n.createElement("div",{className:"flex sticky items-center gap-2"},n.createElement("span",{className:"text-primary font-medium"},"MCP Playground"),n.createElement("span",{className:"px-2 py-0.5 text-xs bg-accent/10 text-accent rounded"},C.length)),n.createElement(i.A,{title:"Close Sidebar"},n.createElement("button",{onClick:a,className:"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50"},n.createElement(u.A,{strokeWidth:1.5,className:"h-6 w-6"})))),n.createElement("div",{className:"p-4 pl-2 border-b border-secondary"},n.createElement("div",{className:"flex items-center justify-between mb-2"},n.createElement("span",{className:"text-sm text-secondary"},"Gallery"),n.createElement(i.A,{title:"Refresh galleries"},n.createElement(d.Ay,{size:"small",icon:I?n.createElement(f.A,{className:"w-3 h-3 animate-spin"}):n.createElement(f.A,{className:"w-3 h-3"}),className:"border-0 hover:bg-secondary",onClick:U,disabled:I}))),n.createElement(o.A,{className:"w-full",placeholder:"Select a gallery",value:null==k?void 0:k.id,onChange:e=>{const t=b.find(t=>t.id===e);t&&j(t)},loading:I},b.map(e=>n.createElement(E,{key:e.id,value:e.id},n.createElement("div",{className:"flex items-center gap-2"},n.createElement(p.A,{className:"w-3 h-3"}),n.createElement("span",null,e.config.name),e.config.url&&n.createElement(h.A,{className:"w-3 h-3 text-secondary"})))))),n.createElement("div",{className:"py-2 flex text-sm text-secondary px-4"},n.createElement("div",{className:"flex"},"MCP Workbenches",l&&n.createElement(f.A,{className:"w-4 h-4 ml-2 animate-spin"}))),!k&&n.createElement("div",{className:"p-2 mr-2 text-center text-secondary text-sm border border-dashed rounded mx-4"},n.createElement(v.A,{className:"w-4 h-4 inline-block mr-1.5 -mt-0.5"}),"Select a gallery to view MCP workbenches"),k&&0===C.length&&n.createElement("div",{className:"p-2 mr-2 text-center text-secondary text-sm border border-dashed rounded ml-2"},n.createElement(v.A,{className:"w-4 h-4 inline-block mr-1.5 -mt-0.5"}),"No MCP workbenches found in this gallery"),n.createElement("div",{className:"scroll overflow-y-auto h-[calc(100%-230px)]"},C.map((e,t)=>{var a,l;const c=((e,t)=>{const a=JSON.stringify(e.config);return`${e.provider}-${e.label||"unnamed"}-${a.slice(0,20)}-${t}`})(e,t),s=t===M;return n.createElement("div",{key:c,className:"relative border-secondary"},n.createElement("div",{className:"absolute top-1 left-0.5 z-50 h-[calc(100%-8px)] w-1 bg-opacity-80 rounded "+(s?"bg-accent":"bg-tertiary")}),n.createElement("div",{className:"group ml-1 flex flex-col p-3 rounded-l cursor-pointer hover:bg-secondary "+(s?"border-accent bg-secondary":"border-transparent"),onClick:()=>{A(t),r(e,null==k?void 0:k.id,t)}},n.createElement("div",{className:"flex items-center justify-between min-w-0"},n.createElement("div",{className:"flex items-center gap-2 min-w-0 flex-1"},n.createElement(x.A,{icon:"mcp",size:4,className:"w-4 h-4 text-accent flex-shrink-0"}),n.createElement("div",{className:"truncate flex-1 text-sm"},n.createElement("span",{className:"font-medium"},e.label)))),n.createElement("div",{className:"mt-1 text-sm text-secondary"},n.createElement("div",{className:"flex items-center gap-2"},n.createElement("span",{className:"truncate text-xs"},(null===(a=e.config.server_params)||void 0===a||null===(l=a.type)||void 0===l?void 0:l.replace("ServerParams",""))||"Unknown Type")))))})),k&&n.createElement("div",{className:"p-3 border-t border-secondary text-sm text-secondary"},n.createElement("div",{className:"flex items-center justify-between"},n.createElement("span",{className:"truncate flex-1"},k.config.name),n.createElement("span",{className:"ml-2 flex-shrink-0"},C.length," MCP workbench",1!==C.length?"es":"")),k.updated_at&&n.createElement("div",{className:"text-xs text-tertiary mt-1"},"Updated ",(0,y.vq)(k.updated_at)))):n.createElement("div",{className:"h-full border-r border-secondary"},n.createElement("div",{className:"p-2 -ml-2"},n.createElement(i.A,{title:`MCP Workbenches (${C.length})`},n.createElement("button",{onClick:a,className:"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50"},n.createElement(m.A,{strokeWidth:1.5,className:"h-6 w-6"})))))},b=a(8107);var N=e=>{var t;let{workbench:a,onTestConnection:r}=e;const l=a.config.server_params;null==l||null===(t=l.type)||void 0===t||t.replace("ServerParams","");return n.createElement("div",{className:"  "},n.createElement(b.dv,{component:a,defaultPanelKey:["testing"],readonly:!0,onChange:()=>{}}))},k=a(2571),S=a(6156);var C=()=>{const{0:e,1:t}=(0,n.useState)(!1),{0:a,1:r}=(0,n.useState)(null),{0:o,1:i}=(0,n.useState)(()=>{if("undefined"!=typeof window){const e=localStorage.getItem("mcpSidebar");return null===e||JSON.parse(e)}return!0}),{user:d}=(0,n.useContext)(s.v),[m,u]=l.Ay.useMessage();(0,n.useEffect)(()=>{"undefined"!=typeof window&&localStorage.setItem("mcpSidebar",JSON.stringify(o))},[o]),(0,n.useEffect)(()=>{const e=new URLSearchParams(window.location.search);e.get("galleryId"),e.get("workbenchIndex")},[a]),(0,n.useEffect)(()=>{const e=()=>{const e=new URLSearchParams(window.location.search),t=e.get("galleryId"),n=e.get("workbenchIndex");t&&n||a&&r(null)};return window.addEventListener("popstate",e),()=>window.removeEventListener("popstate",e)},[a]);return null!=d&&d.id?n.createElement("div",{className:"relative flex h-full w-full"},u,n.createElement("div",{className:"absolute left-0 top-0 h-full transition-all duration-200 ease-in-out "+(o?"w-64":"w-12")},n.createElement(w,{isOpen:o,onToggle:()=>i(!o),onSelectWorkbench:(e,t,a)=>{if(r(e),e&&void 0!==t&&void 0!==a){const e=new URLSearchParams(window.location.search);e.set("galleryId",t.toString()),e.set("workbenchIndex",a.toString()),window.history.pushState({},"",`?${e.toString()}`)}else e||window.history.pushState({},"",window.location.pathname)},isLoading:e,currentWorkbench:a,onGalleryUpdate:async e=>{if(null!=d&&d.id&&e.id)try{t(!0);const a={...e,created_at:void 0,updated_at:void 0};await k.f.updateGallery(e.id,a,d.id),m.success("Gallery updated successfully")}catch(a){console.error("Failed to update gallery:",a),m.error("Failed to update gallery")}finally{t(!1)}}})),n.createElement("div",{className:"flex-1 transition-all -mr-6 duration-200 "+(o?"ml-64":"ml-12")},n.createElement("div",{className:"p-4 pt-2  h-[calc(100%-60px)]"},n.createElement("div",{className:"text-xs text-secondary mb-4 border border-dashed rounded-md p-2 "},n.createElement(S.A,{className:"w-4 h-4 inline-block mr-1 text-warning text-orange-500"})," ","MCP Playground is an experimental view for testing MCP Servers in your Gallery"," "),n.createElement("div",{className:"flex items-center gap-2 mb-4 text-sm"},n.createElement("span",{className:"text-primary font-medium"},"MCP Playground"),a&&n.createElement(n.Fragment,null,n.createElement(c.A,{className:"w-4 h-4 text-secondary"}),n.createElement("span",{className:"text-secondary"},a.label))),e&&!a?n.createElement("div",{className:"flex items-center justify-center h-[calc(100vh-120px)] text-secondary"},"Loading..."):a?n.createElement("div",{className:" h-[calc(100vh-235px)]    scroll overflow-auto"}," ",n.createElement(N,{workbench:a,onTestConnection:()=>(async e=>{try{t(!0),await g.B0.testMcpConnection(e)?m.success("Connection test successful"):m.error("Connection test failed")}catch(a){console.error("Connection test failed:",a),m.error("Connection test failed")}finally{t(!1)}})(a)})):n.createElement("div",{className:"flex items-center justify-center h-[calc(100vh-120px)] text-secondary"},n.createElement("div",{className:"text-center"},n.createElement("h3",{className:"text-lg font-medium mb-2"},"Welcome to MCP Playground"),n.createElement("p",{className:"text-secondary mb-4"},"Select an MCP workbench from the sidebar to start testing")))))):n.createElement("div",{className:"flex items-center justify-center h-[calc(100vh-120px)] text-secondary"},"Please log in to use the MCP Playground")};var P=e=>{let{data:t}=e;return n.createElement(r.A,{meta:t.site.siteMetadata,title:"MCP 游乐场",link:"/mcp"},n.createElement("main",{style:{height:"100%"},className:"h-full"},n.createElement(C,null)))}}}]);
//# sourceMappingURL=component---src-pages-mcp-tsx-7f442b7a1612826fd54d.js.map