from fastapi import APIRouter, Depends, HTTPException, Request
from loguru import logger

from .manager import AuthManager
from .models import User

router = APIRouter()


def get_auth_manager(request: Request) -> AuthManager:
    """Get the auth manager from app state."""
    if not hasattr(request.app.state, "auth_manager"):
        raise HTTPException(status_code=500, detail="Authentication system not initialized")
    return request.app.state.auth_manager


def get_current_user(request: Request) -> User:
    """Get the current authenticated user."""
    if hasattr(request.state, "user"):
        return request.state.user

    # This shouldn't normally happen as middleware should set user
    logger.warning("User not found in request state")
    return User(id="anonymous", name="Anonymous User")





@router.get("/me")
async def get_user_info(current_user: User = Depends(get_current_user)):
    """Get information about the currently authenticated user."""
    return {
        "id": current_user.id,
        "name": current_user.name,
        "email": current_user.email,
        "provider": current_user.provider,
        "roles": current_user.roles,
    }


@router.get("/type")
async def get_auth_type(auth_manager: AuthManager = Depends(get_auth_manager)):
    """Get the configured authentication type."""
    return {"type": auth_manager.config.type, "exclude_paths": auth_manager.config.exclude_paths}
