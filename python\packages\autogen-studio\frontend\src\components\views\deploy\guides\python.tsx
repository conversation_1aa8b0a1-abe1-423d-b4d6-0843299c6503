import React from "react";
import { Alert } from "antd";
import { CodeSection, copyToClipboard } from "./guides";
import { Download } from "lucide-react";

const PythonGuide: React.FC = () => {
  return (
    <div className="">
      <h1 className="tdext-2xl font-bold mb-6">
        在 Python 代码和 REST API 中使用 多智能体工作室 团队
      </h1>

      <Alert
        className="mb-6"
        message="前置条件"
        description={
          <ul className="list-disc pl-4 mt-2 space-y-1">
            <li>已安装 多智能体工作室</li>
          </ul>
        }
        type="info"
      />

      <div className="my-3 text-sm">
        {" "}
        您可以通过使用 TeamManager 类在您的 Python 应用程序中重用在 多智能体工作室 中创建的代理团队的声明性规范。在团队构建器中，选择一个团队配置并点击下载。{" "}
        <Download className="h-4 w-4 inline-block" />{" "}
      </div>

      {/* Basic Usage */}
      <CodeSection
        title="1. 在 Python 中构建您的团队，导出为 JSON"
        description="这是一个在 Python 中构建代理团队并将其导出为 JSON 文件的示例。"
        code={`
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.teams import RoundRobinGroupChat
from autogen_agentchat.ui import Console
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_agentchat.conditions import  TextMentionTermination
 
agent = AssistantAgent(
        name="weather_agent",
        model_client=OpenAIChatCompletionClient(
            model="gpt-4o-mini", 
        ), 
    ) 
agent_team = RoundRobinGroupChat([agent], termination_condition=TextMentionTermination("TERMINATE"))
config = agent_team.dump_component()
print(config.model_dump_json())`}
        onCopy={copyToClipboard}
      />

      {/* Installation Steps */}
      <div className="space-y-6">
        {/* Basic Usage */}
        <CodeSection
          title="2. 在 Python 中运行团队"
          description="这是在您的 Python 代码中使用 多智能体工作室 的 TeamManager 类的简单示例。"
          code={`
from autogenstudio.teammanager import TeamManager

# Initialize the TeamManager
manager = TeamManager()

# Run a task with a specific team configuration
result = await manager.run(
task="What is the weather in New York?",
team_config="team.json"
)
print(result)`}
          onCopy={copyToClipboard}
        />

        <CodeSection
          title="3. 将团队作为 REST API 提供服务"
          description=<div>
            多智能体工作室 提供了一个便捷的 CLI 命令，可以将团队作为 REST API 端点提供服务。{" "}
          </div>
          code={`
autogenstudio serve --team path/to/team.json --port 8084  
          `}
          onCopy={copyToClipboard}
        />
      </div>
    </div>
  );
};

export default PythonGuide;
