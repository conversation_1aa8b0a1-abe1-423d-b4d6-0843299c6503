# api/app.py
import os
from contextlib import asynccontextmanager
from typing import AsyncGenerator
os.environ["OPENAI_API_KEY"] = "sk-aOnHdMDqN5SXgxwAC50298EbCcA54345B906AfC7F5D7B384"
#os.environ["OPENAI_BASE_URL"] = "https://gnomic.nengyongai.cn/v1"
os.environ["OPENAI_BASE_URL"] = "https://127.0.0.1:11434/v1"
os.environ["AUTOGENSTUDIO_JWT_SECRET"] = "xingchen-chatbot-secret-key-2025"
os.environ["AUTOGENSTUDIO_TOKEN_EXPIRY"] = "6000000000"
# import logging
from fastapi import FastAPI, Request, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse, HTMLResponse, Response
from loguru import logger
from pydantic import BaseModel

from ..version import VERSION
from .auth import authroutes
from .auth.middleware import AuthMiddleware
from .auth.manager import AuthManager
from .auth.models import User
from .auth.exceptions import InvalidTokenException
from .config import settings
from .deps import cleanup_managers, init_auth_manager, init_managers, register_auth_dependencies
from .initialization import AppInitializer
from .routes import gallery, mcp, runs, sessions, settingsroute, teams, validation, ws


# Token request model
class TokenRequest(BaseModel):
    token: str

# Initialize application
app_file_path = os.path.dirname(os.path.abspath(__file__))
initializer = AppInitializer(settings, app_file_path)


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """
    Lifecycle manager for the FastAPI application.
    Handles initialization and cleanup of application resources.
    """

    try:
        # Initialize managers (DB, Connection, Team)
        await init_managers(initializer.database_uri, initializer.config_dir, initializer.app_root)

        await register_auth_dependencies(app, auth_manager)

        # Any other initialization code
        logger.info(
            f"Application startup complete. Navigate to http://{os.environ.get('AUTOGENSTUDIO_HOST', '127.0.0.1')}:{os.environ.get('AUTOGENSTUDIO_PORT', '8081')}"
        )

    except Exception as e:
        logger.error(f"Failed to initialize application: {str(e)}")
        raise

    yield  # Application runs here

    # Shutdown
    try:
        logger.info("Cleaning up application resources...")
        await cleanup_managers()
        logger.info("Application shutdown complete")
    except Exception as e:
        logger.error(f"Error during shutdown: {str(e)}")


auth_manager = init_auth_manager(initializer.config_dir)
# Create FastAPI application
app = FastAPI(lifespan=lifespan, debug=True)

# CORS middleware configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for cross-domain requests
    allow_credentials=False,  # Set to False when using allow_origins=["*"]
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)
app.add_middleware(AuthMiddleware, auth_manager=auth_manager)

# Create API router with version and documentation
api = FastAPI(
    root_path="/api",
    title="AutoGen Studio API",
    version=VERSION,
    description="AutoGen Studio is a low-code tool for building and testing multi-agent workflows.",
    docs_url="/docs" if settings.API_DOCS else None,
)

# Include all routers with their prefixes
api.include_router(
    sessions.router,
    prefix="/sessions",
    tags=["sessions"],
    responses={404: {"description": "Not found"}},
)

api.include_router(
    runs.router,
    prefix="/runs",
    tags=["runs"],
    responses={404: {"description": "Not found"}},
)

api.include_router(
    teams.router,
    prefix="/teams",
    tags=["teams"],
    responses={404: {"description": "Not found"}},
)


api.include_router(
    ws.router,
    prefix="/ws",
    tags=["websocket"],
    responses={404: {"description": "Not found"}},
)

api.include_router(
    validation.router,
    prefix="/validate",
    tags=["validation"],
    responses={404: {"description": "Not found"}},
)

api.include_router(
    settingsroute.router,
    prefix="/settings",
    tags=["settings"],
    responses={404: {"description": "Not found"}},
)

api.include_router(
    gallery.router,
    prefix="/gallery",
    tags=["gallery"],
    responses={404: {"description": "Not found"}},
)
# Include authentication routes
api.include_router(
    authroutes.router,
    prefix="/auth",
    tags=["auth"],
    responses={404: {"description": "Not found"}},
)

# api.include_router(
#     maker.router,
#     prefix="/maker",
#     tags=["maker"],
#     responses={404: {"description": "Not found"}},
# )

api.include_router(
    mcp.router,
    prefix="/mcp",
    tags=["mcp"],
    responses={404: {"description": "Not found"}},
)

# Version endpoint


@api.get("/version")
async def get_version():
    """Get API version"""
    return {
        "status": True,
        "message": "Version retrieved successfully",
        "data": {"version": VERSION},
    }


# Health check endpoint


@api.get("/health")
async def health_check():
    """API health check endpoint"""
    return {
        "status": True,
        "message": "Service is healthy",
    }


# Helper function to get auth manager
def get_auth_manager() -> AuthManager:
    """Get the auth manager instance."""
    return auth_manager


# Root POST endpoint for token authentication
@app.post("/")
async def root_token_login(request: Request, auth_mgr: AuthManager = Depends(get_auth_manager)):
    """
    Handle POST requests to root with token parameter for authentication.
    Returns an HTML page that stores the token and redirects to the main application.
    """
    try:
        # Extract token from different request formats
        token = None
        content_type = request.headers.get("content-type", "")

        if "application/json" in content_type:
            # JSON request (API calls)
            try:
                body = await request.json()
                token = body.get("token", "").strip()
            except Exception as e:
                logger.error(f"Failed to parse JSON body: {e}")
        elif "application/x-www-form-urlencoded" in content_type:
            # Form data (HTML form submissions)
            try:
                form_data = await request.form()
                token = form_data.get("token", "").strip()
            except Exception as e:
                logger.error(f"Failed to parse form data: {e}")
        else:
            # Try both formats as fallback
            try:
                body = await request.json()
                token = body.get("token", "").strip()
            except:
                try:
                    form_data = await request.form()
                    token = form_data.get("token", "").strip()
                except Exception as e:
                    logger.error(f"Failed to parse request body: {e}")

        if not token:
            logger.error("Empty token provided in POST request to root")
            return JSONResponse(
                status_code=400,
                content={
                    "status": False,
                    "message": "Token parameter is required",
                    "error": "Empty token provided"
                }
            )

        # Check if request accepts HTML (from browser) or JSON (from API)
        accept_header = request.headers.get("accept", "")
        wants_html = "text/html" in accept_header

        # Validate the token
        if not auth_mgr.config.jwt_secret:
            # Development mode - accept any token
            logger.warning("JWT secret not configured, accepting token in development mode")
            user = User(id="<EMAIL>", name="Default User", provider="none")

            if wants_html:
                # Always return main app directly (skip intermediate page)
                logger.info(f"Returning main app with auth state for user: {user.id} (development mode)")
                main_app_html = _get_main_app_html_with_auth(token, user)
                return HTMLResponse(content=main_app_html, status_code=200)
            else:
                return JSONResponse(
                    status_code=200,
                    content={
                        "status": True,
                        "message": "Token login successful (development mode)",
                        "token": token,
                        "user": {
                            "id": user.id,
                            "name": user.name,
                            "email": user.email,
                            "provider": user.provider
                        },
                        "redirect_url": "/"
                    }
                )

        # Validate JWT token
        try:
            import jwt
            payload = jwt.decode(token, auth_mgr.config.jwt_secret, algorithms=["HS256"])

            # Create User object from token payload
            user = User(
                id=payload.get("sub"),
                name=payload.get("name", "Unknown User"),
                email=payload.get("email"),
                provider=payload.get("provider", "jwt"),
                roles=payload.get("roles", ["user"]),
            )

            logger.info(f"Successful token login for user: {user.id}")

            if wants_html:
                # Always return main app directly (skip intermediate page)
                logger.info(f"Returning main app with auth state for user: {user.id}")
                main_app_html = _get_main_app_html_with_auth(token, user)
                return HTMLResponse(content=main_app_html, status_code=200)
            else:
                return JSONResponse(
                    status_code=200,
                    content={
                        "status": True,
                        "message": "Token login successful",
                        "token": token,
                        "user": {
                            "id": user.id,
                            "name": user.name,
                            "email": user.email,
                            "provider": user.provider,
                            "roles": user.roles
                        },
                        "redirect_url": "/"
                    }
                )

        except jwt.ExpiredSignatureError:
            logger.error("Expired token provided in POST request to root")
            return JSONResponse(
                status_code=401,
                content={
                    "status": False,
                    "message": "Token has expired",
                    "error": "Token authentication failed"
                }
            )
        except jwt.InvalidTokenError:
            logger.error("Invalid token provided in POST request to root")
            return JSONResponse(
                status_code=401,
                content={
                    "status": False,
                    "message": "Invalid token provided",
                    "error": "Token authentication failed"
                }
            )

    except Exception as e:
        logger.error(f"Unexpected error in root token login: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
                "status": False,
                "message": "Internal server error during token authentication",
                "error": str(e)
            }
        )


def _get_main_app_html_with_auth(token: str, user: User) -> str:
    """Get the main application HTML content and inject authentication state."""
    try:
        # Get the path to the main index.html file
        ui_root = initializer.ui_root
        index_path = ui_root / "index.html"

        if not index_path.exists():
            logger.error(f"Main app HTML file not found at: {index_path}")
            return _create_fallback_html(token, user)

        # Read the main HTML content
        with open(index_path, 'r', encoding='utf-8') as f:
            html_content = f.read()

        # Inject authentication state into the HTML
        # Escape user data to prevent XSS
        import json
        user_data = {
            "id": user.id,
            "name": user.name,
            "email": user.email or "",
            "provider": user.provider,
            "roles": user.roles if hasattr(user, 'roles') and user.roles else ["user"]
        }
        user_json = json.dumps(user_data)

        auth_script = f"""
        <script>
            // Inject authentication state for token login
            console.log('🔄 Injecting authentication state from POST token login');

            // Store token in localStorage (this is what the frontend expects)
            localStorage.setItem('auth_token', '{token}');

            // Store user information in a way the frontend can access
            window.__AUTOGEN_AUTH_STATE__ = {{
                token: '{token}',
                user: {user_json},
                isAuthenticated: true,
                loginMethod: 'token_post',
                injectedAt: new Date().toISOString()
            }};

            // Also trigger a custom event that the frontend can listen to
            window.dispatchEvent(new CustomEvent('autogen-auth-injected', {{
                detail: window.__AUTOGEN_AUTH_STATE__
            }}));

            console.log('✅ Authentication state injected:', window.__AUTOGEN_AUTH_STATE__);
            console.log('✅ Token stored in localStorage with key: auth_token');
        </script>
        """

        # Insert the auth script before the closing </head> tag
        if '</head>' in html_content:
            html_content = html_content.replace('</head>', f'{auth_script}</head>')
        else:
            # Fallback: insert at the beginning of the body
            if '<body>' in html_content:
                html_content = html_content.replace('<body>', f'<body>{auth_script}')
            else:
                # Last resort: append to the end
                html_content += auth_script

        logger.info(f"Successfully injected auth state for user: {user.id}")
        return html_content

    except Exception as e:
        logger.error(f"Error getting main app HTML: {str(e)}")
        return _create_fallback_html(token, user)


def _create_fallback_html(token: str, user: User) -> str:
    """Create a fallback HTML page when main app HTML is not available."""
    return f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>多智能体工作室 - 已登录</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
            body {{
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                margin: 0;
                padding: 0;
                min-height: 100vh;
                display: flex;
                justify-content: center;
                align-items: center;
            }}
            .container {{
                background: white;
                padding: 40px;
                border-radius: 12px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                width: 100%;
                max-width: 500px;
                text-align: center;
            }}
            .logo {{
                font-size: 28px;
                font-weight: bold;
                color: #333;
                margin-bottom: 10px;
            }}
            .success {{
                color: #28a745;
                font-size: 20px;
                margin-bottom: 20px;
            }}
            .user-info {{
                background: #f8f9fa;
                padding: 20px;
                border-radius: 8px;
                margin: 20px 0;
                text-align: left;
            }}
            .user-info h3 {{
                margin-top: 0;
                color: #333;
            }}
            .user-detail {{
                margin: 8px 0;
                color: #666;
            }}
            .main-app-btn {{
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 12px 24px;
                border: none;
                border-radius: 6px;
                font-size: 16px;
                font-weight: 500;
                cursor: pointer;
                text-decoration: none;
                display: inline-block;
                margin-top: 20px;
                transition: transform 0.2s;
            }}
            .main-app-btn:hover {{
                transform: translateY(-1px);
            }}
            .note {{
                margin-top: 20px;
                font-size: 14px;
                color: #666;
                line-height: 1.4;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="logo">🤖 多智能体工作室</div>
            <div class="success">✅ 登录成功！</div>

            <div class="user-info">
                <h3>用户信息</h3>
                <div class="user-detail"><strong>姓名：</strong>{user.name}</div>
                <div class="user-detail"><strong>ID：</strong>{user.id}</div>
                <div class="user-detail"><strong>邮箱：</strong>{user.email or "未提供"}</div>
                <div class="user-detail"><strong>提供商：</strong>{user.provider}</div>
            </div>

            <a href="/" class="main-app-btn" onclick="goToMainApp()">
                进入主应用
            </a>

            <div class="note">
                <strong>注意：</strong>您已成功通过Token登录。点击上方按钮进入主应用，或者直接访问根目录。
            </div>
        </div>

        <script>
            // Store authentication state
            localStorage.setItem('auth_token', '{token}');

            window.__AUTOGEN_AUTH_STATE__ = {{
                token: '{token}',
                user: {{
                    id: '{user.id}',
                    name: '{user.name}',
                    email: '{user.email or ""}',
                    provider: '{user.provider}',
                    roles: {user.roles if hasattr(user, 'roles') and user.roles else ['user']}
                }},
                isAuthenticated: true,
                loginMethod: 'token_post'
            }};

            console.log('✅ Token login successful - Fallback page');
            console.log('User:', window.__AUTOGEN_AUTH_STATE__.user);
            console.log('Token stored to localStorage');

            function goToMainApp() {{
                window.location.href = '/';
            }}
        </script>
    </body>
    </html>
    """




# Mount API and static file directories
app.mount("/api", api)
app.mount(
    "/files",
    StaticFiles(directory=initializer.static_root, html=True),
    name="files",
)

# Note: Static files mount for "/" should be last to avoid conflicts with API routes
# The POST route for "/" is defined above and will take precedence over static files
app.mount("/", StaticFiles(directory=initializer.ui_root, html=True), name="ui")

# Error handlers


@app.exception_handler(500)
async def internal_error_handler(request: Request, exc):
    logger.error(f"Internal error: {str(exc)}")
    return {
        "status": False,
        "message": "Internal server error",
        "detail": str(exc) if settings.API_DOCS else "Internal server error",
    }


def create_app() -> FastAPI:
    """
    Factory function to create and configure the FastAPI application.
    Useful for testing and different deployment scenarios.
    """
    return app
