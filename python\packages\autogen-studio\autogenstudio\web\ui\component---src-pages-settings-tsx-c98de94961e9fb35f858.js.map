{"version": 3, "file": "component---src-pages-settings-tsx-c98de94961e9fb35f858.js", "mappings": ";oJASA,MAAMA,GAAgB,E,QAAA,GAAiB,gBAAiB,CACtD,CACE,OACA,CACEC,EAAG,2EACHC,IAAK,WAGT,CAAC,OAAQ,CAAED,EAAG,UAAWC,IAAK,WAC9B,CAAC,OAAQ,CAAED,EAAG,aAAcC,IAAK,Y,yFCXnC,MAAMC,EAAkC,CACtCC,sBAAsB,EACtBC,8BAA8B,EAC9BC,4BAA4B,EAC5BC,4BAA6B,GA0BlBC,GAAmBC,EAAAA,EAAAA,IAAAA,EAC9BC,EAAAA,EAAAA,IACE,CAACC,EAAKC,KAAG,CAEPC,eAAgB,KAChBC,WAAW,EACXC,MAAO,KACPC,WAAYb,EAGZc,mBAAoBC,UAElB,IAAIN,IAAME,UAEV,IACEH,EAAI,CAAEG,WAAW,EAAMC,MAAO,OAC9B,MAAMI,QAAiBC,EAAAA,EAAYC,YAAYC,GAGzCN,EA1BOG,IAChBA,GAAaA,EAASI,QAAWJ,EAASI,OAAOC,GAG/CL,EAASI,OAAOC,GAFdrB,EAwBkBsB,CAAcN,GAEjCR,EAAI,CACFE,eAAgBM,EAChBH,aACAF,WAAW,GAEf,CAAE,MAAOC,GACPW,QAAQX,MAAM,2BAA4BA,GAC1CJ,EAAI,CACFI,MAAO,0BACPD,WAAW,EAEXE,WAAYb,GAEhB,GAKFwB,iBAAmBC,IACjB,MAAM,WAAEZ,GAAeJ,IACjBiB,EAAgB,IAAKb,KAAeY,GAC1CjB,EAAI,CAAEK,WAAYa,KAKpBC,gBAAiBZ,UACfP,EAAI,CAAEK,WAAYb,IACX4B,QAAQC,aAGnB,CACEC,KAAM,qBACNC,WAAaC,IAAK,CAEhBnB,WAAYmB,EAAMnB,e,qEC1FnB,IAAMoB,EAAW,SAAAC,GAAA,SAAAD,IAAA,OAAAC,EAAAC,MAAA,KAAAC,YAAA,MAAAC,EAAAA,EAAAA,GAAAJ,EAAAC,GAAA,IAAAI,EAAAL,EAAAM,UA0CrB,OA1CqBD,EAChBpB,YAANH,eAAkBI,GAChBI,QAAQiB,IAAI,8CAA8CrB,KAE1D,MAAMsB,QAAiBC,MACrB,GAAGC,KAAKC,yBACR,CACEC,QAASF,KAAKG,eAIlBvB,QAAQiB,IAAI,oCAAoCC,EAASM,UAEzD,MAAMC,QAAaP,EAASQ,OAG5B,GAFA1B,QAAQiB,IAAI,iCAAkCQ,IAEzCA,EAAKD,OAAQ,CAChB,MAAMnC,EAAQ,IAAIsC,MAAMF,EAAKG,QAAUH,EAAKI,SAAW,4BAEvD,MADA7B,QAAQX,MAAM,2CAA4CA,EAAMwC,SAC1DxC,CACR,CAEA,OAAOoC,EAAKA,IACd,EAACV,EAEKe,eAANtC,eAAqBC,EAAoBG,GACvC,MAAMmC,EAAe,IAChBtC,EACHuC,QAASvC,EAASuC,SAAWpC,GAG/BI,QAAQiB,IAAI,eAAgBc,GAE5B,MAAMb,QAAiBC,MAAM,GAAGC,KAAKC,yBAA0B,CAC7DY,OAAQ,MACRX,QAASF,KAAKG,aACdW,KAAMC,KAAKC,UAAUL,KAEjBN,QAAaP,EAASQ,OAC5B,IAAKD,EAAKD,OACR,MAAM,IAAIG,MAAMF,EAAKI,SAAW,6BAClC,OAAOJ,EAAKA,IACd,EAACf,CAAA,CA1CqB,C,QAAS2B,GA6C1B,MAAM3C,EAAc,IAAIgB,C,gHCvC/B,MAAM4B,GAAU,EAAAC,EAAA,GAAiB,UAAW,CAC1C,CAAC,SAAU,CAAEC,GAAI,OAAQC,GAAI,MAAOC,EAAG,KAAMC,KAAM,eAAgBnE,IAAK,WACxE,CAAC,SAAU,CAAEgE,GAAI,OAAQC,GAAI,OAAQC,EAAG,KAAMC,KAAM,eAAgBnE,IAAK,WACzE,CAAC,SAAU,CAAEgE,GAAI,MAAOC,GAAI,MAAOC,EAAG,KAAMC,KAAM,eAAgBnE,IAAK,WACvE,CAAC,SAAU,CAAEgE,GAAI,MAAOC,GAAI,OAAQC,EAAG,KAAMC,KAAM,eAAgBnE,IAAK,WACxE,CACE,OACA,CACED,EAAG,2NACHC,IAAK,aCTLoE,GAAW,EAAAL,EAAA,GAAiB,WAAY,CAC5C,CAAC,OAAQ,CAAEhE,EAAG,yBAA0BC,IAAK,WAC7C,CAAC,OAAQ,CAAED,EAAG,wBAAyBC,IAAK,WAC5C,CAAC,OAAQ,CAAEqE,GAAI,KAAMC,GAAI,IAAKC,GAAI,IAAKC,GAAI,KAAMxE,IAAK,WACtD,CAAC,OAAQ,CAAEqE,GAAI,IAAKC,GAAI,KAAMC,GAAI,IAAKC,GAAI,KAAMxE,IAAK,a,2CCAjD,MAAMyE,EAAkDC,IAMxD,IANyD,OAC9DC,EAAM,SACNC,EAAQ,eACRC,EAAc,SACdC,EAAQ,gBACRC,GACDL,EAEC,OAAKC,EAkBHK,EAAAA,cAAA,OAAKC,UAAU,oCAEbD,EAAAA,cAAA,OAAKC,UAAU,kFACbD,EAAAA,cAAA,OAAKC,UAAU,2BACbD,EAAAA,cAAA,QAAMC,UAAU,4BAA2B,MAC3CD,EAAAA,cAAA,QAAMC,UAAU,wDACbL,EAASM,SAGdF,EAAAA,cAACG,EAAAA,EAAO,CAACC,MAAM,SACbJ,EAAAA,cAAA,UACEK,QAASP,EACTG,UAAU,gKAEVD,EAAAA,cAACM,EAAAA,EAAc,CAACC,YAAa,IAAKN,UAAU,eAKlDD,EAAAA,cAAA,OAAKC,UAAU,uCACZL,EAASY,IAAKC,GACbT,EAAAA,cAAA,OAAKhF,IAAKyF,EAAQC,GAAIT,UAAU,YAC9BD,EAAAA,cAAA,OACEC,UAAW,gGAEPJ,EAAea,KAAOD,EAAQC,GAAK,YAAc,iBAGvDV,EAAAA,cAAA,OACEC,UAAW,+FAEPJ,EAAea,KAAOD,EAAQC,GAC1B,6BACA,sBAERL,QAASA,IAAMN,EAAgBU,IAE/BT,EAAAA,cAAA,OAAKC,UAAU,2BACbD,EAAAA,cAACS,EAAQE,KAAI,CAACV,UAAU,YACxBD,EAAAA,cAAA,QAAMC,UAAU,WAAWQ,EAAQL,aAvD7CJ,EAAAA,cAAA,OAAKC,UAAU,oCACbD,EAAAA,cAAA,OAAKC,UAAU,aACbD,EAAAA,cAACG,EAAAA,EAAO,CAACC,MAAO,OAAOR,EAASM,WAC9BF,EAAAA,cAAA,UACEK,QAASP,EACTG,UAAU,gKAEVD,EAAAA,cAACY,EAAAA,EAAa,CAACL,YAAa,IAAKN,UAAU,iB,kFCJzD,MAAMY,EAA8CnB,IAAA,IAAC,QACnDoB,EAAO,SACPC,EAAQ,MACRC,EAAK,YACLC,EAAW,SACXC,GAAW,GACZxB,EAAA,OACCM,EAAAA,cAAA,OAAKC,UAAU,uFACbD,EAAAA,cAAA,OAAKC,UAAU,uBACbD,EAAAA,cAAA,SAAOC,UAAU,eAAee,GAC/BC,GACCjB,EAAAA,cAAA,QAAMC,UAAU,0BAA0BgB,IAG9CjB,EAAAA,cAAA,OAAKC,UAAU,YACbD,EAAAA,cAAA,SACEmB,KAAK,WACLL,QAASA,EACTC,SAAWK,GAAML,EAASK,EAAEC,OAAOP,SACnCI,SAAUA,EACVjB,UAAU,UACVS,GAAI,UAAUM,EAAMM,QAAQ,OAAQ,KAAKC,kBAE3CvB,EAAAA,cAAA,SACEwB,QAAS,UAAUR,EAAMM,QAAQ,OAAQ,KAAKC,gBAC9CtB,UAAW,oKACTiB,EAAW,gCAAkC,MAC3CJ,EAAU,YAAc,iBAE5Bd,EAAAA,cAAA,QACEC,UAAW,8EACTa,EAAU,gBAAkB,uBAQlCW,EAAwDC,IAAA,IAAC,MAC7DC,EAAK,SACLZ,EAAQ,MACRC,EAAK,YACLC,EAAW,SACXC,GAAW,EAAK,IAChBU,EAAM,EAAC,IACPC,EAAM,GAAE,OACRC,EAAS,IACVJ,EAAA,OACC1B,EAAAA,cAAA,OAAKC,UAAU,qFACbD,EAAAA,cAAA,OAAKC,UAAU,uBACbD,EAAAA,cAAA,SAAOC,UAAU,eAAee,GAC/BC,GACCjB,EAAAA,cAAA,QAAMC,UAAU,0BAA0BgB,IAG9CjB,EAAAA,cAAA,OAAKC,UAAU,2BACbD,EAAAA,cAAA,SACEmB,KAAK,SACLQ,MAAOA,EACPZ,SAAWK,IACT,MAAMW,EAAWC,SAASZ,EAAEC,OAAOM,QAC9BM,MAAMF,IAAaA,GAAYH,GAAOG,GAAYF,GACrDd,EAASgB,IAGbb,SAAUA,EACVU,IAAKA,EACLC,IAAKA,EACL5B,UAAU,0JAEX6B,GAAU9B,EAAAA,cAAA,QAAMC,UAAU,0BAA0B6B,MAkO3D,MAzN+DI,IAAiB,IAADC,EAAAC,EAAAC,EAAA,IAAf,OAAEjG,GAAQ8F,EACxE,MAAM,eACJvG,EACAG,WAAYwG,EAAe,mBAC3BvG,IACET,EAAAA,EAAAA,MAGE,EAACiH,EAAgB,EAACC,IAAsBC,EAAAA,EAAAA,UAAqB,CACjEvH,sBAAsB,EACtBC,8BAA8B,EAC9BC,4BAA4B,EAC5BC,4BAA6B,KAGzB,EAACqH,EAAQ,EAACC,IAAcF,EAAAA,EAAAA,WAAS,IACjC,EAACG,EAAS,EAACC,IAAeJ,EAAAA,EAAAA,WAAS,IAClCK,EAAYC,GAAiB1E,EAAAA,GAAQ2E,cAG5CC,EAAAA,EAAAA,WAAU,KACRT,EAAmBF,IAClB,CAACA,IAGJ,MAAMY,EAAsBA,CAC1BlI,EACA2G,KAEAa,EAAoBW,IAAI,IACnBA,EACH,CAACnI,GAAM2G,KAETgB,GAAW,IA4Fb,OACE3C,EAAAA,cAAA,OAAKC,UAAU,KACZ8C,EACD/C,EAAAA,cAAA,OAAKC,UAAU,0CACbD,EAAAA,cAAA,MAAIC,UAAU,uBAAsB,QACpCD,EAAAA,cAAA,OAAKC,UAAU,yBACbD,EAAAA,cAACG,EAAAA,EAAO,CAACC,MAAM,UACbJ,EAAAA,cAACoD,EAAAA,GAAM,CACLzC,KAAMX,EAAAA,cAACqD,EAAAA,EAAS,CAACpD,UAAU,YAC3BI,QAjGQrE,UAClB,IAGE,GAFA6G,GAAY,IAEPlH,EAGH,OAFAmH,EAAWjH,MAAM,cACjBgH,GAAY,GAKd,MAAM5H,EAAkC,CACtCC,sBAAsB,EACtBC,8BAA8B,EAC9BC,4BAA4B,EAC5BC,4BAA6B,GAI/BmH,EAAmBvH,GAGnB,MAAMqI,EAA4B,IAC7B3H,EACHU,OAAQ,IACHV,EAAeU,OAClBC,GAAIrB,GAENsI,gBAAYC,EACZC,gBAAYD,GAGdhH,QAAQiB,IAAI,oBAAqB6F,SAG3BpH,EAAAA,EAAYoC,eAAegF,EAAiBlH,SAG5CL,EAAmBK,GAEzBuG,GAAW,GACXG,EAAWY,QAAQ,WACrB,CAAE,MAAO7H,GACPW,QAAQX,MAAM,+BAAgCA,GAC9CiH,EAAWjH,MAAM,WACnB,CAAC,QACCgH,GAAY,EACd,GAmDU3B,SAAU0B,GACX,OAIH5C,EAAAA,cAACG,EAAAA,EAAO,CAACC,MAAOsC,EAAU,SAAW,YACnC1C,EAAAA,cAACoD,EAAAA,GAAM,CACLjC,KAAK,UACLR,KACEX,EAAAA,cAAA,OAAKC,UAAU,YACbD,EAAAA,cAAC2D,EAAAA,EAAI,CAAC1D,UAAU,YACfyC,GACC1C,EAAAA,cAAA,OAAKC,UAAU,8DAIrBI,QA/DOrE,UACjB,IAGE,GAFA6G,GAAY,IAEPlH,EAGH,OAFAmH,EAAWjH,MAAM,cACjBgH,GAAY,GAKd,MAAMS,EAA4B,IAC7B3H,EACHU,OAAQ,IACHV,EAAeU,OAClBC,GAAIiG,GAENgB,gBAAYC,EACZC,gBAAYD,SAIRtH,EAAAA,EAAYoC,eAAegF,EAAiBlH,SAG5CL,EAAmBK,GAEzBuG,GAAW,GACXG,EAAWY,QAAQ,WACrB,CAAE,MAAO7H,GACPW,QAAQX,MAAM,8BAA+BA,GAC7CiH,EAAWjH,MAAM,WACnB,CAAC,QACCgH,GAAY,EACd,GA8BU3B,UAAWwB,GAAWE,EACtBgB,QAAShB,GACV,SAOP5C,EAAAA,cAAA,OAAKC,UAAU,6CACbD,EAAAA,cAACa,EAAa,CACZC,QAASyB,EAAgBrH,qBACzB6F,SAAWD,GACToC,EAAoB,uBAAwBpC,GAE9CE,MAAM,UACNC,YAAY,qBACZC,SAAU0B,IAGZ5C,EAAAA,cAACa,EAAa,CACZC,QAAqD,QAA9CqB,EAAEI,EAAgBpH,oCAA4B,IAAAgH,GAAAA,EACrDpB,SAAWD,GACToC,EAAoB,+BAAgCpC,GAEtDE,MAAM,SACNC,YAAY,cACZC,SAAU0B,IAGZ5C,EAAAA,cAACa,EAAa,CACZC,QAAmD,QAA5CsB,EAAEG,EAAgBnH,kCAA0B,IAAAgH,GAAAA,EACnDrB,SAAWD,GACToC,EAAoB,6BAA8BpC,GAEpDE,MAAM,YACNC,YAAY,aACZC,SAAU0B,IAGZ5C,EAAAA,cAACyB,EAAkB,CACjBE,MAAkD,QAA7CU,EAAEE,EAAgBlH,mCAA2B,IAAAgH,EAAAA,EAAI,EACtDtB,SAAWY,GACTuB,EAAoB,8BAA+BvB,GAErDX,MAAM,WACNC,YAAY,sBACZC,SAAU0B,EACVhB,IAAK,EACLC,IAAK,GACLC,OAAO,QAIX9B,EAAAA,cAAA,OAAKC,UAAU,sCACbD,EAAAA,cAAClF,EAAAA,EAAa,CACZyF,YAAa,IACbN,UAAU,8BACT,IAAI,yBC1SR,MAAM4D,EAA4BA,KACvC,MAAM,EAACC,EAAc,EAACC,IAAoBtB,EAAAA,EAAAA,UAAS,KACjD,GAAsB,oBAAXuB,OAAwB,CACjC,MAAMC,EAASC,aAAaC,QAAQ,mBACpC,OAAkB,OAAXF,GAAkBtF,KAAKyF,MAAMH,EACtC,CACA,OAAO,KAGH,KAAEI,IAASC,EAAAA,EAAAA,YAAWC,EAAAA,IACpB3I,UAAW4I,EAAW,gBAAEC,IAAoBC,EAAAA,EAAAA,KAC9CtI,GAASiI,aAAI,EAAJA,EAAM3D,KAAM,GAE3BlE,QAAQiB,IAAI,4BAA6B4G,GACzC7H,QAAQiB,IAAI,8BAA+BrB,GAC3CI,QAAQiB,IAAI,mCAAoC+G,GAChDhI,QAAQiB,IAAI,uCAAwCgH,GAEpD,MAAM,eAAE9I,EAAc,gBAAEiB,EAAe,mBAAEb,EAAkB,UAAEH,IAC3DN,EAAAA,EAAAA,KAmBF,IAhBA2H,EAAAA,EAAAA,WAAU,KACRzG,QAAQiB,IAAI,wDAAwDrB,mBAAwBoI,MACvFA,GAAepI,GAAUqI,GAC5BjI,QAAQiB,IAAI,4DAA4DrB,KACxEL,EAAmBK,IAEnBI,QAAQiB,IAAI,yDAAyD+G,cAAwBpI,uBAA4BqI,MAE1H,CAACrI,EAAQL,EAAoByI,EAAaC,KAE7CxB,EAAAA,EAAAA,WAAU,KACc,oBAAXe,QACTE,aAAaS,QAAQ,kBAAmBhG,KAAKC,UAAUkF,KAExD,CAACA,IAEAU,GAAe5I,EACjB,OAAOoE,EAAAA,cAAA,OAAKC,UAAU,mBAAkB,aAInBtE,SAAAA,EAAgBU,OAAOuI,qBAA9C,MAYMC,EAA+B,CACnC,CACE7J,IAAK,KACLgG,MACEhB,EAAAA,cAAA,QAAMC,UAAU,2BACdD,EAAAA,cAAClB,EAAO,CAACmB,UAAU,YAAY,QAInC6E,SAAU9E,EAAAA,cAAC+E,EAAe,CAAC3I,OAAQA,MAwCvC,OACE4D,EAAAA,cAAA,OAAKC,UAAU,+BACbD,EAAAA,cAAA,OACEC,UAAW,yEACT6D,EAAgB,OAAS,SAG3B9D,EAAAA,cAACP,EAAe,CACdE,OAAQmE,EACRlE,SAAU,CACR,CACEc,GAAI,WACJN,MAAO,KACPO,KAAMvB,EACN4F,QAASA,IAAMhF,EAAAA,cAAAA,EAAAA,SAAA,QAGnBH,eAAgB,CACda,GAAI,WACJN,MAAO,KACPO,KAAMvB,EACN4F,QAASA,IAAMhF,EAAAA,cAAAA,EAAAA,SAAA,OAEjBF,SAAUA,IAAMiE,GAAkBD,GAClC/D,gBAAiBA,UAIrBC,EAAAA,cAAA,OACEC,UAAW,uDACT6D,EAAgB,QAAU,UAG5B9D,EAAAA,cAAA,OAAKC,UAAU,YACbD,EAAAA,cAAA,OAAKC,UAAU,wCACbD,EAAAA,cAAA,QAAMC,UAAU,4BAA2B,OAG7CD,EAAAA,cAAA,OAAKC,UAAU,wCACbD,EAAAA,cAAA,QAAMC,UAAU,kBAAiB,cAKnCD,EAAAA,cAAA,OAAKC,UAAU,0BACbD,EAAAA,cAACiF,EAAAA,EAAI,CACHC,iBAAiB,KACjBC,MAAON,EAEPO,KAAK,QACLnF,UAAU,uBCjJxB,MArBqBP,IAAmB,IAAlB,KAAEzB,GAAWyB,EACjC,OACEM,EAAAA,cAACqF,EAAAA,EAAM,CAACC,KAAMrH,EAAKsH,KAAKC,aAAcpF,MAAM,KAAKqF,KAAM,aACrDzF,EAAAA,cAAA,QAAM0F,MAAO,CAAEC,OAAQ,QAAU1F,UAAU,YACzCD,EAAAA,cAAC6D,EAAe,Q,oECPjB,IAAehF,EAAO,oBAAAA,IAAA,KAAAtB,EAAAsB,EAAArB,UAwB3B,OAxB2BD,EACjBM,WAAV,WACE,OAAO+H,EAAAA,EAAAA,KACT,EAACrI,EAESQ,WAAV,WAEE,MAAM8H,EAAQ3B,aAAaC,QAAQ,cAE7BrG,EAAuB,CAC3B,eAAgB,oBAWlB,OAPI+H,GACF/H,EAAuB,cAAI,UAAU+H,IACrCrJ,QAAQiB,IAAI,8CAA8CoI,EAAMC,UAAU,EAAG,WAE7EtJ,QAAQiB,IAAI,8CAGPK,CACT,EAEAe,CAAA,CAxB2B,E,uBCF7B,SAASkH,EAAkBF,EAAOG,EAAWC,GAC3C,MAAM,WACJC,EAAU,MACVC,EAAK,YACLC,GACEH,EACEI,EAAkBD,EAAc,MAAQ,GACxCE,EAAe,CAAC,QAASH,EAAQ,QAAU,KAAM,UAAUI,OAAOC,SAAShG,IAAIiG,GAAK,KAAKA,KAAKJ,KAAmBK,KAAK,KAC5H,MAAO,CACL,CAAC,cAAcV,gBAAyB,CACtCW,gBAAiBd,EAAMe,KAAKf,EAAMgB,WAAWC,KAAK,GAAGC,SAEvD,SAAUC,OAAOC,OAAOD,OAAOC,OAAO,CACpC,CAACX,GAAe,CACdY,OAAQ,IAEThB,EAAa,CACd,CAAC,IAAIA,KAAe,CAClBgB,OAAQ,IAER,CAAC,GAAI,CACP,CAAC,eAAeb,KAAoB,CAClCa,OAAQ,KAIhB,CAEA,SAASC,EAAwBC,EAAWpB,EAAWC,GACrD,MAAM,YACJG,GACEH,EACEI,EAAkBD,EAAc,KAAKA,IAAgB,GAC3D,MAAO,CACL,CAAC,cAAcJ,qBAA6BA,gBAAwBK,KAAoB,CACtFgB,aAAc,GAEhB,CAAC,cAAcrB,eAAuBA,gBAAyB,CAC7D,CAAC,KAAKK,OAAqBe,QAAgBf,OAAqBe,QAAgBf,KAAoB,CAClGiB,qBAAsB,EACtBC,mBAAoB,IAGxB,CAAC,cAAcvB,gBAAwBA,eAAwB,CAC7D,CAAC,KAAKK,OAAqBe,QAAgBf,OAAqBe,QAAgBf,KAAoB,CAClGmB,uBAAwB,EACxBC,qBAAsB,IAI9B,CACO,SAASC,EAAoB7B,EAAOI,EAAU,CACnDE,OAAO,IAEP,MAAM,aACJwB,GACE9B,EACE+B,EAAa,GAAGD,YACtB,MAAO,CACL,CAACC,GAAaZ,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAGlB,EAAkBF,EAAO+B,EAAY3B,IAAWkB,EAAwBQ,EAAcC,EAAY3B,IAEpJ,C,sFCrDA,MAAMtC,GAAO,E,QAAA,GAAiB,OAAQ,CACpC,CACE,OACA,CACE5I,EAAG,qGACHC,IAAK,WAGT,CAAC,OAAQ,CAAED,EAAG,4CAA6CC,IAAK,WAChE,CAAC,OAAQ,CAAED,EAAG,yBAA0BC,IAAK,Y,uDCT/C,MAAMqI,GAAY,E,QAAA,GAAiB,YAAa,CAC9C,CAAC,OAAQ,CAAEtI,EAAG,oDAAqDC,IAAK,WACxE,CAAC,OAAQ,CAAED,EAAG,WAAYC,IAAK,Y", "sources": ["webpack://autogentstudio/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js", "webpack://autogentstudio/./src/components/views/settings/store.tsx", "webpack://autogentstudio/./src/components/views/settings/api.ts", "webpack://autogentstudio/./node_modules/lucide-react/dist/esm/icons/palette.js", "webpack://autogentstudio/./node_modules/lucide-react/dist/esm/icons/variable.js", "webpack://autogentstudio/./src/components/views/settings/sidebar.tsx", "webpack://autogentstudio/./src/components/views/settings/view/ui.tsx", "webpack://autogentstudio/./src/components/views/settings/manager.tsx", "webpack://autogentstudio/./src/pages/settings.tsx", "webpack://autogentstudio/./src/components/utils/baseapi.ts", "webpack://autogentstudio/./node_modules/antd/es/style/compact-item.js", "webpack://autogentstudio/./node_modules/lucide-react/dist/esm/icons/save.js", "webpack://autogentstudio/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js"], "sourcesContent": ["/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst TriangleAlert = createLucideIcon(\"TriangleAlert\", [\n  [\n    \"path\",\n    {\n      d: \"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3\",\n      key: \"wmoenq\"\n    }\n  ],\n  [\"path\", { d: \"M12 9v4\", key: \"juzpu7\" }],\n  [\"path\", { d: \"M12 17h.01\", key: \"p32p05\" }]\n]);\n\nexport { TriangleAlert as default };\n//# sourceMappingURL=triangle-alert.js.map\n", "// store.tsx\r\nimport { create } from \"zustand\";\r\nimport { persist } from \"zustand/middleware\";\r\nimport { settingsAPI } from \"./api\";\r\nimport { Settings, UISettings } from \"../../types/datamodel\";\r\n\r\n// Default UI settings that match the backend defaults\r\nconst DEFAULT_UI_SETTINGS: UISettings = {\r\n  show_llm_call_events: false,\r\n  expanded_messages_by_default: false,\r\n  show_agent_flow_by_default: false,\r\n  human_input_timeout_minutes: 3,\r\n};\r\n\r\ninterface SettingsState {\r\n  // Server-synced settings\r\n  serverSettings: Settings | null;\r\n  isLoading: boolean;\r\n  error: string | null;\r\n\r\n  // UI settings - these will be synced with server but kept in local state for performance\r\n  uiSettings: UISettings;\r\n\r\n  // Actions\r\n  initializeSettings: (userId: string) => Promise<void>;\r\n  updateUISettings: (settings: Partial<UISettings>) => void; // Simplified to avoid async issues\r\n  resetUISettings: () => Promise<void>;\r\n}\r\n\r\n// Helper function to safely access nested properties\r\nconst getUISettings = (settings: Settings | null): UISettings => {\r\n  if (!settings || !settings.config || !settings.config.ui) {\r\n    return DEFAULT_UI_SETTINGS;\r\n  }\r\n  return settings.config.ui;\r\n};\r\n\r\nexport const useSettingsStore = create<SettingsState>()(\r\n  persist(\r\n    (set, get) => ({\r\n      // Initial state\r\n      serverSettings: null,\r\n      isLoading: false,\r\n      error: null,\r\n      uiSettings: DEFAULT_UI_SETTINGS,\r\n\r\n      // Load settings from server\r\n      initializeSettings: async (userId: string) => {\r\n        // Skip if already loading\r\n        if (get().isLoading) return;\r\n\r\n        try {\r\n          set({ isLoading: true, error: null });\r\n          const settings = await settingsAPI.getSettings(userId);\r\n\r\n          // Extract UI settings from server response\r\n          const uiSettings = getUISettings(settings);\r\n\r\n          set({\r\n            serverSettings: settings,\r\n            uiSettings,\r\n            isLoading: false,\r\n          });\r\n        } catch (error) {\r\n          console.error(\"Failed to load settings:\", error);\r\n          set({\r\n            error: \"Failed to load settings\",\r\n            isLoading: false,\r\n            // Use defaults if server fails\r\n            uiSettings: DEFAULT_UI_SETTINGS,\r\n          });\r\n        }\r\n      },\r\n\r\n      // Update UI settings locally only\r\n      // The UISettingsPanel component will handle server syncing\r\n      updateUISettings: (partialSettings: Partial<UISettings>) => {\r\n        const { uiSettings } = get();\r\n        const newUISettings = { ...uiSettings, ...partialSettings };\r\n        set({ uiSettings: newUISettings });\r\n      },\r\n\r\n      // Reset UI settings to defaults - now just resets local state\r\n      // The UISettingsPanel component will handle actual server resets\r\n      resetUISettings: async () => {\r\n        set({ uiSettings: DEFAULT_UI_SETTINGS });\r\n        return Promise.resolve();\r\n      },\r\n    }),\r\n    {\r\n      name: \"ags-app-settings-0\",\r\n      partialize: (state) => ({\r\n        // Only persist UI settings locally for performance\r\n        uiSettings: state.uiSettings,\r\n      }),\r\n    }\r\n  )\r\n);\r\n", "import { Settings } from \"../../types/datamodel\";\r\nimport { BaseAPI } from \"../../utils/baseapi\";\r\n\r\nexport class SettingsAPI extends BaseAPI {\r\n  async getSettings(userId: string): Promise<Settings> {\r\n    console.log(`🔍 SettingsAPI: Getting settings for user: ${userId}`);\r\n    \r\n    const response = await fetch(\r\n      `${this.getBaseUrl()}/settings/`,\r\n      {\r\n        headers: this.getHeaders(),\r\n      }\r\n    );\r\n    \r\n    console.log(`🔍 SettingsAPI: Response status: ${response.status}`);\r\n    \r\n    const data = await response.json();\r\n    console.log(`🔍 SettingsAPI: Response data:`, data);\r\n    \r\n    if (!data.status) {\r\n      const error = new Error(data.detail || data.message || \"Failed to fetch settings\");\r\n      console.error(\"❌ SettingsAPI: Failed to fetch settings:\", error.message);\r\n      throw error;\r\n    }\r\n    \r\n    return data.data;\r\n  }\r\n\r\n  async updateSettings(settings: Settings, userId: string): Promise<Settings> {\r\n    const settingsData = {\r\n      ...settings,\r\n      user_id: settings.user_id || userId,\r\n    };\r\n\r\n    console.log(\"settingsData\", settingsData);\r\n\r\n    const response = await fetch(`${this.getBaseUrl()}/settings/`, {\r\n      method: \"PUT\",\r\n      headers: this.getHeaders(),\r\n      body: JSON.stringify(settingsData),\r\n    });\r\n    const data = await response.json();\r\n    if (!data.status)\r\n      throw new Error(data.message || \"Failed to update settings\");\r\n    return data.data;\r\n  }\r\n}\r\n\r\nexport const settingsAPI = new SettingsAPI();\r\n", "/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst Palette = createLucideIcon(\"Palette\", [\n  [\"circle\", { cx: \"13.5\", cy: \"6.5\", r: \".5\", fill: \"currentColor\", key: \"1okk4w\" }],\n  [\"circle\", { cx: \"17.5\", cy: \"10.5\", r: \".5\", fill: \"currentColor\", key: \"f64h9f\" }],\n  [\"circle\", { cx: \"8.5\", cy: \"7.5\", r: \".5\", fill: \"currentColor\", key: \"fotxhn\" }],\n  [\"circle\", { cx: \"6.5\", cy: \"12.5\", r: \".5\", fill: \"currentColor\", key: \"qy21gx\" }],\n  [\n    \"path\",\n    {\n      d: \"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z\",\n      key: \"12rzf8\"\n    }\n  ]\n]);\n\nexport { Palette as default };\n//# sourceMappingURL=palette.js.map\n", "/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst Variable = createLucideIcon(\"Variable\", [\n  [\"path\", { d: \"M8 21s-4-3-4-9 4-9 4-9\", key: \"uto9ud\" }],\n  [\"path\", { d: \"M16 3s4 3 4 9-4 9-4 9\", key: \"4w2vsq\" }],\n  [\"line\", { x1: \"15\", x2: \"9\", y1: \"9\", y2: \"15\", key: \"f7djnv\" }],\n  [\"line\", { x1: \"9\", x2: \"15\", y1: \"9\", y2: \"15\", key: \"1shsy8\" }]\n]);\n\nexport { Variable as default };\n//# sourceMappingURL=variable.js.map\n", "import React from \"react\";\r\nimport { <PERSON><PERSON>, Tooltip } from \"antd\";\r\nimport { PanelLeftClose, PanelLeftOpen } from \"lucide-react\";\r\nimport { SettingsSection } from \"./types\";\r\n\r\ninterface SettingsSidebarProps {\r\n  isOpen: boolean;\r\n  sections: SettingsSection[];\r\n  currentSection: SettingsSection;\r\n  onToggle: () => void;\r\n  onSelectSection: (section: SettingsSection) => void;\r\n}\r\n\r\nexport const SettingsSidebar: React.FC<SettingsSidebarProps> = ({\r\n  isOpen,\r\n  sections,\r\n  currentSection,\r\n  onToggle,\r\n  onSelectSection,\r\n}) => {\r\n  // Render collapsed state\r\n  if (!isOpen) {\r\n    return (\r\n      <div className=\"h-full border-r border-secondary\">\r\n        <div className=\"p-2 -ml-2\">\r\n          <Tooltip title={`设置 (${sections.length})`}>\r\n            <button\r\n              onClick={onToggle}\r\n              className=\"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50\"\r\n            >\r\n              <PanelLeftOpen strokeWidth={1.5} className=\"h-6 w-6\" />\r\n            </button>\r\n          </Tooltip>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"h-full border-r border-secondary\">\r\n      {/* Header */}\r\n      <div className=\"flex items-center justify-between pt-0 p-4 pl-2 pr-2 border-b border-secondary\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <span className=\"text-primary font-medium\">设置</span>\r\n          <span className=\"px-2 py-0.5 text-xs bg-accent/10 text-accent rounded\">\r\n            {sections.length}\r\n          </span>\r\n        </div>\r\n        <Tooltip title=\"关闭侧边栏\">\r\n          <button\r\n            onClick={onToggle}\r\n            className=\"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50\"\r\n          >\r\n            <PanelLeftClose strokeWidth={1.5} className=\"h-6 w-6\" />\r\n          </button>\r\n        </Tooltip>\r\n      </div>\r\n\r\n      <div className=\"overflow-y-auto h-[calc(100%-64px)]\">\r\n        {sections.map((section) => (\r\n          <div key={section.id} className=\"relative\">\r\n            <div\r\n              className={`absolute top-1 left-0.5 z-50 h-[calc(100%-8px)] w-1 bg-opacity-80 rounded \r\n                ${\r\n                  currentSection.id === section.id ? \"bg-accent\" : \"bg-tertiary\"\r\n                }`}\r\n            />\r\n            <div\r\n              className={`group ml-1 flex flex-col p-3 rounded-l cursor-pointer hover:bg-secondary \r\n                ${\r\n                  currentSection.id === section.id\r\n                    ? \"border-accent bg-secondary\"\r\n                    : \"border-transparent\"\r\n                }`}\r\n              onClick={() => onSelectSection(section)}\r\n            >\r\n              <div className=\"flex items-center gap-2\">\r\n                <section.icon className=\"w-4 h-4\" />\r\n                <span className=\"text-sm\">{section.title}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n", "import React, { useEffect, useState } from \"react\";\r\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, message } from \"antd\";\r\nimport { <PERSON>ota<PERSON><PERSON>c<PERSON>, Save, TriangleAlert } from \"lucide-react\";\r\nimport { settingsAPI } from \"../api\";\r\nimport { Settings, UISettings } from \"../../../types/datamodel\";\r\nimport { useSettingsStore } from \"../store\";\r\n\r\ninterface SettingToggleProps {\r\n  checked: boolean;\r\n  onChange: (checked: boolean) => void;\r\n  label: string;\r\n  description?: string;\r\n  disabled?: boolean;\r\n}\r\n\r\ninterface SettingNumberInputProps {\r\n  value: number;\r\n  onChange: (value: number) => void;\r\n  label: string;\r\n  description?: string;\r\n  disabled?: boolean;\r\n  min?: number;\r\n  max?: number;\r\n  suffix?: string;\r\n}\r\n\r\nconst SettingToggle: React.FC<SettingToggleProps> = ({\r\n  checked,\r\n  onChange,\r\n  label,\r\n  description,\r\n  disabled = false,\r\n}) => (\r\n  <div className=\"flex justify-between items-start p-4 hover:bg-secondary/5 rounded transition-colors\">\r\n    <div className=\"flex flex-col gap-1\">\r\n      <label className=\"font-medium\">{label}</label>\r\n      {description && (\r\n        <span className=\"text-sm text-secondary\">{description}</span>\r\n      )}\r\n    </div>\r\n    <div className=\"relative\">\r\n      <input\r\n        type=\"checkbox\"\r\n        checked={checked}\r\n        onChange={(e) => onChange(e.target.checked)}\r\n        disabled={disabled}\r\n        className=\"sr-only\"\r\n        id={`toggle-${label.replace(/\\s+/g, \"-\").toLowerCase()}`}\r\n      />\r\n      <label\r\n        htmlFor={`toggle-${label.replace(/\\s+/g, \"-\").toLowerCase()}`}\r\n        className={`relative inline-flex items-center h-6 rounded-full w-11 transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50 cursor-pointer ${\r\n          disabled ? \"opacity-50 cursor-not-allowed\" : \"\"\r\n        } ${checked ? \"bg-accent\" : \"bg-gray-300\"}`}\r\n      >\r\n        <span\r\n          className={`inline-block w-4 h-4 transform bg-white rounded-full transition-transform ${\r\n            checked ? \"translate-x-6\" : \"translate-x-1\"\r\n          }`}\r\n        />\r\n      </label>\r\n    </div>\r\n  </div>\r\n);\r\n\r\nconst SettingNumberInput: React.FC<SettingNumberInputProps> = ({\r\n  value,\r\n  onChange,\r\n  label,\r\n  description,\r\n  disabled = false,\r\n  min = 1,\r\n  max = 30,\r\n  suffix = \"\",\r\n}) => (\r\n  <div className=\"flex justify-between items-start p-4 hover:bg-secondary rounded transition-colors\">\r\n    <div className=\"flex flex-col gap-1\">\r\n      <label className=\"font-medium\">{label}</label>\r\n      {description && (\r\n        <span className=\"text-sm text-secondary\">{description}</span>\r\n      )}\r\n    </div>\r\n    <div className=\"flex items-center gap-2\">\r\n      <input\r\n        type=\"number\"\r\n        value={value}\r\n        onChange={(e) => {\r\n          const newValue = parseInt(e.target.value);\r\n          if (!isNaN(newValue) && newValue >= min && newValue <= max) {\r\n            onChange(newValue);\r\n          }\r\n        }}\r\n        disabled={disabled}\r\n        min={min}\r\n        max={max}\r\n        className=\"w-16 px-2 py-1 text-sm border border-secondary rounded focus:border-accent focus:ring-1 focus:ring-accent outline-none disabled:opacity-50 bg-primary\"\r\n      />\r\n      {suffix && <span className=\"text-sm text-secondary\">{suffix}</span>}\r\n    </div>\r\n  </div>\r\n);\r\n\r\ninterface UISettingsPanelProps {\r\n  userId: string;\r\n}\r\n\r\nexport const UISettingsPanel: React.FC<UISettingsPanelProps> = ({ userId }) => {\r\n  const {\r\n    serverSettings,\r\n    uiSettings: storeUISettings,\r\n    initializeSettings,\r\n  } = useSettingsStore();\r\n\r\n  // Local state for UI settings\r\n  const [localUISettings, setLocalUISettings] = useState<UISettings>({\r\n    show_llm_call_events: false,\r\n    expanded_messages_by_default: false,\r\n    show_agent_flow_by_default: false,\r\n    human_input_timeout_minutes: 3,\r\n  });\r\n\r\n  const [isDirty, setIsDirty] = useState(false);\r\n  const [isSaving, setIsSaving] = useState(false);\r\n  const [messageApi, contextHolder] = message.useMessage();\r\n\r\n  // Initialize local state from store\r\n  useEffect(() => {\r\n    setLocalUISettings(storeUISettings);\r\n  }, [storeUISettings]);\r\n\r\n  // Update local state when a setting is changed\r\n  const handleSettingChange = (\r\n    key: keyof UISettings,\r\n    value: boolean | number\r\n  ) => {\r\n    setLocalUISettings((prev) => ({\r\n      ...prev,\r\n      [key]: value,\r\n    }));\r\n    setIsDirty(true);\r\n  };\r\n\r\n  // Reset UI settings to defaults\r\n  const handleReset = async () => {\r\n    try {\r\n      setIsSaving(true);\r\n\r\n      if (!serverSettings) {\r\n        messageApi.error(\"设置未加载\");\r\n        setIsSaving(false);\r\n        return;\r\n      }\r\n\r\n      // Define default UI settings\r\n      const DEFAULT_UI_SETTINGS: UISettings = {\r\n        show_llm_call_events: false,\r\n        expanded_messages_by_default: false,\r\n        show_agent_flow_by_default: false,\r\n        human_input_timeout_minutes: 3,\r\n      };\r\n\r\n      // Update local state\r\n      setLocalUISettings(DEFAULT_UI_SETTINGS);\r\n\r\n      // Prepare for server update\r\n      const updatedSettings: Settings = {\r\n        ...serverSettings,\r\n        config: {\r\n          ...serverSettings.config,\r\n          ui: DEFAULT_UI_SETTINGS,\r\n        },\r\n        created_at: undefined,\r\n        updated_at: undefined,\r\n      };\r\n\r\n      console.log(\"Updated settings:\", updatedSettings);\r\n\r\n      // Update on server\r\n      await settingsAPI.updateSettings(updatedSettings, userId);\r\n\r\n      // Refresh from server\r\n      await initializeSettings(userId);\r\n\r\n      setIsDirty(false);\r\n      messageApi.success(\"界面设置重置成功\");\r\n    } catch (error) {\r\n      console.error(\"Failed to reset UI settings:\", error);\r\n      messageApi.error(\"重置界面设置失败\");\r\n    } finally {\r\n      setIsSaving(false);\r\n    }\r\n  };\r\n\r\n  // Save settings to server\r\n  const handleSave = async () => {\r\n    try {\r\n      setIsSaving(true);\r\n\r\n      if (!serverSettings) {\r\n        messageApi.error(\"设置未加载\");\r\n        setIsSaving(false);\r\n        return;\r\n      }\r\n\r\n      // Prepare updated settings\r\n      const updatedSettings: Settings = {\r\n        ...serverSettings,\r\n        config: {\r\n          ...serverSettings.config,\r\n          ui: localUISettings,\r\n        },\r\n        created_at: undefined,\r\n        updated_at: undefined,\r\n      };\r\n\r\n      // Update on server\r\n      await settingsAPI.updateSettings(updatedSettings, userId);\r\n\r\n      // Refresh from server to ensure sync\r\n      await initializeSettings(userId);\r\n\r\n      setIsDirty(false);\r\n      messageApi.success(\"界面设置保存成功\");\r\n    } catch (error) {\r\n      console.error(\"Failed to save UI settings:\", error);\r\n      messageApi.error(\"保存界面设置失败\");\r\n    } finally {\r\n      setIsSaving(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\" \">\r\n      {contextHolder}\r\n      <div className=\"flex justify-between items-center mb-4\">\r\n        <h3 className=\"text-lg font-medium\">界面设置</h3>\r\n        <div className=\"space-x-2 inline-flex\">\r\n          <Tooltip title=\"重置为默认值\">\r\n            <Button\r\n              icon={<RotateCcw className=\"w-4 h-4\" />}\r\n              onClick={handleReset}\r\n              disabled={isSaving}\r\n            >\r\n              重置\r\n            </Button>\r\n          </Tooltip>\r\n          <Tooltip title={isDirty ? \"保存您的更改\" : \"没有未保存的更改\"}>\r\n            <Button\r\n              type=\"primary\"\r\n              icon={\r\n                <div className=\"relative\">\r\n                  <Save className=\"w-4 h-4\" />\r\n                  {isDirty && (\r\n                    <div className=\"absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full\" />\r\n                  )}\r\n                </div>\r\n              }\r\n              onClick={handleSave}\r\n              disabled={!isDirty || isSaving}\r\n              loading={isSaving}\r\n            >\r\n              保存\r\n            </Button>\r\n          </Tooltip>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"space-y-0 rounded border border-secondary\">\r\n        <SettingToggle\r\n          checked={localUISettings.show_llm_call_events}\r\n          onChange={(checked) =>\r\n            handleSettingChange(\"show_llm_call_events\", checked)\r\n          }\r\n          label=\"显示LLM事件\"\r\n          description=\"在消息线程中显示详细的LLM调用日志\"\r\n          disabled={isSaving}\r\n        />\r\n\r\n        <SettingToggle\r\n          checked={localUISettings.expanded_messages_by_default ?? false}\r\n          onChange={(checked) =>\r\n            handleSettingChange(\"expanded_messages_by_default\", checked)\r\n          }\r\n          label=\"默认展开消息\"\r\n          description=\"加载时自动展开消息线程\"\r\n          disabled={isSaving}\r\n        />\r\n\r\n        <SettingToggle\r\n          checked={localUISettings.show_agent_flow_by_default ?? false}\r\n          onChange={(checked) =>\r\n            handleSettingChange(\"show_agent_flow_by_default\", checked)\r\n          }\r\n          label=\"默认显示智能体流程\"\r\n          description=\"自动显示智能体流程图\"\r\n          disabled={isSaving}\r\n        />\r\n\r\n        <SettingNumberInput\r\n          value={localUISettings.human_input_timeout_minutes ?? 3}\r\n          onChange={(value) =>\r\n            handleSettingChange(\"human_input_timeout_minutes\", value)\r\n          }\r\n          label=\"人工输入超时时间\"\r\n          description=\"等待用户输入的超时时间（1-30分钟）\"\r\n          disabled={isSaving}\r\n          min={1}\r\n          max={30}\r\n          suffix=\"分钟\"\r\n        />\r\n      </div>\r\n\r\n      <div className=\"mt-4 hidden text-xs text-secondary\">\r\n        <TriangleAlert\r\n          strokeWidth={1.5}\r\n          className=\"inline-block mr-1 h-4 w-4\"\r\n        />{\" \"}\r\n        这些设置会自动保存并在浏览器会话间同步\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default UISettingsPanel;\r\n", "import React, { useState, useEffect, useContext } from \"react\";\r\nimport { Tabs, TabsProps } from \"antd\";\r\nimport {\r\n  ChevronRight,\r\n  RotateCcw,\r\n  Variable,\r\n  <PERSON>ting<PERSON>,\r\n  Palette,\r\n  <PERSON>,\r\n} from \"lucide-react\";\r\nimport { useSettingsStore } from \"./store\";\r\nimport { SettingsSidebar } from \"./sidebar\";\r\nimport { appContext } from \"../../../hooks/provider\";\r\nimport { useAuth } from \"../../../auth/context\";\r\nimport UISettingsPanel from \"./view/ui\";\r\nimport { ModelConfigPanel } from \"./view/modelconfig\";\r\nimport { EnvironmentVariablesPanel } from \"./view/environment\";\r\n\r\nexport const SettingsManager: React.FC = () => {\r\n  const [isSidebarOpen, setIsSidebarOpen] = useState(() => {\r\n    if (typeof window !== \"undefined\") {\r\n      const stored = localStorage.getItem(\"settingsSidebar\");\r\n      return stored !== null ? JSON.parse(stored) : true;\r\n    }\r\n    return true;\r\n  });\r\n\r\n  const { user } = useContext(appContext);\r\n  const { isLoading: authLoading, isAuthenticated } = useAuth();\r\n  const userId = user?.id || \"\";\r\n\r\n  console.log(`🔍 SettingsManager: user:`, user);\r\n  console.log(`🔍 SettingsManager: userId:`, userId);\r\n  console.log(`🔍 SettingsManager: authLoading:`, authLoading);\r\n  console.log(`🔍 SettingsManager: isAuthenticated:`, isAuthenticated);\r\n\r\n  const { serverSettings, resetUISettings, initializeSettings, isLoading } =\r\n    useSettingsStore();\r\n\r\n  // Initialize settings when component mounts and auth is ready\r\n  useEffect(() => {\r\n    console.log(`🔍 SettingsManager: useEffect triggered with userId: ${userId}, authLoading: ${authLoading}`);\r\n    if (!authLoading && userId && isAuthenticated) {\r\n      console.log(`🔍 SettingsManager: Calling initializeSettings for user: ${userId}`);\r\n      initializeSettings(userId);\r\n    } else {\r\n      console.log(`⚠️ SettingsManager: Conditions not met - authLoading: ${authLoading}, userId: ${userId}, isAuthenticated: ${isAuthenticated}`);\r\n    }\r\n  }, [userId, initializeSettings, authLoading, isAuthenticated]);\r\n\r\n  useEffect(() => {\r\n    if (typeof window !== \"undefined\") {\r\n      localStorage.setItem(\"settingsSidebar\", JSON.stringify(isSidebarOpen));\r\n    }\r\n  }, [isSidebarOpen]);\r\n\r\n  if (authLoading || isLoading) {\r\n    return <div className=\"p-8 text-center\">正在加载设置...</div>;\r\n  }\r\n\r\n  // Get model component for the model config panel\r\n  const modelComponent = serverSettings?.config.default_model_client || {\r\n    provider: \"openai\",\r\n    component_type: \"model\",\r\n    label: \"Default Model Client\",\r\n    description: \"Default model client for this environment\",\r\n    config: {\r\n      model: \"gpt-3.5-turbo\",\r\n      temperature: 0.7,\r\n      max_tokens: 1000,\r\n    },\r\n  };\r\n\r\n  const tabItems: TabsProps[\"items\"] = [\r\n    {\r\n      key: \"ui\",\r\n      label: (\r\n        <span className=\"flex items-center gap-2\">\r\n          <Palette className=\"w-4 h-4\" />\r\n          界面设置\r\n        </span>\r\n      ),\r\n      children: <UISettingsPanel userId={userId} />,\r\n    },\r\n    // {\r\n    //   key: \"model\",\r\n    //   label: (\r\n    //     <span className=\"flex items-center gap-2\">\r\n    //       <Brain className=\"w-4 h-4\" />\r\n    //       默认模型\r\n    //     </span>\r\n    //   ),\r\n    //   children: (\r\n    //     <div className=\"mt-4\">\r\n    //       <ModelConfigPanel\r\n    //         modelComponent={modelComponent}\r\n    //         onModelUpdate={async () => {}}\r\n    //       />\r\n    //     </div>\r\n    //   ),\r\n    // },\r\n    // {\r\n    //   key: \"environment\",\r\n    //   label: (\r\n    //     <span className=\"flex items-center gap-2\">\r\n    //       <Variable className=\"w-4 h-4\" />\r\n    //       环境变量\r\n    //     </span>\r\n    //   ),\r\n    //   children: (\r\n    //     <div className=\"mt-4\">\r\n    //       <EnvironmentVariablesPanel\r\n    //         serverSettings={serverSettings}\r\n    //         loading={false}\r\n    //         userId={userId}\r\n    //         initializeSettings={initializeSettings}\r\n    //       />\r\n    //     </div>\r\n    //   ),\r\n    // },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"relative flex h-full w-full\">\r\n      <div\r\n        className={`absolute left-0 top-0 h-full transition-all duration-200 ease-in-out ${\r\n          isSidebarOpen ? \"w-64\" : \"w-12\"\r\n        }`}\r\n      >\r\n        <SettingsSidebar\r\n          isOpen={isSidebarOpen}\r\n          sections={[\r\n            {\r\n              id: \"settings\",\r\n              title: \"设置\",\r\n              icon: Variable,\r\n              content: () => <></>,\r\n            },\r\n          ]}\r\n          currentSection={{\r\n            id: \"settings\",\r\n            title: \"设置\",\r\n            icon: Variable,\r\n            content: () => <></>,\r\n          }}\r\n          onToggle={() => setIsSidebarOpen(!isSidebarOpen)}\r\n          onSelectSection={() => {}}\r\n        />\r\n      </div>\r\n\r\n      <div\r\n        className={`flex-1 transition-all max-w-5xl -mr-6 duration-200 ${\r\n          isSidebarOpen ? \"ml-64\" : \"ml-12\"\r\n        }`}\r\n      >\r\n        <div className=\"p-4 pt-2\">\r\n          <div className=\"flex items-center gap-2 mb-4 text-sm\">\r\n            <span className=\"text-primary font-medium\">设置</span>\r\n          </div>\r\n\r\n          <div className=\"flex items-center gap-2 mb-8 text-sm\">\r\n            <span className=\"text-secondary\">\r\n              管理您的设置和偏好\r\n            </span>\r\n          </div>\r\n\r\n          <div className=\"  rounded-lg shadow-sm\">\r\n            <Tabs\r\n              defaultActiveKey=\"ui\"\r\n              items={tabItems}\r\n              // type=\"card\"\r\n              size=\"large\"\r\n              className=\"settings-tabs\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SettingsManager;\r\n", "import * as React from \"react\";\r\nimport Layout from \"../components/layout\";\r\nimport { graphql } from \"gatsby\";\r\nimport { SettingsManager } from \"../components/views/settings/manager\";\r\n\r\n// markup\r\nconst SettingsPage = ({ data }: any) => {\r\n  return (\r\n    <Layout meta={data.site.siteMetadata} title=\"设置\" link={\"/settings\"}>\r\n      <main style={{ height: \"100%\" }} className=\" h-full \">\r\n        <SettingsManager />\r\n      </main>\r\n    </Layout>\r\n  );\r\n};\r\n\r\nexport const query = graphql`\r\n  query HomePageQuery {\r\n    site {\r\n      siteMetadata {\r\n        description\r\n        title\r\n      }\r\n    }\r\n  }\r\n`;\r\n\r\nexport default SettingsPage;\r\n", "import { getServerUrl } from \"./utils\";\r\n\r\n// baseApi.ts\r\nexport abstract class BaseAPI {\r\n  protected getBaseUrl(): string {\r\n    return getServerUrl();\r\n  }\r\n\r\n  protected getHeaders(): HeadersInit {\r\n    // Get auth token from localStorage\r\n    const token = localStorage.getItem(\"auth_token\");\r\n\r\n    const headers: HeadersInit = {\r\n      \"Content-Type\": \"application/json\",\r\n    };\r\n\r\n    // Add authorization header if token exists\r\n    if (token) {\r\n      headers[\"Authorization\"] = `Bearer ${token}`;\r\n      console.log(`🔍 BaseAPI: Using token from localStorage: ${token.substring(0, 30)}...`);\r\n    } else {\r\n      console.log(\"⚠️ BaseAPI: No token found in localStorage\");\r\n    }\r\n\r\n    return headers;\r\n  }\r\n\r\n  // Other common methods\r\n}\r\n", "// handle border collapse\nfunction compactItemBorder(token, parentCls, options) {\n  const {\n    focusElCls,\n    focus,\n    borderElCls\n  } = options;\n  const childCombinator = borderElCls ? '> *' : '';\n  const hoverEffects = ['hover', focus ? 'focus' : null, 'active'].filter(Boolean).map(n => `&:${n} ${childCombinator}`).join(',');\n  return {\n    [`&-item:not(${parentCls}-last-item)`]: {\n      marginInlineEnd: token.calc(token.lineWidth).mul(-1).equal()\n    },\n    '&-item': Object.assign(Object.assign({\n      [hoverEffects]: {\n        zIndex: 2\n      }\n    }, focusElCls ? {\n      [`&${focusElCls}`]: {\n        zIndex: 2\n      }\n    } : {}), {\n      [`&[disabled] ${childCombinator}`]: {\n        zIndex: 0\n      }\n    })\n  };\n}\n// handle border-radius\nfunction compactItemBorderRadius(prefixCls, parentCls, options) {\n  const {\n    borderElCls\n  } = options;\n  const childCombinator = borderElCls ? `> ${borderElCls}` : '';\n  return {\n    [`&-item:not(${parentCls}-first-item):not(${parentCls}-last-item) ${childCombinator}`]: {\n      borderRadius: 0\n    },\n    [`&-item:not(${parentCls}-last-item)${parentCls}-first-item`]: {\n      [`& ${childCombinator}, &${prefixCls}-sm ${childCombinator}, &${prefixCls}-lg ${childCombinator}`]: {\n        borderStartEndRadius: 0,\n        borderEndEndRadius: 0\n      }\n    },\n    [`&-item:not(${parentCls}-first-item)${parentCls}-last-item`]: {\n      [`& ${childCombinator}, &${prefixCls}-sm ${childCombinator}, &${prefixCls}-lg ${childCombinator}`]: {\n        borderStartStartRadius: 0,\n        borderEndStartRadius: 0\n      }\n    }\n  };\n}\nexport function genCompactItemStyle(token, options = {\n  focus: true\n}) {\n  const {\n    componentCls\n  } = token;\n  const compactCls = `${componentCls}-compact`;\n  return {\n    [compactCls]: Object.assign(Object.assign({}, compactItemBorder(token, compactCls, options)), compactItemBorderRadius(componentCls, compactCls, options))\n  };\n}", "/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst Save = createLucideIcon(\"Save\", [\n  [\n    \"path\",\n    {\n      d: \"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z\",\n      key: \"1c8476\"\n    }\n  ],\n  [\"path\", { d: \"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7\", key: \"1ydtos\" }],\n  [\"path\", { d: \"M7 3v4a1 1 0 0 0 1 1h7\", key: \"t51u73\" }]\n]);\n\nexport { Save as default };\n//# sourceMappingURL=save.js.map\n", "/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst RotateCcw = createLucideIcon(\"RotateCcw\", [\n  [\"path\", { d: \"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8\", key: \"1357e3\" }],\n  [\"path\", { d: \"M3 3v5h5\", key: \"1xhq8a\" }]\n]);\n\nexport { RotateCcw as default };\n//# sourceMappingURL=rotate-ccw.js.map\n"], "names": ["Triangle<PERSON><PERSON><PERSON>", "d", "key", "DEFAULT_UI_SETTINGS", "show_llm_call_events", "expanded_messages_by_default", "show_agent_flow_by_default", "human_input_timeout_minutes", "useSettingsStore", "create", "persist", "set", "get", "serverSettings", "isLoading", "error", "uiSettings", "initializeSettings", "async", "settings", "settingsAPI", "getSettings", "userId", "config", "ui", "getUISettings", "console", "updateUISettings", "partialSettings", "newUISettings", "resetUISettings", "Promise", "resolve", "name", "partialize", "state", "SettingsAPI", "_BaseAPI", "apply", "arguments", "_inherits<PERSON><PERSON>e", "_proto", "prototype", "log", "response", "fetch", "this", "getBaseUrl", "headers", "getHeaders", "status", "data", "json", "Error", "detail", "message", "updateSettings", "settingsData", "user_id", "method", "body", "JSON", "stringify", "BaseAPI", "Palette", "createLucideIcon", "cx", "cy", "r", "fill", "Variable", "x1", "x2", "y1", "y2", "SettingsSidebar", "_ref", "isOpen", "sections", "currentSection", "onToggle", "onSelectSection", "React", "className", "length", "<PERSON><PERSON><PERSON>", "title", "onClick", "PanelLeftClose", "strokeWidth", "map", "section", "id", "icon", "PanelLeftOpen", "SettingToggle", "checked", "onChange", "label", "description", "disabled", "type", "e", "target", "replace", "toLowerCase", "htmlFor", "SettingNumberInput", "_ref2", "value", "min", "max", "suffix", "newValue", "parseInt", "isNaN", "_ref3", "_localUISettings$expa", "_localUISettings$show", "_localUISettings$huma", "storeUISettings", "localUISettings", "setLocalUISettings", "useState", "isDirty", "setIsDirty", "isSaving", "setIsSaving", "messageApi", "contextHolder", "useMessage", "useEffect", "handleSettingChange", "prev", "<PERSON><PERSON>", "RotateCcw", "updatedSettings", "created_at", "undefined", "updated_at", "success", "Save", "loading", "SettingsManager", "isSidebarOpen", "setIsSidebarOpen", "window", "stored", "localStorage", "getItem", "parse", "user", "useContext", "appContext", "authLoading", "isAuthenticated", "useAuth", "setItem", "default_model_client", "tabItems", "children", "UISettingsPanel", "content", "Tabs", "defaultActiveKey", "items", "size", "Layout", "meta", "site", "siteMetadata", "link", "style", "height", "getServerUrl", "token", "substring", "compactItemBorder", "parentCls", "options", "focusElCls", "focus", "borderElCls", "childC<PERSON><PERSON>", "hoverEffects", "filter", "Boolean", "n", "join", "marginInlineEnd", "calc", "lineWidth", "mul", "equal", "Object", "assign", "zIndex", "compactItemBorderRadius", "prefixCls", "borderRadius", "borderStartEndRadius", "borderEndEndRadius", "borderStartStartRadius", "borderEndStartRadius", "genCompactItemStyle", "componentCls", "compactCls"], "sourceRoot": ""}