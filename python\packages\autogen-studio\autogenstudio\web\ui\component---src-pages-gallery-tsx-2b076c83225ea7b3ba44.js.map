{"version": 3, "file": "component---src-pages-gallery-tsx-2b076c83225ea7b3ba44.js", "mappings": ";oJASA,MAAMA,GAAQ,E,QAAA,GAAiB,QAAS,CACtC,CAAC,SAAU,CAAEC,GAAI,KAAMC,GAAI,KAAMC,EAAG,KAAMC,IAAK,WAC/C,CAAC,OAAQ,CAAEC,EAAG,kDAAmDD,IAAK,WACtE,CAAC,OAAQ,CAAEC,EAAG,WAAYD,IAAK,Y,k9mECHjC,MAAME,GAAY,E,QAAA,GAAiB,YAAa,CAC9C,CAAC,OAAQ,CAAED,EAAG,qDAAsDD,IAAK,WACzE,CAAC,OAAQ,CAAEC,EAAG,aAAcD,IAAK,WACjC,CAAC,OAAQ,CAAEC,EAAG,sDAAuDD,IAAK,WAC1E,CAAC,OAAQ,CAAEC,EAAG,YAAaD,IAAK,Y,mQCc3B,MAAMG,EAAgDC,IAUtD,IAVuD,OAC5DC,EAAM,UACNC,EAAS,eACTC,EAAc,SACdC,EAAQ,gBACRC,EAAe,gBACfC,EAAe,gBACfC,EAAe,cACfC,EAAa,UACbC,GAAY,GACbT,EAEC,OAAKC,EA8BHS,EAAAA,cAAA,OAAKC,UAAU,oCAEbD,EAAAA,cAAA,OAAKC,UAAU,kFACbD,EAAAA,cAAA,OAAKC,UAAU,2BACbD,EAAAA,cAAA,QAAMC,UAAU,4BAA2B,aAC3CD,EAAAA,cAAA,QAAMC,UAAU,wDACbT,EAAUU,SAGfF,EAAAA,cAACG,EAAAA,EAAO,CAACC,MAAM,iBACbJ,EAAAA,cAAA,UACEK,QAASX,EACTO,UAAU,gKAEVD,EAAAA,cAACM,EAAAA,EAAc,CAACC,YAAa,IAAKN,UAAU,eAMlDD,EAAAA,cAAA,OAAKC,UAAU,qBACbD,EAAAA,cAAA,OAAKC,UAAU,eACbD,EAAAA,cAACG,EAAAA,EAAO,CAACC,MAAM,sBACbJ,EAAAA,cAACQ,EAAAA,GAAM,CACLC,KAAK,UACLR,UAAU,SACVS,KAAMV,EAAAA,cAACW,EAAAA,EAAI,CAACV,UAAU,YACtBI,QAAST,GACV,kBAQPI,EAAAA,cAAA,OAAKC,UAAU,oCACbD,EAAAA,cAAA,OAAKC,UAAU,QAAO,iBACrBF,GAAaC,EAAAA,cAACZ,EAAAA,EAAS,CAACa,UAAU,gCAInCF,GAAkC,IAArBP,EAAUU,QACvBF,EAAAA,cAAA,OAAKC,UAAU,4EACbD,EAAAA,cAACY,EAAAA,EAAI,CAACX,UAAU,wCAAwC,sBAK5DD,EAAAA,cAAA,OAAKC,UAAU,+CACZT,EAAUqB,IAAKC,GACdd,EAAAA,cAAA,OAAKd,IAAK4B,EAAQC,GAAId,UAAU,6BAC9BD,EAAAA,cAAA,OACEC,UAAW,+EACTR,aAAc,EAAdA,EAAgBsB,MAAOD,EAAQC,GAAK,YAAc,iBAGrDD,GAAWA,EAAQE,QAAUF,EAAQE,OAAOC,YAC3CjB,EAAAA,cAAA,OACEC,UAAW,8EACTR,aAAc,EAAdA,EAAgBsB,MAAOD,EAAQC,GAC3B,6BACA,sBAENV,QAASA,IAAMV,EAAgBmB,IAG/Bd,EAAAA,cAAA,OAAKC,UAAU,6CACbD,EAAAA,cAAA,OAAKC,UAAU,0CACbD,EAAAA,cAAA,OAAKC,UAAU,mBACbD,EAAAA,cAAA,QAAMC,UAAU,uBACba,EAAQE,OAAOE,OAGnBJ,EAAQE,OAAOG,KACdnB,EAAAA,cAACG,EAAAA,EAAO,CAACC,MAAM,kBACbJ,EAAAA,cAAClB,EAAAA,EAAK,CAACmB,UAAU,2CAIvBD,EAAAA,cAAA,OAAKC,UAAU,sFACZa,EAAQE,OAAOG,KACdnB,EAAAA,cAACG,EAAAA,EAAO,CAACC,MAAM,gBACbJ,EAAAA,cAACQ,EAAAA,GAAM,CACLC,KAAK,OACLW,KAAK,QACLnB,UAAU,uBACVS,KAAMV,EAAAA,cAACZ,EAAAA,EAAS,CAACa,UAAU,YAC3BI,QAAUgB,IACRA,EAAEC,kBACFxB,EAAcgB,EAAQC,QAK9Bf,EAAAA,cAACG,EAAAA,EAAO,CACNC,MACuB,IAArBZ,EAAUU,OACN,iCACA,kBAGNF,EAAAA,cAACQ,EAAAA,GAAM,CACLC,KAAK,OACLW,KAAK,QACLnB,UAAU,uBACVsB,QAAM,EACNC,SAA+B,IAArBhC,EAAUU,OACpBQ,KAAMV,EAAAA,cAACyB,EAAAA,EAAM,CAACxB,UAAU,yBACxBI,QAAUgB,IACRA,EAAEC,kBACFzB,EAAgBiB,EAAQC,UAQlCf,EAAAA,cAAA,OAAKC,UAAU,uDACbD,EAAAA,cAAA,QAAMC,UAAU,yCAAwC,IACpDa,EAAQE,OAAOU,SAASC,SAE5B3B,EAAAA,cAAA,OAAKC,UAAU,2BACbD,EAAAA,cAAC4B,EAAAA,EAAO,CAAC3B,UAAU,YACnBD,EAAAA,cAAA,YACG6B,OAAOC,OAAOhB,EAAQE,OAAOC,YAAYc,OACxC,CAACC,EAAKC,IAAQD,EAAMC,EAAI/B,OACxB,GACC,IAAI,gBAOZY,EAAQoB,YACPlC,EAAAA,cAAA,OAAKC,UAAU,uDACbD,EAAAA,cAAA,aAAOmC,EAAAA,EAAAA,IAAsBrB,EAAQoB,mBAtKnDlC,EAAAA,cAAA,OAAKC,UAAU,oCACbD,EAAAA,cAAA,OAAKC,UAAU,aACbD,EAAAA,cAACG,EAAAA,EAAO,CAACC,MAAO,cAAcZ,EAAUU,WACtCF,EAAAA,cAAA,UACEK,QAASX,EACTO,UAAU,gKAEVD,EAAAA,cAACoC,EAAAA,EAAa,CAAC7B,YAAa,IAAKN,UAAU,eAKjDD,EAAAA,cAAA,OAAKC,UAAU,mBACbD,EAAAA,cAACG,EAAAA,EAAO,CAACC,MAAM,sBACbJ,EAAAA,cAACQ,EAAAA,GAAM,CACLC,KAAK,OACLR,UAAU,iCACVI,QAAST,EACTc,KAAMV,EAAAA,cAACW,EAAAA,EAAI,CAACV,UAAU,kBAgKpC,I,wCClNA,MAAMoC,GAAQ,EAAAC,EAAA,GAAiB,QAAS,CACtC,CAAC,OAAQ,CAAEnD,EAAG,UAAWD,IAAK,WAC9B,CAAC,OAAQ,CAAEC,EAAG,wCAAyCD,IAAK,WAC5D,CAAC,OAAQ,CAAEC,EAAG,qCAAsCD,IAAK,a,0ECH3D,MAAMqD,GAAY,EAAAD,EAAA,GAAiB,YAAa,CAC9C,CAAC,OAAQ,CAAEnD,EAAG,6CAA8CD,IAAK,UACjE,CAAC,OAAQ,CAAEsD,MAAO,KAAMC,OAAQ,KAAMC,EAAG,IAAKC,EAAG,IAAKC,GAAI,IAAK1D,IAAK,a,gECyC/D,MAAM2D,EAAkD,CAC7D,CACE9B,GAAI,mBACJ+B,MAAO,mBACPC,YAAa,kDACbC,SAAUC,EAAAA,GAAUC,iBACpBC,eAAgB,OAChBxB,QAAS,EACTyB,kBAAmB,EACnBpC,OAAQ,CACNqC,aAAc,GACdC,sBAAuB,CACrBN,SAAUC,EAAAA,GAAUM,aACpBJ,eAAgB,cAChBnC,OAAQ,CAAEwC,KAAM,cAElBC,UAAW,KAGf,CACE1C,GAAI,gBACJ+B,MAAO,gBACPC,YAAa,2DACbC,SAAUC,EAAAA,GAAUS,cACpBP,eAAgB,OAChBxB,QAAS,EACTyB,kBAAmB,EACnBpC,OAAQ,CACNqC,aAAc,GACdM,aAAc,CACZX,SAAUC,EAAAA,GAAUW,OACpBT,eAAgB,QAChBnC,OAAQ,CAAE6C,MAAO,gBAEnBP,sBAAuB,CACrBN,SAAUC,EAAAA,GAAUM,aACpBJ,eAAgB,cAChBnC,OAAQ,CAAEwC,KAAM,cAElBC,UAAW,GACXK,gBACE,mFACFC,wBAAwB,KAMjBC,EAAoD,CAC/D,CACEjD,GAAI,kBACJ+B,MAAO,kBACPC,YAAa,gDACbC,SAAUC,EAAAA,GAAUgB,gBACpBd,eAAgB,QAChBxB,QAAS,EACTyB,kBAAmB,EACnBpC,OAAQ,CACNE,KAAM,kBACN6B,YACE,8DACFmB,eACE,wHACFP,aAAc,CACZX,SAAUC,EAAAA,GAAUW,OACpBT,eAAgB,QAChBnC,OAAQ,CAAE6C,MAAO,gBAEnBM,UAAW,GAEXC,qBAAqB,EACrBC,yBAA0B,WAC1BC,qBAAqB,IAGzB,CACEvD,GAAI,mBACJ+B,MAAO,mBACPC,YAAa,uDACbC,SAAUC,EAAAA,GAAUsB,WACpBpB,eAAgB,QAChBxB,QAAS,EACTyB,kBAAmB,EACnBpC,OAAQ,CACNE,KAAM,aACN6B,YAAa,wDAGjB,CACEhC,GAAI,mBACJ+B,MAAO,mBACPC,YAAa,uDACbC,SAAUC,EAAAA,GAAUuB,WACpBrB,eAAgB,QAChBxB,QAAS,EACTyB,kBAAmB,EACnBpC,OAAQ,CACNE,KAAM,aACN6B,YACE,+DACFY,aAAc,CACZX,SAAUC,EAAAA,GAAUW,OACpBT,eAAgB,QAChBnC,OAAQ,CAAE6C,MAAO,WAEnBY,UAAU,EACVC,WAAY,yBACZC,iBAAiB,EACjBC,qBAAqB,EACrBC,SAAS,EACTC,oBAAoB,KAMbC,EAAoD,CAC/D,CACEhE,GAAI,qBACJ+B,MAAO,qBACPC,YAAa,sDACbC,SAAUC,EAAAA,GAAUW,OACpBT,eAAgB,QAChBxB,QAAS,EACTyB,kBAAmB,EACnBpC,OAAQ,CACN6C,MAAO,cACPmB,YAAa,GACbC,WAAY,OAGhB,CACElE,GAAI,gBACJ+B,MAAO,gBACPC,YAAa,2DACbC,SAAUC,EAAAA,GAAUW,OACpBT,eAAgB,QAChBxB,QAAS,EACTyB,kBAAmB,EACnBpC,OAAQ,CACN6C,MAAO,SACPmB,YAAa,GACbC,WAAY,OAGhB,CACElE,GAAI,2BACJ+B,MAAO,2BACPC,YAAa,+CACbC,SAAUC,EAAAA,GAAUiC,aACpB/B,eAAgB,QAChBxB,QAAS,EACTyB,kBAAmB,EACnBpC,OAAQ,CACN6C,MAAO,cACPsB,eAAgB,0CAChBC,iBAAkB,cAClBC,YAAa,aACbL,YAAa,GACbC,WAAY,OAGhB,CACElE,GAAI,4BACJ+B,MAAO,4BACPC,YAAa,0DACbC,SAAUC,EAAAA,GAAUqC,UACpBnC,eAAgB,QAChBxB,QAAS,EACTyB,kBAAmB,EACnBpC,OAAQ,CACN6C,MAAO,6BACPoB,WAAY,KACZD,YAAa,MAMNO,EAAkD,CAC7D,CACExE,GAAI,gBACJ+B,MAAO,gBACPC,YAAa,wDACbC,SAAUC,EAAAA,GAAUuC,cACpBrC,eAAgB,OAChBxB,QAAS,EACTyB,kBAAmB,EACnBpC,OAAQ,CACNE,KAAM,cACN6B,YAAa,kDACb0C,YAAa,8UAabC,eAAgB,GAChBC,0BAA0B,IAG9B,CACE5E,GAAI,sBACJ+B,MAAO,sBACPC,YAAa,8CACbC,SAAUC,EAAAA,GAAU2C,2BACpBzC,eAAgB,OAChBxB,QAAS,EACTyB,kBAAmB,EACnBpC,OAAQ,CACN6E,SAAU,CACR7C,SACE,gEACFhC,OAAQ,CACN8E,QAAS,GACTC,SAAU,WACVC,iBAAkB,YAClBC,oBAAoB,IAGxBlD,YAAa,8CACb7B,KAAM,oBAMCgF,EAA4D,CACvE,CACEnF,GAAI,mBACJ+B,MAAO,mBACPC,YAAa,yCACbC,SAAUC,EAAAA,GAAUkD,iBACpBhD,eAAgB,YAChBxB,QAAS,EACTyB,kBAAmB,EACnBpC,OAAQ,CACNoF,MAAO,KAGX,CACErF,GAAI,sBACJ+B,MAAO,mBACPC,YAAa,iDACbC,SAAUC,EAAAA,GAAUoD,cACpBlD,eAAgB,YAChBxB,QAAS,EACTyB,kBAAmB,EACnBpC,OAAQ,CACNsF,cAAe,CACb7F,KAAM,oBACN8F,QAAS,MACTC,KAAM,CAAC,2CACPC,IAAK,CAAC,EACNC,qBAAsB,MAI5B,CACE3F,GAAI,oBACJ+B,MAAO,iBACPC,YAAa,uDACbC,SAAUC,EAAAA,GAAUoD,cACpBlD,eAAgB,YAChBxB,QAAS,EACTyB,kBAAmB,EACnBpC,OAAQ,CACNsF,cAAe,CACb7F,KAAM,kBACNU,IAAK,4BACLwF,QAAS,CACPC,cAAe,0BAEjBd,QAAS,GACTe,iBAAkB,MAIxB,CACE9F,GAAI,qBACJ+B,MAAO,kBACPC,YAAa,oDACbC,SAAUC,EAAAA,GAAUoD,cACpBlD,eAAgB,YAChBxB,QAAS,EACTyB,kBAAmB,EACnBpC,OAAQ,CACNsF,cAAe,CACb7F,KAAM,6BACNU,IAAK,4BACLwF,QAAS,CACP,eAAgB,mBAChBC,cAAe,0BAEjBd,QAAS,GACTe,iBAAkB,GAClBC,oBAAoB,MAOfC,EAAgE,CAC3E,CACEhG,GAAI,2BACJ+B,MAAO,2BACPC,YAAa,8CACbC,SAAUC,EAAAA,GAAUM,aACpBJ,eAAgB,cAChBxB,QAAS,EACTyB,kBAAmB,EACnBpC,OAAQ,CACNwC,KAAM,cAGV,CACEzC,GAAI,0BACJ+B,MAAO,0BACPC,YAAa,+CACbC,SAAUC,EAAAA,GAAU+D,YACpB7D,eAAgB,cAChBxB,QAAS,EACTyB,kBAAmB,EACnBpC,OAAQ,CACNiG,aAAc,GACdC,qBAAqB,IAGzB,CACEnG,GAAI,2BACJ+B,MAAO,2BACPC,YAAa,2CACbC,SAAUC,EAAAA,GAAUkE,aACpBhE,eAAgB,cAChBxB,QAAS,EACTyB,kBAAmB,EACnBpC,OAAQ,CAAC,GAEX,CACED,GAAI,0BACJ+B,MAAO,0BACPC,YAAa,gDACbC,SAAUC,EAAAA,GAAUmE,YACpBjE,eAAgB,cAChBxB,QAAS,EACTyB,kBAAmB,EACnBpC,OAAQ,CACNqG,gBAAiB,MAGrB,CACEtG,GAAI,sBACJ+B,MAAO,sBACPC,YAAa,uCACbC,SAAUC,EAAAA,GAAUqE,QACpBnE,eAAgB,cAChBxB,QAAS,EACTyB,kBAAmB,EACnBpC,OAAQ,CACNuG,gBAAiB,MAGrB,CACExG,GAAI,sBACJ+B,MAAO,sBACPC,YAAa,wDACbC,SAAUC,EAAAA,GAAUuE,QACpBrE,eAAgB,cAChBxB,QAAS,EACTyB,kBAAmB,EACnBpC,OAAQ,CACNyG,OAAQ,SAGZ,CACE1G,GAAI,2BACJ+B,MAAO,2BACPC,YAAa,0CACbC,SAAUC,EAAAA,GAAUyE,aACpBvE,eAAgB,cAChBxB,QAAS,EACTyB,kBAAmB,EACnBpC,OAAQ,CACN2G,QAAS,CAAC,YAGd,CACE5G,GAAI,2BACJ+B,MAAO,2BACPC,YAAa,2CACbC,SAAUC,EAAAA,GAAU2E,aACpBzE,eAAgB,cAChBxB,QAAS,EACTyB,kBAAmB,EACnBpC,OAAQ,CACN6G,YAAQC,IAGZ,CACE/G,GAAI,uBACJ+B,MAAO,uBACPC,YAAa,+DACbC,SAAUC,EAAAA,GAAU8E,SACpB5E,eAAgB,cAChBxB,QAAS,EACTyB,kBAAmB,EACnBpC,OAAQ,CAAC,GAEX,CACED,GAAI,iBACJ+B,MAAO,iBACPC,YAAa,+CACbC,SAAUC,EAAAA,GAAU+E,eACpB7E,eAAgB,cAChBxB,QAAS,EACTyB,kBAAmB,EACnBpC,OAAQ,CACNiH,WAAY,CACV,CACEjF,SAAUC,EAAAA,GAAUM,aACpBJ,eAAgB,cAChBnC,OAAQ,CAAEwC,KAAM,cAElB,CACER,SAAUC,EAAAA,GAAU+D,YACpB7D,eAAgB,cAChBnC,OAAQ,CAAEiG,aAAc,QAKhC,CACElG,GAAI,kBACJ+B,MAAO,kBACPC,YAAa,wCACbC,SAAUC,EAAAA,GAAUiF,gBACpB/E,eAAgB,cAChBxB,QAAS,EACTyB,kBAAmB,EACnBpC,OAAQ,CACNiH,WAAY,CACV,CACEjF,SAAUC,EAAAA,GAAUM,aACpBJ,eAAgB,cAChBnC,OAAQ,CAAEwC,KAAM,kBAElB,CACER,SAAUC,EAAAA,GAAU+D,YACpB7D,eAAgB,cAChBnC,OAAQ,CAAEiG,aAAc,QAQrBkB,EAAsB,CACjCC,KAAMvF,EACNwF,MAAOrE,EACPH,MAAOkB,EACPuD,KAAM/C,EACNpB,UAAW+B,EACXqC,YAAaxB,GAUR,SAASyB,EACdC,EACAC,GAGA,OADkBP,EAAoBM,GACrBE,KAAMC,GAAaA,EAAS7H,KAAO2H,EACtD,CASO,SAASG,EACdH,EACAD,EACAK,GAEA,MAAMF,EAAWJ,EAAgBC,EAAeC,GAChD,IAAKE,EACH,MAAM,IAAIG,MACR,YAAYL,kCAA2CD,KAI3D,MAAO,CACLzF,SAAU4F,EAAS5F,SACnBG,eAAgByF,EAASzF,eACzBxB,QAASiH,EAASjH,QAClByB,kBAAmBwF,EAASxF,kBAC5BL,YAAa6F,EAAS7F,YACtB/B,OAAQ4H,EAAS5H,OACjB8B,MAAO8F,EAAS9F,SACbgG,EAEP,CAmBO,SAASE,EACdN,EACAO,GAEA,MAAML,EAAWJ,EAAgB,YAAaE,GAC9C,IAAKE,EACH,MAAM,IAAIG,MAAM,sBAAsBL,eAGxC,OAAOG,EAA4BH,EAAY,YAAa,CAC1D5F,MAAOmG,GAAe,OAAOL,EAAS9F,SAE1C,CAWO,SAASoG,EACdT,GAEA,MAAMU,EAxFD,SACLV,GAEA,OAAON,EAAoBM,IAAkB,EAC/C,CAoFoBW,CAAoBX,GACtC,OAAOU,EAAUtI,IAAK+H,IAAQ,CAC5B1J,IAAK0J,EAAS7H,GACd+B,MAAO8F,EAAS9F,MAChBC,YAAa6F,EAAS7F,YACtB2F,WAAYE,EAAS7H,KAEzB,CAEO,SAASsI,EACdZ,EACAC,EACAO,GAEA,MAAML,EAAWJ,EAAgBC,EAAeC,GAChD,IAAKE,EACH,MAAM,IAAIG,MAAM,GAAGN,cAA0BC,eAG/C,OAAOG,EAA4BH,EAAYD,EAAe,CAC5D3F,MAAOmG,GAAe,OAAOL,EAAS9F,SAE1C,C,gDC3lBA,MA8BawG,GAA4DhK,IAYlE,IAZmE,cACxEmJ,EAAa,QACb3H,EAAO,iBACPyI,EAAgB,SAChB/H,GAAW,EAAK,SAChBgI,GAAW,EAAI,YACfC,GAAc,EAAI,KAClBrI,EAAO,SAAQ,KACfX,EAAO,UAAS,UAChBR,EAAY,GAAE,WACdyJ,EAAU,eACVC,GACDrK,EACC,MAAOsK,EAAYC,GAAiBC,EAAAA,GAAQC,aAwCtCC,EAAiCA,CACrCvB,EACAC,KAEA,MAAMuB,EAvFcxB,KAC+B,CACnDL,KAAM,QACNC,MAAO,SACPxE,MAAO,SACPyE,KAAM,QACNnE,UAAW,cACXoE,YAAa,gBAEAE,IA8EIyB,CAAezB,GAEhC,IACE,IAAI0B,EAEJ,OAAQ1B,GACN,IAAK,OACH0B,EDogBH,SACLzB,EACAO,GAEA,OAAOI,EAAgC,OAAQX,EAAYO,EAC7D,CCzgByBmB,CAAuB1B,GACtC,MACF,IAAK,QACHyB,ED4gBH,SACLzB,EACAO,GAEA,OAAOI,EAAgC,QAASX,EAAYO,EAC9D,CCjhByBoB,CAAwB3B,GACvC,MACF,IAAK,QACHyB,EDohBH,SACLzB,EACAO,GAEA,OAAOI,EAAgC,QAASX,EAAYO,EAC9D,CCzhByBqB,CAAwB5B,GACvC,MACF,IAAK,OACHyB,ED4hBH,SACLzB,EACAO,GAEA,OAAOI,EAAgC,OAAQX,EAAYO,EAC7D,CCjiByBsB,CAAuB7B,GACtC,MACF,IAAK,YACHyB,EAAenB,EAA4BN,GAC3C,MACF,IAAK,cACHyB,EDiiBH,SACLzB,EACAO,GAEA,OAAOI,EACL,cACAX,EACAO,EAEJ,CC1iByBuB,CAA8B9B,GAC7C,MACF,QACE,MAAM,IAAIK,MAAM,+BAA+BN,KAGnDc,EAAiBY,EAAcF,EACjC,CAAE,MAAOQ,GACPC,QAAQD,MAAM,kBAAkBhC,mBAAgCgC,GAChEb,EAAWa,MAAM,oBAAoBhC,IACvC,GAGIU,EA3EJV,KAEA,IAAIU,EAAuC,GAE3C,OAAQV,GACN,IAAK,OACHU,ED0iBCD,EAAwB,QCziBzB,MACF,IAAK,QACHC,EDkjBCD,EAAwB,SCjjBzB,MACF,IAAK,QACHC,ED0jBCD,EAAwB,SCzjBzB,MACF,IAAK,OACHC,EDkkBCD,EAAwB,QCjkBzB,MACF,IAAK,YACHC,EDkeCjD,EAAoBrF,IAAK+H,IAAQ,CACtC1J,IAAK0J,EAAS7H,GACd+B,MAAO8F,EAAS9F,MAChBC,YAAa6F,EAAS7F,YACtB2F,WAAYE,EAAS7H,MCrejB,MACF,IAAK,cACHoI,EDukBCD,EAAwB,eCtkBzB,MACF,QACEC,EAAY,GAQhB,OAJIQ,IACFR,EAAYA,EAAUwB,OAAOhB,IAGxBR,GA2CSyB,CAA4BnC,GAG9C,GAAyB,IAArBU,EAAUjJ,OACZ,OAAO,KAGT,MAAM2K,EACJnB,GACA,OAAOjB,EAAcqC,OAAO,GAAGC,cAAgBtC,EAAcuC,MAAM,KAErE,OACEhL,EAAAA,cAAAA,EAAAA,SAAA,KACG6J,EACD7J,EAAAA,cAACiL,GAAAA,EAAQ,CACPC,KAAM,CACJC,MAAOhC,EAAUtI,IAAK+H,IAAQ,CAC5B1J,IAAK0J,EAAS1J,IACd4D,MACE9C,EAAAA,cAAA,OAAKC,UAAU,QACbD,EAAAA,cAAA,OAAKC,UAAU,eAAe2I,EAAS9F,OACvC9C,EAAAA,cAAA,OAAKC,UAAU,0BACZ2I,EAAS7F,cAIhB1C,QAASA,IACP2J,EACEvB,EACAG,EAASF,gBAIjB0C,QAAS,CAAC,SACV5J,SAAUA,GAEVxB,EAAAA,cAACQ,EAAAA,GAAM,CACLC,KAAMA,EACNW,KAAMA,EACNV,KAAM8I,EAAWxJ,EAAAA,cAACW,EAAAA,EAAI,CAACV,UAAU,iBAAe6H,EAChDtG,SAAUA,EACVvB,UAAW,2BAA2BA,KAErC4K,EACApB,GAAezJ,EAAAA,cAACqL,GAAAA,EAAW,CAACpL,UAAU,gBClIjD,MAAMiK,GAAkBzB,IAC+B,CACnDL,KAAM,QACNC,MAAO,SACPxE,MAAO,SACPyE,KAAM,QACNnE,UAAW,cACXoE,YAAa,gBAEAE,IAUX6C,GAOFhM,IAAA,IAAC,KACHiM,EAAI,OACJC,EAAM,YACNC,EAAW,SACXC,EAAQ,MACRC,EAAK,YACLC,EAAW,SACXpK,GAAW,GACZlC,EAAA,OACCU,EAAAA,cAAA,OACEC,UAAW,sDACTuB,EAAW,gCAAkC,kBAE/CnB,QAASA,KAAOmB,GAAYgK,EAAOD,EAAMI,IAEzC3L,EAAAA,cAAA,OAAKC,UAAU,wEACbD,EAAAA,cAAA,OAAKC,UAAU,0CACZsL,EAAKzI,OAAS,qBAEjB9C,EAAAA,cAAA,OAAKC,UAAU,cACZ2L,GACC5L,EAAAA,cAACQ,EAAAA,GAAM,CACLJ,MAAM,SACNK,KAAK,OACLR,UAAU,oIACVS,KAAMV,EAAAA,cAACqC,EAAK,CAACpC,UAAU,gBACvBuB,SAAUA,EACVnB,QAAUgB,IACRA,EAAEC,kBACGE,GAAUkK,EAASH,EAAMI,MAIpC3L,EAAAA,cAACQ,EAAAA,GAAM,CACLJ,MAAM,YACNK,KAAK,OACLR,UAAU,oGACVS,KAAMV,EAAAA,cAAC6L,EAAAA,EAAI,CAAC5L,UAAU,gBACtBuB,SAAUA,EACVnB,QAAUgB,IACRA,EAAEC,kBACGE,GAAUiK,EAAYF,EAAMI,MAGrC3L,EAAAA,cAACQ,EAAAA,GAAM,CACLJ,MAAM,OACNK,KAAK,OACLR,UAAU,oGACVS,KAAMV,EAAAA,cAAC8L,EAAAA,EAAI,CAAC7L,UAAU,gBACtBuB,SAAUA,EACVnB,QAAUgB,IACRA,EAAEC,kBACGE,GAAUgK,EAAOD,EAAMI,QAKpC3L,EAAAA,cAAA,OAAKC,UAAU,iBACbD,EAAAA,cAAA,OAAKC,UAAU,sDACY,cAAxBsL,EAAKpI,iBAAkC4I,EAAAA,EAAAA,IAAeR,IACrDvL,EAAAA,cAACgM,GAAAA,EAAI,CAACtL,KAAK,MAAMU,KAAM,EAAGnB,UAAU,iBACnC,IACHD,EAAAA,cAAA,QAAMC,UAAU,gBACbsL,EAAKzI,OAAS,sBAGnB9C,EAAAA,cAAA,OAAKC,UAAU,wCACZsL,EAAKvI,UAERhD,EAAAA,cAAA,OAAKC,UAAU,yDACbD,EAAAA,cAACiM,EAAAA,GAAe,CACdC,QAASX,EAAKxI,aAAe,GAC7BoJ,gBAAgB,EAChBC,cAAe,SAQnBC,GAMFC,IAAA,IAAC,MAAEnB,EAAK,MAAE/K,EAAK,SAAEoB,GAAW,KAAU+K,GAASD,EAAA,OACjDtM,EAAAA,cAAA,WACEA,EAAAA,cAAA,OAAKC,UAAU,oFACZkL,EAAMtK,IAAI,CAAC0K,EAAMiB,IAChBxM,EAAAA,cAACsL,GAAazJ,OAAA4K,OAAA,CACZvN,IAAKsN,EACLjB,KAAMA,EACNI,MAAOa,EACPZ,YAAaT,EAAMjL,OAAS,EAC5BsB,SAAUA,GACN+K,QAORG,GAAU,CACdtE,KAAMuE,EAAAA,EACNtE,MAAOuE,EAAAA,EACPtE,KAAMuE,EAAAA,EACNhJ,MAAOiJ,EAAAA,EACPvE,YAAawE,EAAAA,EACb5I,UAAW5B,GAGAyK,GAIRC,IAA8C,IAA7C,QAAEnM,EAAO,OAAEoM,EAAM,mBAAEC,GAAoBF,EAC3C,IAAKnM,EAAQE,OAAOC,WAClB,OAAOjB,EAAAA,cAAA,OAAKC,UAAU,kBAAiB,uBAEzC,MAAM,EAACmN,EAAiB,EAACC,IAAuBC,EAAAA,EAAAA,UAItC,OACJ,EAACC,EAAU,EAACC,IAAgBF,EAAAA,EAAAA,UAAyB,SACrD,EAACG,EAAiB,EAACC,IAAuBJ,EAAAA,EAAAA,WAAS,IACnD,EAACK,EAAc,EAACC,IAAoBN,EAAAA,EAAAA,WAAS,IAC7C,EAACO,EAAY,EAACC,IAAkBR,EAAAA,EAAAA,UAAkBxM,IAClD,EAACiN,EAAU,EAACC,IAAgBV,EAAAA,EAAAA,UAAiB,KAC7C,EAACW,EAAkB,EAACC,IAAwBZ,EAAAA,EAAAA,WAAS,IACrD,EAACa,EAAQ,EAACC,IAAcd,EAAAA,EAAAA,WAAS,IACjC,EAACe,EAAS,EAACC,IAAehB,EAAAA,EAAAA,UAASxM,EAAQE,OAAOE,OAClD,EAACqN,EAAgB,EAACC,IAAsBlB,EAAAA,EAAAA,UAC5CxM,EAAQE,OAAOU,SAASqB,cAGnB6G,EAAYC,GAAiBC,EAAAA,GAAQC,aACtC0E,GAAYC,EAAAA,EAAAA,QAAO,OAEzBC,EAAAA,EAAAA,WAAU,KACRL,EAAYxN,EAAQE,OAAOE,MAC3BsN,EAAmB1N,EAAQE,OAAOU,SAASqB,aAC3C+K,EAAehN,GACfkN,EAAaY,KAAKC,UAAU/N,EAAS,KAAM,IAC3CoN,GAAqB,GACrBE,GAAW,GACXZ,EAAa,QACbH,EAAoB,MACpBO,GAAiB,IAChB,CAAC9M,EAAQC,KAEZ,MAAM+N,EAAgBA,CACpB7E,EACA8E,KAIA,MAAMtP,EAAiBkO,EAAgBE,EAAc/M,EAC/CkO,EAAiB,IAClBvP,EACHuB,OAAQ,IACHvB,EAAeuB,OAClBC,WAAY,IACPxB,EAAeuB,OAAOC,WACzB,CAACgJ,GAAW8E,EAAQtP,EAAeuB,OAAOC,WAAWgJ,OAKvD0D,EACFG,EAAekB,IAEf9B,EAAO8B,GACP7B,GAAmB,KASjB8B,EAAW,CACfzD,OAAQA,CAAC0D,EAAuCvD,KAC9C0B,EAAoB,CAClB6B,YACAjF,SAAUC,GAAeqD,GACzB5B,WAIJF,YAAaA,CAACyD,EAAuCvD,KAAmB,IAADwD,EACrE,MAAMlF,EAAWC,GAAeqD,GAC1B6B,EAA2B,QAAlBD,EAAGD,EAAUpM,aAAK,IAAAqM,OAAA,EAAfA,EAAiBE,QAAQ,QAAS,IAC9CpO,EAAaH,EAAQE,OAAOC,WAAWgJ,IAAa,GAEpDqF,EACJC,KAAKC,IAAGC,MAARF,MAAIG,EAAAA,EAAAA,GACCzO,EACAJ,IAAK8O,IAAmC,IAADC,EACtC,MAAMC,EAAe,QAAVD,EAAGD,EAAE7M,aAAK,IAAA8M,OAAA,EAAPA,EAASC,MACrB,IAAIC,OAAO,IAAIV,gBAEjB,OAAOS,EAAQE,SAASF,EAAM,IAAM,KAAO,IAE5ClF,OAAQqF,IAAeC,MAAMD,KAAGE,OAAA,CACnC,KACE,EAENpB,EAAc7E,EAAWhJ,GAAU,GAAAiP,QAAAR,EAAAA,EAAAA,GAC9BzO,GAAU,CACb,IAAKiO,EAAWpM,MAAO,GAAGsM,KAAaE,SAI3C5D,SAAUA,CAACwD,EAAuCvD,KAChD,MAAM1B,EAAWC,GAAeqD,GAChCuB,EAAc7E,EAAWhJ,GACvBA,EAAW0J,OAAO,CAACwF,EAAGC,IAAMA,IAAMzE,MAMlC0E,EAAuBA,CAC3BlG,EACAF,KAEA6E,EAAc7E,EAAWhJ,IACvB,MAAMqP,EAAa,GAAAJ,QAAAR,EAAAA,EAAAA,GAAOzO,GAAU,CAAEkJ,IAMtC,OALAkD,EAAoB,CAClB6B,UAAW/E,EACXF,WACA0B,MAAO2E,EAAcpQ,OAAS,IAEzBoQ,KAqFL7Q,EAAiBkO,EAAgBE,EAAc/M,EAE/CyP,EAAW1O,OAAO2O,QAAQ9D,IAAS7L,IAAI4P,IAAA,IAAAC,EAAAC,EAAAC,EAAA,IAAE1R,EAAK8M,GAAKyE,EAAA,MAAM,CAC7DvR,MACA4D,MACE9C,EAAAA,cAAA,QAAMC,UAAU,2BACdD,EAAAA,cAACgM,EAAI,CAAC/L,UAAU,YACff,EAAI4L,OAAO,GAAGC,cAAgB7L,EAAI8L,MAAM,GAAG,IAC5ChL,EAAAA,cAAA,QAAMC,UAAU,qCAAoC,KAIjD,QAFAyQ,EAAAjR,EAAeuB,OAAOC,WACrBiJ,GAAehL,WAChB,IAAAwR,OAAA,EAFAA,EAEExQ,SAAU,EAAE,MAKrB2Q,SACE7Q,EAAAA,cAAA,WACEA,EAAAA,cAAA,OAAKC,UAAU,0CACbD,EAAAA,cAAA,MAAIC,UAAU,0BAGX,QAFA0Q,EAAAlR,EAAeuB,OAAOC,WACrBiJ,GAAehL,WAChB,IAAAyR,OAAA,EAFAA,EAEEzQ,SAAU,EAAG,IAGI,MAAnB,QAFC0Q,EAAAnR,EAAeuB,OAAOC,WACtBiJ,GAAehL,WAChB,IAAA0R,OAAA,EAFCA,EAEC1Q,SAAU,GACThB,EAAI4L,OAAO,GAAGC,cAAgB7L,EAAI8L,MAAM,GACxC9L,EAAI4L,OAAO,GAAGC,cAAgB7L,EAAI8L,MAAM,GAAK,KAEnDhL,EAAAA,cAACsJ,GAAoB,CACnBb,cAAevJ,EACf4B,QAASrB,EACT8J,iBAAkB8G,EAClB7O,SAAUmM,KAGd3N,EAAAA,cAACqM,GAAaxK,OAAA4K,OAAA,CACZtB,MACE1L,EAAeuB,OAAOC,WACpBiJ,GAAehL,KACZ,GAEPkB,MAAOlB,EACPsC,SAAUmM,GACNsB,QAMZ,OACEjP,EAAAA,cAAA,OAAKC,UAAU,kBACZ4J,EAED7J,EAAAA,cAAA,OAAKC,UAAU,2DACbD,EAAAA,cAAA,OACE8Q,IAAI,2BACJC,IAAI,iBACJ9Q,UAAU,wCAEZD,EAAAA,cAAA,OAAKC,UAAU,0DACbD,EAAAA,cAAA,WACEA,EAAAA,cAAA,OAAKC,UAAU,qCACbD,EAAAA,cAAA,OAAKC,UAAU,2BACZwN,EACCzN,EAAAA,cAACgR,EAAAA,EAAK,CACJC,MAAO5C,EACP6C,SAAW7P,GAAMiN,EAAYjN,EAAEoG,OAAOwJ,OACtChR,UAAU,oFAGZD,EAAAA,cAAA,MAAIC,UAAU,qCACXR,EAAeuB,OAAOE,MAG1BzB,EAAeuB,OAAOG,KACrBnB,EAAAA,cAACG,EAAAA,EAAO,CAACC,MAAM,kBACbJ,EAAAA,cAAClB,EAAAA,EAAK,CAACmB,UAAU,8BAKxBwN,EACCzN,EAAAA,cAACmR,GAAAA,EAAQ,CACPF,MAAO1C,EACP2C,SAAW7P,GAAMmN,EAAmBnN,EAAEoG,OAAOwJ,OAC7ChR,UAAU,8DACVmR,KAAM,IAGRpR,EAAAA,cAAA,OAAKC,UAAU,uBACbD,EAAAA,cAAA,KAAGC,UAAU,0CACVR,EAAeuB,OAAOU,SAASqB,aAElC/C,EAAAA,cAAA,OAAKC,UAAU,cACbD,EAAAA,cAACG,EAAAA,EAAO,CAACC,MAAM,gBACbJ,EAAAA,cAACQ,EAAAA,GAAM,CACLE,KAAMV,EAAAA,cAAC8L,EAAAA,EAAI,CAAC7L,UAAU,YACtBI,QAASA,IAAMqN,GAAoB,GACnCjN,KAAK,OACLR,UAAU,iCACVuB,SAAUmM,KAGd3N,EAAAA,cAACG,EAAAA,EAAO,CAACC,MAAM,oBACbJ,EAAAA,cAACQ,EAAAA,GAAM,CACLE,KAAMV,EAAAA,cAACqR,EAAAA,EAAQ,CAACpR,UAAU,YAC1BI,QA3HGiR,KACrB,MAAMC,EAAU3C,KAAKC,UAAU/N,EAAS,KAAM,GACxC0Q,EAAW,IAAIC,KAAK,CAACF,GAAU,CAAE9Q,KAAM,qBACvCU,EAAMuQ,IAAIC,gBAAgBH,GAC1BI,EAAOC,SAASC,cAAc,KACpCF,EAAKG,KAAO5Q,EACZyQ,EAAKI,SAAW,GAAGlR,EAAQE,OAAOE,KAC/B+Q,cACA5C,QAAQ,OAAQ,YACnBwC,SAASK,KAAKC,YAAYP,GAC1BA,EAAKQ,QACLP,SAASK,KAAKG,YAAYT,GAC1BF,IAAIY,gBAAgBnR,IAgHFV,KAAK,OACLR,UAAU,oCAGdD,EAAAA,cAACG,EAAAA,EAAO,CACNC,MAAOuN,EAAgB,cAAgB,eAEvC3N,EAAAA,cAACQ,EAAAA,GAAM,CACLE,KACEiN,EACE3N,EAAAA,cAACuS,EAAAA,EAAS,CAACtS,UAAU,YAErBD,EAAAA,cAACwS,EAAAA,EAAI,CAACvS,UAAU,YAGpBI,QAASA,KACP,MAAMoS,GAAe9E,EACrBC,EAAiB6E,GACbA,IACFzE,EAAaY,KAAKC,UAAUpP,EAAgB,KAAM,IAClD2O,GAAW,KAGf3N,KAAK,OACLR,UAAU,sCAMnBwN,GACCzN,EAAAA,cAAA,OAAKC,UAAU,mBACbD,EAAAA,cAACQ,EAAAA,GAAM,CAACH,QAASA,IAAMqN,GAAoB,IAAQ,UAGnD1N,EAAAA,cAACQ,EAAAA,GAAM,CAACC,KAAK,UAAUJ,QAhLXqS,KACxB,MAAM1D,EAAiB,IAClBlO,EACHE,OAAQ,IACHF,EAAQE,OACXE,KAAMmN,EACN3M,SAAU,IACLZ,EAAQE,OAAOU,SAClBqB,YAAawL,KAInBrB,EAAO8B,GACP7B,GAAmB,GACnBO,GAAoB,KAkK2C,UAMzD1N,EAAAA,cAAA,OAAKC,UAAU,cACbD,EAAAA,cAAA,OAAKC,UAAU,iEACbD,EAAAA,cAAC4B,EAAAA,EAAO,CAAC3B,UAAU,2BACnBD,EAAAA,cAAA,QAAMC,UAAU,WACb4B,OAAOC,OAAOrC,EAAeuB,OAAOC,YAAYc,OAC/C,CAACC,EAAKC,IAAQD,EAAMC,EAAI/B,OACxB,GACC,IAAI,eAIXF,EAAAA,cAAA,OAAKC,UAAU,iDAAgD,IAC3DR,EAAeuB,OAAOU,SAASC,YAMxCgM,EACC3N,EAAAA,cAAA,OAAKC,UAAU,aACbD,EAAAA,cAAA,OAAKC,UAAU,qCACbD,EAAAA,cAAA,OAAKC,UAAU,2BACbD,EAAAA,cAAA,MAAIC,UAAU,uBAAsB,eACnCkO,GACCnO,EAAAA,cAAA,QAAMC,UAAU,2BAA0B,sBAK9CD,EAAAA,cAAA,OAAKC,UAAU,cACbD,EAAAA,cAACQ,EAAAA,GAAM,CACLH,QAASA,KACPuN,GAAiB,GACjBQ,GAAW,GACXJ,EAAaY,KAAKC,UAAU/N,EAAS,KAAM,MAE9C,UAGDd,EAAAA,cAACQ,EAAAA,GAAM,CAACC,KAAK,UAAUJ,QA5OZsS,KACrB,IACE,MAAM3D,EAAiBJ,KAAKgE,MAAM7E,GAClCD,EAAekB,GACf9B,EAAO8B,GACP7B,GAAmB,GACnBS,GAAiB,GACjBQ,GAAW,GACXxE,EAAWiJ,QAAQ,gCACrB,CAAE,MAAOpI,GACPb,EAAWa,MAAM,kDACjBC,QAAQD,MAAM,oBAAqBA,EACrC,IAgO0D,kBAKpDzK,EAAAA,cAAA,OAAKC,UAAU,6CACbD,EAAAA,cAAC8S,EAAAA,EAAY,CACXrE,UAAWA,EACXwC,MAAOlD,EACPmD,SArVcD,IACxBjD,EAAaiD,GACb7C,GAAW,IAoVD2E,SAAS,OACTC,SAAS,MAKfhT,EAAAA,cAACiT,EAAAA,EAAI,CACH9H,MAAOoF,EACPtQ,UAAU,eACVmB,KAAK,QACL8P,SAAWhS,GAAQsO,EAAatO,KAIpCc,EAAAA,cAACkT,EAAAA,EAAM,CACL9S,MAAM,iBACN+S,UAAU,QACV/R,KAAK,QACLgS,QAASA,IAAM/F,EAAoB,MACnCgG,OAAQjG,EACRnN,UAAU,2BAETmN,GACCpN,EAAAA,cAACsT,EAAAA,EAAe,CACdpE,UAAW9B,EAAiB8B,UAC5BgC,SA3RRqC,IAEKnG,IAEL0B,EAAc1B,EAAiBnD,SAAWhJ,GACxCA,EAAWJ,IAAI,CAAC8O,EAAGS,IACjBA,IAAMhD,EAAiBzB,MAAQ4H,EAAmB5D,IAGtDtC,EAAoB,QAmRZ+F,QAASA,IAAM/F,EAAoB,MACnCmG,iBAAiB,OAQ7B,I,iCCzoBA,MAWaC,GAXeC,MAC1B,IAGE,OADoBC,EAAQ,KAE9B,CAAE,MAAOlJ,GAEP,MADAC,QAAQD,MAAM,8BAA+BA,GACvCA,CACR,GAG2CiJ,GCAhCE,GAAwDtU,IAI9D,IAJ+D,KACpE+T,EAAI,SACJQ,EAAQ,gBACRjU,GACDN,EACC,MAAM,EAACiO,EAAU,EAACC,IAAgBF,EAAAA,EAAAA,UAAS,QACrC,EAACnM,EAAI,EAAC2S,IAAUxG,EAAAA,EAAAA,UAAS,KACzB,EAACyG,EAAY,EAACC,IAAkB1G,EAAAA,EAAAA,UACpCsB,KAAKC,UAAU4E,GAAgB,KAAM,KAEjC,EAAChJ,EAAM,EAACwJ,IAAY3G,EAAAA,EAAAA,UAAS,KAC7B,EAACvN,EAAU,EAACmU,IAAgB5G,EAAAA,EAAAA,WAAS,GACrCmB,GAAYC,EAAAA,EAAAA,QAAO,MA0DnByF,EAA2B,CAC/BC,OAAQ,QACRC,gBAAgB,EAChBC,cAAehI,IAA0B,IAAzB,KAAEiI,EAAI,UAAEC,GAAWlI,EACjCmI,WAAW,KACTD,GAAaA,EAAU,OACtB,IAELtD,SA9CwBwD,IACxB,MAAM,OAAEC,EAAM,cAAEC,GAAkBF,EAAKH,KACvC,GAAe,SAAXI,GAAqBC,aAAyBC,KAAM,CACtD,MAAMC,EAAS,IAAIC,WACnBD,EAAOE,OAAU3T,IACf,IAAK,IAAD4T,EACF,MAAM/I,EAAU0C,KAAKgE,MACX,QADgBqC,EACxB5T,EAAEoG,cAAM,IAAAwN,OAAA,EAARA,EAAUC,QAIZtV,EAAgB,CACdoB,OAAQkL,IAEV2H,GACF,CAAE,MAAOsB,GACPlB,EAAS,oBACX,GAEFa,EAAOM,WAAWR,EACpB,KAAsB,UAAXD,GACTV,EAAS,wBA4BPoB,GAAW3G,EAAAA,EAAAA,QAAiB,MAE5BvD,EAAQ,CACZ,CACEjM,IAAK,MACL4D,MACE9C,EAAAA,cAAA,QAAMC,UAAU,2BACdD,EAAAA,cAAClB,EAAAA,EAAK,CAACmB,UAAU,YAAY,eAGjC4Q,SACE7Q,EAAAA,cAAA,OAAKC,UAAU,aACbD,EAAAA,cAACgR,EAAAA,EAAK,CACJsE,IAAKD,EACLE,YAAY,uBACZtE,MAAO9P,EACP+P,SAAW7P,GAAMyS,EAAOzS,EAAEoG,OAAOwJ,SAEnCjR,EAAAA,cAAA,OAAKC,UAAU,WAAU,SAEvBD,EAAAA,cAAA,KACEwV,KAAK,SACLnV,QAAUgB,IACRyS,EACE,uIAEFzS,EAAEoU,kBAEJ1D,KAAK,sIACLtK,OAAO,SACPiO,IAAI,aACJzV,UAAU,eAET,IAAI,eACQ,MAGjBD,EAAAA,cAACQ,EAAAA,GAAM,CACLC,KAAK,UACLJ,QA1GcsV,UACtBzB,GAAa,GACbD,EAAS,IACT,IACE,MAAM2B,QAAiBC,MAAM1U,GACvB2U,QAAcF,EAASG,OAE7BnW,EAAgB,CACdoB,OAAQ8U,IAEVjC,GACF,CAAE,MAAOsB,GACPlB,EAAS,4CACX,CAAC,QACCC,GAAa,EACf,GA4FQ1S,UAAWL,GAAOpB,EAClBiW,OAAK,GACN,qBAMP,CACE9W,IAAK,OACL4D,MACE9C,EAAAA,cAAA,QAAMC,UAAU,2BACdD,EAAAA,cAACiW,GAAAA,EAAU,CAAChW,UAAU,YAAY,gBAGtC4Q,SACE7Q,EAAAA,cAAA,OAAKC,UAAU,+DACbD,EAAAA,cAACkW,GAAAA,EAAOC,QAAYhC,EAClBnU,EAAAA,cAAA,KAAGC,UAAU,wBACXD,EAAAA,cAACiW,GAAAA,EAAU,CAAChW,UAAU,oCAExBD,EAAAA,cAAA,KAAGC,UAAU,mBAAkB,2CAOvC,CACEf,IAAK,QACL4D,MACE9C,EAAAA,cAAA,QAAMC,UAAU,2BACdD,EAAAA,cAACwS,EAAAA,EAAI,CAACvS,UAAU,YAAY,eAGhC4Q,SACE7Q,EAAAA,cAAA,OAAKC,UAAU,aACbD,EAAAA,cAAA,OAAKC,UAAU,QACbD,EAAAA,cAAC8S,EAAAA,EAAY,CACX7B,MAAO8C,EACP7C,SAAU8C,EACVvF,UAAWA,EACXsE,SAAS,OACTC,SAAS,KAGbhT,EAAAA,cAACQ,EAAAA,GAAM,CAACC,KAAK,UAAUJ,QA9GL+V,KACxB,IACE,MAAMlK,EAAU0C,KAAKgE,MAAMmB,GAE3BnU,EAAgB,CACdoB,OAAQkL,IAEV2H,GACF,CAAE,MAAOsB,GACPlB,EAAS,sBACX,GAoGyD+B,OAAK,GAAC,kBAQjE,OACEhW,EAAAA,cAACqW,EAAAA,EAAK,CACJjW,MAAM,qBACNiT,KAAMA,EACNQ,SAAUA,EACVyC,OAAQ,KACR9T,MAAO,KAEPxC,EAAAA,cAAA,OAAKC,UAAU,QACbD,EAAAA,cAACiT,EAAAA,EAAI,CAACsD,UAAWhJ,EAAW2D,SAAU1D,EAAcrC,MAAOA,IAE1DV,GACCzK,EAAAA,cAACwW,GAAAA,EAAK,CAAC1M,QAASW,EAAOhK,KAAK,QAAQ+I,UAAQ,EAACvJ,UAAU,YCiFjE,OAhRwCwW,KACtC,MAAM,EAAC1W,EAAU,EAACmU,IAAgB5G,EAAAA,EAAAA,WAAS,IACrC,EAAC9N,EAAU,EAACkX,IAAgBpJ,EAAAA,EAAAA,UAAoB,KAChD,EAAC7N,EAAe,EAACkX,IAAqBrJ,EAAAA,EAAAA,UAAyB,OAC/D,EAACsJ,EAAkB,EAACC,IAAwBvJ,EAAAA,EAAAA,WAAS,IACrD,EAACW,EAAkB,EAACC,IAAwBZ,EAAAA,EAAAA,WAAS,IACrD,EAACwJ,EAAc,EAACC,IAAoBzJ,EAAAA,EAAAA,UAAS,KACjD,GAAsB,oBAAX0J,OAAwB,CACjC,MAAMC,EAASC,aAAaC,QAAQ,kBACpC,OAAkB,OAAXF,GAAkBrI,KAAKgE,MAAMqE,EACtC,CACA,OAAO,KAGH,KAAEG,IAASC,EAAAA,EAAAA,YAAWC,EAAAA,IACrB1N,EAAYC,GAAiBC,EAAAA,GAAQC,cAG5C4E,EAAAA,EAAAA,WAAU,KACc,oBAAXqI,QACTE,aAAaK,QAAQ,iBAAkB3I,KAAKC,UAAUiI,KAEvD,CAACA,IAEJ,MAAMU,GAAiBC,EAAAA,EAAAA,aAAY9B,UACjC,GAAKyB,SAAAA,EAAMrW,GAEX,IACEmT,GAAa,GACb,MAAM4B,QAAa4B,EAAAA,EAAWC,cAAcP,EAAKrW,IACjD2V,EAAaZ,IACRrW,GAAkBqW,EAAK5V,OAAS,GACnCyW,EAAkBb,EAAK,GAE3B,CAAE,MAAOrL,GACPC,QAAQD,MAAM,4BAA6BA,GAC3Cb,EAAWa,MAAM,4BACnB,CAAC,QACCyJ,GAAa,EACf,GACC,CAACkD,aAAI,EAAJA,EAAMrW,GAAItB,EAAgBmK,KAE9B+E,EAAAA,EAAAA,WAAU,KACR6I,KACC,CAACA,KAGJ7I,EAAAA,EAAAA,WAAU,KACR,MACMiJ,EADS,IAAIC,gBAAgBb,OAAOc,SAASC,QAC1BC,IAAI,aAE7B,GAAIJ,IAAcnY,EAAgB,CAChC,MAAMwY,EAAYlI,SAAS6H,EAAW,IACjC3H,MAAMgI,IACTC,EAAoBD,EAExB,GACC,KAGHtJ,EAAAA,EAAAA,WAAU,KACJlP,SAAAA,EAAgBsB,IAClBiW,OAAOmB,QAAQC,UACb,CAAC,EACD,GACA,cAAc3Y,EAAesB,GAAGsX,eAGnC,CAAC5Y,aAAc,EAAdA,EAAgBsB,KAEpB,MAAMmX,EAAsBvC,UACrByB,SAAAA,EAAMrW,KAEPkN,EACFoI,EAAAA,EAAMiC,QAAQ,CACZlY,MAAO,kBACP8L,QAAS,yDACTqM,OAAQ,UACRC,WAAY,UACZC,KAAMA,KACJC,EAAgBd,GAChB1J,GAAqB,YAInBwK,EAAgBd,KAIpBc,EAAkB/C,UACtB,GAAKyB,SAAAA,EAAMrW,GAAX,CAEAmT,GAAa,GACb,IACE,MAAM4B,QAAa4B,EAAAA,EAAWiB,WAAWf,EAAWR,EAAKrW,IACzD4V,EAAkBb,EACpB,CAAE,MAAOrL,GACPC,QAAQD,MAAM,yBAA0BA,GACxCb,EAAWa,MAAM,yBACnB,CAAC,QACCyJ,GAAa,EACf,CAXqB,GA8BjB0E,EAAsBjD,UAC1B,GAAKyB,SAAAA,EAAMrW,IAAOtB,SAAAA,EAAgBsB,GAElC,IACE,MAAM8X,EAAmB,IACpBC,EACHC,gBAAYjR,EACZ5F,gBAAY4F,GAERkH,QAAuB0I,EAAAA,EAAW5I,cACtCrP,EAAesB,GACf8X,EACAzB,EAAKrW,IAEP2V,EACElX,EAAUqB,IAAKmY,GAAOA,EAAEjY,KAAOiO,EAAejO,GAAKiO,EAAiBgK,IAEtErC,EAAkB3H,GAClBd,GAAqB,GACrBtE,EAAWiJ,QAAQ,+BACrB,CAAE,MAAOpI,GACPC,QAAQD,MAAM,0BAA2BA,GACzCb,EAAWa,MAAM,2BACnB,GAiDF,OAAK2M,SAAAA,EAAMrW,GASTf,EAAAA,cAAA,OAAKC,UAAU,+BACZ4J,EAGD7J,EAAAA,cAAC4T,GAAkB,CACjBP,KAAMuD,EACN/C,SAAUA,IAAMgD,GAAqB,GACrCjX,gBAxGsB+V,UAC1B,GAAKyB,SAAAA,EAAMrW,GAAX,CAEAkY,EAAYC,QAAU9B,EAAKrW,GAC3B,IACE,MAAMoY,QAAqBzB,EAAAA,EAAW0B,cAAcH,EAAa7B,EAAKrW,IACtE2V,EAAa,CAACyC,GAAYjJ,QAAAR,EAAAA,EAAAA,GAAKlQ,KAC/BmX,EAAkBwC,GAClBtC,GAAqB,GACrBjN,EAAWiJ,QAAQ,+BACrB,CAAE,MAAOpI,GACPC,QAAQD,MAAM,0BAA2BA,GACzCb,EAAWa,MAAM,2BACnB,CAZqB,KA2GnBzK,EAAAA,cAAA,OACEC,UAAW,yEACT6W,EAAgB,OAAS,SAG3B9W,EAAAA,cAACX,EAAc,CACbE,OAAQuX,EACRtX,UAAWA,EACXC,eAAgBA,EAChBC,SAAUA,IAAMqX,GAAkBD,GAClCnX,gBAAkBmB,GAAYoX,EAAoBpX,EAAQC,IAC1DnB,gBAAiBA,IAAMiX,GAAqB,GAC5ChX,gBA9EoB8V,UAC1B,GAAKyB,SAAAA,EAAMrW,GAEX,UACQ2W,EAAAA,EAAW2B,cAAczB,EAAWR,EAAKrW,IAC/C2V,EAAalX,EAAUmL,OAAQqO,GAAMA,EAAEjY,KAAO6W,KAC1CnY,aAAc,EAAdA,EAAgBsB,MAAO6W,GACzBjB,EAAkB,MAEpB/M,EAAWiJ,QAAQ,+BACrB,CAAE,MAAOpI,GACPC,QAAQD,MAAM,0BAA2BA,GACzCb,EAAWa,MAAM,2BACnB,GAkEM3K,cA/DkB6V,UACxB,GAAKyB,SAAAA,EAAMrW,GAEX,IACEmT,GAAa,GACb,MAAMpT,EAAUtB,EAAUmJ,KAAMqQ,GAAMA,EAAEjY,KAAO6W,GAC/C,GAAK9W,UAAAA,EAASE,OAAOG,IAAK,OAE1B,MAAMmY,QAAsB5B,EAAAA,EAAW6B,YAAYzY,EAAQE,OAAOG,WAC5DyX,EAAoB,IACrBU,EACHvY,GAAI6W,EACJ5W,OAAQ,IACHsY,EAActY,OACjBU,SAAU,IACL4X,EAActY,OAAOU,SACxB8X,YAAY,IAAIC,MAAOC,kBAK7B9P,EAAWiJ,QAAQ,8BACrB,CAAE,MAAOpI,GACPC,QAAQD,MAAM,yBAA0BA,GACxCb,EAAWa,MAAM,yBACnB,CAAC,QACCyJ,GAAa,EACf,GAqCMnU,UAAWA,KAKfC,EAAAA,cAAA,OACEC,UAAW,6CACT6W,EAAgB,QAAU,UAG5B9W,EAAAA,cAAA,OAAKC,UAAU,YAEbD,EAAAA,cAAA,OAAKC,UAAU,wCACbD,EAAAA,cAAA,QAAMC,UAAU,4BAA2B,aAC1CR,GACCO,EAAAA,cAAAA,EAAAA,SAAA,KACEA,EAAAA,cAAC2Z,EAAAA,EAAY,CAAC1Z,UAAU,2BACxBD,EAAAA,cAAA,QAAMC,UAAU,kBACbR,EAAeuB,OAAOE,QAO9BnB,IAAcN,EACbO,EAAAA,cAAA,OAAKC,UAAU,yEAAwE,wBAGrFR,EACFO,EAAAA,cAACgN,GAAa,CACZlM,QAASrB,EACTyN,OAAQ0L,EACRzL,mBAAoBe,IAGtBlO,EAAAA,cAAA,OAAKC,UAAU,yEAAwE,4DApE7FD,EAAAA,cAAA,OAAKC,UAAU,yEAAwE,oCCjL7F,OArBoBX,IAAmB,IAAlB,KAAEwW,GAAWxW,EAChC,OACEU,EAAAA,cAAC4Z,EAAAA,EAAM,CAACC,KAAM/D,EAAKgE,KAAKC,aAAc3Z,MAAM,KAAKwR,KAAM,YACrD5R,EAAAA,cAAA,QAAMga,MAAO,CAAEvX,OAAQ,QAAUxC,UAAU,YACzCD,EAAAA,cAACyW,GAAc,Q", "sources": ["webpack://autogentstudio/./node_modules/lucide-react/dist/esm/icons/globe.js", "webpack://autogentstudio/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js", "webpack://autogentstudio/./src/components/views/gallery/sidebar.tsx", "webpack://autogentstudio/./node_modules/lucide-react/dist/esm/icons/trash.js", "webpack://autogentstudio/./node_modules/lucide-react/dist/esm/icons/briefcase.js", "webpack://autogentstudio/./src/components/types/component-templates.ts", "webpack://autogentstudio/./src/components/shared/AddComponentDropdown.tsx", "webpack://autogentstudio/./src/components/views/gallery/detail.tsx", "webpack://autogentstudio/./src/components/views/gallery/utils.ts", "webpack://autogentstudio/./src/components/views/gallery/create-modal.tsx", "webpack://autogentstudio/./src/components/views/gallery/manager.tsx", "webpack://autogentstudio/./src/pages/gallery.tsx"], "sourcesContent": ["/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst Globe = createLucideIcon(\"Globe\", [\n  [\"circle\", { cx: \"12\", cy: \"12\", r: \"10\", key: \"1mglay\" }],\n  [\"path\", { d: \"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20\", key: \"13o1zl\" }],\n  [\"path\", { d: \"M2 12h20\", key: \"9i4pu4\" }]\n]);\n\nexport { Globe as default };\n//# sourceMappingURL=globe.js.map\n", "/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst RefreshCw = createLucideIcon(\"RefreshCw\", [\n  [\"path\", { d: \"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8\", key: \"v9h5vc\" }],\n  [\"path\", { d: \"M21 3v5h-5\", key: \"1q7to0\" }],\n  [\"path\", { d: \"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16\", key: \"3uifl3\" }],\n  [\"path\", { d: \"M8 16H3v5\", key: \"1cv678\" }]\n]);\n\nexport { RefreshCw as default };\n//# sourceMappingURL=refresh-cw.js.map\n", "import React from \"react\";\r\nimport { <PERSON><PERSON>, Tooltip } from \"antd\";\r\nimport {\r\n  Plus,\r\n  Trash2,\r\n  PanelLeftClose,\r\n  PanelLeftOpen,\r\n  Package,\r\n  RefreshCw,\r\n  Globe,\r\n  Info,\r\n} from \"lucide-react\";\r\nimport type { Gallery } from \"../../types/datamodel\";\r\nimport { getRelativeTimeString } from \"../atoms\";\r\n\r\ninterface GallerySidebarProps {\r\n  isOpen: boolean;\r\n  galleries: Gallery[];\r\n  currentGallery: Gallery | null;\r\n  onToggle: () => void;\r\n  onSelectGallery: (gallery: Gallery) => void;\r\n  onCreateGallery: () => void;\r\n  onDeleteGallery: (galleryId: number) => void;\r\n  onSyncGallery: (galleryId: number) => void;\r\n  isLoading?: boolean;\r\n}\r\n\r\nexport const GallerySidebar: React.FC<GallerySidebarProps> = ({\r\n  isOpen,\r\n  galleries,\r\n  currentGallery,\r\n  onToggle,\r\n  onSelectGallery,\r\n  onCreateGallery,\r\n  onDeleteGallery,\r\n  onSyncGallery,\r\n  isLoading = false,\r\n}) => {\r\n  // Render collapsed state\r\n  if (!isOpen) {\r\n    return (\r\n      <div className=\"h-full border-r border-secondary\">\r\n        <div className=\"p-2 -ml-2\">\r\n          <Tooltip title={`Galleries (${galleries.length})`}>\r\n            <button\r\n              onClick={onToggle}\r\n              className=\"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50\"\r\n            >\r\n              <PanelLeftOpen strokeWidth={1.5} className=\"h-6 w-6\" />\r\n            </button>\r\n          </Tooltip>\r\n        </div>\r\n\r\n        <div className=\"mt-4 px-2 -ml-1\">\r\n          <Tooltip title=\"Create new gallery\">\r\n            <Button\r\n              type=\"text\"\r\n              className=\"w-full p-2 flex justify-center\"\r\n              onClick={onCreateGallery}\r\n              icon={<Plus className=\"w-4 h-4\" />}\r\n            />\r\n          </Tooltip>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Render expanded state\r\n  return (\r\n    <div className=\"h-full border-r border-secondary\">\r\n      {/* Header */}\r\n      <div className=\"flex items-center justify-between pt-0 p-4 pl-2 pr-2 border-b border-secondary\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <span className=\"text-primary font-medium\">Galleries</span>\r\n          <span className=\"px-2 py-0.5 text-xs bg-accent/10 text-accent rounded\">\r\n            {galleries.length}\r\n          </span>\r\n        </div>\r\n        <Tooltip title=\"Close Sidebar\">\r\n          <button\r\n            onClick={onToggle}\r\n            className=\"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50\"\r\n          >\r\n            <PanelLeftClose strokeWidth={1.5} className=\"h-6 w-6\" />\r\n          </button>\r\n        </Tooltip>\r\n      </div>\r\n\r\n      {/* Create Gallery Button */}\r\n      <div className=\"my-4 flex text-sm\">\r\n        <div className=\"mr-2 w-full\">\r\n          <Tooltip title=\"Create new gallery\">\r\n            <Button\r\n              type=\"primary\"\r\n              className=\"w-full\"\r\n              icon={<Plus className=\"w-4 h-4\" />}\r\n              onClick={onCreateGallery}\r\n            >\r\n              New Gallery\r\n            </Button>\r\n          </Tooltip>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Section Label */}\r\n      <div className=\"py-2 flex text-sm text-secondary\">\r\n        <div className=\"flex\">All Galleries</div>\r\n        {isLoading && <RefreshCw className=\"w-4 h-4 ml-2 animate-spin\" />}\r\n      </div>\r\n\r\n      {/* Galleries List */}\r\n      {!isLoading && galleries.length === 0 && (\r\n        <div className=\"p-2 mr-2 text-center text-secondary text-sm border border-dashed rounded\">\r\n          <Info className=\"w-4 h-4 inline-block mr-1.5 -mt-0.5\" />\r\n          No galleries found\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"scroll overflow-y-auto h-[calc(100%-170px)]\">\r\n        {galleries.map((gallery) => (\r\n          <div key={gallery.id} className=\"relative border-secondary\">\r\n            <div\r\n              className={`absolute top-1 left-0.5 z-50 h-[calc(100%-8px)] w-1 bg-opacity-80 rounded ${\r\n                currentGallery?.id === gallery.id ? \"bg-accent\" : \"bg-tertiary\"\r\n              }`}\r\n            />\r\n            {gallery && gallery.config && gallery.config.components && (\r\n              <div\r\n                className={`group ml-1 flex flex-col p-3 rounded-l cursor-pointer hover:bg-secondary ${\r\n                  currentGallery?.id === gallery.id\r\n                    ? \"border-accent bg-secondary\"\r\n                    : \"border-transparent\"\r\n                }`}\r\n                onClick={() => onSelectGallery(gallery)}\r\n              >\r\n                {/* Gallery Name and Actions Row */}\r\n                <div className=\"flex items-center justify-between min-w-0\">\r\n                  <div className=\"flex items-center gap-2 min-w-0 flex-1\">\r\n                    <div className=\"truncate flex-1\">\r\n                      <span className=\"font-medium text-sm\">\r\n                        {gallery.config.name}\r\n                      </span>\r\n                    </div>\r\n                    {gallery.config.url && (\r\n                      <Tooltip title=\"Remote Gallery\">\r\n                        <Globe className=\"w-3 h-3 text-secondary flex-shrink-0\" />\r\n                      </Tooltip>\r\n                    )}\r\n                  </div>\r\n                  <div className=\"flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity ml-2 flex-shrink-0\">\r\n                    {gallery.config.url && (\r\n                      <Tooltip title=\"Sync gallery\">\r\n                        <Button\r\n                          type=\"text\"\r\n                          size=\"small\"\r\n                          className=\"p-0 min-w-[24px] h-6\"\r\n                          icon={<RefreshCw className=\"w-4 h-4\" />}\r\n                          onClick={(e) => {\r\n                            e.stopPropagation();\r\n                            onSyncGallery(gallery.id!);\r\n                          }}\r\n                        />\r\n                      </Tooltip>\r\n                    )}\r\n                    <Tooltip\r\n                      title={\r\n                        galleries.length === 1\r\n                          ? \"Cannot delete the last gallery\"\r\n                          : \"Delete gallery\"\r\n                      }\r\n                    >\r\n                      <Button\r\n                        type=\"text\"\r\n                        size=\"small\"\r\n                        className=\"p-0 min-w-[24px] h-6\"\r\n                        danger\r\n                        disabled={galleries.length === 1}\r\n                        icon={<Trash2 className=\"w-4 h-4 text-red-500\" />}\r\n                        onClick={(e) => {\r\n                          e.stopPropagation();\r\n                          onDeleteGallery(gallery.id!);\r\n                        }}\r\n                      />\r\n                    </Tooltip>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Gallery Metadata */}\r\n                <div className=\"mt-1 flex items-center gap-2 text-xs text-secondary\">\r\n                  <span className=\"bg-secondary/20 truncate rounded px-1\">\r\n                    v{gallery.config.metadata.version}\r\n                  </span>\r\n                  <div className=\"flex items-center gap-1\">\r\n                    <Package className=\"w-3 h-3\" />\r\n                    <span>\r\n                      {Object.values(gallery.config.components).reduce(\r\n                        (sum, arr) => sum + arr.length,\r\n                        0\r\n                      )}{\" \"}\r\n                      components\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Updated Timestamp */}\r\n                {gallery.updated_at && (\r\n                  <div className=\"mt-1 flex items-center gap-1 text-xs text-secondary\">\r\n                    <span>{getRelativeTimeString(gallery.updated_at)}</span>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default GallerySidebar;\r\n", "/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst Trash = createLucideIcon(\"Trash\", [\n  [\"path\", { d: \"M3 6h18\", key: \"d0wm0j\" }],\n  [\"path\", { d: \"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6\", key: \"4alrt4\" }],\n  [\"path\", { d: \"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2\", key: \"v07s0e\" }]\n]);\n\nexport { Trash as default };\n//# sourceMappingURL=trash.js.map\n", "/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst Briefcase = createLucideIcon(\"Briefcase\", [\n  [\"path\", { d: \"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16\", key: \"jecpp\" }],\n  [\"rect\", { width: \"20\", height: \"14\", x: \"2\", y: \"6\", rx: \"2\", key: \"i6l2r4\" }]\n]);\n\nexport { Briefcase as default };\n//# sourceMappingURL=briefcase.js.map\n", "import {\r\n  Component,\r\n  ComponentConfig,\r\n  ComponentTypes,\r\n  TeamConfig,\r\n  AgentConfig,\r\n  ModelConfig,\r\n  ToolConfig,\r\n  WorkbenchConfig,\r\n  TerminationConfig,\r\n  SelectorGroupChatConfig,\r\n  RoundRobinGroupChatConfig,\r\n  AssistantAgentConfig,\r\n  UserProxyAgentConfig,\r\n  MultimodalWebSurferConfig,\r\n  OpenAIClientConfig,\r\n  AzureOpenAIClientConfig,\r\n  AnthropicClientConfig,\r\n  FunctionToolConfig,\r\n  PythonCodeExecutionToolConfig,\r\n  StaticWorkbenchConfig,\r\n  McpWorkbenchConfig,\r\n  StdioServerParams,\r\n  SseServerParams,\r\n  StreamableHttpServerParams,\r\n  MaxMessageTerminationConfig,\r\n  TextMentionTerminationConfig,\r\n  StopMessageTerminationConfig,\r\n  TokenUsageTerminationConfig,\r\n  HandoffTerminationConfig,\r\n  TimeoutTerminationConfig,\r\n  ExternalTerminationConfig,\r\n  SourceMatchTerminationConfig,\r\n  TextMessageTerminationConfig,\r\n  OrTerminationConfig,\r\n  AndTerminationConfig,\r\n} from \"./datamodel\";\r\nimport { PROVIDERS } from \"./guards\";\r\n\r\n// Template interface for component types\r\nexport interface ComponentTemplate<T extends ComponentConfig> {\r\n  id: string;\r\n  label: string;\r\n  description: string;\r\n  provider: string;\r\n  component_type: ComponentTypes;\r\n  config: T;\r\n  version?: number;\r\n  component_version?: number;\r\n}\r\n\r\n// Team Templates\r\nexport const TEAM_TEMPLATES: ComponentTemplate<TeamConfig>[] = [\r\n  {\r\n    id: \"round-robin-team\",\r\n    label: \"Round Robin Team\",\r\n    description: \"A team where agents take turns in a fixed order\",\r\n    provider: PROVIDERS.ROUND_ROBIN_TEAM,\r\n    component_type: \"team\",\r\n    version: 1,\r\n    component_version: 1,\r\n    config: {\r\n      participants: [],\r\n      termination_condition: {\r\n        provider: PROVIDERS.TEXT_MENTION,\r\n        component_type: \"termination\",\r\n        config: { text: \"TERMINATE\" },\r\n      },\r\n      max_turns: 10,\r\n    } as RoundRobinGroupChatConfig,\r\n  },\r\n  {\r\n    id: \"selector-team\",\r\n    label: \"Selector Team\",\r\n    description: \"A team with a model that selects which agent speaks next\",\r\n    provider: PROVIDERS.SELECTOR_TEAM,\r\n    component_type: \"team\",\r\n    version: 1,\r\n    component_version: 1,\r\n    config: {\r\n      participants: [],\r\n      model_client: {\r\n        provider: PROVIDERS.OPENAI,\r\n        component_type: \"model\",\r\n        config: { model: \"gpt-4o-mini\" },\r\n      },\r\n      termination_condition: {\r\n        provider: PROVIDERS.TEXT_MENTION,\r\n        component_type: \"termination\",\r\n        config: { text: \"TERMINATE\" },\r\n      },\r\n      max_turns: 10,\r\n      selector_prompt:\r\n        \"Select the next speaker based on the conversation context and task requirements.\",\r\n      allow_repeated_speaker: true,\r\n    } as SelectorGroupChatConfig,\r\n  },\r\n];\r\n\r\n// Agent Templates\r\nexport const AGENT_TEMPLATES: ComponentTemplate<AgentConfig>[] = [\r\n  {\r\n    id: \"assistant-agent\",\r\n    label: \"Assistant Agent\",\r\n    description: \"A helpful AI assistant with tool capabilities\",\r\n    provider: PROVIDERS.ASSISTANT_AGENT,\r\n    component_type: \"agent\",\r\n    version: 2,\r\n    component_version: 2,\r\n    config: {\r\n      name: \"assistant_agent\",\r\n      description:\r\n        \"A helpful AI assistant that can use tools to solve problems\",\r\n      system_message:\r\n        \"You are a helpful assistant. Solve tasks carefully and methodically. When you have completed the task, say TERMINATE.\",\r\n      model_client: {\r\n        provider: PROVIDERS.OPENAI,\r\n        component_type: \"model\",\r\n        config: { model: \"gpt-4o-mini\" },\r\n      },\r\n      workbench: [],\r\n      // model_context is optional and complex, omit for now\r\n      reflect_on_tool_use: false,\r\n      tool_call_summary_format: \"{result}\",\r\n      model_client_stream: false,\r\n    } as AssistantAgentConfig,\r\n  },\r\n  {\r\n    id: \"user-proxy-agent\",\r\n    label: \"User Proxy Agent\",\r\n    description: \"An agent that represents human input and interaction\",\r\n    provider: PROVIDERS.USER_PROXY,\r\n    component_type: \"agent\",\r\n    version: 1,\r\n    component_version: 1,\r\n    config: {\r\n      name: \"user_proxy\",\r\n      description: \"A human user proxy for providing input and feedback\",\r\n    } as UserProxyAgentConfig,\r\n  },\r\n  {\r\n    id: \"web-surfer-agent\",\r\n    label: \"Web Surfer Agent\",\r\n    description: \"An agent that can browse and interact with web pages\",\r\n    provider: PROVIDERS.WEB_SURFER,\r\n    component_type: \"agent\",\r\n    version: 1,\r\n    component_version: 1,\r\n    config: {\r\n      name: \"web_surfer\",\r\n      description:\r\n        \"An agent that can browse the web and interact with web pages\",\r\n      model_client: {\r\n        provider: PROVIDERS.OPENAI,\r\n        component_type: \"model\",\r\n        config: { model: \"gpt-4o\" }, // Web surfer needs vision capabilities\r\n      },\r\n      headless: true,\r\n      start_page: \"https://www.google.com\",\r\n      animate_actions: false,\r\n      to_save_screenshots: false,\r\n      use_ocr: false,\r\n      to_resize_viewport: true,\r\n    } as MultimodalWebSurferConfig,\r\n  },\r\n];\r\n\r\n// Model Templates\r\nexport const MODEL_TEMPLATES: ComponentTemplate<ModelConfig>[] = [\r\n  {\r\n    id: \"openai-gpt-4o-mini\",\r\n    label: \"OpenAI GPT-4o Mini\",\r\n    description: \"Fast and cost-effective OpenAI model for most tasks\",\r\n    provider: PROVIDERS.OPENAI,\r\n    component_type: \"model\",\r\n    version: 1,\r\n    component_version: 1,\r\n    config: {\r\n      model: \"gpt-4o-mini\",\r\n      temperature: 0.7,\r\n      max_tokens: 4096,\r\n    } as OpenAIClientConfig,\r\n  },\r\n  {\r\n    id: \"openai-gpt-4o\",\r\n    label: \"OpenAI GPT-4o\",\r\n    description: \"Advanced OpenAI model with vision and advanced reasoning\",\r\n    provider: PROVIDERS.OPENAI,\r\n    component_type: \"model\",\r\n    version: 1,\r\n    component_version: 1,\r\n    config: {\r\n      model: \"gpt-4o\",\r\n      temperature: 0.7,\r\n      max_tokens: 4096,\r\n    } as OpenAIClientConfig,\r\n  },\r\n  {\r\n    id: \"azure-openai-gpt-4o-mini\",\r\n    label: \"Azure OpenAI GPT-4o Mini\",\r\n    description: \"Azure-hosted OpenAI model for enterprise use\",\r\n    provider: PROVIDERS.AZURE_OPENAI,\r\n    component_type: \"model\",\r\n    version: 1,\r\n    component_version: 1,\r\n    config: {\r\n      model: \"gpt-4o-mini\",\r\n      azure_endpoint: \"https://your-endpoint.openai.azure.com/\",\r\n      azure_deployment: \"gpt-4o-mini\",\r\n      api_version: \"2024-06-01\",\r\n      temperature: 0.7,\r\n      max_tokens: 4096,\r\n    } as AzureOpenAIClientConfig,\r\n  },\r\n  {\r\n    id: \"anthropic-claude-3-sonnet\",\r\n    label: \"Anthropic Claude-3 Sonnet\",\r\n    description: \"Anthropic's balanced model for reasoning and creativity\",\r\n    provider: PROVIDERS.ANTHROPIC,\r\n    component_type: \"model\",\r\n    version: 1,\r\n    component_version: 1,\r\n    config: {\r\n      model: \"claude-3-5-sonnet-20241022\",\r\n      max_tokens: 4096,\r\n      temperature: 0.7,\r\n    } as AnthropicClientConfig,\r\n  },\r\n];\r\n\r\n// Tool Templates\r\nexport const TOOL_TEMPLATES: ComponentTemplate<ToolConfig>[] = [\r\n  {\r\n    id: \"function-tool\",\r\n    label: \"Function Tool\",\r\n    description: \"A custom Python function that can be called by agents\",\r\n    provider: PROVIDERS.FUNCTION_TOOL,\r\n    component_type: \"tool\",\r\n    version: 1,\r\n    component_version: 1,\r\n    config: {\r\n      name: \"my_function\",\r\n      description: \"A custom function that performs a specific task\",\r\n      source_code: `def my_function(input_text: str) -> str:\r\n    \"\"\"\r\n    A template function that processes input text.\r\n    \r\n    Args:\r\n        input_text: The text to process\r\n    \r\n    Returns:\r\n        Processed text result\r\n    \"\"\"\r\n    # Replace this with your custom function logic\r\n    result = f\"Processed: {input_text}\"\r\n    return result`,\r\n      global_imports: [],\r\n      has_cancellation_support: false,\r\n    } as FunctionToolConfig,\r\n  },\r\n  {\r\n    id: \"code-execution-tool\",\r\n    label: \"Code Execution Tool\",\r\n    description: \"Execute Python code in a secure environment\",\r\n    provider: PROVIDERS.PYTHON_CODE_EXECUTION_TOOL,\r\n    component_type: \"tool\",\r\n    version: 1,\r\n    component_version: 1,\r\n    config: {\r\n      executor: {\r\n        provider:\r\n          \"autogen_ext.code_executors.local.LocalCommandLineCodeExecutor\",\r\n        config: {\r\n          timeout: 60,\r\n          work_dir: \"./coding\",\r\n          functions_module: \"functions\",\r\n          cleanup_temp_files: true,\r\n        },\r\n      },\r\n      description: \"Execute Python code in a secure environment\",\r\n      name: \"code_execution\",\r\n    } as PythonCodeExecutionToolConfig,\r\n  },\r\n];\r\n\r\n// Workbench Templates\r\nexport const WORKBENCH_TEMPLATES: ComponentTemplate<WorkbenchConfig>[] = [\r\n  {\r\n    id: \"static-workbench\",\r\n    label: \"Static Workbench\",\r\n    description: \"A workbench with a collection of tools\",\r\n    provider: PROVIDERS.STATIC_WORKBENCH,\r\n    component_type: \"workbench\",\r\n    version: 1,\r\n    component_version: 1,\r\n    config: {\r\n      tools: [],\r\n    } as StaticWorkbenchConfig,\r\n  },\r\n  {\r\n    id: \"mcp-stdio-workbench\",\r\n    label: \"MCP Stdio Server\",\r\n    description: \"Model Context Protocol server via command line\",\r\n    provider: PROVIDERS.MCP_WORKBENCH,\r\n    component_type: \"workbench\",\r\n    version: 1,\r\n    component_version: 1,\r\n    config: {\r\n      server_params: {\r\n        type: \"StdioServerParams\",\r\n        command: \"npx\",\r\n        args: [\"@modelcontextprotocol/server-everything\"],\r\n        env: {},\r\n        read_timeout_seconds: 30,\r\n      } as StdioServerParams,\r\n    } as McpWorkbenchConfig,\r\n  },\r\n  {\r\n    id: \"mcp-sse-workbench\",\r\n    label: \"MCP SSE Server\",\r\n    description: \"Model Context Protocol server via server-sent events\",\r\n    provider: PROVIDERS.MCP_WORKBENCH,\r\n    component_type: \"workbench\",\r\n    version: 1,\r\n    component_version: 1,\r\n    config: {\r\n      server_params: {\r\n        type: \"SseServerParams\",\r\n        url: \"http://localhost:3001/sse\",\r\n        headers: {\r\n          Authorization: \"Bearer your-token-here\",\r\n        },\r\n        timeout: 30,\r\n        sse_read_timeout: 30,\r\n      } as SseServerParams,\r\n    } as McpWorkbenchConfig,\r\n  },\r\n  {\r\n    id: \"mcp-http-workbench\",\r\n    label: \"MCP HTTP Server\",\r\n    description: \"Model Context Protocol server via streamable HTTP\",\r\n    provider: PROVIDERS.MCP_WORKBENCH,\r\n    component_type: \"workbench\",\r\n    version: 1,\r\n    component_version: 1,\r\n    config: {\r\n      server_params: {\r\n        type: \"StreamableHttpServerParams\",\r\n        url: \"http://localhost:3001/mcp\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          Authorization: \"Bearer your-token-here\",\r\n        },\r\n        timeout: 30,\r\n        sse_read_timeout: 30,\r\n        terminate_on_close: true,\r\n      } as StreamableHttpServerParams,\r\n    } as McpWorkbenchConfig,\r\n  },\r\n];\r\n\r\n// Termination Templates\r\nexport const TERMINATION_TEMPLATES: ComponentTemplate<TerminationConfig>[] = [\r\n  {\r\n    id: \"text-mention-termination\",\r\n    label: \"Text Mention Termination\",\r\n    description: \"Terminate when a specific text is mentioned\",\r\n    provider: PROVIDERS.TEXT_MENTION,\r\n    component_type: \"termination\",\r\n    version: 1,\r\n    component_version: 1,\r\n    config: {\r\n      text: \"TERMINATE\",\r\n    } as TextMentionTerminationConfig,\r\n  },\r\n  {\r\n    id: \"max-message-termination\",\r\n    label: \"Max Message Termination\",\r\n    description: \"Terminate after a maximum number of messages\",\r\n    provider: PROVIDERS.MAX_MESSAGE,\r\n    component_type: \"termination\",\r\n    version: 1,\r\n    component_version: 1,\r\n    config: {\r\n      max_messages: 10,\r\n      include_agent_event: false,\r\n    } as MaxMessageTerminationConfig,\r\n  },\r\n  {\r\n    id: \"stop-message-termination\",\r\n    label: \"Stop Message Termination\",\r\n    description: \"Terminate when a StopMessage is received\",\r\n    provider: PROVIDERS.STOP_MESSAGE,\r\n    component_type: \"termination\",\r\n    version: 1,\r\n    component_version: 1,\r\n    config: {} as StopMessageTerminationConfig,\r\n  },\r\n  {\r\n    id: \"token-usage-termination\",\r\n    label: \"Token Usage Termination\",\r\n    description: \"Terminate when token usage limits are reached\",\r\n    provider: PROVIDERS.TOKEN_USAGE,\r\n    component_type: \"termination\",\r\n    version: 1,\r\n    component_version: 1,\r\n    config: {\r\n      max_total_token: 1000,\r\n    } as TokenUsageTerminationConfig,\r\n  },\r\n  {\r\n    id: \"timeout-termination\",\r\n    label: \"Timeout Termination\",\r\n    description: \"Terminate after a specified duration\",\r\n    provider: PROVIDERS.TIMEOUT,\r\n    component_type: \"termination\",\r\n    version: 1,\r\n    component_version: 1,\r\n    config: {\r\n      timeout_seconds: 300,\r\n    } as TimeoutTerminationConfig,\r\n  },\r\n  {\r\n    id: \"handoff-termination\",\r\n    label: \"Handoff Termination\",\r\n    description: \"Terminate when handoff to specific target is detected\",\r\n    provider: PROVIDERS.HANDOFF,\r\n    component_type: \"termination\",\r\n    version: 1,\r\n    component_version: 1,\r\n    config: {\r\n      target: \"user\",\r\n    } as HandoffTerminationConfig,\r\n  },\r\n  {\r\n    id: \"source-match-termination\",\r\n    label: \"Source Match Termination\",\r\n    description: \"Terminate when specific sources respond\",\r\n    provider: PROVIDERS.SOURCE_MATCH,\r\n    component_type: \"termination\",\r\n    version: 1,\r\n    component_version: 1,\r\n    config: {\r\n      sources: [\"agent1\"],\r\n    } as SourceMatchTerminationConfig,\r\n  },\r\n  {\r\n    id: \"text-message-termination\",\r\n    label: \"Text Message Termination\",\r\n    description: \"Terminate when a TextMessage is received\",\r\n    provider: PROVIDERS.TEXT_MESSAGE,\r\n    component_type: \"termination\",\r\n    version: 1,\r\n    component_version: 1,\r\n    config: {\r\n      source: undefined,\r\n    } as TextMessageTerminationConfig,\r\n  },\r\n  {\r\n    id: \"external-termination\",\r\n    label: \"External Termination\",\r\n    description: \"Terminate when externally controlled by calling set() method\",\r\n    provider: PROVIDERS.EXTERNAL,\r\n    component_type: \"termination\",\r\n    version: 1,\r\n    component_version: 1,\r\n    config: {} as ExternalTerminationConfig,\r\n  },\r\n  {\r\n    id: \"or-termination\",\r\n    label: \"OR Termination\",\r\n    description: \"Terminate when any of the conditions are met\",\r\n    provider: PROVIDERS.OR_TERMINATION,\r\n    component_type: \"termination\",\r\n    version: 1,\r\n    component_version: 1,\r\n    config: {\r\n      conditions: [\r\n        {\r\n          provider: PROVIDERS.TEXT_MENTION,\r\n          component_type: \"termination\",\r\n          config: { text: \"TERMINATE\" },\r\n        },\r\n        {\r\n          provider: PROVIDERS.MAX_MESSAGE,\r\n          component_type: \"termination\",\r\n          config: { max_messages: 20 },\r\n        },\r\n      ],\r\n    } as OrTerminationConfig,\r\n  },\r\n  {\r\n    id: \"and-termination\",\r\n    label: \"AND Termination\",\r\n    description: \"Terminate when all conditions are met\",\r\n    provider: PROVIDERS.AND_TERMINATION,\r\n    component_type: \"termination\",\r\n    version: 1,\r\n    component_version: 1,\r\n    config: {\r\n      conditions: [\r\n        {\r\n          provider: PROVIDERS.TEXT_MENTION,\r\n          component_type: \"termination\",\r\n          config: { text: \"TASK_COMPLETE\" },\r\n        },\r\n        {\r\n          provider: PROVIDERS.MAX_MESSAGE,\r\n          component_type: \"termination\",\r\n          config: { max_messages: 5 },\r\n        },\r\n      ],\r\n    } as AndTerminationConfig,\r\n  },\r\n];\r\n\r\n// Main template registry\r\nexport const COMPONENT_TEMPLATES = {\r\n  team: TEAM_TEMPLATES,\r\n  agent: AGENT_TEMPLATES,\r\n  model: MODEL_TEMPLATES,\r\n  tool: TOOL_TEMPLATES,\r\n  workbench: WORKBENCH_TEMPLATES,\r\n  termination: TERMINATION_TEMPLATES,\r\n} as const;\r\n\r\n// Helper functions\r\nexport function getTemplatesForType<T extends ComponentTypes>(\r\n  componentType: T\r\n): ComponentTemplate<ComponentConfig>[] {\r\n  return COMPONENT_TEMPLATES[componentType] || [];\r\n}\r\n\r\nexport function getTemplateById(\r\n  componentType: ComponentTypes,\r\n  templateId: string\r\n): ComponentTemplate<ComponentConfig> | undefined {\r\n  const templates = COMPONENT_TEMPLATES[componentType];\r\n  return templates.find((template) => template.id === templateId);\r\n}\r\n\r\nexport function getDefaultTemplate(\r\n  componentType: ComponentTypes\r\n): ComponentTemplate<ComponentConfig> | undefined {\r\n  const templates = COMPONENT_TEMPLATES[componentType];\r\n  return templates[0]; // Return first template as default\r\n}\r\n\r\nexport function createComponentFromTemplate(\r\n  templateId: string,\r\n  componentType: ComponentTypes,\r\n  overrides?: Partial<Component<ComponentConfig>>\r\n): Component<ComponentConfig> {\r\n  const template = getTemplateById(componentType, templateId);\r\n  if (!template) {\r\n    throw new Error(\r\n      `Template ${templateId} not found for component type ${componentType}`\r\n    );\r\n  }\r\n\r\n  return {\r\n    provider: template.provider,\r\n    component_type: template.component_type,\r\n    version: template.version,\r\n    component_version: template.component_version,\r\n    description: template.description,\r\n    config: template.config,\r\n    label: template.label,\r\n    ...overrides,\r\n  };\r\n}\r\n\r\n// Workbench-specific helper functions\r\nexport interface WorkbenchDropdownOption {\r\n  key: string;\r\n  label: string;\r\n  description: string;\r\n  templateId: string;\r\n}\r\n\r\nexport function getWorkbenchTemplatesForDropdown(): WorkbenchDropdownOption[] {\r\n  return WORKBENCH_TEMPLATES.map((template) => ({\r\n    key: template.id,\r\n    label: template.label,\r\n    description: template.description,\r\n    templateId: template.id,\r\n  }));\r\n}\r\n\r\nexport function createWorkbenchFromTemplate(\r\n  templateId: string,\r\n  customLabel?: string\r\n): Component<ComponentConfig> {\r\n  const template = getTemplateById(\"workbench\", templateId);\r\n  if (!template) {\r\n    throw new Error(`Workbench template ${templateId} not found`);\r\n  }\r\n\r\n  return createComponentFromTemplate(templateId, \"workbench\", {\r\n    label: customLabel || `New ${template.label}`,\r\n  });\r\n}\r\n\r\n// Generic dropdown option interface\r\nexport interface ComponentDropdownOption {\r\n  key: string;\r\n  label: string;\r\n  description: string;\r\n  templateId: string;\r\n}\r\n\r\n// Generic helper functions for all component types\r\nexport function getTemplatesForDropdown(\r\n  componentType: ComponentTypes\r\n): ComponentDropdownOption[] {\r\n  const templates = getTemplatesForType(componentType);\r\n  return templates.map((template) => ({\r\n    key: template.id,\r\n    label: template.label,\r\n    description: template.description,\r\n    templateId: template.id,\r\n  }));\r\n}\r\n\r\nexport function createComponentFromTemplateById(\r\n  componentType: ComponentTypes,\r\n  templateId: string,\r\n  customLabel?: string\r\n): Component<ComponentConfig> {\r\n  const template = getTemplateById(componentType, templateId);\r\n  if (!template) {\r\n    throw new Error(`${componentType} template ${templateId} not found`);\r\n  }\r\n\r\n  return createComponentFromTemplate(templateId, componentType, {\r\n    label: customLabel || `New ${template.label}`,\r\n  });\r\n}\r\n\r\n// Specific helper functions for each component type\r\nexport function getTeamTemplatesForDropdown(): ComponentDropdownOption[] {\r\n  return getTemplatesForDropdown(\"team\");\r\n}\r\n\r\nexport function createTeamFromTemplate(\r\n  templateId: string,\r\n  customLabel?: string\r\n): Component<ComponentConfig> {\r\n  return createComponentFromTemplateById(\"team\", templateId, customLabel);\r\n}\r\n\r\nexport function getAgentTemplatesForDropdown(): ComponentDropdownOption[] {\r\n  return getTemplatesForDropdown(\"agent\");\r\n}\r\n\r\nexport function createAgentFromTemplate(\r\n  templateId: string,\r\n  customLabel?: string\r\n): Component<ComponentConfig> {\r\n  return createComponentFromTemplateById(\"agent\", templateId, customLabel);\r\n}\r\n\r\nexport function getModelTemplatesForDropdown(): ComponentDropdownOption[] {\r\n  return getTemplatesForDropdown(\"model\");\r\n}\r\n\r\nexport function createModelFromTemplate(\r\n  templateId: string,\r\n  customLabel?: string\r\n): Component<ComponentConfig> {\r\n  return createComponentFromTemplateById(\"model\", templateId, customLabel);\r\n}\r\n\r\nexport function getToolTemplatesForDropdown(): ComponentDropdownOption[] {\r\n  return getTemplatesForDropdown(\"tool\");\r\n}\r\n\r\nexport function createToolFromTemplate(\r\n  templateId: string,\r\n  customLabel?: string\r\n): Component<ComponentConfig> {\r\n  return createComponentFromTemplateById(\"tool\", templateId, customLabel);\r\n}\r\n\r\nexport function getTerminationTemplatesForDropdown(): ComponentDropdownOption[] {\r\n  return getTemplatesForDropdown(\"termination\");\r\n}\r\n\r\nexport function createTerminationFromTemplate(\r\n  templateId: string,\r\n  customLabel?: string\r\n): Component<ComponentConfig> {\r\n  return createComponentFromTemplateById(\r\n    \"termination\",\r\n    templateId,\r\n    customLabel\r\n  );\r\n}\r\n", "import React from \"react\";\r\nimport { But<PERSON>, Dropdown, message } from \"antd\";\r\nimport { Plus, ChevronDown } from \"lucide-react\";\r\nimport {\r\n  Component,\r\n  ComponentConfig,\r\n  ComponentTypes,\r\n  Gallery,\r\n} from \"../types/datamodel\";\r\nimport {\r\n  getTeamTemplatesForDropdown,\r\n  createTeamFromTemplate,\r\n  getAgentTemplatesForDropdown,\r\n  createAgentFromTemplate,\r\n  getModelTemplatesForDropdown,\r\n  createModelFromTemplate,\r\n  getToolTemplatesForDropdown,\r\n  createToolFromTemplate,\r\n  getWorkbenchTemplatesForDropdown,\r\n  createWorkbenchFromTemplate,\r\n  getTerminationTemplatesForDropdown,\r\n  createTerminationFromTemplate,\r\n  type ComponentDropdownOption,\r\n} from \"../types/component-templates\";\r\n\r\ntype CategoryKey =\r\n  | \"teams\"\r\n  | \"agents\"\r\n  | \"models\"\r\n  | \"tools\"\r\n  | \"workbenches\"\r\n  | \"terminations\";\r\n\r\n// Helper function to get the correct category key for components\r\nconst getCategoryKey = (componentType: ComponentTypes): CategoryKey => {\r\n  const mapping: Record<ComponentTypes, CategoryKey> = {\r\n    team: \"teams\",\r\n    agent: \"agents\",\r\n    model: \"models\",\r\n    tool: \"tools\",\r\n    workbench: \"workbenches\",\r\n    termination: \"terminations\",\r\n  };\r\n  return mapping[componentType];\r\n};\r\n\r\ninterface AddComponentDropdownProps {\r\n  componentType: ComponentTypes;\r\n  gallery: Gallery;\r\n  onComponentAdded: (\r\n    component: Component<ComponentConfig>,\r\n    category: CategoryKey\r\n  ) => void;\r\n  disabled?: boolean;\r\n  showIcon?: boolean;\r\n  showChevron?: boolean;\r\n  size?: \"small\" | \"middle\" | \"large\";\r\n  type?: \"default\" | \"primary\" | \"dashed\" | \"link\" | \"text\";\r\n  className?: string;\r\n  buttonText?: string;\r\n  // Optional filter for specific component types (useful for MCP workbenches)\r\n  templateFilter?: (template: ComponentDropdownOption) => boolean;\r\n}\r\n\r\nexport const AddComponentDropdown: React.FC<AddComponentDropdownProps> = ({\r\n  componentType,\r\n  gallery,\r\n  onComponentAdded,\r\n  disabled = false,\r\n  showIcon = true,\r\n  showChevron = true,\r\n  size = \"middle\",\r\n  type = \"primary\",\r\n  className = \"\",\r\n  buttonText,\r\n  templateFilter,\r\n}) => {\r\n  const [messageApi, contextHolder] = message.useMessage();\r\n\r\n  // Helper function to get dropdown templates for each component type\r\n  const getDropdownTemplatesForType = (\r\n    componentType: ComponentTypes\r\n  ): ComponentDropdownOption[] => {\r\n    let templates: ComponentDropdownOption[] = [];\r\n\r\n    switch (componentType) {\r\n      case \"team\":\r\n        templates = getTeamTemplatesForDropdown();\r\n        break;\r\n      case \"agent\":\r\n        templates = getAgentTemplatesForDropdown();\r\n        break;\r\n      case \"model\":\r\n        templates = getModelTemplatesForDropdown();\r\n        break;\r\n      case \"tool\":\r\n        templates = getToolTemplatesForDropdown();\r\n        break;\r\n      case \"workbench\":\r\n        templates = getWorkbenchTemplatesForDropdown();\r\n        break;\r\n      case \"termination\":\r\n        templates = getTerminationTemplatesForDropdown();\r\n        break;\r\n      default:\r\n        templates = [];\r\n    }\r\n\r\n    // Apply filter if provided\r\n    if (templateFilter) {\r\n      templates = templates.filter(templateFilter);\r\n    }\r\n\r\n    return templates;\r\n  };\r\n\r\n  // Generic handler for all component types\r\n  const handleAddComponentFromTemplate = (\r\n    componentType: ComponentTypes,\r\n    templateId: string\r\n  ) => {\r\n    const category = getCategoryKey(componentType);\r\n\r\n    try {\r\n      let newComponent: Component<ComponentConfig>;\r\n\r\n      switch (componentType) {\r\n        case \"team\":\r\n          newComponent = createTeamFromTemplate(templateId);\r\n          break;\r\n        case \"agent\":\r\n          newComponent = createAgentFromTemplate(templateId);\r\n          break;\r\n        case \"model\":\r\n          newComponent = createModelFromTemplate(templateId);\r\n          break;\r\n        case \"tool\":\r\n          newComponent = createToolFromTemplate(templateId);\r\n          break;\r\n        case \"workbench\":\r\n          newComponent = createWorkbenchFromTemplate(templateId);\r\n          break;\r\n        case \"termination\":\r\n          newComponent = createTerminationFromTemplate(templateId);\r\n          break;\r\n        default:\r\n          throw new Error(`Unsupported component type: ${componentType}`);\r\n      }\r\n\r\n      onComponentAdded(newComponent, category);\r\n    } catch (error) {\r\n      console.error(`Error creating ${componentType} from template:`, error);\r\n      messageApi.error(`Failed to create ${componentType}`);\r\n    }\r\n  };\r\n\r\n  const templates = getDropdownTemplatesForType(componentType);\r\n\r\n  // Don't render if no templates available\r\n  if (templates.length === 0) {\r\n    return null;\r\n  }\r\n\r\n  const displayButtonText =\r\n    buttonText ||\r\n    `Add ${componentType.charAt(0).toUpperCase() + componentType.slice(1)}`;\r\n\r\n  return (\r\n    <>\r\n      {contextHolder}\r\n      <Dropdown\r\n        menu={{\r\n          items: templates.map((template) => ({\r\n            key: template.key,\r\n            label: (\r\n              <div className=\"py-1\">\r\n                <div className=\"font-medium\">{template.label}</div>\r\n                <div className=\"text-xs text-secondary\">\r\n                  {template.description}\r\n                </div>\r\n              </div>\r\n            ),\r\n            onClick: () =>\r\n              handleAddComponentFromTemplate(\r\n                componentType,\r\n                template.templateId\r\n              ),\r\n          })),\r\n        }}\r\n        trigger={[\"click\"]}\r\n        disabled={disabled}\r\n      >\r\n        <Button\r\n          type={type}\r\n          size={size}\r\n          icon={showIcon ? <Plus className=\"w-4 h-4\" /> : undefined}\r\n          disabled={disabled}\r\n          className={`flex items-center gap-1 ${className}`}\r\n        >\r\n          {displayButtonText}\r\n          {showChevron && <ChevronDown className=\"w-3 h-3\" />}\r\n        </Button>\r\n      </Dropdown>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default AddComponentDropdown;\r\n", "import React, { useState, useEffect, useCallback, useRef } from \"react\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>,\r\n  Drawer,\r\n  Input,\r\n  message,\r\n  Dropdown,\r\n  Tag,\r\n} from \"antd\";\r\nimport {\r\n  Package,\r\n  Users,\r\n  Bot,\r\n  Globe,\r\n  Wrench,\r\n  Brain,\r\n  Timer,\r\n  Edit,\r\n  Copy,\r\n  Trash,\r\n  Plus,\r\n  Download,\r\n  Briefcase,\r\n  Code,\r\n  FormInput,\r\n  ChevronDown,\r\n  Server,\r\n} from \"lucide-react\";\r\nimport { ComponentEditor } from \"../teambuilder/builder/component-editor/component-editor\";\r\nimport { MonacoEditor } from \"../monaco\";\r\nimport { TruncatableText } from \"../atoms\";\r\nimport {\r\n  Component,\r\n  ComponentConfig,\r\n  ComponentTypes,\r\n  Gallery,\r\n} from \"../../types/datamodel\";\r\nimport {\r\n  getTemplatesForDropdown,\r\n  createComponentFromTemplateById,\r\n  getTeamTemplatesForDropdown,\r\n  createTeamFromTemplate,\r\n  getAgentTemplatesForDropdown,\r\n  createAgentFromTemplate,\r\n  getModelTemplatesForDropdown,\r\n  createModelFromTemplate,\r\n  getToolTemplatesForDropdown,\r\n  createToolFromTemplate,\r\n  getWorkbenchTemplatesForDropdown,\r\n  createWorkbenchFromTemplate,\r\n  getTerminationTemplatesForDropdown,\r\n  createTerminationFromTemplate,\r\n  type ComponentDropdownOption,\r\n} from \"../../types/component-templates\";\r\nimport { isMcpWorkbench } from \"../../types/guards\";\r\nimport TextArea from \"antd/es/input/TextArea\";\r\nimport Icon from \"../../icons\";\r\nimport { AddComponentDropdown } from \"../../shared\";\r\n\r\ntype CategoryKey =\r\n  | \"teams\"\r\n  | \"agents\"\r\n  | \"models\"\r\n  | \"tools\"\r\n  | \"workbenches\"\r\n  | \"terminations\";\r\n\r\n// Helper function to get the correct category key for components\r\nconst getCategoryKey = (componentType: ComponentTypes): CategoryKey => {\r\n  const mapping: Record<ComponentTypes, CategoryKey> = {\r\n    team: \"teams\",\r\n    agent: \"agents\",\r\n    model: \"models\",\r\n    tool: \"tools\",\r\n    workbench: \"workbenches\",\r\n    termination: \"terminations\",\r\n  };\r\n  return mapping[componentType];\r\n};\r\n\r\ninterface CardActions {\r\n  onEdit: (component: Component<ComponentConfig>, index: number) => void;\r\n  onDuplicate: (component: Component<ComponentConfig>, index: number) => void;\r\n  onDelete: (component: Component<ComponentConfig>, index: number) => void;\r\n}\r\n\r\n// Component Card\r\nconst ComponentCard: React.FC<\r\n  CardActions & {\r\n    item: Component<ComponentConfig>;\r\n    index: number;\r\n    allowDelete: boolean;\r\n    disabled?: boolean;\r\n  }\r\n> = ({\r\n  item,\r\n  onEdit,\r\n  onDuplicate,\r\n  onDelete,\r\n  index,\r\n  allowDelete,\r\n  disabled = false,\r\n}) => (\r\n  <div\r\n    className={`bg-secondary rounded overflow-hidden group h-full ${\r\n      disabled ? \"opacity-50 cursor-not-allowed\" : \"cursor-pointer\"\r\n    }`}\r\n    onClick={() => !disabled && onEdit(item, index)}\r\n  >\r\n    <div className=\"px-4 py-3 flex items-center justify-between border-b border-tertiary\">\r\n      <div className=\"text-xs text-secondary truncate flex-1\">\r\n        {item.label || \"Unnamed Component\"}\r\n      </div>\r\n      <div className=\"flex gap-0\">\r\n        {allowDelete && (\r\n          <Button\r\n            title=\"Delete\"\r\n            type=\"text\"\r\n            className=\"h-6 w-6 flex items-center justify-center p-0 opacity-0 group-hover:opacity-100 transition-opacity text-red-500 hover:text-red-600\"\r\n            icon={<Trash className=\"w-3.5 h-3.5\" />}\r\n            disabled={disabled}\r\n            onClick={(e) => {\r\n              e.stopPropagation();\r\n              if (!disabled) onDelete(item, index);\r\n            }}\r\n          />\r\n        )}\r\n        <Button\r\n          title=\"Duplicate\"\r\n          type=\"text\"\r\n          className=\"h-6 w-6 flex items-center justify-center p-0 opacity-0 group-hover:opacity-100 transition-opacity\"\r\n          icon={<Copy className=\"w-3.5 h-3.5\" />}\r\n          disabled={disabled}\r\n          onClick={(e) => {\r\n            e.stopPropagation();\r\n            if (!disabled) onDuplicate(item, index);\r\n          }}\r\n        />\r\n        <Button\r\n          title=\"Edit\"\r\n          type=\"text\"\r\n          className=\"h-6 w-6 flex items-center justify-center p-0 opacity-0 group-hover:opacity-100 transition-opacity\"\r\n          icon={<Edit className=\"w-3.5 h-3.5\" />}\r\n          disabled={disabled}\r\n          onClick={(e) => {\r\n            e.stopPropagation();\r\n            if (!disabled) onEdit(item, index);\r\n          }}\r\n        />\r\n      </div>\r\n    </div>\r\n    <div className=\"p-4 pb-0 pt-3\">\r\n      <div className=\"text-base font-medium mb-2 flex items-center gap-2\">\r\n        {item.component_type === \"workbench\" && isMcpWorkbench(item) && (\r\n          <Icon icon=\"mcp\" size={6} className=\"inline-block\" />\r\n        )}{\" \"}\r\n        <span className=\"line-clamp-1\">\r\n          {item.label || \"Unnamed Component\"}\r\n        </span>\r\n      </div>\r\n      <div className=\"text-xs text-secondary truncate mb-2\">\r\n        {item.provider}\r\n      </div>\r\n      <div className=\"text-sm text-secondary line-clamp-2 mb-3 min-h-[40px]\">\r\n        <TruncatableText\r\n          content={item.description || \"\"}\r\n          showFullscreen={false}\r\n          textThreshold={70}\r\n        />\r\n      </div>\r\n    </div>\r\n  </div>\r\n);\r\n\r\n// Component Grid\r\nconst ComponentGrid: React.FC<\r\n  {\r\n    items: Component<ComponentConfig>[];\r\n    title: string;\r\n    disabled?: boolean;\r\n  } & CardActions\r\n> = ({ items, title, disabled = false, ...actions }) => (\r\n  <div>\r\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 auto-rows-fr\">\r\n      {items.map((item, idx) => (\r\n        <ComponentCard\r\n          key={idx}\r\n          item={item}\r\n          index={idx}\r\n          allowDelete={items.length > 1}\r\n          disabled={disabled}\r\n          {...actions}\r\n        />\r\n      ))}\r\n    </div>\r\n  </div>\r\n);\r\n\r\nconst iconMap = {\r\n  team: Users,\r\n  agent: Bot,\r\n  tool: Wrench,\r\n  model: Brain,\r\n  termination: Timer,\r\n  workbench: Briefcase,\r\n} as const;\r\n\r\nexport const GalleryDetail: React.FC<{\r\n  gallery: Gallery;\r\n  onSave: (updates: Partial<Gallery>) => void;\r\n  onDirtyStateChange: (isDirty: boolean) => void;\r\n}> = ({ gallery, onSave, onDirtyStateChange }) => {\r\n  if (!gallery.config.components) {\r\n    return <div className=\"text-secondary\">No components found</div>;\r\n  }\r\n  const [editingComponent, setEditingComponent] = useState<{\r\n    component: Component<ComponentConfig>;\r\n    category: CategoryKey;\r\n    index: number;\r\n  } | null>(null);\r\n  const [activeTab, setActiveTab] = useState<ComponentTypes>(\"team\");\r\n  const [isEditingDetails, setIsEditingDetails] = useState(false);\r\n  const [isJsonEditing, setIsJsonEditing] = useState(false);\r\n  const [workingCopy, setWorkingCopy] = useState<Gallery>(gallery);\r\n  const [jsonValue, setJsonValue] = useState<string>(\"\");\r\n  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);\r\n  const [isDirty, setIsDirty] = useState(false);\r\n  const [tempName, setTempName] = useState(gallery.config.name);\r\n  const [tempDescription, setTempDescription] = useState(\r\n    gallery.config.metadata.description\r\n  );\r\n\r\n  const [messageApi, contextHolder] = message.useMessage();\r\n  const editorRef = useRef(null);\r\n\r\n  useEffect(() => {\r\n    setTempName(gallery.config.name);\r\n    setTempDescription(gallery.config.metadata.description);\r\n    setWorkingCopy(gallery);\r\n    setJsonValue(JSON.stringify(gallery, null, 2));\r\n    setHasUnsavedChanges(false);\r\n    setIsDirty(false);\r\n    setActiveTab(\"team\");\r\n    setEditingComponent(null);\r\n    setIsJsonEditing(false);\r\n  }, [gallery.id]);\r\n\r\n  const updateGallery = (\r\n    category: CategoryKey,\r\n    updater: (\r\n      components: Component<ComponentConfig>[]\r\n    ) => Component<ComponentConfig>[]\r\n  ) => {\r\n    const currentGallery = isJsonEditing ? workingCopy : gallery;\r\n    const updatedGallery = {\r\n      ...currentGallery,\r\n      config: {\r\n        ...currentGallery.config,\r\n        components: {\r\n          ...currentGallery.config.components,\r\n          [category]: updater(currentGallery.config.components[category]),\r\n        },\r\n      },\r\n    };\r\n\r\n    if (isJsonEditing) {\r\n      setWorkingCopy(updatedGallery);\r\n    } else {\r\n      onSave(updatedGallery);\r\n      onDirtyStateChange(true);\r\n    }\r\n  };\r\n\r\n  const handleJsonChange = (value: string) => {\r\n    setJsonValue(value);\r\n    setIsDirty(true);\r\n  };\r\n\r\n  const handlers = {\r\n    onEdit: (component: Component<ComponentConfig>, index: number) => {\r\n      setEditingComponent({\r\n        component,\r\n        category: getCategoryKey(activeTab),\r\n        index,\r\n      });\r\n    },\r\n\r\n    onDuplicate: (component: Component<ComponentConfig>, index: number) => {\r\n      const category = getCategoryKey(activeTab);\r\n      const baseLabel = component.label?.replace(/_\\d+$/, \"\");\r\n      const components = gallery.config.components[category] || [];\r\n\r\n      const nextNumber =\r\n        Math.max(\r\n          ...components\r\n            .map((c: Component<ComponentConfig>) => {\r\n              const match = c.label?.match(\r\n                new RegExp(`^${baseLabel}_?(\\\\d+)?$`)\r\n              );\r\n              return match ? parseInt(match[1] || \"0\") : 0;\r\n            })\r\n            .filter((n: number) => !isNaN(n)),\r\n          0\r\n        ) + 1;\r\n\r\n      updateGallery(category, (components) => [\r\n        ...components,\r\n        { ...component, label: `${baseLabel}_${nextNumber}` },\r\n      ]);\r\n    },\r\n\r\n    onDelete: (component: Component<ComponentConfig>, index: number) => {\r\n      const category = getCategoryKey(activeTab);\r\n      updateGallery(category, (components) =>\r\n        components.filter((_, i) => i !== index)\r\n      );\r\n    },\r\n  };\r\n\r\n  // Handler for the reusable AddComponentDropdown\r\n  const handleComponentAdded = (\r\n    newComponent: Component<ComponentConfig>,\r\n    category: CategoryKey\r\n  ) => {\r\n    updateGallery(category, (components) => {\r\n      const newComponents = [...components, newComponent];\r\n      setEditingComponent({\r\n        component: newComponent,\r\n        category,\r\n        index: newComponents.length - 1,\r\n      });\r\n      return newComponents;\r\n    });\r\n  };\r\n\r\n  const handleAddWorkbench = (templateId: string) => {\r\n    const category = getCategoryKey(\"workbench\");\r\n\r\n    try {\r\n      const newComponent = createWorkbenchFromTemplate(templateId);\r\n\r\n      updateGallery(category, (components) => {\r\n        const newComponents = [...components, newComponent];\r\n        setEditingComponent({\r\n          component: newComponent,\r\n          category,\r\n          index: newComponents.length - 1,\r\n        });\r\n        return newComponents;\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Error creating workbench from template:\", error);\r\n      messageApi.error(\"Failed to create workbench\");\r\n    }\r\n  };\r\n\r\n  const handleComponentUpdate = (\r\n    updatedComponent: Component<ComponentConfig>\r\n  ) => {\r\n    if (!editingComponent) return;\r\n\r\n    updateGallery(editingComponent.category, (components) =>\r\n      components.map((c, i) =>\r\n        i === editingComponent.index ? updatedComponent : c\r\n      )\r\n    );\r\n    setEditingComponent(null);\r\n  };\r\n\r\n  const handleJsonSave = () => {\r\n    try {\r\n      const updatedGallery = JSON.parse(jsonValue);\r\n      setWorkingCopy(updatedGallery);\r\n      onSave(updatedGallery);\r\n      onDirtyStateChange(true);\r\n      setIsJsonEditing(false);\r\n      setIsDirty(false);\r\n      messageApi.success(\"Gallery updated successfully!\");\r\n    } catch (error) {\r\n      messageApi.error(\"Invalid JSON format. Please check your syntax.\");\r\n      console.error(\"JSON parse error:\", error);\r\n    }\r\n  };\r\n\r\n  const handleDetailsSave = () => {\r\n    const updatedGallery = {\r\n      ...gallery,\r\n      config: {\r\n        ...gallery.config,\r\n        name: tempName,\r\n        metadata: {\r\n          ...gallery.config.metadata,\r\n          description: tempDescription,\r\n        },\r\n      },\r\n    };\r\n    onSave(updatedGallery);\r\n    onDirtyStateChange(true);\r\n    setIsEditingDetails(false);\r\n  };\r\n\r\n  const handleDownload = () => {\r\n    const dataStr = JSON.stringify(gallery, null, 2);\r\n    const dataBlob = new Blob([dataStr], { type: \"application/json\" });\r\n    const url = URL.createObjectURL(dataBlob);\r\n    const link = document.createElement(\"a\");\r\n    link.href = url;\r\n    link.download = `${gallery.config.name\r\n      .toLowerCase()\r\n      .replace(/\\s+/g, \"_\")}.json`;\r\n    document.body.appendChild(link);\r\n    link.click();\r\n    document.body.removeChild(link);\r\n    URL.revokeObjectURL(url);\r\n  };\r\n\r\n  const currentGallery = isJsonEditing ? workingCopy : gallery;\r\n\r\n  const tabItems = Object.entries(iconMap).map(([key, Icon]) => ({\r\n    key,\r\n    label: (\r\n      <span className=\"flex items-center gap-2\">\r\n        <Icon className=\"w-5 h-5\" />\r\n        {key.charAt(0).toUpperCase() + key.slice(1)}s\r\n        <span className=\"text-xs font-light text-secondary\">\r\n          (\r\n          {currentGallery.config.components[\r\n            getCategoryKey(key as ComponentTypes)\r\n          ]?.length || 0}\r\n          )\r\n        </span>\r\n      </span>\r\n    ),\r\n    children: (\r\n      <div>\r\n        <div className=\"flex justify-between items-center mb-4\">\r\n          <h3 className=\"text-base font-medium\">\r\n            {currentGallery.config.components[\r\n              getCategoryKey(key as ComponentTypes)\r\n            ]?.length || 0}{\" \"}\r\n            {(currentGallery.config.components[\r\n              getCategoryKey(key as ComponentTypes)\r\n            ]?.length || 0) === 1\r\n              ? key.charAt(0).toUpperCase() + key.slice(1)\r\n              : key.charAt(0).toUpperCase() + key.slice(1) + \"s\"}\r\n          </h3>\r\n          <AddComponentDropdown\r\n            componentType={key as ComponentTypes}\r\n            gallery={currentGallery}\r\n            onComponentAdded={handleComponentAdded}\r\n            disabled={isJsonEditing}\r\n          />\r\n        </div>\r\n        <ComponentGrid\r\n          items={\r\n            currentGallery.config.components[\r\n              getCategoryKey(key as ComponentTypes)\r\n            ] || []\r\n          }\r\n          title={key}\r\n          disabled={isJsonEditing}\r\n          {...handlers}\r\n        />\r\n      </div>\r\n    ),\r\n  }));\r\n\r\n  return (\r\n    <div className=\"max-w-7xl px-4\">\r\n      {contextHolder}\r\n\r\n      <div className=\"relative h-64 rounded bg-secondary overflow-hidden mb-8\">\r\n        <img\r\n          src=\"/images/bg/layeredbg.svg\"\r\n          alt=\"Gallery Banner\"\r\n          className=\"absolute w-full h-full object-cover\"\r\n        />\r\n        <div className=\"relative z-10 p-6 h-full flex flex-col justify-between\">\r\n          <div>\r\n            <div className=\"flex items-center justify-between\">\r\n              <div className=\"flex items-center gap-2\">\r\n                {isEditingDetails ? (\r\n                  <Input\r\n                    value={tempName}\r\n                    onChange={(e) => setTempName(e.target.value)}\r\n                    className=\"text-2xl font-medium bg-background/50 backdrop-blur px-2 py-1 rounded w-[400px]\"\r\n                  />\r\n                ) : (\r\n                  <h1 className=\"text-2xl font-medium text-primary\">\r\n                    {currentGallery.config.name}\r\n                  </h1>\r\n                )}\r\n                {currentGallery.config.url && (\r\n                  <Tooltip title=\"Remote Gallery\">\r\n                    <Globe className=\"w-5 h-5 text-secondary\" />\r\n                  </Tooltip>\r\n                )}\r\n              </div>\r\n            </div>\r\n            {isEditingDetails ? (\r\n              <TextArea\r\n                value={tempDescription}\r\n                onChange={(e) => setTempDescription(e.target.value)}\r\n                className=\"w-1/2 bg-background/50 backdrop-blur px-2 py-1 rounded mt-2\"\r\n                rows={2}\r\n              />\r\n            ) : (\r\n              <div className=\"flex flex-col gap-2\">\r\n                <p className=\"text-secondary w-1/2 mt-2 line-clamp-2\">\r\n                  {currentGallery.config.metadata.description}\r\n                </p>\r\n                <div className=\"flex gap-0\">\r\n                  <Tooltip title=\"Edit Gallery\">\r\n                    <Button\r\n                      icon={<Edit className=\"w-4 h-4\" />}\r\n                      onClick={() => setIsEditingDetails(true)}\r\n                      type=\"text\"\r\n                      className=\"text-white hover:text-white/80\"\r\n                      disabled={isJsonEditing}\r\n                    />\r\n                  </Tooltip>\r\n                  <Tooltip title=\"Download Gallery\">\r\n                    <Button\r\n                      icon={<Download className=\"w-4 h-4\" />}\r\n                      onClick={handleDownload}\r\n                      type=\"text\"\r\n                      className=\"text-white hover:text-white/80\"\r\n                    />\r\n                  </Tooltip>\r\n                  <Tooltip\r\n                    title={isJsonEditing ? \"Form Editor\" : \"JSON Editor\"}\r\n                  >\r\n                    <Button\r\n                      icon={\r\n                        isJsonEditing ? (\r\n                          <FormInput className=\"w-4 h-4\" />\r\n                        ) : (\r\n                          <Code className=\"w-4 h-4\" />\r\n                        )\r\n                      }\r\n                      onClick={() => {\r\n                        const newJsonMode = !isJsonEditing;\r\n                        setIsJsonEditing(newJsonMode);\r\n                        if (newJsonMode) {\r\n                          setJsonValue(JSON.stringify(currentGallery, null, 2));\r\n                          setIsDirty(false);\r\n                        }\r\n                      }}\r\n                      type=\"text\"\r\n                      className=\"text-white hover:text-white/80\"\r\n                    />\r\n                  </Tooltip>\r\n                </div>\r\n              </div>\r\n            )}\r\n            {isEditingDetails && (\r\n              <div className=\"flex gap-2 mt-2\">\r\n                <Button onClick={() => setIsEditingDetails(false)}>\r\n                  Cancel\r\n                </Button>\r\n                <Button type=\"primary\" onClick={handleDetailsSave}>\r\n                  Save\r\n                </Button>\r\n              </div>\r\n            )}\r\n          </div>\r\n          <div className=\"flex gap-2\">\r\n            <div className=\"bg-tertiary backdrop-blur rounded p-2 flex items-center gap-2\">\r\n              <Package className=\"w-4 h-4 text-secondary\" />\r\n              <span className=\"text-sm\">\r\n                {Object.values(currentGallery.config.components).reduce(\r\n                  (sum, arr) => sum + arr.length,\r\n                  0\r\n                )}{\" \"}\r\n                components\r\n              </span>\r\n            </div>\r\n            <div className=\"bg-tertiary backdrop-blur rounded p-2 text-sm\">\r\n              v{currentGallery.config.metadata.version}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {isJsonEditing ? (\r\n        <div className=\"space-y-4\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <h2 className=\"text-xl font-medium\">JSON Editor</h2>\r\n              {isDirty && (\r\n                <span className=\"text-orange-500 text-sm\">\r\n                  • Unsaved changes\r\n                </span>\r\n              )}\r\n            </div>\r\n            <div className=\"flex gap-2\">\r\n              <Button\r\n                onClick={() => {\r\n                  setIsJsonEditing(false);\r\n                  setIsDirty(false);\r\n                  setJsonValue(JSON.stringify(gallery, null, 2));\r\n                }}\r\n              >\r\n                Cancel\r\n              </Button>\r\n              <Button type=\"primary\" onClick={handleJsonSave}>\r\n                Save Changes\r\n              </Button>\r\n            </div>\r\n          </div>\r\n          <div className=\"h-[600px] border border-secondary rounded\">\r\n            <MonacoEditor\r\n              editorRef={editorRef}\r\n              value={jsonValue}\r\n              onChange={handleJsonChange}\r\n              language=\"json\"\r\n              minimap={true}\r\n            />\r\n          </div>\r\n        </div>\r\n      ) : (\r\n        <Tabs\r\n          items={tabItems}\r\n          className=\"gallery-tabs\"\r\n          size=\"large\"\r\n          onChange={(key) => setActiveTab(key as ComponentTypes)}\r\n        />\r\n      )}\r\n\r\n      <Drawer\r\n        title=\"Edit Component\"\r\n        placement=\"right\"\r\n        size=\"large\"\r\n        onClose={() => setEditingComponent(null)}\r\n        open={!!editingComponent}\r\n        className=\"component-editor-drawer\"\r\n      >\r\n        {editingComponent && (\r\n          <ComponentEditor\r\n            component={editingComponent.component}\r\n            onChange={handleComponentUpdate}\r\n            onClose={() => setEditingComponent(null)}\r\n            navigationDepth={true}\r\n          />\r\n        )}\r\n      </Drawer>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default GalleryDetail;\r\n", "import { GalleryConfig } from \"../../types/datamodel\";\r\n\r\n// Load and parse the gallery JSON file\r\nconst loadGalleryFromJson = (): GalleryConfig => {\r\n  try {\r\n    // You can adjust the path to your JSON file as needed\r\n    const galleryJson = require(\"./default_gallery.json\");\r\n    return galleryJson as GalleryConfig;\r\n  } catch (error) {\r\n    console.error(\"Error loading gallery JSON:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const defaultGallery: GalleryConfig = loadGalleryFromJson();\r\n", "import React, { useState, useRef } from \"react\";\r\nimport { <PERSON><PERSON>, Tabs, Input, Button, Alert, Upload } from \"antd\";\r\nimport { Globe, Upload as UploadIcon, Code } from \"lucide-react\";\r\nimport { MonacoEditor } from \"../monaco\";\r\nimport type { InputRef, UploadFile, UploadProps } from \"antd\";\r\nimport { defaultGallery } from \"./utils\";\r\nimport { Gallery, GalleryConfig } from \"../../types/datamodel\";\r\n\r\ninterface GalleryCreateModalProps {\r\n  open: boolean;\r\n  onCancel: () => void;\r\n  onCreateGallery: (gallery: Gallery) => void;\r\n}\r\n\r\nexport const GalleryCreateModal: React.FC<GalleryCreateModalProps> = ({\r\n  open,\r\n  onCancel,\r\n  onCreateGallery,\r\n}) => {\r\n  const [activeTab, setActiveTab] = useState(\"url\");\r\n  const [url, setUrl] = useState(\"\");\r\n  const [jsonContent, setJsonContent] = useState(\r\n    JSON.stringify(defaultGallery, null, 2)\r\n  );\r\n  const [error, setError] = useState(\"\");\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const editorRef = useRef(null);\r\n\r\n  const handleUrlImport = async () => {\r\n    setIsLoading(true);\r\n    setError(\"\");\r\n    try {\r\n      const response = await fetch(url);\r\n      const data = (await response.json()) as GalleryConfig;\r\n      // TODO: Validate against Gallery schema\r\n      onCreateGallery({\r\n        config: data,\r\n      });\r\n      onCancel();\r\n    } catch (err) {\r\n      setError(\"Failed to fetch or parse gallery from URL\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleFileUpload = (info: { file: UploadFile }) => {\r\n    const { status, originFileObj } = info.file;\r\n    if (status === \"done\" && originFileObj instanceof File) {\r\n      const reader = new FileReader();\r\n      reader.onload = (e: ProgressEvent<FileReader>) => {\r\n        try {\r\n          const content = JSON.parse(\r\n            e.target?.result as string\r\n          ) as GalleryConfig;\r\n\r\n          // TODO: Validate against Gallery schema\r\n          onCreateGallery({\r\n            config: content,\r\n          });\r\n          onCancel();\r\n        } catch (err) {\r\n          setError(\"Invalid JSON file\");\r\n        }\r\n      };\r\n      reader.readAsText(originFileObj);\r\n    } else if (status === \"error\") {\r\n      setError(\"File upload failed\");\r\n    }\r\n  };\r\n\r\n  const handlePasteImport = () => {\r\n    try {\r\n      const content = JSON.parse(jsonContent) as GalleryConfig;\r\n      // TODO: Validate against Gallery schema\r\n      onCreateGallery({\r\n        config: content,\r\n      });\r\n      onCancel();\r\n    } catch (err) {\r\n      setError(\"Invalid JSON format\");\r\n    }\r\n  };\r\n\r\n  const uploadProps: UploadProps = {\r\n    accept: \".json\",\r\n    showUploadList: false,\r\n    customRequest: ({ file, onSuccess }) => {\r\n      setTimeout(() => {\r\n        onSuccess && onSuccess(\"ok\");\r\n      }, 0);\r\n    },\r\n    onChange: handleFileUpload,\r\n  };\r\n\r\n  const inputRef = useRef<InputRef>(null);\r\n\r\n  const items = [\r\n    {\r\n      key: \"url\",\r\n      label: (\r\n        <span className=\"flex items-center gap-2\">\r\n          <Globe className=\"w-4 h-4\" /> URL Import\r\n        </span>\r\n      ),\r\n      children: (\r\n        <div className=\"space-y-4\">\r\n          <Input\r\n            ref={inputRef}\r\n            placeholder=\"Enter gallery URL...\"\r\n            value={url}\r\n            onChange={(e) => setUrl(e.target.value)}\r\n          />\r\n          <div className=\"text-xs\">\r\n            Sample\r\n            <a\r\n              role=\"button\"\r\n              onClick={(e) => {\r\n                setUrl(\r\n                  \"https://raw.githubusercontent.com/victordibia/multiagent-systems-with-autogen/refs/heads/main/research/components/gallery/base.json\"\r\n                );\r\n                e.preventDefault();\r\n              }}\r\n              href=\"https://raw.githubusercontent.com/victordibia/multiagent-systems-with-autogen/refs/heads/main/research/components/gallery/base.json\"\r\n              target=\"_blank\"\r\n              rel=\"noreferrer\"\r\n              className=\"text-accent\"\r\n            >\r\n              {\" \"}\r\n              gallery.json{\" \"}\r\n            </a>\r\n          </div>\r\n          <Button\r\n            type=\"primary\"\r\n            onClick={handleUrlImport}\r\n            disabled={!url || isLoading}\r\n            block\r\n          >\r\n            Import from URL\r\n          </Button>\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      key: \"file\",\r\n      label: (\r\n        <span className=\"flex items-center gap-2\">\r\n          <UploadIcon className=\"w-4 h-4\" /> File Upload\r\n        </span>\r\n      ),\r\n      children: (\r\n        <div className=\"border-2 border-dashed rounded-lg p-8 text-center space-y-4\">\r\n          <Upload.Dragger {...uploadProps}>\r\n            <p className=\"ant-upload-drag-icon\">\r\n              <UploadIcon className=\"w-8 h-8 mx-auto text-secondary\" />\r\n            </p>\r\n            <p className=\"ant-upload-text\">\r\n              Click or drag JSON file to this area\r\n            </p>\r\n          </Upload.Dragger>\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      key: \"paste\",\r\n      label: (\r\n        <span className=\"flex items-center gap-2\">\r\n          <Code className=\"w-4 h-4\" /> Paste JSON\r\n        </span>\r\n      ),\r\n      children: (\r\n        <div className=\"space-y-4\">\r\n          <div className=\"h-64\">\r\n            <MonacoEditor\r\n              value={jsonContent}\r\n              onChange={setJsonContent}\r\n              editorRef={editorRef}\r\n              language=\"json\"\r\n              minimap={false}\r\n            />\r\n          </div>\r\n          <Button type=\"primary\" onClick={handlePasteImport} block>\r\n            Import JSON\r\n          </Button>\r\n        </div>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <Modal\r\n      title=\"Create New Gallery\"\r\n      open={open}\r\n      onCancel={onCancel}\r\n      footer={null}\r\n      width={800}\r\n    >\r\n      <div className=\"mt-4\">\r\n        <Tabs activeKey={activeTab} onChange={setActiveTab} items={items} />\r\n\r\n        {error && (\r\n          <Alert message={error} type=\"error\" showIcon className=\"mt-4\" />\r\n        )}\r\n      </div>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport default GalleryCreateModal;\r\n", "import React, { useCallback, useEffect, useState, useContext } from \"react\";\r\nimport { message, Modal } from \"antd\";\r\nimport { ChevronRight } from \"lucide-react\";\r\nimport { appContext } from \"../../../hooks/provider\";\r\nimport { galleryAPI } from \"./api\";\r\nimport { GallerySidebar } from \"./sidebar\";\r\nimport { GalleryDetail } from \"./detail\";\r\nimport { GalleryCreateModal } from \"./create-modal\";\r\nimport type { Gallery } from \"../../types/datamodel\";\r\n\r\nexport const GalleryManager: React.FC = () => {\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [galleries, setGalleries] = useState<Gallery[]>([]);\r\n  const [currentGallery, setCurrentGallery] = useState<Gallery | null>(null);\r\n  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);\r\n  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);\r\n  const [isSidebarOpen, setIsSidebarOpen] = useState(() => {\r\n    if (typeof window !== \"undefined\") {\r\n      const stored = localStorage.getItem(\"gallerySidebar\");\r\n      return stored !== null ? JSON.parse(stored) : true;\r\n    }\r\n    return true;\r\n  });\r\n\r\n  const { user } = useContext(appContext);\r\n  const [messageApi, contextHolder] = message.useMessage();\r\n\r\n  // Persist sidebar state\r\n  useEffect(() => {\r\n    if (typeof window !== \"undefined\") {\r\n      localStorage.setItem(\"gallerySidebar\", JSON.stringify(isSidebarOpen));\r\n    }\r\n  }, [isSidebarOpen]);\r\n\r\n  const fetchGalleries = useCallback(async () => {\r\n    if (!user?.id) return;\r\n\r\n    try {\r\n      setIsLoading(true);\r\n      const data = await galleryAPI.listGalleries(user.id);\r\n      setGalleries(data);\r\n      if (!currentGallery && data.length > 0) {\r\n        setCurrentGallery(data[0]);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching galleries:\", error);\r\n      messageApi.error(\"Failed to fetch galleries\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, [user?.id, currentGallery, messageApi]);\r\n\r\n  useEffect(() => {\r\n    fetchGalleries();\r\n  }, [fetchGalleries]);\r\n\r\n  // Handle URL params\r\n  useEffect(() => {\r\n    const params = new URLSearchParams(window.location.search);\r\n    const galleryId = params.get(\"galleryId\");\r\n\r\n    if (galleryId && !currentGallery) {\r\n      const numericId = parseInt(galleryId, 10);\r\n      if (!isNaN(numericId)) {\r\n        handleSelectGallery(numericId);\r\n      }\r\n    }\r\n  }, []);\r\n\r\n  // Update URL when gallery changes\r\n  useEffect(() => {\r\n    if (currentGallery?.id) {\r\n      window.history.pushState(\r\n        {},\r\n        \"\",\r\n        `?galleryId=${currentGallery.id.toString()}`\r\n      );\r\n    }\r\n  }, [currentGallery?.id]);\r\n\r\n  const handleSelectGallery = async (galleryId: number) => {\r\n    if (!user?.id) return;\r\n\r\n    if (hasUnsavedChanges) {\r\n      Modal.confirm({\r\n        title: \"Unsaved Changes\",\r\n        content: \"You have unsaved changes. Do you want to discard them?\",\r\n        okText: \"Discard\",\r\n        cancelText: \"Go Back\",\r\n        onOk: () => {\r\n          switchToGallery(galleryId);\r\n          setHasUnsavedChanges(false);\r\n        },\r\n      });\r\n    } else {\r\n      await switchToGallery(galleryId);\r\n    }\r\n  };\r\n\r\n  const switchToGallery = async (galleryId: number) => {\r\n    if (!user?.id) return;\r\n\r\n    setIsLoading(true);\r\n    try {\r\n      const data = await galleryAPI.getGallery(galleryId, user.id);\r\n      setCurrentGallery(data);\r\n    } catch (error) {\r\n      console.error(\"Error loading gallery:\", error);\r\n      messageApi.error(\"Failed to load gallery\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleCreateGallery = async (galleryData: Gallery) => {\r\n    if (!user?.id) return;\r\n\r\n    galleryData.user_id = user.id;\r\n    try {\r\n      const savedGallery = await galleryAPI.createGallery(galleryData, user.id);\r\n      setGalleries([savedGallery, ...galleries]);\r\n      setCurrentGallery(savedGallery);\r\n      setIsCreateModalOpen(false);\r\n      messageApi.success(\"Gallery created successfully\");\r\n    } catch (error) {\r\n      console.error(\"Error creating gallery:\", error);\r\n      messageApi.error(\"Failed to create gallery\");\r\n    }\r\n  };\r\n\r\n  const handleUpdateGallery = async (updates: Partial<Gallery>) => {\r\n    if (!user?.id || !currentGallery?.id) return;\r\n\r\n    try {\r\n      const sanitizedUpdates = {\r\n        ...updates,\r\n        created_at: undefined,\r\n        updated_at: undefined,\r\n      };\r\n      const updatedGallery = await galleryAPI.updateGallery(\r\n        currentGallery.id,\r\n        sanitizedUpdates,\r\n        user.id\r\n      );\r\n      setGalleries(\r\n        galleries.map((g) => (g.id === updatedGallery.id ? updatedGallery : g))\r\n      );\r\n      setCurrentGallery(updatedGallery);\r\n      setHasUnsavedChanges(false);\r\n      messageApi.success(\"Gallery updated successfully\");\r\n    } catch (error) {\r\n      console.error(\"Error updating gallery:\", error);\r\n      messageApi.error(\"Failed to update gallery\");\r\n    }\r\n  };\r\n\r\n  const handleDeleteGallery = async (galleryId: number) => {\r\n    if (!user?.id) return;\r\n\r\n    try {\r\n      await galleryAPI.deleteGallery(galleryId, user.id);\r\n      setGalleries(galleries.filter((g) => g.id !== galleryId));\r\n      if (currentGallery?.id === galleryId) {\r\n        setCurrentGallery(null);\r\n      }\r\n      messageApi.success(\"Gallery deleted successfully\");\r\n    } catch (error) {\r\n      console.error(\"Error deleting gallery:\", error);\r\n      messageApi.error(\"Failed to delete gallery\");\r\n    }\r\n  };\r\n\r\n  const handleSyncGallery = async (galleryId: number) => {\r\n    if (!user?.id) return;\r\n\r\n    try {\r\n      setIsLoading(true);\r\n      const gallery = galleries.find((g) => g.id === galleryId);\r\n      if (!gallery?.config.url) return;\r\n\r\n      const remoteGallery = await galleryAPI.syncGallery(gallery.config.url);\r\n      await handleUpdateGallery({\r\n        ...remoteGallery,\r\n        id: galleryId,\r\n        config: {\r\n          ...remoteGallery.config,\r\n          metadata: {\r\n            ...remoteGallery.config.metadata,\r\n            lastSynced: new Date().toISOString(),\r\n          },\r\n        },\r\n      });\r\n\r\n      messageApi.success(\"Gallery synced successfully\");\r\n    } catch (error) {\r\n      console.error(\"Error syncing gallery:\", error);\r\n      messageApi.error(\"Failed to sync gallery\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  if (!user?.id) {\r\n    return (\r\n      <div className=\"flex items-center justify-center h-[calc(100vh-120px)] text-secondary\">\r\n        Please log in to view galleries\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"relative flex h-full w-full\">\r\n      {contextHolder}\r\n\r\n      {/* Create Modal */}\r\n      <GalleryCreateModal\r\n        open={isCreateModalOpen}\r\n        onCancel={() => setIsCreateModalOpen(false)}\r\n        onCreateGallery={handleCreateGallery}\r\n      />\r\n\r\n      {/* Sidebar */}\r\n      <div\r\n        className={`absolute left-0 top-0 h-full transition-all duration-200 ease-in-out ${\r\n          isSidebarOpen ? \"w-64\" : \"w-12\"\r\n        }`}\r\n      >\r\n        <GallerySidebar\r\n          isOpen={isSidebarOpen}\r\n          galleries={galleries}\r\n          currentGallery={currentGallery}\r\n          onToggle={() => setIsSidebarOpen(!isSidebarOpen)}\r\n          onSelectGallery={(gallery) => handleSelectGallery(gallery.id!)}\r\n          onCreateGallery={() => setIsCreateModalOpen(true)}\r\n          onDeleteGallery={handleDeleteGallery}\r\n          onSyncGallery={handleSyncGallery}\r\n          isLoading={isLoading}\r\n        />\r\n      </div>\r\n\r\n      {/* Main Content */}\r\n      <div\r\n        className={`flex-1 transition-all -mr-6 duration-200 ${\r\n          isSidebarOpen ? \"ml-64\" : \"ml-12\"\r\n        }`}\r\n      >\r\n        <div className=\"p-4 pt-2\">\r\n          {/* Breadcrumb */}\r\n          <div className=\"flex items-center gap-2 mb-4 text-sm\">\r\n            <span className=\"text-primary font-medium\">Galleries</span>\r\n            {currentGallery && (\r\n              <>\r\n                <ChevronRight className=\"w-4 h-4 text-secondary\" />\r\n                <span className=\"text-secondary\">\r\n                  {currentGallery.config.name}\r\n                </span>\r\n              </>\r\n            )}\r\n          </div>\r\n\r\n          {/* Content Area */}\r\n          {isLoading && !currentGallery ? (\r\n            <div className=\"flex items-center justify-center h-[calc(100vh-120px)] text-secondary\">\r\n              Loading galleries...\r\n            </div>\r\n          ) : currentGallery ? (\r\n            <GalleryDetail\r\n              gallery={currentGallery}\r\n              onSave={handleUpdateGallery}\r\n              onDirtyStateChange={setHasUnsavedChanges}\r\n            />\r\n          ) : (\r\n            <div className=\"flex items-center justify-center h-[calc(100vh-120px)] text-secondary\">\r\n              Select a gallery from the sidebar or create a new one\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default GalleryManager;\r\n", "import * as React from \"react\";\r\nimport Layout from \"../components/layout\";\r\nimport { graphql } from \"gatsby\";\r\nimport GalleryManager from \"../components/views/gallery/manager\";\r\n\r\n// markup\r\nconst GalleryPage = ({ data }: any) => {\r\n  return (\r\n    <Layout meta={data.site.siteMetadata} title=\"画廊\" link={\"/gallery\"}>\r\n      <main style={{ height: \"100%\" }} className=\" h-full \">\r\n        <GalleryManager />\r\n      </main>\r\n    </Layout>\r\n  );\r\n};\r\n\r\nexport const query = graphql`\r\n  query HomePageQuery {\r\n    site {\r\n      siteMetadata {\r\n        description\r\n        title\r\n      }\r\n    }\r\n  }\r\n`;\r\n\r\nexport default GalleryPage;\r\n"], "names": ["Globe", "cx", "cy", "r", "key", "d", "RefreshCw", "GallerySidebar", "_ref", "isOpen", "galleries", "currentGallery", "onToggle", "onSelectGallery", "onCreateGallery", "onDeleteGallery", "onSyncGallery", "isLoading", "React", "className", "length", "<PERSON><PERSON><PERSON>", "title", "onClick", "PanelLeftClose", "strokeWidth", "<PERSON><PERSON>", "type", "icon", "Plus", "Info", "map", "gallery", "id", "config", "components", "name", "url", "size", "e", "stopPropagation", "danger", "disabled", "Trash2", "metadata", "version", "Package", "Object", "values", "reduce", "sum", "arr", "updated_at", "getRelativeTimeString", "PanelLeftOpen", "Trash", "createLucideIcon", "Briefcase", "width", "height", "x", "y", "rx", "TEAM_TEMPLATES", "label", "description", "provider", "PROVIDERS", "ROUND_ROBIN_TEAM", "component_type", "component_version", "participants", "termination_condition", "TEXT_MENTION", "text", "max_turns", "SELECTOR_TEAM", "model_client", "OPENAI", "model", "selector_prompt", "allow_repeated_speaker", "AGENT_TEMPLATES", "ASSISTANT_AGENT", "system_message", "workbench", "reflect_on_tool_use", "tool_call_summary_format", "model_client_stream", "USER_PROXY", "WEB_SURFER", "headless", "start_page", "animate_actions", "to_save_screenshots", "use_ocr", "to_resize_viewport", "MODEL_TEMPLATES", "temperature", "max_tokens", "AZURE_OPENAI", "azure_endpoint", "azure_deployment", "api_version", "ANTHROPIC", "TOOL_TEMPLATES", "FUNCTION_TOOL", "source_code", "global_imports", "has_cancellation_support", "PYTHON_CODE_EXECUTION_TOOL", "executor", "timeout", "work_dir", "functions_module", "cleanup_temp_files", "WORKBENCH_TEMPLATES", "STATIC_WORKBENCH", "tools", "MCP_WORKBENCH", "server_params", "command", "args", "env", "read_timeout_seconds", "headers", "Authorization", "sse_read_timeout", "terminate_on_close", "TERMINATION_TEMPLATES", "MAX_MESSAGE", "max_messages", "include_agent_event", "STOP_MESSAGE", "TOKEN_USAGE", "max_total_token", "TIMEOUT", "timeout_seconds", "HANDOFF", "target", "SOURCE_MATCH", "sources", "TEXT_MESSAGE", "source", "undefined", "EXTERNAL", "OR_TERMINATION", "conditions", "AND_TERMINATION", "COMPONENT_TEMPLATES", "team", "agent", "tool", "termination", "getTemplateById", "componentType", "templateId", "find", "template", "createComponentFromTemplate", "overrides", "Error", "createWorkbenchFromTemplate", "customLabel", "getTemplatesForDropdown", "templates", "getTemplatesForType", "createComponentFromTemplateById", "AddComponentDropdown", "onComponentAdded", "showIcon", "showChevron", "buttonText", "templateFilter", "messageApi", "contextHolder", "message", "useMessage", "handleAddComponentFromTemplate", "category", "getCategoryKey", "newComponent", "createTeamFromTemplate", "createAgentFromTemplate", "createModelFromTemplate", "createToolFromTemplate", "createTerminationFromTemplate", "error", "console", "filter", "getDropdownTemplatesForType", "displayButtonText", "char<PERSON>t", "toUpperCase", "slice", "Dropdown", "menu", "items", "trigger", "ChevronDown", "ComponentCard", "item", "onEdit", "onDuplicate", "onDelete", "index", "allowDelete", "Copy", "Edit", "isMcpWorkbench", "Icon", "TruncatableText", "content", "showFullscreen", "textT<PERSON><PERSON>old", "ComponentGrid", "_ref2", "actions", "idx", "assign", "iconMap", "Users", "Bot", "<PERSON><PERSON>", "Brain", "Timer", "GalleryDetail", "_ref3", "onSave", "onDirtyStateChange", "editingComponent", "setEditingComponent", "useState", "activeTab", "setActiveTab", "isEditingDetails", "setIsEditingDetails", "isJsonEditing", "setIsJsonEditing", "workingCopy", "setWorkingCopy", "jsonValue", "setJsonValue", "hasUnsavedChanges", "setHasUnsavedChanges", "isDirty", "setIsDirty", "tempName", "setTempName", "tempDescription", "setTempDescription", "editor<PERSON><PERSON>", "useRef", "useEffect", "JSON", "stringify", "updateGallery", "updater", "updatedGallery", "handlers", "component", "_component$label", "baseLabel", "replace", "nextNumber", "Math", "max", "apply", "_toConsumableArray", "c", "_c$label", "match", "RegExp", "parseInt", "n", "isNaN", "concat", "_", "i", "handleComponentAdded", "newComponents", "tabItems", "entries", "_ref4", "_currentGallery$confi", "_currentGallery$confi2", "_currentGallery$confi3", "children", "src", "alt", "Input", "value", "onChange", "TextArea", "rows", "Download", "handleDownload", "dataStr", "dataBlob", "Blob", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "toLowerCase", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "FormInput", "Code", "newJsonMode", "handleDetailsSave", "handleJsonSave", "parse", "success", "MonacoEditor", "language", "minimap", "Tabs", "Drawer", "placement", "onClose", "open", "ComponentEditor", "updatedComponent", "navigationDepth", "defaultGallery", "loadGalleryFromJson", "require", "GalleryCreateModal", "onCancel", "setUrl", "json<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setError", "setIsLoading", "uploadProps", "accept", "showUploadList", "customRequest", "file", "onSuccess", "setTimeout", "info", "status", "originFileObj", "File", "reader", "FileReader", "onload", "_e$target", "result", "err", "readAsText", "inputRef", "ref", "placeholder", "role", "preventDefault", "rel", "async", "response", "fetch", "data", "json", "block", "UploadIcon", "Upload", "<PERSON><PERSON>", "handlePasteImport", "Modal", "footer", "active<PERSON><PERSON>", "<PERSON><PERSON>", "GalleryManager", "setGalleries", "setCurrentGallery", "isCreateModalOpen", "setIsCreateModalOpen", "isSidebarOpen", "setIsSidebarOpen", "window", "stored", "localStorage", "getItem", "user", "useContext", "appContext", "setItem", "fetchGalleries", "useCallback", "galleryAPI", "listGalleries", "galleryId", "URLSearchParams", "location", "search", "get", "numericId", "handleSelectGallery", "history", "pushState", "toString", "confirm", "okText", "cancelText", "onOk", "switchToGallery", "getGallery", "handleUpdateGallery", "sanitizedUpdates", "updates", "created_at", "g", "galleryData", "user_id", "savedGallery", "createGallery", "deleteGallery", "remoteGallery", "syncGallery", "lastSynced", "Date", "toISOString", "ChevronRight", "Layout", "meta", "site", "siteMetadata", "style"], "sourceRoot": ""}