{"version": 3, "file": "component---src-pages-labs-tsx-5cfcfda7c438b5493b69.js", "mappings": ";qJASA,MAAMA,GAAa,E,QAAA,GAAiB,aAAc,CAChD,CAAC,OAAQ,CAAEC,EAAG,qDAAsDC,IAAK,WACzE,CAAC,OAAQ,CAAED,EAAG,WAAYC,IAAK,WAC/B,CAAC,OAAQ,CAAED,EAAG,sDAAuDC,IAAK,WAC1E,CAAC,OAAQ,CAAED,EAAG,aAAcC,IAAK,Y,gKCO5B,MAAMC,EAA0CC,IAOhD,IAPiD,OACtDC,EAAM,KACNC,EAAI,WACJC,EAAU,SACVC,EAAQ,YACRC,EAAW,UACXC,GAAY,GACbN,EAEC,OAAKC,EAkBHM,EAAAA,cAAA,OAAKC,UAAU,oCAEbD,EAAAA,cAAA,OAAKC,UAAU,kFACbD,EAAAA,cAAA,OAAKC,UAAU,2BAEbD,EAAAA,cAAA,QAAMC,UAAU,4BAA2B,SAK7CD,EAAAA,cAACE,EAAAA,EAAO,CAACC,MAAM,iBACbH,EAAAA,cAAA,UACEI,QAASP,EACTI,UAAU,gKAEVD,EAAAA,cAACK,EAAAA,EAAc,CAACC,YAAa,IAAKL,UAAU,eAMjDF,GACCC,EAAAA,cAAA,OAAKC,UAAU,OACbD,EAAAA,cAACX,EAAAA,EAAU,CAACY,UAAU,wCAKxBF,GAA6B,IAAhBJ,EAAKY,QAClBP,EAAAA,cAAA,OAAKC,UAAU,iFACbD,EAAAA,cAACQ,EAAAA,EAAQ,CAACP,UAAU,wCAAwC,+CAMhED,EAAAA,cAAA,OAAKC,UAAU,4CACZN,EAAKc,IAAKC,GACTV,EAAAA,cAAA,OAAKT,IAAKmB,EAAIC,GAAIV,UAAU,YAC1BD,EAAAA,cAAA,OACEC,UAAW,+FAERL,aAAU,EAAVA,EAAYe,MAAOD,EAAIC,GAAK,YAAc,iBAG/CX,EAAAA,cAAA,OACEC,UAAW,8EACTL,aAAU,EAAVA,EAAYe,MAAOD,EAAIC,GACnB,6BACA,sBAENP,QAASA,IAAMN,EAAYY,IAG3BV,EAAAA,cAAA,OAAKC,UAAU,qCACbD,EAAAA,cAAA,QAAMC,UAAU,oBAAoBS,EAAIP,aAvElDH,EAAAA,cAAA,OAAKC,UAAU,oCACbD,EAAAA,cAAA,OAAKC,UAAU,aACbD,EAAAA,cAACE,EAAAA,EAAO,CAACC,MAAM,iBACbH,EAAAA,cAAA,UACEI,QAASP,EACTI,UAAU,gKAEVD,EAAAA,cAACY,EAAAA,EAAa,CAACN,YAAa,IAAKL,UAAU,iBChC5CY,EAAqB,CAMhC,CACEF,GAAI,aACJR,MAAO,aACPW,KAAM,W,cCWV,MArB+BC,IAE3Bf,EAAAA,cAAA,OAAKC,UAAU,IACbD,EAAAA,cAAA,MAAIC,UAAU,4BAA2B,mDAIzCD,EAAAA,cAACgB,EAAAA,EAAK,CACJf,UAAU,OACVgB,QAAQ,gBACRC,YACElB,EAAAA,cAAA,MAAIC,UAAU,iCACZD,EAAAA,cAAA,UAAI,sBAGRc,KAAK,U,2DClBb,EADmB,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,SAAW,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAC,EAAG,SAAY,CAAC,CAAE,IAAO,QAAS,MAAS,CAAC,KAAQ,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,mZAAuZ,KAAQ,OAAQ,MAAS,Y,UCMrpB,EAAe,SAAsBK,EAAOC,GAC9C,OAAoB,gBAAoBC,EAAAC,GAAU,OAAS,CAAC,EAAGH,EAAO,CACpEC,IAAKA,EACLG,KAAM,IAEV,EAOA,MAJ2B,aAAiB,G,UCQrC,IAAMC,EAAY,SAAAC,GAAA,SAAAD,IAAA,QAAAE,EAAAC,EAAAC,UAAArB,OAAAsB,EAAA,IAAAC,MAAAH,GAAAI,EAAA,EAAAA,EAAAJ,EAAAI,IAAAF,EAAAE,GAAAH,UAAAG,GACK,OADLL,EAAAD,EAAAO,KAAAC,MAAAR,EAAA,OAAAS,OAAAL,KAAA,MACvBM,GAAuB,KAAIT,CAAC,EAADU,EAAAA,EAAAA,GAAAZ,EAAAC,GAAA,IAAAY,EAAAb,EAAAc,UAwD1B,OAtDDD,EACQE,oBAAR,SAA4BC,GAC1B,IACE,IAAIC,EAAUD,EAAIE,QAAQ,gBAAiB,IAQ3C,OANED,EADEA,EAAQE,WAAW,aACXF,EAAQC,QAAQ,OAAQ,IACb,SAAZD,EACCG,OAAOC,SAASC,KAEhBL,EAAQC,QAAQ,OAAQ,IAAIA,QAAQ,MAAO,IAEhDD,CACT,CAAE,MAAOM,GACP,MAAM,IAAIC,MAAM,mCAClB,CACF,EAACX,EAEDY,QAAA,SACEC,EACAC,EACAC,GAGA,MAAMC,EAAYC,KAAKC,aACjBd,EAAUa,KAAKf,oBAAoBc,GAEnCG,EAAQ,GADgC,WAA7BZ,OAAOC,SAASY,SAAwB,OAAS,UACpChB,mBAC9Ba,KAAKnB,GAAK,IAAIS,OAAOc,UAAUF,GAC/BF,KAAKnB,GAAGwB,UAAaC,IACnB,IACE,MAAMC,EAAOC,KAAKC,MAAMH,EAAMC,MAC9BX,EAAUW,EACZ,CAAE,MAAOG,GACHb,GAASA,EAAQa,EACvB,GAEFV,KAAKnB,GAAG8B,QAAWL,IACbT,GAASA,EAAQS,IAEvBN,KAAKnB,GAAG+B,QAAU,KACZd,GAASA,IAEjB,EAACf,EAED8B,gBAAA,SAAgBjD,GACVoC,KAAKnB,IAA6B,IAAvBmB,KAAKnB,GAAGiC,YACrBd,KAAKnB,GAAGkC,KAAKP,KAAKQ,UAAU,CAAEpD,gBAElC,EAACmB,EAEDkC,MAAA,WACMjB,KAAKnB,IACPmB,KAAKnB,GAAGoC,OAEZ,EAAC/C,CAAA,CAzDsB,C,QAASgD,GA4D3B,MAAMC,EAAe,IAAIjD,ECkDhC,MA3H+BkD,KAC7B,MAAM,EAACxD,EAAY,EAACyD,IAAkBC,EAAAA,EAAAA,UAAS,KACzC,EAACC,EAAO,EAACC,IAAaF,EAAAA,EAAAA,UAA2B,KACjD,EAACG,EAAU,EAACC,IAAgBJ,EAAAA,EAAAA,UAAoC,OAChE,EAACK,EAAQ,EAACC,IAAcN,EAAAA,EAAAA,WAAS,IACjC,EAAC7B,EAAM,EAACoC,IAAYP,EAAAA,EAAAA,UAAwB,MAC5CQ,GAAQC,EAAAA,EAAAA,QAAmC,MAiB3CC,EAAcA,KAClBR,EAAU,IACVE,EAAa,MACbG,EAAS,MACTD,GAAW,GACXE,EAAMG,QAAUd,EAChBW,EAAMG,QAAQtC,QACXuC,IACC,GAAI,UAAWA,EACbV,EAAWW,GAAI,GAAAvD,QAAAwD,EAAAA,EAAAA,GAASD,GAAI,CAAED,EAAI5B,cAC7B,GAAI,cAAe4B,EAAK,CAAC,IAADG,EAC7BX,EAAaQ,EAAIT,WACjBG,GAAW,GACE,QAAbS,EAAAP,EAAMG,eAAO,IAAAI,GAAbA,EAAepB,OACjB,MAAO,GAAI,UAAWiB,EAAK,CAAC,IAADI,EACzBT,EAASK,EAAIzC,OACbmC,GAAW,GACE,QAAbU,EAAAR,EAAMG,eAAO,IAAAK,GAAbA,EAAerB,OACjB,GAEDsB,IACCV,EAAS,oBAAsBU,GAC/BX,GAAW,IAEb,KACEA,GAAW,KAGfY,WAAW,KAAO,IAADC,EACF,QAAbA,EAAAX,EAAMG,eAAO,IAAAQ,GAAbA,EAAe5B,gBAAgBjD,IAC9B,MAGL,OACElB,EAAAA,cAAA,OAAKC,UAAU,IACbD,EAAAA,cAAA,MAAIC,UAAU,2BAA0B,6BACxCD,EAAAA,cAAA,KAAGC,UAAU,uBAAsB,yFAKnCD,EAAAA,cAAA,OAAKC,UAAU,QACbD,EAAAA,cAAA,KAAGC,UAAU,2CAA0C,uBAGvDD,EAAAA,cAAA,OAAKC,UAAU,wBA5DA,CACnB,gDACA,0DACA,2CACA,sCACA,8CACA,wCACA,gDACA,6DAqDoBQ,IAAI,CAACuF,EAASC,IAC1BjG,EAAAA,cAACkG,EAAAA,EAAG,CACF3G,IAAK0G,EACLhG,UAAU,uDACVG,QAASA,IAtDO4F,KAC1BrB,EAAeqB,IAqDUG,CAAmBH,IAEjCA,MAKThG,EAAAA,cAAA,OAAKC,UAAU,mBACbD,EAAAA,cAACoG,EAAAA,EAAK,CACJC,MAAOnF,EACPoF,SAAWtC,GAAMW,EAAeX,EAAEuC,OAAOF,OACzCG,YAAY,4EACZC,aAAcnB,EACdoB,SAAUzB,IAEZjF,EAAAA,cAAC2G,EAAAA,GAAM,CACL7F,KAAK,UACLS,KAAMvB,EAAAA,cAAC4G,EAAY,MACnBxG,QAASkF,EACTL,QAASA,EACTyB,UAAWxF,EAAY2F,QAAU5B,GAClC,WAIFlC,GAAS/C,EAAAA,cAACgB,EAAAA,EAAK,CAACF,KAAK,QAAQG,QAAS8B,EAAO9C,UAAU,SACvDgF,GAAWjF,EAAAA,cAAC8G,EAAAA,EAAI,CAAC7G,UAAU,SAC5BD,EAAAA,cAAA,OAAKC,UAAU,QACZ4E,EAAOpE,IAAI,CAACmD,EAAOqC,IAClBjG,EAAAA,cAACgB,EAAAA,EAAK,CACJzB,IAAK0G,EACLnF,KAAK,OACLG,QAAS2C,EAAMmD,OACf7F,YAAa0C,EAAMoD,QACnB/G,UAAU,WAIf8E,GACC/E,EAAAA,cAAA,OAAKC,UAAU,sCACbD,EAAAA,cAAA,MAAIC,UAAU,sBAAqB,kBACnCD,EAAAA,cAAA,OAAKC,UAAU,4DACZ6D,KAAKQ,UAAUS,EAAW,KAAM,OCpHtC,MAGMkC,EAAwCxH,IAAc,IAAb,IAAEiB,GAAKjB,EAE3D,OAAQiB,EAAIC,IACV,IAAK,eACH,OAAOX,EAAAA,cAACe,EAAY,MACtB,IAAK,aACH,OAAOf,EAAAA,cAAC0E,EAAY,MACtB,QACE,OACE1E,EAAAA,cAAA,OAAKC,UAAU,kBAAiB,wBACTD,EAAAA,cAAA,cAASU,EAAIP,OAAe,2BCoE3D,MApFqC+G,KACnC,MAAM,EAACnH,EAAU,EAACoH,IAAgBvC,EAAAA,EAAAA,WAAS,IACrC,EAACjF,EAAK,EAACyH,IAAWxC,EAAAA,EAAAA,UAAgB,KAClC,EAAChF,EAAW,EAACyH,IAAiBzC,EAAAA,EAAAA,UAAqB,OACnD,EAAC0C,EAAc,EAACC,IAAoB3C,EAAAA,EAAAA,UAAS,KACjD,GAAsB,oBAAXhC,OAAwB,CACjC,MAAM4E,EAASC,aAAaC,QAAQ,eACpC,OAAkB,OAAXF,GAAkB1D,KAAKC,MAAMyD,EACtC,CACA,OAAO,IAqBT,OAjBAG,EAAAA,EAAAA,WAAU,KACc,oBAAX/E,QACT6E,aAAaG,QAAQ,cAAe9D,KAAKQ,UAAUgD,KAEpD,CAACA,KAGJK,EAAAA,EAAAA,WAAU,MACH/H,GAAcD,EAAKY,OAAS,GAC/B8G,EAAc1H,EAAK,KAEpB,CAACA,EAAMC,KAEV+H,EAAAA,EAAAA,WAAU,KACRP,EAAQvG,IACP,IAGDb,EAAAA,cAAA,OAAKC,UAAU,kCAEbD,EAAAA,cAAA,OACEC,UAAW,0EACTqH,EAAgB,OAAS,SAG3BtH,EAAAA,cAACR,EAAW,CACVE,OAAQ4H,EACR3H,KAAMA,EACNC,WAAYA,EACZC,SAAUA,IAAM0H,GAAkBD,GAClCxH,YAAauH,EACbtH,UAAWA,KAKfC,EAAAA,cAAA,OACEC,UAAW,wDACTqH,EAAgB,QAAU,UAG5BtH,EAAAA,cAAA,OAAKC,UAAU,YAEbD,EAAAA,cAAA,OAAKC,UAAU,wCACbD,EAAAA,cAAA,QAAMC,UAAU,4BAA2B,QAC1CL,GACCI,EAAAA,cAAAA,EAAAA,SAAA,KACEA,EAAAA,cAAC6H,EAAAA,EAAY,CAAC5H,UAAU,2BACxBD,EAAAA,cAAA,QAAMC,UAAU,kBAAkBL,EAAWO,SAInDH,EAAAA,cAAA,OAAKC,UAAU,kEACbD,EAAAA,cAAC8H,EAAAA,EAAa,CAAC7H,UAAU,oDAAqD,IAAI,sGAKnFL,EACCI,EAAAA,cAACiH,EAAU,CAACvG,IAAKd,IAEjBI,EAAAA,cAAA,OAAKC,UAAU,yEAAwE,oDCpDnG,MArBiBR,IAAmB,IAAlB,KAAEoE,GAAWpE,EAC7B,OACEO,EAAAA,cAAC+H,EAAAA,EAAM,CAACC,KAAMnE,EAAKoE,KAAKC,aAAc/H,MAAM,MAAMgI,KAAM,SACtDnI,EAAAA,cAAA,QAAMoI,MAAO,CAAEC,OAAQ,QAAUpI,UAAU,YACzCD,EAAAA,cAACkH,EAAW,Q,mMCNpB,MAuFaoB,EAAeC,IAC1B,MAAM,UACJC,EAAS,aACTC,EAAY,KACZC,GACEH,EACEI,EAAcJ,EAAMK,WAU1B,OATiB,QAAWL,EAAO,CACjCI,cACAE,eAAe,QAAKH,EAAKH,EAAMO,cAAcC,IAAIJ,GAAaK,SAC9DC,YAAaP,EAAKD,GAAcS,IAAIR,EAAKF,GAAWO,IAAI,IAAIC,QAE5DG,qBAAsB,EAEtBC,gBAAiBb,EAAMc,aAIdC,EAAwBf,IAAS,CAC5Cc,UAAW,IAAI,IAAUd,EAAMgB,qBAAqBC,aAAajB,EAAMkB,kBAAkBC,cACzFC,aAAcpB,EAAMqB,YAEtB,OAAe,QAAc,MAAOrB,GA7GfA,KACnB,MAAM,WACJsB,EAAU,UACVrB,EAAS,qBACTW,EAAoB,aACpBW,EAAY,KACZpB,GACEH,EACEwB,EAAgBrB,EAAKS,GAAsBD,IAAIV,GAAWQ,QAC1DgB,EAAmBtB,EAAKmB,GAAYX,IAAIV,GAAWQ,QACzD,MAAO,CAEL,CAACc,GAAeG,OAAOC,OAAOD,OAAOC,OAAO,CAAC,GAAG,QAAe3B,IAAS,CACtE4B,QAAS,eACT9B,OAAQ,OAER+B,gBAAiB7B,EAAM8B,SACvBN,gBACAO,SAAU/B,EAAMI,YAChB4B,WAAYhC,EAAMM,cAClB2B,WAAY,SACZC,WAAYlC,EAAMc,UAClBqB,OAAQ,IAAG,QAAKnC,EAAMC,cAAcD,EAAMoC,YAAYpC,EAAMqC,cAC5DC,aAActC,EAAMuC,eACpBC,QAAS,EACTC,WAAY,OAAOzC,EAAM0C,oBACzBC,UAAW,QACXC,SAAU,WAEV,CAAC,IAAIrB,SAAqB,CACxBsB,UAAW,OAEb,gBAAiB,CACfC,MAAO9C,EAAMoB,cAEf,CAAC,GAAGG,gBAA4B,CAC9BwB,kBAAmBtB,EACnBM,SAAU/B,EAAMU,YAChBoC,MAAO9C,EAAMgD,UACbC,OAAQ,UACRR,WAAY,OAAOzC,EAAM0C,oBACzB,UAAW,CACTI,MAAO9C,EAAMkD,mBAGjB,CAAC,IAAI3B,eAA2B,CAC9B4B,YAAa,cACb,CAAC,kBAAkBnD,EAAMoD,kBAAkBpD,EAAMoD,uBAAwB,CACvEN,MAAO9C,EAAMqD,sBAGjB,cAAe,CACbC,gBAAiB,cACjBH,YAAa,cACbF,OAAQ,UACR,CAAC,SAAS1B,8BAA0C,CAClDuB,MAAO9C,EAAMuD,aACbD,gBAAiBtD,EAAMwD,oBAEzB,sBAAuB,CACrBV,MAAO9C,EAAMqD,qBAEf,YAAa,CACXC,gBAAiBtD,EAAMuD,aACvB,UAAW,CACTD,gBAAiBtD,EAAMyD,oBAG3B,WAAY,CACVH,gBAAiBtD,EAAM0D,qBAG3B,WAAY,CACV9B,QAAS,QAGX,CAAC,KAAK5B,EAAMoD,4BAA4BpD,EAAMoD,WAAY,CACxDL,kBAAmBvB,KAGvB,CAAC,GAAGD,gBAA4B,CAC9B4B,YAAa,cACbjB,WAAYlC,EAAMa,mBA6Bf8C,CADU5D,EAAaC,IAE7Be,GCnHC6C,EAAgC,SAAUC,EAAGpI,GAC/C,IAAIqI,EAAI,CAAC,EACT,IAAK,IAAIC,KAAKF,EAAOnC,OAAO3H,UAAUiK,eAAevK,KAAKoK,EAAGE,IAAMtI,EAAEwI,QAAQF,GAAK,IAAGD,EAAEC,GAAKF,EAAEE,IAC9F,GAAS,MAALF,GAAqD,mBAAjCnC,OAAOwC,sBAA2C,KAAIC,EAAI,EAAb,IAAgBJ,EAAIrC,OAAOwC,sBAAsBL,GAAIM,EAAIJ,EAAE/L,OAAQmM,IAClI1I,EAAEwI,QAAQF,EAAEI,IAAM,GAAKzC,OAAO3H,UAAUqK,qBAAqB3K,KAAKoK,EAAGE,EAAEI,MAAKL,EAAEC,EAAEI,IAAMN,EAAEE,EAAEI,IADuB,CAGvH,OAAOL,CACT,EAKA,MAAMO,EAA4B,aAAiB,CAACzL,EAAOC,KACzD,MACIyL,UAAWC,EAAkB,MAC7B1E,EAAK,UACLnI,EAAS,QACT8M,EAAO,SACPzG,EAAQ,QACRlG,GACEe,EACJ6L,EAAYb,EAAOhL,EAAO,CAAC,YAAa,QAAS,YAAa,UAAW,WAAY,aACjF,aACJ8L,EAAY,IACZC,GACE,aAAiB,MAKfL,EAAYI,EAAa,MAAOH,IAE/BK,EAAYC,EAAQC,GAAa,EAASR,GAC3CS,EAAM,IAAWT,EAAW,GAAGA,cAAuB,CAC1D,CAAC,GAAGA,uBAAgCE,GACnCG,aAAiC,EAASA,EAAIjN,UAAWA,EAAWmN,EAAQC,GAC/E,OAAOF,EAAwB,gBAAoB,OAAQlD,OAAOC,OAAO,CAAC,EAAG8C,EAAW,CACtF5L,IAAKA,EACLgH,MAAO6B,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAG9B,GAAQ8E,aAAiC,EAASA,EAAI9E,OAC7FnI,UAAWqN,EACXlN,QAdkB4D,IAClBsC,SAAoDA,GAAUyG,GAC9D3M,SAAkDA,EAAQ4D,UAe9D,Q,UCnBA,OAAe,QAAqB,CAAC,MAAO,UAAWuE,GAtBhCA,KAAS,EAAAgF,EAAA,GAAehF,EAAO,CAACiF,GACrDC,YACAC,mBACAC,aACAC,gBACI,CACJ,CAAC,GAAGrF,EAAMuB,eAAevB,EAAMuB,gBAAgB0D,KAAa,CAC1DnC,MAAOoC,EACPhD,WAAYkD,EACZjC,YAAagC,EAEb,YAAa,CACXrC,MAAO9C,EAAMqD,oBACbnB,WAAYmD,EACZlC,YAAakC,GAEf,CAAC,IAAIrF,EAAMuB,2BAA4B,CACrC4B,YAAa,mBAOVmC,CADUvF,EAAaC,IAE7Be,GC1BH,MAAMwE,EAAoB,CAACvF,EAAOxB,EAAQgH,KACxC,MAAMC,ECHa,iBADcC,EDIaF,GCFrCE,EAEGA,EAAIC,OAAO,GAAGC,cAAgBF,EAAIG,MAAM,GAJvC,IAAoBH,EDKjC,MAAO,CACL,CAAC,GAAG1F,EAAMuB,eAAevB,EAAMuB,gBAAgB/C,KAAW,CACxDsE,MAAO9C,EAAM,QAAQwF,KACrBtD,WAAYlC,EAAM,QAAQyF,OAC1BtC,YAAanD,EAAM,QAAQyF,WAC3B,CAAC,IAAIzF,EAAMuB,2BAA4B,CACrC4B,YAAa,kBAMrB,OAAe,QAAqB,CAAC,MAAO,UAAWnD,IACrD,MAAM8F,EAAW/F,EAAaC,GAC9B,MAAO,CAACuF,EAAkBO,EAAU,UAAW,WAAYP,EAAkBO,EAAU,aAAc,QAASP,EAAkBO,EAAU,QAAS,SAAUP,EAAkBO,EAAU,UAAW,aACnM/E,GElBC,EAAgC,SAAU8C,EAAGpI,GAC/C,IAAIqI,EAAI,CAAC,EACT,IAAK,IAAIC,KAAKF,EAAOnC,OAAO3H,UAAUiK,eAAevK,KAAKoK,EAAGE,IAAMtI,EAAEwI,QAAQF,GAAK,IAAGD,EAAEC,GAAKF,EAAEE,IAC9F,GAAS,MAALF,GAAqD,mBAAjCnC,OAAOwC,sBAA2C,KAAIC,EAAI,EAAb,IAAgBJ,EAAIrC,OAAOwC,sBAAsBL,GAAIM,EAAIJ,EAAE/L,OAAQmM,IAClI1I,EAAEwI,QAAQF,EAAEI,IAAM,GAAKzC,OAAO3H,UAAUqK,qBAAqB3K,KAAKoK,EAAGE,EAAEI,MAAKL,EAAEC,EAAEI,IAAMN,EAAEE,EAAEI,IADuB,CAGvH,OAAOL,CACT,EAcA,MAAMiC,EAA2B,aAAiB,CAACC,EAAUnN,KAC3D,MACIyL,UAAWC,EAAkB,UAC7B7M,EAAS,cACTuO,EAAa,MACbpG,EAAK,SACLqG,EAAQ,KACRlN,EAAI,MACJ8J,EAAK,QACLjI,EAAO,SACPsL,GAAW,EACXC,QAASC,GACPL,EACJpN,EAAQ,EAAOoN,EAAU,CAAC,YAAa,YAAa,gBAAiB,QAAS,WAAY,OAAQ,QAAS,UAAW,WAAY,aAC9H,aACJtB,EAAY,UACZ7B,EACA8B,IAAK2B,GACH,aAAiB,OACdF,EAASG,GAAc,YAAe,GACvCC,GAAW,EAAAC,EAAA,GAAK7N,EAAO,CAAC,YAAa,aAM3C,YAAgB,UACY8N,IAAtBL,GACFE,EAAWF,IAEZ,CAACA,IACJ,MAAMM,GAAW,QAAc7D,GACzB8D,GAAW,QAAoB9D,GAC/B+D,EAAkBF,GAAYC,EAC9BE,EAAWpF,OAAOC,OAAOD,OAAOC,OAAO,CAC3C2B,gBAAiBR,IAAU+D,EAAkB/D,OAAQ4D,GACpDJ,aAA+C,EAASA,EAAWzG,OAAQA,GACxEyE,EAAYI,EAAa,MAAOH,IAC/BK,EAAYC,EAAQC,GAAa,EAASR,GAE3CyC,EAAe,IAAWzC,EAAWgC,aAA+C,EAASA,EAAW5O,UAAW,CACvH,CAAC,GAAG4M,KAAaxB,KAAU+D,EAC3B,CAAC,GAAGvC,eAAwBxB,IAAU+D,EACtC,CAAC,GAAGvC,aAAsB8B,EAC1B,CAAC,GAAG9B,SAAgC,QAAdzB,EACtB,CAAC,GAAGyB,iBAA0B6B,GAC7BzO,EAAWuO,EAAepB,EAAQC,GAC/BkC,EAAmBvL,IACvBA,EAAEwL,kBACFpM,SAAkDA,EAAQY,GACtDA,EAAEyL,kBAGNX,GAAW,KAEN,CAAEY,IAAmB,EAAAC,EAAA,IAAY,OAAapB,IAAW,OAAaM,GAAa,CACxFe,UAAU,EACVC,gBAAiBC,IACf,MAAMC,EAA2B,gBAAoB,OAAQ,CAC3D9P,UAAW,GAAG4M,eACdzM,QAASmP,GACRO,GACH,OAAO,QAAeA,EAAUC,EAAaC,IAAe,CAC1D5P,QAAS4D,IACP,IAAIiM,EACqF,QAAxFA,EAAKD,aAAiD,EAASA,EAAY5P,eAA4B,IAAP6P,GAAyBA,EAAGjO,KAAKgO,EAAahM,GAC/IuL,EAAiBvL,IAEnB/D,UAAW,IAAW+P,aAAiD,EAASA,EAAY/P,UAAW,GAAG4M,sBAI1GqD,EAAsC,mBAAlB/O,EAAMf,SAA0BqO,GAA8B,MAAlBA,EAAS3N,KACzEgP,EAAWvO,GAAQ,KACnB4O,EAAOL,EAAyB,gBAAoB,WAAgB,KAAMA,EAAUrB,GAAyB,gBAAoB,OAAQ,KAAMA,IAAcA,EAC7J2B,EAAuB,gBAAoB,OAAQnG,OAAOC,OAAO,CAAC,EAAG6E,EAAU,CACnF3N,IAAKA,EACLnB,UAAWqP,EACXlH,MAAOiH,IACLc,EAAMT,EAAiBR,GAAyB,gBAAoB,EAAW,CACjF3P,IAAK,SACLsN,UAAWA,IACTsC,GAAyB,gBAAoB,EAAW,CAC1D5P,IAAK,SACLsN,UAAWA,KAEb,OAAOM,EAAW+C,EAA0B,gBAAoB,IAAM,CACpEnL,UAAW,OACVqL,GAAWA,KAEVlK,EAAMoI,EAIZpI,EAAI0G,aAAe,EACnB,O,wNCnHA,MAAMyD,EAAoB,CAACC,EAAS5E,EAAa6E,EAAWhI,EAAOiI,KAAa,CAC9E/F,WAAY6F,EACZ5F,OAAQ,IAAG,QAAKnC,EAAMC,cAAcD,EAAMoC,YAAYe,IACtD,CAAC,GAAG8E,UAAkB,CACpBnF,MAAOkF,KAGErE,EAAe3D,IAC1B,MAAM,aACJuB,EACA2G,mBAAoBC,EAAQ,SAC5BrG,EAAQ,SACRsG,EAAQ,SACRrG,EAAQ,WACRsG,EAAU,WACVrG,EACAsG,eAAgBhG,EAAY,oBAC5BiG,EAAmB,wBACnBC,EAAuB,UACvBnH,EAAS,iBACT6B,EAAgB,uBAChBuF,EAAsB,eACtBC,GACE1I,EACJ,MAAO,CACL,CAACuB,GAAeG,OAAOC,OAAOD,OAAOC,OAAO,CAAC,GAAG,QAAe3B,IAAS,CACtE4C,SAAU,WACVhB,QAAS,OACT+G,WAAY,SACZC,QAASF,EACTG,SAAU,aACVvG,eACA,CAAC,IAAIf,SAAqB,CACxBsB,UAAW,OAEb,CAAC,GAAGtB,aAAyB,CAC3BuH,KAAM,EACNC,SAAU,GAEZ,CAAC,GAAGxH,UAAsB,CACxBM,gBAAiBC,EACjBE,WAAY,GAEd,gBAAiB,CACfJ,QAAS,OACTG,WACAC,cAEF,YAAa,CACXc,MAAOI,GAET,CAAC,IAAI3B,kBAA8B,CACjCyH,SAAU,SACVxG,QAAS,EACTC,WAAY,cAAc0F,KAAYI,cAAgCJ,KAAYI,2BACpEJ,KAAYI,qBAAuCJ,KAAYI,6BAC7DJ,KAAYI,KAE9B,CAAC,IAAIhH,yBAAqC,CACxC0H,UAAW,EACXC,aAAc,eACdC,WAAY,EACZC,cAAe,EACf5G,QAAS,KAGb,CAAC,GAAGjB,sBAAkC,CACpCoH,WAAY,aACZC,QAASH,EACT,CAAC,GAAGlH,UAAsB,CACxBM,gBAAiBuG,EACjBrG,SAAUyG,EACVxG,WAAY,GAEd,CAAC,GAAGT,aAAyB,CAC3BK,QAAS,QACTsH,aAAcpH,EACdgB,MAAOI,EACPnB,SAAUsG,GAEZ,CAAC,GAAG9G,iBAA6B,CAC/BK,QAAS,QACTkB,MAAOzB,IAGX,CAAC,GAAGE,YAAwB,CAC1B2H,aAAc,EACd/G,OAAQ,eACRG,aAAc,KAIP+G,EAAerJ,IAC1B,MAAM,aACJuB,EAAY,aACZ+H,EAAY,mBACZC,EAAkB,eAClBC,EAAc,aACdC,EAAY,mBACZC,EAAkB,eAClBC,EAAc,WACdC,EAAU,iBACVC,EAAgB,aAChBC,EAAY,UACZC,EAAS,gBACTC,EAAe,YACfC,GACEjK,EACJ,MAAO,CACL,CAACuB,GAAe,CACd,YAAauG,EAAkB0B,EAAgBD,EAAoBD,EAActJ,EAAOuB,GACxF,SAAUuG,EAAkBmC,EAAaD,EAAiBD,EAAW/J,EAAOuB,GAC5E,YAAauG,EAAkB6B,EAAgBD,EAAoBD,EAAczJ,EAAOuB,GACxF,UAAWG,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAGmG,EAAkBgC,EAAcD,EAAkBD,EAAY5J,EAAOuB,IAAgB,CAC9H,CAAC,GAAGA,uBAAmC,CACrC2I,OAAQ,EACRtB,QAAS,QAMNuB,EAAiBnK,IAC5B,MAAM,aACJuB,EAAY,QACZ6B,EAAO,kBACPV,EAAiB,SACjBZ,EAAQ,aACR5B,EAAY,UACZ8C,EAAS,eACToH,GACEpK,EACJ,MAAO,CACL,CAACuB,GAAe,CACd,WAAY,CACVwB,kBAAmBjB,GAErB,CAAC,GAAGP,gBAA4B,CAC9BwB,kBAAmBjB,EACnB8G,QAAS,EACTI,SAAU,SACVjH,SAAU7B,EACV8B,YAAY,QAAK9B,GACjBoD,gBAAiB,cACjBnB,OAAQ,OACRkI,QAAS,OACTpH,OAAQ,UACR,CAAC,GAAGG,WAAkB,CACpBN,MAAOE,EACPP,WAAY,SAASC,IACrB,UAAW,CACTI,MAAOsH,KAIb,eAAgB,CACdtH,MAAOE,EACPP,WAAY,SAASC,IACrB,UAAW,CACTI,MAAOsH,OAcjB,OAAe,QAAc,QAASpK,GAAS,CAAC2D,EAAa3D,GAAQqJ,EAAarJ,GAAQmK,EAAenK,IARpEA,IAE5B,CACLwI,wBAAyBxI,EAAMsK,iBAC/B5B,eAAgB,GAAG1I,EAAMuK,kCACzB9B,uBAAwB,GAAGzI,EAAMwK,eAAexK,EAAMyK,kCC3KtD7G,EAAgC,SAAUC,EAAGpI,GAC/C,IAAIqI,EAAI,CAAC,EACT,IAAK,IAAIC,KAAKF,EAAOnC,OAAO3H,UAAUiK,eAAevK,KAAKoK,EAAGE,IAAMtI,EAAEwI,QAAQF,GAAK,IAAGD,EAAEC,GAAKF,EAAEE,IAC9F,GAAS,MAALF,GAAqD,mBAAjCnC,OAAOwC,sBAA2C,KAAIC,EAAI,EAAb,IAAgBJ,EAAIrC,OAAOwC,sBAAsBL,GAAIM,EAAIJ,EAAE/L,OAAQmM,IAClI1I,EAAEwI,QAAQF,EAAEI,IAAM,GAAKzC,OAAO3H,UAAUqK,qBAAqB3K,KAAKoK,EAAGE,EAAEI,MAAKL,EAAEC,EAAEI,IAAMN,EAAEE,EAAEI,IADuB,CAGvH,OAAOL,CACT,EAeA,MAAM4G,EAAgB,CACpBC,QAASC,EAAA,EACTC,KAAMC,EAAA,EACNtQ,MAAOuQ,EAAA,EACPC,QAASC,EAAA,GAELC,EAAWtS,IACf,MAAM,KACJI,EAAI,UACJsL,EAAS,KACT/L,GACEK,EACEuS,EAAWT,EAAcnS,IAAS,KACxC,OAAIS,GACK,QAAeA,EAAmB,gBAAoB,OAAQ,CACnEtB,UAAW,GAAG4M,UACbtL,GAAO,KAAM,CACdtB,UAAW,IAAW,GAAG4M,SAAkBtL,EAAKJ,MAAMlB,cAGtC,gBAAoByT,EAAU,CAChDzT,UAAW,GAAG4M,YAGZ8G,EAAgBxS,IACpB,MAAM,WACJyS,EAAU,UACV/G,EAAS,UACTgH,EAAS,YACTC,EAAW,UACXC,GACE5S,EACEuO,GAAgC,IAAdmE,QAAoC5E,IAAd4E,EAAuC,gBAAoBG,EAAA,EAAe,MAAQH,EAChI,OAAOD,EAA2B,gBAAoB,SAAU3J,OAAOC,OAAO,CAC5EpJ,KAAM,SACNV,QAAS0T,EACT7T,UAAW,GAAG4M,eACdoH,SAAU,GACTF,GAAYrE,GAAoB,MAE/B1O,EAAqB,aAAiB,CAACG,EAAOC,KAClD,MAAM,YACFF,EACA2L,UAAWC,EAAkB,QAC7B7L,EAAO,OACPiT,EAAM,UACNjU,EAAS,cACTuO,EAAa,MACbpG,EAAK,aACL+L,EAAY,aACZC,EAAY,QACZhU,EAAO,WACPiU,EAAU,SACVC,EAAQ,SACR1E,EAAQ,UACR2E,EAAS,UACTV,EAAS,OACTW,EAAM,GACN7T,GACEQ,EACJsT,EAAatI,EAAOhL,EAAO,CAAC,cAAe,YAAa,UAAW,SAAU,YAAa,gBAAiB,QAAS,eAAgB,eAAgB,UAAW,aAAc,WAAY,WAAY,YAAa,YAAa,SAAU,QACpOuT,EAAQC,GAAa,YAAe,GAK3C,MAAMC,EAAc,SAAa,MACjC,sBAA0BxT,EAAK,KAAM,CACnCyT,cAAeD,EAAYrP,WAE7B,MAAM,aACJ0H,EAAY,UACZ7B,EACAwE,SAAUkF,EACVjB,UAAWkB,EACX9U,UAAW+U,EACX5M,MAAO6M,IACL,QAAmB,SACjBpI,EAAYI,EAAa,QAASH,IACjCK,EAAYC,EAAQC,GAAa,EAASR,GAC3CiH,EAAc9P,IAClB,IAAIiM,EACJ0E,GAAU,GACe,QAAxB1E,EAAK9O,EAAMiC,eAA4B,IAAP6M,GAAyBA,EAAGjO,KAAKb,EAAO6C,IAErElD,EAAO,UAAc,SACNmO,IAAf9N,EAAML,KACDK,EAAML,KAGRoT,EAAS,UAAY,OAC3B,CAAC/S,EAAML,KAAMoT,IAEVN,EAAa,UAAc,MACP,iBAAbhE,IAAyBA,EAASiE,eACzCU,IAGoB,kBAAb3E,EACFA,GAGS,IAAdiE,SAAuBA,KAGlBiB,IACR,CAACP,EAAWV,EAAWjE,EAAUkF,IAE9BI,KAAahB,QAAuBjF,IAAbqF,IAAgCA,EACvD9D,EAAW,IAAW3D,EAAW,GAAGA,KAAa/L,IAAQ,CAC7D,CAAC,GAAG+L,wBAAiC3L,EACrC,CAAC,GAAG2L,cAAuBqI,EAC3B,CAAC,GAAGrI,cAAuBqH,EAC3B,CAAC,GAAGrH,SAAgC,QAAdzB,GACrB4J,EAAkB/U,EAAWuO,EAAenB,EAAWD,GACpDJ,GAAY,EAAAmI,EAAA,GAAUV,EAAY,CACtCW,MAAM,EACNvR,MAAM,IAEF6L,EAAkB,UAAc,IACZ,iBAAbE,GAAyBA,EAASiE,UACpCjE,EAASiE,UAEdU,SAGctF,IAAd4E,EACKA,EAEsB,iBAApBiB,GAAgCA,EAAgBjB,UAClDiB,EAAgBjB,UAElBkB,GACN,CAAClB,EAAWjE,EAAU2E,EAAWQ,IAC9BM,EAAkB,UAAc,KACpC,MAAMC,EAAS1F,QAA2CA,EAAWkF,EACrE,GAAsB,iBAAXQ,EAAqB,CAC9B,MACIzB,UAAW0B,GACTD,EAEN,OADcnJ,EAAOmJ,EAAQ,CAAC,aAEhC,CACA,MAAO,CAAC,GACP,CAAC1F,EAAUkF,IACd,OAAO3H,EAAwB,gBAAoB,KAAW,CAC5DwB,SAAU+F,EACVc,WAAY,GAAG3I,WACf4I,cAAc,EACdC,aAAa,EACbC,aAAcC,IAAQ,CACpBpE,UAAWoE,EAAKC,eAElBC,WAAYzB,GACX,EACDpU,UAAW8V,EACX3N,MAAO4N,GACNC,IAAyB,gBAAoB,MAAOhM,OAAOC,OAAO,CACnEvJ,GAAIA,EACJS,KAAK,QAAWwT,EAAaqB,GAC7B,aAAcvB,EACdzU,UAAW,IAAWuQ,EAAUuF,GAChC3N,MAAO6B,OAAOC,OAAOD,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAG+K,GAAe7M,GAAQ4N,GAC5E7B,aAAcA,EACdC,aAAcA,EACdhU,QAASA,EACT8V,KAAM,SACLlJ,GAAYkI,EAA2B,gBAAoBzB,EAAU,CACtEvS,YAAaA,EACbK,KAAMJ,EAAMI,KACZsL,UAAWA,EACX/L,KAAMA,IACF,KAAmB,gBAAoB,MAAO,CAClDb,UAAW,GAAG4M,aACb5L,EAAuB,gBAAoB,MAAO,CACnDhB,UAAW,GAAG4M,aACb5L,GAAW,KAAMC,EAA2B,gBAAoB,MAAO,CACxEjB,UAAW,GAAG4M,iBACb3L,GAAe,MAAOsT,EAAsB,gBAAoB,MAAO,CACxEvU,UAAW,GAAG4M,YACb2H,GAAU,KAAmB,gBAAoBb,EAAe,CACjEC,WAAYA,EACZ/G,UAAWA,EACXgH,UAAWnE,EACXoE,YAAaA,EACbC,UAAWsB,SAMf,Q,gEC/MA,IAAIc,EAA6B,SAAUC,GACzC,SAASD,IACP,IAAIzU,ECPY2K,EAAGgK,EAAGrS,EDgBtB,OARA,OAAgBV,KAAM6S,GCRN9J,EDSG/I,KCTA+S,EDSMF,ECTHnS,EDSkBpC,UCRnCyU,GAAI,EAAAC,EAAA,GAAeD,IDQxB3U,GCR4B,EAAA6U,EAAA,GAA0BlK,GAAG,EAAAmK,EAAA,KAA6BC,QAAQC,UAAUL,EAAGrS,GAAK,IAAI,EAAAsS,EAAA,GAAejK,GAAGsK,aAAeN,EAAEpU,MAAMoK,EAAGrI,KDS1J4S,MAAQ,CACZ7T,WAAOkM,EACPmE,KAAM,CACJyD,eAAgB,KAGbnV,CACT,CAEA,OADA,OAAUyU,EAAeC,IAClB,OAAaD,EAAe,CAAC,CAClC5W,IAAK,oBACL8G,MAAO,SAA2BtD,EAAOqQ,GACvC9P,KAAKwT,SAAS,CACZ/T,QACAqQ,QAEJ,GACC,CACD7T,IAAK,SACL8G,MAAO,WACL,MAAM,QACJpF,EAAO,YACPC,EAAW,GACXP,EAAE,SACF8N,GACEnL,KAAKnC,OACH,MACJ4B,EAAK,KACLqQ,GACE9P,KAAKsT,MACHC,GAAkBzD,aAAmC,EAASA,EAAKyD,iBAAmB,KACtFE,OAAkC,IAAZ9V,GAA2B8B,GAAS,IAAIiU,WAAa/V,EAC3EgW,OAA0C,IAAhB/V,EAA8B2V,EAAiB3V,EAC/E,OAAI6B,EACkB,gBAAoB,EAAO,CAC7CpC,GAAIA,EACJG,KAAM,QACNG,QAAS8V,EACT7V,YAA0B,gBAAoB,MAAO,CACnDkH,MAAO,CACLkC,SAAU,QACV4M,UAAW,SAEZD,KAGAxI,CACT,IAEJ,CAtDiC,CAsD/B,aACF,QE3DA,MAAM,EAAQ,EACd,EAAM0H,cAAgB,EACtB,O", "sources": ["webpack://autogentstudio/./node_modules/lucide-react/dist/esm/icons/refresh-ccw.js", "webpack://autogentstudio/./src/components/views/labs/sidebar.tsx", "webpack://autogentstudio/./src/components/views/labs/types.tsx", "webpack://autogentstudio/./src/components/views/labs/labs/component.tsx", "webpack://autogentstudio/./node_modules/@ant-design/icons-svg/es/asn/SendOutlined.js", "webpack://autogentstudio/./node_modules/@ant-design/icons/es/icons/SendOutlined.js", "webpack://autogentstudio/./src/components/views/labs/labs/api.ts", "webpack://autogentstudio/./src/components/views/labs/labs/tool.tsx", "webpack://autogentstudio/./src/components/views/labs/labs/guides.tsx", "webpack://autogentstudio/./src/components/views/labs/manager.tsx", "webpack://autogentstudio/./src/pages/labs.tsx", "webpack://autogentstudio/./node_modules/antd/es/tag/style/index.js", "webpack://autogentstudio/./node_modules/antd/es/tag/CheckableTag.js", "webpack://autogentstudio/./node_modules/antd/es/tag/style/presetCmp.js", "webpack://autogentstudio/./node_modules/antd/es/tag/style/statusCmp.js", "webpack://autogentstudio/./node_modules/antd/es/_util/capitalize.js", "webpack://autogentstudio/./node_modules/antd/es/tag/index.js", "webpack://autogentstudio/./node_modules/antd/es/alert/style/index.js", "webpack://autogentstudio/./node_modules/antd/es/alert/Alert.js", "webpack://autogentstudio/./node_modules/antd/es/alert/ErrorBoundary.js", "webpack://autogentstudio/./node_modules/@babel/runtime/helpers/esm/callSuper.js", "webpack://autogentstudio/./node_modules/antd/es/alert/index.js"], "sourcesContent": ["/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst RefreshCcw = createLucideIcon(\"RefreshCcw\", [\n  [\"path\", { d: \"M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8\", key: \"14sxne\" }],\n  [\"path\", { d: \"M3 3v5h5\", key: \"1xhq8a\" }],\n  [\"path\", { d: \"M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16\", key: \"1hlbsb\" }],\n  [\"path\", { d: \"M16 16h5v5\", key: \"ccwih5\" }]\n]);\n\nexport { RefreshCcw as default };\n//# sourceMappingURL=refresh-ccw.js.map\n", "import React from \"react\";\r\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from \"antd\";\r\nimport {\r\n  PanelLeftClose,\r\n  PanelLeftOpen,\r\n  Book,\r\n  InfoIcon,\r\n  RefreshCcw,\r\n} from \"lucide-react\";\r\nimport type { Lab } from \"./types\";\r\n\r\ninterface LabsSidebarProps {\r\n  isOpen: boolean;\r\n  labs: Lab[];\r\n  currentLab: Lab | null;\r\n  onToggle: () => void;\r\n  onSelectLab: (guide: Lab) => void;\r\n  isLoading?: boolean;\r\n}\r\n\r\nexport const LabsSidebar: React.FC<LabsSidebarProps> = ({\r\n  isOpen,\r\n  labs,\r\n  currentLab,\r\n  onToggle,\r\n  onSelectLab,\r\n  isLoading = false,\r\n}) => {\r\n  // Render collapsed state\r\n  if (!isOpen) {\r\n    return (\r\n      <div className=\"h-full border-r border-secondary\">\r\n        <div className=\"p-2 -ml-2\">\r\n          <Tooltip title=\"Documentation\">\r\n            <button\r\n              onClick={onToggle}\r\n              className=\"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50\"\r\n            >\r\n              <PanelLeftOpen strokeWidth={1.5} className=\"h-6 w-6\" />\r\n            </button>\r\n          </Tooltip>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"h-full border-r border-secondary\">\r\n      {/* Header */}\r\n      <div className=\"flex items-center justify-between pt-0 p-4 pl-2 pr-2 border-b border-secondary\">\r\n        <div className=\"flex items-center gap-2\">\r\n          {/* <Book className=\"w-4 h-4\" /> */}\r\n          <span className=\"text-primary font-medium\">Labs</span>\r\n          {/* <span className=\"px-2 py-0.5 text-xs bg-accent/10 text-accent rounded\">\r\n            {guides.length}\r\n          </span> */}\r\n        </div>\r\n        <Tooltip title=\"Close Sidebar\">\r\n          <button\r\n            onClick={onToggle}\r\n            className=\"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50\"\r\n          >\r\n            <PanelLeftClose strokeWidth={1.5} className=\"h-6 w-6\" />\r\n          </button>\r\n        </Tooltip>\r\n      </div>\r\n\r\n      {/* Loading State */}\r\n      {isLoading && (\r\n        <div className=\"p-4\">\r\n          <RefreshCcw className=\"w-4 h-4 inline-block animate-spin\" />\r\n        </div>\r\n      )}\r\n\r\n      {/* Empty State */}\r\n      {!isLoading && labs.length === 0 && (\r\n        <div className=\"p-2 mt-2 mr-2 text-center text-secondary text-sm border border-dashed rounded\">\r\n          <InfoIcon className=\"w-4 h-4 inline-block mr-1.5 -mt-0.5\" />\r\n          No labs available. Please check back later.\r\n        </div>\r\n      )}\r\n\r\n      {/* Guides List */}\r\n      <div className=\"overflow-y-auto h-[calc(100%-64px)] mt-4\">\r\n        {labs.map((lab) => (\r\n          <div key={lab.id} className=\"relative\">\r\n            <div\r\n              className={`absolute top-1 left-0.5 z-50 h-[calc(100%-8px)]\r\n               w-1 bg-opacity-80 rounded ${\r\n                 currentLab?.id === lab.id ? \"bg-accent\" : \"bg-tertiary\"\r\n               }`}\r\n            />\r\n            <div\r\n              className={`group ml-1 flex flex-col p-2 rounded-l cursor-pointer hover:bg-secondary ${\r\n                currentLab?.id === lab.id\r\n                  ? \"border-accent bg-secondary\"\r\n                  : \"border-transparent\"\r\n              }`}\r\n              onClick={() => onSelectLab(lab)}\r\n            >\r\n              {/* Guide Title */}\r\n              <div className=\"flex items-center justify-between\">\r\n                <span className=\"text-sm truncate\">{lab.title}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n", "export interface Lab {\r\n  id: string;\r\n  title: string;\r\n  type: \"python\" | \"docker\" | \"cloud\";\r\n}\r\n\r\nexport const defaultLabs: Lab[] = [\r\n  // {\r\n  //   id: \"component-builder\",\r\n  //   title: \"Component Builder\",\r\n  //   type: \"python\",\r\n  // },\r\n  {\r\n    id: \"tool-maker\",\r\n    title: \"Tool Maker\",\r\n    type: \"python\",\r\n  },\r\n];\r\n", "import React from \"react\";\r\nimport { Alert } from \"antd\";\r\nimport { copyToClipboard } from \"./guides\";\r\nimport { Download } from \"lucide-react\";\r\n\r\nconst ComponentLab: React.FC = () => {\r\n  return (\r\n    <div className=\"\">\r\n      <h1 className=\"tdext-2xl font-bold mb-6\">\r\n        Using 多智能体工作室 Teams in Python Code and REST API\r\n      </h1>\r\n\r\n      <Alert\r\n        className=\"mb-6\"\r\n        message=\"Prerequisites\"\r\n        description={\r\n          <ul className=\"list-disc pl-4 mt-2 space-y-1\">\r\n            <li>多智能体工作室 installed</li>\r\n          </ul>\r\n        }\r\n        type=\"info\"\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ComponentLab;\r\n", "// This icon file is generated automatically.\nvar SendOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"defs\", \"attrs\": {}, \"children\": [{ \"tag\": \"style\", \"attrs\": {} }] }, { \"tag\": \"path\", \"attrs\": { \"d\": \"M931.4 498.9L94.9 79.5c-3.4-1.7-7.3-2.1-11-1.2a15.99 15.99 0 00-11.7 19.3l86.2 352.2c1.3 5.3 5.2 9.6 10.4 11.3l147.7 50.7-147.6 50.7c-5.2 1.8-9.1 6-10.3 11.3L72.2 926.5c-.9 3.7-.5 7.6 1.2 10.9 3.9 7.9 13.5 11.1 21.5 7.2l836.5-417c3.1-1.5 5.6-4.1 7.2-7.1 3.9-8 .7-17.6-7.2-21.6zM170.8 826.3l50.3-205.6 295.2-101.3c2.3-.8 4.2-2.6 5-5 1.4-4.2-.8-8.7-5-10.2L221.1 403 171 198.2l628 314.9-628.2 313.2z\" } }] }, \"name\": \"send\", \"theme\": \"outlined\" };\nexport default SendOutlined;\n", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport SendOutlinedSvg from \"@ant-design/icons-svg/es/asn/SendOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar SendOutlined = function SendOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: SendOutlinedSvg\n  }));\n};\n\n/**![send](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik05MzEuNCA0OTguOUw5NC45IDc5LjVjLTMuNC0xLjctNy4zLTIuMS0xMS0xLjJhMTUuOTkgMTUuOTkgMCAwMC0xMS43IDE5LjNsODYuMiAzNTIuMmMxLjMgNS4zIDUuMiA5LjYgMTAuNCAxMS4zbDE0Ny43IDUwLjctMTQ3LjYgNTAuN2MtNS4yIDEuOC05LjEgNi0xMC4zIDExLjNMNzIuMiA5MjYuNWMtLjkgMy43LS41IDcuNiAxLjIgMTAuOSAzLjkgNy45IDEzLjUgMTEuMSAyMS41IDcuMmw4MzYuNS00MTdjMy4xLTEuNSA1LjYtNC4xIDcuMi03LjEgMy45LTggLjctMTcuNi03LjItMjEuNnpNMTcwLjggODI2LjNsNTAuMy0yMDUuNiAyOTUuMi0xMDEuM2MyLjMtLjggNC4yLTIuNiA1LTUgMS40LTQuMi0uOC04LjctNS0xMC4yTDIyMS4xIDQwMyAxNzEgMTk4LjJsNjI4IDMxNC45LTYyOC4yIDMxMy4yeiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(SendOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'SendOutlined';\n}\nexport default RefIcon;", "// api.ts for Tool Maker Lab\r\nimport { BaseAPI } from \"../../../utils/baseapi\";\r\n\r\nexport interface ToolMakerEvent {\r\n  status: string;\r\n  content: string;\r\n}\r\n\r\nexport interface ToolComponentModel {\r\n  provider: string;\r\n  component_type: string;\r\n  version: number;\r\n  component_version: number;\r\n  description: string;\r\n  label: string;\r\n  config: any;\r\n}\r\n\r\nexport type ToolMakerStreamMessage =\r\n  | { event: ToolMakerEvent }\r\n  | { component: ToolComponentModel }\r\n  | { error: string };\r\n\r\nexport class ToolMakerAPI extends BaseAPI {\r\n  ws: WebSocket | null = null;\r\n\r\n  // Helper for WebSocket URL construction (similar to MCP implementation)\r\n  private getWebSocketBaseUrl(url: string): string {\r\n    try {\r\n      let baseUrl = url.replace(/(^\\w+:|^)\\/\\//, \"\");\r\n      if (baseUrl.startsWith(\"localhost\")) {\r\n        baseUrl = baseUrl.replace(\"/api\", \"\");\r\n      } else if (baseUrl === \"/api\") {\r\n        baseUrl = window.location.host;\r\n      } else {\r\n        baseUrl = baseUrl.replace(\"/api\", \"\").replace(/\\/$/, \"\");\r\n      }\r\n      return baseUrl;\r\n    } catch (error) {\r\n      throw new Error(\"Invalid server URL configuration\");\r\n    }\r\n  }\r\n\r\n  connect(\r\n    onMessage: (msg: ToolMakerStreamMessage) => void,\r\n    onError?: (err: any) => void,\r\n    onClose?: () => void\r\n  ) {\r\n    // Use the same server URL logic as other APIs\r\n    const serverUrl = this.getBaseUrl(); // e.g., \"/api\" or \"http://localhost:8081/api\"\r\n    const baseUrl = this.getWebSocketBaseUrl(serverUrl);\r\n    const protocol = window.location.protocol === \"https:\" ? \"wss:\" : \"ws:\";\r\n    const wsUrl = `${protocol}//${baseUrl}/api/maker/tool`;\r\n    this.ws = new window.WebSocket(wsUrl);\r\n    this.ws.onmessage = (event) => {\r\n      try {\r\n        const data = JSON.parse(event.data);\r\n        onMessage(data);\r\n      } catch (e) {\r\n        if (onError) onError(e);\r\n      }\r\n    };\r\n    this.ws.onerror = (event) => {\r\n      if (onError) onError(event);\r\n    };\r\n    this.ws.onclose = () => {\r\n      if (onClose) onClose();\r\n    };\r\n  }\r\n\r\n  sendDescription(description: string) {\r\n    if (this.ws && this.ws.readyState === 1) {\r\n      this.ws.send(JSON.stringify({ description }));\r\n    }\r\n  }\r\n\r\n  close() {\r\n    if (this.ws) {\r\n      this.ws.close();\r\n    }\r\n  }\r\n}\r\n\r\nexport const toolMakerAPI = new ToolMakerAPI();\r\n", "import React, { useState, useRef } from \"react\";\r\nimport { In<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Tag } from \"antd\";\r\nimport { SendOutlined } from \"@ant-design/icons\";\r\nimport {\r\n  toolMakerAPI,\r\n  ToolMakerEvent,\r\n  ToolComponentModel,\r\n  ToolMakerStreamMessage,\r\n} from \"./api\";\r\n\r\nconst ToolMakerLab: React.FC = () => {\r\n  const [description, setDescription] = useState(\"\");\r\n  const [events, setEvents] = useState<ToolMakerEvent[]>([]);\r\n  const [component, setComponent] = useState<ToolComponentModel | null>(null);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const wsRef = useRef<typeof toolMakerAPI | null>(null);\r\n\r\n  const exampleTools = [\r\n    \"A tool that fetches the content of a web page\",\r\n    \"A calculator tool that performs mathematical operations\",\r\n    \"A tool that generates QR codes from text\",\r\n    \"A tool that converts text to speech\",\r\n    \"A tool that extracts text from images (OCR)\",\r\n    \"A tool that validates email addresses\",\r\n    \"A tool that generates secure random passwords\",\r\n    \"A tool that converts currencies using live exchange rates\",\r\n  ];\r\n\r\n  const handleExampleClick = (example: string) => {\r\n    setDescription(example);\r\n  };\r\n\r\n  const handleStart = () => {\r\n    setEvents([]);\r\n    setComponent(null);\r\n    setError(null);\r\n    setLoading(true);\r\n    wsRef.current = toolMakerAPI;\r\n    wsRef.current.connect(\r\n      (msg: ToolMakerStreamMessage) => {\r\n        if (\"event\" in msg) {\r\n          setEvents((prev) => [...prev, msg.event]);\r\n        } else if (\"component\" in msg) {\r\n          setComponent(msg.component);\r\n          setLoading(false);\r\n          wsRef.current?.close();\r\n        } else if (\"error\" in msg) {\r\n          setError(msg.error);\r\n          setLoading(false);\r\n          wsRef.current?.close();\r\n        }\r\n      },\r\n      (err) => {\r\n        setError(\"WebSocket error: \" + err);\r\n        setLoading(false);\r\n      },\r\n      () => {\r\n        setLoading(false);\r\n      }\r\n    );\r\n    setTimeout(() => {\r\n      wsRef.current?.sendDescription(description);\r\n    }, 200); // Give ws time to connect\r\n  };\r\n\r\n  return (\r\n    <div className=\"\">\r\n      <h1 className=\"text-2xl font-bold mb-6\">Tool Maker (Experimental)</h1>\r\n      <p className=\"mb-4 text-secondary\">\r\n        This lab allows you to create and test new tools using natural language\r\n        descriptions.\r\n      </p>\r\n\r\n      <div className=\"mb-4\">\r\n        <p className=\"text-sm font-medium mb-2 text-secondary\">\r\n          Try these examples:\r\n        </p>\r\n        <div className=\"flex flex-wrap gap-2\">\r\n          {exampleTools.map((example, idx) => (\r\n            <Tag\r\n              key={idx}\r\n              className=\"cursor-pointer hover:bg-primary/10 transition-colors\"\r\n              onClick={() => handleExampleClick(example)}\r\n            >\r\n              {example}\r\n            </Tag>\r\n          ))}\r\n        </div>\r\n      </div>\r\n      <div className=\"flex gap-2 mb-4\">\r\n        <Input\r\n          value={description}\r\n          onChange={(e) => setDescription(e.target.value)}\r\n          placeholder=\"Describe your tool (e.g. 'A tool that fetches the content of a web page')\"\r\n          onPressEnter={handleStart}\r\n          disabled={loading}\r\n        />\r\n        <Button\r\n          type=\"primary\"\r\n          icon={<SendOutlined />}\r\n          onClick={handleStart}\r\n          loading={loading}\r\n          disabled={!description.trim() || loading}\r\n        >\r\n          Create\r\n        </Button>\r\n      </div>\r\n      {error && <Alert type=\"error\" message={error} className=\"mb-4\" />}\r\n      {loading && <Spin className=\"mb-4\" />}\r\n      <div className=\"mb-4\">\r\n        {events.map((event, idx) => (\r\n          <Alert\r\n            key={idx}\r\n            type=\"info\"\r\n            message={event.status}\r\n            description={event.content}\r\n            className=\"mb-2\"\r\n          />\r\n        ))}\r\n      </div>\r\n      {component && (\r\n        <div className=\"border rounded p-4 bg-secondary/10\">\r\n          <h2 className=\"font-semibold mb-2\">Generated Tool</h2>\r\n          <pre className=\"bg-secondary/20 p-2 rounded text-xs overflow-x-auto mb-2\">\r\n            {JSON.stringify(component, null, 2)}\r\n          </pre>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ToolMakerLab;\r\n", "import React from \"react\";\r\nimport { Lab } from \"../types\";\r\nimport ComponentLab from \"./component\";\r\nimport ToolMakerLab from \"./tool\";\r\n\r\ninterface LabContentProps {\r\n  lab: Lab;\r\n}\r\n\r\nexport const copyToClipboard = (text: string) => {\r\n  navigator.clipboard.writeText(text);\r\n};\r\nexport const LabContent: React.FC<LabContentProps> = ({ lab }) => {\r\n  // Render different content based on guide type and id\r\n  switch (lab.id) {\r\n    case \"python-setup\":\r\n      return <ComponentLab />;\r\n    case \"tool-maker\":\r\n      return <ToolMakerLab />;\r\n    default:\r\n      return (\r\n        <div className=\"text-secondary\">\r\n          A Lab with the title <strong>{lab.title}</strong> is work in progress!\r\n        </div>\r\n      );\r\n  }\r\n};\r\n\r\nexport default LabContent;\r\n", "import React, { useState, useEffect } from \"react\";\r\nimport { <PERSON><PERSON>ronR<PERSON>, TriangleAlert } from \"lucide-react\";\r\nimport { LabsSidebar } from \"./sidebar\";\r\nimport { Lab, defaultLabs } from \"./types\";\r\nimport { LabContent } from \"./labs/guides\";\r\n\r\nexport const LabsManager: React.FC = () => {\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [labs, setLabs] = useState<Lab[]>([]);\r\n  const [currentLab, setcurrentLab] = useState<Lab | null>(null);\r\n  const [isSidebarOpen, setIsSidebarOpen] = useState(() => {\r\n    if (typeof window !== \"undefined\") {\r\n      const stored = localStorage.getItem(\"labsSidebar\");\r\n      return stored !== null ? JSON.parse(stored) : true;\r\n    }\r\n    return true;\r\n  });\r\n\r\n  // Persist sidebar state\r\n  useEffect(() => {\r\n    if (typeof window !== \"undefined\") {\r\n      localStorage.setItem(\"labsSidebar\", JSON.stringify(isSidebarOpen));\r\n    }\r\n  }, [isSidebarOpen]);\r\n\r\n  // Set first guide as current if none selected\r\n  useEffect(() => {\r\n    if (!currentLab && labs.length > 0) {\r\n      setcurrentLab(labs[0]);\r\n    }\r\n  }, [labs, currentLab]);\r\n\r\n  useEffect(() => {\r\n    setLabs(defaultLabs);\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"relative    flex h-full w-full\">\r\n      {/* Sidebar */}\r\n      <div\r\n        className={`absolute  left-0 top-0 h-full transition-all duration-200 ease-in-out ${\r\n          isSidebarOpen ? \"w-64\" : \"w-12\"\r\n        }`}\r\n      >\r\n        <LabsSidebar\r\n          isOpen={isSidebarOpen}\r\n          labs={labs}\r\n          currentLab={currentLab}\r\n          onToggle={() => setIsSidebarOpen(!isSidebarOpen)}\r\n          onSelectLab={setcurrentLab}\r\n          isLoading={isLoading}\r\n        />\r\n      </div>\r\n\r\n      {/* Main Content */}\r\n      <div\r\n        className={`flex-1 transition-all max-w-5xl  -mr-6 duration-200 ${\r\n          isSidebarOpen ? \"ml-64\" : \"ml-12\"\r\n        }`}\r\n      >\r\n        <div className=\"p-4 pt-2\">\r\n          {/* Breadcrumb */}\r\n          <div className=\"flex items-center gap-2 mb-4 text-sm\">\r\n            <span className=\"text-primary font-medium\">Labs</span>\r\n            {currentLab && (\r\n              <>\r\n                <ChevronRight className=\"w-4 h-4 text-secondary\" />\r\n                <span className=\"text-secondary\">{currentLab.title}</span>\r\n              </>\r\n            )}\r\n          </div>\r\n          <div className=\"rounded border border-secondary border-dashed p-2 text-sm mb-4\">\r\n            <TriangleAlert className=\"w-4 h-4 inline-block mr-2 -mt-1 text-secondary \" />{\" \"}\r\n            Labs is designed to host experimental features for building and\r\n            debugging multiagent applications.\r\n          </div>\r\n          {/* Content Area */}\r\n          {currentLab ? (\r\n            <LabContent lab={currentLab} />\r\n          ) : (\r\n            <div className=\"flex items-center justify-center h-[calc(100vh-190px)] text-secondary\">\r\n              Select a lab from the sidebar to get started\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LabsManager;\r\n", "import * as React from \"react\";\r\nimport Layout from \"../components/layout\";\r\nimport { graphql } from \"gatsby\";\r\nimport DeployManager from \"../components/views/deploy/manager\";\r\nimport LabsManager from \"../components/views/labs/manager\";\r\n\r\n// markup\r\nconst LabsPage = ({ data }: any) => {\r\n  return (\r\n    <Layout meta={data.site.siteMetadata} title=\"实验室\" link={\"/labs\"}>\r\n      <main style={{ height: \"100%\" }} className=\" h-full \">\r\n        <LabsManager />\r\n      </main>\r\n    </Layout>\r\n  );\r\n};\r\n\r\nexport const query = graphql`\r\n  query HomePageQuery {\r\n    site {\r\n      siteMetadata {\r\n        description\r\n        title\r\n      }\r\n    }\r\n  }\r\n`;\r\n\r\nexport default LabsPage;\r\n", "import { unit } from '@ant-design/cssinjs';\nimport { FastColor } from '@ant-design/fast-color';\nimport { resetComponent } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\n// ============================== Styles ==============================\nconst genBaseStyle = token => {\n  const {\n    paddingXXS,\n    lineWidth,\n    tagPaddingHorizontal,\n    componentCls,\n    calc\n  } = token;\n  const paddingInline = calc(tagPaddingHorizontal).sub(lineWidth).equal();\n  const iconMarginInline = calc(paddingXXS).sub(lineWidth).equal();\n  return {\n    // Result\n    [componentCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      display: 'inline-block',\n      height: 'auto',\n      // https://github.com/ant-design/ant-design/pull/47504\n      marginInlineEnd: token.marginXS,\n      paddingInline,\n      fontSize: token.tagFontSize,\n      lineHeight: token.tagLineHeight,\n      whiteSpace: 'nowrap',\n      background: token.defaultBg,\n      border: `${unit(token.lineWidth)} ${token.lineType} ${token.colorBorder}`,\n      borderRadius: token.borderRadiusSM,\n      opacity: 1,\n      transition: `all ${token.motionDurationMid}`,\n      textAlign: 'start',\n      position: 'relative',\n      // RTL\n      [`&${componentCls}-rtl`]: {\n        direction: 'rtl'\n      },\n      '&, a, a:hover': {\n        color: token.defaultColor\n      },\n      [`${componentCls}-close-icon`]: {\n        marginInlineStart: iconMarginInline,\n        fontSize: token.tagIconSize,\n        color: token.colorIcon,\n        cursor: 'pointer',\n        transition: `all ${token.motionDurationMid}`,\n        '&:hover': {\n          color: token.colorTextHeading\n        }\n      },\n      [`&${componentCls}-has-color`]: {\n        borderColor: 'transparent',\n        [`&, a, a:hover, ${token.iconCls}-close, ${token.iconCls}-close:hover`]: {\n          color: token.colorTextLightSolid\n        }\n      },\n      '&-checkable': {\n        backgroundColor: 'transparent',\n        borderColor: 'transparent',\n        cursor: 'pointer',\n        [`&:not(${componentCls}-checkable-checked):hover`]: {\n          color: token.colorPrimary,\n          backgroundColor: token.colorFillSecondary\n        },\n        '&:active, &-checked': {\n          color: token.colorTextLightSolid\n        },\n        '&-checked': {\n          backgroundColor: token.colorPrimary,\n          '&:hover': {\n            backgroundColor: token.colorPrimaryHover\n          }\n        },\n        '&:active': {\n          backgroundColor: token.colorPrimaryActive\n        }\n      },\n      '&-hidden': {\n        display: 'none'\n      },\n      // To ensure that a space will be placed between character and `Icon`.\n      [`> ${token.iconCls} + span, > span + ${token.iconCls}`]: {\n        marginInlineStart: paddingInline\n      }\n    }),\n    [`${componentCls}-borderless`]: {\n      borderColor: 'transparent',\n      background: token.tagBorderlessBg\n    }\n  };\n};\n// ============================== Export ==============================\nexport const prepareToken = token => {\n  const {\n    lineWidth,\n    fontSizeIcon,\n    calc\n  } = token;\n  const tagFontSize = token.fontSizeSM;\n  const tagToken = mergeToken(token, {\n    tagFontSize,\n    tagLineHeight: unit(calc(token.lineHeightSM).mul(tagFontSize).equal()),\n    tagIconSize: calc(fontSizeIcon).sub(calc(lineWidth).mul(2)).equal(),\n    // Tag icon is much smaller\n    tagPaddingHorizontal: 8,\n    // Fixed padding.\n    tagBorderlessBg: token.defaultBg\n  });\n  return tagToken;\n};\nexport const prepareComponentToken = token => ({\n  defaultBg: new FastColor(token.colorFillQuaternary).onBackground(token.colorBgContainer).toHexString(),\n  defaultColor: token.colorText\n});\nexport default genStyleHooks('Tag', token => {\n  const tagToken = prepareToken(token);\n  return genBaseStyle(tagToken);\n}, prepareComponentToken);", "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { ConfigContext } from '../config-provider';\nimport useStyle from './style';\nconst CheckableTag = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      prefixCls: customizePrefixCls,\n      style,\n      className,\n      checked,\n      onChange,\n      onClick\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"style\", \"className\", \"checked\", \"onChange\", \"onClick\"]);\n  const {\n    getPrefixCls,\n    tag\n  } = React.useContext(ConfigContext);\n  const handleClick = e => {\n    onChange === null || onChange === void 0 ? void 0 : onChange(!checked);\n    onClick === null || onClick === void 0 ? void 0 : onClick(e);\n  };\n  const prefixCls = getPrefixCls('tag', customizePrefixCls);\n  // Style\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const cls = classNames(prefixCls, `${prefixCls}-checkable`, {\n    [`${prefixCls}-checkable-checked`]: checked\n  }, tag === null || tag === void 0 ? void 0 : tag.className, className, hashId, cssVarCls);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"span\", Object.assign({}, restProps, {\n    ref: ref,\n    style: Object.assign(Object.assign({}, style), tag === null || tag === void 0 ? void 0 : tag.style),\n    className: cls,\n    onClick: handleClick\n  })));\n});\nexport default CheckableTag;", "// Style as status component\nimport { prepareComponentToken, prepareToken } from '.';\nimport { genPresetColor, genSubStyleComponent } from '../../theme/internal';\n// ============================== Preset ==============================\nconst genPresetStyle = token => genPresetColor(token, (colorKey, {\n  textColor,\n  lightBorderColor,\n  lightColor,\n  darkColor\n}) => ({\n  [`${token.componentCls}${token.componentCls}-${colorKey}`]: {\n    color: textColor,\n    background: lightColor,\n    borderColor: lightBorderColor,\n    // Inverse color\n    '&-inverse': {\n      color: token.colorTextLightSolid,\n      background: darkColor,\n      borderColor: darkColor\n    },\n    [`&${token.componentCls}-borderless`]: {\n      borderColor: 'transparent'\n    }\n  }\n}));\n// ============================== Export ==============================\nexport default genSubStyleComponent(['Tag', 'preset'], token => {\n  const tagToken = prepareToken(token);\n  return genPresetStyle(tagToken);\n}, prepareComponentToken);", "import { prepareComponentToken, prepareToken } from '.';\nimport capitalize from '../../_util/capitalize';\nimport { genSubStyleComponent } from '../../theme/internal';\nconst genTagStatusStyle = (token, status, cssVariableType) => {\n  const capitalizedCssVariableType = capitalize(cssVariableType);\n  return {\n    [`${token.componentCls}${token.componentCls}-${status}`]: {\n      color: token[`color${cssVariableType}`],\n      background: token[`color${capitalizedCssVariableType}Bg`],\n      borderColor: token[`color${capitalizedCssVariableType}Border`],\n      [`&${token.componentCls}-borderless`]: {\n        borderColor: 'transparent'\n      }\n    }\n  };\n};\n// ============================== Export ==============================\nexport default genSubStyleComponent(['Tag', 'status'], token => {\n  const tagToken = prepareToken(token);\n  return [genTagStatusStyle(tagToken, 'success', 'Success'), genTagStatusStyle(tagToken, 'processing', 'Info'), genTagStatusStyle(tagToken, 'error', 'Error'), genTagStatusStyle(tagToken, 'warning', 'Warning')];\n}, prepareComponentToken);", "export default function capitalize(str) {\n  if (typeof str !== 'string') {\n    return str;\n  }\n  const ret = str.charAt(0).toUpperCase() + str.slice(1);\n  return ret;\n}", "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport { isPresetColor, isPresetStatusColor } from '../_util/colors';\nimport useClosable, { pickClosable } from '../_util/hooks/useClosable';\nimport { replaceElement } from '../_util/reactNode';\nimport { devUseWarning } from '../_util/warning';\nimport Wave from '../_util/wave';\nimport { ConfigContext } from '../config-provider';\nimport CheckableTag from './CheckableTag';\nimport useStyle from './style';\nimport PresetCmp from './style/presetCmp';\nimport StatusCmp from './style/statusCmp';\nconst InternalTag = /*#__PURE__*/React.forwardRef((tagProps, ref) => {\n  const {\n      prefixCls: customizePrefixCls,\n      className,\n      rootClassName,\n      style,\n      children,\n      icon,\n      color,\n      onClose,\n      bordered = true,\n      visible: deprecatedVisible\n    } = tagProps,\n    props = __rest(tagProps, [\"prefixCls\", \"className\", \"rootClassName\", \"style\", \"children\", \"icon\", \"color\", \"onClose\", \"bordered\", \"visible\"]);\n  const {\n    getPrefixCls,\n    direction,\n    tag: tagContext\n  } = React.useContext(ConfigContext);\n  const [visible, setVisible] = React.useState(true);\n  const domProps = omit(props, ['closeIcon', 'closable']);\n  // Warning for deprecated usage\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Tag');\n    warning.deprecated(!('visible' in tagProps), 'visible', 'visible && <Tag />');\n  }\n  React.useEffect(() => {\n    if (deprecatedVisible !== undefined) {\n      setVisible(deprecatedVisible);\n    }\n  }, [deprecatedVisible]);\n  const isPreset = isPresetColor(color);\n  const isStatus = isPresetStatusColor(color);\n  const isInternalColor = isPreset || isStatus;\n  const tagStyle = Object.assign(Object.assign({\n    backgroundColor: color && !isInternalColor ? color : undefined\n  }, tagContext === null || tagContext === void 0 ? void 0 : tagContext.style), style);\n  const prefixCls = getPrefixCls('tag', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  // Style\n  const tagClassName = classNames(prefixCls, tagContext === null || tagContext === void 0 ? void 0 : tagContext.className, {\n    [`${prefixCls}-${color}`]: isInternalColor,\n    [`${prefixCls}-has-color`]: color && !isInternalColor,\n    [`${prefixCls}-hidden`]: !visible,\n    [`${prefixCls}-rtl`]: direction === 'rtl',\n    [`${prefixCls}-borderless`]: !bordered\n  }, className, rootClassName, hashId, cssVarCls);\n  const handleCloseClick = e => {\n    e.stopPropagation();\n    onClose === null || onClose === void 0 ? void 0 : onClose(e);\n    if (e.defaultPrevented) {\n      return;\n    }\n    setVisible(false);\n  };\n  const [, mergedCloseIcon] = useClosable(pickClosable(tagProps), pickClosable(tagContext), {\n    closable: false,\n    closeIconRender: iconNode => {\n      const replacement = /*#__PURE__*/React.createElement(\"span\", {\n        className: `${prefixCls}-close-icon`,\n        onClick: handleCloseClick\n      }, iconNode);\n      return replaceElement(iconNode, replacement, originProps => ({\n        onClick: e => {\n          var _a;\n          (_a = originProps === null || originProps === void 0 ? void 0 : originProps.onClick) === null || _a === void 0 ? void 0 : _a.call(originProps, e);\n          handleCloseClick(e);\n        },\n        className: classNames(originProps === null || originProps === void 0 ? void 0 : originProps.className, `${prefixCls}-close-icon`)\n      }));\n    }\n  });\n  const isNeedWave = typeof props.onClick === 'function' || children && children.type === 'a';\n  const iconNode = icon || null;\n  const kids = iconNode ? (/*#__PURE__*/React.createElement(React.Fragment, null, iconNode, children && /*#__PURE__*/React.createElement(\"span\", null, children))) : children;\n  const tagNode = /*#__PURE__*/React.createElement(\"span\", Object.assign({}, domProps, {\n    ref: ref,\n    className: tagClassName,\n    style: tagStyle\n  }), kids, mergedCloseIcon, isPreset && /*#__PURE__*/React.createElement(PresetCmp, {\n    key: \"preset\",\n    prefixCls: prefixCls\n  }), isStatus && /*#__PURE__*/React.createElement(StatusCmp, {\n    key: \"status\",\n    prefixCls: prefixCls\n  }));\n  return wrapCSSVar(isNeedWave ? /*#__PURE__*/React.createElement(Wave, {\n    component: \"Tag\"\n  }, tagNode) : tagNode);\n});\nconst Tag = InternalTag;\nif (process.env.NODE_ENV !== 'production') {\n  Tag.displayName = 'Tag';\n}\nTag.CheckableTag = CheckableTag;\nexport default Tag;", "import { unit } from '@ant-design/cssinjs';\nimport { resetComponent } from '../../style';\nimport { genStyleHooks } from '../../theme/internal';\nconst genAlertTypeStyle = (bgColor, borderColor, iconColor, token, alertCls) => ({\n  background: bgColor,\n  border: `${unit(token.lineWidth)} ${token.lineType} ${borderColor}`,\n  [`${alertCls}-icon`]: {\n    color: iconColor\n  }\n});\nexport const genBaseStyle = token => {\n  const {\n    componentCls,\n    motionDurationSlow: duration,\n    marginXS,\n    marginSM,\n    fontSize,\n    fontSizeLG,\n    lineHeight,\n    borderRadiusLG: borderRadius,\n    motionEaseInOutCirc,\n    withDescriptionIconSize,\n    colorText,\n    colorTextHeading,\n    withDescriptionPadding,\n    defaultPadding\n  } = token;\n  return {\n    [componentCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      position: 'relative',\n      display: 'flex',\n      alignItems: 'center',\n      padding: defaultPadding,\n      wordWrap: 'break-word',\n      borderRadius,\n      [`&${componentCls}-rtl`]: {\n        direction: 'rtl'\n      },\n      [`${componentCls}-content`]: {\n        flex: 1,\n        minWidth: 0\n      },\n      [`${componentCls}-icon`]: {\n        marginInlineEnd: marginXS,\n        lineHeight: 0\n      },\n      '&-description': {\n        display: 'none',\n        fontSize,\n        lineHeight\n      },\n      '&-message': {\n        color: colorTextHeading\n      },\n      [`&${componentCls}-motion-leave`]: {\n        overflow: 'hidden',\n        opacity: 1,\n        transition: `max-height ${duration} ${motionEaseInOutCirc}, opacity ${duration} ${motionEaseInOutCirc},\n        padding-top ${duration} ${motionEaseInOutCirc}, padding-bottom ${duration} ${motionEaseInOutCirc},\n        margin-bottom ${duration} ${motionEaseInOutCirc}`\n      },\n      [`&${componentCls}-motion-leave-active`]: {\n        maxHeight: 0,\n        marginBottom: '0 !important',\n        paddingTop: 0,\n        paddingBottom: 0,\n        opacity: 0\n      }\n    }),\n    [`${componentCls}-with-description`]: {\n      alignItems: 'flex-start',\n      padding: withDescriptionPadding,\n      [`${componentCls}-icon`]: {\n        marginInlineEnd: marginSM,\n        fontSize: withDescriptionIconSize,\n        lineHeight: 0\n      },\n      [`${componentCls}-message`]: {\n        display: 'block',\n        marginBottom: marginXS,\n        color: colorTextHeading,\n        fontSize: fontSizeLG\n      },\n      [`${componentCls}-description`]: {\n        display: 'block',\n        color: colorText\n      }\n    },\n    [`${componentCls}-banner`]: {\n      marginBottom: 0,\n      border: '0 !important',\n      borderRadius: 0\n    }\n  };\n};\nexport const genTypeStyle = token => {\n  const {\n    componentCls,\n    colorSuccess,\n    colorSuccessBorder,\n    colorSuccessBg,\n    colorWarning,\n    colorWarningBorder,\n    colorWarningBg,\n    colorError,\n    colorErrorBorder,\n    colorErrorBg,\n    colorInfo,\n    colorInfoBorder,\n    colorInfoBg\n  } = token;\n  return {\n    [componentCls]: {\n      '&-success': genAlertTypeStyle(colorSuccessBg, colorSuccessBorder, colorSuccess, token, componentCls),\n      '&-info': genAlertTypeStyle(colorInfoBg, colorInfoBorder, colorInfo, token, componentCls),\n      '&-warning': genAlertTypeStyle(colorWarningBg, colorWarningBorder, colorWarning, token, componentCls),\n      '&-error': Object.assign(Object.assign({}, genAlertTypeStyle(colorErrorBg, colorErrorBorder, colorError, token, componentCls)), {\n        [`${componentCls}-description > pre`]: {\n          margin: 0,\n          padding: 0\n        }\n      })\n    }\n  };\n};\nexport const genActionStyle = token => {\n  const {\n    componentCls,\n    iconCls,\n    motionDurationMid,\n    marginXS,\n    fontSizeIcon,\n    colorIcon,\n    colorIconHover\n  } = token;\n  return {\n    [componentCls]: {\n      '&-action': {\n        marginInlineStart: marginXS\n      },\n      [`${componentCls}-close-icon`]: {\n        marginInlineStart: marginXS,\n        padding: 0,\n        overflow: 'hidden',\n        fontSize: fontSizeIcon,\n        lineHeight: unit(fontSizeIcon),\n        backgroundColor: 'transparent',\n        border: 'none',\n        outline: 'none',\n        cursor: 'pointer',\n        [`${iconCls}-close`]: {\n          color: colorIcon,\n          transition: `color ${motionDurationMid}`,\n          '&:hover': {\n            color: colorIconHover\n          }\n        }\n      },\n      '&-close-text': {\n        color: colorIcon,\n        transition: `color ${motionDurationMid}`,\n        '&:hover': {\n          color: colorIconHover\n        }\n      }\n    }\n  };\n};\nexport const prepareComponentToken = token => {\n  const paddingHorizontal = 12; // Fixed value here.\n  return {\n    withDescriptionIconSize: token.fontSizeHeading3,\n    defaultPadding: `${token.paddingContentVerticalSM}px ${paddingHorizontal}px`,\n    withDescriptionPadding: `${token.paddingMD}px ${token.paddingContentHorizontalLG}px`\n  };\n};\nexport default genStyleHooks('Alert', token => [genBaseStyle(token), genTypeStyle(token), genActionStyle(token)], prepareComponentToken);", "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport CheckCircleFilled from \"@ant-design/icons/es/icons/CheckCircleFilled\";\nimport CloseCircleFilled from \"@ant-design/icons/es/icons/CloseCircleFilled\";\nimport CloseOutlined from \"@ant-design/icons/es/icons/CloseOutlined\";\nimport ExclamationCircleFilled from \"@ant-design/icons/es/icons/ExclamationCircleFilled\";\nimport InfoCircleFilled from \"@ant-design/icons/es/icons/InfoCircleFilled\";\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport { replaceElement } from '../_util/reactNode';\nimport { devUseWarning } from '../_util/warning';\nimport { useComponentConfig } from '../config-provider/context';\nimport useStyle from './style';\nconst iconMapFilled = {\n  success: CheckCircleFilled,\n  info: InfoCircleFilled,\n  error: CloseCircleFilled,\n  warning: ExclamationCircleFilled\n};\nconst IconNode = props => {\n  const {\n    icon,\n    prefixCls,\n    type\n  } = props;\n  const iconType = iconMapFilled[type] || null;\n  if (icon) {\n    return replaceElement(icon, /*#__PURE__*/React.createElement(\"span\", {\n      className: `${prefixCls}-icon`\n    }, icon), () => ({\n      className: classNames(`${prefixCls}-icon`, icon.props.className)\n    }));\n  }\n  return /*#__PURE__*/React.createElement(iconType, {\n    className: `${prefixCls}-icon`\n  });\n};\nconst CloseIconNode = props => {\n  const {\n    isClosable,\n    prefixCls,\n    closeIcon,\n    handleClose,\n    ariaProps\n  } = props;\n  const mergedCloseIcon = closeIcon === true || closeIcon === undefined ? /*#__PURE__*/React.createElement(CloseOutlined, null) : closeIcon;\n  return isClosable ? (/*#__PURE__*/React.createElement(\"button\", Object.assign({\n    type: \"button\",\n    onClick: handleClose,\n    className: `${prefixCls}-close-icon`,\n    tabIndex: 0\n  }, ariaProps), mergedCloseIcon)) : null;\n};\nconst Alert = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      description,\n      prefixCls: customizePrefixCls,\n      message,\n      banner,\n      className,\n      rootClassName,\n      style,\n      onMouseEnter,\n      onMouseLeave,\n      onClick,\n      afterClose,\n      showIcon,\n      closable,\n      closeText,\n      closeIcon,\n      action,\n      id\n    } = props,\n    otherProps = __rest(props, [\"description\", \"prefixCls\", \"message\", \"banner\", \"className\", \"rootClassName\", \"style\", \"onMouseEnter\", \"onMouseLeave\", \"onClick\", \"afterClose\", \"showIcon\", \"closable\", \"closeText\", \"closeIcon\", \"action\", \"id\"]);\n  const [closed, setClosed] = React.useState(false);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Alert');\n    warning.deprecated(!closeText, 'closeText', 'closable.closeIcon');\n  }\n  const internalRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => ({\n    nativeElement: internalRef.current\n  }));\n  const {\n    getPrefixCls,\n    direction,\n    closable: contextClosable,\n    closeIcon: contextCloseIcon,\n    className: contextClassName,\n    style: contextStyle\n  } = useComponentConfig('alert');\n  const prefixCls = getPrefixCls('alert', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const handleClose = e => {\n    var _a;\n    setClosed(true);\n    (_a = props.onClose) === null || _a === void 0 ? void 0 : _a.call(props, e);\n  };\n  const type = React.useMemo(() => {\n    if (props.type !== undefined) {\n      return props.type;\n    }\n    // banner mode defaults to 'warning'\n    return banner ? 'warning' : 'info';\n  }, [props.type, banner]);\n  // closeable when closeText or closeIcon is assigned\n  const isClosable = React.useMemo(() => {\n    if (typeof closable === 'object' && closable.closeIcon) return true;\n    if (closeText) {\n      return true;\n    }\n    if (typeof closable === 'boolean') {\n      return closable;\n    }\n    // should be true when closeIcon is 0 or ''\n    if (closeIcon !== false && closeIcon !== null && closeIcon !== undefined) {\n      return true;\n    }\n    return !!contextClosable;\n  }, [closeText, closeIcon, closable, contextClosable]);\n  // banner mode defaults to Icon\n  const isShowIcon = banner && showIcon === undefined ? true : showIcon;\n  const alertCls = classNames(prefixCls, `${prefixCls}-${type}`, {\n    [`${prefixCls}-with-description`]: !!description,\n    [`${prefixCls}-no-icon`]: !isShowIcon,\n    [`${prefixCls}-banner`]: !!banner,\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, contextClassName, className, rootClassName, cssVarCls, hashId);\n  const restProps = pickAttrs(otherProps, {\n    aria: true,\n    data: true\n  });\n  const mergedCloseIcon = React.useMemo(() => {\n    if (typeof closable === 'object' && closable.closeIcon) {\n      return closable.closeIcon;\n    }\n    if (closeText) {\n      return closeText;\n    }\n    if (closeIcon !== undefined) {\n      return closeIcon;\n    }\n    if (typeof contextClosable === 'object' && contextClosable.closeIcon) {\n      return contextClosable.closeIcon;\n    }\n    return contextCloseIcon;\n  }, [closeIcon, closable, closeText, contextCloseIcon]);\n  const mergedAriaProps = React.useMemo(() => {\n    const merged = closable !== null && closable !== void 0 ? closable : contextClosable;\n    if (typeof merged === 'object') {\n      const {\n          closeIcon: _\n        } = merged,\n        ariaProps = __rest(merged, [\"closeIcon\"]);\n      return ariaProps;\n    }\n    return {};\n  }, [closable, contextClosable]);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(CSSMotion, {\n    visible: !closed,\n    motionName: `${prefixCls}-motion`,\n    motionAppear: false,\n    motionEnter: false,\n    onLeaveStart: node => ({\n      maxHeight: node.offsetHeight\n    }),\n    onLeaveEnd: afterClose\n  }, ({\n    className: motionClassName,\n    style: motionStyle\n  }, setRef) => (/*#__PURE__*/React.createElement(\"div\", Object.assign({\n    id: id,\n    ref: composeRef(internalRef, setRef),\n    \"data-show\": !closed,\n    className: classNames(alertCls, motionClassName),\n    style: Object.assign(Object.assign(Object.assign({}, contextStyle), style), motionStyle),\n    onMouseEnter: onMouseEnter,\n    onMouseLeave: onMouseLeave,\n    onClick: onClick,\n    role: \"alert\"\n  }, restProps), isShowIcon ? (/*#__PURE__*/React.createElement(IconNode, {\n    description: description,\n    icon: props.icon,\n    prefixCls: prefixCls,\n    type: type\n  })) : null, /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-content`\n  }, message ? /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-message`\n  }, message) : null, description ? /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-description`\n  }, description) : null), action ? /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-action`\n  }, action) : null, /*#__PURE__*/React.createElement(CloseIconNode, {\n    isClosable: isClosable,\n    prefixCls: prefixCls,\n    closeIcon: mergedCloseIcon,\n    handleClose: handleClose,\n    ariaProps: mergedAriaProps\n  })))));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Alert.displayName = 'Alert';\n}\nexport default Alert;", "\"use client\";\n\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _callSuper from \"@babel/runtime/helpers/esm/callSuper\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport * as React from 'react';\nimport Alert from './Alert';\nlet ErrorBoundary = /*#__PURE__*/function (_React$Component) {\n  function ErrorBoundary() {\n    var _this;\n    _classCallCheck(this, ErrorBoundary);\n    _this = _callSuper(this, ErrorBoundary, arguments);\n    _this.state = {\n      error: undefined,\n      info: {\n        componentStack: ''\n      }\n    };\n    return _this;\n  }\n  _inherits(ErrorBoundary, _React$Component);\n  return _createClass(ErrorBoundary, [{\n    key: \"componentDidCatch\",\n    value: function componentDidCatch(error, info) {\n      this.setState({\n        error,\n        info\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      const {\n        message,\n        description,\n        id,\n        children\n      } = this.props;\n      const {\n        error,\n        info\n      } = this.state;\n      const componentStack = (info === null || info === void 0 ? void 0 : info.componentStack) || null;\n      const errorMessage = typeof message === 'undefined' ? (error || '').toString() : message;\n      const errorDescription = typeof description === 'undefined' ? componentStack : description;\n      if (error) {\n        return /*#__PURE__*/React.createElement(Alert, {\n          id: id,\n          type: \"error\",\n          message: errorMessage,\n          description: /*#__PURE__*/React.createElement(\"pre\", {\n            style: {\n              fontSize: '0.9em',\n              overflowX: 'auto'\n            }\n          }, errorDescription)\n        });\n      }\n      return children;\n    }\n  }]);\n}(React.Component);\nexport default ErrorBoundary;", "import getPrototypeOf from \"./getPrototypeOf.js\";\nimport isNativeReflectConstruct from \"./isNativeReflectConstruct.js\";\nimport possibleConstructorReturn from \"./possibleConstructorReturn.js\";\nfunction _callSuper(t, o, e) {\n  return o = getPrototypeOf(o), possibleConstructorReturn(t, isNativeReflectConstruct() ? Reflect.construct(o, e || [], getPrototypeOf(t).constructor) : o.apply(t, e));\n}\nexport { _callSuper as default };", "\"use client\";\n\nimport InternalAlert from './Alert';\nimport ErrorBoundary from './ErrorBoundary';\nconst Alert = InternalAlert;\nAlert.ErrorBoundary = ErrorBoundary;\nexport default Alert;"], "names": ["RefreshCcw", "d", "key", "LabsSidebar", "_ref", "isOpen", "labs", "currentLab", "onToggle", "onSelectLab", "isLoading", "React", "className", "<PERSON><PERSON><PERSON>", "title", "onClick", "PanelLeftClose", "strokeWidth", "length", "InfoIcon", "map", "lab", "id", "PanelLeftOpen", "defaultLabs", "type", "ComponentLab", "<PERSON><PERSON>", "message", "description", "props", "ref", "AntdIcon", "A", "icon", "ToolMakerAPI", "_BaseAPI", "_this", "_len", "arguments", "args", "Array", "_key", "call", "apply", "concat", "ws", "_inherits<PERSON><PERSON>e", "_proto", "prototype", "getWebSocketBaseUrl", "url", "baseUrl", "replace", "startsWith", "window", "location", "host", "error", "Error", "connect", "onMessage", "onError", "onClose", "serverUrl", "this", "getBaseUrl", "wsUrl", "protocol", "WebSocket", "onmessage", "event", "data", "JSON", "parse", "e", "onerror", "onclose", "sendDescription", "readyState", "send", "stringify", "close", "BaseAPI", "toolMakerAPI", "ToolMakerLab", "setDescription", "useState", "events", "setEvents", "component", "setComponent", "loading", "setLoading", "setError", "wsRef", "useRef", "handleStart", "current", "msg", "prev", "_toConsumableArray", "_wsRef$current", "_wsRef$current2", "err", "setTimeout", "_wsRef$current3", "example", "idx", "Tag", "handleExampleClick", "Input", "value", "onChange", "target", "placeholder", "onPressEnter", "disabled", "<PERSON><PERSON>", "SendOutlined", "trim", "Spin", "status", "content", "Lab<PERSON><PERSON>nt", "LabsManager", "setIsLoading", "setLabs", "setcurrentLab", "isSidebarOpen", "setIsSidebarOpen", "stored", "localStorage", "getItem", "useEffect", "setItem", "ChevronRight", "Triangle<PERSON><PERSON><PERSON>", "Layout", "meta", "site", "siteMetadata", "link", "style", "height", "prepareToken", "token", "lineWidth", "fontSizeIcon", "calc", "tagFontSize", "fontSizeSM", "tagLineHeight", "lineHeightSM", "mul", "equal", "tagIconSize", "sub", "tagPaddingHorizontal", "tagBorderlessBg", "defaultBg", "prepareComponentToken", "colorFillQuaternary", "onBackground", "colorBgContainer", "toHexString", "defaultColor", "colorText", "paddingXXS", "componentCls", "paddingInline", "iconMarginInline", "Object", "assign", "display", "marginInlineEnd", "marginXS", "fontSize", "lineHeight", "whiteSpace", "background", "border", "lineType", "colorBorder", "borderRadius", "borderRadiusSM", "opacity", "transition", "motionDurationMid", "textAlign", "position", "direction", "color", "marginInlineStart", "colorIcon", "cursor", "colorTextHeading", "borderColor", "iconCls", "colorTextLightSolid", "backgroundColor", "colorPrimary", "colorFillSecondary", "colorPrimaryHover", "colorPrimaryActive", "genBaseStyle", "__rest", "s", "t", "p", "hasOwnProperty", "indexOf", "getOwnPropertySymbols", "i", "propertyIsEnumerable", "CheckableTag", "prefixCls", "customizePrefixCls", "checked", "restProps", "getPrefixCls", "tag", "wrapCSSVar", "hashId", "cssVarCls", "cls", "genPresetColor", "colorKey", "textColor", "lightBorderColor", "lightColor", "darkColor", "genPresetStyle", "genTagStatusStyle", "cssVariableType", "capitalizedCssVariableType", "str", "char<PERSON>t", "toUpperCase", "slice", "tagToken", "InternalTag", "tagProps", "rootClassName", "children", "bordered", "visible", "deprecatedVisible", "tagContext", "setVisible", "domProps", "omit", "undefined", "isPreset", "isStatus", "isInternalColor", "tagStyle", "tagClassName", "handleCloseClick", "stopPropagation", "defaultPrevented", "mergedCloseIcon", "useClosable", "closable", "closeIconRender", "iconNode", "replacement", "originProps", "_a", "isNeedWave", "kids", "tagNode", "genAlertTypeStyle", "bgColor", "iconColor", "alertCls", "motionDurationSlow", "duration", "marginSM", "fontSizeLG", "borderRadiusLG", "motionEaseInOutCirc", "withDescriptionIconSize", "withDescriptionPadding", "defaultPadding", "alignItems", "padding", "wordWrap", "flex", "min<PERSON><PERSON><PERSON>", "overflow", "maxHeight", "marginBottom", "paddingTop", "paddingBottom", "genTypeStyle", "colorSuccess", "colorSuccessBorder", "colorSuccessBg", "colorWarning", "colorWarningBorder", "colorWarningBg", "colorError", "colorErrorBorder", "colorErrorBg", "colorInfo", "colorInfoBorder", "colorInfoBg", "margin", "genActionStyle", "colorIconHover", "outline", "fontSizeHeading3", "paddingContentVerticalSM", "paddingMD", "paddingContentHorizontalLG", "iconMapFilled", "success", "CheckCircleFilled", "info", "InfoCircleFilled", "CloseCircleFilled", "warning", "ExclamationCircleFilled", "IconNode", "iconType", "CloseIconNode", "isClosable", "closeIcon", "handleClose", "ariaProps", "CloseOutlined", "tabIndex", "banner", "onMouseEnter", "onMouseLeave", "afterClose", "showIcon", "closeText", "action", "otherProps", "closed", "setClosed", "internalRef", "nativeElement", "contextClosable", "contextCloseIcon", "contextClassName", "contextStyle", "isShowIcon", "pickAttrs", "aria", "mergedAriaProps", "merged", "_", "motionName", "motionAppear", "motionEnter", "onLeaveStart", "node", "offsetHeight", "onLeaveEnd", "motionClassName", "motionStyle", "setRef", "role", "Error<PERSON>ou<PERSON><PERSON>", "_React$Component", "o", "getPrototypeOf", "possibleConstructorReturn", "isNativeReflectConstruct", "Reflect", "construct", "constructor", "state", "componentStack", "setState", "errorMessage", "toString", "errorDescription", "overflowX"], "sourceRoot": ""}