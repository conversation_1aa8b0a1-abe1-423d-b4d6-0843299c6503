import * as React from "react"
import { Link, HeadFC, PageProps } from "gatsby"

const pageStyles = {
  color: "#232129",
  padding: "96px",
  fontFamily: "-apple-system, Roboto, sans-serif, serif",
}
const headingStyles = {
  marginTop: 0,
  marginBottom: 64,
  maxWidth: 320,
}

const paragraphStyles = {
  marginBottom: 48,
}
const codeStyles = {
  color: "#8A6534",
  padding: 4,
  backgroundColor: "#FFF4DB",
  fontSize: "1.25rem",
  borderRadius: 4,
}

const NotFoundPage: React.FC<PageProps> = () => {
  return (
    <main style={pageStyles}>
      <h1 style={headingStyles}>页面未找到</h1>
      <p style={paragraphStyles}>
        抱歉 😔，我们找不到您要查找的内容。
        <br />
        {process.env.NODE_ENV === "development" ? (
          <>
            <br />
            尝试在 <code style={codeStyles}>src/pages/</code> 中创建页面。
            <br />
          </>
        ) : null}
        <br />
        <Link to="/">返回首页</Link>。
      </p>
    </main>
  )
}

export default NotFoundPage

export const Head: HeadFC = () => <title>页面未找到</title>
