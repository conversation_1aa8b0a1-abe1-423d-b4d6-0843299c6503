{"version": 3, "file": "component---src-pages-login-tsx-9de13b3861f00f5d33fc.js", "mappings": "oMAQA,MAAM,MAAEA,EAAK,KAAEC,GAASC,EAAAA,EAwExB,UAnEkBC,IAAmB,IAAlB,KAAEC,GAAWD,EAC9B,MAAM,gBAAEE,EAAe,UAAEC,EAAS,SAAEC,IAAaC,EAAAA,EAAAA,KAkBjD,OAhBAC,EAAAA,EAAAA,WAAU,KAEJJ,IAAoBC,IACtBI,EAAAA,EAAAA,UAAS,MAEV,CAACL,EAAiBC,KAGrBG,EAAAA,EAAAA,WAAU,KACS,SAAbF,GAAwBD,IAC1BI,EAAAA,EAAAA,UAAS,MAEV,CAACH,EAAUD,IAIVA,EAEAK,EAAAA,cAACC,EAAAA,EAAM,CAACC,KAAMT,EAAKU,KAAKC,aAAcC,MAAM,QAAQC,KAAK,UACvDN,EAAAA,cAAA,OAAKO,UAAU,6CACbP,EAAAA,cAACQ,EAAAA,EAAI,CAACC,KAAK,QAAQC,IAAI,aAO7BV,EAAAA,cAACC,EAAAA,EAAM,CACLC,KAAMT,EAAKU,KAAKC,aAChBC,MAAM,KACNC,KAAK,SACLK,YAAY,EACZC,YAAY,GAEZZ,EAAAA,cAAA,OAAKO,UAAU,0DACbP,EAAAA,cAAA,OAAKO,UAAU,uDACbP,EAAAA,cAAA,OAAKO,UAAU,oBACbP,EAAAA,cAAA,OAAKO,UAAU,QACbP,EAAAA,cAACa,EAAAA,EAAI,CAACC,KAAK,MAAML,KAAM,MAEzBT,EAAAA,cAAA,OAAKO,UAAU,4CAA2C,OACnDd,EAAKU,KAAKC,aAAaC,OAE9BL,EAAAA,cAAA,OAAKO,UAAU,0BAAyB,wB", "sources": ["webpack://autogentstudio/./src/pages/login.tsx"], "sourcesContent": ["import React, { useEffect } from \"react\";\r\nimport { useAuth } from \"../auth/context\";\r\nimport { navigate } from \"gatsby\";\r\nimport { Typography, Spin } from \"antd\";\r\nimport Layout from \"../components/layout\";\r\nimport { graphql } from \"gatsby\";\r\nimport Icon from \"../components/icons\";\r\n\r\nconst { Title, Text } = Typography;\r\n\r\n// Use the same token key as in context.tsx\r\nconst TOKEN_KEY = \"auth_token\";\r\n\r\nconst LoginPage = ({ data }: any) => {\r\n  const { isAuthenticated, isLoading, authType } = useAuth();\r\n\r\n  useEffect(() => {\r\n    // If user is already authenticated, redirect to home\r\n    if (isAuthenticated && !isLoading) {\r\n      navigate(\"/\");\r\n    }\r\n  }, [isAuthenticated, isLoading]);\r\n\r\n  // If auth type is 'none', redirect to home\r\n  useEffect(() => {\r\n    if (authType === \"none\" && !isLoading) {\r\n      navigate(\"/\");\r\n    }\r\n  }, [authType, isLoading]);\r\n\r\n\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <Layout meta={data.site.siteMetadata} title=\"Login\" link=\"/login\">\r\n        <div className=\"flex items-center justify-center h-screen\">\r\n          <Spin size=\"large\" tip=\"加载中...\" />\r\n        </div>\r\n      </Layout>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Layout\r\n      meta={data.site.siteMetadata}\r\n      title=\"登录\"\r\n      link=\"/login\"\r\n      showHeader={true}\r\n      restricted={false}\r\n    >\r\n      <div className=\"flex items-center justify-center h-[calc(100vh-164px)]\">\r\n        <div className=\"w-full rounded bg-secondary max-w-md p-8 sxhadow-sm\">\r\n          <div className=\"text-center mb-8\">\r\n            <div className=\"mb-3\">\r\n              <Icon icon=\"app\" size={12} />\r\n            </div>\r\n            <div className=\"text-2xl mb-1 font-semibold text-primary\">\r\n              登录到 {data.site.siteMetadata.title}\r\n            </div>\r\n            <div className=\"text-secondary text-sm\">\r\n              请使用token登录或联系管理员\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </Layout>\r\n  );\r\n};\r\n\r\nexport const query = graphql`\r\n  query LoginPageQuery {\r\n    site {\r\n      siteMetadata {\r\n        description\r\n        title\r\n      }\r\n    }\r\n  }\r\n`;\r\n\r\nexport default LoginPage;\r\n"], "names": ["Title", "Text", "Typography", "_ref", "data", "isAuthenticated", "isLoading", "authType", "useAuth", "useEffect", "navigate", "React", "Layout", "meta", "site", "siteMetadata", "title", "link", "className", "Spin", "size", "tip", "showHeader", "restricted", "Icon", "icon"], "sourceRoot": ""}