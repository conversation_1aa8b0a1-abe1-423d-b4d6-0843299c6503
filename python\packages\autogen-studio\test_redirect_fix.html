<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重定向修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 700px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="url"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button.success {
            background-color: #28a745;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        .note {
            background: #f8f9fa;
            padding: 15px;
            border-left: 4px solid #007bff;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 重定向修复测试</h1>
        <p>测试修复后的POST登录重定向功能</p>
        
        <div class="note">
            <strong>修复内容：</strong>
            <ul>
                <li>✅ 使用window.location.origin确保正确的重定向URL</li>
                <li>✅ 添加了手动跳转链接作为备选</li>
                <li>✅ 改进了调试日志</li>
                <li>✅ 延长了自动跳转时间到3秒</li>
            </ul>
        </div>
        
        <div class="form-group">
            <label for="serverUrl">服务器地址:</label>
            <input type="url" id="serverUrl" value="http://127.0.0.1:8081">
        </div>
        
        <div class="form-group">
            <label for="token">JWT Token:</label>
            <input type="text" id="token" placeholder="输入您的JWT token">
        </div>
        
        <button onclick="testFixedRedirect()">测试修复后的登录</button>
        <button onclick="testDirectRedirect()" class="success">测试直接重定向</button>
        <button onclick="fillRealToken()">填入真实Token</button>
        
        <div id="result"></div>
    </div>

    <script>
        // 使用您提供的真实token
        const REAL_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************.bnwZX2RbtQiPy2qX7nWhKKtKFAgCfoXu0QdLjK7Hybs';

        function fillRealToken() {
            document.getElementById('token').value = REAL_TOKEN;
            showResult('✅ 已填入真实Token', 'success');
        }

        async function testFixedRedirect() {
            const serverUrl = document.getElementById('serverUrl').value.trim();
            const token = document.getElementById('token').value.trim();
            
            if (!serverUrl || !token) {
                showResult('请输入服务器地址和token', 'error');
                return;
            }

            try {
                showResult('🔄 正在测试修复后的登录重定向...', 'info');

                const response = await fetch(serverUrl + '/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
                    },
                    body: 'token=' + encodeURIComponent(token)
                });

                if (response.ok) {
                    const html = await response.text();
                    
                    showResult(`✅ 登录请求成功！

服务器返回了HTML页面 (${html.length} 字符)
正在新窗口中打开...

预期行为：
1. 显示登录成功页面
2. 自动存储token到localStorage
3. 3秒后自动重定向到 ${serverUrl}
4. 如果自动重定向失败，5秒后显示手动跳转链接

请观察新窗口的行为`, 'success');

                    // 在新窗口中显示HTML
                    const newWindow = window.open('', '_blank');
                    newWindow.document.write(html);
                    newWindow.document.close();
                    
                    // 监听新窗口的变化
                    let checkCount = 0;
                    const checkInterval = setInterval(() => {
                        checkCount++;
                        try {
                            if (newWindow.closed) {
                                clearInterval(checkInterval);
                                showResult(`ℹ️ 新窗口已关闭

如果重定向成功，您应该看到：
1. 新窗口跳转到了 ${serverUrl}
2. 主应用显示正确的用户信息（admin）
3. 用户状态为已登录`, 'info');
                            } else if (checkCount > 20) {
                                clearInterval(checkInterval);
                                showResult(`⚠️ 检查超时

请手动检查新窗口是否正确重定向到主应用`, 'warning');
                            }
                        } catch (e) {
                            // 跨域访问限制，忽略错误
                        }
                    }, 1000);
                    
                } else {
                    const errorText = await response.text();
                    showResult(`❌ 登录请求失败:

状态码: ${response.status}
响应: ${errorText}`, 'error');
                }
            } catch (error) {
                showResult(`❌ 请求异常: ${error.message}`, 'error');
            }
        }

        function testDirectRedirect() {
            const serverUrl = document.getElementById('serverUrl').value.trim();
            const token = document.getElementById('token').value.trim();
            
            if (!serverUrl || !token) {
                showResult('请输入服务器地址和token', 'error');
                return;
            }

            showResult(`🔄 正在测试直接重定向...

这种方式会：
1. 先存储token到当前页面的localStorage
2. 直接跳转到主应用
3. 主应用应该能检测到token并显示已登录状态

请等待跳转...`, 'info');

            // 直接存储token并跳转
            localStorage.setItem('auth_token', token);
            
            setTimeout(() => {
                window.open(serverUrl, '_blank');
                showResult(`✅ 已存储token并打开主应用

Token已存储到当前页面的localStorage
主应用已在新标签页打开

注意：如果主应用仍显示未登录，可能是localStorage域隔离问题`, 'success');
            }, 1000);
        }

        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = `result ${type}`;
        }

        // 页面加载时自动填入真实token
        window.onload = function() {
            fillRealToken();
        };
    </script>
</body>
</html>
