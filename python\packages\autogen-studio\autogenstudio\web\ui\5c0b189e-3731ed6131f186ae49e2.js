"use strict";(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[348],{8689:function(e,n,t){var r;t.d(n,{Zx:function(){return D},bv:function(){return L},vW:function(){return F},we:function(){return A}});var o=t(6540),l=t(6984),c=t(7193),u=t(6635),s=t(961),f=t(4743),i=t(6885);const a={...r||(r=t.t(o,2))},g=a.useInsertionEffect||(e=>e());function d(e){const n=o.useRef(()=>{0});return g(()=>{n.current=e}),o.useCallback(function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return null==n.current?void 0:n.current(...t)},[])}const m="ArrowUp",p="ArrowDown",v="ArrowLeft",h="ArrowRight";var R="undefined"!=typeof document?o.useLayoutEffect:o.useEffect;const w=[v,h],C=[m,p];let b=!1,x=0;const T=()=>"floating-ui-"+Math.random().toString(36).slice(2,6)+x++;const y=a.useId||function(){const[e,n]=o.useState(()=>b?T():void 0);return R(()=>{null==e&&n(T())},[]),o.useEffect(()=>{b=!0},[]),e};function M(){const e=new Map;return{emit(n,t){var r;null==(r=e.get(n))||r.forEach(e=>e(t))},on(n,t){e.set(n,[...e.get(n)||[],t])},off(n,t){var r;e.set(n,(null==(r=e.get(n))?void 0:r.filter(e=>e!==t))||[])}}}const H=o.createContext(null),k=o.createContext(null),E=()=>{var e;return(null==(e=o.useContext(H))?void 0:e.id)||null},S=()=>o.useContext(k);const I="data-floating-ui-focusable";function A(e){void 0===e&&(e={});const{nodeId:n}=e,t=function(e){const{open:n=!1,onOpenChange:t,elements:r}=e,l=y(),c=o.useRef({}),[u]=o.useState(()=>M()),s=null!=E(),[f,i]=o.useState(r.reference),a=d((e,n,r)=>{c.current.openEvent=e?n:void 0,u.emit("openchange",{open:e,event:n,reason:r,nested:s}),null==t||t(e,n,r)}),g=o.useMemo(()=>({setPositionReference:i}),[]),m=o.useMemo(()=>({reference:f||r.reference||null,floating:r.floating||null,domReference:r.reference}),[f,r.reference,r.floating]);return o.useMemo(()=>({dataRef:c,open:n,onOpenChange:a,elements:m,events:u,floatingId:l,refs:g}),[n,a,m,u,l,g])}({...e,elements:{reference:null,floating:null,...e.elements}}),r=e.rootContext||t,l=r.elements,[c,s]=o.useState(null),[i,a]=o.useState(null),g=(null==l?void 0:l.domReference)||c,m=o.useRef(null),p=S();R(()=>{g&&(m.current=g)},[g]);const v=(0,f.we)({...e,elements:{...l,...i&&{reference:i}}}),h=o.useCallback(e=>{const n=(0,u.vq)(e)?{getBoundingClientRect:()=>e.getBoundingClientRect(),contextElement:e}:e;a(n),v.refs.setReference(n)},[v.refs]),w=o.useCallback(e=>{((0,u.vq)(e)||null===e)&&(m.current=e,s(e)),((0,u.vq)(v.refs.reference.current)||null===v.refs.reference.current||null!==e&&!(0,u.vq)(e))&&v.refs.setReference(e)},[v.refs]),C=o.useMemo(()=>({...v.refs,setReference:w,setPositionReference:h,domReference:m}),[v.refs,w,h]),b=o.useMemo(()=>({...v.elements,domReference:g}),[v.elements,g]),x=o.useMemo(()=>({...v,...r,refs:C,elements:b,nodeId:n}),[v,C,b,n,r]);return R(()=>{r.dataRef.current.floatingContext=x;const e=null==p?void 0:p.nodesRef.current.find(e=>e.id===n);e&&(e.context=x)}),o.useMemo(()=>({...v,context:x,refs:C,elements:b}),[v,C,b,x])}const _="active",q="selected";function P(e,n,t){const r=new Map,o="item"===t;let l=e;if(o&&e){const{[_]:n,[q]:t,...r}=e;l=r}return{..."floating"===t&&{tabIndex:-1,[I]:""},...l,...n.map(n=>{const r=n?n[t]:null;return"function"==typeof r?e?r(e):null:r}).concat(e).reduce((e,n)=>n?(Object.entries(n).forEach(n=>{let[t,l]=n;var c;o&&[_,q].includes(t)||(0===t.indexOf("on")?(r.has(t)||r.set(t,[]),"function"==typeof l&&(null==(c=r.get(t))||c.push(l),e[t]=function(){for(var e,n=arguments.length,o=new Array(n),l=0;l<n;l++)o[l]=arguments[l];return null==(e=r.get(t))?void 0:e.map(e=>e(...o)).find(e=>void 0!==e)})):e[t]=l)}),e):e,{})}}function L(e){void 0===e&&(e=[]);const n=e.map(e=>null==e?void 0:e.reference),t=e.map(e=>null==e?void 0:e.floating),r=e.map(e=>null==e?void 0:e.item),l=o.useCallback(n=>P(n,e,"reference"),n),c=o.useCallback(n=>P(n,e,"floating"),t),u=o.useCallback(n=>P(n,e,"item"),r);return o.useMemo(()=>({getReferenceProps:l,getFloatingProps:c,getItemProps:u}),[l,c,u])}function O(e,n){return{...e,rects:{...e.rects,floating:{...e.rects.floating,height:n}}}}const F=e=>({name:"inner",options:e,async fn(n){const{listRef:t,overflowRef:r,onFallbackChange:o,offset:l=0,index:u=0,minItemsVisible:a=4,referenceOverflowThreshold:g=0,scrollRef:d,...m}=(0,c._3)(e,n),{rects:p,elements:{floating:v}}=n,h=t.current[u],R=(null==d?void 0:d.current)||v,w=v.clientTop||R.clientTop,C=0!==v.clientTop,b=0!==R.clientTop,x=v===R;if(!h)return{};const T={...n,...await(0,f.cY)(-h.offsetTop-v.clientTop-p.reference.height/2-h.offsetHeight/2-l).fn(n)},y=await(0,i.__)(O(T,R.scrollHeight+w+v.clientTop),m),M=await(0,i.__)(T,{...m,elementContext:"reference"}),H=(0,c.T9)(0,y.top),k=T.y+H,E=(R.scrollHeight>R.clientHeight?e=>e:c.LI)((0,c.T9)(0,R.scrollHeight+(C&&x||b?2*w:0)-H-(0,c.T9)(0,y.bottom)));if(R.style.maxHeight=E+"px",R.scrollTop=H,o){const e=R.offsetHeight<h.offsetHeight*(0,c.jk)(a,t.current.length)-1||M.top>=-g||M.bottom>=-g;s.flushSync(()=>o(e))}return r&&(r.current=await(0,i.__)(O({...T,y:k},R.offsetHeight+w+v.clientTop),m)),{y:k}}});function D(e,n){const{open:t,elements:r}=e,{enabled:c=!0,overflowRef:u,scrollRef:f,onChange:i}=n,a=d(i),g=o.useRef(!1),m=o.useRef(null),p=o.useRef(null);o.useEffect(()=>{if(!c)return;function e(e){if(e.ctrlKey||!n||null==u.current)return;const t=e.deltaY,r=u.current.top>=-.5,o=u.current.bottom>=-.5,c=n.scrollHeight-n.clientHeight,f=t<0?-1:1,i=t<0?"max":"min";n.scrollHeight<=n.clientHeight||(!r&&t>0||!o&&t<0?(e.preventDefault(),s.flushSync(()=>{a(e=>e+Math[i](t,c*f))})):/firefox/i.test((0,l.$t)())&&(n.scrollTop+=t))}const n=(null==f?void 0:f.current)||r.floating;return t&&n?(n.addEventListener("wheel",e),requestAnimationFrame(()=>{m.current=n.scrollTop,null!=u.current&&(p.current={...u.current})}),()=>{m.current=null,p.current=null,n.removeEventListener("wheel",e)}):void 0},[c,t,r.floating,u,f,a]);const v=o.useMemo(()=>({onKeyDown(){g.current=!0},onWheel(){g.current=!1},onPointerMove(){g.current=!1},onScroll(){const e=(null==f?void 0:f.current)||r.floating;if(u.current&&e&&g.current){if(null!==m.current){const n=e.scrollTop-m.current;(u.current.bottom<-.5&&n<-1||u.current.top<-.5&&n>1)&&s.flushSync(()=>a(e=>e+n))}requestAnimationFrame(()=>{m.current=e.scrollTop})}}}),[r.floating,a,u,f]);return o.useMemo(()=>c?{floating:v}:{},[c,v])}}}]);
//# sourceMappingURL=5c0b189e-3731ed6131f186ae49e2.js.map