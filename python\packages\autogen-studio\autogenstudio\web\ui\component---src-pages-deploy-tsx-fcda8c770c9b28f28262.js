/*! For license information please see component---src-pages-deploy-tsx-fcda8c770c9b28f28262.js.LICENSE.txt */
"use strict";(self.webpackChunkautogentstudio=self.webpackChunkautogentstudio||[]).push([[362],{418:function(e,t,n){n.d(t,{A:function(){return a}});const a=(0,n(1788).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},4060:function(e,t,n){n.d(t,{A:function(){return a}});const a=(0,n(1788).A)("RefreshCcw",[["path",{d:"M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"14sxne"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16",key:"1hlbsb"}],["path",{d:"M16 16h5v5",key:"ccwih5"}]])},5404:function(e,t,n){n.d(t,{A:function(){return a}});const a=(0,n(1788).A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},6389:function(e,t,n){n.r(t),n.d(t,{default:function(){return A}});var a=n(6540),r=n(1155),o=n(7677),c=n(418),l=n(367),s=n(9910),i=n(9644),m=n(4060),d=n(7213);const u=e=>{let{isOpen:t,guides:n,currentGuide:r,onToggle:o,onSelectGuide:c,isLoading:u=!1}=e;return t?a.createElement("div",{className:"h-full border-r border-secondary"},a.createElement("div",{className:"flex items-center justify-between pt-0 p-4 pl-2 pr-2 border-b border-secondary"},a.createElement("div",{className:"flex items-center gap-2"},a.createElement("span",{className:"text-primary font-medium"},"指南")),a.createElement(l.A,{title:"关闭侧边栏"},a.createElement("button",{onClick:o,className:"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50"},a.createElement(i.A,{strokeWidth:1.5,className:"h-6 w-6"})))),u&&a.createElement("div",{className:"p-4"},a.createElement(m.A,{className:"w-4 h-4 inline-block animate-spin"})),!u&&0===n.length&&a.createElement("div",{className:"p-2 m-2 text-center text-secondary text-sm border border-dashed rounded"},a.createElement(d.A,{className:"w-4 h-4 inline-block mr-1.5 -mt-0.5"}),"无可用的部署指南"),a.createElement("div",{className:"overflow-y-auto h-[calc(100%-64px)] mt-4"},n.map(e=>a.createElement("div",{key:e.id,className:"relative"},a.createElement("div",{className:"absolute top-1 left-0.5 z-50 h-[calc(100%-8px)]\n               w-1 bg-opacity-80 rounded "+((null==r?void 0:r.id)===e.id?"bg-accent":"bg-tertiary")}),a.createElement("div",{className:"group ml-1 flex flex-col p-2 rounded-l cursor-pointer hover:bg-secondary "+((null==r?void 0:r.id)===e.id?"border-accent bg-secondary":"border-transparent"),onClick:()=>c(e)},a.createElement("div",{className:"flex items-center justify-between"},a.createElement("span",{className:"text-sm truncate"},e.title))))))):a.createElement("div",{className:"h-full border-r border-secondary"},a.createElement("div",{className:"p-2 -ml-2"},a.createElement(l.A,{title:"文档"},a.createElement("button",{onClick:o,className:"p-2 rounded-md hover:bg-secondary hover:text-accent text-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-opacity-50"},a.createElement(s.A,{strokeWidth:1.5,className:"h-6 w-6"})))))},p=[{id:"python-setup",title:"Python",type:"python"},{id:"docker-setup",title:"Docker",type:"docker"}];var h=n(5404),g=n(7260),f=n(8309);var E=()=>a.createElement("div",{className:""},a.createElement("h1",{className:"tdext-2xl font-bold mb-6"},"在 Python 代码和 REST API 中使用 多智能体工作室 团队"),a.createElement(g.A,{className:"mb-6",message:"前置条件",description:a.createElement("ul",{className:"list-disc pl-4 mt-2 space-y-1"},a.createElement("li",null,"已安装 多智能体工作室")),type:"info"}),a.createElement("div",{className:"my-3 text-sm"}," ","您可以通过使用 TeamManager 类在您的 Python 应用程序中重用在 多智能体工作室 中创建的代理团队的声明性规范。在团队构建器中，选择一个团队配置并点击下载。"," ",a.createElement(f.A,{className:"h-4 w-4 inline-block"})," "),a.createElement(k,{title:"1. 在 Python 中构建您的团队，导出为 JSON",description:"这是一个在 Python 中构建代理团队并将其导出为 JSON 文件的示例。",code:'\nfrom autogen_agentchat.agents import AssistantAgent\nfrom autogen_agentchat.teams import RoundRobinGroupChat\nfrom autogen_agentchat.ui import Console\nfrom autogen_ext.models.openai import OpenAIChatCompletionClient\nfrom autogen_agentchat.conditions import  TextMentionTermination\n \nagent = AssistantAgent(\n        name="weather_agent",\n        model_client=OpenAIChatCompletionClient(\n            model="gpt-4o-mini", \n        ), \n    ) \nagent_team = RoundRobinGroupChat([agent], termination_condition=TextMentionTermination("TERMINATE"))\nconfig = agent_team.dump_component()\nprint(config.model_dump_json())',onCopy:N}),a.createElement("div",{className:"space-y-6"},a.createElement(k,{title:"2. 在 Python 中运行团队",description:"这是在您的 Python 代码中使用 多智能体工作室 的 TeamManager 类的简单示例。",code:'\nfrom autogenstudio.teammanager import TeamManager\n\n# Initialize the TeamManager\nmanager = TeamManager()\n\n# Run a task with a specific team configuration\nresult = await manager.run(\ntask="What is the weather in New York?",\nteam_config="team.json"\n)\nprint(result)',onCopy:N}),a.createElement(k,{title:"3. 将团队作为 REST API 提供服务",description:a.createElement("div",null,"多智能体工作室 提供了一个便捷的 CLI 命令，可以将团队作为 REST API 端点提供服务。"," "),code:"\nautogenstudio serve --team path/to/team.json --port 8084  \n          ",onCopy:N})));var y=()=>a.createElement("div",{className:"max-w-4xl"},a.createElement("h1",{className:"tdext-2xl font-bold mb-6"},"Docker 容器设置"),a.createElement(g.A,{className:"mb-6",message:"前置条件",description:a.createElement("ul",{className:"list-disc pl-4 mt-2 space-y-1"},a.createElement("li",null,"系统上已安装 Docker")),type:"info"}),a.createElement(k,{title:"1. Dockerfile",description:a.createElement("div",null,"多智能体工作室 提供了一个",a.createElement("a",{href:"https://github.com/microsoft/autogen/blob/main/python/packages/autogen-studio/Dockerfile",target:"_blank",rel:"noreferrer",className:"text-accent underline px-1"},"Dockerfile"),"，您可以使用它来构建您的 Docker 容器。"," "),code:'FROM python:3.10-slim\n\nWORKDIR /code\n\nRUN pip install -U gunicorn autogenstudio\n\nRUN useradd -m -u 1000 user\nUSER user\nENV HOME=/home/<USER>/home/<USER>/.local/bin:$PATH \n    AUTOGENSTUDIO_APPDIR=/home/<USER>/app\n\nWORKDIR $HOME/app\n\nCOPY --chown=user . $HOME/app\n\nCMD gunicorn -w $((2 * $(getconf _NPROCESSORS_ONLN) + 1)) --timeout 12600 -k uvicorn.workers.UvicornWorker autogenstudio.web.app:app --bind "0.0.0.0:8081"',onCopy:N}),a.createElement(k,{title:"2. 构建和运行",description:"构建并运行您的 Docker 容器：",code:"docker build -t autogenstudio .\ndocker run -p 8081:8081 autogenstudio",onCopy:N})),b=n(9872);const N=e=>{navigator.clipboard.writeText(e)},v=e=>{let{guide:t}=e;switch(t.id){case"python-setup":return a.createElement(E,null);case"docker-setup":return a.createElement(y,null);default:return a.createElement("div",{className:"text-secondary"},"标题为 ",a.createElement("strong",null,t.title)," 的指南正在开发中！")}},x=a.createRef(),k=e=>{let{title:t,description:n,code:r,onCopy:o,language:c="python"}=e;return a.createElement("section",{className:"mt-6 bg-seco"},a.createElement("h2",{className:"text-md font-semibold mb-3"},t),n&&a.createElement("div",{className:"  mb-3"},n),r&&a.createElement("div",{className:"relative bg-secondary text-sm p-4 rounded overflow-auto scroll h-72"},a.createElement(b.T,{language:c,editorRef:x,value:r}),a.createElement("button",{onClick:()=>o(r),className:"absolute right-2 top-2 p-2  bg-secondary hover:bg-primary rounded-md"},a.createElement(h.A,{className:"w-4 h-4 hover:text-accent transition duration-100"}))))};var w=()=>{const{0:e,1:t}=(0,a.useState)(!1),{0:n,1:r}=(0,a.useState)(p),{0:l,1:s}=(0,a.useState)(null),{0:i,1:m}=(0,a.useState)(()=>{if("undefined"!=typeof window){const e=localStorage.getItem("deploySidebar");return null===e||JSON.parse(e)}return!0});return(0,a.useEffect)(()=>{"undefined"!=typeof window&&localStorage.setItem("deploySidebar",JSON.stringify(i))},[i]),(0,a.useEffect)(()=>{!l&&n.length>0&&s(n[0])},[n,l]),a.createElement("div",{className:"relative    flex h-full w-full"},a.createElement("div",{className:"absolute  left-0 top-0 h-full transition-all duration-200 ease-in-out "+(i?"w-64":"w-12")},a.createElement(u,{isOpen:i,guides:n,currentGuide:l,onToggle:()=>m(!i),onSelectGuide:s,isLoading:e})),a.createElement("div",{className:"flex-1 transition-all max-w-5xl  -mr-6 duration-200 "+(i?"ml-64":"ml-12")},a.createElement("div",{className:"p-4 pt-2"},a.createElement("div",{className:"flex items-center gap-2 mb-4 text-sm"},a.createElement("span",{className:"text-primary font-medium"},"部署"),l&&a.createElement(a.Fragment,null,a.createElement(o.A,{className:"w-4 h-4 text-secondary"}),a.createElement("span",{className:"text-secondary"},l.title))),a.createElement("div",{className:"rounded border border-secondary border-dashed p-2 text-sm mb-4"},a.createElement(c.A,{className:"w-4 h-4 inline-block mr-2 -mt-1 text-secondary "})," ","部署指南部分正在开发中。"),l?a.createElement(v,{guide:l}):a.createElement("div",{className:"flex items-center justify-center h-[calc(100vh-190px)] text-secondary"},"从侧边栏选择一个指南开始"))))};var A=e=>{let{data:t}=e;return a.createElement(r.A,{meta:t.site.siteMetadata,title:"部署",link:"/deploy"},a.createElement("main",{style:{height:"100%"},className:" h-full "},a.createElement(w,null)))}},7213:function(e,t,n){n.d(t,{A:function(){return a}});const a=(0,n(1788).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},7677:function(e,t,n){n.d(t,{A:function(){return a}});const a=(0,n(1788).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])}}]);
//# sourceMappingURL=component---src-pages-deploy-tsx-fcda8c770c9b28f28262.js.map