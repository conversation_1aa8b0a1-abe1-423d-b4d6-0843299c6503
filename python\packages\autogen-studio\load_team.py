import asyncio
import json
import os
from pathlib import Path
from typing import Any, Dict, Optional, Union

from autogen_agentchat.teams import RoundRobinGroupChat





def create_team_from_config(config: Dict[str, Any]):
    """Create team instance from configuration dictionary"""
    try:
        # Use AutoGen's component loading system
        team = RoundRobinGroupChat.load_component(config)
        return team
    except Exception as e:
        print(f"Failed to create team: {e}")
        raise


async def load_and_run_team(
    team_config_path: str, 
    task: str,
    api_key: Optional[str] = None,
    base_url: Optional[str] = None
) -> Any:
    """Complete workflow: load team config and run task"""
    
    # Set environment variables if provided
    if api_key:
        os.environ["OPENAI_API_KEY"] = api_key
    if base_url:
        os.environ["OPENAI_BASE_URL"] = base_url
    
    # Load team configuration
    config = await load_team_from_file(team_config_path)
    print(f"Loaded team config from {team_config_path}")
    
    # Create team instance
    team = create_team_from_config(config)
    print(f"Created team instance: {type(team).__name__}")
    
    # Run the task
    print(f"Running task: {task}")
    result = await team.run(task=task)
    
    return result


async def main(stream=False):
    """Main execution function"""
    # Set API credentials
    os.environ["OPENAI_API_KEY"] = "sk-aOnHdMDqN5SXgxwAC50298EbCcA54345B906AfC7F5D7B384"
    os.environ["OPENAI_BASE_URL"] = "https://gnomic.nengyongai.cn/v1"
    
    try:
        # Load team configuration
        config = await load_team_from_file("test_team.json")
        
        # Create team instance
        team = create_team_from_config(config)
        if not stream:
            # Run task
            result = await team.run(task="Write a short poem about the fall season.")
            print("Task completed successfully!")
            print("Result:", result)
        else:
            stream = team.run_stream(task="Write a long poem about the fall season.")
            async for message in stream:
                print(message)
    except FileNotFoundError:
        print("Error: test_team.json not found. Please ensure the team config file exists.")
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    asyncio.run(main(True))
