{"version": 3, "file": "911de66654c883e5fa7bf2156c1eabe51c084373-cad302fe78d3900833f8.js", "mappings": "sLAEA,EADmB,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,SAAW,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,uZAA2Z,KAAQ,OAAQ,MAAS,Y,UCM1kB,EAAe,SAAsBA,EAAOC,GAC9C,OAAoB,gBAAoBC,EAAAC,GAAU,OAAS,CAAC,EAAGH,EAAO,CACpEC,IAAKA,EACLG,KAAM,IAEV,EAOA,MAJ2B,aAAiB,G,uFCdxCC,EAAqB,SAA4BC,GACnD,IAAI,EAAAC,EAAA,MAAeC,OAAOC,SAASC,gBAAiB,CAClD,IAAIC,EAAgBC,MAAMC,QAAQP,GAAaA,EAAY,CAACA,GACxDI,EAAkBF,OAAOC,SAASC,gBACtC,OAAOC,EAAcG,KAAK,SAAUC,GAClC,OAAOA,KAAQL,EAAgBM,KACjC,EACF,CACA,OAAO,CACT,EAUO,SAASC,EAAeX,EAAWY,GACxC,OAAKN,MAAMC,QAAQP,SAA6Ba,IAAfD,EAG1Bb,EAAmBC,GAbF,SAA6BA,EAAWc,GAChE,IAAKf,EAAmBC,GACtB,OAAO,EAET,IAAIe,EAAMZ,SAASa,cAAc,OAC7BC,EAASF,EAAIL,MAAMV,GAEvB,OADAe,EAAIL,MAAMV,GAAac,EAChBC,EAAIL,MAAMV,KAAeiB,CAClC,CAGWC,CAAoBlB,EAAWY,EAG1C,C,iCCvBA,EADoB,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,SAAW,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,iLAAqL,KAAQ,QAAS,MAAS,YCMtW,EAAgB,SAAuBlB,EAAOC,GAChD,OAAoB,gBAAoBC,EAAAC,GAAU,OAAS,CAAC,EAAGH,EAAO,CACpEC,IAAKA,EACLG,KAAM,IAEV,EAOA,MAJ2B,aAAiB,G,qECH5C,MAaaqB,EAAiBC,IAC5B,MACMC,EAAS,CAAC,EAShB,MAViB,CAAC,EAAG,EAAG,EAAG,EAAG,GAErBC,QAAQC,IACfF,EAAO,YACFE,oBACKA,mBACAA,yBACLA,WArBa,EAACC,EAAUC,EAAYC,EAAON,KAClD,MAAM,kBACJO,EAAiB,iBACjBC,GACER,EACJ,MAAO,CACLS,aAAcF,EACdD,QACAI,WAAYF,EACZJ,WACAC,eAYKM,CAAcX,EAAM,kBAAkBG,KAAiBH,EAAM,oBAAoBG,KAAiBH,EAAMY,iBAAkBZ,KAE1HC,GAEIY,EAAgBb,IAC3B,MAAM,aACJc,GACEd,EACJ,MAAO,CACL,QAASe,OAAOC,OAAOD,OAAOC,OAAO,CAAC,GAAG,QAAchB,IAAS,CAC9DiB,WAAY,OACZ,CAAC,iBAAiBH,cAA0B,CAC1CR,MAAON,EAAMkB,kBACbC,OAAQ,cACR,oBAAqB,CACnBb,MAAON,EAAMkB,mBAEf,WAAY,CACVE,cAAe,aAMZC,EAAiBrB,IAAS,CACrCsB,KAAM,CACJC,OAAQ,UACRC,cAAe,QACfC,aAAc,cACdrB,SAAU,MACVsB,WAAY1B,EAAM2B,eAClBC,WAAY,2BACZC,OAAQ,qCACRC,aAAc,GAEhBC,IAAK,CACHR,OAAQ,UACRC,cAAe,QACfC,aAAc,eACdrB,SAAU,MACVsB,WAAY1B,EAAM2B,eAClBC,WAAY,4BACZC,OAAQ,qCACRG,kBAAmB,EACnBF,aAAc,GAEhBG,KAAM,CACJC,QAAS,EAETC,gBAAiB,KAAK,IAExB,SAAU,CACRC,eAAgB,YAChBC,sBAAuB,QAEzB,SAAU,CACRD,eAAgB,gBAElBE,OAAQ,CACN5B,WAAYV,EAAMQ,kBAGpB,SAAU,CACR+B,aAAc,EACdC,YAAa,QACbN,QAAS,EACTO,GAAI,CACFF,aAAc,SACdC,YAAa,EACbhB,cAAe,QACfC,aAAc,IAGlBiB,GAAI,CACFC,cAAe,SACfD,GAAI,CACFC,cAAe,SAGnBC,GAAI,CACFD,cAAe,WAGjB,kBAAmB,CACjBpB,OAAQ,SAEVsB,IAAK,CACHX,QAAS,cACTY,WAAY,WACZC,SAAU,aACVnB,WAAY,2BACZC,OAAQ,qCACRC,aAAc,EACdJ,WAAY1B,EAAM2B,eAElBL,KAAM,CACJ0B,QAAS,SACTzB,OAAQ,EACRW,QAAS,EACT9B,SAAU,UACVsB,WAAY,UACZE,WAAY,cACZC,OAAQ,IAGZoB,WAAY,CACVzB,cAAe,UACfC,aAAc,EACdyB,kBAAmB,qCACnBC,QAAS,OAGAC,EAAoBpD,IAC/B,MAAM,aACJc,EAAY,UACZuC,GACErD,EACEsD,EAAaD,EACnB,MAAO,CACL,iBAAkB,CAChBE,SAAU,WACV,OAAQ,CACNC,iBAAkBxD,EAAMyD,KAAKzD,EAAMqD,WAAWK,KAAK,GAAGC,QACtDC,UAAW5D,EAAMyD,KAAKH,GAAYI,KAAK,GAAGC,QAC1ClD,aAAc,eAAc,QAAK6C,OAEnC,CAAC,GAAGxC,0BAAsC,CACxCyC,SAAU,WACVM,eAAgB7D,EAAMyD,KAAKzD,EAAM8D,UAAUC,IAAI,GAAGJ,QAClDK,cAAehE,EAAM8D,SACrBxD,MAAON,EAAMiE,UAEbvD,WAAY,SACZN,SAAUJ,EAAMI,SAChB8D,UAAW,SACX9C,cAAe,QAEjB+C,SAAU,CACR5C,OAAQ,cAER6C,cAAe,OACfC,OAAQ,UAKHC,EAAoBtE,IAAS,CACxC,CAAC,GAAGA,EAAMc,6BAA8B,CACtC,sCAGW,CACTR,MAAON,EAAMuE,eAGjB,CAAC,GAAGvE,EAAMc,+BAAgC,CACxC0D,kBAAmB,KC3LjBC,EAAqBzE,IACzB,MAAM,aACJc,EAAY,eACZ4D,GACE1E,EACJ,MAAO,CACL,CAACc,GAAeC,OAAOC,OAAOD,OAAOC,OAAOD,OAAOC,OAAOD,OAAOC,OAAOD,OAAOC,OAAOD,OAAOC,OAAOD,OAAOC,OAAOD,OAAOC,OAAOD,OAAOC,OAAO,CAC5IV,MAAON,EAAM2E,UACbC,UAAW,aACXvE,WAAYL,EAAMK,WAClB,CAAC,IAAIS,eAA2B,CAC9BR,MAAON,EAAM6E,sBAEf,CAAC,IAAI/D,aAAyB,CAC5BR,MAAON,EAAM8E,kBAEf,CAAC,IAAIhE,aAAyB,CAC5BR,MAAON,EAAM+E,kBAEf,CAAC,IAAIjE,YAAwB,CAC3BR,MAAON,EAAMgF,eACb,sBAAuB,CACrB1E,MAAON,EAAMiF,sBAEf,WAAY,CACV3E,MAAON,EAAMkF,sBAGjB,CAAC,IAAIpE,cAA0B,CAC7BR,MAAON,EAAMkB,kBACbC,OAAQ,cACRF,WAAY,QAEd,qCAGI,CACFR,aAAc,QAEfV,EAAeC,IAAS,CACzB,CAAC,iBACOc,mBACAA,mBACAA,mBACAA,mBACAA,aACJ,CACF8C,UAAWc,GAEb,qGASM,CACJ,uFAMI,CACFd,UAAWc,MAGbrD,EAAerB,IAASa,EAAcb,IAAS,CAEjD,CAAC,aACGc,sBACAA,wBACAA,oBACAA,kBACAC,OAAOC,OAAOD,OAAOC,OAAO,CAAC,GAAG,QAAchB,IAAS,CACzDwE,kBAAmBxE,EAAMmF,cAEzB/B,EAAkBpD,IAASsE,EAAkBtE,ID+Gd,CACrC,yCAGI,CACFgD,QAAS,eACToC,SAAU,QAEZ,yBAA0B,CACxBtC,WAAY,SACZuC,SAAU,SACVC,aAAc,WAEd,YAAa,CACXC,cAAe,UAEjB,SAAU,CACR9D,aAAc,EACd2D,SAAU,qBACVpC,QAAS,eACTqC,SAAU,SACVC,aAAc,WACdC,cAAe,SAEfC,UAAW,gBAGf,2BAA4B,CAC1BxC,QAAS,cACTqC,SAAU,SACVI,gBAAiB,EACjBC,gBAAiB,cC9I+D,CAC9E,QAAS,CACPC,UAAW,WAUnB,OAAe,QAAc,aAAc3F,GAAS,CAACyE,EAAmBzE,IALnC,KAAM,CACzC0E,eAAgB,QAChBnE,kBAAmB,WCqBrB,MAvGiBjC,IACf,MAAM,UACJsH,EACA,aAAcC,EAAS,UACvBC,EAAS,MACTxG,EAAK,UACLqG,EAAS,UACTI,EAAS,SACTC,GAAW,EAAI,MACftG,EAAK,OACLuG,EAAM,SACNC,EAAQ,MACRC,EAAK,UACLC,EAAS,UACTC,EAAyB,gBAAoB,EAAe,OAC1D/H,EACEC,EAAM,SAAa,MACnB+H,EAAgB,UAAa,GAC7BC,EAAc,SAAa,OAC1BC,EAASC,GAAc,WAAe/G,GAC7C,YAAgB,KACd+G,EAAW/G,IACV,CAACA,IACJ,YAAgB,KACd,IAAIgH,EACJ,GAA2B,QAAtBA,EAAKnI,EAAIiI,eAA4B,IAAPE,OAAgB,EAASA,EAAGC,kBAAmB,CAChF,MAAM,SACJC,GACErI,EAAIiI,QAAQG,kBAChBC,EAASC,QACT,MAAM,OACJC,GACEF,EAASlH,MACbkH,EAASG,kBAAkBD,EAAQA,EACrC,GACC,IACH,MAkBME,EAAgB,KACpBf,EAAOO,EAAQS,UAuBVC,EAAYC,EAAQC,GAAa,EAASxB,GAC3CyB,EAAoB,IAAWzB,EAAW,GAAGA,iBAA0B,CAC3E,CAAC,GAAGA,SAAgC,QAAdD,EACtB,CAAC,GAAGC,KAAaQ,OAAgBA,GAChCN,EAAWqB,EAAQC,GACtB,OAAOF,EAAwB,gBAAoB,MAAO,CACxDpB,UAAWuB,EACX/H,MAAOA,GACO,gBAAoBgI,EAAA,EAAU,CAC5C/I,IAAKA,EACLwH,UAAWA,EACXrG,MAAO8G,EACPe,SAtDe,EACfC,aAEAf,EAAWe,EAAO9H,MAAM+H,QAAQ,UAAW,MAoD3CC,UA5CgB,EAChBC,cAGIrB,EAAcE,UAClBD,EAAYC,QAAUmB,IAwCtBC,QAnCc,EACdD,UACAE,UACAC,SACAC,UACAC,eAGIzB,EAAYC,UAAYmB,GAAWrB,EAAcE,SAAWqB,GAAWC,GAAUC,GAAWC,IAG5FL,IAAYM,EAAA,EAAQC,OACtBlB,IACAb,SAA8CA,KACrCwB,IAAYM,EAAA,EAAQE,KAC7BjC,MAqBFkC,mBApDyB,KACzB9B,EAAcE,SAAU,GAoDxB6B,iBAlDuB,KACvB/B,EAAcE,SAAU,GAkDxB8B,OApBa,KACbtB,KAoBA,aAAcnB,EACd0C,KAAM,EACNvC,SAAUA,IACM,OAAdK,GAAqB,QAAaA,EAAW,CAC/CP,UAAW,GAAGF,2BACX,Q,6BCxGP,MANe,CAAC4C,EAAWC,GAAY,IACjCA,GAAa,MAACD,EACT,GAEFtJ,MAAMC,QAAQqJ,GAAaA,EAAY,CAACA,GCJ7CE,EAAsC,SAAUC,EAASC,EAAYC,EAAGC,GAM1E,OAAO,IAAKD,IAAMA,EAAIE,UAAU,SAAUC,EAASC,GACjD,SAASC,EAAUxJ,GACjB,IACEyJ,EAAKL,EAAUM,KAAK1J,GACtB,CAAE,MAAO2J,GACPJ,EAAOI,EACT,CACF,CACA,SAASC,EAAS5J,GAChB,IACEyJ,EAAKL,EAAiB,MAAEpJ,GAC1B,CAAE,MAAO2J,GACPJ,EAAOI,EACT,CACF,CACA,SAASF,EAAKI,GApBhB,IAAe7J,EAqBX6J,EAAOC,KAAOR,EAAQO,EAAO7J,QArBlBA,EAqBiC6J,EAAO7J,MApB9CA,aAAiBmJ,EAAInJ,EAAQ,IAAImJ,EAAE,SAAUG,GAClDA,EAAQtJ,EACV,IAkB4D+J,KAAKP,EAAWI,EAC5E,CACAH,GAAML,EAAYA,EAAUY,MAAMf,EAASC,GAAc,KAAKQ,OAChE,EACF,EAkDA,MA7CqB,EACnBO,aACAC,eAEA,MAAOC,EAAQC,GAAa,YAAe,IACpCC,EAAaC,GAAkB,YAAe,GAC/CC,EAAY,SAAa,MACzBC,EAAc,KACdD,EAAUzD,SACZ2D,aAAaF,EAAUzD,UAGrB4D,EAAc,CAAC,EACjBT,EAAWU,SACbD,EAAYC,OAASV,EAAWU,QAElC,YAAgB,IAAMH,EAAa,IAuBnC,MAAO,CACLL,SACAE,cACAO,SAxBc,EAAAC,EAAA,GAASlB,GAAKX,OAAU,OAAQ,OAAQ,EAAQ,YAC9D,IAAIhC,EACJ2C,SAAsCA,EAAEmB,iBACxCnB,SAAsCA,EAAEoB,kBACxCT,GAAe,GACf,IACE,MAAMU,EAAkC,mBAApBf,EAAWe,WAA4Bf,EAAWe,OAASf,EAAWe,KAC1F,IAAKA,GAAQ,EAAOd,GAAU,GAAMe,KAAK,KAAO,GAAIP,GACpDJ,GAAe,GACfF,GAAU,GAEVI,IACAD,EAAUzD,QAAUoE,WAAW,KAC7Bd,GAAU,IACT,KAC0B,QAA5BpD,EAAKiD,EAAWkB,cAA2B,IAAPnE,GAAyBA,EAAGoE,KAAKnB,EAAYN,EACpF,CAAE,MAAO0B,GAEP,MADAf,GAAe,GACTe,CACR,CACF,MCpEa,SAASC,EAAgBC,EAAYC,GAClD,OAAO,UAAc,KACnB,MAAMC,IAAYF,EAClB,MAAO,CAACE,EAASpK,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAGkK,GAAiBC,GAAiC,iBAAfF,EAA0BA,EAAa,QAC1H,CAACA,GACN,CCEA,MAPoBvL,IAClB,MAAMnB,GAAM,IAAA6M,aAAO3L,GAInB,OAHA,IAAA4L,WAAU,KACR9M,EAAIiI,QAAU9G,IAETnB,EAAIiI,SCeb,MApBwB,CAAC8E,EAASC,EAAgB3B,KAAa,IAAA4B,SAAQ,KACrD,IAAZF,EACK,CACLG,MAAOF,QAAuDA,EAAiB3B,IAGlE,IAAA8B,gBAAeJ,GACvB,CACLG,MAAOH,GAGY,iBAAZA,EACFvK,OAAOC,OAAO,CACnByK,MAAOF,QAAuDA,EAAiB3B,GAC9E0B,GAEE,CACLG,MAAOH,GAER,CAACA,EAASC,EAAgB3B,IClBzB+B,EAAgC,SAAUC,EAAGvC,GAC/C,IAAIwC,EAAI,CAAC,EACT,IAAK,IAAIC,KAAKF,EAAO7K,OAAOgL,UAAUC,eAAelB,KAAKc,EAAGE,IAAMzC,EAAE4C,QAAQH,GAAK,IAAGD,EAAEC,GAAKF,EAAEE,IAC9F,GAAS,MAALF,GAAqD,mBAAjC7K,OAAOmL,sBAA2C,KAAIC,EAAI,EAAb,IAAgBL,EAAI/K,OAAOmL,sBAAsBN,GAAIO,EAAIL,EAAEhF,OAAQqF,IAClI9C,EAAE4C,QAAQH,EAAEK,IAAM,GAAKpL,OAAOgL,UAAUK,qBAAqBtB,KAAKc,EAAGE,EAAEK,MAAKN,EAAEC,EAAEK,IAAMP,EAAEE,EAAEK,IADuB,CAGvH,OAAON,CACT,EAOA,MAAMQ,EAA0B,aAAiB,CAAC/N,EAAOC,KACvD,MACIqH,UAAW0G,EACXlG,UAAWmG,EAAY,UAAS,UAChCzG,EAAS,cACT0G,EAAa,cACbC,EAAa,SACb7C,EACAjE,UAAW+G,EAAmB,MAC9BpN,GACEhB,EACJqO,EAAYhB,EAAOrN,EAAO,CAAC,YAAa,YAAa,YAAa,gBAAiB,gBAAiB,WAAY,YAAa,WACzH,aACJsO,EACAjH,UAAWkH,EACX/G,UAAWgH,EACXxN,MAAOyN,IACL,QAAmB,cACjBpH,EAAY+G,QAAiEA,EAAsBG,EACnGG,EAAYP,GAAgB,QAAWlO,EAAKkO,GAAiBlO,EAC7DqH,EAAYgH,EAAa,aAAcN,GAM7C,MAAOpF,EAAYC,EAAQC,GAAa,EAASxB,GAC3CqH,EAAqB,IAAWrH,EAAWkH,EAAkB,CACjE,CAAC,GAAGlH,SAAgC,QAAdD,GACrBG,EAAW0G,EAAerF,EAAQC,GAC/B8F,EAAcnM,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAG+L,GAAezN,GACnE,OAAO4H,EAGP,gBAAoBqF,EAAWxL,OAAOC,OAAO,CAC3C8E,UAAWmH,EACX3N,MAAO4N,EACP3O,IAAKyO,GACJL,GAAY/C,MAKjB,Q,WCzDA,GADmB,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,SAAW,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,8XAAkY,KAAQ,OAAQ,MAAS,YCMjjB,GAAe,SAAsBtL,EAAOC,GAC9C,OAAoB,gBAAoBC,EAAAC,GAAU,OAAS,CAAC,EAAGH,EAAO,CACpEC,IAAKA,EACLG,KAAM,KAEV,EAOA,OAJ2B,aAAiB,I,WCfrC,SAAS,GAAOyO,GACrB,OAAY,IAARA,EACK,EAAC,GAAO,GAEVjO,MAAMC,QAAQgO,GAAOA,EAAM,CAACA,EACrC,CACO,SAASC,GAAQC,EAAKC,EAAaC,GACxC,OAAY,IAARF,QAAwB5N,IAAR4N,EACXC,EAEFD,GAAOE,GAAWD,CAC3B,CA2BO,MAAME,GAAcL,GAAO,CAAC,SAAU,UAAUM,gBAAgBN,GCIvE,OAjCgB,EACdvH,YACAiE,SACA6D,SACAC,WACAC,WACAlP,OACAmP,WACAhD,SACAiD,QAASC,MAET,MAAMC,EAAe,GAAOJ,GACtBK,EAAY,GAAOvP,IAEvBmL,OAAQqE,EACRC,KAAMC,GACJV,QAAuCA,EAAS,CAAC,EAC/CW,EAAYxE,EAASqE,EAAaE,EAClCE,EAAYlB,GAAQY,EAAanE,EAAS,EAAI,GAAIwE,GAClDxI,EAAiC,iBAAdyI,EAAyBA,EAAYD,EAC9D,OAAoB,gBAAoB,IAAS,CAC/C5C,MAAO6C,GACO,gBAAoB,SAAU,CAC5CC,KAAM,SACNzI,UAAW,IAAW,GAAGF,SAAkB,CACzC,CAAC,GAAGA,kBAA2BiE,EAC/B,CAAC,GAAGjE,oBAA6B+H,IAEnCrD,QAASO,EACT,aAAchF,EACdgI,SAAUA,GACThE,EAASuD,GAAQa,EAAU,GAAiB,gBAAoBO,GAAA,EAAe,OAAO,GAAQpB,GAAQa,EAAU,GAAIF,EAA0B,gBAAoBU,GAAA,EAAiB,MAAqB,gBAAoB,GAAc,OAAO,MCjCtP,MAAMC,GAA2B,aAAiB,EAChDpP,QACAsK,YACCrL,KACD,MAAMoQ,EAAU,SAAa,MAQ7B,OAPA,sBAA0BpQ,EAAK,KAAM,CACnCqQ,SAAU,KACR,MAAMC,EAAOF,EAAQnI,QACrB,OAAOqI,EAAKC,aAAeD,EAAKE,cAElCC,UAAW,IAAML,EAAQnI,QAAQuI,gBAEf,gBAAoB,OAAQ,CAC9C,eAAe,EACfxQ,IAAKoQ,EACLrP,MAAOyB,OAAOC,OAAO,CACnBuC,SAAU,QACVP,QAAS,QACTiM,KAAM,EACNC,IAAK,EACL9N,cAAe,OACfe,gBAAiB,yBAChB7C,IACFsK,KAGL,SAASuF,GAAWC,EAAUC,GAC5B,IAAIC,EAAU,EACd,MAAMC,EAAkB,GACxB,IAAK,IAAIpD,EAAI,EAAGA,EAAIiD,EAAStI,OAAQqF,GAAK,EAAG,CAE3C,GAAImD,IAAYD,EACd,OAAOE,EAET,MAAMC,EAAOJ,EAASjD,GAGhBsD,EAAUH,GAFD9B,GAAYgC,GACFE,OAAOF,GAAM1I,OAAS,GAI/C,GAAI2I,EAAUJ,EAAK,CACjB,MAAMM,EAAUN,EAAMC,EAEtB,OADAC,EAAgBK,KAAKF,OAAOF,GAAMK,MAAM,EAAGF,IACpCJ,CACT,CACAA,EAAgBK,KAAKJ,GACrBF,EAAUG,CACZ,CACA,OAAOL,CACT,CAEA,MAAMU,GAAsB,EAItBC,GAAkC,EAClCC,GAAgB,CACpBhN,QAAS,cACTqC,SAAU,SACVK,gBAAiB,YAEJ,SAASuK,GAAgB3R,GACtC,MAAM,cACJ4R,EAAa,MACbC,EAAK,KACLzF,EAAI,SACJd,EAAQ,KACRrB,EAAI,SACJ6H,EAAQ,SACRC,EAAQ,WACRC,GACEhS,EACE8Q,EAAW,UAAc,KAAM,EAAAmB,EAAA,GAAQ7F,GAAO,CAACA,IAC/C8F,EAAU,UAAc,IAhDZpB,IAAYA,EAASqB,OAAO,CAACC,EAAUlB,IAASkB,GAAYlD,GAAYgC,GAAQE,OAAOF,GAAM1I,OAAS,GAAI,GAgDxF6J,CAAYvB,GAAW,CAAC1E,IAGtDkG,EAAc,UAAc,IAAMhH,EAASwF,GAAU,GAAQ,CAAC1E,KAE7DmG,EAAkBC,GAAuB,WAAe,MACzDC,EAAY,SAAa,MAEzBC,EAAuB,SAAa,MACpCC,EAAkB,SAAa,MAE/BC,EAAsB,SAAa,MACnCC,EAAuB,SAAa,OACnCC,EAAaC,GAAkB,YAAe,IAC9CC,EAAcC,GAAmB,WAAezB,KAChD0B,EAAgBC,GAAqB,WAAe,IACpDC,EAAkBC,GAAuB,WAAe,OAE/D,EAAAC,EAAA,GAAgB,KAEZL,EADErB,GAAiBC,GAASK,EAxCH,EA2CTV,KAEjB,CAACK,EAAOzF,EAAMnC,EAAM2H,EAAed,KAEtC,EAAAwC,EAAA,GAAgB,KACd,IAAIlL,EAAImL,EAAIC,EAAIC,EAChB,GAjD2B,IAiDvBT,EAAyC,CAC3CC,EAjDuB,GAmDvB,MAAMS,EAAiBhB,EAAqBxK,SAAWyL,iBAAiBjB,EAAqBxK,SAAS1D,WACtG6O,EAAoBK,EACtB,MAAO,GArDkB,IAqDdV,EAAuC,CAChD,MAAMY,KAAmD,QAAlCxL,EAAKuK,EAAgBzK,eAA4B,IAAPE,OAAgB,EAASA,EAAGkI,YAC7F2C,EAAgBW,EAtDe,EAsD6BnC,IAC5De,EAAoBoB,EAAa,CAAC,EAAG1B,GAAW,MAChDa,EAAea,GAEf,MAAMC,GAA6D,QAAlCN,EAAKZ,EAAgBzK,eAA4B,IAAPqL,OAAgB,EAASA,EAAG7C,cAAgB,EAEjHoD,EAAkC,IAAT7J,EAAa,GAA4C,QAAtCuJ,EAAKZ,EAAoB1K,eAA4B,IAAPsL,OAAgB,EAASA,EAAG9C,cAAgB,EACtIqD,GAAmE,QAAvCN,EAAKZ,EAAqB3K,eAA4B,IAAPuL,OAAgB,EAASA,EAAG/C,cAAgB,EACvHsD,EAAgBC,KAAKC,IAAIL,EAE/BC,EAAyBC,GACzBZ,EAAkBa,EAAgB,GAClChC,EAAW4B,EACb,GACC,CAACZ,IAEJ,MAAMmB,EAAc5B,EAAmB0B,KAAKG,MAAM7B,EAAiB,GAAKA,EAAiB,IAAM,GAAK,GACpG,EAAAe,EAAA,GAAgB,KACd,IAAIlL,EACJ,MAAOiM,EAAUC,GAAY/B,GAAoB,CAAC,EAAG,GACrD,GAAI8B,IAAaC,EAAU,CACzB,MACMV,IAD0C,QAA5BxL,EAAKqK,EAAUvK,eAA4B,IAAPE,OAAgB,EAASA,EAAGsI,cAAgB,GACrEwC,EAC/B,IAAIqB,EAAiBJ,EACjBG,EAAWD,IAAa,IAC1BE,EAAiBX,EAAaS,EAAWC,GAE3C9B,EAAoBoB,EAAa,CAACS,EAAUE,GAAkB,CAACA,EAAgBD,GACjF,GACC,CAAC/B,EAAkB4B,IAEtB,MAAMK,EAAe,UAAc,KAEjC,IAAK5C,EACH,OAAOtG,EAASwF,GAAU,GAE5B,GA1FiC,IA0F7BkC,IAAkDT,GAAoBA,EAAiB,KAAOA,EAAiB,GAAI,CACrH,MAAMkC,EAAUnJ,EAASwF,GAAU,GAGnC,MAAI,CAACW,GAAiCD,IAAqBrC,SAAS6D,GAC3DyB,EAEW,gBAAoB,OAAQ,CAC9CzT,MAAOyB,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAGgP,IAAgB,CACrDvK,gBAAiB8C,KAElBwK,EACL,CACA,OAAOnJ,EAASwG,EAAWhB,EAAWD,GAAWC,EAAUyB,EAAiB,IAAKO,IAChF,CAAChB,EAAUkB,EAAcT,EAAkBzB,GAAU4D,QAAO,OAAmB3C,KAE5E4C,EAAe,CACnB9C,QACA5O,OAAQ,EACRW,QAAS,EACTY,WAAiC,WAArB4O,EAAgC,SAAW,WAEzD,OAAoB,gBAAoB,WAAgB,KAAMoB,EAjHnC,IAiHiDxB,GAAuD,gBAAoB,WAAgB,KAAmB,gBAAoB5C,GAAa,CACzNpP,MAAOyB,OAAOC,OAAOD,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAGiS,GAAejD,IAAgB,CAClFvK,gBAAiB8C,IAEnBhK,IAAK0S,GACJL,GAA2B,gBAAoBlC,GAAa,CAC7DpP,MAAOyB,OAAOC,OAAOD,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAGiS,GAAejD,IAAgB,CAClFvK,gBAAiB8C,EAAO,IAE1BhK,IAAK2S,GACJN,GAA2B,gBAAoBlC,GAAa,CAC7DpP,MAAOyB,OAAOC,OAAOD,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAGiS,GAAejD,IAAgB,CAClFvK,gBAAiB,IAEnBlH,IAAK4S,GACJvH,EAAS,IAAI,KA/HmB,IA+HT0H,GAAiDT,GAAoBA,EAAiB,KAAOA,EAAiB,IAAoB,gBAAoBnC,GAAa,CAC3LpP,MAAOyB,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAGiS,GAAe,CACpD/D,IAAK,MAEP3Q,IAAKwS,GACJnH,EAASuF,GAAWC,EAAUqD,IAAc,IAtIlB,IAsI2BnB,GAAyD,gBAAoB,OAAQ,CAC3IhS,MAAO,CACLwD,WAAY,WAEdvE,IAAKyS,IAET,CCnLA,OAhBwB,EACtBkC,iBACAC,aACAvJ,WACAwJ,mBAEMA,aAAmD,EAASA,EAAa3H,QAAWyH,EAGtE,gBAAoB,IAASnS,OAAOC,OAAO,CAC7DqS,OAAMF,QAAa1T,GAClB2T,GAAexJ,GAJTA,ECRP,GAAgC,SAAUgC,EAAGvC,GAC/C,IAAIwC,EAAI,CAAC,EACT,IAAK,IAAIC,KAAKF,EAAO7K,OAAOgL,UAAUC,eAAelB,KAAKc,EAAGE,IAAMzC,EAAE4C,QAAQH,GAAK,IAAGD,EAAEC,GAAKF,EAAEE,IAC9F,GAAS,MAALF,GAAqD,mBAAjC7K,OAAOmL,sBAA2C,KAAIC,EAAI,EAAb,IAAgBL,EAAI/K,OAAOmL,sBAAsBN,GAAIO,EAAIL,EAAEhF,OAAQqF,IAClI9C,EAAE4C,QAAQH,EAAEK,IAAM,GAAKpL,OAAOgL,UAAUK,qBAAqBtB,KAAKc,EAAGE,EAAEK,MAAKN,EAAEC,EAAEK,IAAMP,EAAEE,EAAEK,IADuB,CAGvH,OAAON,CACT,EAiDA,MACMyH,GAAmB,CAAC,SAAU,OAAQ,OAAQ,YAAa,SAAU,WAAY,UACjFC,GAAoB,aAAiB,CAACjV,EAAOC,KACjD,IAAImI,EACJ,MACId,UAAW0G,EAAkB,UAC7BxG,EAAS,MACTxG,EAAK,KACLiP,EAAI,SACJiF,EAAQ,SACR5J,EAAQ,SACR6J,EAAQ,SACRC,EAAQ,SACRC,EAAQ,UACRvN,EAAS,MACTqF,GACEnN,EACJqO,EAAY,GAAOrO,EAAO,CAAC,YAAa,YAAa,QAAS,OAAQ,WAAY,WAAY,WAAY,WAAY,WAAY,YAAa,WAC3I,aACJsO,EAAY,UACZjH,GACE,aAAiB,OACdiO,IAAc,EAAAC,EAAA,GAAU,QACzBC,EAAgB,SAAa,MAC7BC,EAAc,SAAa,MAE3BnO,EAAYgH,EAAa,aAAcN,GACvC0H,GAAY,EAAAC,EAAA,GAAKtH,EAAW2G,KAE3BY,EAAYC,GAAcnJ,EAAgB0I,IAC1CU,EAASC,IAAc,EAAAC,EAAA,IAAe,EAAO,CAClD5U,MAAOyU,EAAWC,WAEd,YACJG,EAAc,CAAC,SACbJ,EACEK,EAAcC,IAClB,IAAI/N,EACA+N,IAC4B,QAA7B/N,EAAKyN,EAAWO,eAA4B,IAAPhO,GAAyBA,EAAGoE,KAAKqJ,IAEzEE,EAAWI,IAGPE,EAAc,EAAYP,IAChC,EAAAxC,EAAA,GAAgB,KACd,IAAIlL,GACC0N,GAAWO,IACiB,QAA9BjO,EAAKqN,EAAYvN,eAA4B,IAAPE,GAAyBA,EAAGG,UAEpE,CAACuN,IACJ,MAAMQ,EAAcvL,IAClBA,SAAsCA,EAAEmB,iBACxCgK,GAAY,IAERK,EAAenV,IACnB,IAAIgH,EAC2B,QAA9BA,EAAKyN,EAAW5M,gBAA6B,IAAPb,GAAyBA,EAAGoE,KAAKqJ,EAAYzU,GACpF8U,GAAY,IAERM,EAAe,KACnB,IAAIpO,EAC2B,QAA9BA,EAAKyN,EAAWjO,gBAA6B,IAAPQ,GAAyBA,EAAGoE,KAAKqJ,GACxEK,GAAY,KAGPO,EAAYpL,GAAcqB,EAAgB2I,IAC3C,OACJ9J,GAAM,YACNE,GACAO,QAAS0K,IACP,EAAa,CACfrL,aACAC,cAGKqL,GAAoBC,IAAyB,YAAe,IAC5DC,GAAuBC,IAA4B,YAAe,IAClEC,GAAcC,IAAmB,YAAe,IAChDC,GAAkBC,IAAuB,YAAe,IACxDC,GAAiBC,IAAsB,YAAe,IACtDxC,GAAgByC,IAAkB3K,EAAgByI,EAAU,CACjEmC,YAAY,EACZC,OAAQC,GAAcA,EAAalC,aAA+C,EAASA,EAAWmC,SAAWnC,aAA+C,EAASA,EAAWoC,UAE/K5F,GAAU6F,KAAe,EAAA3B,EAAA,GAAeqB,GAAeO,kBAAmB,EAAO,CACtFxW,MAAOiW,GAAevF,WAElB+F,GAAuBjD,MAAoB9C,IAA0C,gBAA9BuF,GAAeC,aAEtE,KACJrN,GAAO,GACLoN,GACES,GAAsB,UAAc,IAE1CD,UAE0B1W,IAA1BkW,GAAeU,QAAwBV,GAAerF,YAEtDqF,GAAeC,YAAc1B,GAAca,GAAa,CAACoB,GAAsBR,GAAgBzB,EAAYa,KAC3G,EAAAnD,EAAA,GAAgB,KACVsB,KAAmBkD,KACrBlB,GAAsB3V,EAAe,oBACrC6V,GAAyB7V,EAAe,mBAEzC,CAAC6W,GAAqBlD,KACzB,MAAOoD,GAAaC,IAAkB,WAAeJ,IAC/CK,GAAoB,UAAc,KAClCJ,KAGS,IAAT7N,GACK4M,GAEFF,IACN,CAACmB,GAAqBjB,GAAuBF,MAGhD,EAAArD,EAAA,GAAgB,KACd2E,GAAeC,IAAqBL,KACnC,CAACK,GAAmBL,KACvB,MAAMM,GAAmBN,KAAyBG,GAAcf,GAAmBF,IAC7EqB,GAAkBP,IAAiC,IAAT5N,IAAc+N,GACxDK,GAAeR,IAAwB5N,GAAO,GAAK+N,IAOlDM,GAAeC,IAAoB,WAAe,GAOnDC,GAAeC,IACnB,IAAIrQ,EACJ4O,GAAgByB,GAEZ1B,KAAiB0B,IACkB,QAApCrQ,EAAKiP,GAAerF,kBAA+B,IAAP5J,GAAyBA,EAAGoE,KAAK6K,GAAgBoB,KAIlG,YAAgB,KACd,MAAMC,EAAUlD,EAActN,QAC9B,GAAI0M,IAAkBoD,IAAeU,EAAS,CAC5C,MAAMC,EJ9LL,SAAuBtX,GAE5B,MAAMuX,EAAWnY,SAASa,cAAc,MACxCD,EAAIwX,YAAYD,GAKhB,MAAME,EAAOzX,EAAI0X,wBACXC,EAAYJ,EAASG,wBAI3B,OAFA1X,EAAI4X,YAAYL,GAIdE,EAAKnI,KAAOqI,EAAUrI,MAAQqI,EAAUE,MAAQJ,EAAKI,OAErDJ,EAAKlI,IAAMoI,EAAUpI,KAAOoI,EAAUG,OAASL,EAAKK,MAExD,CI2K8BC,CAAcV,GAClCzB,KAAqB0B,GACvBzB,GAAoByB,EAExB,GACC,CAAC/D,GAAgBoD,GAAa1M,EAAU+M,GAAclB,GAAiBmB,KAG1E,YAAgB,KACd,MAAMI,EAAUlD,EAActN,QAC9B,GAAoC,oBAAzBmR,uBAAyCX,IAAYV,KAAgBH,GAC9E,OAGF,MAAMyB,EAAW,IAAID,qBAAqB,KACxCjC,KAAqBsB,EAAQa,gBAG/B,OADAD,EAASE,QAAQd,GACV,KACLY,EAASG,eAEV,CAACzB,GAAaH,KAEjB,MAAM/C,GAAe,EAAgBuC,GAAerK,QAAS6I,EAAWzJ,KAAMd,GACxEoO,GAAe,UAAc,KACjC,GAAK9E,KAAkBoD,GAGvB,MAAO,CAACnC,EAAWzJ,KAAMd,EAAU6B,EAAO2H,GAAa3H,OAAOwM,KAAKzK,KAClE,CAAC0F,GAAgBoD,GAAa7K,EAAO2H,GAAa3H,MAAOgL,KAG5D,GAAIrC,EACF,OAAoB,gBAAoB,EAAU,CAChD1U,MAAkC,QAA1BgH,EAAKyN,EAAWzJ,YAAyB,IAAPhE,EAAgBA,EAAyB,iBAAbkD,EAAwBA,EAAW,GACzG3D,OAAQ4O,EACR3O,SAAU4O,EACV3O,MAAOgO,EAAWhO,MAClBP,UAAWA,EACXE,UAAWA,EACXxG,MAAOA,EACPqG,UAAWA,EACXS,UAAWA,EACXL,UAAWoO,EAAWpO,UACtBC,SAAUmO,EAAWnO,SACrBK,UAAW8N,EAAW9N,YAK1B,MAAM6R,GAAe,KACnB,MAAM,WACJtC,EAAU,OACVC,GACEF,GACJ,OAAOC,EAA2B,gBAAoB,SAAU,CAC9DrH,KAAM,SACN4J,IAAK,SACLrS,UAAW,GAAGF,KAAawK,GAAW,WAAa,WACnD9F,QAASjB,GAnFS,EAACA,EAAG+O,KACxB,IAAI1R,EACJuP,GAAYmC,EAAKhI,UACkB,QAAlC1J,EAAKiP,GAAe0C,gBAA6B,IAAP3R,GAAyBA,EAAGoE,KAAK6K,GAAgBtM,EAAG+O,IAgF/EE,CAAcjP,EAAG,CAC7B+G,UAAWA,KAEb,aAAcA,GAAWwD,EAAWmC,SAAWnC,aAA+C,EAASA,EAAWoC,QAC/F,mBAAXH,EAAwBA,EAAOzF,IAAYyF,GAAW,MAG5D0C,GAAa,KACjB,IAAKrE,EACH,OAEF,MAAM,KACJxV,EAAI,QACJ4M,EAAO,SACPuC,GACEsG,EACEqE,GAAY,EAAAjI,EAAA,GAAQjF,GAAS,KAAOsI,aAA+C,EAASA,EAAWa,MACvG5O,EAAiC,iBAAd2S,EAAyBA,EAAY,GAC9D,OAAOjE,EAAY9G,SAAS,QAAwB,gBAAoB,IAAS,CAC/E0K,IAAK,OACL1M,OAAmB,IAAZH,EAAoB,GAAKkN,GAClB,gBAAoB,SAAU,CAC5CjK,KAAM,SACNhQ,IAAKwV,EACLjO,UAAW,GAAGF,SACd0E,QAASsK,EACT,aAAc/O,EACdgI,SAAUA,GACTnP,GAAqB,gBAAoB,EAAc,CACxD+Z,KAAM,aACA,MAkBJC,GAAmBtH,GAAe,CAACA,GAAe8G,KAAgBK,KAdjExD,EAGe,gBAAoB,GAAShU,OAAOC,OAAO,CAC7DmX,IAAK,QACJxO,EAAY,CACb/D,UAAWA,EACXiE,OAAQA,GACR6D,OAAQkG,EACR/I,OAAQmK,GACRlH,QAAS/D,GACT4D,SAAU/D,WAVH,MAkBX,OAAoB,gBAAoB,IAAgB,CACtD+O,SAnIe,EACfC,kBAEA/B,GAAiB+B,IAiIjBpF,UAAW2C,IACV0C,GAA2B,gBAAoB,GAAiB,CACjEzF,aAAcA,GACdF,eAAgBiD,GAChBhD,WAAYsD,IACE,gBAAoB,EAAY1V,OAAOC,OAAO,CAC5D8E,UAAW,IAAW,CACpB,CAAC,GAAGF,KAAa2I,KAASA,EAC1B,CAAC,GAAG3I,cAAuB4N,EAC3B,CAAC,GAAG5N,cAAuBsN,GAC3B,CAAC,GAAGtN,0BAAmC8Q,GACvC,CAAC,GAAG9Q,4BAAqC+Q,IACxC7Q,GACHF,UAAW0G,EACXhN,MAAOyB,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAG1B,GAAQ,CAC7CmG,gBAAiBkR,GAAepO,QAAO9I,IAEzC2G,UAAWA,EACX7H,KAAK,QAAWsa,EAAW/E,EAAevV,GAC1CoH,UAAWA,EACX2E,QAASiK,EAAY9G,SAAS,QAAUmH,OAAcnV,EACtD,aAAcuY,cAAmD,EAASA,GAAac,WACvFrN,MAAOA,GACNuI,GAAyB,gBAAoB,GAAU,CACxD9D,cAAeiG,KAAyBG,GACxC5L,KAAMd,EACNrB,KAAMA,GACN4H,MAAOyG,GACPtG,WAAYwG,GACZ1G,SAAUA,GACVC,SAAU,CAACxG,GAAQuG,GAAUrG,GAAamK,EAAYa,EAAYnB,GAAYZ,QAAO,OAAmBM,GAAiByF,IAAIZ,GAAO7Z,EAAM6Z,OACzI,CAAC3I,EAAM4B,IA/TZ,UAA4B,KAC1BnP,EAAI,KACJX,EAAI,UACJ0X,EACAC,OAAQC,EAAG,OACX5W,EAAM,SACN6W,EAAQ,OACRC,GACCrG,GACD,IAAIsG,EAAiBtG,EACrB,SAASuG,EAAKC,EAAKC,GACZA,IAGLH,EAA8B,gBAAoBE,EAAK,CAAC,EAAGF,GAC7D,CAQA,OAPAC,EAAK,SAAUhX,GACfgX,EAAK,IAAKN,GACVM,EAAK,MAAOJ,GACZI,EAAK,OAAQhY,GACbgY,EAAK,OAAQrX,GACbqX,EAAK,MAAOH,GACZG,EAAK,IAAKF,GACHC,CACT,CAuS4BI,CAAmBnb,EAAoB,gBAAoB,WAAgB,KAAMkR,EAAK1I,OAAS,GAAKsK,IAAgBhB,IAAY4H,GAA6B,gBAAoB,OAAQ,CACjNG,IAAK,eACL,eAAe,GACd3I,GAASA,EAxCW4B,IAAe,CAACA,IAAgBhB,IAA0B,gBAAoB,OAAQ,CAC3G,eAAe,EACf+H,IAAK,YAnQY,OAoQAxC,GAAeU,OAAQqC,GAAiBtH,IAqCzCsI,CAAetI,WAEnC,UCpWI,GAAgC,SAAUxF,EAAGvC,GAC/C,IAAIwC,EAAI,CAAC,EACT,IAAK,IAAIC,KAAKF,EAAO7K,OAAOgL,UAAUC,eAAelB,KAAKc,EAAGE,IAAMzC,EAAE4C,QAAQH,GAAK,IAAGD,EAAEC,GAAKF,EAAEE,IAC9F,GAAS,MAALF,GAAqD,mBAAjC7K,OAAOmL,sBAA2C,KAAIC,EAAI,EAAb,IAAgBL,EAAI/K,OAAOmL,sBAAsBN,GAAIO,EAAIL,EAAEhF,OAAQqF,IAClI9C,EAAE4C,QAAQH,EAAEK,IAAM,GAAKpL,OAAOgL,UAAUK,qBAAqBtB,KAAKc,EAAGE,EAAEK,MAAKN,EAAEC,EAAEK,IAAMP,EAAEE,EAAEK,IADuB,CAGvH,OAAON,CACT,EAyBA,OArB0B,aAAiB,CAACnF,EAAInI,KAC9C,IAAI,SACAkV,EAAQ,IACRkG,GACEjT,EACJiG,EAAY,GAAOjG,EAAI,CAAC,WAAY,QAKtC,MAAMkT,EAAc7Y,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAG2L,GAAY,CAC9DgN,SAAala,IAARka,GAA0C,WAArBhN,EAAUnF,OAAsB,sBAAwBmS,IAIpF,cADOC,EAAYC,SACC,gBAAoB,GAAM9Y,OAAOC,OAAO,CAAC,EAAG4Y,EAAa,CAC3Erb,IAAKA,EACLkV,WAAYA,EACZrN,UAAW,SCtBf,OAL+B,aAAiB,CAAC9H,EAAOC,IAAsB,gBAAoB,GAAMwC,OAAOC,OAAO,CACpHzC,IAAKA,GACJD,EAAO,CACR8H,UAAW,UCLT,GAAgC,SAAUwF,EAAGvC,GAC/C,IAAIwC,EAAI,CAAC,EACT,IAAK,IAAIC,KAAKF,EAAO7K,OAAOgL,UAAUC,eAAelB,KAAKc,EAAGE,IAAMzC,EAAE4C,QAAQH,GAAK,IAAGD,EAAEC,GAAKF,EAAEE,IAC9F,GAAS,MAALF,GAAqD,mBAAjC7K,OAAOmL,sBAA2C,KAAIC,EAAI,EAAb,IAAgBL,EAAI/K,OAAOmL,sBAAsBN,GAAIO,EAAIL,EAAEhF,OAAQqF,IAClI9C,EAAE4C,QAAQH,EAAEK,IAAM,GAAKpL,OAAOgL,UAAUK,qBAAqBtB,KAAKc,EAAGE,EAAEK,MAAKN,EAAEC,EAAEK,IAAMP,EAAEE,EAAEK,IADuB,CAGvH,OAAON,CACT,EAKA,MAAMiO,GAAO,CAACpT,EAAInI,KAChB,IAAI,SACAkV,GACE/M,EACJiG,EAAY,GAAOjG,EAAI,CAAC,aAC1B,MAAMqT,EAAiB,UAAc,IAC/BtG,GAAgC,iBAAbA,GACd,EAAAQ,EAAA,GAAKR,EAAU,CAAC,aAAc,SAEhCA,EACN,CAACA,IAKJ,OAAoB,gBAAoB,GAAM1S,OAAOC,OAAO,CAC1DzC,IAAKA,GACJoO,EAAW,CACZ8G,SAAUsG,EACV3T,UAAW,WAGf,OAA4B,aAAiB0T,IClCzC,GAAgC,SAAUlO,EAAGvC,GAC/C,IAAIwC,EAAI,CAAC,EACT,IAAK,IAAIC,KAAKF,EAAO7K,OAAOgL,UAAUC,eAAelB,KAAKc,EAAGE,IAAMzC,EAAE4C,QAAQH,GAAK,IAAGD,EAAEC,GAAKF,EAAEE,IAC9F,GAAS,MAALF,GAAqD,mBAAjC7K,OAAOmL,sBAA2C,KAAIC,EAAI,EAAb,IAAgBL,EAAI/K,OAAOmL,sBAAsBN,GAAIO,EAAIL,EAAEhF,OAAQqF,IAClI9C,EAAE4C,QAAQH,EAAEK,IAAM,GAAKpL,OAAOgL,UAAUK,qBAAqBtB,KAAKc,EAAGE,EAAEK,MAAKN,EAAEC,EAAEK,IAAMP,EAAEE,EAAEK,IADuB,CAGvH,OAAON,CACT,EAIA,MAAMmO,GAAiB,CAAC,EAAG,EAAG,EAAG,EAAG,GAiBpC,OAhB2B,aAAiB,CAAC1b,EAAOC,KAClD,MAAM,MACF0b,EAAQ,GACN3b,EACJqO,EAAY,GAAOrO,EAAO,CAAC,UAK7B,MAAM8H,EAAY4T,GAAevM,SAASwM,GAAS,IAAIA,IAAU,KACjE,OAAoB,gBAAoB,GAAMlZ,OAAOC,OAAO,CAC1DzC,IAAKA,GACJoO,EAAW,CACZvG,UAAWA,OCpBf,MAAM,GAAa,EACnB,GAAW0T,KAAO,GAClB,GAAWI,KAAO,GAClB,GAAWC,MAAQ,GACnB,GAAWC,UAAY,GACvB,S,4FCVA,EADoB,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,SAAW,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,+LAAmM,KAAQ,QAAS,MAAS,Y,UCMpX,EAAgB,SAAuB9b,EAAOC,GAChD,OAAoB,gBAAoBC,EAAAC,GAAU,OAAS,CAAC,EAAGH,EAAO,CACpEC,IAAKA,EACLG,KAAM,IAEV,EAOA,MAJ2B,aAAiB,E,mBCd5C2b,EAAOC,QAAU,WACf,IAAIC,EAAYxb,SAASyb,eACzB,IAAKD,EAAUE,WACb,OAAO,WAAa,EAKtB,IAHA,IAAIC,EAAS3b,SAAS4b,cAElBC,EAAS,GACJzO,EAAI,EAAGA,EAAIoO,EAAUE,WAAYtO,IACxCyO,EAAOhL,KAAK2K,EAAUM,WAAW1O,IAGnC,OAAQuO,EAAOI,QAAQC,eACrB,IAAK,QACL,IAAK,WACHL,EAAOM,OACP,MAEF,QACEN,EAAS,KAKb,OADAH,EAAUU,kBACH,WACc,UAAnBV,EAAUhM,MACVgM,EAAUU,kBAELV,EAAUE,YACbG,EAAO1a,QAAQ,SAASgb,GACtBX,EAAUY,SAASD,EACrB,GAGFR,GACAA,EAAO7T,OACT,CACF,C,oCCpCA,IAAIuU,EAAkB,EAAQ,MAE1BC,EAA4B,CAC9B,aAAc,OACd,YAAa,MACb,QAAW,QA2GbhB,EAAOC,QAjGP,SAAc5P,EAAM4Q,GAClB,IAAIC,EACFC,EACAC,EACAP,EACAX,EACAtY,EACAyZ,GAAU,EACPJ,IACHA,EAAU,CAAC,GAEbC,EAAQD,EAAQC,QAAS,EACzB,IAkDE,GAjDAE,EAAmBL,IAEnBF,EAAQnc,SAAS4c,cACjBpB,EAAYxb,SAASyb,gBAErBvY,EAAOlD,SAASa,cAAc,SACzBgc,YAAclR,EAEnBzI,EAAK4Z,WAAa,OAElB5Z,EAAK3C,MAAMwc,IAAM,QAEjB7Z,EAAK3C,MAAMiE,SAAW,QACtBtB,EAAK3C,MAAM4P,IAAM,EACjBjN,EAAK3C,MAAMyc,KAAO,mBAElB9Z,EAAK3C,MAAMwD,WAAa,MAExBb,EAAK3C,MAAM0c,iBAAmB,OAC9B/Z,EAAK3C,MAAM2c,cAAgB,OAC3Bha,EAAK3C,MAAM4c,aAAe,OAC1Bja,EAAK3C,MAAM2B,WAAa,OACxBgB,EAAKka,iBAAiB,OAAQ,SAAS9S,GAErC,GADAA,EAAEoB,kBACE6Q,EAAQjR,OAEV,GADAhB,EAAEmB,sBAC6B,IAApBnB,EAAE+S,cAA+B,CAC1Cb,GAASc,QAAQC,KAAK,iCACtBf,GAASc,QAAQC,KAAK,4BACtBxd,OAAOsd,cAAcG,YACrB,IAAIlS,EAASgR,EAA0BC,EAAQjR,SAAWgR,EAAmC,QAC7Fvc,OAAOsd,cAAcI,QAAQnS,EAAQK,EACvC,MACErB,EAAE+S,cAAcG,YAChBlT,EAAE+S,cAAcI,QAAQlB,EAAQjR,OAAQK,GAGxC4Q,EAAQzQ,SACVxB,EAAEmB,iBACF8Q,EAAQzQ,OAAOxB,EAAE+S,eAErB,GAEArd,SAAS0d,KAAKtF,YAAYlV,GAE1BiZ,EAAMwB,mBAAmBza,GACzBsY,EAAUY,SAASD,IAEFnc,SAAS4d,YAAY,QAEpC,MAAM,IAAIC,MAAM,iCAElBlB,GAAU,CACZ,CAAE,MAAOmB,GACPtB,GAASc,QAAQtR,MAAM,qCAAsC8R,GAC7DtB,GAASc,QAAQC,KAAK,4BACtB,IACExd,OAAOsd,cAAcI,QAAQlB,EAAQjR,QAAU,OAAQK,GACvD4Q,EAAQzQ,QAAUyQ,EAAQzQ,OAAO/L,OAAOsd,eACxCV,GAAU,CACZ,CAAE,MAAOmB,GACPtB,GAASc,QAAQtR,MAAM,uCAAwC8R,GAC/DtB,GAASc,QAAQtR,MAAM,0BACvByQ,EAjFN,SAAgBA,GACd,IAAIsB,GAAW,YAAYC,KAAKC,UAAUC,WAAa,IAAM,QAAU,KACvE,OAAOzB,EAAQ/T,QAAQ,gBAAiBqV,EAC1C,CA8EgBzS,CAAO,YAAaiR,EAAUA,EAAQE,QAnFjC,oCAoFf1c,OAAOoe,OAAO1B,EAAS9Q,EACzB,CACF,CAAE,QACI6P,IACkC,mBAAzBA,EAAU4C,YACnB5C,EAAU4C,YAAYjC,GAEtBX,EAAUU,mBAIVhZ,GACFlD,SAAS0d,KAAKlF,YAAYtV,GAE5BwZ,GACF,CAEA,OAAOC,CACT,C", "sources": ["webpack://autogentstudio/./node_modules/@ant-design/icons-svg/es/asn/EditOutlined.js", "webpack://autogentstudio/./node_modules/@ant-design/icons/es/icons/EditOutlined.js", "webpack://autogentstudio/./node_modules/rc-util/es/Dom/styleChecker.js", "webpack://autogentstudio/./node_modules/@ant-design/icons-svg/es/asn/EnterOutlined.js", "webpack://autogentstudio/./node_modules/@ant-design/icons/es/icons/EnterOutlined.js", "webpack://autogentstudio/./node_modules/antd/es/typography/style/mixins.js", "webpack://autogentstudio/./node_modules/antd/es/typography/style/index.js", "webpack://autogentstudio/./node_modules/antd/es/typography/Editable.js", "webpack://autogentstudio/./node_modules/antd/es/_util/toList.js", "webpack://autogentstudio/./node_modules/antd/es/typography/hooks/useCopyClick.js", "webpack://autogentstudio/./node_modules/antd/es/typography/hooks/useMergedConfig.js", "webpack://autogentstudio/./node_modules/antd/es/typography/hooks/usePrevious.js", "webpack://autogentstudio/./node_modules/antd/es/typography/hooks/useTooltipProps.js", "webpack://autogentstudio/./node_modules/antd/es/typography/Typography.js", "webpack://autogentstudio/./node_modules/@ant-design/icons-svg/es/asn/CopyOutlined.js", "webpack://autogentstudio/./node_modules/@ant-design/icons/es/icons/CopyOutlined.js", "webpack://autogentstudio/./node_modules/antd/es/typography/Base/util.js", "webpack://autogentstudio/./node_modules/antd/es/typography/Base/CopyBtn.js", "webpack://autogentstudio/./node_modules/antd/es/typography/Base/Ellipsis.js", "webpack://autogentstudio/./node_modules/antd/es/typography/Base/EllipsisTooltip.js", "webpack://autogentstudio/./node_modules/antd/es/typography/Base/index.js", "webpack://autogentstudio/./node_modules/antd/es/typography/Link.js", "webpack://autogentstudio/./node_modules/antd/es/typography/Paragraph.js", "webpack://autogentstudio/./node_modules/antd/es/typography/Text.js", "webpack://autogentstudio/./node_modules/antd/es/typography/Title.js", "webpack://autogentstudio/./node_modules/antd/es/typography/index.js", "webpack://autogentstudio/./node_modules/@ant-design/icons-svg/es/asn/CheckOutlined.js", "webpack://autogentstudio/./node_modules/@ant-design/icons/es/icons/CheckOutlined.js", "webpack://autogentstudio/./node_modules/toggle-selection/index.js", "webpack://autogentstudio/./node_modules/copy-to-clipboard/index.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar EditOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z\" } }] }, \"name\": \"edit\", \"theme\": \"outlined\" };\nexport default EditOutlined;\n", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport EditOutlinedSvg from \"@ant-design/icons-svg/es/asn/EditOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar EditOutlined = function EditOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: EditOutlinedSvg\n  }));\n};\n\n/**![edit](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI1Ny43IDc1MmMyIDAgNC0uMiA2LS41TDQzMS45IDcyMmMyLS40IDMuOS0xLjMgNS4zLTIuOGw0MjMuOS00MjMuOWE5Ljk2IDkuOTYgMCAwMDAtMTQuMUw2OTQuOSAxMTQuOWMtMS45LTEuOS00LjQtMi45LTcuMS0yLjlzLTUuMiAxLTcuMSAyLjlMMjU2LjggNTM4LjhjLTEuNSAxLjUtMi40IDMuMy0yLjggNS4zbC0yOS41IDE2OC4yYTMzLjUgMzMuNSAwIDAwOS40IDI5LjhjNi42IDYuNCAxNC45IDkuOSAyMy44IDkuOXptNjcuNC0xNzQuNEw2ODcuOCAyMTVsNzMuMyA3My4zLTM2Mi43IDM2Mi42LTg4LjkgMTUuNyAxNS42LTg5ek04ODAgODM2SDE0NGMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2MzZjMCA0LjQgMy42IDggOCA4aDc4NGM0LjQgMCA4LTMuNiA4LTh2LTM2YzAtMTcuNy0xNC4zLTMyLTMyLTMyeiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(EditOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'EditOutlined';\n}\nexport default RefIcon;", "import canUseDom from \"./canUseDom\";\nvar isStyleNameSupport = function isStyleNameSupport(styleName) {\n  if (canUseDom() && window.document.documentElement) {\n    var styleNameList = Array.isArray(styleName) ? styleName : [styleName];\n    var documentElement = window.document.documentElement;\n    return styleNameList.some(function (name) {\n      return name in documentElement.style;\n    });\n  }\n  return false;\n};\nvar isStyleValueSupport = function isStyleValueSupport(styleName, value) {\n  if (!isStyleNameSupport(styleName)) {\n    return false;\n  }\n  var ele = document.createElement('div');\n  var origin = ele.style[styleName];\n  ele.style[styleName] = value;\n  return ele.style[styleName] !== origin;\n};\nexport function isStyleSupport(styleName, styleValue) {\n  if (!Array.isArray(styleName) && styleValue !== undefined) {\n    return isStyleValueSupport(styleName, styleValue);\n  }\n  return isStyleNameSupport(styleName);\n}", "// This icon file is generated automatically.\nvar EnterOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M864 170h-60c-4.4 0-8 3.6-8 8v518H310v-73c0-6.7-7.8-10.5-13-6.3l-141.9 112a8 8 0 000 12.6l141.9 112c5.3 4.2 13 .4 13-6.3v-75h498c35.3 0 64-28.7 64-64V178c0-4.4-3.6-8-8-8z\" } }] }, \"name\": \"enter\", \"theme\": \"outlined\" };\nexport default EnterOutlined;\n", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport EnterOutlinedSvg from \"@ant-design/icons-svg/es/asn/EnterOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar EnterOutlined = function EnterOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: EnterOutlinedSvg\n  }));\n};\n\n/**![enter](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg2NCAxNzBoLTYwYy00LjQgMC04IDMuNi04IDh2NTE4SDMxMHYtNzNjMC02LjctNy44LTEwLjUtMTMtNi4zbC0xNDEuOSAxMTJhOCA4IDAgMDAwIDEyLjZsMTQxLjkgMTEyYzUuMyA0LjIgMTMgLjQgMTMtNi4zdi03NWg0OThjMzUuMyAwIDY0LTI4LjcgNjQtNjRWMTc4YzAtNC40LTMuNi04LTgtOHoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(EnterOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'EnterOutlined';\n}\nexport default RefIcon;", "/*\n.typography-title(@fontSize; @fontWeight; @lineHeight; @headingColor; @headingMarginBottom;) {\n margin-bottom: @headingMarginBottom;\n color: @headingColor;\n font-weight: @fontWeight;\n fontSize: @fontSize;\n line-height: @lineHeight;\n}\n*/\nimport { gold } from '@ant-design/colors';\nimport { unit } from '@ant-design/cssinjs';\nimport { operationUnit } from '../../style';\nconst getTitleStyle = (fontSize, lineHeight, color, token) => {\n  const {\n    titleMarginBottom,\n    fontWeightStrong\n  } = token;\n  return {\n    marginBottom: titleMarginBottom,\n    color,\n    fontWeight: fontWeightStrong,\n    fontSize,\n    lineHeight\n  };\n};\nexport const getTitleStyles = token => {\n  const headings = [1, 2, 3, 4, 5];\n  const styles = {};\n  headings.forEach(headingLevel => {\n    styles[`\n      h${headingLevel}&,\n      div&-h${headingLevel},\n      div&-h${headingLevel} > textarea,\n      h${headingLevel}\n    `] = getTitleStyle(token[`fontSizeHeading${headingLevel}`], token[`lineHeightHeading${headingLevel}`], token.colorTextHeading, token);\n  });\n  return styles;\n};\nexport const getLinkStyles = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    'a&, a': Object.assign(Object.assign({}, operationUnit(token)), {\n      userSelect: 'text',\n      [`&[disabled], &${componentCls}-disabled`]: {\n        color: token.colorTextDisabled,\n        cursor: 'not-allowed',\n        '&:active, &:hover': {\n          color: token.colorTextDisabled\n        },\n        '&:active': {\n          pointerEvents: 'none'\n        }\n      }\n    })\n  };\n};\nexport const getResetStyles = token => ({\n  code: {\n    margin: '0 0.2em',\n    paddingInline: '0.4em',\n    paddingBlock: '0.2em 0.1em',\n    fontSize: '85%',\n    fontFamily: token.fontFamilyCode,\n    background: 'rgba(150, 150, 150, 0.1)',\n    border: '1px solid rgba(100, 100, 100, 0.2)',\n    borderRadius: 3\n  },\n  kbd: {\n    margin: '0 0.2em',\n    paddingInline: '0.4em',\n    paddingBlock: '0.15em 0.1em',\n    fontSize: '90%',\n    fontFamily: token.fontFamilyCode,\n    background: 'rgba(150, 150, 150, 0.06)',\n    border: '1px solid rgba(100, 100, 100, 0.2)',\n    borderBottomWidth: 2,\n    borderRadius: 3\n  },\n  mark: {\n    padding: 0,\n    // FIXME hardcode in v4\n    backgroundColor: gold[2]\n  },\n  'u, ins': {\n    textDecoration: 'underline',\n    textDecorationSkipInk: 'auto'\n  },\n  's, del': {\n    textDecoration: 'line-through'\n  },\n  strong: {\n    fontWeight: token.fontWeightStrong\n  },\n  // list\n  'ul, ol': {\n    marginInline: 0,\n    marginBlock: '0 1em',\n    padding: 0,\n    li: {\n      marginInline: '20px 0',\n      marginBlock: 0,\n      paddingInline: '4px 0',\n      paddingBlock: 0\n    }\n  },\n  ul: {\n    listStyleType: 'circle',\n    ul: {\n      listStyleType: 'disc'\n    }\n  },\n  ol: {\n    listStyleType: 'decimal'\n  },\n  // pre & block\n  'pre, blockquote': {\n    margin: '1em 0'\n  },\n  pre: {\n    padding: '0.4em 0.6em',\n    whiteSpace: 'pre-wrap',\n    wordWrap: 'break-word',\n    background: 'rgba(150, 150, 150, 0.1)',\n    border: '1px solid rgba(100, 100, 100, 0.2)',\n    borderRadius: 3,\n    fontFamily: token.fontFamilyCode,\n    // Compatible for marked\n    code: {\n      display: 'inline',\n      margin: 0,\n      padding: 0,\n      fontSize: 'inherit',\n      fontFamily: 'inherit',\n      background: 'transparent',\n      border: 0\n    }\n  },\n  blockquote: {\n    paddingInline: '0.6em 0',\n    paddingBlock: 0,\n    borderInlineStart: '4px solid rgba(100, 100, 100, 0.2)',\n    opacity: 0.85\n  }\n});\nexport const getEditableStyles = token => {\n  const {\n    componentCls,\n    paddingSM\n  } = token;\n  const inputShift = paddingSM;\n  return {\n    '&-edit-content': {\n      position: 'relative',\n      'div&': {\n        insetInlineStart: token.calc(token.paddingSM).mul(-1).equal(),\n        marginTop: token.calc(inputShift).mul(-1).equal(),\n        marginBottom: `calc(1em - ${unit(inputShift)})`\n      },\n      [`${componentCls}-edit-content-confirm`]: {\n        position: 'absolute',\n        insetInlineEnd: token.calc(token.marginXS).add(2).equal(),\n        insetBlockEnd: token.marginXS,\n        color: token.colorIcon,\n        // default style\n        fontWeight: 'normal',\n        fontSize: token.fontSize,\n        fontStyle: 'normal',\n        pointerEvents: 'none'\n      },\n      textarea: {\n        margin: '0!important',\n        // Fix Editable Textarea flash in Firefox\n        MozTransition: 'none',\n        height: '1em'\n      }\n    }\n  };\n};\nexport const getCopyableStyles = token => ({\n  [`${token.componentCls}-copy-success`]: {\n    [`\n    &,\n    &:hover,\n    &:focus`]: {\n      color: token.colorSuccess\n    }\n  },\n  [`${token.componentCls}-copy-icon-only`]: {\n    marginInlineStart: 0\n  }\n});\nexport const getEllipsisStyles = () => ({\n  [`\n  a&-ellipsis,\n  span&-ellipsis\n  `]: {\n    display: 'inline-block',\n    maxWidth: '100%'\n  },\n  '&-ellipsis-single-line': {\n    whiteSpace: 'nowrap',\n    overflow: 'hidden',\n    textOverflow: 'ellipsis',\n    // https://blog.csdn.net/iefreer/article/details/50421025\n    'a&, span&': {\n      verticalAlign: 'bottom'\n    },\n    '> code': {\n      paddingBlock: 0,\n      maxWidth: 'calc(100% - 1.2em)',\n      display: 'inline-block',\n      overflow: 'hidden',\n      textOverflow: 'ellipsis',\n      verticalAlign: 'bottom',\n      // https://github.com/ant-design/ant-design/issues/45953\n      boxSizing: 'content-box'\n    }\n  },\n  '&-ellipsis-multiple-line': {\n    display: '-webkit-box',\n    overflow: 'hidden',\n    WebkitLineClamp: 3,\n    WebkitBoxOrient: 'vertical'\n  }\n});", "import { operationUnit } from '../../style';\nimport { genStyleHooks } from '../../theme/internal';\nimport { getCopyableStyles, getEditableStyles, getEllipsisStyles, getLinkStyles, getResetStyles, getTitleStyles } from './mixins';\nconst genTypographyStyle = token => {\n  const {\n    componentCls,\n    titleMarginTop\n  } = token;\n  return {\n    [componentCls]: Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({\n      color: token.colorText,\n      wordBreak: 'break-word',\n      lineHeight: token.lineHeight,\n      [`&${componentCls}-secondary`]: {\n        color: token.colorTextDescription\n      },\n      [`&${componentCls}-success`]: {\n        color: token.colorSuccessText\n      },\n      [`&${componentCls}-warning`]: {\n        color: token.colorWarningText\n      },\n      [`&${componentCls}-danger`]: {\n        color: token.colorErrorText,\n        'a&:active, a&:focus': {\n          color: token.colorErrorTextActive\n        },\n        'a&:hover': {\n          color: token.colorErrorTextHover\n        }\n      },\n      [`&${componentCls}-disabled`]: {\n        color: token.colorTextDisabled,\n        cursor: 'not-allowed',\n        userSelect: 'none'\n      },\n      [`\n        div&,\n        p\n      `]: {\n        marginBottom: '1em'\n      }\n    }, getTitleStyles(token)), {\n      [`\n      & + h1${componentCls},\n      & + h2${componentCls},\n      & + h3${componentCls},\n      & + h4${componentCls},\n      & + h5${componentCls}\n      `]: {\n        marginTop: titleMarginTop\n      },\n      [`\n      div,\n      ul,\n      li,\n      p,\n      h1,\n      h2,\n      h3,\n      h4,\n      h5`]: {\n        [`\n        + h1,\n        + h2,\n        + h3,\n        + h4,\n        + h5\n        `]: {\n          marginTop: titleMarginTop\n        }\n      }\n    }), getResetStyles(token)), getLinkStyles(token)), {\n      // Operation\n      [`\n        ${componentCls}-expand,\n        ${componentCls}-collapse,\n        ${componentCls}-edit,\n        ${componentCls}-copy\n      `]: Object.assign(Object.assign({}, operationUnit(token)), {\n        marginInlineStart: token.marginXXS\n      })\n    }), getEditableStyles(token)), getCopyableStyles(token)), getEllipsisStyles()), {\n      '&-rtl': {\n        direction: 'rtl'\n      }\n    })\n  };\n};\nexport const prepareComponentToken = () => ({\n  titleMarginTop: '1.2em',\n  titleMarginBottom: '0.5em'\n});\n// ============================== Export ==============================\nexport default genStyleHooks('Typography', token => [genTypographyStyle(token)], prepareComponentToken);", "\"use client\";\n\nimport * as React from 'react';\nimport EnterOutlined from \"@ant-design/icons/es/icons/EnterOutlined\";\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport { cloneElement } from '../_util/reactNode';\nimport TextArea from '../input/TextArea';\nimport useStyle from './style';\nconst Editable = props => {\n  const {\n    prefixCls,\n    'aria-label': ariaLabel,\n    className,\n    style,\n    direction,\n    maxLength,\n    autoSize = true,\n    value,\n    onSave,\n    onCancel,\n    onEnd,\n    component,\n    enterIcon = /*#__PURE__*/React.createElement(EnterOutlined, null)\n  } = props;\n  const ref = React.useRef(null);\n  const inComposition = React.useRef(false);\n  const lastKeyCode = React.useRef(null);\n  const [current, setCurrent] = React.useState(value);\n  React.useEffect(() => {\n    setCurrent(value);\n  }, [value]);\n  React.useEffect(() => {\n    var _a;\n    if ((_a = ref.current) === null || _a === void 0 ? void 0 : _a.resizableTextArea) {\n      const {\n        textArea\n      } = ref.current.resizableTextArea;\n      textArea.focus();\n      const {\n        length\n      } = textArea.value;\n      textArea.setSelectionRange(length, length);\n    }\n  }, []);\n  const onChange = ({\n    target\n  }) => {\n    setCurrent(target.value.replace(/[\\n\\r]/g, ''));\n  };\n  const onCompositionStart = () => {\n    inComposition.current = true;\n  };\n  const onCompositionEnd = () => {\n    inComposition.current = false;\n  };\n  const onKeyDown = ({\n    keyCode\n  }) => {\n    // We don't record keyCode when IME is using\n    if (inComposition.current) return;\n    lastKeyCode.current = keyCode;\n  };\n  const confirmChange = () => {\n    onSave(current.trim());\n  };\n  const onKeyUp = ({\n    keyCode,\n    ctrlKey,\n    altKey,\n    metaKey,\n    shiftKey\n  }) => {\n    // Check if it's a real key\n    if (lastKeyCode.current !== keyCode || inComposition.current || ctrlKey || altKey || metaKey || shiftKey) {\n      return;\n    }\n    if (keyCode === KeyCode.ENTER) {\n      confirmChange();\n      onEnd === null || onEnd === void 0 ? void 0 : onEnd();\n    } else if (keyCode === KeyCode.ESC) {\n      onCancel();\n    }\n  };\n  const onBlur = () => {\n    confirmChange();\n  };\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const textAreaClassName = classNames(prefixCls, `${prefixCls}-edit-content`, {\n    [`${prefixCls}-rtl`]: direction === 'rtl',\n    [`${prefixCls}-${component}`]: !!component\n  }, className, hashId, cssVarCls);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", {\n    className: textAreaClassName,\n    style: style\n  }, /*#__PURE__*/React.createElement(TextArea, {\n    ref: ref,\n    maxLength: maxLength,\n    value: current,\n    onChange: onChange,\n    onKeyDown: onKeyDown,\n    onKeyUp: onKeyUp,\n    onCompositionStart: onCompositionStart,\n    onCompositionEnd: onCompositionEnd,\n    onBlur: onBlur,\n    \"aria-label\": ariaLabel,\n    rows: 1,\n    autoSize: autoSize\n  }), enterIcon !== null ? cloneElement(enterIcon, {\n    className: `${prefixCls}-edit-content-confirm`\n  }) : null));\n};\nexport default Editable;", "const toList = (candidate, skipEmpty = false) => {\n  if (skipEmpty && (candidate === undefined || candidate === null)) {\n    return [];\n  }\n  return Array.isArray(candidate) ? candidate : [candidate];\n};\nexport default toList;", "var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nimport * as React from 'react';\nimport copy from 'copy-to-clipboard';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport toList from '../../_util/toList';\nconst useCopyClick = ({\n  copyConfig,\n  children\n}) => {\n  const [copied, setCopied] = React.useState(false);\n  const [copyLoading, setCopyLoading] = React.useState(false);\n  const copyIdRef = React.useRef(null);\n  const cleanCopyId = () => {\n    if (copyIdRef.current) {\n      clearTimeout(copyIdRef.current);\n    }\n  };\n  const copyOptions = {};\n  if (copyConfig.format) {\n    copyOptions.format = copyConfig.format;\n  }\n  React.useEffect(() => cleanCopyId, []);\n  // Keep copy action up to date\n  const onClick = useEvent(e => __awaiter(void 0, void 0, void 0, function* () {\n    var _a;\n    e === null || e === void 0 ? void 0 : e.preventDefault();\n    e === null || e === void 0 ? void 0 : e.stopPropagation();\n    setCopyLoading(true);\n    try {\n      const text = typeof copyConfig.text === 'function' ? yield copyConfig.text() : copyConfig.text;\n      copy(text || toList(children, true).join('') || '', copyOptions);\n      setCopyLoading(false);\n      setCopied(true);\n      // Trigger tips update\n      cleanCopyId();\n      copyIdRef.current = setTimeout(() => {\n        setCopied(false);\n      }, 3000);\n      (_a = copyConfig.onCopy) === null || _a === void 0 ? void 0 : _a.call(copyConfig, e);\n    } catch (error) {\n      setCopyLoading(false);\n      throw error;\n    }\n  }));\n  return {\n    copied,\n    copyLoading,\n    onClick\n  };\n};\nexport default useCopyClick;", "import * as React from 'react';\nexport default function useMergedConfig(propConfig, templateConfig) {\n  return React.useMemo(() => {\n    const support = !!propConfig;\n    return [support, Object.assign(Object.assign({}, templateConfig), support && typeof propConfig === 'object' ? propConfig : null)];\n  }, [propConfig]);\n}", "import { useEffect, useRef } from 'react';\nconst usePrevious = value => {\n  const ref = useRef(undefined);\n  useEffect(() => {\n    ref.current = value;\n  });\n  return ref.current;\n};\nexport default usePrevious;", "import { isValidElement, useMemo } from 'react';\nconst useTooltipProps = (tooltip, editConfigText, children) => useMemo(() => {\n  if (tooltip === true) {\n    return {\n      title: editConfigText !== null && editConfigText !== void 0 ? editConfigText : children\n    };\n  }\n  if (/*#__PURE__*/isValidElement(tooltip)) {\n    return {\n      title: tooltip\n    };\n  }\n  if (typeof tooltip === 'object') {\n    return Object.assign({\n      title: editConfigText !== null && editConfigText !== void 0 ? editConfigText : children\n    }, tooltip);\n  }\n  return {\n    title: tooltip\n  };\n}, [tooltip, editConfigText, children]);\nexport default useTooltipProps;", "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { composeRef } from \"rc-util/es/ref\";\nimport { devUseWarning } from '../_util/warning';\nimport { useComponentConfig } from '../config-provider/context';\nimport useStyle from './style';\nconst Typography = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      prefixCls: customizePrefixCls,\n      component: Component = 'article',\n      className,\n      rootClassName,\n      setContentRef,\n      children,\n      direction: typographyDirection,\n      style\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"component\", \"className\", \"rootClassName\", \"setContentRef\", \"children\", \"direction\", \"style\"]);\n  const {\n    getPrefixCls,\n    direction: contextDirection,\n    className: contextClassName,\n    style: contextStyle\n  } = useComponentConfig('typography');\n  const direction = typographyDirection !== null && typographyDirection !== void 0 ? typographyDirection : contextDirection;\n  const mergedRef = setContentRef ? composeRef(ref, setContentRef) : ref;\n  const prefixCls = getPrefixCls('typography', customizePrefixCls);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Typography');\n    warning.deprecated(!setContentRef, 'setContentRef', 'ref');\n  }\n  // Style\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const componentClassName = classNames(prefixCls, contextClassName, {\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, className, rootClassName, hashId, cssVarCls);\n  const mergedStyle = Object.assign(Object.assign({}, contextStyle), style);\n  return wrapCSSVar(\n  /*#__PURE__*/\n  // @ts-expect-error: Expression produces a union type that is too complex to represent.\n  React.createElement(Component, Object.assign({\n    className: componentClassName,\n    style: mergedStyle,\n    ref: mergedRef\n  }, restProps), children));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Typography.displayName = 'Typography';\n}\nexport default Typography;", "// This icon file is generated automatically.\nvar CopyOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.2c3.5 1.3 7.2 2 11 2H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM664 888H414V746c0-22.1-17.9-40-40-40H232V264h432v624z\" } }] }, \"name\": \"copy\", \"theme\": \"outlined\" };\nexport default CopyOutlined;\n", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport CopyOutlinedSvg from \"@ant-design/icons-svg/es/asn/CopyOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar CopyOutlined = function CopyOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: CopyOutlinedSvg\n  }));\n};\n\n/**![copy](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgzMiA2NEgyOTZjLTQuNCAwLTggMy42LTggOHY1NmMwIDQuNCAzLjYgOCA4IDhoNDk2djY4OGMwIDQuNCAzLjYgOCA4IDhoNTZjNC40IDAgOC0zLjYgOC04Vjk2YzAtMTcuNy0xNC4zLTMyLTMyLTMyek03MDQgMTkySDE5MmMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2NTMwLjdjMCA4LjUgMy40IDE2LjYgOS40IDIyLjZsMTczLjMgMTczLjNjMi4yIDIuMiA0LjcgNCA3LjQgNS41djEuOWg0LjJjMy41IDEuMyA3LjIgMiAxMSAySDcwNGMxNy43IDAgMzItMTQuMyAzMi0zMlYyMjRjMC0xNy43LTE0LjMtMzItMzItMzJ6TTM1MCA4NTYuMkwyNjMuOSA3NzBIMzUwdjg2LjJ6TTY2NCA4ODhINDE0Vjc0NmMwLTIyLjEtMTcuOS00MC00MC00MEgyMzJWMjY0aDQzMnY2MjR6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(CopyOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'CopyOutlined';\n}\nexport default RefIcon;", "export function toList(val) {\n  if (val === false) {\n    return [false, false];\n  }\n  return Array.isArray(val) ? val : [val];\n}\nexport function getNode(dom, defaultNode, needDom) {\n  if (dom === true || dom === undefined) {\n    return defaultNode;\n  }\n  return dom || needDom && defaultNode;\n}\n/**\n * Check for element is native ellipsis\n * ref:\n * - https://github.com/ant-design/ant-design/issues/50143\n * - https://github.com/ant-design/ant-design/issues/50414\n */\nexport function isEleEllipsis(ele) {\n  // Create a new div to get the size\n  const childDiv = document.createElement('em');\n  ele.appendChild(childDiv);\n  // For test case\n  if (process.env.NODE_ENV !== 'production') {\n    childDiv.className = 'ant-typography-css-ellipsis-content-measure';\n  }\n  const rect = ele.getBoundingClientRect();\n  const childRect = childDiv.getBoundingClientRect();\n  // Reset\n  ele.removeChild(childDiv);\n  // Range checker\n  return (\n    // Horizontal out of range\n    rect.left > childRect.left || childRect.right > rect.right ||\n    // Vertical out of range\n    rect.top > childRect.top || childRect.bottom > rect.bottom\n  );\n}\nexport const isValidText = val => ['string', 'number'].includes(typeof val);", "\"use client\";\n\nimport * as React from 'react';\nimport CheckOutlined from \"@ant-design/icons/es/icons/CheckOutlined\";\nimport CopyOutlined from \"@ant-design/icons/es/icons/CopyOutlined\";\nimport LoadingOutlined from \"@ant-design/icons/es/icons/LoadingOutlined\";\nimport classNames from 'classnames';\nimport Tooltip from '../../tooltip';\nimport { getNode, toList } from './util';\nconst CopyBtn = ({\n  prefixCls,\n  copied,\n  locale,\n  iconOnly,\n  tooltips,\n  icon,\n  tabIndex,\n  onCopy,\n  loading: btnLoading\n}) => {\n  const tooltipNodes = toList(tooltips);\n  const iconNodes = toList(icon);\n  const {\n    copied: copiedText,\n    copy: copyText\n  } = locale !== null && locale !== void 0 ? locale : {};\n  const systemStr = copied ? copiedText : copyText;\n  const copyTitle = getNode(tooltipNodes[copied ? 1 : 0], systemStr);\n  const ariaLabel = typeof copyTitle === 'string' ? copyTitle : systemStr;\n  return /*#__PURE__*/React.createElement(Tooltip, {\n    title: copyTitle\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    className: classNames(`${prefixCls}-copy`, {\n      [`${prefixCls}-copy-success`]: copied,\n      [`${prefixCls}-copy-icon-only`]: iconOnly\n    }),\n    onClick: onCopy,\n    \"aria-label\": ariaLabel,\n    tabIndex: tabIndex\n  }, copied ? getNode(iconNodes[1], /*#__PURE__*/React.createElement(CheckOutlined, null), true) : getNode(iconNodes[0], btnLoading ? /*#__PURE__*/React.createElement(LoadingOutlined, null) : /*#__PURE__*/React.createElement(CopyOutlined, null), true)));\n};\nexport default CopyBtn;", "\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport { isValidText } from './util';\nconst MeasureText = /*#__PURE__*/React.forwardRef(({\n  style,\n  children\n}, ref) => {\n  const spanRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => ({\n    isExceed: () => {\n      const span = spanRef.current;\n      return span.scrollHeight > span.clientHeight;\n    },\n    getHeight: () => spanRef.current.clientHeight\n  }));\n  return /*#__PURE__*/React.createElement(\"span\", {\n    \"aria-hidden\": true,\n    ref: spanRef,\n    style: Object.assign({\n      position: 'fixed',\n      display: 'block',\n      left: 0,\n      top: 0,\n      pointerEvents: 'none',\n      backgroundColor: 'rgba(255, 0, 0, 0.65)'\n    }, style)\n  }, children);\n});\nconst getNodesLen = nodeList => nodeList.reduce((totalLen, node) => totalLen + (isValidText(node) ? String(node).length : 1), 0);\nfunction sliceNodes(nodeList, len) {\n  let currLen = 0;\n  const currentNodeList = [];\n  for (let i = 0; i < nodeList.length; i += 1) {\n    // Match to return\n    if (currLen === len) {\n      return currentNodeList;\n    }\n    const node = nodeList[i];\n    const canCut = isValidText(node);\n    const nodeLen = canCut ? String(node).length : 1;\n    const nextLen = currLen + nodeLen;\n    // Exceed but current not which means we need cut this\n    // This will not happen on validate ReactElement\n    if (nextLen > len) {\n      const restLen = len - currLen;\n      currentNodeList.push(String(node).slice(0, restLen));\n      return currentNodeList;\n    }\n    currentNodeList.push(node);\n    currLen = nextLen;\n  }\n  return nodeList;\n}\n// Measure for the `text` is exceed the `rows` or not\nconst STATUS_MEASURE_NONE = 0;\nconst STATUS_MEASURE_PREPARE = 1;\nconst STATUS_MEASURE_START = 2;\nconst STATUS_MEASURE_NEED_ELLIPSIS = 3;\nconst STATUS_MEASURE_NO_NEED_ELLIPSIS = 4;\nconst lineClipStyle = {\n  display: '-webkit-box',\n  overflow: 'hidden',\n  WebkitBoxOrient: 'vertical'\n};\nexport default function EllipsisMeasure(props) {\n  const {\n    enableMeasure,\n    width,\n    text,\n    children,\n    rows,\n    expanded,\n    miscDeps,\n    onEllipsis\n  } = props;\n  const nodeList = React.useMemo(() => toArray(text), [text]);\n  const nodeLen = React.useMemo(() => getNodesLen(nodeList), [text]);\n  // ========================= Full Content =========================\n  // Used for measure only, which means it's always render as no need ellipsis\n  const fullContent = React.useMemo(() => children(nodeList, false), [text]);\n  // ========================= Cut Content ==========================\n  const [ellipsisCutIndex, setEllipsisCutIndex] = React.useState(null);\n  const cutMidRef = React.useRef(null);\n  // ========================= NeedEllipsis =========================\n  const measureWhiteSpaceRef = React.useRef(null);\n  const needEllipsisRef = React.useRef(null);\n  // Measure for `rows-1` height, to avoid operation exceed the line height\n  const descRowsEllipsisRef = React.useRef(null);\n  const symbolRowEllipsisRef = React.useRef(null);\n  const [canEllipsis, setCanEllipsis] = React.useState(false);\n  const [needEllipsis, setNeedEllipsis] = React.useState(STATUS_MEASURE_NONE);\n  const [ellipsisHeight, setEllipsisHeight] = React.useState(0);\n  const [parentWhiteSpace, setParentWhiteSpace] = React.useState(null);\n  // Trigger start measure\n  useLayoutEffect(() => {\n    if (enableMeasure && width && nodeLen) {\n      setNeedEllipsis(STATUS_MEASURE_PREPARE);\n    } else {\n      setNeedEllipsis(STATUS_MEASURE_NONE);\n    }\n  }, [width, text, rows, enableMeasure, nodeList]);\n  // Measure process\n  useLayoutEffect(() => {\n    var _a, _b, _c, _d;\n    if (needEllipsis === STATUS_MEASURE_PREPARE) {\n      setNeedEllipsis(STATUS_MEASURE_START);\n      // Parent ref `white-space`\n      const nextWhiteSpace = measureWhiteSpaceRef.current && getComputedStyle(measureWhiteSpaceRef.current).whiteSpace;\n      setParentWhiteSpace(nextWhiteSpace);\n    } else if (needEllipsis === STATUS_MEASURE_START) {\n      const isOverflow = !!((_a = needEllipsisRef.current) === null || _a === void 0 ? void 0 : _a.isExceed());\n      setNeedEllipsis(isOverflow ? STATUS_MEASURE_NEED_ELLIPSIS : STATUS_MEASURE_NO_NEED_ELLIPSIS);\n      setEllipsisCutIndex(isOverflow ? [0, nodeLen] : null);\n      setCanEllipsis(isOverflow);\n      // Get the basic height of ellipsis rows\n      const baseRowsEllipsisHeight = ((_b = needEllipsisRef.current) === null || _b === void 0 ? void 0 : _b.getHeight()) || 0;\n      // Get the height of `rows - 1` + symbol height\n      const descRowsEllipsisHeight = rows === 1 ? 0 : ((_c = descRowsEllipsisRef.current) === null || _c === void 0 ? void 0 : _c.getHeight()) || 0;\n      const symbolRowEllipsisHeight = ((_d = symbolRowEllipsisRef.current) === null || _d === void 0 ? void 0 : _d.getHeight()) || 0;\n      const maxRowsHeight = Math.max(baseRowsEllipsisHeight,\n      // height of rows with ellipsis\n      descRowsEllipsisHeight + symbolRowEllipsisHeight);\n      setEllipsisHeight(maxRowsHeight + 1);\n      onEllipsis(isOverflow);\n    }\n  }, [needEllipsis]);\n  // ========================= Cut Measure ==========================\n  const cutMidIndex = ellipsisCutIndex ? Math.ceil((ellipsisCutIndex[0] + ellipsisCutIndex[1]) / 2) : 0;\n  useLayoutEffect(() => {\n    var _a;\n    const [minIndex, maxIndex] = ellipsisCutIndex || [0, 0];\n    if (minIndex !== maxIndex) {\n      const midHeight = ((_a = cutMidRef.current) === null || _a === void 0 ? void 0 : _a.getHeight()) || 0;\n      const isOverflow = midHeight > ellipsisHeight;\n      let targetMidIndex = cutMidIndex;\n      if (maxIndex - minIndex === 1) {\n        targetMidIndex = isOverflow ? minIndex : maxIndex;\n      }\n      setEllipsisCutIndex(isOverflow ? [minIndex, targetMidIndex] : [targetMidIndex, maxIndex]);\n    }\n  }, [ellipsisCutIndex, cutMidIndex]);\n  // ========================= Text Content =========================\n  const finalContent = React.useMemo(() => {\n    // Skip everything if `enableMeasure` is disabled\n    if (!enableMeasure) {\n      return children(nodeList, false);\n    }\n    if (needEllipsis !== STATUS_MEASURE_NEED_ELLIPSIS || !ellipsisCutIndex || ellipsisCutIndex[0] !== ellipsisCutIndex[1]) {\n      const content = children(nodeList, false);\n      // Limit the max line count to avoid scrollbar blink unless no need ellipsis\n      // https://github.com/ant-design/ant-design/issues/42958\n      if ([STATUS_MEASURE_NO_NEED_ELLIPSIS, STATUS_MEASURE_NONE].includes(needEllipsis)) {\n        return content;\n      }\n      return /*#__PURE__*/React.createElement(\"span\", {\n        style: Object.assign(Object.assign({}, lineClipStyle), {\n          WebkitLineClamp: rows\n        })\n      }, content);\n    }\n    return children(expanded ? nodeList : sliceNodes(nodeList, ellipsisCutIndex[0]), canEllipsis);\n  }, [expanded, needEllipsis, ellipsisCutIndex, nodeList].concat(_toConsumableArray(miscDeps)));\n  // ============================ Render ============================\n  const measureStyle = {\n    width,\n    margin: 0,\n    padding: 0,\n    whiteSpace: parentWhiteSpace === 'nowrap' ? 'normal' : 'inherit'\n  };\n  return /*#__PURE__*/React.createElement(React.Fragment, null, finalContent, needEllipsis === STATUS_MEASURE_START && (/*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(MeasureText, {\n    style: Object.assign(Object.assign(Object.assign({}, measureStyle), lineClipStyle), {\n      WebkitLineClamp: rows\n    }),\n    ref: needEllipsisRef\n  }, fullContent), /*#__PURE__*/React.createElement(MeasureText, {\n    style: Object.assign(Object.assign(Object.assign({}, measureStyle), lineClipStyle), {\n      WebkitLineClamp: rows - 1\n    }),\n    ref: descRowsEllipsisRef\n  }, fullContent), /*#__PURE__*/React.createElement(MeasureText, {\n    style: Object.assign(Object.assign(Object.assign({}, measureStyle), lineClipStyle), {\n      WebkitLineClamp: 1\n    }),\n    ref: symbolRowEllipsisRef\n  }, children([], true)))), needEllipsis === STATUS_MEASURE_NEED_ELLIPSIS && ellipsisCutIndex && ellipsisCutIndex[0] !== ellipsisCutIndex[1] && (/*#__PURE__*/React.createElement(MeasureText, {\n    style: Object.assign(Object.assign({}, measureStyle), {\n      top: 400\n    }),\n    ref: cutMidRef\n  }, children(sliceNodes(nodeList, cutMidIndex), true))), needEllipsis === STATUS_MEASURE_PREPARE && (/*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      whiteSpace: 'inherit'\n    },\n    ref: measureWhiteSpaceRef\n  })));\n}", "\"use client\";\n\nimport * as React from 'react';\nimport Tooltip from '../../tooltip';\nconst EllipsisTooltip = ({\n  enableEllipsis,\n  isEllipsis,\n  children,\n  tooltipProps\n}) => {\n  if (!(tooltipProps === null || tooltipProps === void 0 ? void 0 : tooltipProps.title) || !enableEllipsis) {\n    return children;\n  }\n  return /*#__PURE__*/React.createElement(Tooltip, Object.assign({\n    open: isEllipsis ? undefined : false\n  }, tooltipProps), children);\n};\nif (process.env.NODE_ENV !== 'production') {\n  EllipsisTooltip.displayName = 'EllipsisTooltip';\n}\nexport default EllipsisTooltip;", "\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport EditOutlined from \"@ant-design/icons/es/icons/EditOutlined\";\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport omit from \"rc-util/es/omit\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport { isStyleSupport } from '../../_util/styleChecker';\nimport { ConfigContext } from '../../config-provider';\nimport useLocale from '../../locale/useLocale';\nimport Tooltip from '../../tooltip';\nimport Editable from '../Editable';\nimport useCopyClick from '../hooks/useCopyClick';\nimport useMergedConfig from '../hooks/useMergedConfig';\nimport usePrevious from '../hooks/usePrevious';\nimport useTooltipProps from '../hooks/useTooltipProps';\nimport Typography from '../Typography';\nimport CopyBtn from './CopyBtn';\nimport Ellipsis from './Ellipsis';\nimport EllipsisTooltip from './EllipsisTooltip';\nimport { isEleEllipsis, isValidText } from './util';\nfunction wrapperDecorations({\n  mark,\n  code,\n  underline,\n  delete: del,\n  strong,\n  keyboard,\n  italic\n}, content) {\n  let currentContent = content;\n  function wrap(tag, needed) {\n    if (!needed) {\n      return;\n    }\n    currentContent = /*#__PURE__*/React.createElement(tag, {}, currentContent);\n  }\n  wrap('strong', strong);\n  wrap('u', underline);\n  wrap('del', del);\n  wrap('code', code);\n  wrap('mark', mark);\n  wrap('kbd', keyboard);\n  wrap('i', italic);\n  return currentContent;\n}\nconst ELLIPSIS_STR = '...';\nconst DECORATION_PROPS = ['delete', 'mark', 'code', 'underline', 'strong', 'keyboard', 'italic'];\nconst Base = /*#__PURE__*/React.forwardRef((props, ref) => {\n  var _a;\n  const {\n      prefixCls: customizePrefixCls,\n      className,\n      style,\n      type,\n      disabled,\n      children,\n      ellipsis,\n      editable,\n      copyable,\n      component,\n      title\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"className\", \"style\", \"type\", \"disabled\", \"children\", \"ellipsis\", \"editable\", \"copyable\", \"component\", \"title\"]);\n  const {\n    getPrefixCls,\n    direction\n  } = React.useContext(ConfigContext);\n  const [textLocale] = useLocale('Text');\n  const typographyRef = React.useRef(null);\n  const editIconRef = React.useRef(null);\n  // ============================ MISC ============================\n  const prefixCls = getPrefixCls('typography', customizePrefixCls);\n  const textProps = omit(restProps, DECORATION_PROPS);\n  // ========================== Editable ==========================\n  const [enableEdit, editConfig] = useMergedConfig(editable);\n  const [editing, setEditing] = useMergedState(false, {\n    value: editConfig.editing\n  });\n  const {\n    triggerType = ['icon']\n  } = editConfig;\n  const triggerEdit = edit => {\n    var _a;\n    if (edit) {\n      (_a = editConfig.onStart) === null || _a === void 0 ? void 0 : _a.call(editConfig);\n    }\n    setEditing(edit);\n  };\n  // Focus edit icon when back\n  const prevEditing = usePrevious(editing);\n  useLayoutEffect(() => {\n    var _a;\n    if (!editing && prevEditing) {\n      (_a = editIconRef.current) === null || _a === void 0 ? void 0 : _a.focus();\n    }\n  }, [editing]);\n  const onEditClick = e => {\n    e === null || e === void 0 ? void 0 : e.preventDefault();\n    triggerEdit(true);\n  };\n  const onEditChange = value => {\n    var _a;\n    (_a = editConfig.onChange) === null || _a === void 0 ? void 0 : _a.call(editConfig, value);\n    triggerEdit(false);\n  };\n  const onEditCancel = () => {\n    var _a;\n    (_a = editConfig.onCancel) === null || _a === void 0 ? void 0 : _a.call(editConfig);\n    triggerEdit(false);\n  };\n  // ========================== Copyable ==========================\n  const [enableCopy, copyConfig] = useMergedConfig(copyable);\n  const {\n    copied,\n    copyLoading,\n    onClick: onCopyClick\n  } = useCopyClick({\n    copyConfig,\n    children\n  });\n  // ========================== Ellipsis ==========================\n  const [isLineClampSupport, setIsLineClampSupport] = React.useState(false);\n  const [isTextOverflowSupport, setIsTextOverflowSupport] = React.useState(false);\n  const [isJsEllipsis, setIsJsEllipsis] = React.useState(false);\n  const [isNativeEllipsis, setIsNativeEllipsis] = React.useState(false);\n  const [isNativeVisible, setIsNativeVisible] = React.useState(true);\n  const [enableEllipsis, ellipsisConfig] = useMergedConfig(ellipsis, {\n    expandable: false,\n    symbol: isExpanded => isExpanded ? textLocale === null || textLocale === void 0 ? void 0 : textLocale.collapse : textLocale === null || textLocale === void 0 ? void 0 : textLocale.expand\n  });\n  const [expanded, setExpanded] = useMergedState(ellipsisConfig.defaultExpanded || false, {\n    value: ellipsisConfig.expanded\n  });\n  const mergedEnableEllipsis = enableEllipsis && (!expanded || ellipsisConfig.expandable === 'collapsible');\n  // Shared prop to reduce bundle size\n  const {\n    rows = 1\n  } = ellipsisConfig;\n  const needMeasureEllipsis = React.useMemo(() =>\n  // Disable ellipsis\n  mergedEnableEllipsis && (\n  // Provide suffix\n  ellipsisConfig.suffix !== undefined || ellipsisConfig.onEllipsis ||\n  // Can't use css ellipsis since we need to provide the place for button\n  ellipsisConfig.expandable || enableEdit || enableCopy), [mergedEnableEllipsis, ellipsisConfig, enableEdit, enableCopy]);\n  useLayoutEffect(() => {\n    if (enableEllipsis && !needMeasureEllipsis) {\n      setIsLineClampSupport(isStyleSupport('webkitLineClamp'));\n      setIsTextOverflowSupport(isStyleSupport('textOverflow'));\n    }\n  }, [needMeasureEllipsis, enableEllipsis]);\n  const [cssEllipsis, setCssEllipsis] = React.useState(mergedEnableEllipsis);\n  const canUseCssEllipsis = React.useMemo(() => {\n    if (needMeasureEllipsis) {\n      return false;\n    }\n    if (rows === 1) {\n      return isTextOverflowSupport;\n    }\n    return isLineClampSupport;\n  }, [needMeasureEllipsis, isTextOverflowSupport, isLineClampSupport]);\n  // We use effect to change from css ellipsis to js ellipsis.\n  // To make SSR still can see the ellipsis.\n  useLayoutEffect(() => {\n    setCssEllipsis(canUseCssEllipsis && mergedEnableEllipsis);\n  }, [canUseCssEllipsis, mergedEnableEllipsis]);\n  const isMergedEllipsis = mergedEnableEllipsis && (cssEllipsis ? isNativeEllipsis : isJsEllipsis);\n  const cssTextOverflow = mergedEnableEllipsis && rows === 1 && cssEllipsis;\n  const cssLineClamp = mergedEnableEllipsis && rows > 1 && cssEllipsis;\n  // >>>>> Expand\n  const onExpandClick = (e, info) => {\n    var _a;\n    setExpanded(info.expanded);\n    (_a = ellipsisConfig.onExpand) === null || _a === void 0 ? void 0 : _a.call(ellipsisConfig, e, info);\n  };\n  const [ellipsisWidth, setEllipsisWidth] = React.useState(0);\n  const onResize = ({\n    offsetWidth\n  }) => {\n    setEllipsisWidth(offsetWidth);\n  };\n  // >>>>> JS Ellipsis\n  const onJsEllipsis = jsEllipsis => {\n    var _a;\n    setIsJsEllipsis(jsEllipsis);\n    // Trigger if changed\n    if (isJsEllipsis !== jsEllipsis) {\n      (_a = ellipsisConfig.onEllipsis) === null || _a === void 0 ? void 0 : _a.call(ellipsisConfig, jsEllipsis);\n    }\n  };\n  // >>>>> Native ellipsis\n  React.useEffect(() => {\n    const textEle = typographyRef.current;\n    if (enableEllipsis && cssEllipsis && textEle) {\n      const currentEllipsis = isEleEllipsis(textEle);\n      if (isNativeEllipsis !== currentEllipsis) {\n        setIsNativeEllipsis(currentEllipsis);\n      }\n    }\n  }, [enableEllipsis, cssEllipsis, children, cssLineClamp, isNativeVisible, ellipsisWidth]);\n  // https://github.com/ant-design/ant-design/issues/36786\n  // Use IntersectionObserver to check if element is invisible\n  React.useEffect(() => {\n    const textEle = typographyRef.current;\n    if (typeof IntersectionObserver === 'undefined' || !textEle || !cssEllipsis || !mergedEnableEllipsis) {\n      return;\n    }\n    /* eslint-disable-next-line compat/compat */\n    const observer = new IntersectionObserver(() => {\n      setIsNativeVisible(!!textEle.offsetParent);\n    });\n    observer.observe(textEle);\n    return () => {\n      observer.disconnect();\n    };\n  }, [cssEllipsis, mergedEnableEllipsis]);\n  // ========================== Tooltip ===========================\n  const tooltipProps = useTooltipProps(ellipsisConfig.tooltip, editConfig.text, children);\n  const topAriaLabel = React.useMemo(() => {\n    if (!enableEllipsis || cssEllipsis) {\n      return undefined;\n    }\n    return [editConfig.text, children, title, tooltipProps.title].find(isValidText);\n  }, [enableEllipsis, cssEllipsis, title, tooltipProps.title, isMergedEllipsis]);\n  // =========================== Render ===========================\n  // >>>>>>>>>>> Editing input\n  if (editing) {\n    return /*#__PURE__*/React.createElement(Editable, {\n      value: (_a = editConfig.text) !== null && _a !== void 0 ? _a : typeof children === 'string' ? children : '',\n      onSave: onEditChange,\n      onCancel: onEditCancel,\n      onEnd: editConfig.onEnd,\n      prefixCls: prefixCls,\n      className: className,\n      style: style,\n      direction: direction,\n      component: component,\n      maxLength: editConfig.maxLength,\n      autoSize: editConfig.autoSize,\n      enterIcon: editConfig.enterIcon\n    });\n  }\n  // >>>>>>>>>>> Typography\n  // Expand\n  const renderExpand = () => {\n    const {\n      expandable,\n      symbol\n    } = ellipsisConfig;\n    return expandable ? (/*#__PURE__*/React.createElement(\"button\", {\n      type: \"button\",\n      key: \"expand\",\n      className: `${prefixCls}-${expanded ? 'collapse' : 'expand'}`,\n      onClick: e => onExpandClick(e, {\n        expanded: !expanded\n      }),\n      \"aria-label\": expanded ? textLocale.collapse : textLocale === null || textLocale === void 0 ? void 0 : textLocale.expand\n    }, typeof symbol === 'function' ? symbol(expanded) : symbol)) : null;\n  };\n  // Edit\n  const renderEdit = () => {\n    if (!enableEdit) {\n      return;\n    }\n    const {\n      icon,\n      tooltip,\n      tabIndex\n    } = editConfig;\n    const editTitle = toArray(tooltip)[0] || (textLocale === null || textLocale === void 0 ? void 0 : textLocale.edit);\n    const ariaLabel = typeof editTitle === 'string' ? editTitle : '';\n    return triggerType.includes('icon') ? (/*#__PURE__*/React.createElement(Tooltip, {\n      key: \"edit\",\n      title: tooltip === false ? '' : editTitle\n    }, /*#__PURE__*/React.createElement(\"button\", {\n      type: \"button\",\n      ref: editIconRef,\n      className: `${prefixCls}-edit`,\n      onClick: onEditClick,\n      \"aria-label\": ariaLabel,\n      tabIndex: tabIndex\n    }, icon || /*#__PURE__*/React.createElement(EditOutlined, {\n      role: \"button\"\n    })))) : null;\n  };\n  // Copy\n  const renderCopy = () => {\n    if (!enableCopy) {\n      return null;\n    }\n    return /*#__PURE__*/React.createElement(CopyBtn, Object.assign({\n      key: \"copy\"\n    }, copyConfig, {\n      prefixCls: prefixCls,\n      copied: copied,\n      locale: textLocale,\n      onCopy: onCopyClick,\n      loading: copyLoading,\n      iconOnly: children === null || children === undefined\n    }));\n  };\n  const renderOperations = canEllipsis => [canEllipsis && renderExpand(), renderEdit(), renderCopy()];\n  const renderEllipsis = canEllipsis => [canEllipsis && !expanded && (/*#__PURE__*/React.createElement(\"span\", {\n    \"aria-hidden\": true,\n    key: \"ellipsis\"\n  }, ELLIPSIS_STR)), ellipsisConfig.suffix, renderOperations(canEllipsis)];\n  return /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onResize,\n    disabled: !mergedEnableEllipsis\n  }, resizeRef => (/*#__PURE__*/React.createElement(EllipsisTooltip, {\n    tooltipProps: tooltipProps,\n    enableEllipsis: mergedEnableEllipsis,\n    isEllipsis: isMergedEllipsis\n  }, /*#__PURE__*/React.createElement(Typography, Object.assign({\n    className: classNames({\n      [`${prefixCls}-${type}`]: type,\n      [`${prefixCls}-disabled`]: disabled,\n      [`${prefixCls}-ellipsis`]: enableEllipsis,\n      [`${prefixCls}-ellipsis-single-line`]: cssTextOverflow,\n      [`${prefixCls}-ellipsis-multiple-line`]: cssLineClamp\n    }, className),\n    prefixCls: customizePrefixCls,\n    style: Object.assign(Object.assign({}, style), {\n      WebkitLineClamp: cssLineClamp ? rows : undefined\n    }),\n    component: component,\n    ref: composeRef(resizeRef, typographyRef, ref),\n    direction: direction,\n    onClick: triggerType.includes('text') ? onEditClick : undefined,\n    \"aria-label\": topAriaLabel === null || topAriaLabel === void 0 ? void 0 : topAriaLabel.toString(),\n    title: title\n  }, textProps), /*#__PURE__*/React.createElement(Ellipsis, {\n    enableMeasure: mergedEnableEllipsis && !cssEllipsis,\n    text: children,\n    rows: rows,\n    width: ellipsisWidth,\n    onEllipsis: onJsEllipsis,\n    expanded: expanded,\n    miscDeps: [copied, expanded, copyLoading, enableEdit, enableCopy, textLocale].concat(_toConsumableArray(DECORATION_PROPS.map(key => props[key])))\n  }, (node, canEllipsis) => wrapperDecorations(props, /*#__PURE__*/React.createElement(React.Fragment, null, node.length > 0 && canEllipsis && !expanded && topAriaLabel ? (/*#__PURE__*/React.createElement(\"span\", {\n    key: \"show-content\",\n    \"aria-hidden\": true\n  }, node)) : node, renderEllipsis(canEllipsis))))))));\n});\nexport default Base;", "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport { devUseWarning } from '../_util/warning';\nimport Base from './Base';\nconst Link = /*#__PURE__*/React.forwardRef((_a, ref) => {\n  var {\n      ellipsis,\n      rel\n    } = _a,\n    restProps = __rest(_a, [\"ellipsis\", \"rel\"]);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Typography.Link');\n    process.env.NODE_ENV !== \"production\" ? warning(typeof ellipsis !== 'object', 'usage', '`ellipsis` only supports boolean value.') : void 0;\n  }\n  const mergedProps = Object.assign(Object.assign({}, restProps), {\n    rel: rel === undefined && restProps.target === '_blank' ? 'noopener noreferrer' : rel\n  });\n  // @ts-expect-error: https://github.com/ant-design/ant-design/issues/26622\n  delete mergedProps.navigate;\n  return /*#__PURE__*/React.createElement(Base, Object.assign({}, mergedProps, {\n    ref: ref,\n    ellipsis: !!ellipsis,\n    component: \"a\"\n  }));\n});\nexport default Link;", "\"use client\";\n\nimport * as React from 'react';\nimport Base from './Base';\nconst Paragraph = /*#__PURE__*/React.forwardRef((props, ref) => (/*#__PURE__*/React.createElement(Base, Object.assign({\n  ref: ref\n}, props, {\n  component: \"div\"\n}))));\nexport default Paragraph;", "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport omit from \"rc-util/es/omit\";\nimport { devUseWarning } from '../_util/warning';\nimport Base from './Base';\nconst Text = (_a, ref) => {\n  var {\n      ellipsis\n    } = _a,\n    restProps = __rest(_a, [\"ellipsis\"]);\n  const mergedEllipsis = React.useMemo(() => {\n    if (ellipsis && typeof ellipsis === 'object') {\n      return omit(ellipsis, ['expandable', 'rows']);\n    }\n    return ellipsis;\n  }, [ellipsis]);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Typography.Text');\n    process.env.NODE_ENV !== \"production\" ? warning(typeof ellipsis !== 'object' || !ellipsis || !('expandable' in ellipsis) && !('rows' in ellipsis), 'usage', '`ellipsis` do not support `expandable` or `rows` props.') : void 0;\n  }\n  return /*#__PURE__*/React.createElement(Base, Object.assign({\n    ref: ref\n  }, restProps, {\n    ellipsis: mergedEllipsis,\n    component: \"span\"\n  }));\n};\nexport default /*#__PURE__*/React.forwardRef(Text);", "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport { devUseWarning } from '../_util/warning';\nimport Base from './Base';\nconst TITLE_ELE_LIST = [1, 2, 3, 4, 5];\nconst Title = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      level = 1\n    } = props,\n    restProps = __rest(props, [\"level\"]);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Typography.Title');\n    process.env.NODE_ENV !== \"production\" ? warning(TITLE_ELE_LIST.includes(level), 'usage', 'Title only accept `1 | 2 | 3 | 4 | 5` as `level` value. And `5` need 4.6.0+ version.') : void 0;\n  }\n  const component = TITLE_ELE_LIST.includes(level) ? `h${level}` : `h1`;\n  return /*#__PURE__*/React.createElement(Base, Object.assign({\n    ref: ref\n  }, restProps, {\n    component: component\n  }));\n});\nexport default Title;", "\"use client\";\n\nimport Link from './<PERSON>';\nimport Paragraph from './Paragraph';\nimport Text from './Text';\nimport Title from './Title';\nimport OriginTypography from './Typography';\nconst Typography = OriginTypography;\nTypography.Text = Text;\nTypography.Link = Link;\nTypography.Title = Title;\nTypography.Paragraph = Paragraph;\nexport default Typography;", "// This icon file is generated automatically.\nvar CheckOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z\" } }] }, \"name\": \"check\", \"theme\": \"outlined\" };\nexport default CheckOutlined;\n", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport CheckOutlinedSvg from \"@ant-design/icons-svg/es/asn/CheckOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar CheckOutlined = function CheckOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: CheckOutlinedSvg\n  }));\n};\n\n/**![check](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkxMiAxOTBoLTY5LjljLTkuOCAwLTE5LjEgNC41LTI1LjEgMTIuMkw0MDQuNyA3MjQuNSAyMDcgNDc0YTMyIDMyIDAgMDAtMjUuMS0xMi4ySDExMmMtNi43IDAtMTAuNCA3LjctNi4zIDEyLjlsMjczLjkgMzQ3YzEyLjggMTYuMiAzNy40IDE2LjIgNTAuMyAwbDQ4OC40LTYxOC45YzQuMS01LjEuNC0xMi44LTYuMy0xMi44eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(CheckOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'CheckOutlined';\n}\nexport default RefIcon;", "\nmodule.exports = function () {\n  var selection = document.getSelection();\n  if (!selection.rangeCount) {\n    return function () {};\n  }\n  var active = document.activeElement;\n\n  var ranges = [];\n  for (var i = 0; i < selection.rangeCount; i++) {\n    ranges.push(selection.getRangeAt(i));\n  }\n\n  switch (active.tagName.toUpperCase()) { // .toUpperCase handles XHTML\n    case 'INPUT':\n    case 'TEXTAREA':\n      active.blur();\n      break;\n\n    default:\n      active = null;\n      break;\n  }\n\n  selection.removeAllRanges();\n  return function () {\n    selection.type === 'Caret' &&\n    selection.removeAllRanges();\n\n    if (!selection.rangeCount) {\n      ranges.forEach(function(range) {\n        selection.addRange(range);\n      });\n    }\n\n    active &&\n    active.focus();\n  };\n};\n", "\"use strict\";\n\nvar deselectCurrent = require(\"toggle-selection\");\n\nvar clipboardToIE11Formatting = {\n  \"text/plain\": \"Text\",\n  \"text/html\": \"Url\",\n  \"default\": \"Text\"\n}\n\nvar defaultMessage = \"Copy to clipboard: #{key}, Enter\";\n\nfunction format(message) {\n  var copyKey = (/mac os x/i.test(navigator.userAgent) ? \"⌘\" : \"Ctrl\") + \"+C\";\n  return message.replace(/#{\\s*key\\s*}/g, copyKey);\n}\n\nfunction copy(text, options) {\n  var debug,\n    message,\n    reselectPrevious,\n    range,\n    selection,\n    mark,\n    success = false;\n  if (!options) {\n    options = {};\n  }\n  debug = options.debug || false;\n  try {\n    reselectPrevious = deselectCurrent();\n\n    range = document.createRange();\n    selection = document.getSelection();\n\n    mark = document.createElement(\"span\");\n    mark.textContent = text;\n    // avoid screen readers from reading out loud the text\n    mark.ariaHidden = \"true\"\n    // reset user styles for span element\n    mark.style.all = \"unset\";\n    // prevents scrolling to the end of the page\n    mark.style.position = \"fixed\";\n    mark.style.top = 0;\n    mark.style.clip = \"rect(0, 0, 0, 0)\";\n    // used to preserve spaces and line breaks\n    mark.style.whiteSpace = \"pre\";\n    // do not inherit user-select (it may be `none`)\n    mark.style.webkitUserSelect = \"text\";\n    mark.style.MozUserSelect = \"text\";\n    mark.style.msUserSelect = \"text\";\n    mark.style.userSelect = \"text\";\n    mark.addEventListener(\"copy\", function(e) {\n      e.stopPropagation();\n      if (options.format) {\n        e.preventDefault();\n        if (typeof e.clipboardData === \"undefined\") { // IE 11\n          debug && console.warn(\"unable to use e.clipboardData\");\n          debug && console.warn(\"trying IE specific stuff\");\n          window.clipboardData.clearData();\n          var format = clipboardToIE11Formatting[options.format] || clipboardToIE11Formatting[\"default\"]\n          window.clipboardData.setData(format, text);\n        } else { // all other browsers\n          e.clipboardData.clearData();\n          e.clipboardData.setData(options.format, text);\n        }\n      }\n      if (options.onCopy) {\n        e.preventDefault();\n        options.onCopy(e.clipboardData);\n      }\n    });\n\n    document.body.appendChild(mark);\n\n    range.selectNodeContents(mark);\n    selection.addRange(range);\n\n    var successful = document.execCommand(\"copy\");\n    if (!successful) {\n      throw new Error(\"copy command was unsuccessful\");\n    }\n    success = true;\n  } catch (err) {\n    debug && console.error(\"unable to copy using execCommand: \", err);\n    debug && console.warn(\"trying IE specific stuff\");\n    try {\n      window.clipboardData.setData(options.format || \"text\", text);\n      options.onCopy && options.onCopy(window.clipboardData);\n      success = true;\n    } catch (err) {\n      debug && console.error(\"unable to copy using clipboardData: \", err);\n      debug && console.error(\"falling back to prompt\");\n      message = format(\"message\" in options ? options.message : defaultMessage);\n      window.prompt(message, text);\n    }\n  } finally {\n    if (selection) {\n      if (typeof selection.removeRange == \"function\") {\n        selection.removeRange(range);\n      } else {\n        selection.removeAllRanges();\n      }\n    }\n\n    if (mark) {\n      document.body.removeChild(mark);\n    }\n    reselectPrevious();\n  }\n\n  return success;\n}\n\nmodule.exports = copy;\n"], "names": ["props", "ref", "AntdIcon", "A", "icon", "isStyleNameSupport", "styleName", "canUseDom", "window", "document", "documentElement", "styleNameList", "Array", "isArray", "some", "name", "style", "isStyleSupport", "styleValue", "undefined", "value", "ele", "createElement", "origin", "isStyleValueSupport", "getTitleStyles", "token", "styles", "for<PERSON>ach", "headingLevel", "fontSize", "lineHeight", "color", "titleMarginBottom", "fontWeightStrong", "marginBottom", "fontWeight", "getTitleStyle", "colorTextHeading", "getLinkStyles", "componentCls", "Object", "assign", "userSelect", "colorTextDisabled", "cursor", "pointerEvents", "getResetStyles", "code", "margin", "paddingInline", "paddingBlock", "fontFamily", "fontFamilyCode", "background", "border", "borderRadius", "kbd", "borderBottomWidth", "mark", "padding", "backgroundColor", "textDecoration", "textDecorationSkipInk", "strong", "marginInline", "marginBlock", "li", "ul", "listStyleType", "ol", "pre", "whiteSpace", "wordWrap", "display", "blockquote", "borderInlineStart", "opacity", "getEditableStyles", "paddingSM", "inputShift", "position", "insetInlineStart", "calc", "mul", "equal", "marginTop", "insetInlineEnd", "marginXS", "add", "insetBlockEnd", "colorIcon", "fontStyle", "textarea", "MozTransition", "height", "getCopyableStyles", "colorSuccess", "marginInlineStart", "genTypographyStyle", "titleMarginTop", "colorText", "wordBreak", "colorTextDescription", "colorSuccessText", "colorWarningText", "colorErrorText", "colorErrorTextActive", "colorErrorTextHover", "marginXXS", "max<PERSON><PERSON><PERSON>", "overflow", "textOverflow", "verticalAlign", "boxSizing", "WebkitLineClamp", "WebkitBoxOrient", "direction", "prefixCls", "aria<PERSON><PERSON><PERSON>", "className", "max<PERSON><PERSON><PERSON>", "autoSize", "onSave", "onCancel", "onEnd", "component", "enterIcon", "inComposition", "lastKeyCode", "current", "setCurrent", "_a", "resizableTextArea", "textArea", "focus", "length", "setSelectionRange", "confirmChange", "trim", "wrapCSSVar", "hashId", "cssVarCls", "textAreaClassName", "TextArea", "onChange", "target", "replace", "onKeyDown", "keyCode", "onKeyUp", "ctrl<PERSON>ey", "altKey", "metaKey", "shift<PERSON>ey", "KeyCode", "ENTER", "ESC", "onCompositionStart", "onCompositionEnd", "onBlur", "rows", "candidate", "<PERSON><PERSON><PERSON><PERSON>", "__awaiter", "thisArg", "_arguments", "P", "generator", "Promise", "resolve", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "copyConfig", "children", "copied", "setCopied", "copyLoading", "setCopyLoading", "copyIdRef", "cleanCopyId", "clearTimeout", "copyOptions", "format", "onClick", "useEvent", "preventDefault", "stopPropagation", "text", "join", "setTimeout", "onCopy", "call", "error", "useMergedConfig", "propConfig", "templateConfig", "support", "useRef", "useEffect", "tooltip", "editConfigText", "useMemo", "title", "isValidElement", "__rest", "s", "t", "p", "prototype", "hasOwnProperty", "indexOf", "getOwnPropertySymbols", "i", "propertyIsEnumerable", "Typography", "customizePrefixCls", "Component", "rootClassName", "setContentRef", "typographyDirection", "restProps", "getPrefixCls", "contextDirection", "contextClassName", "contextStyle", "mergedRef", "componentClassName", "mergedStyle", "val", "getNode", "dom", "defaultNode", "needDom", "isValidText", "includes", "locale", "iconOnly", "tooltips", "tabIndex", "loading", "btnLoading", "tooltipNodes", "iconNodes", "copiedText", "copy", "copyText", "systemStr", "copyTitle", "type", "CheckOutlined", "LoadingOutlined", "MeasureText", "spanRef", "isExceed", "span", "scrollHeight", "clientHeight", "getHeight", "left", "top", "sliceNodes", "nodeList", "len", "currLen", "currentNodeList", "node", "nextLen", "String", "restLen", "push", "slice", "STATUS_MEASURE_NONE", "STATUS_MEASURE_NO_NEED_ELLIPSIS", "lineClipStyle", "EllipsisMeasure", "enableMeasure", "width", "expanded", "miscDeps", "onEllipsis", "toArray", "nodeLen", "reduce", "totalLen", "getNodesLen", "fullContent", "ellipsisCutIndex", "setEllipsisCutIndex", "cutMidRef", "measureWhiteSpaceRef", "needEllipsisRef", "descRowsEllipsisRef", "symbolRowEllipsisRef", "canEllipsis", "setCanEllipsis", "needEllipsis", "setNeedEllipsis", "ellipsisHeight", "setEllipsisHeight", "parentWhiteSpace", "setParentWhiteSpace", "useLayoutEffect", "_b", "_c", "_d", "nextWhiteSpace", "getComputedStyle", "isOverflow", "baseRowsEllipsisHeight", "descRowsEllipsisHeight", "symbolRowEllipsisHeight", "maxRowsHeight", "Math", "max", "cutMidIndex", "ceil", "minIndex", "maxIndex", "targetMidIndex", "finalContent", "content", "concat", "measureStyle", "enableEllipsis", "isEllipsis", "tooltipProps", "open", "DECORATION_PROPS", "Base", "disabled", "ellipsis", "editable", "copyable", "textLocale", "useLocale", "typographyRef", "editIconRef", "textProps", "omit", "enableEdit", "editConfig", "editing", "setEditing", "useMergedState", "triggerType", "triggerEdit", "edit", "onStart", "prevEditing", "onEditClick", "onEditChange", "onEditCancel", "enableCopy", "onCopyClick", "isLineClampSupport", "setIsLineClampSupport", "isTextOverflowSupport", "setIsTextOverflowSupport", "isJsEllipsis", "setIsJsEllipsis", "isNativeEllipsis", "setIsNativeEllipsis", "isNativeVisible", "setIsNativeVisible", "ellipsisConfig", "expandable", "symbol", "isExpanded", "collapse", "expand", "setExpanded", "defaultExpanded", "mergedEnableEllipsis", "needMeasureEllipsis", "suffix", "cssEllipsis", "setCssEllipsis", "canUseCssEllipsis", "isMergedEllipsis", "cssTextOverflow", "cssLineClamp", "ellip<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "onJsEllipsis", "js<PERSON><PERSON><PERSON>", "textEle", "currentEllipsis", "childDiv", "append<PERSON><PERSON><PERSON>", "rect", "getBoundingClientRect", "childRect", "<PERSON><PERSON><PERSON><PERSON>", "right", "bottom", "isEleEllipsis", "IntersectionObserver", "observer", "offsetParent", "observe", "disconnect", "topAriaLabel", "find", "renderExpand", "key", "info", "onExpand", "onExpandClick", "renderEdit", "editTitle", "role", "renderOperations", "onResize", "offsetWidth", "resizeRef", "toString", "map", "underline", "delete", "del", "keyboard", "italic", "currentC<PERSON>nt", "wrap", "tag", "needed", "wrapperDecorations", "renderEllipsis", "rel", "mergedProps", "navigate", "Text", "mergedEllipsis", "TITLE_ELE_LIST", "level", "Link", "Title", "Paragraph", "module", "exports", "selection", "getSelection", "rangeCount", "active", "activeElement", "ranges", "getRangeAt", "tagName", "toUpperCase", "blur", "removeAllRanges", "range", "addRange", "deselectCurrent", "clipboardToIE11Formatting", "options", "debug", "message", "reselectPrevious", "success", "createRange", "textContent", "ariaHidden", "all", "clip", "webkitUserSelect", "MozUserSelect", "msUserSelect", "addEventListener", "clipboardData", "console", "warn", "clearData", "setData", "body", "selectNodeContents", "execCommand", "Error", "err", "copyKey", "test", "navigator", "userAgent", "prompt", "<PERSON><PERSON><PERSON><PERSON>"], "sourceRoot": ""}